"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[86],{3515:(e,t,a)=>{a.d(t,{SignButton:()=>u});var s=a(5155),r=a(9393),n=a(5565),i=a(4822),o=a(8427),l=a(6046),d=a(8173),c=a.n(d),m=a(2558);function u(e){let{size:t="default"}=e,a=(0,l.useRouter)(),{data:d,status:u}=(0,i.wV)();return"loading"===u?(0,s.jsx)("div",{className:"h-9"}):(null==d?void 0:d.user)?(0,s.jsxs)("div",{className:"flex items-center gap-4",children:[(0,s.jsxs)(c(),{href:"/profile",className:"flex items-center gap-2 hover:opacity-80 transition-opacity",children:[d.user.image&&(0,s.jsx)(n.default,{src:d.user.image,alt:d.user.name||"用户头像",width:24,height:24,className:"rounded-full"}),(0,s.jsx)("span",{className:"text-sm",children:d.user.name})]}),(0,s.jsx)(r.$,{onClick:()=>(0,i.CI)({callbackUrl:"/"}),variant:"outline",className:(0,m.cn)("flex-shrink-0","lg"===t?"px-8":""),size:t,children:"登出"})]}):(0,s.jsxs)(r.$,{onClick:()=>a.push("/login"),className:(0,m.cn)("gap-2","lg"===t?"px-8":""),size:t,children:[(0,s.jsx)(o.A,{className:"lg"===t?"w-5 h-5":"w-4 h-4"}),"登录/注册"]})}},1665:(e,t,a)=>{a.d(t,{ThemeToggle:()=>l});var s=a(5155),r=a(7725),n=a(2104),i=a(8872),o=a(9393);function l(){let{theme:e,setTheme:t}=(0,i.D)();return(0,s.jsxs)(o.$,{variant:"ghost",size:"icon",onClick:()=>t("light"===e?"dark":"light"),className:"rounded-full",children:[(0,s.jsx)(r.A,{className:"h-5 w-5 rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0"}),(0,s.jsx)(n.A,{className:"absolute h-5 w-5 rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100"}),(0,s.jsx)("span",{className:"sr-only",children:"切换主题"})]})}},9393:(e,t,a)=>{a.d(t,{$:()=>d,r:()=>l});var s=a(5155),r=a(2115),n=a(2317),i=a(1027),o=a(2558);let l=(0,i.F)("inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground shadow hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90",outline:"border border-input bg-transparent shadow-sm hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2",sm:"h-8 rounded-md px-3 text-xs",lg:"h-10 rounded-md px-8",icon:"h-9 w-9"}},defaultVariants:{variant:"default",size:"default"}}),d=r.forwardRef((e,t)=>{let{className:a,variant:r,size:i,asChild:d=!1,...c}=e,m=d?n.DX:"button";return(0,s.jsx)(m,{className:(0,o.cn)(l({variant:r,size:i,className:a})),ref:t,...c})});d.displayName="Button"},705:(e,t,a)=>{a.d(t,{Cf:()=>f,Es:()=>h,HM:()=>m,L3:()=>g,c7:()=>p,lG:()=>l,rr:()=>x,zM:()=>d});var s=a(5155),r=a(2115),n=a(3322),i=a(767),o=a(2558);let l=n.bL,d=n.l9,c=n.ZL,m=n.bm,u=r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,s.jsx)(n.hJ,{ref:t,className:(0,o.cn)("fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",a),...r})});u.displayName=n.hJ.displayName;let f=r.forwardRef((e,t)=>{let{className:a,children:r,...l}=e;return(0,s.jsxs)(c,{children:[(0,s.jsx)(u,{}),(0,s.jsxs)(n.UC,{ref:t,className:(0,o.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",a),...l,children:[r,(0,s.jsxs)(n.bm,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[(0,s.jsx)(i.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{className:"sr-only",children:"关闭"})]})]})]})});f.displayName=n.UC.displayName;let p=e=>{let{className:t,...a}=e;return(0,s.jsx)("div",{className:(0,o.cn)("flex flex-col space-y-1.5 text-center sm:text-left",t),...a})};p.displayName="DialogHeader";let h=e=>{let{className:t,...a}=e;return(0,s.jsx)("div",{className:(0,o.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",t),...a})};h.displayName="DialogFooter";let g=r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,s.jsx)(n.hE,{ref:t,className:(0,o.cn)("text-lg font-semibold leading-none tracking-tight",a),...r})});g.displayName=n.hE.displayName;let x=r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,s.jsx)(n.VY,{ref:t,className:(0,o.cn)("text-sm text-muted-foreground",a),...r})});x.displayName=n.VY.displayName},9367:(e,t,a)=>{a.d(t,{p:()=>i});var s=a(5155),r=a(2115),n=a(2558);let i=r.forwardRef((e,t)=>{let{className:a,type:r,...i}=e;return(0,s.jsx)("input",{type:r,className:(0,n.cn)("flex h-9 w-full rounded-md border border-input bg-transparent px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50",a),ref:t,...i})});i.displayName="Input"},477:(e,t,a)=>{a.d(t,{J:()=>o});var s=a(5155),r=a(2115),n=a(6195),i=a(2558);let o=r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,s.jsx)(n.b,{ref:t,className:(0,i.cn)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",a),...r})});o.displayName=n.b.displayName},5368:(e,t,a)=>{a.d(t,{Logo:()=>i});var s=a(5155),r=a(8173),n=a.n(r);function i(){return(0,s.jsxs)(n(),{href:"/",className:"flex items-center gap-2 hover:opacity-80 transition-opacity",children:[(0,s.jsx)("div",{className:"relative w-8 h-8",children:(0,s.jsx)("div",{className:"absolute inset-0 grid grid-cols-8 grid-rows-8 gap-px",children:(0,s.jsxs)("svg",{width:"32",height:"32",viewBox:"0 0 32 32",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:"text-primary",children:[(0,s.jsx)("path",{d:"M4 8h24v16H4V8z",className:"fill-primary/20"}),(0,s.jsx)("path",{d:"M4 8h24v2H4V8zM4 22h24v2H4v-2z",className:"fill-primary"}),(0,s.jsx)("path",{d:"M14 12h4v4h-4v-4zM12 14h2v4h-2v-4zM18 14h2v4h-2v-4zM14 18h4v2h-4v-2z",className:"fill-primary"}),(0,s.jsx)("path",{d:"M4 8l12 8 12-8",className:"stroke-primary stroke-2",fill:"none"}),(0,s.jsx)("path",{d:"M8 18h2v2H8v-2zM22 18h2v2h-2v-2z",className:"fill-primary/60"}),(0,s.jsx)("path",{d:"M8 14h2v2H8v-2zM22 14h2v2h-2v-2z",className:"fill-primary/40"})]})})}),(0,s.jsx)("span",{className:"font-bold tracking-wider bg-clip-text text-transparent bg-gradient-to-r from-primary to-purple-600",children:"MoeMail"})]})}},8941:(e,t,a)=>{a.d(t,{bq:()=>c,eb:()=>u,gC:()=>m,l6:()=>d,yv:()=>f});var s=a(5155),r=a(2115),n=a(3123),i=a(1719),o=a(8867),l=a(2558);let d=n.bL,c=r.forwardRef((e,t)=>{let{className:a,children:r,...o}=e;return(0,s.jsxs)(n.l9,{ref:t,className:(0,l.cn)("flex h-9 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",a),...o,children:[r,(0,s.jsx)(n.In,{asChild:!0,children:(0,s.jsx)(i.A,{className:"h-4 w-4 opacity-50"})})]})});c.displayName=n.l9.displayName;let m=r.forwardRef((e,t)=>{let{className:a,children:r,position:i="popper",...o}=e;return(0,s.jsx)(n.ZL,{children:(0,s.jsx)(n.UC,{ref:t,className:(0,l.cn)("relative z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===i&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",a),position:i,...o,children:(0,s.jsx)(n.LM,{className:(0,l.cn)("p-1","popper"===i&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:r})})})});m.displayName=n.UC.displayName;let u=r.forwardRef((e,t)=>{let{className:a,children:r,...i}=e;return(0,s.jsxs)(n.q7,{ref:t,className:(0,l.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",a),...i,children:[(0,s.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,s.jsx)(n.VF,{children:(0,s.jsx)(o.A,{className:"h-4 w-4"})})}),(0,s.jsx)(n.p4,{children:r})]})});u.displayName=n.q7.displayName;let f=n.WT},7762:(e,t,a)=>{a.d(t,{dj:()=>u});var s=a(2115);let r=0,n=new Map,i=e=>{if(n.has(e))return;let t=setTimeout(()=>{n.delete(e),c({type:"REMOVE_TOAST",toastId:e})},1e6);n.set(e,t)},o=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,1)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case"DISMISS_TOAST":{let{toastId:a}=t;return a?i(a):e.toasts.forEach(e=>{i(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===a||void 0===a?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===t.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)}}},l=[],d={toasts:[]};function c(e){d=o(d,e),l.forEach(e=>{e(d)})}function m(e){let{...t}=e,a=(r=(r+1)%Number.MAX_VALUE).toString(),s=()=>c({type:"DISMISS_TOAST",toastId:a});return c({type:"ADD_TOAST",toast:{...t,id:a,open:!0,onOpenChange:e=>{e||s()}}}),{id:a,dismiss:s,update:e=>c({type:"UPDATE_TOAST",toast:{...e,id:a}})}}function u(){let[e,t]=s.useState(d);return s.useEffect(()=>(l.push(t),()=>{let e=l.indexOf(t);e>-1&&l.splice(e,1)}),[e]),{...e,toast:m,dismiss:e=>c({type:"DISMISS_TOAST",toastId:e})}}},8200:(e,t,a)=>{a.d(t,{q:()=>s});let s={MAX_ACTIVE_EMAILS:30,POLL_INTERVAL:1e4}},3244:(e,t,a)=>{a.d(t,{U:()=>l});var s=a(9827),r=a(2619),n=a(8200),i=a(2115);let o=(0,s.v)(e=>({config:null,loading:!1,error:null,fetch:async()=>{try{e({loading:!0,error:null});let t=await fetch("/api/config");if(!t.ok)throw Error("获取配置失败");let a=await t.json();e({config:{defaultRole:a.defaultRole||r.gg.CIVILIAN,emailDomains:a.emailDomains,emailDomainsArray:a.emailDomains.split(","),adminContact:a.adminContact||"",maxEmails:Number(a.maxEmails)||n.q.MAX_ACTIVE_EMAILS},loading:!1})}catch(t){e({error:t instanceof Error?t.message:"获取配置失败",loading:!1})}}}));function l(){let e=o();return(0,i.useEffect)(()=>{e.config||e.loading||e.fetch()},[e.config,e.loading]),e}},5023:(e,t,a)=>{a.d(t,{T:()=>n});var s=a(2115),r=a(7762);function n(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},{toast:t}=(0,r.dj)(),{successMessage:a="已复制到剪贴板",errorMessage:n="复制失败"}=e;return{copyToClipboard:(0,s.useCallback)(async e=>{try{return await navigator.clipboard.writeText(e),t({title:"成功",description:a}),!0}catch(e){return t({title:"错误",description:n,variant:"destructive"}),!1}},[a,n,t])}}},2619:(e,t,a)=>{a.d(t,{Jj:()=>r,_m:()=>i,gg:()=>s});let s={EMPEROR:"emperor",DUKE:"duke",KNIGHT:"knight",CIVILIAN:"civilian"},r={MANAGE_EMAIL:"manage_email",MANAGE_WEBHOOK:"manage_webhook",PROMOTE_USER:"promote_user",MANAGE_CONFIG:"manage_config",MANAGE_API_KEY:"manage_api_key",MANAGE_EMAIL_CREDENTIALS:"manage_email_credentials"},n={[s.EMPEROR]:Object.values(r),[s.DUKE]:[r.MANAGE_EMAIL,r.MANAGE_WEBHOOK,r.MANAGE_API_KEY,r.MANAGE_EMAIL_CREDENTIALS],[s.KNIGHT]:[r.MANAGE_EMAIL,r.MANAGE_WEBHOOK,r.MANAGE_EMAIL_CREDENTIALS],[s.CIVILIAN]:[r.MANAGE_EMAIL,r.MANAGE_EMAIL_CREDENTIALS]};function i(e,t){return e.some(e=>{var a;return null===(a=n[e])||void 0===a?void 0:a.includes(t)})}},2558:(e,t,a)=>{a.d(t,{cn:()=>n});var s=a(3463),r=a(9795);function n(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return(0,r.QP)((0,s.$)(t))}a(2818)}}]);