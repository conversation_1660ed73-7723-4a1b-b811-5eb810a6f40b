"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[246],{767:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(7401).A)("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},8872:(e,t,n)=>{n.d(t,{D:()=>s,N:()=>c});var r=n(2115);let i=["light","dark"],o="(prefers-color-scheme: dark)",l="undefined"==typeof window,u=(0,r.createContext)(void 0),a={setTheme:e=>{},themes:[]},s=()=>{var e;return null!==(e=(0,r.useContext)(u))&&void 0!==e?e:a},c=e=>(0,r.useContext)(u)?r.createElement(r.Fragment,null,e.children):r.createElement(d,e),f=["light","dark"],d=({forcedTheme:e,disableTransitionOnChange:t=!1,enableSystem:n=!0,enableColorScheme:l=!0,storageKey:a="theme",themes:s=f,defaultTheme:c=n?"system":"light",attribute:d="data-theme",value:y,children:v,nonce:w})=>{let[b,x]=(0,r.useState)(()=>h(a,c)),[E,T]=(0,r.useState)(()=>h(a)),R=y?Object.values(y):s,S=(0,r.useCallback)(e=>{let r=e;if(!r)return;"system"===e&&n&&(r=g());let o=y?y[r]:r,u=t?p():null,a=document.documentElement;if("class"===d?(a.classList.remove(...R),o&&a.classList.add(o)):o?a.setAttribute(d,o):a.removeAttribute(d),l){let e=i.includes(c)?c:null,t=i.includes(r)?r:e;a.style.colorScheme=t}null==u||u()},[]),N=(0,r.useCallback)(e=>{x(e);try{localStorage.setItem(a,e)}catch(e){}},[e]),A=(0,r.useCallback)(t=>{T(g(t)),"system"===b&&n&&!e&&S("system")},[b,e]);(0,r.useEffect)(()=>{let e=window.matchMedia(o);return e.addListener(A),A(e),()=>e.removeListener(A)},[A]),(0,r.useEffect)(()=>{let e=e=>{e.key===a&&N(e.newValue||c)};return window.addEventListener("storage",e),()=>window.removeEventListener("storage",e)},[N]),(0,r.useEffect)(()=>{S(null!=e?e:b)},[e,b]);let L=(0,r.useMemo)(()=>({theme:b,setTheme:N,forcedTheme:e,resolvedTheme:"system"===b?E:b,themes:n?[...s,"system"]:s,systemTheme:n?E:void 0}),[b,N,e,E,n,s]);return r.createElement(u.Provider,{value:L},r.createElement(m,{forcedTheme:e,disableTransitionOnChange:t,enableSystem:n,enableColorScheme:l,storageKey:a,themes:s,defaultTheme:c,attribute:d,value:y,children:v,attrs:R,nonce:w}),v)},m=(0,r.memo)(({forcedTheme:e,storageKey:t,attribute:n,enableSystem:l,enableColorScheme:u,defaultTheme:a,value:s,attrs:c,nonce:f})=>{let d="system"===a,m="class"===n?`var d=document.documentElement,c=d.classList;c.remove(${c.map(e=>`'${e}'`).join(",")});`:`var d=document.documentElement,n='${n}',s='setAttribute';`,h=u?i.includes(a)&&a?`if(e==='light'||e==='dark'||!e)d.style.colorScheme=e||'${a}'`:"if(e==='light'||e==='dark')d.style.colorScheme=e":"",p=(e,t=!1,r=!0)=>{let o=s?s[e]:e,l=t?e+"|| ''":`'${o}'`,a="";return u&&r&&!t&&i.includes(e)&&(a+=`d.style.colorScheme = '${e}';`),"class"===n?a+=t||o?`c.add(${l})`:"null":o&&(a+=`d[s](n,${l})`),a},g=e?`!function(){${m}${p(e)}}()`:l?`!function(){try{${m}var e=localStorage.getItem('${t}');if('system'===e||(!e&&${d})){var t='${o}',m=window.matchMedia(t);if(m.media!==t||m.matches){${p("dark")}}else{${p("light")}}}else if(e){${s?`var x=${JSON.stringify(s)};`:""}${p(s?"x[e]":"e",!0)}}${d?"":"else{"+p(a,!1,!1)+"}"}${h}}catch(e){}}()`:`!function(){try{${m}var e=localStorage.getItem('${t}');if(e){${s?`var x=${JSON.stringify(s)};`:""}${p(s?"x[e]":"e",!0)}}else{${p(a,!1,!1)};}${h}}catch(t){}}();`;return r.createElement("script",{nonce:f,dangerouslySetInnerHTML:{__html:g}})},()=>!0),h=(e,t)=>{let n;if(!l){try{n=localStorage.getItem(e)||void 0}catch(e){}return n||t}},p=()=>{let e=document.createElement("style");return e.appendChild(document.createTextNode("*{-webkit-transition:none!important;-moz-transition:none!important;-o-transition:none!important;-ms-transition:none!important;transition:none!important}")),document.head.appendChild(e),()=>{window.getComputedStyle(document.body),setTimeout(()=>{document.head.removeChild(e)},1)}},g=e=>(e||(e=window.matchMedia(o)),e.matches?"dark":"light")},7205:(e,t,n)=>{n.d(t,{UE:()=>ef,ll:()=>eo,rD:()=>em,UU:()=>ea,jD:()=>ec,ER:()=>ed,cY:()=>el,BN:()=>eu,Ej:()=>es});let r=["top","right","bottom","left"],i=Math.min,o=Math.max,l=Math.round,u=Math.floor,a=e=>({x:e,y:e}),s={left:"right",right:"left",bottom:"top",top:"bottom"},c={start:"end",end:"start"};function f(e,t){return"function"==typeof e?e(t):e}function d(e){return e.split("-")[0]}function m(e){return e.split("-")[1]}function h(e){return"x"===e?"y":"x"}function p(e){return"y"===e?"height":"width"}function g(e){return["top","bottom"].includes(d(e))?"y":"x"}function y(e){return e.replace(/start|end/g,e=>c[e])}function v(e){return e.replace(/left|right|bottom|top/g,e=>s[e])}function w(e){return"number"!=typeof e?{top:0,right:0,bottom:0,left:0,...e}:{top:e,right:e,bottom:e,left:e}}function b(e){let{x:t,y:n,width:r,height:i}=e;return{width:r,height:i,top:n,left:t,right:t+r,bottom:n+i,x:t,y:n}}function x(e,t,n){let r,{reference:i,floating:o}=e,l=g(t),u=h(g(t)),a=p(u),s=d(t),c="y"===l,f=i.x+i.width/2-o.width/2,y=i.y+i.height/2-o.height/2,v=i[a]/2-o[a]/2;switch(s){case"top":r={x:f,y:i.y-o.height};break;case"bottom":r={x:f,y:i.y+i.height};break;case"right":r={x:i.x+i.width,y:y};break;case"left":r={x:i.x-o.width,y:y};break;default:r={x:i.x,y:i.y}}switch(m(t)){case"start":r[u]-=v*(n&&c?-1:1);break;case"end":r[u]+=v*(n&&c?-1:1)}return r}let E=async(e,t,n)=>{let{placement:r="bottom",strategy:i="absolute",middleware:o=[],platform:l}=n,u=o.filter(Boolean),a=await (null==l.isRTL?void 0:l.isRTL(t)),s=await l.getElementRects({reference:e,floating:t,strategy:i}),{x:c,y:f}=x(s,r,a),d=r,m={},h=0;for(let n=0;n<u.length;n++){let{name:o,fn:p}=u[n],{x:g,y:y,data:v,reset:w}=await p({x:c,y:f,initialPlacement:r,placement:d,strategy:i,middlewareData:m,rects:s,platform:l,elements:{reference:e,floating:t}});c=null!=g?g:c,f=null!=y?y:f,m={...m,[o]:{...m[o],...v}},w&&h<=50&&(h++,"object"==typeof w&&(w.placement&&(d=w.placement),w.rects&&(s=!0===w.rects?await l.getElementRects({reference:e,floating:t,strategy:i}):w.rects),{x:c,y:f}=x(s,d,a)),n=-1)}return{x:c,y:f,placement:d,strategy:i,middlewareData:m}};async function T(e,t){var n;void 0===t&&(t={});let{x:r,y:i,platform:o,rects:l,elements:u,strategy:a}=e,{boundary:s="clippingAncestors",rootBoundary:c="viewport",elementContext:d="floating",altBoundary:m=!1,padding:h=0}=f(t,e),p=w(h),g=u[m?"floating"===d?"reference":"floating":d],y=b(await o.getClippingRect({element:null==(n=await (null==o.isElement?void 0:o.isElement(g)))||n?g:g.contextElement||await (null==o.getDocumentElement?void 0:o.getDocumentElement(u.floating)),boundary:s,rootBoundary:c,strategy:a})),v="floating"===d?{x:r,y:i,width:l.floating.width,height:l.floating.height}:l.reference,x=await (null==o.getOffsetParent?void 0:o.getOffsetParent(u.floating)),E=await (null==o.isElement?void 0:o.isElement(x))&&await (null==o.getScale?void 0:o.getScale(x))||{x:1,y:1},T=b(o.convertOffsetParentRelativeRectToViewportRelativeRect?await o.convertOffsetParentRelativeRectToViewportRelativeRect({elements:u,rect:v,offsetParent:x,strategy:a}):v);return{top:(y.top-T.top+p.top)/E.y,bottom:(T.bottom-y.bottom+p.bottom)/E.y,left:(y.left-T.left+p.left)/E.x,right:(T.right-y.right+p.right)/E.x}}function R(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function S(e){return r.some(t=>e[t]>=0)}async function N(e,t){let{placement:n,platform:r,elements:i}=e,o=await (null==r.isRTL?void 0:r.isRTL(i.floating)),l=d(n),u=m(n),a="y"===g(n),s=["left","top"].includes(l)?-1:1,c=o&&a?-1:1,h=f(t,e),{mainAxis:p,crossAxis:y,alignmentAxis:v}="number"==typeof h?{mainAxis:h,crossAxis:0,alignmentAxis:null}:{mainAxis:h.mainAxis||0,crossAxis:h.crossAxis||0,alignmentAxis:h.alignmentAxis};return u&&"number"==typeof v&&(y="end"===u?-1*v:v),a?{x:y*c,y:p*s}:{x:p*s,y:y*c}}function A(){return"undefined"!=typeof window}function L(e){return k(e)?(e.nodeName||"").toLowerCase():"#document"}function O(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function C(e){var t;return null==(t=(k(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function k(e){return!!A()&&(e instanceof Node||e instanceof O(e).Node)}function M(e){return!!A()&&(e instanceof Element||e instanceof O(e).Element)}function $(e){return!!A()&&(e instanceof HTMLElement||e instanceof O(e).HTMLElement)}function D(e){return!!A()&&"undefined"!=typeof ShadowRoot&&(e instanceof ShadowRoot||e instanceof O(e).ShadowRoot)}function P(e){let{overflow:t,overflowX:n,overflowY:r,display:i}=j(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!["inline","contents"].includes(i)}function U(e){return[":popover-open",":modal"].some(t=>{try{return e.matches(t)}catch(e){return!1}})}function _(e){let t=F(),n=M(e)?j(e):e;return"none"!==n.transform||"none"!==n.perspective||!!n.containerType&&"normal"!==n.containerType||!t&&!!n.backdropFilter&&"none"!==n.backdropFilter||!t&&!!n.filter&&"none"!==n.filter||["transform","perspective","filter"].some(e=>(n.willChange||"").includes(e))||["paint","layout","strict","content"].some(e=>(n.contain||"").includes(e))}function F(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}function I(e){return["html","body","#document"].includes(L(e))}function j(e){return O(e).getComputedStyle(e)}function H(e){return M(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function W(e){if("html"===L(e))return e;let t=e.assignedSlot||e.parentNode||D(e)&&e.host||C(e);return D(t)?t.host:t}function B(e,t,n){var r;void 0===t&&(t=[]),void 0===n&&(n=!0);let i=function e(t){let n=W(t);return I(n)?t.ownerDocument?t.ownerDocument.body:t.body:$(n)&&P(n)?n:e(n)}(e),o=i===(null==(r=e.ownerDocument)?void 0:r.body),l=O(i);if(o){let e=V(l);return t.concat(l,l.visualViewport||[],P(i)?i:[],e&&n?B(e):[])}return t.concat(i,B(i,[],n))}function V(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function z(e){let t=j(e),n=parseFloat(t.width)||0,r=parseFloat(t.height)||0,i=$(e),o=i?e.offsetWidth:n,u=i?e.offsetHeight:r,a=l(n)!==o||l(r)!==u;return a&&(n=o,r=u),{width:n,height:r,$:a}}function X(e){return M(e)?e:e.contextElement}function Y(e){let t=X(e);if(!$(t))return a(1);let n=t.getBoundingClientRect(),{width:r,height:i,$:o}=z(t),u=(o?l(n.width):n.width)/r,s=(o?l(n.height):n.height)/i;return u&&Number.isFinite(u)||(u=1),s&&Number.isFinite(s)||(s=1),{x:u,y:s}}let K=a(0);function q(e){let t=O(e);return F()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:K}function J(e,t,n,r){var i;void 0===t&&(t=!1),void 0===n&&(n=!1);let o=e.getBoundingClientRect(),l=X(e),u=a(1);t&&(r?M(r)&&(u=Y(r)):u=Y(e));let s=(void 0===(i=n)&&(i=!1),r&&(!i||r===O(l))&&i)?q(l):a(0),c=(o.left+s.x)/u.x,f=(o.top+s.y)/u.y,d=o.width/u.x,m=o.height/u.y;if(l){let e=O(l),t=r&&M(r)?O(r):r,n=e,i=V(n);for(;i&&r&&t!==n;){let e=Y(i),t=i.getBoundingClientRect(),r=j(i),o=t.left+(i.clientLeft+parseFloat(r.paddingLeft))*e.x,l=t.top+(i.clientTop+parseFloat(r.paddingTop))*e.y;c*=e.x,f*=e.y,d*=e.x,m*=e.y,c+=o,f+=l,i=V(n=O(i))}}return b({width:d,height:m,x:c,y:f})}function G(e,t){let n=H(e).scrollLeft;return t?t.left+n:J(C(e)).left+n}function Q(e,t,n){void 0===n&&(n=!1);let r=e.getBoundingClientRect();return{x:r.left+t.scrollLeft-(n?0:G(e,r)),y:r.top+t.scrollTop}}function Z(e,t,n){let r;if("viewport"===t)r=function(e,t){let n=O(e),r=C(e),i=n.visualViewport,o=r.clientWidth,l=r.clientHeight,u=0,a=0;if(i){o=i.width,l=i.height;let e=F();(!e||e&&"fixed"===t)&&(u=i.offsetLeft,a=i.offsetTop)}return{width:o,height:l,x:u,y:a}}(e,n);else if("document"===t)r=function(e){let t=C(e),n=H(e),r=e.ownerDocument.body,i=o(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),l=o(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight),u=-n.scrollLeft+G(e),a=-n.scrollTop;return"rtl"===j(r).direction&&(u+=o(t.clientWidth,r.clientWidth)-i),{width:i,height:l,x:u,y:a}}(C(e));else if(M(t))r=function(e,t){let n=J(e,!0,"fixed"===t),r=n.top+e.clientTop,i=n.left+e.clientLeft,o=$(e)?Y(e):a(1),l=e.clientWidth*o.x;return{width:l,height:e.clientHeight*o.y,x:i*o.x,y:r*o.y}}(t,n);else{let n=q(e);r={x:t.x-n.x,y:t.y-n.y,width:t.width,height:t.height}}return b(r)}function ee(e){return"static"===j(e).position}function et(e,t){if(!$(e)||"fixed"===j(e).position)return null;if(t)return t(e);let n=e.offsetParent;return C(e)===n&&(n=n.ownerDocument.body),n}function en(e,t){let n=O(e);if(U(e))return n;if(!$(e)){let t=W(e);for(;t&&!I(t);){if(M(t)&&!ee(t))return t;t=W(t)}return n}let r=et(e,t);for(;r&&["table","td","th"].includes(L(r))&&ee(r);)r=et(r,t);return r&&I(r)&&ee(r)&&!_(r)?n:r||function(e){let t=W(e);for(;$(t)&&!I(t);){if(_(t))return t;if(U(t))break;t=W(t)}return null}(e)||n}let er=async function(e){let t=this.getOffsetParent||en,n=this.getDimensions,r=await n(e.floating);return{reference:function(e,t,n){let r=$(t),i=C(t),o="fixed"===n,l=J(e,!0,o,t),u={scrollLeft:0,scrollTop:0},s=a(0);if(r||!r&&!o){if(("body"!==L(t)||P(i))&&(u=H(t)),r){let e=J(t,!0,o,t);s.x=e.x+t.clientLeft,s.y=e.y+t.clientTop}else i&&(s.x=G(i))}let c=!i||r||o?a(0):Q(i,u);return{x:l.left+u.scrollLeft-s.x-c.x,y:l.top+u.scrollTop-s.y-c.y,width:l.width,height:l.height}}(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}},ei={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:n,offsetParent:r,strategy:i}=e,o="fixed"===i,l=C(r),u=!!t&&U(t.floating);if(r===l||u&&o)return n;let s={scrollLeft:0,scrollTop:0},c=a(1),f=a(0),d=$(r);if((d||!d&&!o)&&(("body"!==L(r)||P(l))&&(s=H(r)),$(r))){let e=J(r);c=Y(r),f.x=e.x+r.clientLeft,f.y=e.y+r.clientTop}let m=!l||d||o?a(0):Q(l,s,!0);return{width:n.width*c.x,height:n.height*c.y,x:n.x*c.x-s.scrollLeft*c.x+f.x+m.x,y:n.y*c.y-s.scrollTop*c.y+f.y+m.y}},getDocumentElement:C,getClippingRect:function(e){let{element:t,boundary:n,rootBoundary:r,strategy:l}=e,u=[..."clippingAncestors"===n?U(t)?[]:function(e,t){let n=t.get(e);if(n)return n;let r=B(e,[],!1).filter(e=>M(e)&&"body"!==L(e)),i=null,o="fixed"===j(e).position,l=o?W(e):e;for(;M(l)&&!I(l);){let t=j(l),n=_(l);n||"fixed"!==t.position||(i=null),(o?!n&&!i:!n&&"static"===t.position&&!!i&&["absolute","fixed"].includes(i.position)||P(l)&&!n&&function e(t,n){let r=W(t);return!(r===n||!M(r)||I(r))&&("fixed"===j(r).position||e(r,n))}(e,l))?r=r.filter(e=>e!==l):i=t,l=W(l)}return t.set(e,r),r}(t,this._c):[].concat(n),r],a=u[0],s=u.reduce((e,n)=>{let r=Z(t,n,l);return e.top=o(r.top,e.top),e.right=i(r.right,e.right),e.bottom=i(r.bottom,e.bottom),e.left=o(r.left,e.left),e},Z(t,a,l));return{width:s.right-s.left,height:s.bottom-s.top,x:s.left,y:s.top}},getOffsetParent:en,getElementRects:er,getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){let{width:t,height:n}=z(e);return{width:t,height:n}},getScale:Y,isElement:M,isRTL:function(e){return"rtl"===j(e).direction}};function eo(e,t,n,r){let l;void 0===r&&(r={});let{ancestorScroll:a=!0,ancestorResize:s=!0,elementResize:c="function"==typeof ResizeObserver,layoutShift:f="function"==typeof IntersectionObserver,animationFrame:d=!1}=r,m=X(e),h=a||s?[...m?B(m):[],...B(t)]:[];h.forEach(e=>{a&&e.addEventListener("scroll",n,{passive:!0}),s&&e.addEventListener("resize",n)});let p=m&&f?function(e,t){let n,r=null,l=C(e);function a(){var e;clearTimeout(n),null==(e=r)||e.disconnect(),r=null}return!function s(c,f){void 0===c&&(c=!1),void 0===f&&(f=1),a();let{left:d,top:m,width:h,height:p}=e.getBoundingClientRect();if(c||t(),!h||!p)return;let g=u(m),y=u(l.clientWidth-(d+h)),v={rootMargin:-g+"px "+-y+"px "+-u(l.clientHeight-(m+p))+"px "+-u(d)+"px",threshold:o(0,i(1,f))||1},w=!0;function b(e){let t=e[0].intersectionRatio;if(t!==f){if(!w)return s();t?s(!1,t):n=setTimeout(()=>{s(!1,1e-7)},1e3)}w=!1}try{r=new IntersectionObserver(b,{...v,root:l.ownerDocument})}catch(e){r=new IntersectionObserver(b,v)}r.observe(e)}(!0),a}(m,n):null,g=-1,y=null;c&&(y=new ResizeObserver(e=>{let[r]=e;r&&r.target===m&&y&&(y.unobserve(t),cancelAnimationFrame(g),g=requestAnimationFrame(()=>{var e;null==(e=y)||e.observe(t)})),n()}),m&&!d&&y.observe(m),y.observe(t));let v=d?J(e):null;return d&&function t(){let r=J(e);v&&(r.x!==v.x||r.y!==v.y||r.width!==v.width||r.height!==v.height)&&n(),v=r,l=requestAnimationFrame(t)}(),n(),()=>{var e;h.forEach(e=>{a&&e.removeEventListener("scroll",n),s&&e.removeEventListener("resize",n)}),null==p||p(),null==(e=y)||e.disconnect(),y=null,d&&cancelAnimationFrame(l)}}let el=function(e){return void 0===e&&(e=0),{name:"offset",options:e,async fn(t){var n,r;let{x:i,y:o,placement:l,middlewareData:u}=t,a=await N(t,e);return l===(null==(n=u.offset)?void 0:n.placement)&&null!=(r=u.arrow)&&r.alignmentOffset?{}:{x:i+a.x,y:o+a.y,data:{...a,placement:l}}}}},eu=function(e){return void 0===e&&(e={}),{name:"shift",options:e,async fn(t){let{x:n,y:r,placement:l}=t,{mainAxis:u=!0,crossAxis:a=!1,limiter:s={fn:e=>{let{x:t,y:n}=e;return{x:t,y:n}}},...c}=f(e,t),m={x:n,y:r},p=await T(t,c),y=g(d(l)),v=h(y),w=m[v],b=m[y];if(u){let e="y"===v?"top":"left",t="y"===v?"bottom":"right",n=w+p[e],r=w-p[t];w=o(n,i(w,r))}if(a){let e="y"===y?"top":"left",t="y"===y?"bottom":"right",n=b+p[e],r=b-p[t];b=o(n,i(b,r))}let x=s.fn({...t,[v]:w,[y]:b});return{...x,data:{x:x.x-n,y:x.y-r,enabled:{[v]:u,[y]:a}}}}}},ea=function(e){return void 0===e&&(e={}),{name:"flip",options:e,async fn(t){var n,r,i,o,l;let{placement:u,middlewareData:a,rects:s,initialPlacement:c,platform:w,elements:b}=t,{mainAxis:x=!0,crossAxis:E=!0,fallbackPlacements:R,fallbackStrategy:S="bestFit",fallbackAxisSideDirection:N="none",flipAlignment:A=!0,...L}=f(e,t);if(null!=(n=a.arrow)&&n.alignmentOffset)return{};let O=d(u),C=g(c),k=d(c)===c,M=await (null==w.isRTL?void 0:w.isRTL(b.floating)),$=R||(k||!A?[v(c)]:function(e){let t=v(e);return[y(e),t,y(t)]}(c)),D="none"!==N;!R&&D&&$.push(...function(e,t,n,r){let i=m(e),o=function(e,t,n){let r=["left","right"],i=["right","left"];switch(e){case"top":case"bottom":if(n)return t?i:r;return t?r:i;case"left":case"right":return t?["top","bottom"]:["bottom","top"];default:return[]}}(d(e),"start"===n,r);return i&&(o=o.map(e=>e+"-"+i),t&&(o=o.concat(o.map(y)))),o}(c,A,N,M));let P=[c,...$],U=await T(t,L),_=[],F=(null==(r=a.flip)?void 0:r.overflows)||[];if(x&&_.push(U[O]),E){let e=function(e,t,n){void 0===n&&(n=!1);let r=m(e),i=h(g(e)),o=p(i),l="x"===i?r===(n?"end":"start")?"right":"left":"start"===r?"bottom":"top";return t.reference[o]>t.floating[o]&&(l=v(l)),[l,v(l)]}(u,s,M);_.push(U[e[0]],U[e[1]])}if(F=[...F,{placement:u,overflows:_}],!_.every(e=>e<=0)){let e=((null==(i=a.flip)?void 0:i.index)||0)+1,t=P[e];if(t)return{data:{index:e,overflows:F},reset:{placement:t}};let n=null==(o=F.filter(e=>e.overflows[0]<=0).sort((e,t)=>e.overflows[1]-t.overflows[1])[0])?void 0:o.placement;if(!n)switch(S){case"bestFit":{let e=null==(l=F.filter(e=>{if(D){let t=g(e.placement);return t===C||"y"===t}return!0}).map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)]).sort((e,t)=>e[1]-t[1])[0])?void 0:l[0];e&&(n=e);break}case"initialPlacement":n=c}if(u!==n)return{reset:{placement:n}}}return{}}}},es=function(e){return void 0===e&&(e={}),{name:"size",options:e,async fn(t){var n,r;let l,u;let{placement:a,rects:s,platform:c,elements:h}=t,{apply:p=()=>{},...y}=f(e,t),v=await T(t,y),w=d(a),b=m(a),x="y"===g(a),{width:E,height:R}=s.floating;"top"===w||"bottom"===w?(l=w,u=b===(await (null==c.isRTL?void 0:c.isRTL(h.floating))?"start":"end")?"left":"right"):(u=w,l="end"===b?"top":"bottom");let S=R-v.top-v.bottom,N=E-v.left-v.right,A=i(R-v[l],S),L=i(E-v[u],N),O=!t.middlewareData.shift,C=A,k=L;if(null!=(n=t.middlewareData.shift)&&n.enabled.x&&(k=N),null!=(r=t.middlewareData.shift)&&r.enabled.y&&(C=S),O&&!b){let e=o(v.left,0),t=o(v.right,0),n=o(v.top,0),r=o(v.bottom,0);x?k=E-2*(0!==e||0!==t?e+t:o(v.left,v.right)):C=R-2*(0!==n||0!==r?n+r:o(v.top,v.bottom))}await p({...t,availableWidth:k,availableHeight:C});let M=await c.getDimensions(h.floating);return E!==M.width||R!==M.height?{reset:{rects:!0}}:{}}}},ec=function(e){return void 0===e&&(e={}),{name:"hide",options:e,async fn(t){let{rects:n}=t,{strategy:r="referenceHidden",...i}=f(e,t);switch(r){case"referenceHidden":{let e=R(await T(t,{...i,elementContext:"reference"}),n.reference);return{data:{referenceHiddenOffsets:e,referenceHidden:S(e)}}}case"escaped":{let e=R(await T(t,{...i,altBoundary:!0}),n.floating);return{data:{escapedOffsets:e,escaped:S(e)}}}default:return{}}}}},ef=e=>({name:"arrow",options:e,async fn(t){let{x:n,y:r,placement:l,rects:u,platform:a,elements:s,middlewareData:c}=t,{element:d,padding:y=0}=f(e,t)||{};if(null==d)return{};let v=w(y),b={x:n,y:r},x=h(g(l)),E=p(x),T=await a.getDimensions(d),R="y"===x,S=R?"clientHeight":"clientWidth",N=u.reference[E]+u.reference[x]-b[x]-u.floating[E],A=b[x]-u.reference[x],L=await (null==a.getOffsetParent?void 0:a.getOffsetParent(d)),O=L?L[S]:0;O&&await (null==a.isElement?void 0:a.isElement(L))||(O=s.floating[S]||u.floating[E]);let C=O/2-T[E]/2-1,k=i(v[R?"top":"left"],C),M=i(v[R?"bottom":"right"],C),$=O-T[E]-M,D=O/2-T[E]/2+(N/2-A/2),P=o(k,i(D,$)),U=!c.arrow&&null!=m(l)&&D!==P&&u.reference[E]/2-(D<k?k:M)-T[E]/2<0,_=U?D<k?D-k:D-$:0;return{[x]:b[x]+_,data:{[x]:P,centerOffset:D-P-_,...U&&{alignmentOffset:_}},reset:U}}}),ed=function(e){return void 0===e&&(e={}),{options:e,fn(t){let{x:n,y:r,placement:i,rects:o,middlewareData:l}=t,{offset:u=0,mainAxis:a=!0,crossAxis:s=!0}=f(e,t),c={x:n,y:r},m=g(i),p=h(m),y=c[p],v=c[m],w=f(u,t),b="number"==typeof w?{mainAxis:w,crossAxis:0}:{mainAxis:0,crossAxis:0,...w};if(a){let e="y"===p?"height":"width",t=o.reference[p]-o.floating[e]+b.mainAxis,n=o.reference[p]+o.reference[e]-b.mainAxis;y<t?y=t:y>n&&(y=n)}if(s){var x,E;let e="y"===p?"width":"height",t=["top","left"].includes(d(i)),n=o.reference[m]-o.floating[e]+(t&&(null==(x=l.offset)?void 0:x[m])||0)+(t?0:b.crossAxis),r=o.reference[m]+o.reference[e]+(t?0:(null==(E=l.offset)?void 0:E[m])||0)-(t?b.crossAxis:0);v<n?v=n:v>r&&(v=r)}return{[p]:y,[m]:v}}}},em=(e,t,n)=>{let r=new Map,i={platform:ei,...n},o={...i.platform,_c:r};return E(e,t,{...i,platform:o})}},6932:(e,t,n)=>{n.d(t,{BN:()=>h,ER:()=>p,Ej:()=>y,UE:()=>w,UU:()=>g,cY:()=>m,jD:()=>v,we:()=>f});var r=n(7205),i=n(2115),o=n(7650),l="undefined"!=typeof document?i.useLayoutEffect:i.useEffect;function u(e,t){let n,r,i;if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if((n=e.length)!==t.length)return!1;for(r=n;0!=r--;)if(!u(e[r],t[r]))return!1;return!0}if((n=(i=Object.keys(e)).length)!==Object.keys(t).length)return!1;for(r=n;0!=r--;)if(!({}).hasOwnProperty.call(t,i[r]))return!1;for(r=n;0!=r--;){let n=i[r];if(("_owner"!==n||!e.$$typeof)&&!u(e[n],t[n]))return!1}return!0}return e!=e&&t!=t}function a(e){return"undefined"==typeof window?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function s(e,t){let n=a(e);return Math.round(t*n)/n}function c(e){let t=i.useRef(e);return l(()=>{t.current=e}),t}function f(e){void 0===e&&(e={});let{placement:t="bottom",strategy:n="absolute",middleware:f=[],platform:d,elements:{reference:m,floating:h}={},transform:p=!0,whileElementsMounted:g,open:y}=e,[v,w]=i.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[b,x]=i.useState(f);u(b,f)||x(f);let[E,T]=i.useState(null),[R,S]=i.useState(null),N=i.useCallback(e=>{e!==C.current&&(C.current=e,T(e))},[]),A=i.useCallback(e=>{e!==k.current&&(k.current=e,S(e))},[]),L=m||E,O=h||R,C=i.useRef(null),k=i.useRef(null),M=i.useRef(v),$=null!=g,D=c(g),P=c(d),U=c(y),_=i.useCallback(()=>{if(!C.current||!k.current)return;let e={placement:t,strategy:n,middleware:b};P.current&&(e.platform=P.current),(0,r.rD)(C.current,k.current,e).then(e=>{let t={...e,isPositioned:!1!==U.current};F.current&&!u(M.current,t)&&(M.current=t,o.flushSync(()=>{w(t)}))})},[b,t,n,P,U]);l(()=>{!1===y&&M.current.isPositioned&&(M.current.isPositioned=!1,w(e=>({...e,isPositioned:!1})))},[y]);let F=i.useRef(!1);l(()=>(F.current=!0,()=>{F.current=!1}),[]),l(()=>{if(L&&(C.current=L),O&&(k.current=O),L&&O){if(D.current)return D.current(L,O,_);_()}},[L,O,_,D,$]);let I=i.useMemo(()=>({reference:C,floating:k,setReference:N,setFloating:A}),[N,A]),j=i.useMemo(()=>({reference:L,floating:O}),[L,O]),H=i.useMemo(()=>{let e={position:n,left:0,top:0};if(!j.floating)return e;let t=s(j.floating,v.x),r=s(j.floating,v.y);return p?{...e,transform:"translate("+t+"px, "+r+"px)",...a(j.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:t,top:r}},[n,p,j.floating,v.x,v.y]);return i.useMemo(()=>({...v,update:_,refs:I,elements:j,floatingStyles:H}),[v,_,I,j,H])}let d=e=>({name:"arrow",options:e,fn(t){let{element:n,padding:i}="function"==typeof e?e(t):e;return n&&({}).hasOwnProperty.call(n,"current")?null!=n.current?(0,r.UE)({element:n.current,padding:i}).fn(t):{}:n?(0,r.UE)({element:n,padding:i}).fn(t):{}}}),m=(e,t)=>({...(0,r.cY)(e),options:[e,t]}),h=(e,t)=>({...(0,r.BN)(e),options:[e,t]}),p=(e,t)=>({...(0,r.ER)(e),options:[e,t]}),g=(e,t)=>({...(0,r.UU)(e),options:[e,t]}),y=(e,t)=>({...(0,r.Ej)(e),options:[e,t]}),v=(e,t)=>({...(0,r.jD)(e),options:[e,t]}),w=(e,t)=>({...d(e),options:[e,t]})},3610:(e,t,n)=>{n.d(t,{m:()=>r});function r(e,t,{checkForDefaultPrevented:n=!0}={}){return function(r){if(e?.(r),!1===n||!r.defaultPrevented)return t?.(r)}}},8166:(e,t,n)=>{n.d(t,{A:()=>o});var r=n(2115),i=n(5155);function o(e,t=[]){let n=[],l=()=>{let t=n.map(e=>r.createContext(e));return function(n){let i=n?.[e]||t;return r.useMemo(()=>({[`__scope${e}`]:{...n,[e]:i}}),[n,i])}};return l.scopeName=e,[function(t,o){let l=r.createContext(o),u=n.length;n=[...n,o];let a=t=>{let{scope:n,children:o,...a}=t,s=n?.[e]?.[u]||l,c=r.useMemo(()=>a,Object.values(a));return(0,i.jsx)(s.Provider,{value:c,children:o})};return a.displayName=t+"Provider",[a,function(n,i){let a=i?.[e]?.[u]||l,s=r.useContext(a);if(s)return s;if(void 0!==o)return o;throw Error(`\`${n}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let n=()=>{let n=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let i=n.reduce((t,{useScope:n,scopeName:r})=>{let i=n(e)[`__scope${r}`];return{...t,...i}},{});return r.useMemo(()=>({[`__scope${t.scopeName}`]:i}),[i])}};return n.scopeName=t.scopeName,n}(l,...t)]}},7668:(e,t,n)=>{n.d(t,{B:()=>a});var r,i=n(2115),o=n(6611),l=(r||(r=n.t(i,2)))["useId".toString()]||(()=>void 0),u=0;function a(e){let[t,n]=i.useState(l());return(0,o.N)(()=>{e||n(e=>e??String(u++))},[e]),e||(t?`radix-${t}`:"")}},7028:(e,t,n)=>{n.d(t,{C:()=>l});var r=n(2115),i=n(8068),o=n(6611),l=e=>{let{present:t,children:n}=e,l=function(e){var t,n;let[i,l]=r.useState(),a=r.useRef({}),s=r.useRef(e),c=r.useRef("none"),[f,d]=(t=e?"mounted":"unmounted",n={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},r.useReducer((e,t)=>{let r=n[e][t];return null!=r?r:e},t));return r.useEffect(()=>{let e=u(a.current);c.current="mounted"===f?e:"none"},[f]),(0,o.N)(()=>{let t=a.current,n=s.current;if(n!==e){let r=c.current,i=u(t);e?d("MOUNT"):"none"===i||(null==t?void 0:t.display)==="none"?d("UNMOUNT"):n&&r!==i?d("ANIMATION_OUT"):d("UNMOUNT"),s.current=e}},[e,d]),(0,o.N)(()=>{if(i){var e;let t;let n=null!==(e=i.ownerDocument.defaultView)&&void 0!==e?e:window,r=e=>{let r=u(a.current).includes(e.animationName);if(e.target===i&&r&&(d("ANIMATION_END"),!s.current)){let e=i.style.animationFillMode;i.style.animationFillMode="forwards",t=n.setTimeout(()=>{"forwards"===i.style.animationFillMode&&(i.style.animationFillMode=e)})}},o=e=>{e.target===i&&(c.current=u(a.current))};return i.addEventListener("animationstart",o),i.addEventListener("animationcancel",r),i.addEventListener("animationend",r),()=>{n.clearTimeout(t),i.removeEventListener("animationstart",o),i.removeEventListener("animationcancel",r),i.removeEventListener("animationend",r)}}d("ANIMATION_END")},[i,d]),{isPresent:["mounted","unmountSuspended"].includes(f),ref:r.useCallback(e=>{e&&(a.current=getComputedStyle(e)),l(e)},[])}}(t),a="function"==typeof n?n({present:l.isPresent}):r.Children.only(n),s=(0,i.s)(l.ref,function(e){var t,n;let r=null===(t=Object.getOwnPropertyDescriptor(e.props,"ref"))||void 0===t?void 0:t.get,i=r&&"isReactWarning"in r&&r.isReactWarning;return i?e.ref:(i=(r=null===(n=Object.getOwnPropertyDescriptor(e,"ref"))||void 0===n?void 0:n.get)&&"isReactWarning"in r&&r.isReactWarning)?e.props.ref:e.props.ref||e.ref}(a));return"function"==typeof n||l.isPresent?r.cloneElement(a,{ref:s}):null};function u(e){return(null==e?void 0:e.animationName)||"none"}l.displayName="Presence"},3360:(e,t,n)=>{n.d(t,{hO:()=>a,sG:()=>u});var r=n(2115),i=n(7650),o=n(2317),l=n(5155),u=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"].reduce((e,t)=>{let n=r.forwardRef((e,n)=>{let{asChild:r,...i}=e,u=r?o.DX:t;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,l.jsx)(u,{...i,ref:n})});return n.displayName=`Primitive.${t}`,{...e,[t]:n}},{});function a(e,t){e&&i.flushSync(()=>e.dispatchEvent(t))}},1524:(e,t,n)=>{n.d(t,{c:()=>i});var r=n(2115);function i(e){let t=r.useRef(e);return r.useEffect(()=>{t.current=e}),r.useMemo(()=>(...e)=>t.current?.(...e),[])}},1488:(e,t,n)=>{n.d(t,{i:()=>o});var r=n(2115),i=n(1524);function o({prop:e,defaultProp:t,onChange:n=()=>{}}){let[o,l]=function({defaultProp:e,onChange:t}){let n=r.useState(e),[o]=n,l=r.useRef(o),u=(0,i.c)(t);return r.useEffect(()=>{l.current!==o&&(u(o),l.current=o)},[o,l,u]),n}({defaultProp:t,onChange:n}),u=void 0!==e,a=u?e:o,s=(0,i.c)(n);return[a,r.useCallback(t=>{if(u){let n="function"==typeof t?t(e):t;n!==e&&s(n)}else l(t)},[u,e,l,s])]}},6611:(e,t,n)=>{n.d(t,{N:()=>i});var r=n(2115),i=globalThis?.document?r.useLayoutEffect:()=>{}},7510:(e,t,n)=>{n.d(t,{X:()=>o});var r=n(2115),i=n(6611);function o(e){let[t,n]=r.useState(void 0);return(0,i.N)(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let r,i;if(!Array.isArray(t)||!t.length)return;let o=t[0];if("borderBoxSize"in o){let e=o.borderBoxSize,t=Array.isArray(e)?e[0]:e;r=t.inlineSize,i=t.blockSize}else r=e.offsetWidth,i=e.offsetHeight;n({width:r,height:i})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}n(void 0)},[e]),t}}}]);