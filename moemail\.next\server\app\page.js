(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[974],{65521:e=>{"use strict";e.exports=require("node:async_hooks")},25356:e=>{"use strict";e.exports=require("node:buffer")},34012:(e,t,r)=>{"use strict";r.r(t),r.d(t,{ComponentMod:()=>M,default:()=>E});var a,s={};r.r(s),r.d(s,{ClientPageRoot:()=>p.Fy,ClientSegmentRoot:()=>p.pl,GlobalError:()=>d.default,HTTPAccessFallbackBoundary:()=>p.nQ,LayoutRouter:()=>p.C3,MetadataBoundary:()=>p.qB,OutletBoundary:()=>p.Cr,Postpone:()=>p.fK,RenderFromTemplateContext:()=>p.IY,ViewportBoundary:()=>p.PX,__next_app__:()=>f,actionAsyncStorage:()=>p.sc,collectSegmentData:()=>p.Uy,createMetadataComponents:()=>p.IB,createPrerenderParamsForClientSegment:()=>p.lu,createPrerenderSearchParamsForClientPage:()=>p.jO,createServerParamsForMetadata:()=>p.Kx,createServerParamsForServerSegment:()=>p.LV,createServerSearchParamsForMetadata:()=>p.mh,createServerSearchParamsForServerPage:()=>p.Vv,createTemporaryReferenceSet:()=>p.XI,decodeAction:()=>p.Jk,decodeFormState:()=>p.Am,decodeReply:()=>p.X$,pages:()=>h,patchFetch:()=>p.V5,preconnect:()=>p.kZ,preloadFont:()=>p.PY,preloadStyle:()=>p.vI,prerender:()=>p.CR,renderToReadableStream:()=>p.WK,routeModule:()=>g,serverHooks:()=>p.ge,taintObjectReference:()=>p.N2,tree:()=>u,workAsyncStorage:()=>p.J_,workUnitAsyncStorage:()=>p.FP}),r(79556);var i=r(19022),o=r(58661),n=r(20809),l=r(11939),c=r(13348),m=r(14139),d=r(86867),p=r(71102);let u=["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,46576)),"F:\\CODE\\Project\\tempmail\\moemail\\app\\page.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,27711)),"F:\\CODE\\Project\\tempmail\\moemail\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,64913)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.bind(r,2412)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.bind(r,77757)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],h=["F:\\CODE\\Project\\tempmail\\moemail\\app\\page.tsx"],f={require:r,loadChunk:()=>Promise.resolve()},g=new c.AppPageRouteModule({definition:{kind:m.A.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:u}});var x=r(18058),v=r(72495),b=r(75376);globalThis.__nextCacheHandlers||(globalThis.__nextCacheHandlers={});let y=e=>e?JSON.parse(e):void 0,j=self.__BUILD_MANIFEST,N=y(self.__REACT_LOADABLE_MANIFEST),C=null==(a=self.__RSC_MANIFEST)?void 0:a["/page"],P=y(self.__RSC_SERVER_MANIFEST),w=y(self.__NEXT_FONT_MANIFEST),S=y(self.__INTERCEPTION_ROUTE_REWRITE_MANIFEST)??[];C&&P&&(0,v.fQ)({page:"/page",clientReferenceManifest:C,serverActionsManifest:P,serverModuleMap:(0,b.e)({serverActionsManifest:P})});let k=(0,o.R)({pagesType:x.g.APP,dev:!1,page:"/page",appMod:null,pageMod:s,errorMod:null,error500Mod:null,Document:null,buildManifest:j,renderToHTML:l.W,reactLoadableManifest:N,clientReferenceManifest:C,serverActionsManifest:P,serverActions:void 0,subresourceIntegrityManifest:void 0,config:{env:{},eslint:{ignoreDuringBuilds:!1},typescript:{ignoreBuildErrors:!1,tsconfigPath:"tsconfig.json"},distDir:".next",cleanDistDir:!0,assetPrefix:"",cacheMaxMemorySize:0x3200000,configOrigin:"next.config.ts",useFileSystemPublicRoutes:!0,generateEtags:!0,pageExtensions:["tsx","ts","jsx","js"],poweredByHeader:!0,compress:!0,images:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",loaderFile:"",domains:[],disableStaticImages:!1,minimumCacheTTL:60,formats:["image/webp"],dangerouslyAllowSVG:!1,contentSecurityPolicy:"script-src 'none'; frame-src 'none'; sandbox;",contentDispositionType:"attachment",remotePatterns:[{protocol:"https",hostname:"avatars.githubusercontent.com"}],unoptimized:!1},devIndicators:{appIsrStatus:!0,buildActivity:!0,buildActivityPosition:"bottom-right"},onDemandEntries:{maxInactiveAge:6e4,pagesBufferLength:5},amp:{canonicalBase:""},basePath:"",sassOptions:{},trailingSlash:!1,i18n:null,productionBrowserSourceMaps:!1,excludeDefaultMomentLocales:!0,serverRuntimeConfig:{},publicRuntimeConfig:{},reactProductionProfiling:!1,reactStrictMode:null,reactMaxHeadersLength:6e3,httpAgentOptions:{keepAlive:!0},logging:{},expireTime:31536e3,staticPageGenerationTimeout:60,modularizeImports:{"@mui/icons-material":{transform:"@mui/icons-material/{{member}}"},lodash:{transform:"lodash/{{member}}"}},outputFileTracingRoot:"F:\\CODE\\Project\\tempmail\\moemail",experimental:{cacheLife:{default:{stale:300,revalidate:900,expire:0xfffffffe},seconds:{stale:0,revalidate:1,expire:60},minutes:{stale:300,revalidate:60,expire:3600},hours:{stale:300,revalidate:3600,expire:86400},days:{stale:300,revalidate:86400,expire:604800},weeks:{stale:300,revalidate:604800,expire:2592e3},max:{stale:300,revalidate:2592e3,expire:0xfffffffe}},cacheHandlers:{},cssChunking:!0,multiZoneDraftMode:!1,appNavFailHandling:!1,prerenderEarlyExit:!0,serverMinification:!0,serverSourceMaps:!1,linkNoTouchStart:!1,caseSensitiveRoutes:!1,clientSegmentCache:!1,preloadEntriesOnStart:!0,clientRouterFilter:!0,clientRouterFilterRedirects:!1,fetchCacheKeyPrefix:"",middlewarePrefetch:"flexible",optimisticClientCache:!0,manualClientBasePath:!1,cpus:11,memoryBasedWorkersCount:!1,imgOptConcurrency:null,imgOptTimeoutInSeconds:7,imgOptMaxInputPixels:0xfff8001,imgOptSequentialRead:null,isrFlushToDisk:!0,workerThreads:!1,optimizeCss:!1,nextScriptWorkers:!1,scrollRestoration:!1,externalDir:!1,disableOptimizedLoading:!1,gzipSize:!0,craCompat:!1,esmExternals:!0,fullySpecified:!1,swcTraceProfiling:!1,forceSwcTransforms:!1,largePageDataBytes:128e3,turbo:{root:"F:\\CODE\\Project\\tempmail\\moemail"},typedRoutes:!1,typedEnv:!1,parallelServerCompiles:!1,parallelServerBuildTraces:!1,ppr:!1,authInterrupts:!1,reactOwnerStack:!1,webpackMemoryOptimizations:!1,optimizeServerReact:!0,useEarlyImport:!1,staleTimes:{dynamic:0,static:300},serverComponentsHmrCache:!0,staticGenerationMaxConcurrency:8,staticGenerationMinPagesPerWorker:25,dynamicIO:!1,inlineCss:!1,optimizePackageImports:["lucide-react","date-fns","lodash-es","ramda","antd","react-bootstrap","ahooks","@ant-design/icons","@headlessui/react","@headlessui-float/react","@heroicons/react/20/solid","@heroicons/react/24/solid","@heroicons/react/24/outline","@visx/visx","@tremor/react","rxjs","@mui/material","@mui/icons-material","recharts","react-use","effect","@effect/schema","@effect/platform","@effect/platform-node","@effect/platform-browser","@effect/platform-bun","@effect/sql","@effect/sql-mssql","@effect/sql-mysql2","@effect/sql-pg","@effect/sql-squlite-node","@effect/sql-squlite-bun","@effect/sql-squlite-wasm","@effect/sql-squlite-react-native","@effect/rpc","@effect/rpc-http","@effect/typeclass","@effect/experimental","@effect/opentelemetry","@material-ui/core","@material-ui/icons","@tabler/icons-react","mui-core","react-icons/ai","react-icons/bi","react-icons/bs","react-icons/cg","react-icons/ci","react-icons/di","react-icons/fa","react-icons/fa6","react-icons/fc","react-icons/fi","react-icons/gi","react-icons/go","react-icons/gr","react-icons/hi","react-icons/hi2","react-icons/im","react-icons/io","react-icons/io5","react-icons/lia","react-icons/lib","react-icons/lu","react-icons/md","react-icons/pi","react-icons/ri","react-icons/rx","react-icons/si","react-icons/sl","react-icons/tb","react-icons/tfi","react-icons/ti","react-icons/vsc","react-icons/wi"]},bundlePagesRouterDependencies:!1,configFile:"F:\\CODE\\Project\\tempmail\\moemail\\next.config.ts",configFileName:"next.config.ts"},buildId:process.env.__NEXT_BUILD_ID,nextFontManifest:w,incrementalCacheHandler:null,interceptionRouteRewrites:S}),M=s;function E(e){return(0,i.O)({...e,IncrementalCache:n.N,handler:k})}},5294:(e,t,r)=>{Promise.resolve().then(r.bind(r,44245)),Promise.resolve().then(r.bind(r,55041)),Promise.resolve().then(r.bind(r,34980)),Promise.resolve().then(r.bind(r,26336))},68342:(e,t,r)=>{Promise.resolve().then(r.bind(r,37357)),Promise.resolve().then(r.bind(r,87209)),Promise.resolve().then(r.bind(r,56588)),Promise.resolve().then(r.bind(r,52744))},37357:(e,t,r)=>{"use strict";r.d(t,{SignButton:()=>d});var a=r(37785),s=r(277),i=r(34655),o=r(31648),n=r(71443),l=r(90842),c=r(81505),m=r(39086);function d({size:e="default"}){let t=(0,l.rd)(),{data:r,status:d}=(0,o.wV)();return"loading"===d?(0,a.jsx)("div",{className:"h-9"}):r?.user?(0,a.jsxs)("div",{className:"flex items-center gap-4",children:[(0,a.jsxs)(c.A,{href:"/profile",className:"flex items-center gap-2 hover:opacity-80 transition-opacity",children:[r.user.image&&(0,a.jsx)(i.A,{src:r.user.image,alt:r.user.name||"用户头像",width:24,height:24,className:"rounded-full"}),(0,a.jsx)("span",{className:"text-sm",children:r.user.name})]}),(0,a.jsx)(s.$,{onClick:()=>(0,o.CI)({callbackUrl:"/"}),variant:"outline",className:(0,m.cn)("flex-shrink-0","lg"===e?"px-8":""),size:e,children:"登出"})]}):(0,a.jsxs)(s.$,{onClick:()=>t.push("/login"),className:(0,m.cn)("gap-2","lg"===e?"px-8":""),size:e,children:[(0,a.jsx)(n.A,{className:"lg"===e?"w-5 h-5":"w-4 h-4"}),"登录/注册"]})}},87209:(e,t,r)=>{"use strict";r.d(t,{ActionButton:()=>l});var a=r(37785),s=r(277),i=r(43178),o=r(90842),n=r(37357);function l({isLoggedIn:e}){let t=(0,o.rd)();return e?(0,a.jsxs)(s.$,{size:"lg",onClick:()=>t.push("/moe"),className:"gap-2 bg-primary hover:bg-primary/90 text-white px-8",children:[(0,a.jsx)(i.A,{className:"w-5 h-5"}),"进入邮箱"]}):(0,a.jsx)(n.SignButton,{size:"lg"})}},56588:(e,t,r)=>{"use strict";r.d(t,{ThemeToggle:()=>l});var a=r(37785),s=r(72883),i=r(85480),o=r(38182),n=r(277);function l(){let{theme:e,setTheme:t}=(0,o.D)();return(0,a.jsxs)(n.$,{variant:"ghost",size:"icon",onClick:()=>t("light"===e?"dark":"light"),className:"rounded-full",children:[(0,a.jsx)(s.A,{className:"h-5 w-5 rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0"}),(0,a.jsx)(i.A,{className:"absolute h-5 w-5 rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100"}),(0,a.jsx)("span",{className:"sr-only",children:"切换主题"})]})}},52744:(e,t,r)=>{"use strict";r.d(t,{Logo:()=>i});var a=r(37785),s=r(81505);function i(){return(0,a.jsxs)(s.A,{href:"/",className:"flex items-center gap-2 hover:opacity-80 transition-opacity",children:[(0,a.jsx)("div",{className:"relative w-8 h-8",children:(0,a.jsx)("div",{className:"absolute inset-0 grid grid-cols-8 grid-rows-8 gap-px",children:(0,a.jsxs)("svg",{width:"32",height:"32",viewBox:"0 0 32 32",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:"text-primary",children:[(0,a.jsx)("path",{d:"M4 8h24v16H4V8z",className:"fill-primary/20"}),(0,a.jsx)("path",{d:"M4 8h24v2H4V8zM4 22h24v2H4v-2z",className:"fill-primary"}),(0,a.jsx)("path",{d:"M14 12h4v4h-4v-4zM12 14h2v4h-2v-4zM18 14h2v4h-2v-4zM14 18h4v2h-4v-2z",className:"fill-primary"}),(0,a.jsx)("path",{d:"M4 8l12 8 12-8",className:"stroke-primary stroke-2",fill:"none"}),(0,a.jsx)("path",{d:"M8 18h2v2H8v-2zM22 18h2v2h-2v-2z",className:"fill-primary/60"}),(0,a.jsx)("path",{d:"M8 14h2v2H8v-2zM22 14h2v2h-2v-2z",className:"fill-primary/40"})]})})}),(0,a.jsx)("span",{className:"font-bold tracking-wider bg-clip-text text-transparent bg-gradient-to-r from-primary to-purple-600",children:"MoeMail"})]})}},44245:(e,t,r)=>{"use strict";r.d(t,{SignButton:()=>a});let a=(0,r(45710).YR)(function(){throw Error("Attempted to call SignButton() from the server but SignButton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"F:\\CODE\\Project\\tempmail\\moemail\\app\\components\\auth\\sign-button.tsx","SignButton")},55041:(e,t,r)=>{"use strict";r.d(t,{ActionButton:()=>a});let a=(0,r(45710).YR)(function(){throw Error("Attempted to call ActionButton() from the server but ActionButton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"F:\\CODE\\Project\\tempmail\\moemail\\app\\components\\home\\action-button.tsx","ActionButton")},96942:(e,t,r)=>{"use strict";r.d(t,{Y:()=>n});var a=r(4302),s=r(44245),i=r(34980),o=r(26336);function n(){return(0,a.jsx)("header",{className:"fixed top-0 left-0 right-0 z-50 h-16 bg-background/80 backdrop-blur-sm border-b",children:(0,a.jsx)("div",{className:"container mx-auto h-full px-4",children:(0,a.jsxs)("div",{className:"h-full flex items-center justify-between",children:[(0,a.jsx)(o.Logo,{}),(0,a.jsxs)("div",{className:"flex items-center gap-4",children:[(0,a.jsx)(i.ThemeToggle,{}),(0,a.jsx)(s.SignButton,{})]})]})})})}},34980:(e,t,r)=>{"use strict";r.d(t,{ThemeToggle:()=>a});let a=(0,r(45710).YR)(function(){throw Error("Attempted to call ThemeToggle() from the server but ThemeToggle is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"F:\\CODE\\Project\\tempmail\\moemail\\app\\components\\theme\\theme-toggle.tsx","ThemeToggle")},26336:(e,t,r)=>{"use strict";r.d(t,{Logo:()=>a});let a=(0,r(45710).YR)(function(){throw Error("Attempted to call Logo() from the server but Logo is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"F:\\CODE\\Project\\tempmail\\moemail\\app\\components\\ui\\logo.tsx","Logo")},46576:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>v,runtime:()=>x});var a=r(4302),s=r(96942),i=r(83553),o=r(86411);let n=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),l=(...e)=>e.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim();var c={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let m=(0,o.forwardRef)(({color:e="currentColor",size:t=24,strokeWidth:r=2,absoluteStrokeWidth:a,className:s="",children:i,iconNode:n,...m},d)=>(0,o.createElement)("svg",{ref:d,...c,width:t,height:t,stroke:e,strokeWidth:a?24*Number(r)/Number(t):r,className:l("lucide",s),...m},[...n.map(([e,t])=>(0,o.createElement)(e,t)),...Array.isArray(i)?i:[i]])),d=(e,t)=>{let r=(0,o.forwardRef)(({className:r,...a},s)=>(0,o.createElement)(m,{ref:s,iconNode:t,className:l(`lucide-${n(e)}`,r),...a}));return r.displayName=`${e}`,r},p=d("Shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]]),u=d("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]]),h=d("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]]);var f=r(55041);function g({icon:e,title:t,description:r}){return(0,a.jsx)("div",{className:"p-4 rounded border-2 border-primary/20 hover:border-primary/40 transition-colors bg-white/5 backdrop-blur",children:(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)("div",{className:"rounded-lg bg-primary/10 text-primary p-2",children:e}),(0,a.jsxs)("div",{className:"text-left",children:[(0,a.jsx)("h3",{className:"font-bold",children:t}),(0,a.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:r})]})]})})}let x="edge";async function v(){let e=await (0,i.j2)();return(0,a.jsx)("div",{className:"bg-gradient-to-b from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 h-screen",children:(0,a.jsxs)("div",{className:"container mx-auto h-full px-4 lg:px-8 max-w-[1600px]",children:[(0,a.jsx)(s.Y,{}),(0,a.jsx)("main",{className:"h-full",children:(0,a.jsxs)("div",{className:"h-[calc(100vh-4rem)] flex flex-col items-center justify-center text-center px-4 relative",children:[(0,a.jsx)("div",{className:"absolute inset-0 -z-10 bg-grid-primary/5"}),(0,a.jsxs)("div",{className:"w-full max-w-3xl mx-auto space-y-12 py-8",children:[(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("h1",{className:"text-3xl sm:text-4xl md:text-5xl font-bold tracking-wider",children:(0,a.jsx)("span",{className:"bg-clip-text text-transparent bg-gradient-to-r from-primary to-purple-600",children:"MoeMail"})}),(0,a.jsx)("p",{className:"text-lg sm:text-xl text-gray-600 dark:text-gray-300 tracking-wide",children:"萌萌哒临时邮箱服务"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 px-4 sm:px-0",children:[(0,a.jsx)(g,{icon:(0,a.jsx)(p,{className:"w-5 h-5"}),title:"隐私保护",description:"保护您的真实邮箱地址"}),(0,a.jsx)(g,{icon:(0,a.jsx)(u,{className:"w-5 h-5"}),title:"即时收件",description:"实时接收邮件通知"}),(0,a.jsx)(g,{icon:(0,a.jsx)(h,{className:"w-5 h-5"}),title:"自动过期",description:"到期自动失效"})]}),(0,a.jsx)("div",{className:"flex flex-col sm:flex-row items-center justify-center gap-4 px-4 sm:px-0",children:(0,a.jsx)(f.ActionButton,{isLoggedIn:!!e})})]})]})})]})})}}},e=>{var t=t=>e(e.s=t);e.O(0,[730,752,899,498,943,86,156,481],()=>t(34012));var r=e.O();(_ENTRIES="undefined"==typeof _ENTRIES?{}:_ENTRIES)["middleware_app/page"]=r}]);
//# sourceMappingURL=page.js.map