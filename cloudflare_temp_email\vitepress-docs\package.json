{"name": "temp-mail-docs", "private": true, "version": "0.10.0", "type": "module", "devDependencies": {"@types/node": "^22.15.19", "vitepress": "^1.6.3", "wrangler": "^4.15.2"}, "scripts": {"dev": "vitepress dev docs", "build": "vitepress build docs", "preview": "vitepress preview docs", "deploy": "npm run build && wrangler pages deploy ./docs/.vitepress/dist --project-name=temp-mail-docs --branch production"}, "dependencies": {"jszip": "^3.10.1"}, "packageManager": "pnpm@10.10.0+sha512.d615db246fe70f25dcfea6d8d73dee782ce23e2245e3c4f6f888249fb568149318637dca73c2c5c8ef2a4ca0d5657fb9567188bfab47f566d1ee6ce987815c39"}