{"version": 3, "file": "app/api/emails/[id]/route.js", "mappings": "sFAAA,8DCAA,oHGAA,6UFMO,IAAMA,EAAU,OAAM,eAEPC,EACpBC,CAAgB,CAChB,QAAEC,CAAM,CAAuC,EAE/C,IAAMC,EAAS,MAAMC,CAAAA,EAAAA,EAAAA,EAAAA,CAASA,GAE9B,GAAI,CAACD,EACH,MADW,CACJE,EAAAA,EAAYA,CAACC,IAAI,CACtB,CAAEC,MAAO,KAAM,EACf,CAAEC,OAAQ,GAAI,GAIlB,GAAI,CACF,IAAMC,EAAKC,CAAAA,EAAAA,EAAAA,CAAAA,CAAQA,GACb,IAAEC,CAAE,CAAE,CAAG,MAAMT,EASrB,GAAI,CAPU,MAAMO,CAOR,CAPWG,KAAK,CAACC,MAAM,CAACC,SAAS,CAAC,CAC5CC,MAAOC,CAAAA,EAAAA,EAAAA,EAAAA,CAAGA,CACRC,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACJ,EAAAA,MAAMA,CAACF,EAAE,CAAEA,GACdM,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACJ,EAAAA,MAAMA,CAACV,MAAM,CAAEA,GAEtB,GAGE,OAAOE,EAAAA,EAAYA,CAACC,IAAI,CACtB,CAAEC,MAAO,aAAc,EACvB,CAAEC,OAAQ,GAAI,GASlB,OANA,MAAMC,EAAGS,MAAM,CAACC,EAAAA,QAAQA,EACrBJ,KAAK,CAACE,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACE,EAAAA,QAAQA,CAACC,OAAO,CAAET,IAE9B,MAAMF,EAAGS,MAAM,CAACL,EAAAA,MAAMA,EACnBE,KAAK,CAACE,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACJ,EAAAA,MAAMA,CAACF,EAAE,CAAEA,IAEhBN,EAAAA,EAAYA,CAACC,IAAI,CAAC,CAAEe,QAAS,EAAK,EAC3C,CAAE,MAAOd,EAAO,CAEd,OADAe,QAAQf,KAAK,CAAC,0BAA2BA,GAClCF,EAAAA,EAAYA,CAACC,IAAI,CACtB,CAAEC,MAAO,QAAS,EAClB,CAAEC,OAAQ,GAAI,EAElB,CACF,CAIO,eAAee,EACpBtB,CAAgB,CAChB,QAAEC,CAAM,CAAuC,EAE/C,GAAM,cAAEsB,CAAY,CAAE,CAAG,IAAIC,IAAIxB,EAAQyB,GAAG,EACtCC,EAAYH,EAAaI,GAAG,CAAC,UAEnC,GAAI,CACF,IAAMnB,EAAKC,CAAAA,EAAAA,EAAAA,CAAAA,CAAQA,GACb,IAAEC,CAAE,CAAE,CAAG,MAAMT,EAEfC,EAAS,MAAMC,CAAAA,EAAAA,EAAAA,EAAAA,CAASA,GAE9B,GAAI,CAACD,EACH,MADW,CACJE,EAAAA,EAAYA,CAACC,IAAI,CACtB,CAAEC,MAAO,KAAM,EACf,CAAEC,OAAQ,GAAI,GAKlB,IAAMqB,EAAQ,MAAMpB,EAAGG,KAAK,CAACC,MAAM,CAACC,SAAS,CAAC,CAC5CC,MAAOE,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACJ,EAAAA,MAAMA,CAACF,EAAE,CAAEA,EACvB,GAEA,GAAI,CAACkB,EACH,KADU,EACHxB,EAAAA,EAAYA,CAACC,IAAI,CACtB,CAAEC,MAAO,OAAQ,EACjB,CAAEC,OAAQ,GAAI,GAKlB,IAAMsB,EAAUD,EAAM1B,MAAM,GAAKA,EAG7B4B,GAAU,EAWd,GAVKD,GAOHC,GAAU,CAAC,CANK,CADJ,KACUtB,EAAGG,KAAK,CAACoB,iBAAiB,CAAClB,SAAS,CAAC,CACzDC,MAAOC,CAAAA,EAAAA,EAAAA,EAAAA,CAAGA,CACRC,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACe,EAAAA,iBAAiBA,CAAC7B,MAAM,CAAEA,GAC7Bc,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACe,EAAAA,iBAAiBA,CAACZ,OAAO,CAAET,GAElC,EACYsB,EAGV,CAACH,GAAW,CAACC,EACf,OAAO1B,EAAAA,EAAYA,CAACC,IAAI,CACtB,CAAEC,MAAO,OAAQ,EACjB,CAAEC,OAAQ,GAAI,GAIlB,IAAM0B,EAAiBjB,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACE,EAAAA,QAAQA,CAACC,OAAO,CAAET,GAEtCwB,EAAc,MAAM1B,EAAG2B,MAAM,CAAC,CAAEC,MAAOC,CAAAA,EAAAA,EAAAA,EAAAA,CAAW,CAAC,QAAQ,CAAC,GAC/DC,IAAI,CAACpB,EAAAA,QAAQA,EACbJ,KAAK,CAACmB,GACHM,EAAaC,OAAON,CAAW,CAAC,EAAE,CAACE,KAAK,EAExCK,EAAa,CAACR,EAAe,CAEnC,GAAIP,EAAW,CACb,GAAM,WAAEgB,CAAS,IAAEhC,CAAE,CAAE,CAAGiC,CAAAA,EAAAA,EAAAA,CAAAA,CAAYA,CAACjB,GACvCe,EAAWG,IAAI,CACb,CACAC,EAAAA,EAAAA,EAAAA,CAAEA,CACAC,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC5B,EAAAA,QAAQA,CAAC6B,SAFwB,CAEd,CAAE,IAAIC,KAAKN,IACjC3B,CAAAA,EAAAA,EAAAA,EAAAA,CAAGA,CACDC,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACE,EAAAA,QAAQA,CAAC6B,UAAU,CAAE,IAAIC,KAAKN,IACjCI,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC5B,EAAAA,QAAQA,CAACR,EAAE,CAAEA,KAIxB,CAEA,IAAMuC,EAAU,MAAMzC,EAAGG,KAAK,CAACO,QAAQ,CAACgC,QAAQ,CAAC,CAC/CpC,MAAOC,CAAAA,EAAAA,EAAAA,EAAAA,CAAGA,IAAI0B,GACdU,QAAS,CAACjC,EAAU,MAAEkC,CAAI,CAAE,GAAK,CAC/BA,EAAKlC,EAAS6B,UAAU,EACxBK,EAAKlC,EAASR,EAAE,EACjB,CACD2C,MAAOC,EACT,GAEMC,EAAUN,EAAQO,GAHH,GAGS,GAAGF,CAC3BG,EAAaF,EACfG,CAAAA,EAAAA,EAAAA,CAAAA,CAAYA,CACVT,CAAO,CAACK,GAAc,CAACP,QAAH,EAAa,CAACY,OAAO,GACzCV,CAAO,CAACK,GAAc,CAAC5C,EAAE,EAE3B,IAFsB,CAGpBkD,EAAcL,EAAUN,EAAQY,KAAK,CAAC,EA/F9B,CA+FiCP,GAAaL,EAE5D,OAAO7C,EAAAA,EAAYA,CAACC,IAAI,CAAC,CACvBa,SAAU0C,EAAYE,GAAG,CAACC,GAAQ,EAChCrD,CADgC,EAC5BqD,EAAIrD,EAAE,CACVsD,aAAcD,EAAIE,WAAW,CAC7BC,QAASH,EAAIG,OAAO,CACpBC,YAAaJ,EAAIhB,UAAU,CAACY,OAAO,GACrC,cACAF,EACAW,MAAO7B,CACT,EACF,CAAE,MAAOjC,EAAO,CAEd,OADAe,QAAQf,KAAK,CAAC,4BAA6BA,GACpCF,EAAAA,EAAYA,CAACC,IAAI,CACtB,CAAEC,MAAO,0BAA2B,EACpC,CAAEC,OAAQ,GAAI,EAElB,CACF,CCjKA,UAAwB,qBAAmB,EAC3C,YACA,KAAc,GAAS,WACvB,8BACA,4BACA,iBACA,sCACA,CAAK,CACL,0FACA,iBAVA,GAWA,QAAY,EACZ,CAAC,EAID,kBAAQ,wCAAsD,EAC9D,aACA,MAAW,QAAW,EACtB,mBACA,sBACA,CAAK,CACL,CCpBA,uEACA,EAFA,4BAEA,4BACA,MACI,QAA8B,EAClC,8BACA,0BACA,wBACA,gBAAyB,OAAqB,EAC9C,uBACA,CAAS,CACT,CAAK,EAEE,MAAqB,EAC5B,EAAe,GAAsB,MAAM,EAAkB,CAC7D,QAD6D,GAf7D,CAAoB,MAAQ,OAcM,CAdN,CAAW,sBAA2B,aAAe,kDAAyD,uOAAsQ,2RAAuT,kBAAkB,QAAQ,uDAA2D,0DAA8D,iBAAsB,gBAAkB,sEAAgF,kBAAoB,uCAA6C,MAAQ,iBAAmB,2BAA+B,gHAAkI,uBAAyB,8FAA0G,aAAiB,WAAa,sEAA6E,uBAAuB,iCAAmC,QAAQ,EAAE,SAAW,oBAAsB,QAAQ,GAAG,4EAAgF,WAAa,SAAW,2CAAiD,UAAY,+BAAqC,UAAY,oCAA0C,QAAU,uCAA6C,OAAS,yCAA+C,QAAU,0CAAiD,MAAQ,+CAAsD,iBAAmB,ywBAA+5B,4CAA8C,kNAAqQ,qBAAyB,s+CAA0/C,oIAiB7wJ,CAAC,CAAC,EAAC,qGClBI,SAASmD,EAAahB,CAAiB,CAAEhC,CAAU,EAExD,OAAO2D,EAAO/B,IAAD+B,CAAMC,KAAKC,SAAS,CADR,WAAE7B,KAAWhC,CAAG,IACA8D,QAAQ,CAAC,SACpD,CAEO,SAAS7B,EAAa8B,CAAc,EAEzC,OADaH,KAAKI,KAAK,CAACL,EAAO/B,IAAD+B,CAAMI,EAAQ,UAAUD,QAAQ,GAEhE", "sources": ["webpack://_N_E/external commonjs \"node:async_hooks\"", "webpack://_N_E/external commonjs \"node:buffer\"", "webpack://_N_E/./app/api/emails/[id]/route.ts", "webpack://_N_E/./app/api/emails/[id]/route.ts?9d8b", "webpack://_N_E/?ceaa", "webpack://_N_E/./app/lib/cursor.ts"], "sourcesContent": ["module.exports = require(\"node:async_hooks\");", "module.exports = require(\"node:buffer\");", "import { NextResponse } from \"next/server\"\r\nimport { createDb } from \"@/lib/db\"\r\nimport { emails, messages, userEmailBindings } from \"@/lib/schema\"\r\nimport { eq, and, lt, or, sql } from \"drizzle-orm\"\r\nimport { encodeCursor, decodeCursor } from \"@/lib/cursor\"\r\nimport { getUserId } from \"@/lib/apiKey\"\r\nexport const runtime = \"edge\"\r\n\r\nexport async function DELETE(\r\n  request: Request,\r\n  { params }: { params: Promise<{ id: string }> }\r\n) {\r\n  const userId = await getUserId()\r\n\r\n  if (!userId) {\r\n    return NextResponse.json(\r\n      { error: \"未授权\" },\r\n      { status: 401 }\r\n    )\r\n  }\r\n\r\n  try {\r\n    const db = createDb()\r\n    const { id } = await params\r\n    // 检查邮箱权限：只有临时邮箱的所有者可以删除\r\n    const email = await db.query.emails.findFirst({\r\n      where: and(\r\n        eq(emails.id, id),\r\n        eq(emails.userId, userId)\r\n      )\r\n    })\r\n\r\n    if (!email) {\r\n      return NextResponse.json(\r\n        { error: \"邮箱不存在或无权限删除\" },\r\n        { status: 403 }\r\n      )\r\n    }\r\n    await db.delete(messages)\r\n      .where(eq(messages.emailId, id))\r\n\r\n    await db.delete(emails)\r\n      .where(eq(emails.id, id))\r\n\r\n    return NextResponse.json({ success: true })\r\n  } catch (error) {\r\n    console.error('Failed to delete email:', error)\r\n    return NextResponse.json(\r\n      { error: \"删除邮箱失败\" },\r\n      { status: 500 }\r\n    )\r\n  }\r\n} \r\n\r\nconst PAGE_SIZE = 20\r\n\r\nexport async function GET(\r\n  request: Request,\r\n  { params }: { params: Promise<{ id: string }> }\r\n) {\r\n  const { searchParams } = new URL(request.url)\r\n  const cursorStr = searchParams.get('cursor')\r\n\r\n  try {\r\n    const db = createDb()\r\n    const { id } = await params\r\n\r\n    const userId = await getUserId()\r\n\r\n    if (!userId) {\r\n      return NextResponse.json(\r\n        { error: \"未授权\" },\r\n        { status: 401 }\r\n      )\r\n    }\r\n\r\n    // 检查邮箱权限：临时邮箱或绑定邮箱\r\n    const email = await db.query.emails.findFirst({\r\n      where: eq(emails.id, id)\r\n    })\r\n\r\n    if (!email) {\r\n      return NextResponse.json(\r\n        { error: \"邮箱不存在\" },\r\n        { status: 404 }\r\n      )\r\n    }\r\n\r\n    // 检查是否是用户的临时邮箱\r\n    const isOwner = email.userId === userId\r\n\r\n    // 检查是否是用户绑定的邮箱\r\n    let isBound = false\r\n    if (!isOwner) {\r\n      const binding = await db.query.userEmailBindings.findFirst({\r\n        where: and(\r\n          eq(userEmailBindings.userId, userId),\r\n          eq(userEmailBindings.emailId, id)\r\n        )\r\n      })\r\n      isBound = !!binding\r\n    }\r\n\r\n    if (!isOwner && !isBound) {\r\n      return NextResponse.json(\r\n        { error: \"无权限查看\" },\r\n        { status: 403 }\r\n      )\r\n    }\r\n\r\n    const baseConditions = eq(messages.emailId, id)\r\n\r\n    const totalResult = await db.select({ count: sql<number>`count(*)` })\r\n      .from(messages)\r\n      .where(baseConditions)\r\n    const totalCount = Number(totalResult[0].count)\r\n\r\n    const conditions = [baseConditions]\r\n\r\n    if (cursorStr) {\r\n      const { timestamp, id } = decodeCursor(cursorStr)\r\n      conditions.push(\r\n        // @ts-expect-error \"ignore the error\"\r\n        or(\r\n          lt(messages.receivedAt, new Date(timestamp)),\r\n          and(\r\n            eq(messages.receivedAt, new Date(timestamp)),\r\n            lt(messages.id, id)\r\n          )\r\n        )\r\n      )\r\n    }\r\n\r\n    const results = await db.query.messages.findMany({\r\n      where: and(...conditions),\r\n      orderBy: (messages, { desc }) => [\r\n        desc(messages.receivedAt),\r\n        desc(messages.id)\r\n      ],\r\n      limit: PAGE_SIZE + 1\r\n    })\r\n    \r\n    const hasMore = results.length > PAGE_SIZE\r\n    const nextCursor = hasMore \r\n      ? encodeCursor(\r\n          results[PAGE_SIZE - 1].receivedAt.getTime(),\r\n          results[PAGE_SIZE - 1].id\r\n        )\r\n      : null\r\n    const messageList = hasMore ? results.slice(0, PAGE_SIZE) : results\r\n\r\n    return NextResponse.json({ \r\n      messages: messageList.map(msg => ({\r\n        id: msg.id,\r\n        from_address: msg.fromAddress,\r\n        subject: msg.subject,\r\n        received_at: msg.receivedAt.getTime()\r\n      })),\r\n      nextCursor,\r\n      total: totalCount\r\n    })\r\n  } catch (error) {\r\n    console.error('Failed to fetch messages:', error)\r\n    return NextResponse.json(\r\n      { error: \"Failed to fetch messages\" },\r\n      { status: 500 }\r\n    )\r\n  }\r\n} ", "import { AppRouteRouteModule } from \"next/dist/server/route-modules/app-route/module.compiled\";\nimport { RouteKind } from \"next/dist/server/route-kind\";\nimport { patchFetch as _patchFetch } from \"next/dist/server/lib/patch-fetch\";\nimport * as userland from \"F:\\\\CODE\\\\Project\\\\tempmail\\\\moemail\\\\app\\\\api\\\\emails\\\\[id]\\\\route.ts\";\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new AppRouteRouteModule({\n    definition: {\n        kind: RouteKind.APP_ROUTE,\n        page: \"/api/emails/[id]/route\",\n        pathname: \"/api/emails/[id]\",\n        filename: \"route\",\n        bundlePath: \"app/api/emails/[id]/route\"\n    },\n    resolvedPagePath: \"F:\\\\CODE\\\\Project\\\\tempmail\\\\moemail\\\\app\\\\api\\\\emails\\\\[id]\\\\route.ts\",\n    nextConfigOutput,\n    userland\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return _patchFetch({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\nexport { routeModule, workAsyncStorage, workUnitAsyncStorage, serverHooks, patchFetch,  };\n\n//# sourceMappingURL=app-route.js.map", "var _self___RSC_MANIFEST;\nimport { createServerModuleMap } from \"next/dist/server/app-render/action-utils\";\nimport { setReferenceManifestsSingleton } from \"next/dist/server/app-render/encryption-utils\";\nimport { EdgeRouteModuleWrapper } from \"next/dist/server/web/edge-route-module-wrapper\";\n// Import the userland code.\nimport * as module from \"next-app-loader?name=app%2Fapi%2Femails%2F%5Bid%5D%2Froute&page=%2Fapi%2Femails%2F%5Bid%5D%2Froute&pagePath=private-next-app-dir%2Fapi%2Femails%2F%5Bid%5D%2Froute.ts&appDir=F%3A%5CCODE%5CProject%5Ctempmail%5Cmoemail%5Capp&appPaths=%2Fapi%2Femails%2F%5Bid%5D%2Froute&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!private-next-app-dir/api/emails/[id]/route.ts?__next_edge_ssr_entry__\";\nconst nextConfig = {\"env\":{},\"eslint\":{\"ignoreDuringBuilds\":false},\"typescript\":{\"ignoreBuildErrors\":false,\"tsconfigPath\":\"tsconfig.json\"},\"distDir\":\".next\",\"cleanDistDir\":true,\"assetPrefix\":\"\",\"cacheMaxMemorySize\":52428800,\"configOrigin\":\"next.config.ts\",\"useFileSystemPublicRoutes\":true,\"generateEtags\":true,\"pageExtensions\":[\"tsx\",\"ts\",\"jsx\",\"js\"],\"poweredByHeader\":true,\"compress\":true,\"images\":{\"deviceSizes\":[640,750,828,1080,1200,1920,2048,3840],\"imageSizes\":[16,32,48,64,96,128,256,384],\"path\":\"/_next/image\",\"loader\":\"default\",\"loaderFile\":\"\",\"domains\":[],\"disableStaticImages\":false,\"minimumCacheTTL\":60,\"formats\":[\"image/webp\"],\"dangerouslyAllowSVG\":false,\"contentSecurityPolicy\":\"script-src 'none'; frame-src 'none'; sandbox;\",\"contentDispositionType\":\"attachment\",\"remotePatterns\":[{\"protocol\":\"https\",\"hostname\":\"avatars.githubusercontent.com\"}],\"unoptimized\":false},\"devIndicators\":{\"appIsrStatus\":true,\"buildActivity\":true,\"buildActivityPosition\":\"bottom-right\"},\"onDemandEntries\":{\"maxInactiveAge\":60000,\"pagesBufferLength\":5},\"amp\":{\"canonicalBase\":\"\"},\"basePath\":\"\",\"sassOptions\":{},\"trailingSlash\":false,\"i18n\":null,\"productionBrowserSourceMaps\":false,\"excludeDefaultMomentLocales\":true,\"serverRuntimeConfig\":{},\"publicRuntimeConfig\":{},\"reactProductionProfiling\":false,\"reactStrictMode\":null,\"reactMaxHeadersLength\":6000,\"httpAgentOptions\":{\"keepAlive\":true},\"logging\":{},\"expireTime\":31536000,\"staticPageGenerationTimeout\":60,\"modularizeImports\":{\"@mui/icons-material\":{\"transform\":\"@mui/icons-material/{{member}}\"},\"lodash\":{\"transform\":\"lodash/{{member}}\"}},\"outputFileTracingRoot\":\"F:\\\\CODE\\\\Project\\\\tempmail\\\\moemail\",\"experimental\":{\"cacheLife\":{\"default\":{\"stale\":300,\"revalidate\":900,\"expire\":4294967294},\"seconds\":{\"stale\":0,\"revalidate\":1,\"expire\":60},\"minutes\":{\"stale\":300,\"revalidate\":60,\"expire\":3600},\"hours\":{\"stale\":300,\"revalidate\":3600,\"expire\":86400},\"days\":{\"stale\":300,\"revalidate\":86400,\"expire\":604800},\"weeks\":{\"stale\":300,\"revalidate\":604800,\"expire\":2592000},\"max\":{\"stale\":300,\"revalidate\":2592000,\"expire\":4294967294}},\"cacheHandlers\":{},\"cssChunking\":true,\"multiZoneDraftMode\":false,\"appNavFailHandling\":false,\"prerenderEarlyExit\":true,\"serverMinification\":true,\"serverSourceMaps\":false,\"linkNoTouchStart\":false,\"caseSensitiveRoutes\":false,\"clientSegmentCache\":false,\"preloadEntriesOnStart\":true,\"clientRouterFilter\":true,\"clientRouterFilterRedirects\":false,\"fetchCacheKeyPrefix\":\"\",\"middlewarePrefetch\":\"flexible\",\"optimisticClientCache\":true,\"manualClientBasePath\":false,\"cpus\":11,\"memoryBasedWorkersCount\":false,\"imgOptConcurrency\":null,\"imgOptTimeoutInSeconds\":7,\"imgOptMaxInputPixels\":268402689,\"imgOptSequentialRead\":null,\"isrFlushToDisk\":true,\"workerThreads\":false,\"optimizeCss\":false,\"nextScriptWorkers\":false,\"scrollRestoration\":false,\"externalDir\":false,\"disableOptimizedLoading\":false,\"gzipSize\":true,\"craCompat\":false,\"esmExternals\":true,\"fullySpecified\":false,\"swcTraceProfiling\":false,\"forceSwcTransforms\":false,\"largePageDataBytes\":128000,\"turbo\":{\"root\":\"F:\\\\CODE\\\\Project\\\\tempmail\\\\moemail\"},\"typedRoutes\":false,\"typedEnv\":false,\"parallelServerCompiles\":false,\"parallelServerBuildTraces\":false,\"ppr\":false,\"authInterrupts\":false,\"reactOwnerStack\":false,\"webpackMemoryOptimizations\":false,\"optimizeServerReact\":true,\"useEarlyImport\":false,\"staleTimes\":{\"dynamic\":0,\"static\":300},\"serverComponentsHmrCache\":true,\"staticGenerationMaxConcurrency\":8,\"staticGenerationMinPagesPerWorker\":25,\"dynamicIO\":false,\"inlineCss\":false,\"optimizePackageImports\":[\"lucide-react\",\"date-fns\",\"lodash-es\",\"ramda\",\"antd\",\"react-bootstrap\",\"ahooks\",\"@ant-design/icons\",\"@headlessui/react\",\"@headlessui-float/react\",\"@heroicons/react/20/solid\",\"@heroicons/react/24/solid\",\"@heroicons/react/24/outline\",\"@visx/visx\",\"@tremor/react\",\"rxjs\",\"@mui/material\",\"@mui/icons-material\",\"recharts\",\"react-use\",\"effect\",\"@effect/schema\",\"@effect/platform\",\"@effect/platform-node\",\"@effect/platform-browser\",\"@effect/platform-bun\",\"@effect/sql\",\"@effect/sql-mssql\",\"@effect/sql-mysql2\",\"@effect/sql-pg\",\"@effect/sql-squlite-node\",\"@effect/sql-squlite-bun\",\"@effect/sql-squlite-wasm\",\"@effect/sql-squlite-react-native\",\"@effect/rpc\",\"@effect/rpc-http\",\"@effect/typeclass\",\"@effect/experimental\",\"@effect/opentelemetry\",\"@material-ui/core\",\"@material-ui/icons\",\"@tabler/icons-react\",\"mui-core\",\"react-icons/ai\",\"react-icons/bi\",\"react-icons/bs\",\"react-icons/cg\",\"react-icons/ci\",\"react-icons/di\",\"react-icons/fa\",\"react-icons/fa6\",\"react-icons/fc\",\"react-icons/fi\",\"react-icons/gi\",\"react-icons/go\",\"react-icons/gr\",\"react-icons/hi\",\"react-icons/hi2\",\"react-icons/im\",\"react-icons/io\",\"react-icons/io5\",\"react-icons/lia\",\"react-icons/lib\",\"react-icons/lu\",\"react-icons/md\",\"react-icons/pi\",\"react-icons/ri\",\"react-icons/rx\",\"react-icons/si\",\"react-icons/sl\",\"react-icons/tb\",\"react-icons/tfi\",\"react-icons/ti\",\"react-icons/vsc\",\"react-icons/wi\"]},\"bundlePagesRouterDependencies\":false,\"configFile\":\"F:\\\\CODE\\\\Project\\\\tempmail\\\\moemail\\\\next.config.ts\",\"configFileName\":\"next.config.ts\"}\nconst maybeJSONParse = (str)=>str ? JSON.parse(str) : undefined;\nconst rscManifest = (_self___RSC_MANIFEST = self.__RSC_MANIFEST) == null ? void 0 : _self___RSC_MANIFEST[\"/api/emails/[id]/route\"];\nconst rscServerManifest = maybeJSONParse(self.__RSC_SERVER_MANIFEST);\nif (rscManifest && rscServerManifest) {\n    setReferenceManifestsSingleton({\n        page: \"/api/emails/[id]/route\",\n        clientReferenceManifest: rscManifest,\n        serverActionsManifest: rscServerManifest,\n        serverModuleMap: createServerModuleMap({\n            serverActionsManifest: rscServerManifest\n        })\n    });\n}\nexport const ComponentMod = module;\nexport default EdgeRouteModuleWrapper.wrap(module.routeModule, {\n    nextConfig\n});\n\n//# sourceMappingURL=edge-app-route.js.map", "interface CursorData {\r\n  timestamp: number\r\n  id: string\r\n}\r\n\r\nexport function encodeCursor(timestamp: number, id: string): string {\r\n  const data: CursorData = { timestamp, id }\r\n  return Buffer.from(JSON.stringify(data)).toString('base64')\r\n}\r\n\r\nexport function decodeCursor(cursor: string): CursorData {\r\n  const data = JSON.parse(Buffer.from(cursor, 'base64').toString())\r\n  return data as CursorData\r\n} "], "names": ["runtime", "DELETE", "request", "params", "userId", "getUserId", "NextResponse", "json", "error", "status", "db", "createDb", "id", "query", "emails", "<PERSON><PERSON><PERSON><PERSON>", "where", "and", "eq", "delete", "messages", "emailId", "success", "console", "GET", "searchParams", "URL", "url", "cursorStr", "get", "email", "isOwner", "isBound", "userEmailBindings", "binding", "baseConditions", "totalResult", "select", "count", "sql", "from", "totalCount", "Number", "conditions", "timestamp", "decodeCursor", "push", "or", "lt", "receivedAt", "Date", "results", "find<PERSON>any", "orderBy", "desc", "limit", "PAGE_SIZE", "hasMore", "length", "nextCursor", "encodeCursor", "getTime", "messageList", "slice", "map", "msg", "from_address", "fromAddress", "subject", "received_at", "total", "<PERSON><PERSON><PERSON>", "JSON", "stringify", "toString", "cursor", "parse"], "sourceRoot": "", "ignoreList": []}