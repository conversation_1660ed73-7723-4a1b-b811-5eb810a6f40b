/**
 * 邮箱凭证系统测试脚本
 * 
 * 用于测试新的邮箱凭证系统是否正常工作
 */

import { EmailCredentialManager } from '../app/lib/emailCredentials'

async function testEmailCredentials() {
  console.log('开始测试邮箱凭证系统...')
  
  try {
    // 1. 测试获取所有凭证
    console.log('\n1. 测试获取所有凭证')
    const allCredentials = await EmailCredentialManager.getAllCredentials()
    console.log(`找到 ${allCredentials.length} 个邮箱凭证`)
    
    if (allCredentials.length > 0) {
      const firstCredential = allCredentials[0]
      console.log(`示例凭证:`)
      console.log(`  邮箱: ${firstCredential.emailAddress}`)
      console.log(`  启用: ${firstCredential.enabled}`)
      console.log(`  绑定数: ${firstCredential.bindingCount}`)
      console.log(`  创建时间: ${firstCredential.createdAt}`)
      
      // 2. 测试根据邮箱获取凭证
      console.log('\n2. 测试根据邮箱获取凭证')
      const credential = await EmailCredentialManager.getCredentialByEmail(firstCredential.emailAddress)
      if (credential) {
        console.log(`成功获取 ${credential.emailAddress} 的凭证`)
        console.log(`JWT长度: ${credential.jwt.length}`)
        
        // 3. 测试验证凭证
        console.log('\n3. 测试验证凭证')
        const verifiedCredential = await EmailCredentialManager.verifyCredential(credential.jwt)
        if (verifiedCredential) {
          console.log(`凭证验证成功: ${verifiedCredential.emailAddress}`)
        } else {
          console.log('凭证验证失败')
        }
        
        // 4. 测试绑定统计
        console.log('\n4. 测试绑定统计')
        const bindingCount = await EmailCredentialManager.getBindingCount(credential.emailAddress)
        console.log(`${credential.emailAddress} 的绑定用户数: ${bindingCount}`)
        
      } else {
        console.log('获取凭证失败')
      }
    }
    
    console.log('\n邮箱凭证系统测试完成！')
    
  } catch (error) {
    console.error('测试过程中发生错误:', error)
    throw error
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  testEmailCredentials()
    .then(() => {
      console.log('测试成功完成')
      process.exit(0)
    })
    .catch((error) => {
      console.error('测试失败:', error)
      process.exit(1)
    })
}

export { testEmailCredentials }
