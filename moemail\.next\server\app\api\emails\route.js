(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[245],{65521:e=>{"use strict";e.exports=require("node:async_hooks")},25356:e=>{"use strict";e.exports=require("node:buffer")},38130:(e,t,i)=>{"use strict";i.r(t),i.d(t,{ComponentMod:()=>R,default:()=>M});var r,a={};i.r(a),i.d(a,{GET:()=>y,runtime:()=>v});var s={};i.r(s),i.d(s,{patchFetch:()=>w,routeModule:()=>S,serverHooks:()=>E,workAsyncStorage:()=>b,workUnitAsyncStorage:()=>A});var n=i(26312),o=i(35471),c=i(22680),l=i(65954),d=i(14139),m=i(43113),f=i(85885),u=i(13091),p=i(25601),g=i(43144),h=i(1189),x=i(5120);let v="edge";async function y(e){let t=await (0,x.F6)();if(!t)return p.Rp.json({error:"未授权"},{status:401});let{searchParams:i}=new URL(e.url),r=i.get("cursor"),a=(0,f.d)();try{let e=(0,u.Uo)((0,u.eq)(g.emails.userId,t),(0,u.gt)(g.emails.expiresAt,new Date)),i=await a.query.emails.findMany({where:e,orderBy:(e,{desc:t})=>[t(e.createdAt),t(e.id)]}),s=await a.query.userEmailBindings.findMany({where:(0,u.eq)(g.userEmailBindings.userId,t),orderBy:(e,{desc:t})=>[t(e.createdAt)],with:{email:!0}}),n=i.map(e=>({id:e.id,address:e.address,createdAt:e.createdAt.getTime(),expiresAt:e.expiresAt.getTime(),type:"temporary"})),o=s.map(e=>({id:e.email?.id||e.id,address:e.email?.address||"",createdAt:e.createdAt.getTime(),expiresAt:0x9184e729fff,type:"bound",credentialId:e.credentialId})),c=[...n,...o].sort((e,t)=>t.createdAt-e.createdAt),l=c;if(r){let{timestamp:e,id:t}=(0,h.L)(r),i=c.findIndex(i=>i.createdAt<e||i.createdAt===e&&i.id<t);i>-1&&(l=c.slice(i))}let d=l.length>20,m=d?(0,h.v)(l[19].createdAt,l[19].id):null,f=d?l.slice(0,20):l;return p.Rp.json({emails:f,nextCursor:m,total:c.length})}catch(e){return console.error("Failed to fetch user emails:",e),p.Rp.json({error:"Failed to fetch emails"},{status:500})}}let S=new l.AppRouteRouteModule({definition:{kind:d.A.APP_ROUTE,page:"/api/emails/route",pathname:"/api/emails",filename:"route",bundlePath:"app/api/emails/route"},resolvedPagePath:"F:\\CODE\\Project\\tempmail\\moemail\\app\\api\\emails\\route.ts",nextConfigOutput:"",userland:a}),{workAsyncStorage:b,workUnitAsyncStorage:A,serverHooks:E}=S;function w(){return(0,m.V5)({workAsyncStorage:b,workUnitAsyncStorage:A})}let C=null==(r=self.__RSC_MANIFEST)?void 0:r["/api/emails/route"],P=(e=>e?JSON.parse(e):void 0)(self.__RSC_SERVER_MANIFEST);C&&P&&(0,o.fQ)({page:"/api/emails/route",clientReferenceManifest:C,serverActionsManifest:P,serverModuleMap:(0,n.e)({serverActionsManifest:P})});let R=s,M=c.s.wrap(S,{nextConfig:{env:{},eslint:{ignoreDuringBuilds:!1},typescript:{ignoreBuildErrors:!1,tsconfigPath:"tsconfig.json"},distDir:".next",cleanDistDir:!0,assetPrefix:"",cacheMaxMemorySize:0x3200000,configOrigin:"next.config.ts",useFileSystemPublicRoutes:!0,generateEtags:!0,pageExtensions:["tsx","ts","jsx","js"],poweredByHeader:!0,compress:!0,images:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",loaderFile:"",domains:[],disableStaticImages:!1,minimumCacheTTL:60,formats:["image/webp"],dangerouslyAllowSVG:!1,contentSecurityPolicy:"script-src 'none'; frame-src 'none'; sandbox;",contentDispositionType:"attachment",remotePatterns:[{protocol:"https",hostname:"avatars.githubusercontent.com"}],unoptimized:!1},devIndicators:{appIsrStatus:!0,buildActivity:!0,buildActivityPosition:"bottom-right"},onDemandEntries:{maxInactiveAge:6e4,pagesBufferLength:5},amp:{canonicalBase:""},basePath:"",sassOptions:{},trailingSlash:!1,i18n:null,productionBrowserSourceMaps:!1,excludeDefaultMomentLocales:!0,serverRuntimeConfig:{},publicRuntimeConfig:{},reactProductionProfiling:!1,reactStrictMode:null,reactMaxHeadersLength:6e3,httpAgentOptions:{keepAlive:!0},logging:{},expireTime:31536e3,staticPageGenerationTimeout:60,modularizeImports:{"@mui/icons-material":{transform:"@mui/icons-material/{{member}}"},lodash:{transform:"lodash/{{member}}"}},outputFileTracingRoot:"F:\\CODE\\Project\\tempmail\\moemail",experimental:{cacheLife:{default:{stale:300,revalidate:900,expire:0xfffffffe},seconds:{stale:0,revalidate:1,expire:60},minutes:{stale:300,revalidate:60,expire:3600},hours:{stale:300,revalidate:3600,expire:86400},days:{stale:300,revalidate:86400,expire:604800},weeks:{stale:300,revalidate:604800,expire:2592e3},max:{stale:300,revalidate:2592e3,expire:0xfffffffe}},cacheHandlers:{},cssChunking:!0,multiZoneDraftMode:!1,appNavFailHandling:!1,prerenderEarlyExit:!0,serverMinification:!0,serverSourceMaps:!1,linkNoTouchStart:!1,caseSensitiveRoutes:!1,clientSegmentCache:!1,preloadEntriesOnStart:!0,clientRouterFilter:!0,clientRouterFilterRedirects:!1,fetchCacheKeyPrefix:"",middlewarePrefetch:"flexible",optimisticClientCache:!0,manualClientBasePath:!1,cpus:11,memoryBasedWorkersCount:!1,imgOptConcurrency:null,imgOptTimeoutInSeconds:7,imgOptMaxInputPixels:0xfff8001,imgOptSequentialRead:null,isrFlushToDisk:!0,workerThreads:!1,optimizeCss:!1,nextScriptWorkers:!1,scrollRestoration:!1,externalDir:!1,disableOptimizedLoading:!1,gzipSize:!0,craCompat:!1,esmExternals:!0,fullySpecified:!1,swcTraceProfiling:!1,forceSwcTransforms:!1,largePageDataBytes:128e3,turbo:{root:"F:\\CODE\\Project\\tempmail\\moemail"},typedRoutes:!1,typedEnv:!1,parallelServerCompiles:!1,parallelServerBuildTraces:!1,ppr:!1,authInterrupts:!1,reactOwnerStack:!1,webpackMemoryOptimizations:!1,optimizeServerReact:!0,useEarlyImport:!1,staleTimes:{dynamic:0,static:300},serverComponentsHmrCache:!0,staticGenerationMaxConcurrency:8,staticGenerationMinPagesPerWorker:25,dynamicIO:!1,inlineCss:!1,optimizePackageImports:["lucide-react","date-fns","lodash-es","ramda","antd","react-bootstrap","ahooks","@ant-design/icons","@headlessui/react","@headlessui-float/react","@heroicons/react/20/solid","@heroicons/react/24/solid","@heroicons/react/24/outline","@visx/visx","@tremor/react","rxjs","@mui/material","@mui/icons-material","recharts","react-use","effect","@effect/schema","@effect/platform","@effect/platform-node","@effect/platform-browser","@effect/platform-bun","@effect/sql","@effect/sql-mssql","@effect/sql-mysql2","@effect/sql-pg","@effect/sql-squlite-node","@effect/sql-squlite-bun","@effect/sql-squlite-wasm","@effect/sql-squlite-react-native","@effect/rpc","@effect/rpc-http","@effect/typeclass","@effect/experimental","@effect/opentelemetry","@material-ui/core","@material-ui/icons","@tabler/icons-react","mui-core","react-icons/ai","react-icons/bi","react-icons/bs","react-icons/cg","react-icons/ci","react-icons/di","react-icons/fa","react-icons/fa6","react-icons/fc","react-icons/fi","react-icons/gi","react-icons/go","react-icons/gr","react-icons/hi","react-icons/hi2","react-icons/im","react-icons/io","react-icons/io5","react-icons/lia","react-icons/lib","react-icons/lu","react-icons/md","react-icons/pi","react-icons/ri","react-icons/rx","react-icons/si","react-icons/sl","react-icons/tb","react-icons/tfi","react-icons/ti","react-icons/vsc","react-icons/wi"]},bundlePagesRouterDependencies:!1,configFile:"F:\\CODE\\Project\\tempmail\\moemail\\next.config.ts",configFileName:"next.config.ts"}})},96487:()=>{},78335:()=>{},1189:(e,t,i)=>{"use strict";i.d(t,{L:()=>s,v:()=>a});var r=i(25356).Buffer;function a(e,t){return r.from(JSON.stringify({timestamp:e,id:t})).toString("base64")}function s(e){return JSON.parse(r.from(e,"base64").toString())}}},e=>{var t=t=>e(e.s=t);e.O(0,[730,752,899,498,220,156],()=>t(38130));var i=e.O();(_ENTRIES="undefined"==typeof _ENTRIES?{}:_ENTRIES)["middleware_app/api/emails/route"]=i}]);
//# sourceMappingURL=route.js.map