import { auth } from "@/lib/auth"
import { NextResponse } from "next/server"
import { EmailCredentialManager } from "@/lib/emailCredentials"
import { z } from "zod"

export const runtime = "edge"

// 用户提取自己绑定邮箱凭证的请求体验证
const extractUserCredentialSchema = z.object({
  emailAddresses: z.array(z.string().email("无效的邮箱地址")).min(1, "至少选择一个邮箱"),
})

// 用户提取自己绑定邮箱的凭证
export async function POST(request: Request) {
  const session = await auth()
  if (!session?.user?.id) {
    return NextResponse.json({ error: "未授权" }, { status: 401 })
  }

  try {
    const body = await request.json()
    const validatedData = extractUserCredentialSchema.parse(body)

    // 获取用户绑定的邮箱列表
    const userBindings = await EmailCredentialManager.getUserBindings(session.user.id)
    const userEmailAddresses = userBindings.map(binding => binding.emailAddress)

    // 验证用户只能提取自己绑定的邮箱凭证
    const invalidEmails = validatedData.emailAddresses.filter(
      email => !userEmailAddresses.includes(email)
    )

    if (invalidEmails.length > 0) {
      return NextResponse.json(
        { error: `您没有权限提取以下邮箱的凭证: ${invalidEmails.join(', ')}` },
        { status: 403 }
      )
    }

    // 提取凭证
    const credentials = []
    for (const emailAddress of validatedData.emailAddresses) {
      const credential = await EmailCredentialManager.getCredentialByEmail(emailAddress)
      if (credential) {
        credentials.push({
          id: credential.id,
          emailAddress: credential.emailAddress,
          jwt: credential.jwt,
          enabled: credential.enabled,
          createdAt: credential.createdAt.toISOString(),
          lastUsedAt: credential.lastUsedAt?.toISOString() || null,
          bindingCount: credential.bindingCount || 0
        })
      }
    }

    return NextResponse.json({
      success: true,
      credentials
    })
  } catch (error) {
    console.error("Failed to extract user email credentials:", error)

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: error.errors[0].message },
        { status: 400 }
      )
    }

    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: "提取邮箱凭证失败" },
      { status: 500 }
    )
  }
}
