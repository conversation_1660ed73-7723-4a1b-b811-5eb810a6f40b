/**
 * 邮箱凭证系统迁移脚本
 * 
 * 此脚本用于将旧的复杂邮箱凭证系统迁移到新的简化系统
 * 
 * 迁移内容：
 * 1. 删除旧的 email_credentials 表中的复杂字段
 * 2. 为现有邮箱创建新的简化凭证
 * 3. 更新用户绑定关系
 */

import { createDb } from '../app/lib/db'
import { emails, emailCredentials, userEmailBindings } from '../app/lib/schema'
import { EmailCredentialManager } from '../app/lib/emailCredentials'
import { eq } from 'drizzle-orm'

async function migrateEmailCredentials() {
  console.log('开始迁移邮箱凭证系统...')
  
  const db = createDb()
  
  try {
    // 1. 获取所有现有邮箱
    console.log('获取现有邮箱列表...')
    const existingEmails = await db.query.emails.findMany()
    console.log(`找到 ${existingEmails.length} 个邮箱`)
    
    // 2. 为每个邮箱创建新的凭证（如果不存在）
    console.log('为邮箱创建凭证...')
    let createdCount = 0
    let skippedCount = 0
    
    for (const email of existingEmails) {
      try {
        // 检查是否已有凭证
        const existingCredential = await db.query.emailCredentials.findFirst({
          where: eq(emailCredentials.emailAddress, email.address)
        })
        
        if (existingCredential) {
          console.log(`跳过 ${email.address}，凭证已存在`)
          skippedCount++
          continue
        }
        
        // 创建新凭证
        await EmailCredentialManager.createCredentialForEmail(email.address)
        console.log(`为 ${email.address} 创建凭证`)
        createdCount++
        
      } catch (error) {
        console.error(`为 ${email.address} 创建凭证失败:`, error)
      }
    }
    
    console.log(`凭证创建完成: 新建 ${createdCount} 个，跳过 ${skippedCount} 个`)
    
    // 3. 清理旧的绑定关系（如果需要）
    console.log('检查用户绑定关系...')
    const bindings = await db.query.userEmailBindings.findMany()
    console.log(`找到 ${bindings.length} 个用户绑定关系`)
    
    // 4. 验证迁移结果
    console.log('验证迁移结果...')
    const allCredentials = await db.query.emailCredentials.findMany()
    console.log(`当前系统中共有 ${allCredentials.length} 个邮箱凭证`)
    
    // 显示统计信息
    for (const credential of allCredentials) {
      const bindingCount = await EmailCredentialManager.getBindingCount(credential.emailAddress)
      console.log(`${credential.emailAddress}: ${bindingCount} 个用户绑定`)
    }
    
    console.log('邮箱凭证系统迁移完成！')
    
  } catch (error) {
    console.error('迁移过程中发生错误:', error)
    throw error
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  migrateEmailCredentials()
    .then(() => {
      console.log('迁移成功完成')
      process.exit(0)
    })
    .catch((error) => {
      console.error('迁移失败:', error)
      process.exit(1)
    })
}

export { migrateEmailCredentials }
