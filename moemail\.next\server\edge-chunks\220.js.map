{"version": 3, "file": "edge-chunks/220.js", "mappings": "yEAAA,MAAM,aAAa,OAAO,QAAQ,4CAA4C,cAAmB,SAA0F,SAAmB,UAAU,eAAe,gBAAmB,sBAAgC,wBAA0B,mDAAuD,gCAAiC,oBAAiD,gBAA8C,8BAA9C,qBAA8C,CAA/F,iCAAiD,CAAiF,CAAS,gBAAyB,oCAA6C,oBAAyB,aAAwB,mBAAwB,oBAArkB,gBAAkB,gCAAqC,2BAAkiB,kCAAwD,aAAa,kCAAkC,wBAAyB,2CAAsC,6BAAiC,0CAAiD,GAAU,kCAAuD,gCAAgC,eAAe,qBAAqB,kCAAsC,IAAI,IAAK,aAAa,UAAU,sCAA+D,uCAAgC,EAAe,OAAiB,SAAhC,CAAgC,CAAjB,CAAkC,uCAAuD,cAAc,6BAAiC,6CAA6C,SAA8D,OAArD,8CAAqD,GAAU,qCAAwC,wCAA0C,0CAA4C,4CAA8C,8CAAgD,iDAAkD,qBAAyB,IAAI,IAAK,oBAAoB,wBAAwB,KAAK,iBAAiB,QAAQ,IAAI,IAAgE,CAA3D,0DAA2D,GAAU,kCAAkC,KAAM,qCAAoC,KAAM,uCAAsC,KAAM,yCAAwC,KAAM,oCAAuC,IAAI,IAAK,oBAAoB,+BAAgC,UAAa,+BAA6C,yBAAsC,iCAAiD,yBAAqC,6CAAuE,cAAc,gCAAgC,MAA0B,OAAnB,UAAmB,KAAY,sBAAsB,QAAS,wCAAgD,WAAoB,IAAK,4BAA4B,IAAI,IAAK,mDAAoD,YAAc,8CAAgD,UAAwB,aAAa,2CAAyE,MAAM,UAAM,UAAU,2BAAsC,EAAK,mBAAwB,qBAAoB,MAAa,2CAAiE,uCAA6D,aAAwB,iBAAgD,WAAT,CAAiC,EAA9B,GAA8B,CAA1B,GAAmC,kBAAkB,aAAY,CAAE,0BAAmC,OAAO,iCAAuC,OAAO,YAAc,QAAQ,IAAM,aAAa,sCAAsC,SAAW,EAAwI,UAAtI,gBAA2B,QAAQ,WAAe,WAAW,YAAc,KAAU,eAAiB,MAAM,OAAO,CAAK,IAAK,SAAS,CAAwB,eAAe,sCAAsC,SAAW,EAAE,YAAe,SAAoB,cAAc,eAAe,aAA6C,OAAS,UAAzC,iBAAiB,WAAW,KAAa,gBAA2B,6DAA6D,oBAAoB,OAAO,4DAAgE,0BAA0B,UAAU,0BAA4B,4BAAyC,UAAU,oEAAwE,WAAW,2BAA2B,YAA2B,eAAe,YAAe,uBAAiC,eAAe,SAAS,0BAA0B,mCAA6C,2BAA6B,mEAAuE,YAAiB,KAAK,OAAO,sBAAyB,yBAA0B,IAAI,OAAO,SAAS,KAAK,OAAO,sDAAyD,GAAG,cAAc,+BAAiD,8BAAiC,WAAW,KAAK,IAAK,mBAAoB,gBAAgB,EAAG,EAAG,YAAmB,oBAA8B,2BAAsC,KAAS,cAAgC,WAAW,cAAkB,iBAAiB,YAAY,YAAY,KAAW,IAAI,oBAAsC,KAAQ,QAAQ,eAAiB,iBAAiB,KAAmE,KAAc,IAAL,EAAK,GAAS,MAAM,OAAQ,eAAR,EAAQ,cAAsC,SAAW,EAAE,aAAiC,SAAiC,SAAiC,SAAmB,2BAA2B,YAAuB,eAAe,YAAkR,GAAtQ,QAAQ,sBAAsB,oBAAoB,qBAAqB,qBAAyB,oBAAsK,mBAA9I,iBAAiB,0GAAyH,KAAI,+BAAyD,gFAAoF,qFAAqF,MAAM,qBAAqB,IAAI,sEAA0E,2EAA+E,kFAAkF,MAAM,kBAAkB,IAAI,4DAA4D,4DAAiE,gCAAgC,0BAA0B,6BAA6B,8BAA8B,+BAA+B,wBAAwB,2CAA6C,gCAAmC,gCAAgC,sEAAsE,kCAAkC,4CAA4C,QAAQ,qBAAqB,0BAA0B,kBAAkB,mBAAmB,qBAAqB,qBAAyB,yBAA2B,oBAAoB,oBAAwB,mBAAmB,oBAAoB,mBAAmB,mCAAmC,uBAA0B,oBAAoB,iBAAmB,8BAAiC,0BAA4B,UAAqL,OAA/F,0BAAgC,iCAAiC,yBAAyB,KAAK,GAA7K,wEAA6K,CAAa,KAA5G,CAA4G,GAAa,qBAAqB,MAAyB,CAAzB,iBAAiI,OAAxG,kBAAqB,gCAAgC,wBAA2B,wBAAwB,GAAa,oBAAoB,gCAAkC,qEAAqE,kCAA8B,KAAO,CAAa,oBAAoB,IAAI,GAAM,mCAAmC,KAAa,SAAa,8BAA8B,yCAA0D,SAA1D,GAAiE,kCAAmC,mBAAmB,iBAAkB,6CAA4C,cAAc,oEAAsE,gCAAgC,yBAA2B,yEAAyE,qBAAqB,gBAAgB,kCAAmC,kBAAkB,yBAAyB,mBAAmB,+BAAiC,gFAAoF,EAAE,MAAM,SAAS,IAAI,oBAAoB,qBAAqB,gBAAgB,EAAE,2BAA4B,gBAAoB,qBAAqB,sBAAsB,IAAI,oIAAgJ,mEAAuE,IAAK,GAAoB,WAAW,SAAS,KAAK,cAAc,yBAA2B,0BAA0B,iBAAiB,EAAG,kBAAkB,kDAAoD,QAAQ,uBAAoB,CAAY,kBAAqB,qBAAqB,CAAtD,KAAkE,QAAQ,kBAAoB,QAAQ,iCAAiC,gBAAgB,MAAyB,CAAzB,iBAAgC,uBAAwB,yBAA2B,wBAAwB,IAAI,KAAK,EAAG,eAAe,8BAAiD,CAAjD,iBAAwD,uBAAwB,wBAA0B,uBAAuB,IAAI,KAAK,EAAG,WAAW,wBAAwB,UAAU,oCAAoC,cAAc,0BAA0B,eAAe,sBAAsB,cAAc,qBAAqB,eAAe,iBAAiB,aAAoB,GAAI,aAAiB,cCA5rT,MAAM,aAAa,OAAO,QAA8F,UAAtF,YAAuC,IAAtB,sBAAsB,GAAS,yBAAyB,aAAa,GAAiB,KAAS,cAAgC,WAAW,cAAkB,iBAAiB,YAAY,YAAY,KAAW,IAAI,oBAAsC,KAAQ,QAAQ,eAAiB,iBAAiB,KAAmE,KAAc,IAAL,EAAK,OAA+B,aAAiB,2ECQzd,kCAAiC,EAAuB,EAC/D,mBAAuB,EACvB,gBAgDA,MA7CA,EAFA,QAAqF,IAAkB,KAAN,CAAM,MACvG,EAA8B,OADuD,WACvC,YAc9C,KAXA,EADA,EACA,EA2CA,EA3CA,OA4CA,CAAQ,MAAa,UACrB,EAEA,QA/CA,CASA,wBAGA,OAEA,aAAoB,WAAkB,EACtC,OACA,KACA,OACA,UACA,OACA,CACA,CACA,CAAK,CACL,kFC+BA,IAAMA,EAAoCC,OAAOC,GAAG,CAClD,gCAGK,SAASC,EAA+B,MAC7CC,CAAI,CACJC,yBAAuB,uBACvBC,CAAqB,iBACrBC,CAAe,CAYhB,MAEyCC,EAAxC,IAAMC,EAEL,OAFuCD,EAAAA,UAAU,CAChDR,EAAAA,EACD,OAFuCQ,EAErCC,+BAA+B,CAKlCD,UAAU,CAACR,EAAkC,CAAG,CAC9CS,gCAAiC,CAC/B,GAAGA,CAA+B,CAClC,CAACC,CAAAA,EAAAA,EAAAA,CAAAA,CAAgBA,CAACN,GAAM,CAAEC,CAC5B,wBACAC,kBACAC,CACF,CACF,8HCvDO,2BAGA,OAFA,uBAGA,SCtDA,SACP,qBACA,MAGA,SDmCO,cACP,MAAoB,GAAc,ECpC4C,GDoC5C,YAIlC,OACA,qBAJA,MAAsC,IAA2B,IACjE,gBAIA,wBAHA,MAAgD,IAA0C,CAI1F,CACA,EC5C8E,0BAC9E,iBAAwD,EAA4B,iBACpF,iDAEA,mDACA,sBACA,CACA,SACA,wBACA,sFAEA,0BACA,KAAkB,EAClB,0BACA,YACA,SAA4D,CAAtC,KAAsC,CAC5D,EADwE,CAAK,IACzD,GACpB,QACA,CAFwC,CAGxC,CACA,UAIA,0BACA,KAAkB,EAClB,SACA,YACA,KAH8C,IAGc,CAAtC,KAAsC,CAC5D,EADwE,CAAK,KACzD,EACpB,SACA,CAFwC,OAExC,WACA,CAAS,CACT,CACA,gBCnBA,gBACA,iGACA,2CACA,cACA,YAA6B,QAAkB,IAC/C,yBAIA,aAFA,IAAoC,IAAe,IAEnD,SACA,QAEA,CACA,CAKO,sBACP,gBAGA,qBACA,cACA,GACA,2BAEA,CACA,SACA,OACA,eACA,QACA,mBAIA,KACA,oBACA,mBACA,CAAS,CACT,cAMA,OALA,WAGA,WA3DA,YACA,MAAoB,GAAc,SAClC,aAAyB,IAAc,CACvC,0BAEA,OAAW,GAAc,QACzB,EAqDA,YAEA,UACS,CACT,cACA,eAGA,UAA2C,IAAc,CAAC,GAAc,kBACxE,OAGA,UAAgC,IAAqB,QACrD,CACA,iBACS,CACT,mBACA,eACA,CAAS,CACT,qBACA,sBACA,MAzEA,cACA,UAAwB,IAAc,CAAC,GAAc,UACrD,OAAW,IAA4B,UACvC,EAsEA,2BACA,OACA,kBACA,CACA,wBACS,CACT,8BACA,+BACA,MAAgD,QAA0B,qBAC1E,4BACA,CACA,iCACS,CACT,gBAIA,OAHA,aACA,iBAAsC,EAAiB,uCAEvD,YACS,CACT,8BACA,eACA,kEAEA,EApEA,2CACA,yGC1C4F,sBAC5F,MACA,WAIA,WACA,IACA,gBACA,EAAM,OAEN,eAiBA,KACA,iCACA,qCACA,OACA,uDACA,wHACA,sEACA,CACA,EAzBA,OACA,aACA,CACA,CACA,cACA,OACA,sCACA,kBACA,IACA,oBACA,wBACS,CACT,sDACA,0BACA,IAEA,CAUA,oBAA+C,iEAA8D,EAC7G,MACA,oBACA,0DACA,oBACA,EACA,CACA,oCCpCO,SACP,uBAAkB,0BAAiC,EACnD,4BACA,iBACA,eACA,mBACA,sBAAiC,MAAY,IAC7C,sBACA,CACA,SACA,GAAY,OAAU,IACtB,gBACA,IAEA,mEACU,wBAEV,yBAEA,kEAEA,CACA,eAEA,gBACA,IAEA,MAA8B,IAAoB,YAClD,GACA,2BAEA,MAA+B,GAAqB,YAKpD,yBACA,uBAGA,kCACA,2DACA,iDAOA,MAAgC,QAAY,WAC5C,IACA,MAAsB,GAAqB,MAC3C,oBACA,CAAiB,SACjB,CAAc,SACd,kCACA,CACA,CAAS,EACT,yBACA,CACA,4BAEA,OADA,sCACA,mBACA,CACA,qBACA,sCACA,iCACA,gBAEA,MAA0B,kBAAgB,YAC1C,MACA,UAAsB,GAAc,mDAEpC,OAAe,EAAsB,OACrC,aADqC,KACrC,SACA,6BAEA,CACA,qBAIA,GADA,gIACA,iBAEA,IACA,qDACA,CAAc,SACd,kBAAkC,GAAc,4EAChD,OACA,CAAiB,EACjB,CAEA,CACA,CACA,aACA,kHACA,gBCtGO,iBAA2B,6EAA6E,EAkB/G,OACA,mBAFA,8DAGA,OACA,sBACA,MAAe,OAAgB,IAC/B,iBAEA,kDACA,sCACA,4BACA,4BACA,wBACA,4CACA,0BACA,oBACA,oBACA,kBACA,kEAAqG,CACrG,+CACA,aAMA,YACA,cAAY,gCAAuC,EACnD,WAAe,EAAY,CAC3B,SAD2B,CAC3B,EACA,UACA,aACA,CAAK,CACL,EAbA,EACA,EAGA,OADA,UACA,CACA,mBC3CI,kBAA8E,iHCEvE,SACX,sBAAkB,eAAsB,EACxC,gBACA,iBACA,CACA,0BCLW,OACX,MACA,OACA,UACA,OACA,MACA,SACA,QACA,gBCVA,UACA,OACA,UACA,CAGA,sBACA,mBACA,YAAuB,aAA8B,KACrD,6BACA,IAEA,2CACA,MAAqC,EAAY,EAAE,uBAAsC,SAEzF,UAEA,CACA,CACA,QACA,gDEpBA,OACA,OACA,UACA,CACA,aACA,0BACA,UACA,CAAK,CACL,iFGRO,oBAKP,EAHA,IDSW,YAEX,GAAQ,OAAmB,ICXO,CDa1B,OAAiB,KAKjB,0BAAoB,IAPD,eAS3B,ECpBkC,IAIlC,4DAEA,IADA,YACA,0BACA,cACA,kBACA,SACA,qBAAiD,GAAO;AACxD;AACA,kBAAkB,EAAQ,EAC1B,wCACA,iBACA,MACA,CACA,MACM,oBACN,MAEA,MACA,uBAA+B,GAAO;AACtC;AACA,oBAAoB,EAAQ,GAC5B,MACA,CACA,uBAA2B,GAAO,0OAClC,iBAEA,oGK5BW,SACX,cACA,aACA,uBACA,kBACA,oBACA,mBACA,CACA,sBACA,mBACA,oBACA,sBAEA,GADA,oBACA,gBACA,YAAmC,6BAAgC,IACnE,wBAEA,6BACA,CACA,CAAa,GAEb,mBACA,oBACA,gBAEA,GADA,oBACA,gBACA,YAAmC,wBAA2B,IAC9D,mBAEA,wBACA,CACA,CAAa,IAEb,CAIA,aACA,uBACA,4BACA,gBACA,0BAEA,CAAS,CACT,CAKA,aACA,uBACA,uBACA,gBACA,0BAEA,CAAS,CACT,CACA,YACA,YACA,CACA,UAOA,aACA,gBACA,0BAEA,CACA,sEChCW,iBAAkC,EAC7C,SADwD,CACxD,mBAAqC,CAAa,aAClD,UAAkB,MADgC,OAChC,wCAA0D,EAwB5E,GAvBA,OACA,WACA,YACA,CAAS,EAET,0BAAkC,IAAoB,CAEtD,sBAA8B,kBAAgB,CAG9C,iBAAyB,EAGzB,kBAHoC,KAGpC,CAAgC,GAAkB,CAClD,wBACA,wBAGA,aTxDO,YAGP,MAAoB,EAAY,gBAChC,KAGA,WACA,EAAS,GAAK,EAGd,UAAgC,EAAY,iBAG5C,aAFA,uBAEA,CAIA,eACA,QAEA,aAEA,eAEA,QACA,CAEA,kBAGA,OACA,aACA,EACA,EAGA,6BACA,eAIA,OACA,yBACA,CAGA,kCACA,WACA,SACA,CAAiB,EAEjB,iBACA,QACA,CACA,yFAAqG,EAAO,EAC5G,CACA,QACA,ESF2C,GAE3C,kCAoZW,SAEX,uDAtZA,GAEA,mCACA,kCACA,kCACA,+DAAmF,WAAoB,2HACzF,IAAU,SFnEjB,GACP,4JEkE0C,kCAC1C,sGAA0H,WAAoB,2GAE9I,qBAEA,CAuBA,CAMA,kBZzFA,KY2Fa,KZ3Fb,CY2FyB,GAIzB,GAJyB,CAIzB,YAJyB,uBACzB,UACA,CAAa,CAGb,CACA,eAGA,aACA,IAyOA,MA7NA,EAXA,2BACA,6DAEQ,QAAU,EAClB,uCACA,+CACS,EACT,OACA,gBAAqC,QAA0B,CAAC,SR5HrD,GACX,SACA,CQ0HsF,GR1HtF,6BACA,YACA,SAEA,QACA,EQqHsF,mBACtF,EACA,OAEA,IACA,MACA,+BACA,EAGA,mBAAmF,IAAc,GACjG,UAwCA,EAtBA,0BACA,KACA,MAA4C,EAC5C,EAA0C,QAA0B,SACpE,KACA,iBACA,eACA,eACA,sBACA,aACA,cAGA,kBACA,aACA,OAAgC,IAAc,CAC9C,MAA+B,IAAc,CAC7C,SACA,EACA,CACA,6BACA,EAEA,IACA,wCACA,CAAsB,SACtB,iBAGA,KAC0B,oEACE,EAAyC,UAErE,CAgBA,GAfA,yBAHqE,EAGrE,8BAIA,aAAqD,KACrD,iBAGA,KAC8B,8BACE,EAAyC,UAEzE,CAAyB,EAEzB,qBACA,GAGA,EARyE,EAQzE,EAA8C,QAAqB,IACnE,KACA,UAAsC,oBAAkB,UAAU,SAAiB,oDAAoD,EAAc,+EAGrJ,OADA,6IACA,IAAsC,oBAAkB,UAAU,SAAiB,0IAEnF,CAIA,0BACA,EAAsC,QAA0B,SAChE,SACA,iBACA,eACA,eACA,sBACA,aACA,iBACA,kBACA,aACA,OAAgC,IAAc,CAC9C,MAA+B,IAAc,CAC7C,SACA,EACA,CACA,6BACA,EACA,KA4CA,GA3CA,4BACwB,QAAiB,WACzC,IACA,mDACA,KAEA,OACkC,6BAElC,KACA,MACA,CACA,KACA,SACA,yBACA,IACA,KACA,kBACA,kBACA,gBACA,wBACyC,GAEzC,CAAiC,IACD,QAAiB,MACjD,IACA,KACA,UACA,cAEA,CAAiC,CACjC,CAA8B,SAC9B,IACA,CACA,CAAyB,EACD,QAAiB,MACzC,IACA,KACA,UACA,cAEA,CAAyB,CACzB,CAAqB,EACrB,iBAEA,iBAKA,SAEA,EAAkB,IAClB,GACA,wBACA,eACA,eACA,aACA,OAAgC,IAAc,CAC9C,MAA+B,IAAc,CAC7C,SACA,EACA,EAEA,QAAgC,IAAoB,aAEpD,EAAc,IACd,QAA4B,IAAoB,aAEhD,CAAU,SACV,GAAgB,QAAe,KAC/B,MAA4B,QAAuB,IACnD,MACA,yDAIA,mBACA,UACA,CAAiB,EASjB,MAJA,oBACoB,QAAoB,qBAGxC,mBAIA,kBAAmD,GAAkB,UAAY,QAA8B,IAC/G,SACA,CAAiB,CACjB,CAAc,GAAS,QAAyB,IAEhD,0BACA,OAFmC,QAA2B,GAG9D,CAAiB,CAEjB,QACA,CAEA,4BACA,2DAA2E,sBAAsB,4FAEjG,0CACA,2CACA,8EACA,sCAA+D,EAC/D,EACA,IAEA,+DACA,8CACA,sCACA,qCAKA,mCACA,oBAA+C,QAAoB,qBACnE,qBACA,gBACA,wBACA,SACA,CAAa,EAEb,CACA,CACA,kBAEA,6BAEA,GAEA,yBACA,0BACA,wBAGA,kDACA,OACA,cACA,SJ1WA,CI0WsB,QJjYf,OACP,EIgYuC,CJ9XvC,+BACA,gBAAmC,IAAa,sBAChD,kCAEA,YAA+B,IAAa,sBAC5C,mCAEA,qEACA,8EACA,wDAEA,OACA,WACA,qBACA,oBACA,gBACA,eANA,WAOA,CACA,EI4WuC,GJ1WvC,gBI4WA,EAA6B,SX9WtB,MW8WqC,CX5W5C,SAIA,aADA,KAEA,KAAiB,IAA0B,CAAC,EAAE,EAAI,EAClD,UAIA,eACA,SAAuB,IAA0B,CAAC,EAAE,WAAa,EACjE,SACA,CACA,QACA,EW6V4C,+BAC5C,GACA,EAA6B,QAAwB,mDACrD,EAA0B,OAAe,IAIzC,oHAGA,6BACA,sBACA,UAA4C,oBAAkB,yEAG9D,OAFA,oCACA,4BACA,CACA,CAIA,QAEA,qBACA,oBAGA,kBACA,KAEA,oBAGA,iBAGA,iBACA,KACA,aAGA,wBACA,yCACA,KACA,SAEA,WAqHA,KACA,OACA,WACA,UACA,aACA,mBACA,UACA,WACA,aACA,eACA,aAIA,OADA,IAD8C,IAAoB,YAClE,WAA0E,EAAK,GAChD,GAAc,WAE7C,aACA,8CACA,SACA,OAA2B,GAAc,WACzC,CACA,CACA,EACA,GACA,SACA,UACA,cACA,0CACA,eACA,cACA,UACA,WACA,WACA,WACA,WACA,kBACA,eAOA,OAJA,IAD8C,IAAoB,YAClE,WAA0E,EAAK,GAIhD,GAAc,WAE7C,aACA,iCAOA,aACA,SAIA,OAA2B,GAAc,WACzC,CACA,CACA,EACA,qBACA,EApLA,IACA,CAEA,MV/aW,YAEX,cACA,CU4a6D,CV5a7D,aACA,cAEA,sBAIA,MAHA,iBAEA,gCAEA,EUoa6D,uBAC7D,EAAmC,QAAS,GAG5C,OADA,uCACA,QAAwC,IAAyB,aACjE,sCAA+D,EAAM,EACrE,YACA,cACA,CACA,CAAqB,iCACrB,CAAiB,IAGjB,4BAEA,0BACA,UACA,CAAa,EAEb,yCACA,kJAEA,4CAEA,4LAEA,QACA,CACA,CAiBA,wBACA,kBACA,kBACA,yBACA,iBACA,qBACA,oBACA,oBAKA,GACA,WACA,UACA,cACA,mBAAyE,GAAc,oBAAoB,GAC3G,eACA,mBAAyE,IAAqB,UAAU,gBAAc,eAAe,IACrI,eACA,0CACA,WAIA,0BACA,MACA,SACA,MACA,aACA,iCAOA,aACA,SACA,OAAuB,GAAc,WACrC,CACA,CACA,EACA,GACA,WACA,UAEA,aACA,QACA,oBACA,uCACA,YACA,mBHhhBW,QGghBgE,CHhhBhE,GACX,iBAIA,OAHA,wBACA,YACA,kBACA,CACA,EG0gB2E,aAC3E,cACA,eACA,8BAEA,WAIA,MACA,aACA,8CACA,SACA,OAAuB,GAAc,WACrC,CACA,CACA,EAiEA,GACA,WACA,UACA,cACA,0CACA,eACA,cACA,UACA,WACA,WACA,WACA,WACA,kBACA,eACA,UAA0B,GAAqB,UAAU,oBAAyB,uFAAuF,EAAK,KAC9K,aACA,iCAOA,aACA,SACA,OAAuB,GAAc,WACrC,CACA,CACA,EACA,GACA,WACA,UACA,aACA,mBACA,UACA,WACA,aACA,eACA,aACA,UAA0B,GAAqB,UAAU,YAAiB,uFAAuF,EAAK,KACtK,aACA,8CACA,SACA,OAAuB,GAAc,WACrC,CACA,CACA,EACA,cACA,WAAe,oBAAkB,UAAU,GAAO,yIAClD,CACO,kBACP,MACA,oBACA,qBAAqC,SAAa,QAAQ,EAAW,kJAAkJ,EAAW,wKACxN,6BACV,qBAAqC,SAAa,QAAQ,EAAW,mLAAmL,EAAW,+KAEnQ,CACA,wBACA,UAAkB,GAAqB,UAAU,SAAa,+EAA+E,EAAW,+HAExJ,MACA,yBAEA,qBAA6C,SAAa,OAAO,GAAY,iIACjE,QAA2C,eACvD,EAAU,gCAEE,QAAoB,mCACtB,gCAEV,eACA,UAA4B,oBAAkB,UAAU,SAAa,oDAAoD,EAAW,+EAGpI,OAFA,4BACA,4BACA,CACA,EAIA,SAJmB,KAA2F,EAAE,EAEvG,kBShrBT,EECI,EVLG,mBAEP,MADA,EUIe,WVJf,6GAGA,WACA,mBAEA,4DACA,GACA,QAEA,cACA,6BACA,IACA,kBACA,CAAU,SAEV,MADA,mEAAmF,UAAY,EAC/F,CACA,CAEA,CAWA,WACO,aAIP,OAHA,GACA,QAEA,CACA,CACA,cAEA,oDAAyD,EAAO;AAChE,0EA0BA,UAAoB,GAAM,WAE1B,YAAsB,GAAM,aACpB,GAAM,kBAId,yDACA,MAhCA,YACA,4BAAyC,EACzC,SACA,cACA,QAEA,kBACA,CAAS,CACT,YACA,iBACA,CAAS,CACT,aACA,2BACA,cAEA,kBACA,CACA,CAAK,EACL,mBAAuB,EACvB,SACA,CAAK,CACL,EAYA,cACA,eACA,CAAK,EAEL,8BC/EA,yBACA,wBACA,qBACA,SACA,iBACA,WACA,WACA,gBACA,UACA,EAAU,CACV,gBACA,YAEA,CAEA,eACA,SACA,4BAEA,CAEA,yBACA,UACA,CACA,aACA,6BAIA,MADA,qBACA,GAIA,wBAEA,CACA,CAIO,kBACP,eACA,MACA,wDACA,uBAMA,cACA,UAAkB,IAAkB,EACpC,qBACS,CACT,CAKA,cACA,UAAkB,IAAkB,EACpC,qBACS,CACT,CACA,2BC5DO,SAASI,EAAcC,CAAoB,CAAEC,CAAkB,EACpE,IAAMC,EAA0B,UAAhB,OAAOD,EAAoB,IAAIE,IAAIF,GAAQA,EACrDG,EAAW,IAAID,IAAIH,EAAKC,GACxBI,EAAYH,EAAQI,QAAQ,CAAC,KAAIJ,EAAQK,IAAI,CACnD,OAAUH,EAASE,QAAQ,CAAC,KAAIF,EAASG,IAAI,GAAOF,EAChDD,EAASI,QAAQ,GAAGC,OAAO,CAACJ,EAAQ,IACpCD,EAASI,QAAQ,EACvB,2BCXA,OACA,iBACA,eACA,kCACA,sBACA,mBACI,IAAoB,CACxB,CACA,GACA,gBACA,+ECYO,SACP,WACA,iBACA,4DAEA,wCACA,gBACA,CACA,gBACA,iBACA,4DAEA,mBACA,8CAEA,gBACA,CACA,cACA,4BACA,iBACA,gBACA,CACA,CCzCW,aACX,OACA,cAA4D,CAArC,GAAqC,8BAAwD,CACpH,EADoG,oBACpG,gDACA,2EACA,CACA,SCJA,yCCeO,iBAA8B,GAAW,CAChD,eACA,sBACA,uBAEA,cACA,UAAkB,IAAkB,EACpC,qBACS,CACT,CACA,cACA,UAAkB,IAAkB,EACpC,qBACS,CACT,CACA,YACA,UAAkB,IAAkB,EACpC,qBACS,CACT,CACA,CACA,OACA,6BACA,2BACA,EACA,SACmB,QAAS,GAC5B,qCAEA,KAWO,wBACP,MAmGA,EACA,GA/GA,WACA,QACA,KACA,+CACA,sBAAoB,wBAAwC,EAAU,KAA6C,EACnH,IACA,MAFqE,CAKrE,IAIA,MAAU,IAEV,2BAFyC,SAEzC,CACA,cAAyB,OAAe,gBACxC,UAA2B,GAAO,gBAClC,0BACA,gCACK,EAML,YAHA,IACA,sBACA,CACA,CACA,+BACQ,QAAuB,OAE/B,aADA,yBACA,GACA,2BAEA,wBACA,CAAS,CACT,CAEA,gBACA,aACA,yCACA,0BACA,iBAEA,MAA2B,QAA2B,oBACtD,UAEA,MACA,aAA6B,IAAc,EAC3C,sBACA,WACA,IACA,WACA,YAEA,CAGA,aACA,YAEA,MAAe,CJ9FR,cACP,UI6FwC,MJ7FxC,SACA,iBACA,eACA,yBAEA,KACA,eACA,yBAGA,uBACA,GI8EqG,EAI7D,eACxC,MACA,oBACA,UACA,wBACA,gCACA,wBAEA,CAAK,EAKL,GACA,oCACA,cACA,QACA,CAAS,EAET,oDAEA,uDACA,UACA,cACA,aAAyB,EACzB,UAD6C,UACZ,EAAyC,CAC1E,KAAiB,EACjB,UADqC,KACrC,kBACA,wBACA,yBACA,EACA,WACA,SAA8B,CAC9B,gBAAqC,CACrC,kBACA,QAA6B,GAC7B,EAEA,EAAS,EAIT,UAPgD,KAOhD,aAA6G,MAA7G,GDxJO,WAEP,MADA,OCuJ6G,GDtJ7G,IACA,6BACA,GCoJ6G,sBAC7G,MAAsB,EAAc,CACpC,UACA,CAFoC,IAEpC,OACA,WACA,WACA,EAAU,MACV,CAAK,EA8DL,IA3DA,iBAGA,GADA,mDACA,CAIA,0BACA,MAAwC,EACxC,MAAmB,QAAS,SAAS,IAAc,UACnD,uBAAwC,UAAgB,EAAE,mBAAyB,EACnF,YACA,iCACA,uBAEA,CAAa,WACb,IACA,YAIA,MAAyC,IACzC,EAAyC,QAAwB,KADL,OACK,QAJjE,IACA,GACA,EAEiE,GACjE,EAAsC,OAAe,EACrD,SACA,yBACA,YACA,oGACA,cACA,qBACA,aACA,yGACA,CAA6B,CAC7B,cACA,2BACA,YACA,0BACA,uBACA,CAAyB,CACzB,mBACA,QACA,CAAyB,CACzB,gCAA+D,IAA2B,CAC1F,CAAqB,EACrB,aAAiC,kBAAgB,WAAoB,IAAoB,sBACzF,EAAkB,OAKlB,gBACA,iBACA,CAAqB,GACrB,CACA,CAAa,CACb,CACA,qBACA,EAAK,GAEL,yBACA,mEAEA,MACA,8BAOA,2DACA,aACA,UAA+B,GAAO,IACtC,eACA,0BACA,gCACS,CAET,2BACA,uBACA,iDAOA,MAAmC,EAAa,qBAChD,GAIA,mCAEA,CAKA,+CACA,aACA,UAAgC,GAAO,IACvC,eACA,0BACA,gCACS,EAIT,yBAEA,0BACA,uBACA,qCAOA,IACA,6BACA,kCAAsD,EAAa,sBAEnE,CACA,SAAgD,GAAY,QAE5D,iDACA,KACA,MACA,iBACA,sCAA8D,EAAI,KAClE,SAEA,aACA,gEAEA,CACA,OACA,WACA,UAAmB,CNxQnB,2BMwQ+C,INxQ/C,8BAAgH,WMwQjE,kBAC/C,4BAEA,sCC/SA,QAEA,wBACA,wBAEA,wBACA,6BAEA,yCACA,0BACA,0BACA,kBACA,SACA,CAAK,MACL,gBACA,QAAqB,CACrB,gBACK,YACL,SAMA,OAHA,GACA,gCAAgD,GAAK,QAAQ,EAAW,GAExE,UALA,QAOA,CAAK,eACL,eACA,CAAK,CACL,CACe,QACf,qBACA,gCACA,iBACA,aACA,4BACA,eACA,sBAEA,QACA,CACA,sBACA,+EACA,CACA,eAGA,GAFA,gBACA,gDACA,wBACA,uCACA,eACA,4BAEA,qBAEA,4EACA,iFAIA,GAHA,uCACA,sCAAsD,sCAAsC,GAE5F,GACA,+CACA,uBAAoC,EAAQ,KAAK,EAAO,EAAE,MAAiB,EAC3E,GACA,sDAEA,EAAU,OACV,0CAEA,sBACA,IACA,GACA,kDAEA,MAAkC,GAAQ,sCAA2C,EAAO,EAC5F,MACA,MACA,UACsB,YAAwB,IAAe,UAC7D,sCACsB,YAAwB,IAAe,OAC7D,sEACsB,SAAwB,IAAe,OAC7D,kCACsB,SAAwB,IAAe,WAC7D,cAGA,iDAAkG,IAAe,qDACjH,CAAiB,GAGjB,GACA,qDAGA,CACA,oBACA,kBACA,CACA,0BACA,SAOA,GANA,sBACA,EACA,GACA,GACA,+BAEA,UACA,iBACA,GACA,+BAEA,MACA,CACA,YAAuB,yBAAiC,KACxD,4BACA,IACA,iBAA2D,mBAAmB,qCAAqC,0CAA0D,GAC7K,cACA,qBAEA,MACA,WACA,CACA,CAAiB,EACjB,mBACA,4CACA,wBACA,CACA,SACA,0CAAkE,SAAW,GAE7E,CAAc,SACd,4CACA,CACA,EACA,CACA,gBACA,MACA,WACA,MAAgB,2CAAqD,EACrE,OAAyB,IAAoB,OAC7C,YAEA,gBAIA,OAHA,GACA,4BAEA,KAKA,8BACA,qDAAyI,IAAe,qDAGxJ,gCACA,IACA,iBAOA,iBAA2C,mBAAmB,qBAAqB,EAAI,GACvF,aACA,SACA,gBACA,MACA,qCACA,CAAyB,IAA2B,kCACpD,CAAqB,CACrB,KAdA,CACA,YACA,sBACA,WACA,UACA,CAUA,CAAiB,EACjB,mBACA,4CACA,wBACA,CACA,kBAIA,OAHA,GACA,wCAAgE,EAAI,cAAc,aAAmB,KAErG,KAEA,SAEA,MADA,8BACA,qCAAmE,SAAW,GAE9E,qBACA,gBAA+C,IAAe,OAI9D,MAHA,gBACA,QACA,CAAqB,EACrB,6BAGA,YAAoC,IAAe,OAEnD,aADA,YACA,OACA,oBACA,eAIA,oBA1MA,wBA2MA,uBACA,GACA,QAGA,oCAAwE,IAAc,mCACtF,EACA,GACA,yCAA6D,EAAI,cAAc,aAAmB,YAAY,sBAA2B,iBAAiB,GAAY,QAAQ,4BAAwC,YAAY,2BAA+C,GAEjR,GACA,qBAEA,CAAc,SAEd,GACA,iDAEA,CAEA,cACA,CACA,gBACA,aACA,YAAgB,gCAAuC,EACvD,MACA,iBACA,GACA,4BAEA,MACA,CAKA,GAJA,kBACA,QACA,uBACA,CAAS,EACT,mBACA,IACA,gBACA,6BACA,0CAEA,wCACA,cAnPA,yBAmPA,kCAEA,sBACA,KAGA,WACA,CAAiB,EACjB,GACA,2BAQA,qBAA2C,mBAAmB,qBAAqB,EAAI,GACvF,cACA,SACA,gBACA,UACA,oCACA,CAAqB,CACrB,OACA,KAdA,CACA,YACA,sBACA,WACA,UACA,CAUA,CAAiB,EACjB,mBACA,4CACA,wBACA,CACA,SAEA,MADA,+BACA,0BAAwD,SAAW,GAEnE,GACA,mDAAuE,EAAI,cAAc,aAAmB,YAAY,SAAY,EAEpI,CAAc,SAEd,GACA,+CAEA,EAGA,CACA,yBC1SO,OACP,QACA,CCEe,UACf,eACA,aACA,+BACA,mCACA,uCACA,kDACA,qBACiB,IACjB,YACA,aAF4B,qCAIZ,EAAW,IAAO,GAAQ,oBAAf,CAAe,iBAA2C,EAAO,EAC5F,MACA,MACA,UACsB,YAAwB,IAAe,UAC7D,sCACsB,YAAwB,IAAe,OAC7D,sEACsB,SAAwB,IAAe,OAC7D,kCACsB,SAAwB,IAAe,WAC7D,cAGA,iDAAkG,IAAe,qDACjH,CAAiB,GAEP,YACV,qDAEA,CACA,qBACA,0BACA,SAOA,GANA,sBACA,EACA,GACA,YACA,+BAEA,aAGA,gBACA,MAAyB,EAAY,YACrC,4BACY,EAAY,WAExB,CACA,oBACA,MAiIA,EAhIA,WACA,MAAgB,sDAAsD,EACtE,EAA8B,MAAX,EAAW,OAAoB,EAAW,OA6H7D,GA5HA,QAD8B,EAC9B,EACA,KAF6D,GAE7D,qBA2HA,mDAAyG,IAAe,8DAA4G,IAAe,YAEnP,EACA,yCAAiH,IAAsB,EAIvI,GAHA,oBACA,iBAEA,2BACA,WACA,MACA,OAAwC,MAAZ,EAAY,gBAA+C,EAAY,oCAAuF,MAAZ,EAAY,OAAoB,EAAY,CAAhC,IAAgC,iEAC1N,CAAiB,EAKjB,WAGA,EAAU,sDAAwG,IAAe,QACjI,IACA,SACA,MACA,CACA,SACA,YACA,oCAGA,CAAwB,QAAY,gBAA+C,EAAY,oCAAuF,MAAZ,EAAY,OAAoB,EAAY,CAAhC,IAAgC,iEACtN,CAAa,GAIb,WAGA,cACA,CACA,gBACA,aACA,YAAgB,GAAa,EAQ7B,GAPmB,MAAX,GAA+B,EAAW,OAClD,QACA,SAFmB,GAA+B,CAElD,UACA,CAAS,EACT,YACA,qBAEA,qBACA,YAA0B,IAAe,YACzC,0BAAiD,EAAI,OAAQ,IAAoB,WACjF,qBAAgC,WAAY,KAC5C,kCACA,OACA,kBACA,gBACA,iBACA,mBACA,CACA,6CAAgE,IAAgB,2BAChF,EAAU,gBAAuB,IAAe,iBAAwB,IAAe,WACvF,eAA4C,IAAe,UAC3D,sBAAiD,EAAI,SAAoB,IAAoB,UAAY,IAAoB,QAO7H,GANA,oBAAgC,WAAY,KAC5C,kCAEA,GACA,4CAA4D,EAAI,EAAE,sBAAoC,IAAmB,CAAG,IAAU,CAAG,IAAgB,CAAC,IAAe,IAAoB,UAAY,IAAoB,gDAE7N,0BAAwD,IAAe,WACvE,OACA,kBACA,gBACA,sBACA,mBACA,CACA,6CAAoE,IAAgB,oBACpF,CACA,EAAU,gBAAuB,IAAe,QAChD,yBAAmD,IAAoB,OACvE,qBAAgC,WAAY,KAC5C,0CACA,KACA,YACa,EACb,EACA,CACA,iBACA,UACA,KAAiB,IAAoB,OAGrC,OAAuB,QAAS,iDAChC,MAAiB,IAAoB,OACrC,OAAuB,QAAS,8BAChC,MAAiB,IAAoB,OACrC,KAAiB,IAAoB,UACrC,KAAiB,IAAoB,WACrC,OAAuB,QAAS,4BAChC,SACA,0CAA8D,EAAK,EACnE,CACA,CACA,4BC5RA,IAAME,GAAa,uBAEZ,SAASC,GAAeC,CAAa,EAK1C,MAJIC,CAAAA,EAAAA,GAAAA,EAAAA,CAA0BA,CAACD,KAC7BA,EAAQE,CAD6B,EAC7BA,GAAAA,EAAAA,CAAmCA,CAACF,GAAOG,gBAAAA,EAG9CL,GAAWM,IAAI,CAACJ,EACzB,CEQW,eACX,4CACA,SCpBW,UACX,UAGA,iCAIA,GACA,wBACA,CAUA,OACA,QAIA,wBACA,eAEA,SADA,kFAGA,SADA,kFAHA,QAMA,CAMA,SACA,mBACA,CAGA,QACA,kBACA,CACA,gBC/BO,UACP,gBAAkB,kOAAuN,EACzO,WACA,oBACA,4CACA,gCACA,yCACA,aACA,KAqBU,GACV,qDAtBA,CAEA,WACA,8BACA,gBAEA,OACA,GACA,8CAEA,EAAsC,IAElB,EAAU,SAFuB,EAEvB,EAC9B,iBACA,CAAiB,UACjB,GACA,yCAEA,EAAsC,GAGtC,CAGA,CAHU,KAHsC,EAMhD,+BAEA,uDAEA,WACA,oBACA,qEAIA,KADA,WACA,GACA,sBACA,uBACA,mCACA,2BACA,2BAAqC,GAAuB,wBAC5D,2BACA,SACA,EAA2B,IAA2B,0FACtD,+BAEA,GAAmF,UAAnF,SAAiD,IAAkC,KAAiC,IAAsC,0FAC1J,KAA6C,IAAkC,cAE/E,GACA,0BACA,MACA,KACA,cACA,gBACA,kBACA,qBACA,kBACA,qBACA,EAAa,CAEb,CACA,6BAGA,qEAGA,iCAAoE,GAAO,aAE3E,MADA,4BAEA,CACA,kBACA,WHpFI,SGoFmC,QHpFlBI,IAAI,CAACxB,IAAS,CAACmB,GGoFoB,GHnF/C,QADyCA,CGoFM,EHlFhDnB,MGkFgD,EHjF9C,SACAyB,CAAAA,EAAAA,GAAAA,CAAAA,CAAkBA,CAACzB,EGiF7B,CACA,oBACA,OACA,sEACA,CACA,cACA,4BACA,oBACA,GACA,QAEA,sBACA,YACA,IACA,oBAEA,CACA,CAAS,EAET,OADA,oBACA,CACA,CACA,uBACA,QACA,sFACA,CAEA,6BAAyC,EAIzC,SACA,kBACA,kBACA,WAEA,wCACA,aACA,KACA,IACA,mCACA,SACA,oBACA,oBACA,YAEA,UACA,mBACA,SACA,CAAiC,GAEjC,CACA,CAAqB,GAErB,mBAEA,oCACA,oBAEA,IACA,eACA,WACA,WAGA,YACA,CAAkB,SAClB,uCACA,CACA,EAAc,uCACd,aAEA,aADA,iBACA,YACA,SACA,IACA,kBACA,UAAuC,EAAI,GAAG,kCAC9C,mBACA,EAEA,gBAEqB,YAAc,EACnC,CAEA,EAAc,8CACd,aACA,wBACA,uBACA,oBACA,EACA,EACA,YACiB,CACjB,EAAc,6BACd,eACA,kBAGA,uCAAkD,oDAA2E,WAC7H,yCACA,sBAvEA,KAyEA,6BACA,EACA,SACA,EACA,OACA,WACA,cACA,WACA,iBACA,YACA,QACA,EACA,CAC+C,MAC/C,EAGA,kBACA,OAJA,EAIA,wCAHA,sFAIA,CAIA,CAJU,KAAK,CAMf,CAHS,GAGT,SACA,QAwDA,EACA,EAtDA,+BAA8C,IAAoB,QAClE,MAAkC,IAA4B,YAC9D,IAAoD,QAAwB,SAC5E,MACA,qBACA,6BAAkF,IAAe,OACjG,OACA,WACA,QACA,mBACA,aACA,CAEA,CACA,CAGA,gDAAiE,IAAoB,2DACrF,YAEA,eAAgB,GAAa,EAC7B,+BAA4D,IAAoB,QAChF,WACA,eACA,wDACA,sDAA6H,IAAe,QAM5I,OAJA,cACA,eACA,CAEA,SACA,MACA,0DACA,CAAa,EACb,YAEA,wBAEA,MADA,mEACA,EACA,eACA,OACA,UACA,OACA,KAA0B,IAAe,OACzC,OACA,YACA,CAAiB,CACjB,+DACA,YACA,CACA,CACA,iCAAyD,GAAO,UAGhE,sCACA,KACA,KAAmC,IAAc,EAGjD,UADA,6HAA8L,IAAoB,uBAClN,oDAEA,GACA,IACA,UACA,gBACA,kBACA,cACA,YACA,GAEA,wDAMA,GACA,UACA,WACA,gBACA,kBACA,YACA,EACA,uBAEA,CACA,CAEA,iBAKA,gDAAyE,IAAe,QACxF,MAAkC,IAA4B,YAC9D,IAA6D,QAA2B,SACxF,GACA,gBAEA,CACA,2DACA,oCAEA,+BACA,iBAEA,wCACA,YACA,mFAAuG,GAAU,SAEjH,MACA,CACA,IACA,KAGA,sCACA,2BAA2C,GAAO,iBAElD,uDACA,CAAU,SACV,wDACA,CACA,CACA,2BCjWO,UACP,eACA,kBACY,GAAc,aAC1B,cAA2B,QAAe,CAAC,SAAa,cAExD,CAKA,eACA,gCAEA,gBACA,6BAEA,SACA,0BACA,EACA,CACA,2BACA,iBAHA,IAKA,CACA,QACA,iBACA,6BACA,EACA,CACA,QACA,EAHA,IAIA,QACA,6BACA,GAEA,IACA,CACA,CCtCA,iDACA,GACA,gBACA,iBACA,mBACA,wBACA,qBACA,CAAC,6BCGU,UAMX,iBACA,mBACA,kBAEA,iBAA2B,GAAY,aACvC,CASA,iBAEA,6BAEA,UACmB,EAAO,CAC1B,IAD0B,CAE1B,gBAAgC,IAEhC,yBACA,CAAa,CAEb,CACA,mBAUA,WAAgB,GATM,QAAQ,EAC9B,qCACA,sCACA,4BAEA,WAAwB,CAExB,gBACA,CAAS,EACgB,4BAAoC,SAAsB,0BACnF,sBACA,MAAoC,EAIpC,GACA,SACA,CANmD,iBAMnD,CACA,UACA,SAA0B,CAC1B,gBAAiC,CACjC,QAT6B,IAU7B,eAVgD,EAUhD,CACa,CACb,YACA,2BACA,YACA,0BACA,wBACA,cACA,aAAiC,KAA6B,UAC9D,EAAsC,CACrB,CACjB,GAFqF,KAErF,GACA,yDAEA,EAEA,qCACA,GDhEA,oBCkEA,CAmBA,OAlBA,+BACA,sCAEA,4BACA,OAQA,eADgC,SdrFzB,KACP,KcoFmD,CdpFnD,qBACA,UACA,GAEA,CAAK,EACL,uBACA,Ec8EmD,8BACnD,CACA,gBACA,wBACA,kBACa,EATb,oCAWA,CACA,CACA,gCC5GA,qCAA6C,CAC7C,QACA,CAAC,EAAC,SAKF,KACA,0CACA,cACA,SACK,CACL,EACA,GACA,0BACA,QACA,CAAK,CACL,uBACA,QACA,CACA,CAAC,EAED,UADyB,EAAQ,MAAkB,EACnD,SADgC,QAChC,CACA,gBACA,yCACA,KAMA,OACA,IAJA,SAKA,UAJA,UAKA,SAJA,gCAKA,CACA,CACA,kBACA,oBACA,EAGA,WAFA,GAGA,CACA,uBACA,eAIA,KACA,cAGA,qDCrDA,qCAA6C,CAC7C,QACA,CAAC,EAAC,SAMF,KACA,0CACA,cACA,SACK,CACL,EACA,GACA,uBACA,QACA,CAAK,CACL,0BACA,QACA,CAAK,CACL,kBACA,QACA,CACA,CAAC,EACD,MAAiB,EAAQ,KAAW,EACpC,GACA,OACA,MAEA,cACA,gBAEA,EAkBA,sBACA,QAAY,6GAAsG,EAClH,OACA,WACA,YACA,SACA,MACA,SACA,YACA,cACA,CACA,kBA5BA,WACA,sCAEA,YAAmB,WAAkB,IACrC,kBACA,aACA,KACA,CAQA,OADA,EAFA,IAFA,2CAEA,YAEA,2DACA,YACA,IAcA,CACA,CACA,OAAyB,EAAM,oDAC/B,QACA,cACA,YACA,OACA,WACA,WACA,gBACA,CACA,CACA,CAQA,sBACA,gCACA,MAEA,YAEA,aAAY,eAAsB,EAClC,eACA,8BAAyD,EAAU,GACnE,cACA,uBACA,MAEA,WACA,CACA,CAAK,EACL,SACA,qCAAiD,SAAY,GAE7D,qBACA,KAAY,GAAM,EAClB,UACA,eACA,WACA,aACA,gBACA,sCAAsD,UAAgB,EAAE,MAAY,GAGpF,CACA,gBArCA,GACA,WAAY,oBAAwB,WACpC,sBAA+B,EAAM,uBACrC,SACA,sBACA,CAAK,CACL,EA+BA,EACA,CACA,cAUA,OATI,GAAM,qBACV,YAGA,oDACA,OAEA,qBACA,EACA,KACQ,GAAM,QACd,CACA,gCCjIA,qCAA6C,CAC7C,QACA,CAAC,EAWD,SANA,KACA,0CACA,cACA,SACK,CACL,EACA,GACA,6BACA,QACA,CAAK,CACL,8BACA,QACA,CACA,CAAC,EACD,MAAiB,EAAQ,KAAW,EACpC,EAAe,EAAQ,IAAS,EADR,SAExB,EADsB,EAEtB,2BAAsC,GAAM,OAC5C,CACA,cACA,qDACA", "sources": ["webpack://_N_E/./node_modules/next/dist/compiled/p-queue/index.js", "webpack://_N_E/./node_modules/next/dist/compiled/string-hash/index.js", "webpack://_N_E/./node_modules/next/dist/esm/server/app-render/action-utils.js", "webpack://_N_E/../../../src/server/app-render/encryption-utils.ts", "webpack://_N_E/./node_modules/next/dist/esm/server/api-utils/index.js", "webpack://_N_E/./node_modules/next/dist/esm/server/async-storage/draft-mode-provider.js", "webpack://_N_E/./node_modules/next/dist/esm/server/async-storage/request-store.js", "webpack://_N_E/./node_modules/next/dist/esm/server/after/revalidation-utils.js", "webpack://_N_E/./node_modules/next/dist/esm/server/after/after-context.js", "webpack://_N_E/./node_modules/next/dist/esm/server/async-storage/work-store.js", "webpack://_N_E/./node_modules/next/dist/esm/server/route-modules/app-route/module.compiled.js", "webpack://_N_E/./node_modules/next/dist/esm/server/route-modules/route-module.js", "webpack://_N_E/./node_modules/next/dist/esm/server/web/http.js", "webpack://_N_E/./node_modules/next/dist/esm/server/lib/implicit-tags.js", "webpack://_N_E/./node_modules/next/dist/esm/server/route-modules/app-route/helpers/get-pathname-from-absolute-path.js", "webpack://_N_E/./node_modules/next/dist/esm/server/route-modules/app-route/helpers/auto-implement-methods.js", "webpack://_N_E/./node_modules/next/dist/esm/server/route-modules/app-route/helpers/parsed-url-query-to-params.js", "webpack://_N_E/./node_modules/next/dist/esm/server/app-render/create-error-handler.js", "webpack://_N_E/./node_modules/next/dist/esm/server/app-render/prospective-render-utils.js", "webpack://_N_E/./node_modules/next/dist/esm/server/route-modules/app-route/shared-modules.js", "webpack://_N_E/./node_modules/next/dist/esm/server/lib/server-action-request-meta.js", "webpack://_N_E/./node_modules/next/dist/esm/server/route-modules/app-route/helpers/clean-url.js", "webpack://_N_E/./node_modules/next/dist/esm/server/route-modules/app-route/helpers/is-static-gen-enabled.js", "webpack://_N_E/./node_modules/next/dist/esm/server/app-render/cache-signal.js", "webpack://_N_E/./node_modules/next/dist/esm/server/route-modules/app-route/module.js", "webpack://_N_E/./node_modules/next/dist/esm/server/web/globals.js", "webpack://_N_E/./node_modules/next/dist/esm/server/web/spec-extension/fetch-event.js", "webpack://_N_E/../../../../src/shared/lib/router/utils/relativize-url.ts", "webpack://_N_E/./node_modules/next/dist/esm/server/internal-utils.js", "webpack://_N_E/./node_modules/next/dist/esm/server/web/web-on-close.js", "webpack://_N_E/./node_modules/next/dist/esm/server/web/get-edge-preview-props.js", "webpack://_N_E/./node_modules/next/dist/esm/server/after/builtin-request-context.js", "webpack://_N_E/./node_modules/next/dist/esm/server/web/adapter.js", "webpack://_N_E/./node_modules/next/dist/esm/server/lib/incremental-cache/fetch-cache.js", "webpack://_N_E/./node_modules/next/dist/esm/server/lib/incremental-cache/tags-manifest.external.js", "webpack://_N_E/./node_modules/next/dist/esm/server/lib/incremental-cache/file-system-cache.js", "webpack://_N_E/../../../../src/shared/lib/router/utils/is-dynamic.ts", "webpack://_N_E/../../../../src/shared/lib/page-path/normalize-page-path.ts", "webpack://_N_E/./node_modules/next/dist/esm/server/lib/to-route.js", "webpack://_N_E/./node_modules/next/dist/esm/server/lib/incremental-cache/shared-revalidate-timings.js", "webpack://_N_E/./node_modules/next/dist/esm/server/lib/incremental-cache/index.js", "webpack://_N_E/./node_modules/next/dist/esm/server/route-matchers/route-matcher.js", "webpack://_N_E/./node_modules/next/dist/esm/server/web/internal-edge-wait-until.js", "webpack://_N_E/./node_modules/next/dist/esm/server/web/edge-route-module-wrapper.js", "webpack://_N_E/./node_modules/next/dist/experimental/testmode/context.js", "webpack://_N_E/./node_modules/next/dist/experimental/testmode/fetch.js", "webpack://_N_E/./node_modules/next/dist/experimental/testmode/server-edge.js"], "sourcesContent": ["(()=>{\"use strict\";var e={993:e=>{var t=Object.prototype.hasOwnProperty,n=\"~\";function Events(){}if(Object.create){Events.prototype=Object.create(null);if(!(new Events).__proto__)n=false}function EE(e,t,n){this.fn=e;this.context=t;this.once=n||false}function addListener(e,t,r,i,s){if(typeof r!==\"function\"){throw new TypeError(\"The listener must be a function\")}var o=new EE(r,i||e,s),u=n?n+t:t;if(!e._events[u])e._events[u]=o,e._eventsCount++;else if(!e._events[u].fn)e._events[u].push(o);else e._events[u]=[e._events[u],o];return e}function clearEvent(e,t){if(--e._eventsCount===0)e._events=new Events;else delete e._events[t]}function EventEmitter(){this._events=new Events;this._eventsCount=0}EventEmitter.prototype.eventNames=function eventNames(){var e=[],r,i;if(this._eventsCount===0)return e;for(i in r=this._events){if(t.call(r,i))e.push(n?i.slice(1):i)}if(Object.getOwnPropertySymbols){return e.concat(Object.getOwnPropertySymbols(r))}return e};EventEmitter.prototype.listeners=function listeners(e){var t=n?n+e:e,r=this._events[t];if(!r)return[];if(r.fn)return[r.fn];for(var i=0,s=r.length,o=new Array(s);i<s;i++){o[i]=r[i].fn}return o};EventEmitter.prototype.listenerCount=function listenerCount(e){var t=n?n+e:e,r=this._events[t];if(!r)return 0;if(r.fn)return 1;return r.length};EventEmitter.prototype.emit=function emit(e,t,r,i,s,o){var u=n?n+e:e;if(!this._events[u])return false;var a=this._events[u],l=arguments.length,c,h;if(a.fn){if(a.once)this.removeListener(e,a.fn,undefined,true);switch(l){case 1:return a.fn.call(a.context),true;case 2:return a.fn.call(a.context,t),true;case 3:return a.fn.call(a.context,t,r),true;case 4:return a.fn.call(a.context,t,r,i),true;case 5:return a.fn.call(a.context,t,r,i,s),true;case 6:return a.fn.call(a.context,t,r,i,s,o),true}for(h=1,c=new Array(l-1);h<l;h++){c[h-1]=arguments[h]}a.fn.apply(a.context,c)}else{var _=a.length,f;for(h=0;h<_;h++){if(a[h].once)this.removeListener(e,a[h].fn,undefined,true);switch(l){case 1:a[h].fn.call(a[h].context);break;case 2:a[h].fn.call(a[h].context,t);break;case 3:a[h].fn.call(a[h].context,t,r);break;case 4:a[h].fn.call(a[h].context,t,r,i);break;default:if(!c)for(f=1,c=new Array(l-1);f<l;f++){c[f-1]=arguments[f]}a[h].fn.apply(a[h].context,c)}}}return true};EventEmitter.prototype.on=function on(e,t,n){return addListener(this,e,t,n,false)};EventEmitter.prototype.once=function once(e,t,n){return addListener(this,e,t,n,true)};EventEmitter.prototype.removeListener=function removeListener(e,t,r,i){var s=n?n+e:e;if(!this._events[s])return this;if(!t){clearEvent(this,s);return this}var o=this._events[s];if(o.fn){if(o.fn===t&&(!i||o.once)&&(!r||o.context===r)){clearEvent(this,s)}}else{for(var u=0,a=[],l=o.length;u<l;u++){if(o[u].fn!==t||i&&!o[u].once||r&&o[u].context!==r){a.push(o[u])}}if(a.length)this._events[s]=a.length===1?a[0]:a;else clearEvent(this,s)}return this};EventEmitter.prototype.removeAllListeners=function removeAllListeners(e){var t;if(e){t=n?n+e:e;if(this._events[t])clearEvent(this,t)}else{this._events=new Events;this._eventsCount=0}return this};EventEmitter.prototype.off=EventEmitter.prototype.removeListener;EventEmitter.prototype.addListener=EventEmitter.prototype.on;EventEmitter.prefixed=n;EventEmitter.EventEmitter=EventEmitter;if(true){e.exports=EventEmitter}},213:e=>{e.exports=(e,t)=>{t=t||(()=>{});return e.then((e=>new Promise((e=>{e(t())})).then((()=>e))),(e=>new Promise((e=>{e(t())})).then((()=>{throw e}))))}},574:(e,t)=>{Object.defineProperty(t,\"__esModule\",{value:true});function lowerBound(e,t,n){let r=0;let i=e.length;while(i>0){const s=i/2|0;let o=r+s;if(n(e[o],t)<=0){r=++o;i-=s+1}else{i=s}}return r}t[\"default\"]=lowerBound},821:(e,t,n)=>{Object.defineProperty(t,\"__esModule\",{value:true});const r=n(574);class PriorityQueue{constructor(){this._queue=[]}enqueue(e,t){t=Object.assign({priority:0},t);const n={priority:t.priority,run:e};if(this.size&&this._queue[this.size-1].priority>=t.priority){this._queue.push(n);return}const i=r.default(this._queue,n,((e,t)=>t.priority-e.priority));this._queue.splice(i,0,n)}dequeue(){const e=this._queue.shift();return e===null||e===void 0?void 0:e.run}filter(e){return this._queue.filter((t=>t.priority===e.priority)).map((e=>e.run))}get size(){return this._queue.length}}t[\"default\"]=PriorityQueue},816:(e,t,n)=>{const r=n(213);class TimeoutError extends Error{constructor(e){super(e);this.name=\"TimeoutError\"}}const pTimeout=(e,t,n)=>new Promise(((i,s)=>{if(typeof t!==\"number\"||t<0){throw new TypeError(\"Expected `milliseconds` to be a positive number\")}if(t===Infinity){i(e);return}const o=setTimeout((()=>{if(typeof n===\"function\"){try{i(n())}catch(e){s(e)}return}const r=typeof n===\"string\"?n:`Promise timed out after ${t} milliseconds`;const o=n instanceof Error?n:new TimeoutError(r);if(typeof e.cancel===\"function\"){e.cancel()}s(o)}),t);r(e.then(i,s),(()=>{clearTimeout(o)}))}));e.exports=pTimeout;e.exports[\"default\"]=pTimeout;e.exports.TimeoutError=TimeoutError}};var t={};function __nccwpck_require__(n){var r=t[n];if(r!==undefined){return r.exports}var i=t[n]={exports:{}};var s=true;try{e[n](i,i.exports,__nccwpck_require__);s=false}finally{if(s)delete t[n]}return i.exports}if(typeof __nccwpck_require__!==\"undefined\")__nccwpck_require__.ab=__dirname+\"/\";var n={};(()=>{var e=n;Object.defineProperty(e,\"__esModule\",{value:true});const t=__nccwpck_require__(993);const r=__nccwpck_require__(816);const i=__nccwpck_require__(821);const empty=()=>{};const s=new r.TimeoutError;class PQueue extends t{constructor(e){var t,n,r,s;super();this._intervalCount=0;this._intervalEnd=0;this._pendingCount=0;this._resolveEmpty=empty;this._resolveIdle=empty;e=Object.assign({carryoverConcurrencyCount:false,intervalCap:Infinity,interval:0,concurrency:Infinity,autoStart:true,queueClass:i.default},e);if(!(typeof e.intervalCap===\"number\"&&e.intervalCap>=1)){throw new TypeError(`Expected \\`intervalCap\\` to be a number from 1 and up, got \\`${(n=(t=e.intervalCap)===null||t===void 0?void 0:t.toString())!==null&&n!==void 0?n:\"\"}\\` (${typeof e.intervalCap})`)}if(e.interval===undefined||!(Number.isFinite(e.interval)&&e.interval>=0)){throw new TypeError(`Expected \\`interval\\` to be a finite number >= 0, got \\`${(s=(r=e.interval)===null||r===void 0?void 0:r.toString())!==null&&s!==void 0?s:\"\"}\\` (${typeof e.interval})`)}this._carryoverConcurrencyCount=e.carryoverConcurrencyCount;this._isIntervalIgnored=e.intervalCap===Infinity||e.interval===0;this._intervalCap=e.intervalCap;this._interval=e.interval;this._queue=new e.queueClass;this._queueClass=e.queueClass;this.concurrency=e.concurrency;this._timeout=e.timeout;this._throwOnTimeout=e.throwOnTimeout===true;this._isPaused=e.autoStart===false}get _doesIntervalAllowAnother(){return this._isIntervalIgnored||this._intervalCount<this._intervalCap}get _doesConcurrentAllowAnother(){return this._pendingCount<this._concurrency}_next(){this._pendingCount--;this._tryToStartAnother();this.emit(\"next\")}_resolvePromises(){this._resolveEmpty();this._resolveEmpty=empty;if(this._pendingCount===0){this._resolveIdle();this._resolveIdle=empty;this.emit(\"idle\")}}_onResumeInterval(){this._onInterval();this._initializeIntervalIfNeeded();this._timeoutId=undefined}_isIntervalPaused(){const e=Date.now();if(this._intervalId===undefined){const t=this._intervalEnd-e;if(t<0){this._intervalCount=this._carryoverConcurrencyCount?this._pendingCount:0}else{if(this._timeoutId===undefined){this._timeoutId=setTimeout((()=>{this._onResumeInterval()}),t)}return true}}return false}_tryToStartAnother(){if(this._queue.size===0){if(this._intervalId){clearInterval(this._intervalId)}this._intervalId=undefined;this._resolvePromises();return false}if(!this._isPaused){const e=!this._isIntervalPaused();if(this._doesIntervalAllowAnother&&this._doesConcurrentAllowAnother){const t=this._queue.dequeue();if(!t){return false}this.emit(\"active\");t();if(e){this._initializeIntervalIfNeeded()}return true}}return false}_initializeIntervalIfNeeded(){if(this._isIntervalIgnored||this._intervalId!==undefined){return}this._intervalId=setInterval((()=>{this._onInterval()}),this._interval);this._intervalEnd=Date.now()+this._interval}_onInterval(){if(this._intervalCount===0&&this._pendingCount===0&&this._intervalId){clearInterval(this._intervalId);this._intervalId=undefined}this._intervalCount=this._carryoverConcurrencyCount?this._pendingCount:0;this._processQueue()}_processQueue(){while(this._tryToStartAnother()){}}get concurrency(){return this._concurrency}set concurrency(e){if(!(typeof e===\"number\"&&e>=1)){throw new TypeError(`Expected \\`concurrency\\` to be a number from 1 and up, got \\`${e}\\` (${typeof e})`)}this._concurrency=e;this._processQueue()}async add(e,t={}){return new Promise(((n,i)=>{const run=async()=>{this._pendingCount++;this._intervalCount++;try{const o=this._timeout===undefined&&t.timeout===undefined?e():r.default(Promise.resolve(e()),t.timeout===undefined?this._timeout:t.timeout,(()=>{if(t.throwOnTimeout===undefined?this._throwOnTimeout:t.throwOnTimeout){i(s)}return undefined}));n(await o)}catch(e){i(e)}this._next()};this._queue.enqueue(run,t);this._tryToStartAnother();this.emit(\"add\")}))}async addAll(e,t){return Promise.all(e.map((async e=>this.add(e,t))))}start(){if(!this._isPaused){return this}this._isPaused=false;this._processQueue();return this}pause(){this._isPaused=true}clear(){this._queue=new this._queueClass}async onEmpty(){if(this._queue.size===0){return}return new Promise((e=>{const t=this._resolveEmpty;this._resolveEmpty=()=>{t();e()}}))}async onIdle(){if(this._pendingCount===0&&this._queue.size===0){return}return new Promise((e=>{const t=this._resolveIdle;this._resolveIdle=()=>{t();e()}}))}get size(){return this._queue.size}sizeBy(e){return this._queue.filter(e).length}get pending(){return this._pendingCount}get isPaused(){return this._isPaused}get timeout(){return this._timeout}set timeout(e){this._timeout=e}}e[\"default\"]=PQueue})();module.exports=n})();", "(()=>{\"use strict\";var e={328:e=>{function hash(e){var r=5381,_=e.length;while(_){r=r*33^e.charCodeAt(--_)}return r>>>0}e.exports=hash}};var r={};function __nccwpck_require__(_){var a=r[_];if(a!==undefined){return a.exports}var t=r[_]={exports:{}};var i=true;try{e[_](t,t.exports,__nccwpck_require__);i=false}finally{if(i)delete r[_]}return t.exports}if(typeof __nccwpck_require__!==\"undefined\")__nccwpck_require__.ab=__dirname+\"/\";var _=__nccwpck_require__(328);module.exports=_})();", "import { normalizeAppPath } from '../../shared/lib/router/utils/app-paths';\nimport { pathHasPrefix } from '../../shared/lib/router/utils/path-has-prefix';\nimport { removePathPrefix } from '../../shared/lib/router/utils/remove-path-prefix';\nimport { workAsyncStorage } from './work-async-storage.external';\n// This function creates a Flight-acceptable server module map proxy from our\n// Server Reference Manifest similar to our client module map.\n// This is because our manifest contains a lot of internal Next.js data that\n// are relevant to the runtime, workers, etc. that <PERSON>act doesn't need to know.\nexport function createServerModuleMap({ serverActionsManifest }) {\n    return new Proxy({}, {\n        get: (_, id)=>{\n            const workers = serverActionsManifest[process.env.NEXT_RUNTIME === 'edge' ? 'edge' : 'node'][id].workers;\n            const workStore = workAsyncStorage.getStore();\n            let workerEntry;\n            if (workStore) {\n                workerEntry = workers[normalizeWorkerPageName(workStore.page)];\n            } else {\n                // If there's no work store defined, we can assume that a server\n                // module map is needed during module evaluation, e.g. to create a\n                // server action using a higher-order function. Therefore it should be\n                // safe to return any entry from the manifest that matches the action\n                // ID. They all refer to the same module ID, which must also exist in\n                // the current page bundle. TODO: This is currently not guaranteed in\n                // Turbopack, and needs to be fixed.\n                workerEntry = Object.values(workers).at(0);\n            }\n            if (!workerEntry) {\n                return undefined;\n            }\n            const { moduleId, async } = workerEntry;\n            return {\n                id: moduleId,\n                name: id,\n                chunks: [],\n                async\n            };\n        }\n    });\n}\n/**\n * Checks if the requested action has a worker for the current page.\n * If not, it returns the first worker that has a handler for the action.\n */ export function selectWorkerForForwarding(actionId, pageName, serverActionsManifest) {\n    var _serverActionsManifest__actionId;\n    const workers = (_serverActionsManifest__actionId = serverActionsManifest[process.env.NEXT_RUNTIME === 'edge' ? 'edge' : 'node'][actionId]) == null ? void 0 : _serverActionsManifest__actionId.workers;\n    const workerName = normalizeWorkerPageName(pageName);\n    // no workers, nothing to forward to\n    if (!workers) return;\n    // if there is a worker for this page, no need to forward it.\n    if (workers[workerName]) {\n        return;\n    }\n    // otherwise, grab the first worker that has a handler for this action id\n    return denormalizeWorkerPageName(Object.keys(workers)[0]);\n}\n/**\n * The flight entry loader keys actions by bundlePath.\n * bundlePath corresponds with the relative path (including 'app') to the page entrypoint.\n */ function normalizeWorkerPageName(pageName) {\n    if (pathHasPrefix(pageName, 'app')) {\n        return pageName;\n    }\n    return 'app' + pageName;\n}\n/**\n * Converts a bundlePath (relative path to the entrypoint) to a routable page name\n */ function denormalizeWorkerPageName(bundlePath) {\n    return normalizeAppPath(removePathPrefix(bundlePath, 'app'));\n}\n\n//# sourceMappingURL=action-utils.js.map", "import type { ActionManifest } from '../../build/webpack/plugins/flight-client-entry-plugin'\nimport type {\n  ClientReferenceManifest,\n  ClientReferenceManifestForRsc,\n} from '../../build/webpack/plugins/flight-manifest-plugin'\nimport type { DeepReadonly } from '../../shared/lib/deep-readonly'\nimport { InvariantError } from '../../shared/lib/invariant-error'\nimport { normalizeAppPath } from '../../shared/lib/router/utils/app-paths'\nimport { workAsyncStorage } from './work-async-storage.external'\n\nlet __next_loaded_action_key: CryptoKey\n\nexport function arrayBufferToString(\n  buffer: ArrayBuffer | Uint8Array<ArrayBufferLike>\n) {\n  const bytes = new Uint8Array(buffer)\n  const len = bytes.byteLength\n\n  // @anonrig: V8 has a limit of 65535 arguments in a function.\n  // For len < 65535, this is faster.\n  // https://github.com/vercel/next.js/pull/56377#pullrequestreview-1656181623\n  if (len < 65535) {\n    return String.fromCharCode.apply(null, bytes as unknown as number[])\n  }\n\n  let binary = ''\n  for (let i = 0; i < len; i++) {\n    binary += String.fromCharCode(bytes[i])\n  }\n  return binary\n}\n\nexport function stringToUint8Array(binary: string) {\n  const len = binary.length\n  const arr = new Uint8Array(len)\n\n  for (let i = 0; i < len; i++) {\n    arr[i] = binary.charCodeAt(i)\n  }\n\n  return arr\n}\n\nexport function encrypt(key: CryptoKey, iv: Uint8Array, data: Uint8Array) {\n  return crypto.subtle.encrypt(\n    {\n      name: 'AES-GCM',\n      iv,\n    },\n    key,\n    data\n  )\n}\n\nexport function decrypt(key: CryptoKey, iv: Uint8Array, data: Uint8Array) {\n  return crypto.subtle.decrypt(\n    {\n      name: 'AES-GCM',\n      iv,\n    },\n    key,\n    data\n  )\n}\n\n// This is a global singleton that is used to encode/decode the action bound args from\n// the closure. This can't be using a AsyncLocalStorage as it might happen on the module\n// level. Since the client reference manifest won't be mutated, let's use a global singleton\n// to keep it.\nconst SERVER_ACTION_MANIFESTS_SINGLETON = Symbol.for(\n  'next.server.action-manifests'\n)\n\nexport function setReferenceManifestsSingleton({\n  page,\n  clientReferenceManifest,\n  serverActionsManifest,\n  serverModuleMap,\n}: {\n  page: string\n  clientReferenceManifest: DeepReadonly<ClientReferenceManifest>\n  serverActionsManifest: DeepReadonly<ActionManifest>\n  serverModuleMap: {\n    [id: string]: {\n      id: string\n      chunks: string[]\n      name: string\n    }\n  }\n}) {\n  // @ts-expect-error\n  const clientReferenceManifestsPerPage = globalThis[\n    SERVER_ACTION_MANIFESTS_SINGLETON\n  ]?.clientReferenceManifestsPerPage as\n    | undefined\n    | DeepReadonly<Record<string, ClientReferenceManifest>>\n\n  // @ts-expect-error\n  globalThis[SERVER_ACTION_MANIFESTS_SINGLETON] = {\n    clientReferenceManifestsPerPage: {\n      ...clientReferenceManifestsPerPage,\n      [normalizeAppPath(page)]: clientReferenceManifest,\n    },\n    serverActionsManifest,\n    serverModuleMap,\n  }\n}\n\nexport function getServerModuleMap() {\n  const serverActionsManifestSingleton = (globalThis as any)[\n    SERVER_ACTION_MANIFESTS_SINGLETON\n  ] as {\n    serverModuleMap: {\n      [id: string]: {\n        id: string\n        chunks: string[]\n        name: string\n      }\n    }\n  }\n\n  if (!serverActionsManifestSingleton) {\n    throw new InvariantError('Missing manifest for Server Actions.')\n  }\n\n  return serverActionsManifestSingleton.serverModuleMap\n}\n\nexport function getClientReferenceManifestForRsc(): DeepReadonly<ClientReferenceManifestForRsc> {\n  const serverActionsManifestSingleton = (globalThis as any)[\n    SERVER_ACTION_MANIFESTS_SINGLETON\n  ] as {\n    clientReferenceManifestsPerPage: DeepReadonly<\n      Record<string, ClientReferenceManifest>\n    >\n  }\n\n  if (!serverActionsManifestSingleton) {\n    throw new InvariantError('Missing manifest for Server Actions.')\n  }\n\n  const { clientReferenceManifestsPerPage } = serverActionsManifestSingleton\n  const workStore = workAsyncStorage.getStore()\n\n  if (!workStore) {\n    // If there's no work store defined, we can assume that a client reference\n    // manifest is needed during module evaluation, e.g. to create a server\n    // action using a higher-order function. This might also use client\n    // components which need to be serialized by Flight, and therefore client\n    // references need to be resolvable. To make this work, we're returning a\n    // merged manifest across all pages. This is fine as long as the module IDs\n    // are not page specific, which they are not for Webpack. TODO: Fix this in\n    // Turbopack.\n    return mergeClientReferenceManifests(clientReferenceManifestsPerPage)\n  }\n\n  const clientReferenceManifest =\n    clientReferenceManifestsPerPage[workStore.route]\n\n  if (!clientReferenceManifest) {\n    throw new InvariantError(\n      `Missing Client Reference Manifest for ${workStore.route}.`\n    )\n  }\n\n  return clientReferenceManifest\n}\n\nexport async function getActionEncryptionKey() {\n  if (__next_loaded_action_key) {\n    return __next_loaded_action_key\n  }\n\n  const serverActionsManifestSingleton = (globalThis as any)[\n    SERVER_ACTION_MANIFESTS_SINGLETON\n  ] as {\n    serverActionsManifest: DeepReadonly<ActionManifest>\n  }\n\n  if (!serverActionsManifestSingleton) {\n    throw new InvariantError('Missing manifest for Server Actions.')\n  }\n\n  const rawKey =\n    process.env.NEXT_SERVER_ACTIONS_ENCRYPTION_KEY ||\n    serverActionsManifestSingleton.serverActionsManifest.encryptionKey\n\n  if (rawKey === undefined) {\n    throw new InvariantError('Missing encryption key for Server Actions')\n  }\n\n  __next_loaded_action_key = await crypto.subtle.importKey(\n    'raw',\n    stringToUint8Array(atob(rawKey)),\n    'AES-GCM',\n    true,\n    ['encrypt', 'decrypt']\n  )\n\n  return __next_loaded_action_key\n}\n\nfunction mergeClientReferenceManifests(\n  clientReferenceManifestsPerPage: DeepReadonly<\n    Record<string, ClientReferenceManifest>\n  >\n): ClientReferenceManifestForRsc {\n  const clientReferenceManifests = Object.values(\n    clientReferenceManifestsPerPage as Record<string, ClientReferenceManifest>\n  )\n\n  const mergedClientReferenceManifest: ClientReferenceManifestForRsc = {\n    clientModules: {},\n    edgeRscModuleMapping: {},\n    rscModuleMapping: {},\n  }\n\n  for (const clientReferenceManifest of clientReferenceManifests) {\n    mergedClientReferenceManifest.clientModules = {\n      ...mergedClientReferenceManifest.clientModules,\n      ...clientReferenceManifest.clientModules,\n    }\n    mergedClientReferenceManifest.edgeRscModuleMapping = {\n      ...mergedClientReferenceManifest.edgeRscModuleMapping,\n      ...clientReferenceManifest.edgeRscModuleMapping,\n    }\n    mergedClientReferenceManifest.rscModuleMapping = {\n      ...mergedClientReferenceManifest.rscModuleMapping,\n      ...clientReferenceManifest.rscModuleMapping,\n    }\n  }\n\n  return mergedClientReferenceManifest\n}\n", "import { HeadersAdapter } from '../web/spec-extension/adapters/headers';\nimport { PRERENDER_REVALIDATE_HEADER, PRERENDER_REVALIDATE_ONLY_GENERATED_HEADER } from '../../lib/constants';\nimport { getTracer } from '../lib/trace/tracer';\nimport { NodeSpan } from '../lib/trace/constants';\nexport function wrapApiHandler(page, handler) {\n    return (...args)=>{\n        getTracer().setRootSpanAttribute('next.route', page);\n        // Call API route method\n        return getTracer().trace(NodeSpan.runHandler, {\n            spanName: `executing api route (pages) ${page}`\n        }, ()=>handler(...args));\n    };\n}\n/**\n *\n * @param res response object\n * @param statusCode `HTTP` status code of response\n */ export function sendStatusCode(res, statusCode) {\n    res.statusCode = statusCode;\n    return res;\n}\n/**\n *\n * @param res response object\n * @param [statusOrUrl] `HTTP` status code of redirect\n * @param url URL of redirect\n */ export function redirect(res, statusOrUrl, url) {\n    if (typeof statusOrUrl === 'string') {\n        url = statusOrUrl;\n        statusOrUrl = 307;\n    }\n    if (typeof statusOrUrl !== 'number' || typeof url !== 'string') {\n        throw new Error(`Invalid redirect arguments. Please use a single argument URL, e.g. res.redirect('/destination') or use a status code and URL, e.g. res.redirect(307, '/destination').`);\n    }\n    res.writeHead(statusOrUrl, {\n        Location: url\n    });\n    res.write(url);\n    res.end();\n    return res;\n}\nexport function checkIsOnDemandRevalidate(req, previewProps) {\n    const headers = HeadersAdapter.from(req.headers);\n    const previewModeId = headers.get(PRERENDER_REVALIDATE_HEADER);\n    const isOnDemandRevalidate = previewModeId === previewProps.previewModeId;\n    const revalidateOnlyGenerated = headers.has(PRERENDER_REVALIDATE_ONLY_GENERATED_HEADER);\n    return {\n        isOnDemandRevalidate,\n        revalidateOnlyGenerated\n    };\n}\nexport const COOKIE_NAME_PRERENDER_BYPASS = `__prerender_bypass`;\nexport const COOKIE_NAME_PRERENDER_DATA = `__next_preview_data`;\nexport const RESPONSE_LIMIT_DEFAULT = 4 * 1024 * 1024;\nexport const SYMBOL_PREVIEW_DATA = Symbol(COOKIE_NAME_PRERENDER_DATA);\nexport const SYMBOL_CLEARED_COOKIES = Symbol(COOKIE_NAME_PRERENDER_BYPASS);\nexport function clearPreviewData(res, options = {}) {\n    if (SYMBOL_CLEARED_COOKIES in res) {\n        return res;\n    }\n    const { serialize } = require('next/dist/compiled/cookie');\n    const previous = res.getHeader('Set-Cookie');\n    res.setHeader(`Set-Cookie`, [\n        ...typeof previous === 'string' ? [\n            previous\n        ] : Array.isArray(previous) ? previous : [],\n        serialize(COOKIE_NAME_PRERENDER_BYPASS, '', {\n            // To delete a cookie, set `expires` to a date in the past:\n            // https://tools.ietf.org/html/rfc6265#section-4.1.1\n            // `Max-Age: 0` is not valid, thus ignored, and the cookie is persisted.\n            expires: new Date(0),\n            httpOnly: true,\n            sameSite: process.env.NODE_ENV !== 'development' ? 'none' : 'lax',\n            secure: process.env.NODE_ENV !== 'development',\n            path: '/',\n            ...options.path !== undefined ? {\n                path: options.path\n            } : undefined\n        }),\n        serialize(COOKIE_NAME_PRERENDER_DATA, '', {\n            // To delete a cookie, set `expires` to a date in the past:\n            // https://tools.ietf.org/html/rfc6265#section-4.1.1\n            // `Max-Age: 0` is not valid, thus ignored, and the cookie is persisted.\n            expires: new Date(0),\n            httpOnly: true,\n            sameSite: process.env.NODE_ENV !== 'development' ? 'none' : 'lax',\n            secure: process.env.NODE_ENV !== 'development',\n            path: '/',\n            ...options.path !== undefined ? {\n                path: options.path\n            } : undefined\n        })\n    ]);\n    Object.defineProperty(res, SYMBOL_CLEARED_COOKIES, {\n        value: true,\n        enumerable: false\n    });\n    return res;\n}\n/**\n * Custom error class\n */ export class ApiError extends Error {\n    constructor(statusCode, message){\n        super(message);\n        this.statusCode = statusCode;\n    }\n}\n/**\n * Sends error in `response`\n * @param res response object\n * @param statusCode of response\n * @param message of response\n */ export function sendError(res, statusCode, message) {\n    res.statusCode = statusCode;\n    res.statusMessage = message;\n    res.end(message);\n}\n/**\n * Execute getter function only if its needed\n * @param LazyProps `req` and `params` for lazyProp\n * @param prop name of property\n * @param getter function to get data\n */ export function setLazyProp({ req }, prop, getter) {\n    const opts = {\n        configurable: true,\n        enumerable: true\n    };\n    const optsReset = {\n        ...opts,\n        writable: true\n    };\n    Object.defineProperty(req, prop, {\n        ...opts,\n        get: ()=>{\n            const value = getter();\n            // we set the property on the object to avoid recalculating it\n            Object.defineProperty(req, prop, {\n                ...optsReset,\n                value\n            });\n            return value;\n        },\n        set: (value)=>{\n            Object.defineProperty(req, prop, {\n                ...optsReset,\n                value\n            });\n        }\n    });\n}\n\n//# sourceMappingURL=index.js.map", "import { COOKIE_NAME_PRERENDER_BYPASS, checkIsOnDemandRevalidate } from '../api-utils';\nexport class DraftModeProvider {\n    constructor(previewProps, req, cookies, mutableCookies){\n        var _cookies_get;\n        // The logic for draftMode() is very similar to tryGetPreviewData()\n        // but Draft Mode does not have any data associated with it.\n        const isOnDemandRevalidate = previewProps && checkIsOnDemandRevalidate(req, previewProps).isOnDemandRevalidate;\n        const cookieValue = (_cookies_get = cookies.get(COOKIE_NAME_PRERENDER_BYPASS)) == null ? void 0 : _cookies_get.value;\n        this.isEnabled = Boolean(!isOnDemandRevalidate && cookieValue && previewProps && (cookieValue === previewProps.previewModeId || // In dev mode, the cookie can be actual hash value preview id but the preview props can still be `development-id`.\n        process.env.NODE_ENV !== 'production' && previewProps.previewModeId === 'development-id'));\n        this._previewModeId = previewProps == null ? void 0 : previewProps.previewModeId;\n        this._mutableCookies = mutableCookies;\n    }\n    enable() {\n        if (!this._previewModeId) {\n            throw new Error('Invariant: previewProps missing previewModeId this should never happen');\n        }\n        this._mutableCookies.set({\n            name: COOKIE_NAME_PRERENDER_BYPASS,\n            value: this._previewModeId,\n            httpOnly: true,\n            sameSite: process.env.NODE_ENV !== 'development' ? 'none' : 'lax',\n            secure: process.env.NODE_ENV !== 'development',\n            path: '/'\n        });\n    }\n    disable() {\n        // To delete a cookie, set `expires` to a date in the past:\n        // https://tools.ietf.org/html/rfc6265#section-4.1.1\n        // `Max-Age: 0` is not valid, thus ignored, and the cookie is persisted.\n        this._mutableCookies.set({\n            name: COOKIE_NAME_PRERENDER_BYPASS,\n            value: '',\n            httpOnly: true,\n            sameSite: process.env.NODE_ENV !== 'development' ? 'none' : 'lax',\n            secure: process.env.NODE_ENV !== 'development',\n            path: '/',\n            expires: new Date(0)\n        });\n    }\n}\n\n//# sourceMappingURL=draft-mode-provider.js.map", "import { FLIGHT_HEADERS } from '../../client/components/app-router-headers';\nimport { HeadersAdapter } from '../web/spec-extension/adapters/headers';\nimport { MutableRequestCookiesAdapter, RequestCookiesAdapter, responseCookiesToRequestCookies, wrapWithMutableAccessCheck } from '../web/spec-extension/adapters/request-cookies';\nimport { ResponseCookies, RequestCookies } from '../web/spec-extension/cookies';\nimport { DraftModeProvider } from './draft-mode-provider';\nimport { splitCookiesString } from '../web/utils';\nfunction getHeaders(headers) {\n    const cleaned = HeadersAdapter.from(headers);\n    for (const header of FLIGHT_HEADERS){\n        cleaned.delete(header.toLowerCase());\n    }\n    return HeadersAdapter.seal(cleaned);\n}\nfunction getMutableCookies(headers, onUpdateCookies) {\n    const cookies = new RequestCookies(HeadersAdapter.from(headers));\n    return MutableRequestCookiesAdapter.wrap(cookies, onUpdateCookies);\n}\n/**\n * If middleware set cookies in this request (indicated by `x-middleware-set-cookie`),\n * then merge those into the existing cookie object, so that when `cookies()` is accessed\n * it's able to read the newly set cookies.\n */ function mergeMiddlewareCookies(req, existingCookies) {\n    if ('x-middleware-set-cookie' in req.headers && typeof req.headers['x-middleware-set-cookie'] === 'string') {\n        const setCookieValue = req.headers['x-middleware-set-cookie'];\n        const responseHeaders = new Headers();\n        for (const cookie of splitCookiesString(setCookieValue)){\n            responseHeaders.append('set-cookie', cookie);\n        }\n        const responseCookies = new ResponseCookies(responseHeaders);\n        // Transfer cookies from ResponseCookies to RequestCookies\n        for (const cookie of responseCookies.getAll()){\n            existingCookies.set(cookie);\n        }\n    }\n}\nexport function createRequestStoreForRender(req, res, url, implicitTags, onUpdateCookies, previewProps, isHmrRefresh, serverComponentsHmrCache, renderResumeDataCache) {\n    return createRequestStoreImpl(// Pages start in render phase by default\n    'render', req, res, url, implicitTags, onUpdateCookies, renderResumeDataCache, previewProps, isHmrRefresh, serverComponentsHmrCache);\n}\nexport function createRequestStoreForAPI(req, url, implicitTags, onUpdateCookies, previewProps) {\n    return createRequestStoreImpl(// API routes start in action phase by default\n    'action', req, undefined, url, implicitTags, onUpdateCookies, undefined, previewProps, false, undefined);\n}\nfunction createRequestStoreImpl(phase, req, res, url, implicitTags, onUpdateCookies, renderResumeDataCache, previewProps, isHmrRefresh, serverComponentsHmrCache) {\n    function defaultOnUpdateCookies(cookies) {\n        if (res) {\n            res.setHeader('Set-Cookie', cookies);\n        }\n    }\n    const cache = {};\n    return {\n        type: 'request',\n        phase,\n        implicitTags: implicitTags ?? [],\n        // Rather than just using the whole `url` here, we pull the parts we want\n        // to ensure we don't use parts of the URL that we shouldn't. This also\n        // lets us avoid requiring an empty string for `search` in the type.\n        url: {\n            pathname: url.pathname,\n            search: url.search ?? ''\n        },\n        get headers () {\n            if (!cache.headers) {\n                // Seal the headers object that'll freeze out any methods that could\n                // mutate the underlying data.\n                cache.headers = getHeaders(req.headers);\n            }\n            return cache.headers;\n        },\n        get cookies () {\n            if (!cache.cookies) {\n                // if middleware is setting cookie(s), then include those in\n                // the initial cached cookies so they can be read in render\n                const requestCookies = new RequestCookies(HeadersAdapter.from(req.headers));\n                mergeMiddlewareCookies(req, requestCookies);\n                // Seal the cookies object that'll freeze out any methods that could\n                // mutate the underlying data.\n                cache.cookies = RequestCookiesAdapter.seal(requestCookies);\n            }\n            return cache.cookies;\n        },\n        set cookies (value){\n            cache.cookies = value;\n        },\n        get mutableCookies () {\n            if (!cache.mutableCookies) {\n                const mutableCookies = getMutableCookies(req.headers, onUpdateCookies || (res ? defaultOnUpdateCookies : undefined));\n                mergeMiddlewareCookies(req, mutableCookies);\n                cache.mutableCookies = mutableCookies;\n            }\n            return cache.mutableCookies;\n        },\n        get userspaceMutableCookies () {\n            if (!cache.userspaceMutableCookies) {\n                const userspaceMutableCookies = wrapWithMutableAccessCheck(this.mutableCookies);\n                cache.userspaceMutableCookies = userspaceMutableCookies;\n            }\n            return cache.userspaceMutableCookies;\n        },\n        get draftMode () {\n            if (!cache.draftMode) {\n                cache.draftMode = new DraftModeProvider(previewProps, req, this.cookies, this.mutableCookies);\n            }\n            return cache.draftMode;\n        },\n        renderResumeDataCache: renderResumeDataCache ?? null,\n        isHmrRefresh,\n        serverComponentsHmrCache: serverComponentsHmrCache || globalThis.__serverComponentsHmrCache\n    };\n}\nexport function synchronizeMutableCookies(store) {\n    // TODO: does this need to update headers as well?\n    store.cookies = RequestCookiesAdapter.seal(responseCookiesToRequestCookies(store.mutableCookies));\n}\n\n//# sourceMappingURL=request-store.js.map", "/** Run a callback, and execute any *new* revalidations added during its runtime. */ export async function withExecuteRevalidates(store, callback) {\n    if (!store) {\n        return callback();\n    }\n    // If we executed any revalidates during the request, then we don't want to execute them again.\n    // save the state so we can check if anything changed after we're done running callbacks.\n    const savedRevalidationState = cloneRevalidationState(store);\n    try {\n        return await callback();\n    } finally{\n        // Check if we have any new revalidates, and if so, wait until they are all resolved.\n        const newRevalidates = diffRevalidationState(savedRevalidationState, cloneRevalidationState(store));\n        await executeRevalidates(store, newRevalidates);\n    }\n}\nfunction cloneRevalidationState(store) {\n    return {\n        revalidatedTags: store.revalidatedTags ? [\n            ...store.revalidatedTags\n        ] : [],\n        pendingRevalidates: {\n            ...store.pendingRevalidates\n        },\n        pendingRevalidateWrites: store.pendingRevalidateWrites ? [\n            ...store.pendingRevalidateWrites\n        ] : []\n    };\n}\nfunction diffRevalidationState(prev, curr) {\n    const prevTags = new Set(prev.revalidatedTags);\n    const prevRevalidateWrites = new Set(prev.pendingRevalidateWrites);\n    return {\n        revalidatedTags: curr.revalidatedTags.filter((tag)=>!prevTags.has(tag)),\n        pendingRevalidates: Object.fromEntries(Object.entries(curr.pendingRevalidates).filter(([key])=>!(key in prev.pendingRevalidates))),\n        pendingRevalidateWrites: curr.pendingRevalidateWrites.filter((promise)=>!prevRevalidateWrites.has(promise))\n    };\n}\nasync function executeRevalidates(workStore, { revalidatedTags, pendingRevalidates, pendingRevalidateWrites }) {\n    var _workStore_incrementalCache;\n    return Promise.all([\n        (_workStore_incrementalCache = workStore.incrementalCache) == null ? void 0 : _workStore_incrementalCache.revalidateTag(revalidatedTags),\n        ...Object.values(pendingRevalidates),\n        ...pendingRevalidateWrites\n    ]);\n}\n\n//# sourceMappingURL=revalidation-utils.js.map", "import PromiseQueue from 'next/dist/compiled/p-queue';\nimport { InvariantError } from '../../shared/lib/invariant-error';\nimport { isThenable } from '../../shared/lib/is-thenable';\nimport { workAsyncStorage } from '../app-render/work-async-storage.external';\nimport { withExecuteRevalidates } from './revalidation-utils';\nimport { bindSnapshot } from '../app-render/async-local-storage';\nimport { workUnitAsyncStorage } from '../app-render/work-unit-async-storage.external';\nimport { afterTaskAsyncStorage } from '../app-render/after-task-async-storage.external';\nexport class AfterContext {\n    constructor({ waitUntil, onClose, onTaskError }){\n        this.workUnitStores = new Set();\n        this.waitUntil = waitUntil;\n        this.onClose = onClose;\n        this.onTaskError = onTaskError;\n        this.callbackQueue = new PromiseQueue();\n        this.callbackQueue.pause();\n    }\n    after(task) {\n        if (isThenable(task)) {\n            if (!this.waitUntil) {\n                errorWaitUntilNotAvailable();\n            }\n            this.waitUntil(task.catch((error)=>this.reportTaskError('promise', error)));\n        } else if (typeof task === 'function') {\n            // TODO(after): implement tracing\n            this.addCallback(task);\n        } else {\n            throw new Error('`after()`: Argument must be a promise or a function');\n        }\n    }\n    addCallback(callback) {\n        // if something is wrong, throw synchronously, bubbling up to the `after` callsite.\n        if (!this.waitUntil) {\n            errorWaitUntilNotAvailable();\n        }\n        const workUnitStore = workUnitAsyncStorage.getStore();\n        if (workUnitStore) {\n            this.workUnitStores.add(workUnitStore);\n        }\n        const afterTaskStore = afterTaskAsyncStorage.getStore();\n        // This is used for checking if request APIs can be called inside `after`.\n        // Note that we need to check the phase in which the *topmost* `after` was called (which should be \"action\"),\n        // not the current phase (which might be \"after\" if we're in a nested after).\n        // Otherwise, we might allow `after(() => headers())`, but not `after(() => after(() => headers()))`.\n        const rootTaskSpawnPhase = afterTaskStore ? afterTaskStore.rootTaskSpawnPhase // nested after\n         : workUnitStore == null ? void 0 : workUnitStore.phase // topmost after\n        ;\n        // this should only happen once.\n        if (!this.runCallbacksOnClosePromise) {\n            this.runCallbacksOnClosePromise = this.runCallbacksOnClose();\n            this.waitUntil(this.runCallbacksOnClosePromise);\n        }\n        // Bind the callback to the current execution context (i.e. preserve all currently available ALS-es).\n        // We do this because we want all of these to be equivalent in every regard except timing:\n        //   after(() => x())\n        //   after(x())\n        //   await x()\n        const wrappedCallback = bindSnapshot(async ()=>{\n            try {\n                await afterTaskAsyncStorage.run({\n                    rootTaskSpawnPhase\n                }, ()=>callback());\n            } catch (error) {\n                this.reportTaskError('function', error);\n            }\n        });\n        this.callbackQueue.add(wrappedCallback);\n    }\n    async runCallbacksOnClose() {\n        await new Promise((resolve)=>this.onClose(resolve));\n        return this.runCallbacks();\n    }\n    async runCallbacks() {\n        if (this.callbackQueue.size === 0) return;\n        for (const workUnitStore of this.workUnitStores){\n            workUnitStore.phase = 'after';\n        }\n        const workStore = workAsyncStorage.getStore();\n        if (!workStore) {\n            throw new InvariantError('Missing workStore in AfterContext.runCallbacks');\n        }\n        return withExecuteRevalidates(workStore, ()=>{\n            this.callbackQueue.start();\n            return this.callbackQueue.onIdle();\n        });\n    }\n    reportTaskError(taskKind, error) {\n        // TODO(after): this is fine for now, but will need better intergration with our error reporting.\n        // TODO(after): should we log this if we have a onTaskError callback?\n        console.error(taskKind === 'promise' ? `A promise passed to \\`after()\\` rejected:` : `An error occurred in a function passed to \\`after()\\`:`, error);\n        if (this.onTaskError) {\n            // this is very defensive, but we really don't want anything to blow up in an error handler\n            try {\n                this.onTaskError == null ? void 0 : this.onTaskError.call(this, error);\n            } catch (handlerError) {\n                console.error(new InvariantError('`onTaskError` threw while handling an error thrown from an `after` task', {\n                    cause: handlerError\n                }));\n            }\n        }\n    }\n}\nfunction errorWaitUntilNotAvailable() {\n    throw new Error('`after()` will not work correctly, because `waitUntil` is not available in the current environment.');\n}\n\n//# sourceMappingURL=after-context.js.map", "import { AfterContext } from '../after/after-context';\nimport { normalizeAppPath } from '../../shared/lib/router/utils/app-paths';\nexport function createWorkStore({ page, fallbackRouteParams, renderOpts, requestEndedState, isPrefetchRequest }) {\n    /**\n   * Rules of Static & Dynamic HTML:\n   *\n   *    1.) We must generate static HTML unless the caller explicitly opts\n   *        in to dynamic HTML support.\n   *\n   *    2.) If dynamic HTML support is requested, we must honor that request\n   *        or throw an error. It is the sole responsibility of the caller to\n   *        ensure they aren't e.g. requesting dynamic HTML for an AMP page.\n   *\n   *    3.) If the request is in draft mode, we must generate dynamic HTML.\n   *\n   *    4.) If the request is a server action, we must generate dynamic HTML.\n   *\n   * These rules help ensure that other existing features like request caching,\n   * coalescing, and ISR continue working as intended.\n   */ const isStaticGeneration = !renderOpts.supportsDynamicResponse && !renderOpts.isDraftMode && !renderOpts.isServerAction;\n    const store = {\n        isStaticGeneration,\n        page,\n        fallbackRouteParams,\n        route: normalizeAppPath(page),\n        incrementalCache: // we fallback to a global incremental cache for edge-runtime locally\n        // so that it can access the fs cache without mocks\n        renderOpts.incrementalCache || globalThis.__incrementalCache,\n        cacheLifeProfiles: renderOpts.cacheLifeProfiles,\n        isRevalidate: renderOpts.isRevalidate,\n        isPrerendering: renderOpts.nextExport,\n        fetchCache: renderOpts.fetchCache,\n        isOnDemandRevalidate: renderOpts.isOnDemandRevalidate,\n        isDraftMode: renderOpts.isDraftMode,\n        requestEndedState,\n        isPrefetchRequest,\n        buildId: renderOpts.buildId,\n        reactLoadableManifest: (renderOpts == null ? void 0 : renderOpts.reactLoadableManifest) || {},\n        assetPrefix: (renderOpts == null ? void 0 : renderOpts.assetPrefix) || '',\n        afterContext: createAfterContext(renderOpts)\n    };\n    // TODO: remove this when we resolve accessing the store outside the execution context\n    renderOpts.store = store;\n    return store;\n}\nfunction createAfterContext(renderOpts) {\n    const { waitUntil, onClose, onAfterTaskError } = renderOpts;\n    return new AfterContext({\n        waitUntil,\n        onClose,\n        onTaskError: onAfterTaskError\n    });\n}\n\n//# sourceMappingURL=work-store.js.map", "if (process.env.NEXT_RUNTIME === 'edge') {\n    module.exports = require('next/dist/server/route-modules/app-route/module.js');\n} else {\n    if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n        if (process.env.NODE_ENV === 'development') {\n            module.exports = require('next/dist/compiled/next-server/app-route-experimental.runtime.dev.js');\n        } else if (process.env.TURBOPACK) {\n            module.exports = require('next/dist/compiled/next-server/app-route-turbo-experimental.runtime.prod.js');\n        } else {\n            module.exports = require('next/dist/compiled/next-server/app-route-experimental.runtime.prod.js');\n        }\n    } else {\n        if (process.env.NODE_ENV === 'development') {\n            module.exports = require('next/dist/compiled/next-server/app-route.runtime.dev.js');\n        } else if (process.env.TURBOPACK) {\n            module.exports = require('next/dist/compiled/next-server/app-route-turbo.runtime.prod.js');\n        } else {\n            module.exports = require('next/dist/compiled/next-server/app-route.runtime.prod.js');\n        }\n    }\n}\n\n//# sourceMappingURL=module.compiled.js.map", "/**\n * RouteModule is the base class for all route modules. This class should be\n * extended by all route modules.\n */ export class RouteModule {\n    constructor({ userland, definition }){\n        this.userland = userland;\n        this.definition = definition;\n    }\n}\n\n//# sourceMappingURL=route-module.js.map", "/**\n * List of valid HTTP methods that can be implemented by Next.js's Custom App\n * Routes.\n */ export const HTTP_METHODS = [\n    'GET',\n    'HEAD',\n    'OPTIONS',\n    'POST',\n    'PUT',\n    'DELETE',\n    'PATCH'\n];\n/**\n * Checks to see if the passed string is an HTTP method. Note that this is case\n * sensitive.\n *\n * @param maybeMethod the string that may be an HTTP method\n * @returns true if the string is an HTTP method\n */ export function isHTTPMethod(maybeMethod) {\n    return HTTP_METHODS.includes(maybeMethod);\n}\n\n//# sourceMappingURL=http.js.map", "import { NEXT_CACHE_IMPLICIT_TAG_ID } from '../../lib/constants';\nconst getDerivedTags = (pathname)=>{\n    const derivedTags = [\n        `/layout`\n    ];\n    // we automatically add the current path segments as tags\n    // for revalidatePath handling\n    if (pathname.startsWith('/')) {\n        const pathnameParts = pathname.split('/');\n        for(let i = 1; i < pathnameParts.length + 1; i++){\n            let curPathname = pathnameParts.slice(0, i).join('/');\n            if (curPathname) {\n                // all derived tags other than the page are layout tags\n                if (!curPathname.endsWith('/page') && !curPathname.endsWith('/route')) {\n                    curPathname = `${curPathname}${!curPathname.endsWith('/') ? '/' : ''}layout`;\n                }\n                derivedTags.push(curPathname);\n            }\n        }\n    }\n    return derivedTags;\n};\nexport function getImplicitTags(page, url, fallbackRouteParams) {\n    // TODO: Cache the result\n    const newTags = [];\n    const hasFallbackRouteParams = fallbackRouteParams && fallbackRouteParams.size > 0;\n    // Add the derived tags from the page.\n    const derivedTags = getDerivedTags(page);\n    for (let tag of derivedTags){\n        tag = `${NEXT_CACHE_IMPLICIT_TAG_ID}${tag}`;\n        newTags.push(tag);\n    }\n    // Add the tags from the pathname. If the route has unknown params, we don't\n    // want to add the pathname as a tag, as it will be invalid.\n    if (url.pathname && !hasFallbackRouteParams) {\n        const tag = `${NEXT_CACHE_IMPLICIT_TAG_ID}${url.pathname}`;\n        newTags.push(tag);\n    }\n    return newTags;\n}\n\n//# sourceMappingURL=implicit-tags.js.map", "/**\n * Get pathname from absolute path.\n *\n * @param absolutePath the absolute path\n * @returns the pathname\n */ export function getPathnameFromAbsolutePath(absolutePath) {\n    // Remove prefix including app dir\n    let appDir = '/app/';\n    if (!absolutePath.includes(appDir)) {\n        appDir = '\\\\app\\\\';\n    }\n    const [, ...parts] = absolutePath.split(appDir);\n    const relativePath = appDir[0] + parts.join(appDir);\n    // remove extension\n    const pathname = relativePath.split('.').slice(0, -1).join('.');\n    return pathname;\n}\n\n//# sourceMappingURL=get-pathname-from-absolute-path.js.map", "import { HTTP_METHODS } from '../../../web/http';\nconst AUTOMATIC_ROUTE_METHODS = [\n    'HEAD',\n    'OPTIONS'\n];\nfunction handleMethodNotAllowedResponse() {\n    return new Response(null, {\n        status: 405\n    });\n}\nexport function autoImplementMethods(handlers) {\n    // Loop through all the HTTP methods to create the initial methods object.\n    // Each of the methods will be set to the 405 response handler.\n    const methods = HTTP_METHODS.reduce((acc, method)=>({\n            ...acc,\n            // If the userland module implements the method, then use it. Otherwise,\n            // use the 405 response handler.\n            [method]: handlers[method] ?? handleMethodNotAllowedResponse\n        }), {});\n    // Get all the methods that could be automatically implemented that were not\n    // implemented by the userland module.\n    const implemented = new Set(HTTP_METHODS.filter((method)=>handlers[method]));\n    const missing = AUTOMATIC_ROUTE_METHODS.filter((method)=>!implemented.has(method));\n    // Loop over the missing methods to automatically implement them if we can.\n    for (const method of missing){\n        // If the userland module doesn't implement the HEAD method, then\n        // we'll automatically implement it by calling the GET method (if it\n        // exists).\n        if (method === 'HEAD') {\n            if (handlers.GET) {\n                // Implement the HEAD method by calling the GET method.\n                methods.HEAD = handlers.GET;\n                // Mark it as implemented.\n                implemented.add('HEAD');\n            }\n            continue;\n        }\n        // If OPTIONS is not provided then implement it.\n        if (method === 'OPTIONS') {\n            // TODO: check if HEAD is implemented, if so, use it to add more headers\n            // Get all the methods that were implemented by the userland module.\n            const allow = [\n                'OPTIONS',\n                ...implemented\n            ];\n            // If the list of methods doesn't include HEAD, but it includes GET, then\n            // add HEAD as it's automatically implemented.\n            if (!implemented.has('HEAD') && implemented.has('GET')) {\n                allow.push('HEAD');\n            }\n            // Sort and join the list with commas to create the `Allow` header. See:\n            // https://httpwg.org/specs/rfc9110.html#field.allow\n            const headers = {\n                Allow: allow.sort().join(', ')\n            };\n            // Implement the OPTIONS method by returning a 204 response with the\n            // `Allow` header.\n            methods.OPTIONS = ()=>new Response(null, {\n                    status: 204,\n                    headers\n                });\n            // Mark this method as implemented.\n            implemented.add('OPTIONS');\n            continue;\n        }\n        throw new Error(`Invariant: should handle all automatic implementable methods, got method: ${method}`);\n    }\n    return methods;\n}\n\n//# sourceMappingURL=auto-implement-methods.js.map", "/**\n * Converts the query into params.\n *\n * @param query the query to convert to params\n * @returns the params\n */ export function parsedUrlQueryToParams(query) {\n    const params = {};\n    for (const [key, value] of Object.entries(query)){\n        if (typeof value === 'undefined') continue;\n        params[key] = value;\n    }\n    return params;\n}\n\n//# sourceMappingURL=parsed-url-query-to-params.js.map", "import stringHash from 'next/dist/compiled/string-hash';\nimport { formatServerError } from '../../lib/format-server-error';\nimport { SpanStatusCode, getTracer } from '../lib/trace/tracer';\nimport { isAbortError } from '../pipe-readable';\nimport { isBailoutToCSRError } from '../../shared/lib/lazy-dynamic/bailout-to-csr';\nimport { isDynamicServerError } from '../../client/components/hooks-server-context';\nimport { isNextRouterError } from '../../client/components/is-next-router-error';\nimport { getProperError } from '../../lib/is-error';\n/**\n * Returns a digest for well-known Next.js errors, otherwise `undefined`. If a\n * digest is returned this also means that the error does not need to be\n * reported.\n */ export function getDigestForWellKnownError(error) {\n    // If we're bailing out to CSR, we don't need to log the error.\n    if (isBailoutToCSRError(error)) return error.digest;\n    // If this is a navigation error, we don't need to log the error.\n    if (isNextRouterError(error)) return error.digest;\n    // If this error occurs, we know that we should be stopping the static\n    // render. This is only thrown in static generation when PPR is not enabled,\n    // which causes the whole page to be marked as dynamic. We don't need to\n    // tell the user about this error, as it's not actionable.\n    if (isDynamicServerError(error)) return error.digest;\n    return undefined;\n}\nexport function createFlightReactServerErrorHandler(shouldFormatError, onReactServerRenderError) {\n    return (thrownValue)=>{\n        if (typeof thrownValue === 'string') {\n            // TODO-APP: look at using webcrypto instead. Requires a promise to be awaited.\n            return stringHash(thrownValue).toString();\n        }\n        // If the response was closed, we don't need to log the error.\n        if (isAbortError(thrownValue)) return;\n        const digest = getDigestForWellKnownError(thrownValue);\n        if (digest) {\n            return digest;\n        }\n        const err = getProperError(thrownValue);\n        // If the error already has a digest, respect the original digest,\n        // so it won't get re-generated into another new error.\n        if (!err.digest) {\n            // TODO-APP: look at using webcrypto instead. Requires a promise to be awaited.\n            err.digest = stringHash(err.message + err.stack || '').toString();\n        }\n        // Format server errors in development to add more helpful error messages\n        if (shouldFormatError) {\n            formatServerError(err);\n        }\n        // Record exception in an active span, if available.\n        const span = getTracer().getActiveScopeSpan();\n        if (span) {\n            span.recordException(err);\n            span.setStatus({\n                code: SpanStatusCode.ERROR,\n                message: err.message\n            });\n        }\n        onReactServerRenderError(err);\n        return err.digest;\n    };\n}\nexport function createHTMLReactServerErrorHandler(shouldFormatError, isNextExport, reactServerErrors, silenceLogger, onReactServerRenderError) {\n    return (thrownValue)=>{\n        var _err_message;\n        if (typeof thrownValue === 'string') {\n            // TODO-APP: look at using webcrypto instead. Requires a promise to be awaited.\n            return stringHash(thrownValue).toString();\n        }\n        // If the response was closed, we don't need to log the error.\n        if (isAbortError(thrownValue)) return;\n        const digest = getDigestForWellKnownError(thrownValue);\n        if (digest) {\n            return digest;\n        }\n        const err = getProperError(thrownValue);\n        // If the error already has a digest, respect the original digest,\n        // so it won't get re-generated into another new error.\n        if (!err.digest) {\n            // TODO-APP: look at using webcrypto instead. Requires a promise to be awaited.\n            err.digest = stringHash(err.message + (err.stack || '')).toString();\n        }\n        // @TODO by putting this here and not at the top it is possible that\n        // we don't error the build in places we actually expect to\n        if (!reactServerErrors.has(err.digest)) {\n            reactServerErrors.set(err.digest, err);\n        }\n        // Format server errors in development to add more helpful error messages\n        if (shouldFormatError) {\n            formatServerError(err);\n        }\n        // Don't log the suppressed error during export\n        if (!(isNextExport && (err == null ? void 0 : (_err_message = err.message) == null ? void 0 : _err_message.includes('The specific message is omitted in production builds to avoid leaking sensitive details.')))) {\n            // Record exception in an active span, if available.\n            const span = getTracer().getActiveScopeSpan();\n            if (span) {\n                span.recordException(err);\n                span.setStatus({\n                    code: SpanStatusCode.ERROR,\n                    message: err.message\n                });\n            }\n            if (!silenceLogger) {\n                onReactServerRenderError == null ? void 0 : onReactServerRenderError(err);\n            }\n        }\n        return err.digest;\n    };\n}\nexport function createHTMLErrorHandler(shouldFormatError, isNextExport, reactServerErrors, allCapturedErrors, silenceLogger, onHTMLRenderSSRError) {\n    return (thrownValue, errorInfo)=>{\n        var _err_message;\n        let isSSRError = true;\n        allCapturedErrors.push(thrownValue);\n        // If the response was closed, we don't need to log the error.\n        if (isAbortError(thrownValue)) return;\n        const digest = getDigestForWellKnownError(thrownValue);\n        if (digest) {\n            return digest;\n        }\n        const err = getProperError(thrownValue);\n        // If the error already has a digest, respect the original digest,\n        // so it won't get re-generated into another new error.\n        if (err.digest) {\n            if (reactServerErrors.has(err.digest)) {\n                // This error is likely an obfuscated error from react-server.\n                // We recover the original error here.\n                thrownValue = reactServerErrors.get(err.digest);\n                isSSRError = false;\n            } else {\n            // The error is not from react-server but has a digest\n            // from other means so we don't need to produce a new one\n            }\n        } else {\n            err.digest = stringHash(err.message + ((errorInfo == null ? void 0 : errorInfo.componentStack) || err.stack || '')).toString();\n        }\n        // Format server errors in development to add more helpful error messages\n        if (shouldFormatError) {\n            formatServerError(err);\n        }\n        // Don't log the suppressed error during export\n        if (!(isNextExport && (err == null ? void 0 : (_err_message = err.message) == null ? void 0 : _err_message.includes('The specific message is omitted in production builds to avoid leaking sensitive details.')))) {\n            // Record exception in an active span, if available.\n            const span = getTracer().getActiveScopeSpan();\n            if (span) {\n                span.recordException(err);\n                span.setStatus({\n                    code: SpanStatusCode.ERROR,\n                    message: err.message\n                });\n            }\n            if (!silenceLogger && // HTML errors contain RSC errors as well, filter them out before reporting\n            isSSRError) {\n                onHTMLRenderSSRError(err, errorInfo);\n            }\n        }\n        return err.digest;\n    };\n}\nexport function isUserLandError(err) {\n    return !isAbortError(err) && !isBailoutToCSRError(err) && !isNextRouterError(err);\n}\n\n//# sourceMappingURL=create-error-handler.js.map", "import { getDigestForWellKnownError } from './create-error-handler';\nexport function printDebugThrownValueForProspectiveRender(thrownValue, route) {\n    // We don't need to print well-known Next.js errors.\n    if (getDigestForWellKnownError(thrownValue)) {\n        return;\n    }\n    let message;\n    if (typeof thrownValue === 'object' && thrownValue !== null && typeof thrownValue.message === 'string') {\n        message = thrownValue.message;\n        if (typeof thrownValue.stack === 'string') {\n            const originalErrorStack = thrownValue.stack;\n            const stackStart = originalErrorStack.indexOf('\\n');\n            if (stackStart > -1) {\n                const error = new Error(`Route ${route} errored during the prospective render. These errors are normally ignored and may not prevent the route from prerendering but are logged here because build debugging is enabled.\n          \nOriginal Error: ${message}`);\n                error.stack = 'Error: ' + error.message + originalErrorStack.slice(stackStart);\n                console.error(error);\n                return;\n            }\n        }\n    } else if (typeof thrownValue === 'string') {\n        message = thrownValue;\n    }\n    if (message) {\n        console.error(`Route ${route} errored during the prospective render. These errors are normally ignored and may not prevent the route from prerendering but are logged here because build debugging is enabled. No stack was provided.\n          \nOriginal Message: ${message}`);\n        return;\n    }\n    console.error(`Route ${route} errored during the prospective render. These errors are normally ignored and may not prevent the route from prerendering but are logged here because build debugging is enabled. The thrown value is logged just following this message`);\n    console.error(thrownValue);\n    return;\n}\n\n//# sourceMappingURL=prospective-render-utils.js.map", "// the name of the export has to be the camelCase version of the file name (without the extension)\n// TODO: remove this. We need it because using notFound from next/navigation imports this file :(\nexport * as appRouterContext from '../../../shared/lib/app-router-context.shared-runtime';\n\n//# sourceMappingURL=shared-modules.js.map", "import { ACTION_HEADER } from '../../client/components/app-router-headers';\nexport function getServerActionRequestMetadata(req) {\n    let actionId;\n    let contentType;\n    if (req.headers instanceof Headers) {\n        actionId = req.headers.get(ACTION_HEADER.toLowerCase()) ?? null;\n        contentType = req.headers.get('content-type');\n    } else {\n        actionId = req.headers[ACTION_HEADER.toLowerCase()] ?? null;\n        contentType = req.headers['content-type'] ?? null;\n    }\n    const isURLEncodedAction = Boolean(req.method === 'POST' && contentType === 'application/x-www-form-urlencoded');\n    const isMultipartAction = Boolean(req.method === 'POST' && (contentType == null ? void 0 : contentType.startsWith('multipart/form-data')));\n    const isFetchAction = Boolean(actionId !== undefined && typeof actionId === 'string' && req.method === 'POST');\n    const isServerAction = Boolean(isFetchAction || isURLEncodedAction || isMultipartAction);\n    return {\n        actionId,\n        isURLEncodedAction,\n        isMultipartAction,\n        isFetchAction,\n        isServerAction\n    };\n}\nexport function getIsServerAction(req) {\n    return getServerActionRequestMetadata(req).isServerAction;\n}\n\n//# sourceMappingURL=server-action-request-meta.js.map", "/**\n * Cleans a URL by stripping the protocol, host, and search params.\n *\n * @param urlString the url to clean\n * @returns the cleaned url\n */ export function cleanURL(url) {\n    const u = new URL(url);\n    u.host = 'localhost:3000';\n    u.search = '';\n    u.protocol = 'http';\n    return u;\n}\n\n//# sourceMappingURL=clean-url.js.map", "// route handlers are only statically optimized if they define\n// one of these top-level configs manually\n//   - dynamic = 'force-static'\n//   - dynamic = 'error'\n//   - revalidate > 0\n//   - revalidate = false\n//   - generateStaticParams\nexport function isStaticGenEnabled(mod) {\n    return mod.dynamic === 'force-static' || mod.dynamic === 'error' || mod.revalidate === false || mod.revalidate !== undefined && mod.revalidate > 0 || typeof mod.generateStaticParams == 'function';\n}\n\n//# sourceMappingURL=is-static-gen-enabled.js.map", "/**\n * This class is used to detect when all cache reads for a given render are settled.\n * We do this to allow for cache warming the prerender without having to continue rendering\n * the remainder of the page. This feature is really only useful when the dynamicIO flag is on\n * and should only be used in codepaths gated with this feature.\n */ export class CacheSignal {\n    constructor(){\n        this.count = 0;\n        this.earlyListeners = [];\n        this.listeners = [];\n        this.tickPending = false;\n        this.taskPending = false;\n    }\n    noMorePendingCaches() {\n        if (!this.tickPending) {\n            this.tickPending = true;\n            process.nextTick(()=>{\n                this.tickPending = false;\n                if (this.count === 0) {\n                    for(let i = 0; i < this.earlyListeners.length; i++){\n                        this.earlyListeners[i]();\n                    }\n                    this.earlyListeners.length = 0;\n                }\n            });\n        }\n        if (!this.taskPending) {\n            this.taskPending = true;\n            setTimeout(()=>{\n                this.taskPending = false;\n                if (this.count === 0) {\n                    for(let i = 0; i < this.listeners.length; i++){\n                        this.listeners[i]();\n                    }\n                    this.listeners.length = 0;\n                }\n            }, 0);\n        }\n    }\n    /**\n   * This promise waits until there are no more in progress cache reads but no later.\n   * This allows for adding more cache reads after to delay cacheReady.\n   */ inputReady() {\n        return new Promise((resolve)=>{\n            this.earlyListeners.push(resolve);\n            if (this.count === 0) {\n                this.noMorePendingCaches();\n            }\n        });\n    }\n    /**\n   * If there are inflight cache reads this Promise can resolve in a microtask however\n   * if there are no inflight cache reads then we wait at least one task to allow initial\n   * cache reads to be initiated.\n   */ cacheReady() {\n        return new Promise((resolve)=>{\n            this.listeners.push(resolve);\n            if (this.count === 0) {\n                this.noMorePendingCaches();\n            }\n        });\n    }\n    beginRead() {\n        this.count++;\n    }\n    endRead() {\n        // If this is the last read we need to wait a task before we can claim the cache is settled.\n        // The cache read will likely ping a Server Component which can read from the cache again and this\n        // will play out in a microtask so we need to only resolve pending listeners if we're still at 0\n        // after at least one task.\n        // We only want one task scheduled at a time so when we hit count 1 we don't decrement the counter immediately.\n        // If intervening reads happen before the scheduled task runs they will never observe count 1 preventing reentrency\n        this.count--;\n        if (this.count === 0) {\n            this.noMorePendingCaches();\n        }\n    }\n}\n\n//# sourceMappingURL=cache-signal.js.map", "import { RouteModule } from '../route-module';\nimport { createRequestStoreForAPI } from '../../async-storage/request-store';\nimport { createWorkStore } from '../../async-storage/work-store';\nimport { HTTP_METHODS, isHTTPMethod } from '../../web/http';\nimport { getImplicitTags } from '../../lib/implicit-tags';\nimport { patchFetch } from '../../lib/patch-fetch';\nimport { getTracer } from '../../lib/trace/tracer';\nimport { AppRouteRouteHandlersSpan } from '../../lib/trace/constants';\nimport { getPathnameFromAbsolutePath } from './helpers/get-pathname-from-absolute-path';\nimport * as Log from '../../../build/output/log';\nimport { autoImplementMethods } from './helpers/auto-implement-methods';\nimport { appendMutableCookies } from '../../web/spec-extension/adapters/request-cookies';\nimport { HeadersAdapter } from '../../web/spec-extension/adapters/headers';\nimport { RequestCookiesAdapter } from '../../web/spec-extension/adapters/request-cookies';\nimport { parsedUrlQueryToParams } from './helpers/parsed-url-query-to-params';\nimport { printDebugThrownValueForProspectiveRender } from '../../app-render/prospective-render-utils';\nimport * as serverHooks from '../../../client/components/hooks-server-context';\nimport { DynamicServerError } from '../../../client/components/hooks-server-context';\nimport { workAsyncStorage } from '../../app-render/work-async-storage.external';\nimport { workUnitAsyncStorage } from '../../app-render/work-unit-async-storage.external';\nimport { actionAsyncStorage } from '../../app-render/action-async-storage.external';\nimport * as sharedModules from './shared-modules';\nimport { getIsServerAction } from '../../lib/server-action-request-meta';\nimport { RequestCookies } from 'next/dist/compiled/@edge-runtime/cookies';\nimport { cleanURL } from './helpers/clean-url';\nimport { StaticGenBailoutError } from '../../../client/components/static-generation-bailout';\nimport { isStaticGenEnabled } from './helpers/is-static-gen-enabled';\nimport { abortAndThrowOnSynchronousRequestDataAccess, postponeWithTracking, createDynamicTrackingState, getFirstDynamicReason } from '../../app-render/dynamic-rendering';\nimport { ReflectAdapter } from '../../web/spec-extension/adapters/reflect';\nimport { CacheSignal } from '../../app-render/cache-signal';\nimport { scheduleImmediate } from '../../../lib/scheduler';\nimport { createServerParamsForRoute } from '../../request/params';\nimport { getRedirectStatusCodeFromError, getURLFromRedirectError } from '../../../client/components/redirect';\nimport { isRedirectError } from '../../../client/components/redirect-error';\nimport { getAccessFallbackHTTPStatus, isHTTPAccessFallbackError } from '../../../client/components/http-access-fallback/http-access-fallback';\nimport { RedirectStatusCode } from '../../../client/components/redirect-status-code';\nimport { INFINITE_CACHE } from '../../../lib/constants';\nexport class WrappedNextRouterError {\n    constructor(error, headers){\n        this.error = error;\n        this.headers = headers;\n    }\n}\n/**\n * AppRouteRouteHandler is the handler for app routes.\n */ export class AppRouteRouteModule extends RouteModule {\n    static #_ = this.sharedModules = sharedModules;\n    constructor({ userland, definition, resolvedPagePath, nextConfigOutput }){\n        super({\n            userland,\n            definition\n        }), /**\n   * A reference to the request async storage.\n   */ this.workUnitAsyncStorage = workUnitAsyncStorage, /**\n   * A reference to the static generation async storage.\n   */ this.workAsyncStorage = workAsyncStorage, /**\n   * An interface to call server hooks which interact with the underlying\n   * storage.\n   */ this.serverHooks = serverHooks, /**\n   * A reference to the mutation related async storage, such as mutations of\n   * cookies.\n   */ this.actionAsyncStorage = actionAsyncStorage;\n        this.resolvedPagePath = resolvedPagePath;\n        this.nextConfigOutput = nextConfigOutput;\n        // Automatically implement some methods if they aren't implemented by the\n        // userland module.\n        this.methods = autoImplementMethods(userland);\n        // Get the non-static methods for this route.\n        this.hasNonStaticMethods = hasNonStaticMethods(userland);\n        // Get the dynamic property from the userland module.\n        this.dynamic = this.userland.dynamic;\n        if (this.nextConfigOutput === 'export') {\n            if (this.dynamic === 'force-dynamic') {\n                throw new Error(`export const dynamic = \"force-dynamic\" on page \"${definition.pathname}\" cannot be used with \"output: export\". See more info here: https://nextjs.org/docs/advanced-features/static-html-export`);\n            } else if (!isStaticGenEnabled(this.userland) && this.userland['GET']) {\n                throw new Error(`export const dynamic = \"force-static\"/export const revalidate not configured on route \"${definition.pathname}\" with \"output: export\". See more info here: https://nextjs.org/docs/advanced-features/static-html-export`);\n            } else {\n                this.dynamic = 'error';\n            }\n        }\n        // We only warn in development after here, so return if we're not in\n        // development.\n        if (process.env.NODE_ENV === 'development') {\n            // Print error in development if the exported handlers are in lowercase, only\n            // uppercase handlers are supported.\n            const lowercased = HTTP_METHODS.map((method)=>method.toLowerCase());\n            for (const method of lowercased){\n                if (method in this.userland) {\n                    Log.error(`Detected lowercase method '${method}' in '${this.resolvedPagePath}'. Export the uppercase '${method.toUpperCase()}' method name to fix this error.`);\n                }\n            }\n            // Print error if the module exports a default handler, they must use named\n            // exports for each HTTP method.\n            if ('default' in this.userland) {\n                Log.error(`Detected default export in '${this.resolvedPagePath}'. Export a named export for each HTTP method instead.`);\n            }\n            // If there is no methods exported by this module, then return a not found\n            // response.\n            if (!HTTP_METHODS.some((method)=>method in this.userland)) {\n                Log.error(`No HTTP methods exported in '${this.resolvedPagePath}'. Export a named export for each HTTP method.`);\n            }\n        }\n    }\n    /**\n   * Resolves the handler function for the given method.\n   *\n   * @param method the requested method\n   * @returns the handler function for the given method\n   */ resolve(method) {\n        // Ensure that the requested method is a valid method (to prevent RCE's).\n        if (!isHTTPMethod(method)) return ()=>new Response(null, {\n                status: 400\n            });\n        // Return the handler.\n        return this.methods[method];\n    }\n    async do(handler, actionStore, workStore, // @TODO refactor to not take this argument but instead construct the RequestStore\n    // inside this function. Right now we get passed a RequestStore even when\n    // we're going to do a prerender. We should probably just split do up into prexecute and execute\n    requestStore, implicitTags, request, context) {\n        var _context_renderOpts_experimental, _workStore_incrementalCache;\n        const isStaticGeneration = workStore.isStaticGeneration;\n        const dynamicIOEnabled = !!((_context_renderOpts_experimental = context.renderOpts.experimental) == null ? void 0 : _context_renderOpts_experimental.dynamicIO);\n        // Patch the global fetch.\n        patchFetch({\n            workAsyncStorage: this.workAsyncStorage,\n            workUnitAsyncStorage: this.workUnitAsyncStorage\n        });\n        const handlerContext = {\n            params: context.params ? createServerParamsForRoute(parsedUrlQueryToParams(context.params), workStore) : undefined\n        };\n        let prerenderStore = null;\n        let res;\n        try {\n            if (isStaticGeneration) {\n                const userlandRevalidate = this.userland.revalidate;\n                const defaultRevalidate = // If the static generation store does not have a revalidate value\n                // set, then we should set it the revalidate value from the userland\n                // module or default to false.\n                userlandRevalidate === false || userlandRevalidate === undefined ? INFINITE_CACHE : userlandRevalidate;\n                if (dynamicIOEnabled) {\n                    /**\n           * When we are attempting to statically prerender the GET handler of a route.ts module\n           * and dynamicIO is on we follow a similar pattern to rendering.\n           *\n           * We first run the handler letting caches fill. If something synchronously dynamic occurs\n           * during this prospective render then we can infer it will happen on every render and we\n           * just bail out of prerendering.\n           *\n           * Next we run the handler again and we check if we get a result back in a microtask.\n           * Next.js expects the return value to be a Response or a Thenable that resolves to a Response.\n           * Unfortunately Response's do not allow for accessing the response body synchronously or in\n           * a microtask so we need to allow one more task to unwrap the response body. This is a slightly\n           * different semantic than what we have when we render and it means that certain tasks can still\n           * execute before a prerender completes such as a carefully timed setImmediate.\n           *\n           * Functionally though IO should still take longer than the time it takes to unwrap the response body\n           * so our heuristic of excluding any IO should be preserved.\n           */ const prospectiveController = new AbortController();\n                    let prospectiveRenderIsDynamic = false;\n                    const cacheSignal = new CacheSignal();\n                    let dynamicTracking = createDynamicTrackingState(undefined);\n                    const prospectiveRoutePrerenderStore = prerenderStore = {\n                        type: 'prerender',\n                        phase: 'action',\n                        implicitTags: implicitTags,\n                        renderSignal: prospectiveController.signal,\n                        controller: prospectiveController,\n                        cacheSignal,\n                        // During prospective render we don't use a controller\n                        // because we need to let all caches fill.\n                        dynamicTracking,\n                        revalidate: defaultRevalidate,\n                        expire: INFINITE_CACHE,\n                        stale: INFINITE_CACHE,\n                        tags: [\n                            ...implicitTags\n                        ],\n                        prerenderResumeDataCache: null\n                    };\n                    let prospectiveResult;\n                    try {\n                        prospectiveResult = this.workUnitAsyncStorage.run(prospectiveRoutePrerenderStore, handler, request, handlerContext);\n                    } catch (err) {\n                        if (prospectiveController.signal.aborted) {\n                            // the route handler called an API which is always dynamic\n                            // there is no need to try again\n                            prospectiveRenderIsDynamic = true;\n                        } else if (process.env.NEXT_DEBUG_BUILD || process.env.__NEXT_VERBOSE_LOGGING) {\n                            printDebugThrownValueForProspectiveRender(err, workStore.route);\n                        }\n                    }\n                    if (typeof prospectiveResult === 'object' && prospectiveResult !== null && typeof prospectiveResult.then === 'function') {\n                        // The handler returned a Thenable. We'll listen for rejections to determine\n                        // if the route is erroring for dynamic reasons.\n                        ;\n                        prospectiveResult.then(()=>{}, (err)=>{\n                            if (prospectiveController.signal.aborted) {\n                                // the route handler called an API which is always dynamic\n                                // there is no need to try again\n                                prospectiveRenderIsDynamic = true;\n                            } else if (process.env.NEXT_DEBUG_BUILD) {\n                                printDebugThrownValueForProspectiveRender(err, workStore.route);\n                            }\n                        });\n                    }\n                    await cacheSignal.cacheReady();\n                    if (prospectiveRenderIsDynamic) {\n                        // the route handler called an API which is always dynamic\n                        // there is no need to try again\n                        const dynamicReason = getFirstDynamicReason(dynamicTracking);\n                        if (dynamicReason) {\n                            throw new DynamicServerError(`Route ${workStore.route} couldn't be rendered statically because it used \\`${dynamicReason}\\`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`);\n                        } else {\n                            console.error('Expected Next.js to keep track of reason for opting out of static rendering but one was not found. This is a bug in Next.js');\n                            throw new DynamicServerError(`Route ${workStore.route} couldn't be rendered statically because it used a dynamic API. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`);\n                        }\n                    }\n                    // TODO start passing this controller to the route handler. We should expose\n                    // it so the handler to abort inflight requests and other operations if we abort\n                    // the prerender.\n                    const finalController = new AbortController();\n                    dynamicTracking = createDynamicTrackingState(undefined);\n                    const finalRoutePrerenderStore = prerenderStore = {\n                        type: 'prerender',\n                        phase: 'action',\n                        implicitTags: implicitTags,\n                        renderSignal: finalController.signal,\n                        controller: finalController,\n                        cacheSignal: null,\n                        dynamicTracking,\n                        revalidate: defaultRevalidate,\n                        expire: INFINITE_CACHE,\n                        stale: INFINITE_CACHE,\n                        tags: [\n                            ...implicitTags\n                        ],\n                        prerenderResumeDataCache: null\n                    };\n                    let responseHandled = false;\n                    res = await new Promise((resolve, reject)=>{\n                        scheduleImmediate(async ()=>{\n                            try {\n                                const result = await this.workUnitAsyncStorage.run(finalRoutePrerenderStore, handler, request, handlerContext);\n                                if (responseHandled) {\n                                    // we already rejected in the followup task\n                                    return;\n                                } else if (!(result instanceof Response)) {\n                                    // This is going to error but we let that happen below\n                                    resolve(result);\n                                    return;\n                                }\n                                responseHandled = true;\n                                let bodyHandled = false;\n                                result.arrayBuffer().then((body)=>{\n                                    if (!bodyHandled) {\n                                        bodyHandled = true;\n                                        resolve(new Response(body, {\n                                            headers: result.headers,\n                                            status: result.status,\n                                            statusText: result.statusText\n                                        }));\n                                    }\n                                }, reject);\n                                scheduleImmediate(()=>{\n                                    if (!bodyHandled) {\n                                        bodyHandled = true;\n                                        finalController.abort();\n                                        reject(createDynamicIOError(workStore.route));\n                                    }\n                                });\n                            } catch (err) {\n                                reject(err);\n                            }\n                        });\n                        scheduleImmediate(()=>{\n                            if (!responseHandled) {\n                                responseHandled = true;\n                                finalController.abort();\n                                reject(createDynamicIOError(workStore.route));\n                            }\n                        });\n                    });\n                    if (finalController.signal.aborted) {\n                        // We aborted from within the execution\n                        throw createDynamicIOError(workStore.route);\n                    } else {\n                        // We didn't abort during the execution. We can abort now as a matter of semantics\n                        // though at the moment nothing actually consumes this signal so it won't halt any\n                        // inflight work.\n                        finalController.abort();\n                    }\n                } else {\n                    prerenderStore = {\n                        type: 'prerender-legacy',\n                        phase: 'action',\n                        implicitTags: implicitTags,\n                        revalidate: defaultRevalidate,\n                        expire: INFINITE_CACHE,\n                        stale: INFINITE_CACHE,\n                        tags: [\n                            ...implicitTags\n                        ]\n                    };\n                    res = await workUnitAsyncStorage.run(prerenderStore, handler, request, handlerContext);\n                }\n            } else {\n                res = await workUnitAsyncStorage.run(requestStore, handler, request, handlerContext);\n            }\n        } catch (err) {\n            if (isRedirectError(err)) {\n                const url = getURLFromRedirectError(err);\n                if (!url) {\n                    throw new Error('Invariant: Unexpected redirect url format');\n                }\n                // We need to capture any headers that should be sent on\n                // the response.\n                const headers = new Headers({\n                    Location: url\n                });\n                // Let's append any cookies that were added by the\n                // cookie API.\n                // TODO leaving the gate here b/c it indicates that we we might not actually want to do this\n                // on every `do` call. During prerender there should be no mutableCookies because\n                if (requestStore.type === 'request') {\n                    appendMutableCookies(headers, requestStore.mutableCookies);\n                }\n                // Return the redirect response.\n                return new Response(null, {\n                    // If we're in an action, we want to use a 303 redirect as we don't\n                    // want the POST request to follow the redirect, as it could result in\n                    // erroneous re-submissions.\n                    status: actionStore.isAction ? RedirectStatusCode.SeeOther : getRedirectStatusCodeFromError(err),\n                    headers\n                });\n            } else if (isHTTPAccessFallbackError(err)) {\n                const httpStatus = getAccessFallbackHTTPStatus(err);\n                return new Response(null, {\n                    status: httpStatus\n                });\n            }\n            throw err;\n        }\n        // Validate that the response is a valid response object.\n        if (!(res instanceof Response)) {\n            throw new Error(`No response is returned from route handler '${this.resolvedPagePath}'. Ensure you return a \\`Response\\` or a \\`NextResponse\\` in all branches of your handler.`);\n        }\n        context.renderOpts.fetchMetrics = workStore.fetchMetrics;\n        context.renderOpts.pendingWaitUntil = Promise.all([\n            (_workStore_incrementalCache = workStore.incrementalCache) == null ? void 0 : _workStore_incrementalCache.revalidateTag(workStore.revalidatedTags || []),\n            ...Object.values(workStore.pendingRevalidates || {})\n        ]);\n        if (prerenderStore) {\n            var _prerenderStore_tags;\n            context.renderOpts.collectedTags = (_prerenderStore_tags = prerenderStore.tags) == null ? void 0 : _prerenderStore_tags.join(',');\n            context.renderOpts.collectedRevalidate = prerenderStore.revalidate;\n            context.renderOpts.collectedExpire = prerenderStore.expire;\n            context.renderOpts.collectedStale = prerenderStore.stale;\n        }\n        // It's possible cookies were set in the handler, so we need\n        // to merge the modified cookies and the returned response\n        // here.\n        const headers = new Headers(res.headers);\n        if (requestStore.type === 'request' && appendMutableCookies(headers, requestStore.mutableCookies)) {\n            return new Response(res.body, {\n                status: res.status,\n                statusText: res.statusText,\n                headers\n            });\n        }\n        return res;\n    }\n    async handle(req, context) {\n        // Get the handler function for the given method.\n        const handler = this.resolve(req.method);\n        // Get the context for the static generation.\n        const staticGenerationContext = {\n            // App Routes don't support unknown route params.\n            fallbackRouteParams: null,\n            page: this.definition.page,\n            renderOpts: context.renderOpts\n        };\n        // Add the fetchCache option to the renderOpts.\n        staticGenerationContext.renderOpts.fetchCache = this.userland.fetchCache;\n        const actionStore = {\n            isAppRoute: true,\n            isAction: getIsServerAction(req)\n        };\n        const implicitTags = getImplicitTags(this.definition.page, req.nextUrl, // App Routes don't support unknown route params.\n        null);\n        const requestStore = createRequestStoreForAPI(req, req.nextUrl, implicitTags, undefined, context.prerenderManifest.preview);\n        const workStore = createWorkStore(staticGenerationContext);\n        // Run the handler with the request AsyncLocalStorage to inject the helper\n        // support. We set this to `unknown` because the type is not known until\n        // runtime when we do a instanceof check below.\n        const response = await this.actionAsyncStorage.run(actionStore, ()=>this.workUnitAsyncStorage.run(requestStore, ()=>this.workAsyncStorage.run(workStore, async ()=>{\n                    // Check to see if we should bail out of static generation based on\n                    // having non-static methods.\n                    if (this.hasNonStaticMethods) {\n                        if (workStore.isStaticGeneration) {\n                            const err = new DynamicServerError('Route is configured with methods that cannot be statically generated.');\n                            workStore.dynamicUsageDescription = err.message;\n                            workStore.dynamicUsageStack = err.stack;\n                            throw err;\n                        }\n                    }\n                    // We assume we can pass the original request through however we may end up\n                    // proxying it in certain circumstances based on execution type and configuration\n                    let request = req;\n                    // Update the static generation store based on the dynamic property.\n                    switch(this.dynamic){\n                        case 'force-dynamic':\n                            {\n                                // Routes of generated paths should be dynamic\n                                workStore.forceDynamic = true;\n                                break;\n                            }\n                        case 'force-static':\n                            // The dynamic property is set to force-static, so we should\n                            // force the page to be static.\n                            workStore.forceStatic = true;\n                            // We also Proxy the request to replace dynamic data on the request\n                            // with empty stubs to allow for safely executing as static\n                            request = new Proxy(req, forceStaticRequestHandlers);\n                            break;\n                        case 'error':\n                            // The dynamic property is set to error, so we should throw an\n                            // error if the page is being statically generated.\n                            workStore.dynamicShouldError = true;\n                            if (workStore.isStaticGeneration) request = new Proxy(req, requireStaticRequestHandlers);\n                            break;\n                        default:\n                            // We proxy `NextRequest` to track dynamic access, and potentially bail out of static generation\n                            request = proxyNextRequest(req, workStore);\n                    }\n                    // TODO: propagate this pathname from route matcher\n                    const route = getPathnameFromAbsolutePath(this.resolvedPagePath);\n                    const tracer = getTracer();\n                    // Update the root span attribute for the route.\n                    tracer.setRootSpanAttribute('next.route', route);\n                    return tracer.trace(AppRouteRouteHandlersSpan.runHandler, {\n                        spanName: `executing api route (app) ${route}`,\n                        attributes: {\n                            'next.route': route\n                        }\n                    }, async ()=>this.do(handler, actionStore, workStore, requestStore, implicitTags, request, context));\n                })));\n        // If the handler did't return a valid response, then return the internal\n        // error response.\n        if (!(response instanceof Response)) {\n            // TODO: validate the correct handling behavior, maybe log something?\n            return new Response(null, {\n                status: 500\n            });\n        }\n        if (response.headers.has('x-middleware-rewrite')) {\n            throw new Error('NextResponse.rewrite() was used in a app route handler, this is not currently supported. Please remove the invocation to continue.');\n        }\n        if (response.headers.get('x-middleware-next') === '1') {\n            // TODO: move this error into the `NextResponse.next()` function.\n            throw new Error('NextResponse.next() was used in a app route handler, this is not supported. See here for more info: https://nextjs.org/docs/messages/next-response-next-in-app-route-handler');\n        }\n        return response;\n    }\n}\nexport default AppRouteRouteModule;\n/**\n * Gets all the method names for handlers that are not considered static.\n *\n * @param handlers the handlers from the userland module\n * @returns the method names that are not considered static or false if all\n *          methods are static\n */ export function hasNonStaticMethods(handlers) {\n    if (// Order these by how common they are to be used\n    handlers.POST || handlers.PUT || handlers.DELETE || handlers.PATCH || handlers.OPTIONS) {\n        return true;\n    }\n    return false;\n}\n// These symbols will be used to stash cached values on Proxied requests without requiring\n// additional closures or storage such as WeakMaps.\nconst nextURLSymbol = Symbol('nextUrl');\nconst requestCloneSymbol = Symbol('clone');\nconst urlCloneSymbol = Symbol('clone');\nconst searchParamsSymbol = Symbol('searchParams');\nconst hrefSymbol = Symbol('href');\nconst toStringSymbol = Symbol('toString');\nconst headersSymbol = Symbol('headers');\nconst cookiesSymbol = Symbol('cookies');\n/**\n * The general technique with these proxy handlers is to prioritize keeping them static\n * by stashing computed values on the Proxy itself. This is safe because the Proxy is\n * inaccessible to the consumer since all operations are forwarded\n */ const forceStaticRequestHandlers = {\n    get (target, prop, receiver) {\n        switch(prop){\n            case 'headers':\n                return target[headersSymbol] || (target[headersSymbol] = HeadersAdapter.seal(new Headers({})));\n            case 'cookies':\n                return target[cookiesSymbol] || (target[cookiesSymbol] = RequestCookiesAdapter.seal(new RequestCookies(new Headers({}))));\n            case 'nextUrl':\n                return target[nextURLSymbol] || (target[nextURLSymbol] = new Proxy(target.nextUrl, forceStaticNextUrlHandlers));\n            case 'url':\n                // we don't need to separately cache this we can just read the nextUrl\n                // and return the href since we know it will have been stripped of any\n                // dynamic parts. We access via the receiver to trigger the get trap\n                return receiver.nextUrl.href;\n            case 'geo':\n            case 'ip':\n                return undefined;\n            case 'clone':\n                return target[requestCloneSymbol] || (target[requestCloneSymbol] = ()=>new Proxy(// This is vaguely unsafe but it's required since NextRequest does not implement\n                    // clone. The reason we might expect this to work in this context is the Proxy will\n                    // respond with static-amenable values anyway somewhat restoring the interface.\n                    // @TODO we need to rethink NextRequest and NextURL because they are not sufficientlly\n                    // sophisticated to adequately represent themselves in all contexts. A better approach is\n                    // to probably embed the static generation logic into the class itself removing the need\n                    // for any kind of proxying\n                    target.clone(), forceStaticRequestHandlers));\n            default:\n                return ReflectAdapter.get(target, prop, receiver);\n        }\n    }\n};\nconst forceStaticNextUrlHandlers = {\n    get (target, prop, receiver) {\n        switch(prop){\n            // URL properties\n            case 'search':\n                return '';\n            case 'searchParams':\n                return target[searchParamsSymbol] || (target[searchParamsSymbol] = new URLSearchParams());\n            case 'href':\n                return target[hrefSymbol] || (target[hrefSymbol] = cleanURL(target.href).href);\n            case 'toJSON':\n            case 'toString':\n                return target[toStringSymbol] || (target[toStringSymbol] = ()=>receiver.href);\n            // NextUrl properties\n            case 'url':\n                // Currently nextURL does not expose url but our Docs indicate that it is an available property\n                // I am forcing this to undefined here to avoid accidentally exposing a dynamic value later if\n                // the underlying nextURL ends up adding this property\n                return undefined;\n            case 'clone':\n                return target[urlCloneSymbol] || (target[urlCloneSymbol] = ()=>new Proxy(target.clone(), forceStaticNextUrlHandlers));\n            default:\n                return ReflectAdapter.get(target, prop, receiver);\n        }\n    }\n};\nfunction proxyNextRequest(request, workStore) {\n    const nextUrlHandlers = {\n        get (target, prop, receiver) {\n            switch(prop){\n                case 'search':\n                case 'searchParams':\n                case 'url':\n                case 'href':\n                case 'toJSON':\n                case 'toString':\n                case 'origin':\n                    {\n                        const workUnitStore = workUnitAsyncStorage.getStore();\n                        trackDynamic(workStore, workUnitStore, `nextUrl.${prop}`);\n                        return ReflectAdapter.get(target, prop, receiver);\n                    }\n                case 'clone':\n                    return target[urlCloneSymbol] || (target[urlCloneSymbol] = ()=>new Proxy(target.clone(), nextUrlHandlers));\n                default:\n                    return ReflectAdapter.get(target, prop, receiver);\n            }\n        }\n    };\n    const nextRequestHandlers = {\n        get (target, prop) {\n            switch(prop){\n                case 'nextUrl':\n                    return target[nextURLSymbol] || (target[nextURLSymbol] = new Proxy(target.nextUrl, nextUrlHandlers));\n                case 'headers':\n                case 'cookies':\n                case 'url':\n                case 'body':\n                case 'blob':\n                case 'json':\n                case 'text':\n                case 'arrayBuffer':\n                case 'formData':\n                    {\n                        const workUnitStore = workUnitAsyncStorage.getStore();\n                        trackDynamic(workStore, workUnitStore, `request.${prop}`);\n                        // The receiver arg is intentionally the same as the target to fix an issue with\n                        // edge runtime, where attempting to access internal slots with the wrong `this` context\n                        // results in an error.\n                        return ReflectAdapter.get(target, prop, target);\n                    }\n                case 'clone':\n                    return target[requestCloneSymbol] || (target[requestCloneSymbol] = ()=>new Proxy(// This is vaguely unsafe but it's required since NextRequest does not implement\n                        // clone. The reason we might expect this to work in this context is the Proxy will\n                        // respond with static-amenable values anyway somewhat restoring the interface.\n                        // @TODO we need to rethink NextRequest and NextURL because they are not sufficientlly\n                        // sophisticated to adequately represent themselves in all contexts. A better approach is\n                        // to probably embed the static generation logic into the class itself removing the need\n                        // for any kind of proxying\n                        target.clone(), nextRequestHandlers));\n                default:\n                    // The receiver arg is intentionally the same as the target to fix an issue with\n                    // edge runtime, where attempting to access internal slots with the wrong `this` context\n                    // results in an error.\n                    return ReflectAdapter.get(target, prop, target);\n            }\n        }\n    };\n    return new Proxy(request, nextRequestHandlers);\n}\nconst requireStaticRequestHandlers = {\n    get (target, prop, receiver) {\n        switch(prop){\n            case 'nextUrl':\n                return target[nextURLSymbol] || (target[nextURLSymbol] = new Proxy(target.nextUrl, requireStaticNextUrlHandlers));\n            case 'headers':\n            case 'cookies':\n            case 'url':\n            case 'body':\n            case 'blob':\n            case 'json':\n            case 'text':\n            case 'arrayBuffer':\n            case 'formData':\n                throw new StaticGenBailoutError(`Route ${target.nextUrl.pathname} with \\`dynamic = \"error\"\\` couldn't be rendered statically because it used \\`request.${prop}\\`.`);\n            case 'clone':\n                return target[requestCloneSymbol] || (target[requestCloneSymbol] = ()=>new Proxy(// This is vaguely unsafe but it's required since NextRequest does not implement\n                    // clone. The reason we might expect this to work in this context is the Proxy will\n                    // respond with static-amenable values anyway somewhat restoring the interface.\n                    // @TODO we need to rethink NextRequest and NextURL because they are not sufficientlly\n                    // sophisticated to adequately represent themselves in all contexts. A better approach is\n                    // to probably embed the static generation logic into the class itself removing the need\n                    // for any kind of proxying\n                    target.clone(), requireStaticRequestHandlers));\n            default:\n                return ReflectAdapter.get(target, prop, receiver);\n        }\n    }\n};\nconst requireStaticNextUrlHandlers = {\n    get (target, prop, receiver) {\n        switch(prop){\n            case 'search':\n            case 'searchParams':\n            case 'url':\n            case 'href':\n            case 'toJSON':\n            case 'toString':\n            case 'origin':\n                throw new StaticGenBailoutError(`Route ${target.pathname} with \\`dynamic = \"error\"\\` couldn't be rendered statically because it used \\`nextUrl.${prop}\\`.`);\n            case 'clone':\n                return target[urlCloneSymbol] || (target[urlCloneSymbol] = ()=>new Proxy(target.clone(), requireStaticNextUrlHandlers));\n            default:\n                return ReflectAdapter.get(target, prop, receiver);\n        }\n    }\n};\nfunction createDynamicIOError(route) {\n    return new DynamicServerError(`Route ${route} couldn't be rendered statically because it used IO that was not cached. See more info here: https://nextjs.org/docs/messages/dynamic-io`);\n}\nexport function trackDynamic(store, workUnitStore, expression) {\n    if (workUnitStore) {\n        if (workUnitStore.type === 'cache') {\n            throw new Error(`Route ${store.route} used \"${expression}\" inside \"use cache\". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use \"${expression}\" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`);\n        } else if (workUnitStore.type === 'unstable-cache') {\n            throw new Error(`Route ${store.route} used \"${expression}\" inside a function cached with \"unstable_cache(...)\". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use \"${expression}\" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`);\n        }\n    }\n    if (store.dynamicShouldError) {\n        throw new StaticGenBailoutError(`Route ${store.route} with \\`dynamic = \"error\"\\` couldn't be rendered statically because it used \\`${expression}\\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`);\n    }\n    if (workUnitStore) {\n        if (workUnitStore.type === 'prerender') {\n            // dynamicIO Prerender\n            const error = new Error(`Route ${store.route} used ${expression} without first calling \\`await connection()\\`. See more info here: https://nextjs.org/docs/messages/next-prerender-sync-request`);\n            abortAndThrowOnSynchronousRequestDataAccess(store.route, expression, error, workUnitStore);\n        } else if (workUnitStore.type === 'prerender-ppr') {\n            // PPR Prerender\n            postponeWithTracking(store.route, expression, workUnitStore.dynamicTracking);\n        } else if (workUnitStore.type === 'prerender-legacy') {\n            // legacy Prerender\n            workUnitStore.revalidate = 0;\n            const err = new DynamicServerError(`Route ${store.route} couldn't be rendered statically because it used \\`${expression}\\`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`);\n            store.dynamicUsageDescription = expression;\n            store.dynamicUsageStack = err.stack;\n            throw err;\n        } else if (process.env.NODE_ENV === 'development' && workUnitStore && workUnitStore.type === 'request') {\n            workUnitStore.usedDynamic = true;\n        }\n    }\n}\n\n//# sourceMappingURL=module.js.map", "export async function getEdgeInstrumentationModule() {\n    const instrumentation = '_ENTRIES' in globalThis && _ENTRIES.middleware_instrumentation && await _ENTRIES.middleware_instrumentation;\n    return instrumentation;\n}\nlet instrumentationModulePromise = null;\nasync function registerInstrumentation() {\n    // Ensure registerInstrumentation is not called in production build\n    if (process.env.NEXT_PHASE === 'phase-production-build') return;\n    if (!instrumentationModulePromise) {\n        instrumentationModulePromise = getEdgeInstrumentationModule();\n    }\n    const instrumentation = await instrumentationModulePromise;\n    if (instrumentation == null ? void 0 : instrumentation.register) {\n        try {\n            await instrumentation.register();\n        } catch (err) {\n            err.message = `An error occurred while loading instrumentation hook: ${err.message}`;\n            throw err;\n        }\n    }\n}\nexport async function edgeInstrumentationOnRequestError(...args) {\n    const instrumentation = await getEdgeInstrumentationModule();\n    try {\n        var _instrumentation_onRequestError;\n        await (instrumentation == null ? void 0 : (_instrumentation_onRequestError = instrumentation.onRequestError) == null ? void 0 : _instrumentation_onRequestError.call(instrumentation, ...args));\n    } catch (err) {\n        // Log the soft error and continue, since the original error has already been thrown\n        console.error('Error in instrumentation.onRequestError:', err);\n    }\n}\nlet registerInstrumentationPromise = null;\nexport function ensureInstrumentationRegistered() {\n    if (!registerInstrumentationPromise) {\n        registerInstrumentationPromise = registerInstrumentation();\n    }\n    return registerInstrumentationPromise;\n}\nfunction getUnsupportedModuleErrorMessage(module) {\n    // warning: if you change these messages, you must adjust how react-dev-overlay's middleware detects modules not found\n    return `The edge runtime does not support Node.js '${module}' module.\nLearn More: https://nextjs.org/docs/messages/node-module-in-edge-runtime`;\n}\nfunction __import_unsupported(moduleName) {\n    const proxy = new Proxy(function() {}, {\n        get (_obj, prop) {\n            if (prop === 'then') {\n                return {};\n            }\n            throw new Error(getUnsupportedModuleErrorMessage(moduleName));\n        },\n        construct () {\n            throw new Error(getUnsupportedModuleErrorMessage(moduleName));\n        },\n        apply (_target, _this, args) {\n            if (typeof args[0] === 'function') {\n                return args[0](proxy);\n            }\n            throw new Error(getUnsupportedModuleErrorMessage(moduleName));\n        }\n    });\n    return new Proxy({}, {\n        get: ()=>proxy\n    });\n}\nfunction enhanceGlobals() {\n    // The condition is true when the \"process\" module is provided\n    if (process !== global.process) {\n        // prefer local process but global.process has correct \"env\"\n        process.env = global.process.env;\n        global.process = process;\n    }\n    // to allow building code that import but does not use node.js modules,\n    // webpack will expect this function to exist in global scope\n    Object.defineProperty(globalThis, '__import_unsupported', {\n        value: __import_unsupported,\n        enumerable: false,\n        configurable: false\n    });\n    // Eagerly fire instrumentation hook to make the startup faster.\n    void ensureInstrumentationRegistered();\n}\nenhanceGlobals();\n\n//# sourceMappingURL=globals.js.map", "import { PageSignatureError } from '../error';\nconst responseSymbol = Symbol('response');\nconst passThroughSymbol = Symbol('passThrough');\nconst waitUntilSymbol = Symbol('waitUntil');\nclass FetchEvent {\n    constructor(_request, waitUntil){\n        this[passThroughSymbol] = false;\n        this[waitUntilSymbol] = waitUntil ? {\n            kind: 'external',\n            function: waitUntil\n        } : {\n            kind: 'internal',\n            promises: []\n        };\n    }\n    // TODO: is this dead code? NextFetchEvent never lets this get called\n    respondWith(response) {\n        if (!this[responseSymbol]) {\n            this[responseSymbol] = Promise.resolve(response);\n        }\n    }\n    // TODO: is this dead code? passThroughSymbol is unused\n    passThroughOnException() {\n        this[passThroughSymbol] = true;\n    }\n    waitUntil(promise) {\n        if (this[waitUntilSymbol].kind === 'external') {\n            // if we received an external waitUntil, we delegate to it\n            // TODO(after): this will make us not go through `getServerError(error, 'edge-server')` in `sandbox`\n            const waitUntil = this[waitUntilSymbol].function;\n            return waitUntil(promise);\n        } else {\n            // if we didn't receive an external waitUntil, we make it work on our own\n            // (and expect the caller to do something with the promises)\n            this[waitUntilSymbol].promises.push(promise);\n        }\n    }\n}\nexport function getWaitUntilPromiseFromEvent(event) {\n    return event[waitUntilSymbol].kind === 'internal' ? Promise.all(event[waitUntilSymbol].promises).then(()=>{}) : undefined;\n}\nexport class NextFetchEvent extends FetchEvent {\n    constructor(params){\n        var _params_context;\n        super(params.request, (_params_context = params.context) == null ? void 0 : _params_context.waitUntil);\n        this.sourcePage = params.page;\n    }\n    /**\n   * @deprecated The `request` is now the first parameter and the API is now async.\n   *\n   * Read more: https://nextjs.org/docs/messages/middleware-new-signature\n   */ get request() {\n        throw new PageSignatureError({\n            page: this.sourcePage\n        });\n    }\n    /**\n   * @deprecated Using `respondWith` is no longer needed.\n   *\n   * Read more: https://nextjs.org/docs/messages/middleware-new-signature\n   */ respondWith() {\n        throw new PageSignatureError({\n            page: this.sourcePage\n        });\n    }\n}\n\n//# sourceMappingURL=fetch-event.js.map", "/**\n * Given a URL as a string and a base URL it will make the URL relative\n * if the parsed protocol and host is the same as the one in the base\n * URL. Otherwise it returns the same URL string.\n */\nexport function relativizeURL(url: string | string, base: string | URL) {\n  const baseURL = typeof base === 'string' ? new URL(base) : base\n  const relative = new URL(url, base)\n  const origin = `${baseURL.protocol}//${baseURL.host}`\n  return `${relative.protocol}//${relative.host}` === origin\n    ? relative.toString().replace(origin, '')\n    : relative.toString()\n}\n", "import { NEXT_RSC_UNION_QUERY } from '../client/components/app-router-headers';\nconst INTERNAL_QUERY_NAMES = [\n    '__nextFallback',\n    '__nextLocale',\n    '__nextInferredLocaleFromDefault',\n    '__nextDefaultLocale',\n    '__nextIsNotFound',\n    NEXT_RSC_UNION_QUERY\n];\nconst EDGE_EXTENDED_INTERNAL_QUERY_NAMES = [\n    '__nextDataReq'\n];\nexport function stripInternalQueries(query) {\n    for (const name of INTERNAL_QUERY_NAMES){\n        delete query[name];\n    }\n}\nexport function stripInternalSearchParams(url, isEdge) {\n    const isStringUrl = typeof url === 'string';\n    const instance = isStringUrl ? new URL(url) : url;\n    for (const name of INTERNAL_QUERY_NAMES){\n        instance.searchParams.delete(name);\n    }\n    if (isEdge) {\n        for (const name of EDGE_EXTENDED_INTERNAL_QUERY_NAMES){\n            instance.searchParams.delete(name);\n        }\n    }\n    return isStringUrl ? instance.toString() : instance;\n}\n\n//# sourceMappingURL=internal-utils.js.map", "/** Monitor when the consumer finishes reading the response body.\nthat's as close as we can get to `res.on('close')` using web APIs.\n*/ export function trackBodyConsumed(body, onEnd) {\n    if (typeof body === 'string') {\n        const generator = async function* generate() {\n            const encoder = new TextEncoder();\n            yield encoder.encode(body);\n            onEnd();\n        };\n        // @ts-expect-error BodyInit typings doesn't seem to include AsyncIterables even though it's supported in practice\n        return generator();\n    } else {\n        return trackStreamConsumed(body, onEnd);\n    }\n}\nexport function trackStreamConsumed(stream, onEnd) {\n    const closePassThrough = new TransformStream({\n        flush: ()=>{\n            return onEnd();\n        }\n    });\n    return stream.pipeThrough(closePassThrough);\n}\nexport class CloseController {\n    onClose(callback) {\n        if (this.isClosed) {\n            throw new Error('Cannot subscribe to a closed CloseController');\n        }\n        this.target.addEventListener('close', callback);\n        this.listeners++;\n    }\n    dispatchClose() {\n        if (this.isClosed) {\n            throw new Error('Cannot close a CloseController multiple times');\n        }\n        if (this.listeners > 0) {\n            this.target.dispatchEvent(new Event('close'));\n        }\n        this.isClosed = true;\n    }\n    constructor(){\n        this.target = new EventTarget();\n        this.listeners = 0;\n        this.isClosed = false;\n    }\n}\n\n//# sourceMappingURL=web-on-close.js.map", "/**\n * In edge runtime, these props directly accessed from environment variables.\n *   - local: env vars will be injected through edge-runtime as runtime env vars\n *   - deployment: env vars will be replaced by edge build pipeline\n */ export function getEdgePreviewProps() {\n    return {\n        previewModeId: process.env.NODE_ENV === 'production' ? process.env.__NEXT_PREVIEW_MODE_ID : 'development-id',\n        previewModeSigningKey: process.env.__NEXT_PREVIEW_MODE_SIGNING_KEY || '',\n        previewModeEncryptionKey: process.env.__NEXT_PREVIEW_MODE_ENCRYPTION_KEY || ''\n    };\n}\n\n//# sourceMappingURL=get-edge-preview-props.js.map", "import { createAsyncLocalStorage } from '../app-render/async-local-storage';\nexport function getBuiltinRequestContext() {\n    const _globalThis = globalThis;\n    const ctx = _globalThis[NEXT_REQUEST_CONTEXT_SYMBOL];\n    return ctx == null ? void 0 : ctx.get();\n}\nconst NEXT_REQUEST_CONTEXT_SYMBOL = Symbol.for('@next/request-context');\n/** \"@next/request-context\" has a different signature from AsyncLocalStorage,\n * matching [AsyncContext.Variable](https://github.com/tc39/proposal-async-context).\n * We don't need a full AsyncContext adapter here, just having `.get()` is enough\n */ export function createLocalRequestContext() {\n    const storage = createAsyncLocalStorage();\n    return {\n        get: ()=>storage.getStore(),\n        run: (value, callback)=>storage.run(value, callback)\n    };\n}\n\n//# sourceMappingURL=builtin-request-context.js.map", "import { PageSignatureError } from './error';\nimport { fromNodeOutgoingHttpHeaders, normalizeNextQueryParam } from './utils';\nimport { NextFetchEvent, getWaitUntilPromiseFromEvent } from './spec-extension/fetch-event';\nimport { NextRequest } from './spec-extension/request';\nimport { NextResponse } from './spec-extension/response';\nimport { relativizeURL } from '../../shared/lib/router/utils/relativize-url';\nimport { NextURL } from './next-url';\nimport { stripInternalSearchParams } from '../internal-utils';\nimport { normalizeRscURL } from '../../shared/lib/router/utils/app-paths';\nimport { FLIGHT_HEADERS } from '../../client/components/app-router-headers';\nimport { ensureInstrumentationRegistered } from './globals';\nimport { createRequestStoreForAPI } from '../async-storage/request-store';\nimport { workUnitAsyncStorage } from '../app-render/work-unit-async-storage.external';\nimport { createWorkStore } from '../async-storage/work-store';\nimport { workAsyncStorage } from '../app-render/work-async-storage.external';\nimport { NEXT_ROUTER_PREFETCH_HEADER } from '../../client/components/app-router-headers';\nimport { getTracer } from '../lib/trace/tracer';\nimport { MiddlewareSpan } from '../lib/trace/constants';\nimport { CloseController } from './web-on-close';\nimport { getEdgePreviewProps } from './get-edge-preview-props';\nimport { getBuiltinRequestContext } from '../after/builtin-request-context';\nexport class NextRequestHint extends NextRequest {\n    constructor(params){\n        super(params.input, params.init);\n        this.sourcePage = params.page;\n    }\n    get request() {\n        throw new PageSignatureError({\n            page: this.sourcePage\n        });\n    }\n    respondWith() {\n        throw new PageSignatureError({\n            page: this.sourcePage\n        });\n    }\n    waitUntil() {\n        throw new PageSignatureError({\n            page: this.sourcePage\n        });\n    }\n}\nconst headersGetter = {\n    keys: (headers)=>Array.from(headers.keys()),\n    get: (headers, key)=>headers.get(key) ?? undefined\n};\nlet propagator = (request, fn)=>{\n    const tracer = getTracer();\n    return tracer.withPropagatedContext(request.headers, fn, headersGetter);\n};\nlet testApisIntercepted = false;\nfunction ensureTestApisIntercepted() {\n    if (!testApisIntercepted) {\n        testApisIntercepted = true;\n        if (process.env.NEXT_PRIVATE_TEST_PROXY === 'true') {\n            const { interceptTestApis, wrapRequestHandler } = require('next/dist/experimental/testmode/server-edge');\n            interceptTestApis();\n            propagator = wrapRequestHandler(propagator);\n        }\n    }\n}\nexport async function adapter(params) {\n    var _getBuiltinRequestContext;\n    ensureTestApisIntercepted();\n    await ensureInstrumentationRegistered();\n    // TODO-APP: use explicit marker for this\n    const isEdgeRendering = typeof self.__BUILD_MANIFEST !== 'undefined';\n    params.request.url = normalizeRscURL(params.request.url);\n    const requestUrl = new NextURL(params.request.url, {\n        headers: params.request.headers,\n        nextConfig: params.request.nextConfig\n    });\n    // Iterator uses an index to keep track of the current iteration. Because of deleting and appending below we can't just use the iterator.\n    // Instead we use the keys before iteration.\n    const keys = [\n        ...requestUrl.searchParams.keys()\n    ];\n    for (const key of keys){\n        const value = requestUrl.searchParams.getAll(key);\n        normalizeNextQueryParam(key, (normalizedKey)=>{\n            requestUrl.searchParams.delete(normalizedKey);\n            for (const val of value){\n                requestUrl.searchParams.append(normalizedKey, val);\n            }\n            requestUrl.searchParams.delete(key);\n        });\n    }\n    // Ensure users only see page requests, never data requests.\n    const buildId = requestUrl.buildId;\n    requestUrl.buildId = '';\n    const isNextDataRequest = params.request.headers['x-nextjs-data'];\n    if (isNextDataRequest && requestUrl.pathname === '/index') {\n        requestUrl.pathname = '/';\n    }\n    const requestHeaders = fromNodeOutgoingHttpHeaders(params.request.headers);\n    const flightHeaders = new Map();\n    // Headers should only be stripped for middleware\n    if (!isEdgeRendering) {\n        for (const header of FLIGHT_HEADERS){\n            const key = header.toLowerCase();\n            const value = requestHeaders.get(key);\n            if (value) {\n                flightHeaders.set(key, value);\n                requestHeaders.delete(key);\n            }\n        }\n    }\n    const normalizeUrl = process.env.__NEXT_NO_MIDDLEWARE_URL_NORMALIZE ? new URL(params.request.url) : requestUrl;\n    const request = new NextRequestHint({\n        page: params.page,\n        // Strip internal query parameters off the request.\n        input: stripInternalSearchParams(normalizeUrl, true).toString(),\n        init: {\n            body: params.request.body,\n            headers: requestHeaders,\n            method: params.request.method,\n            nextConfig: params.request.nextConfig,\n            signal: params.request.signal\n        }\n    });\n    /**\n   * This allows to identify the request as a data request. The user doesn't\n   * need to know about this property neither use it. We add it for testing\n   * purposes.\n   */ if (isNextDataRequest) {\n        Object.defineProperty(request, '__isData', {\n            enumerable: false,\n            value: true\n        });\n    }\n    if (!globalThis.__incrementalCache && params.IncrementalCache) {\n        ;\n        globalThis.__incrementalCache = new params.IncrementalCache({\n            appDir: true,\n            fetchCache: true,\n            minimalMode: process.env.NODE_ENV !== 'development',\n            fetchCacheKeyPrefix: process.env.__NEXT_FETCH_CACHE_KEY_PREFIX,\n            dev: process.env.NODE_ENV === 'development',\n            requestHeaders: params.request.headers,\n            requestProtocol: 'https',\n            getPrerenderManifest: ()=>{\n                return {\n                    version: -1,\n                    routes: {},\n                    dynamicRoutes: {},\n                    notFoundRoutes: [],\n                    preview: getEdgePreviewProps()\n                };\n            }\n        });\n    }\n    // if we're in an edge runtime sandbox, we should use the waitUntil\n    // that we receive from the enclosing NextServer\n    const outerWaitUntil = params.request.waitUntil ?? ((_getBuiltinRequestContext = getBuiltinRequestContext()) == null ? void 0 : _getBuiltinRequestContext.waitUntil);\n    const event = new NextFetchEvent({\n        request,\n        page: params.page,\n        context: outerWaitUntil ? {\n            waitUntil: outerWaitUntil\n        } : undefined\n    });\n    let response;\n    let cookiesFromResponse;\n    response = await propagator(request, ()=>{\n        // we only care to make async storage available for middleware\n        const isMiddleware = params.page === '/middleware' || params.page === '/src/middleware';\n        if (isMiddleware) {\n            // if we're in an edge function, we only get a subset of `nextConfig` (no `experimental`),\n            // so we have to inject it via DefinePlugin.\n            // in `next start` this will be passed normally (see `NextNodeServer.runMiddleware`).\n            const waitUntil = event.waitUntil.bind(event);\n            const closeController = new CloseController();\n            return getTracer().trace(MiddlewareSpan.execute, {\n                spanName: `middleware ${request.method} ${request.nextUrl.pathname}`,\n                attributes: {\n                    'http.target': request.nextUrl.pathname,\n                    'http.method': request.method\n                }\n            }, async ()=>{\n                try {\n                    var _params_request_nextConfig_experimental, _params_request_nextConfig, _params_request_nextConfig_experimental1, _params_request_nextConfig1;\n                    const onUpdateCookies = (cookies)=>{\n                        cookiesFromResponse = cookies;\n                    };\n                    const previewProps = getEdgePreviewProps();\n                    const requestStore = createRequestStoreForAPI(request, request.nextUrl, undefined, onUpdateCookies, previewProps);\n                    const workStore = createWorkStore({\n                        page: '/',\n                        fallbackRouteParams: null,\n                        renderOpts: {\n                            cacheLifeProfiles: (_params_request_nextConfig = params.request.nextConfig) == null ? void 0 : (_params_request_nextConfig_experimental = _params_request_nextConfig.experimental) == null ? void 0 : _params_request_nextConfig_experimental.cacheLife,\n                            experimental: {\n                                isRoutePPREnabled: false,\n                                dynamicIO: false,\n                                authInterrupts: !!((_params_request_nextConfig1 = params.request.nextConfig) == null ? void 0 : (_params_request_nextConfig_experimental1 = _params_request_nextConfig1.experimental) == null ? void 0 : _params_request_nextConfig_experimental1.authInterrupts)\n                            },\n                            buildId: buildId ?? '',\n                            supportsDynamicResponse: true,\n                            waitUntil,\n                            onClose: closeController.onClose.bind(closeController),\n                            onAfterTaskError: undefined\n                        },\n                        requestEndedState: {\n                            ended: false\n                        },\n                        isPrefetchRequest: request.headers.has(NEXT_ROUTER_PREFETCH_HEADER)\n                    });\n                    return await workAsyncStorage.run(workStore, ()=>workUnitAsyncStorage.run(requestStore, params.handler, request, event));\n                } finally{\n                    // middleware cannot stream, so we can consider the response closed\n                    // as soon as the handler returns.\n                    // we can delay running it until a bit later --\n                    // if it's needed, we'll have a `waitUntil` lock anyway.\n                    setTimeout(()=>{\n                        closeController.dispatchClose();\n                    }, 0);\n                }\n            });\n        }\n        return params.handler(request, event);\n    });\n    // check if response is a Response object\n    if (response && !(response instanceof Response)) {\n        throw new TypeError('Expected an instance of Response to be returned');\n    }\n    if (response && cookiesFromResponse) {\n        response.headers.set('set-cookie', cookiesFromResponse);\n    }\n    /**\n   * For rewrites we must always include the locale in the final pathname\n   * so we re-create the NextURL forcing it to include it when the it is\n   * an internal rewrite. Also we make sure the outgoing rewrite URL is\n   * a data URL if the request was a data request.\n   */ const rewrite = response == null ? void 0 : response.headers.get('x-middleware-rewrite');\n    if (response && rewrite && !isEdgeRendering) {\n        const rewriteUrl = new NextURL(rewrite, {\n            forceLocale: true,\n            headers: params.request.headers,\n            nextConfig: params.request.nextConfig\n        });\n        if (!process.env.__NEXT_NO_MIDDLEWARE_URL_NORMALIZE) {\n            if (rewriteUrl.host === request.nextUrl.host) {\n                rewriteUrl.buildId = buildId || rewriteUrl.buildId;\n                response.headers.set('x-middleware-rewrite', String(rewriteUrl));\n            }\n        }\n        /**\n     * When the request is a data request we must show if there was a rewrite\n     * with an internal header so the client knows which component to load\n     * from the data request.\n     */ const relativizedRewrite = relativizeURL(String(rewriteUrl), String(requestUrl));\n        if (isNextDataRequest && // if the rewrite is external and external rewrite\n        // resolving config is enabled don't add this header\n        // so the upstream app can set it instead\n        !(process.env.__NEXT_EXTERNAL_MIDDLEWARE_REWRITE_RESOLVE && relativizedRewrite.match(/http(s)?:\\/\\//))) {\n            response.headers.set('x-nextjs-rewrite', relativizedRewrite);\n        }\n    }\n    /**\n   * For redirects we will not include the locale in case when it is the\n   * default and we must also make sure the outgoing URL is a data one if\n   * the incoming request was a data request.\n   */ const redirect = response == null ? void 0 : response.headers.get('Location');\n    if (response && redirect && !isEdgeRendering) {\n        const redirectURL = new NextURL(redirect, {\n            forceLocale: false,\n            headers: params.request.headers,\n            nextConfig: params.request.nextConfig\n        });\n        /**\n     * Responses created from redirects have immutable headers so we have\n     * to clone the response to be able to modify it.\n     */ response = new Response(response.body, response);\n        if (!process.env.__NEXT_NO_MIDDLEWARE_URL_NORMALIZE) {\n            if (redirectURL.host === request.nextUrl.host) {\n                redirectURL.buildId = buildId || redirectURL.buildId;\n                response.headers.set('Location', String(redirectURL));\n            }\n        }\n        /**\n     * When the request is a data request we can't use the location header as\n     * it may end up with CORS error. Instead we map to an internal header so\n     * the client knows the destination.\n     */ if (isNextDataRequest) {\n            response.headers.delete('Location');\n            response.headers.set('x-nextjs-redirect', relativizeURL(String(redirectURL), String(requestUrl)));\n        }\n    }\n    const finalResponse = response ? response : NextResponse.next();\n    // Flight headers are not overridable / removable so they are applied at the end.\n    const middlewareOverrideHeaders = finalResponse.headers.get('x-middleware-override-headers');\n    const overwrittenHeaders = [];\n    if (middlewareOverrideHeaders) {\n        for (const [key, value] of flightHeaders){\n            finalResponse.headers.set(`x-middleware-request-${key}`, value);\n            overwrittenHeaders.push(key);\n        }\n        if (overwrittenHeaders.length > 0) {\n            finalResponse.headers.set('x-middleware-override-headers', middlewareOverrideHeaders + ',' + overwrittenHeaders.join(','));\n        }\n    }\n    return {\n        response: finalResponse,\n        waitUntil: getWaitUntilPromiseFromEvent(event) ?? Promise.resolve(),\n        fetchMetrics: request.fetchMetrics\n    };\n}\n\n//# sourceMappingURL=adapter.js.map", "import { Cached<PERSON>out<PERSON><PERSON>ind, IncrementalCacheKind } from '../../response-cache';\nimport { LRUCache } from '../lru-cache';\nimport { CACHE_ONE_YEAR, NEXT_CACHE_SOFT_TAGS_HEADER } from '../../../lib/constants';\nlet rateLimitedUntil = 0;\nlet memoryCache;\nconst CACHE_TAGS_HEADER = 'x-vercel-cache-tags';\nconst CACHE_HEADERS_HEADER = 'x-vercel-sc-headers';\nconst CACHE_STATE_HEADER = 'x-vercel-cache-state';\nconst CACHE_REVALIDATE_HEADER = 'x-vercel-revalidate';\nconst CACHE_FETCH_URL_HEADER = 'x-vercel-cache-item-name';\nconst CACHE_CONTROL_VALUE_HEADER = 'x-vercel-cache-control';\nconst DEBUG = Boolean(process.env.NEXT_PRIVATE_DEBUG_CACHE);\nasync function fetchRetryWithTimeout(url, init, retryIndex = 0) {\n    const controller = new AbortController();\n    const timeout = setTimeout(()=>{\n        controller.abort();\n    }, 500);\n    return fetch(url, {\n        ...init || {},\n        signal: controller.signal\n    }).catch((err)=>{\n        if (retryIndex === 3) {\n            throw err;\n        } else {\n            if (DEBUG) {\n                console.log(`Fetch failed for ${url} retry ${retryIndex}`);\n            }\n            return fetchRetryWithTimeout(url, init, retryIndex + 1);\n        }\n    }).finally(()=>{\n        clearTimeout(timeout);\n    });\n}\nexport default class FetchCache {\n    hasMatchingTags(arr1, arr2) {\n        if (arr1.length !== arr2.length) return false;\n        const set1 = new Set(arr1);\n        const set2 = new Set(arr2);\n        if (set1.size !== set2.size) return false;\n        for (let tag of set1){\n            if (!set2.has(tag)) return false;\n        }\n        return true;\n    }\n    static isAvailable(ctx) {\n        return !!(ctx._requestHeaders['x-vercel-sc-host'] || process.env.SUSPENSE_CACHE_URL);\n    }\n    constructor(ctx){\n        this.headers = {};\n        this.headers['Content-Type'] = 'application/json';\n        if (CACHE_HEADERS_HEADER in ctx._requestHeaders) {\n            const newHeaders = JSON.parse(ctx._requestHeaders[CACHE_HEADERS_HEADER]);\n            for(const k in newHeaders){\n                this.headers[k] = newHeaders[k];\n            }\n            delete ctx._requestHeaders[CACHE_HEADERS_HEADER];\n        }\n        const scHost = ctx._requestHeaders['x-vercel-sc-host'] || process.env.SUSPENSE_CACHE_URL;\n        const scBasePath = ctx._requestHeaders['x-vercel-sc-basepath'] || process.env.SUSPENSE_CACHE_BASEPATH;\n        if (process.env.SUSPENSE_CACHE_AUTH_TOKEN) {\n            this.headers['Authorization'] = `Bearer ${process.env.SUSPENSE_CACHE_AUTH_TOKEN}`;\n        }\n        if (scHost) {\n            const scProto = process.env.SUSPENSE_CACHE_PROTO || 'https';\n            this.cacheEndpoint = `${scProto}://${scHost}${scBasePath || ''}`;\n            if (DEBUG) {\n                console.log('using cache endpoint', this.cacheEndpoint);\n            }\n        } else if (DEBUG) {\n            console.log('no cache endpoint available');\n        }\n        if (ctx.maxMemoryCacheSize) {\n            if (!memoryCache) {\n                if (DEBUG) {\n                    console.log('using memory store for fetch cache');\n                }\n                memoryCache = new LRUCache(ctx.maxMemoryCacheSize, function length({ value }) {\n                    var _JSON_stringify;\n                    if (!value) {\n                        return 25;\n                    } else if (value.kind === CachedRouteKind.REDIRECT) {\n                        return JSON.stringify(value.props).length;\n                    } else if (value.kind === CachedRouteKind.IMAGE) {\n                        throw new Error('invariant image should not be incremental-cache');\n                    } else if (value.kind === CachedRouteKind.FETCH) {\n                        return JSON.stringify(value.data || '').length;\n                    } else if (value.kind === CachedRouteKind.APP_ROUTE) {\n                        return value.body.length;\n                    }\n                    // rough estimate of size of cache value\n                    return value.html.length + (((_JSON_stringify = JSON.stringify(value.kind === CachedRouteKind.APP_PAGE ? value.rscData : value.pageData)) == null ? void 0 : _JSON_stringify.length) || 0);\n                });\n            }\n        } else {\n            if (DEBUG) {\n                console.log('not using memory store for fetch cache');\n            }\n        }\n    }\n    resetRequestCache() {\n        memoryCache == null ? void 0 : memoryCache.reset();\n    }\n    async revalidateTag(...args) {\n        let [tags] = args;\n        tags = typeof tags === 'string' ? [\n            tags\n        ] : tags;\n        if (DEBUG) {\n            console.log('revalidateTag', tags);\n        }\n        if (!tags.length) return;\n        if (Date.now() < rateLimitedUntil) {\n            if (DEBUG) {\n                console.log('rate limited ', rateLimitedUntil);\n            }\n            return;\n        }\n        for(let i = 0; i < Math.ceil(tags.length / 64); i++){\n            const currentTags = tags.slice(i * 64, i * 64 + 64);\n            try {\n                const res = await fetchRetryWithTimeout(`${this.cacheEndpoint}/v1/suspense-cache/revalidate?tags=${currentTags.map((tag)=>encodeURIComponent(tag)).join(',')}`, {\n                    method: 'POST',\n                    headers: this.headers,\n                    // @ts-expect-error not on public type\n                    next: {\n                        internal: true\n                    }\n                });\n                if (res.status === 429) {\n                    const retryAfter = res.headers.get('retry-after') || '60000';\n                    rateLimitedUntil = Date.now() + parseInt(retryAfter);\n                }\n                if (!res.ok) {\n                    throw new Error(`Request failed with status ${res.status}.`);\n                }\n            } catch (err) {\n                console.warn(`Failed to revalidate tag`, currentTags, err);\n            }\n        }\n    }\n    async get(...args) {\n        var _data_value;\n        const [key, ctx] = args;\n        const { tags, softTags, kind: kindHint, fetchIdx, fetchUrl } = ctx;\n        if (kindHint !== IncrementalCacheKind.FETCH) {\n            return null;\n        }\n        if (Date.now() < rateLimitedUntil) {\n            if (DEBUG) {\n                console.log('rate limited');\n            }\n            return null;\n        }\n        // memory cache is cleared at the end of each request\n        // so that revalidate events are pulled from upstream\n        // on successive requests\n        let data = memoryCache == null ? void 0 : memoryCache.get(key);\n        const hasFetchKindAndMatchingTags = (data == null ? void 0 : (_data_value = data.value) == null ? void 0 : _data_value.kind) === CachedRouteKind.FETCH && this.hasMatchingTags(tags ?? [], data.value.tags ?? []);\n        // Get data from fetch cache. Also check if new tags have been\n        // specified with the same cache key (fetch URL)\n        if (this.cacheEndpoint && (!data || !hasFetchKindAndMatchingTags)) {\n            try {\n                const start = Date.now();\n                const fetchParams = {\n                    internal: true,\n                    fetchType: 'cache-get',\n                    fetchUrl: fetchUrl,\n                    fetchIdx\n                };\n                const res = await fetch(`${this.cacheEndpoint}/v1/suspense-cache/${key}`, {\n                    method: 'GET',\n                    headers: {\n                        ...this.headers,\n                        [CACHE_FETCH_URL_HEADER]: fetchUrl,\n                        [CACHE_TAGS_HEADER]: (tags == null ? void 0 : tags.join(',')) || '',\n                        [NEXT_CACHE_SOFT_TAGS_HEADER]: (softTags == null ? void 0 : softTags.join(',')) || ''\n                    },\n                    next: fetchParams\n                });\n                if (res.status === 429) {\n                    const retryAfter = res.headers.get('retry-after') || '60000';\n                    rateLimitedUntil = Date.now() + parseInt(retryAfter);\n                }\n                if (res.status === 404) {\n                    if (DEBUG) {\n                        console.log(`no fetch cache entry for ${key}, duration: ${Date.now() - start}ms`);\n                    }\n                    return null;\n                }\n                if (!res.ok) {\n                    console.error(await res.text());\n                    throw new Error(`invalid response from cache ${res.status}`);\n                }\n                const cached = await res.json();\n                if (!cached || cached.kind !== CachedRouteKind.FETCH) {\n                    DEBUG && console.log({\n                        cached\n                    });\n                    throw new Error('invalid cache value');\n                }\n                // if new tags were specified, merge those tags to the existing tags\n                if (cached.kind === CachedRouteKind.FETCH) {\n                    cached.tags ??= [];\n                    for (const tag of tags ?? []){\n                        if (!cached.tags.includes(tag)) {\n                            cached.tags.push(tag);\n                        }\n                    }\n                }\n                const cacheState = res.headers.get(CACHE_STATE_HEADER);\n                const age = res.headers.get('age');\n                data = {\n                    value: cached,\n                    // if it's already stale set it to a time in the past\n                    // if not derive last modified from age\n                    lastModified: cacheState !== 'fresh' ? Date.now() - CACHE_ONE_YEAR : Date.now() - parseInt(age || '0', 10) * 1000\n                };\n                if (DEBUG) {\n                    console.log(`got fetch cache entry for ${key}, duration: ${Date.now() - start}ms, size: ${Object.keys(cached).length}, cache-state: ${cacheState} tags: ${tags == null ? void 0 : tags.join(',')} softTags: ${softTags == null ? void 0 : softTags.join(',')}`);\n                }\n                if (data) {\n                    memoryCache == null ? void 0 : memoryCache.set(key, data);\n                }\n            } catch (err) {\n                // unable to get data from fetch-cache\n                if (DEBUG) {\n                    console.error(`Failed to get from fetch-cache`, err);\n                }\n            }\n        }\n        return data || null;\n    }\n    async set(...args) {\n        const [key, data, ctx] = args;\n        const { fetchCache, fetchIdx, fetchUrl, tags } = ctx;\n        if (!fetchCache) return;\n        if (Date.now() < rateLimitedUntil) {\n            if (DEBUG) {\n                console.log('rate limited');\n            }\n            return;\n        }\n        memoryCache == null ? void 0 : memoryCache.set(key, {\n            value: data,\n            lastModified: Date.now()\n        });\n        if (this.cacheEndpoint) {\n            try {\n                const start = Date.now();\n                if (data !== null && 'revalidate' in data) {\n                    this.headers[CACHE_REVALIDATE_HEADER] = data.revalidate.toString();\n                }\n                if (!this.headers[CACHE_REVALIDATE_HEADER] && data !== null && 'data' in data) {\n                    this.headers[CACHE_CONTROL_VALUE_HEADER] = data.data.headers['cache-control'];\n                }\n                const body = JSON.stringify({\n                    ...data,\n                    // we send the tags in the header instead\n                    // of in the body here\n                    tags: undefined\n                });\n                if (DEBUG) {\n                    console.log('set cache', key);\n                }\n                const fetchParams = {\n                    internal: true,\n                    fetchType: 'cache-set',\n                    fetchUrl,\n                    fetchIdx\n                };\n                const res = await fetch(`${this.cacheEndpoint}/v1/suspense-cache/${key}`, {\n                    method: 'POST',\n                    headers: {\n                        ...this.headers,\n                        [CACHE_FETCH_URL_HEADER]: fetchUrl || '',\n                        [CACHE_TAGS_HEADER]: (tags == null ? void 0 : tags.join(',')) || ''\n                    },\n                    body: body,\n                    next: fetchParams\n                });\n                if (res.status === 429) {\n                    const retryAfter = res.headers.get('retry-after') || '60000';\n                    rateLimitedUntil = Date.now() + parseInt(retryAfter);\n                }\n                if (!res.ok) {\n                    DEBUG && console.log(await res.text());\n                    throw new Error(`invalid response ${res.status}`);\n                }\n                if (DEBUG) {\n                    console.log(`successfully set to fetch-cache for ${key}, duration: ${Date.now() - start}ms, size: ${body.length}`);\n                }\n            } catch (err) {\n                // unable to set to fetch-cache\n                if (DEBUG) {\n                    console.error(`Failed to update fetch cache`, err);\n                }\n            }\n        }\n        return;\n    }\n}\n\n//# sourceMappingURL=fetch-cache.js.map", "// we share tags manifest between \"use cache\" handlers and\n// previous file-system-cache\nexport const tagsManifest = {\n    items: {}\n};\nexport const isTagStale = (tags, timestamp)=>{\n    for (const tag of tags){\n        const tagEntry = tagsManifest.items[tag];\n        if (typeof (tagEntry == null ? void 0 : tagEntry.revalidatedAt) === 'number' && // TODO: use performance.now and update file-system-cache?\n        tagEntry.revalidatedAt >= timestamp) {\n            return true;\n        }\n    }\n    return false;\n};\n\n//# sourceMappingURL=tags-manifest.external.js.map", "import { CachedRouteKind, IncrementalCacheKind } from '../../response-cache';\nimport { LRUCache } from '../lru-cache';\nimport path from '../../../shared/lib/isomorphic/path';\nimport { NEXT_CACHE_TAGS_HEADER, NEXT_DATA_SUFFIX, NEXT_META_SUFFIX, RSC_PREFETCH_SUFFIX, RSC_SEGMENT_SUFFIX, RSC_SEGMENTS_DIR_SUFFIX, RSC_SUFFIX } from '../../../lib/constants';\nimport { tagsManifest } from './tags-manifest.external';\nlet memoryCache;\nexport default class FileSystemCache {\n    constructor(ctx){\n        this.fs = ctx.fs;\n        this.flushToDisk = ctx.flushToDisk;\n        this.serverDistDir = ctx.serverDistDir;\n        this.revalidatedTags = ctx.revalidatedTags;\n        this.debug = !!process.env.NEXT_PRIVATE_DEBUG_CACHE;\n        if (ctx.maxMemoryCacheSize) {\n            if (!memoryCache) {\n                if (this.debug) {\n                    console.log('using memory store for fetch cache');\n                }\n                memoryCache = new LRUCache(ctx.maxMemoryCacheSize, function length({ value }) {\n                    var _JSON_stringify;\n                    if (!value) {\n                        return 25;\n                    } else if (value.kind === CachedRouteKind.REDIRECT) {\n                        return JSON.stringify(value.props).length;\n                    } else if (value.kind === CachedRouteKind.IMAGE) {\n                        throw new Error('invariant image should not be incremental-cache');\n                    } else if (value.kind === CachedRouteKind.FETCH) {\n                        return JSON.stringify(value.data || '').length;\n                    } else if (value.kind === CachedRouteKind.APP_ROUTE) {\n                        return value.body.length;\n                    }\n                    // rough estimate of size of cache value\n                    return value.html.length + (((_JSON_stringify = JSON.stringify(value.kind === CachedRouteKind.APP_PAGE ? value.rscData : value.pageData)) == null ? void 0 : _JSON_stringify.length) || 0);\n                });\n            }\n        } else if (this.debug) {\n            console.log('not using memory store for fetch cache');\n        }\n    }\n    resetRequestCache() {}\n    async revalidateTag(...args) {\n        let [tags] = args;\n        tags = typeof tags === 'string' ? [\n            tags\n        ] : tags;\n        if (this.debug) {\n            console.log('revalidateTag', tags);\n        }\n        if (tags.length === 0) {\n            return;\n        }\n        for (const tag of tags){\n            const data = tagsManifest.items[tag] || {};\n            data.revalidatedAt = Date.now();\n            tagsManifest.items[tag] = data;\n        }\n    }\n    async get(...args) {\n        var _data_value, _data_value1, _data_value2;\n        const [key, ctx] = args;\n        const { tags, softTags, kind, isRoutePPREnabled, isFallback } = ctx;\n        let data = memoryCache == null ? void 0 : memoryCache.get(key);\n        if (this.debug) {\n            console.log('get', key, tags, kind, !!data);\n        }\n        // let's check the disk for seed data\n        if (!data && process.env.NEXT_RUNTIME !== 'edge') {\n            if (kind === IncrementalCacheKind.APP_ROUTE) {\n                try {\n                    const filePath = this.getFilePath(`${key}.body`, IncrementalCacheKind.APP_ROUTE);\n                    const fileData = await this.fs.readFile(filePath);\n                    const { mtime } = await this.fs.stat(filePath);\n                    const meta = JSON.parse(await this.fs.readFile(filePath.replace(/\\.body$/, NEXT_META_SUFFIX), 'utf8'));\n                    const cacheEntry = {\n                        lastModified: mtime.getTime(),\n                        value: {\n                            kind: CachedRouteKind.APP_ROUTE,\n                            body: fileData,\n                            headers: meta.headers,\n                            status: meta.status\n                        }\n                    };\n                    return cacheEntry;\n                } catch  {\n                    return null;\n                }\n            }\n            try {\n                const filePath = this.getFilePath(kind === IncrementalCacheKind.FETCH ? key : `${key}.html`, kind);\n                const fileData = await this.fs.readFile(filePath, 'utf8');\n                const { mtime } = await this.fs.stat(filePath);\n                if (kind === IncrementalCacheKind.FETCH) {\n                    var _data_value3;\n                    if (!this.flushToDisk) return null;\n                    const lastModified = mtime.getTime();\n                    const parsedData = JSON.parse(fileData);\n                    data = {\n                        lastModified,\n                        value: parsedData\n                    };\n                    if (((_data_value3 = data.value) == null ? void 0 : _data_value3.kind) === CachedRouteKind.FETCH) {\n                        var _data_value4;\n                        const storedTags = (_data_value4 = data.value) == null ? void 0 : _data_value4.tags;\n                        // update stored tags if a new one is being added\n                        // TODO: remove this when we can send the tags\n                        // via header on GET same as SET\n                        if (!(tags == null ? void 0 : tags.every((tag)=>storedTags == null ? void 0 : storedTags.includes(tag)))) {\n                            if (this.debug) {\n                                console.log('tags vs storedTags mismatch', tags, storedTags);\n                            }\n                            await this.set(key, data.value, {\n                                tags,\n                                isRoutePPREnabled\n                            });\n                        }\n                    }\n                } else if (kind === IncrementalCacheKind.APP_PAGE) {\n                    // We try to load the metadata file, but if it fails, we don't\n                    // error. We also don't load it if this is a fallback.\n                    let meta;\n                    try {\n                        meta = JSON.parse(await this.fs.readFile(filePath.replace(/\\.html$/, NEXT_META_SUFFIX), 'utf8'));\n                    } catch  {}\n                    let maybeSegmentData;\n                    if (meta == null ? void 0 : meta.segmentPaths) {\n                        // Collect all the segment data for this page.\n                        // TODO: To optimize file system reads, we should consider creating\n                        // separate cache entries for each segment, rather than storing them\n                        // all on the page's entry. Though the behavior is\n                        // identical regardless.\n                        const segmentData = new Map();\n                        maybeSegmentData = segmentData;\n                        const segmentsDir = key + RSC_SEGMENTS_DIR_SUFFIX;\n                        await Promise.all(meta.segmentPaths.map(async (segmentPath)=>{\n                            const segmentDataFilePath = this.getFilePath(segmentPath === '/' ? segmentsDir + '/_index' + RSC_SEGMENT_SUFFIX : segmentsDir + segmentPath + RSC_SEGMENT_SUFFIX, IncrementalCacheKind.APP_PAGE);\n                            try {\n                                segmentData.set(segmentPath, await this.fs.readFile(segmentDataFilePath));\n                            } catch  {\n                            // This shouldn't happen, but if for some reason we fail to\n                            // load a segment from the filesystem, treat it the same as if\n                            // the segment is dynamic and does not have a prefetch.\n                            }\n                        }));\n                    }\n                    let rscData;\n                    if (!isFallback) {\n                        rscData = await this.fs.readFile(this.getFilePath(`${key}${isRoutePPREnabled ? RSC_PREFETCH_SUFFIX : RSC_SUFFIX}`, IncrementalCacheKind.APP_PAGE));\n                    }\n                    data = {\n                        lastModified: mtime.getTime(),\n                        value: {\n                            kind: CachedRouteKind.APP_PAGE,\n                            html: fileData,\n                            rscData,\n                            postponed: meta == null ? void 0 : meta.postponed,\n                            headers: meta == null ? void 0 : meta.headers,\n                            status: meta == null ? void 0 : meta.status,\n                            segmentData: maybeSegmentData\n                        }\n                    };\n                } else if (kind === IncrementalCacheKind.PAGES) {\n                    let meta;\n                    let pageData = {};\n                    if (!isFallback) {\n                        pageData = JSON.parse(await this.fs.readFile(this.getFilePath(`${key}${NEXT_DATA_SUFFIX}`, IncrementalCacheKind.PAGES), 'utf8'));\n                    }\n                    data = {\n                        lastModified: mtime.getTime(),\n                        value: {\n                            kind: CachedRouteKind.PAGES,\n                            html: fileData,\n                            pageData,\n                            headers: meta == null ? void 0 : meta.headers,\n                            status: meta == null ? void 0 : meta.status\n                        }\n                    };\n                } else {\n                    throw new Error(`Invariant: Unexpected route kind ${kind} in file system cache.`);\n                }\n                if (data) {\n                    memoryCache == null ? void 0 : memoryCache.set(key, data);\n                }\n            } catch  {\n                return null;\n            }\n        }\n        if ((data == null ? void 0 : (_data_value = data.value) == null ? void 0 : _data_value.kind) === CachedRouteKind.APP_PAGE || (data == null ? void 0 : (_data_value1 = data.value) == null ? void 0 : _data_value1.kind) === CachedRouteKind.PAGES) {\n            var _data_value_headers;\n            let cacheTags;\n            const tagsHeader = (_data_value_headers = data.value.headers) == null ? void 0 : _data_value_headers[NEXT_CACHE_TAGS_HEADER];\n            if (typeof tagsHeader === 'string') {\n                cacheTags = tagsHeader.split(',');\n            }\n            if (cacheTags == null ? void 0 : cacheTags.length) {\n                const isStale = cacheTags.some((tag)=>{\n                    var _tagsManifest_items_tag;\n                    return (tagsManifest == null ? void 0 : (_tagsManifest_items_tag = tagsManifest.items[tag]) == null ? void 0 : _tagsManifest_items_tag.revalidatedAt) && (tagsManifest == null ? void 0 : tagsManifest.items[tag].revalidatedAt) >= ((data == null ? void 0 : data.lastModified) || Date.now());\n                });\n                // we trigger a blocking validation if an ISR page\n                // had a tag revalidated, if we want to be a background\n                // revalidation instead we return data.lastModified = -1\n                if (isStale) {\n                    return null;\n                }\n            }\n        } else if ((data == null ? void 0 : (_data_value2 = data.value) == null ? void 0 : _data_value2.kind) === CachedRouteKind.FETCH) {\n            const combinedTags = [\n                ...tags || [],\n                ...softTags || []\n            ];\n            const wasRevalidated = combinedTags.some((tag)=>{\n                var _tagsManifest_items_tag;\n                if (this.revalidatedTags.includes(tag)) {\n                    return true;\n                }\n                return (tagsManifest == null ? void 0 : (_tagsManifest_items_tag = tagsManifest.items[tag]) == null ? void 0 : _tagsManifest_items_tag.revalidatedAt) && (tagsManifest == null ? void 0 : tagsManifest.items[tag].revalidatedAt) >= ((data == null ? void 0 : data.lastModified) || Date.now());\n            });\n            // When revalidate tag is called we don't return\n            // stale data so it's updated right away\n            if (wasRevalidated) {\n                data = undefined;\n            }\n        }\n        return data ?? null;\n    }\n    async set(...args) {\n        const [key, data, ctx] = args;\n        const { isFallback } = ctx;\n        memoryCache == null ? void 0 : memoryCache.set(key, {\n            value: data,\n            lastModified: Date.now()\n        });\n        if (this.debug) {\n            console.log('set', key);\n        }\n        if (!this.flushToDisk || !data) return;\n        if (data.kind === CachedRouteKind.APP_ROUTE) {\n            const filePath = this.getFilePath(`${key}.body`, IncrementalCacheKind.APP_ROUTE);\n            await this.fs.mkdir(path.dirname(filePath));\n            await this.fs.writeFile(filePath, data.body);\n            const meta = {\n                headers: data.headers,\n                status: data.status,\n                postponed: undefined,\n                segmentPaths: undefined\n            };\n            await this.fs.writeFile(filePath.replace(/\\.body$/, NEXT_META_SUFFIX), JSON.stringify(meta, null, 2));\n        } else if (data.kind === CachedRouteKind.PAGES || data.kind === CachedRouteKind.APP_PAGE) {\n            const isAppPath = data.kind === CachedRouteKind.APP_PAGE;\n            const htmlPath = this.getFilePath(`${key}.html`, isAppPath ? IncrementalCacheKind.APP_PAGE : IncrementalCacheKind.PAGES);\n            await this.fs.mkdir(path.dirname(htmlPath));\n            await this.fs.writeFile(htmlPath, data.html);\n            // Fallbacks don't generate a data file.\n            if (!isFallback) {\n                await this.fs.writeFile(this.getFilePath(`${key}${isAppPath ? ctx.isRoutePPREnabled ? RSC_PREFETCH_SUFFIX : RSC_SUFFIX : NEXT_DATA_SUFFIX}`, isAppPath ? IncrementalCacheKind.APP_PAGE : IncrementalCacheKind.PAGES), isAppPath ? data.rscData : JSON.stringify(data.pageData));\n            }\n            if ((data == null ? void 0 : data.kind) === CachedRouteKind.APP_PAGE) {\n                const meta = {\n                    headers: data.headers,\n                    status: data.status,\n                    postponed: data.postponed,\n                    segmentPaths: undefined\n                };\n                await this.fs.writeFile(htmlPath.replace(/\\.html$/, NEXT_META_SUFFIX), JSON.stringify(meta));\n            }\n        } else if (data.kind === CachedRouteKind.FETCH) {\n            const filePath = this.getFilePath(key, IncrementalCacheKind.FETCH);\n            await this.fs.mkdir(path.dirname(filePath));\n            await this.fs.writeFile(filePath, JSON.stringify({\n                ...data,\n                tags: ctx.tags\n            }));\n        }\n    }\n    getFilePath(pathname, kind) {\n        switch(kind){\n            case IncrementalCacheKind.FETCH:\n                // we store in .next/cache/fetch-cache so it can be persisted\n                // across deploys\n                return path.join(this.serverDistDir, '..', 'cache', 'fetch-cache', pathname);\n            case IncrementalCacheKind.PAGES:\n                return path.join(this.serverDistDir, 'pages', pathname);\n            case IncrementalCacheKind.IMAGE:\n            case IncrementalCacheKind.APP_PAGE:\n            case IncrementalCacheKind.APP_ROUTE:\n                return path.join(this.serverDistDir, 'app', pathname);\n            default:\n                throw new Error(`Unexpected file path kind: ${kind}`);\n        }\n    }\n}\n\n//# sourceMappingURL=file-system-cache.js.map", "import {\n  extractInterceptionRouteInformation,\n  isInterceptionRouteAppPath,\n} from '../../../../server/lib/interception-routes'\n\n// Identify /[param]/ in route string\nconst TEST_ROUTE = /\\/\\[[^/]+?\\](?=\\/|$)/\n\nexport function isDynamicRoute(route: string): boolean {\n  if (isInterceptionRouteAppPath(route)) {\n    route = extractInterceptionRouteInformation(route).interceptedRoute\n  }\n\n  return TEST_ROUTE.test(route)\n}\n", "import { ensureLeadingSlash } from './ensure-leading-slash'\nimport { isDynamicRoute } from '../router/utils'\nimport { NormalizeError } from '../utils'\n\n/**\n * Takes a page and transforms it into its file counterpart ensuring that the\n * output is normalized. Note this function is not idempotent because a page\n * `/index` can be referencing `/index/index.js` and `/index/index` could be\n * referencing `/index/index/index.js`. Examples:\n *  - `/` -> `/index`\n *  - `/index/foo` -> `/index/index/foo`\n *  - `/index` -> `/index/index`\n */\nexport function normalizePagePath(page: string): string {\n  const normalized =\n    /^\\/index(\\/|$)/.test(page) && !isDynamicRoute(page)\n      ? `/index${page}`\n      : page === '/'\n        ? '/index'\n        : ensureLeadingSlash(page)\n\n  if (process.env.NEXT_RUNTIME !== 'edge') {\n    const { posix } = require('path')\n    const resolvedPage = posix.normalize(normalized)\n    if (resolvedPage !== normalized) {\n      throw new NormalizeError(\n        `Requested and resolved page mismatch: ${normalized} ${resolvedPage}`\n      )\n    }\n  }\n\n  return normalized\n}\n", "/**\n * This transforms a URL pathname into a route. It removes any trailing slashes\n * and the `/index` suffix.\n *\n * @param {string} pathname - The URL path that needs to be optimized.\n * @returns {string} - The route\n *\n * @example\n * // returns '/example'\n * toRoute('/example/index/');\n *\n * @example\n * // returns '/example'\n * toRoute('/example/');\n *\n * @example\n * // returns '/'\n * toRoute('/index/');\n *\n * @example\n * // returns '/'\n * toRoute('/');\n */ export function toRoute(pathname) {\n    return pathname.replace(/(?:\\/index)?\\/?$/, '') || '/';\n}\n\n//# sourceMappingURL=to-route.js.map", "/**\n * A shared cache of revalidate timings for routes. This cache is used so we\n * don't have to modify the prerender manifest when we want to update the\n * revalidate timings for a route.\n */ export class SharedRevalidateTimings {\n    static #_ = /**\n   * The in-memory cache of revalidate timings for routes. This cache is\n   * populated when the cache is updated with new timings.\n   */ this.timings = new Map();\n    constructor(/**\n     * The prerender manifest that contains the initial revalidate timings for\n     * routes.\n     */ prerenderManifest){\n        this.prerenderManifest = prerenderManifest;\n    }\n    /**\n   * Try to get the revalidate timings for a route. This will first try to get\n   * the timings from the in-memory cache. If the timings are not present in the\n   * in-memory cache, then the timings will be sourced from the prerender\n   * manifest.\n   *\n   * @param route the route to get the revalidate timings for\n   * @returns the revalidate timings for the route, or undefined if the timings\n   *          are not present in the in-memory cache or the prerender manifest\n   */ get(route) {\n        var _this_prerenderManifest_routes_route, _this_prerenderManifest_dynamicRoutes_route;\n        // This is a copy on write cache that is updated when the cache is updated.\n        // If the cache is never written to, then the timings will be sourced from\n        // the prerender manifest.\n        let revalidate = SharedRevalidateTimings.timings.get(route);\n        if (typeof revalidate !== 'undefined') return revalidate;\n        revalidate = (_this_prerenderManifest_routes_route = this.prerenderManifest.routes[route]) == null ? void 0 : _this_prerenderManifest_routes_route.initialRevalidateSeconds;\n        if (typeof revalidate !== 'undefined') return revalidate;\n        revalidate = (_this_prerenderManifest_dynamicRoutes_route = this.prerenderManifest.dynamicRoutes[route]) == null ? void 0 : _this_prerenderManifest_dynamicRoutes_route.fallbackRevalidate;\n        if (typeof revalidate !== 'undefined') return revalidate;\n        return undefined;\n    }\n    /**\n   * Set the revalidate timings for a route.\n   *\n   * @param route the route to set the revalidate timings for\n   * @param revalidate the revalidate timings for the route\n   */ set(route, revalidate) {\n        SharedRevalidateTimings.timings.set(route, revalidate);\n    }\n    /**\n   * Clear the in-memory cache of revalidate timings for routes.\n   */ clear() {\n        SharedRevalidateTimings.timings.clear();\n    }\n}\n\n//# sourceMappingURL=shared-revalidate-timings.js.map", "import { IncrementalCacheKind, CachedRouteKind } from '../../response-cache';\nimport FetchCache from './fetch-cache';\nimport FileSystemCache from './file-system-cache';\nimport { normalizePagePath } from '../../../shared/lib/page-path/normalize-page-path';\nimport { CACHE_ONE_YEAR, NEXT_CACHE_REVALIDATED_TAGS_HEADER, NEXT_CACHE_REVALIDATE_TAG_TOKEN_HEADER, PRERENDER_REVALIDATE_HEADER } from '../../../lib/constants';\nimport { toRoute } from '../to-route';\nimport { SharedRevalidateTimings } from './shared-revalidate-timings';\nimport { workUnitAsyncStorageInstance } from '../../app-render/work-unit-async-storage-instance';\nimport { getPrerenderResumeDataCache, getRenderResumeDataCache } from '../../app-render/work-unit-async-storage.external';\nexport class CacheHandler {\n    // eslint-disable-next-line\n    constructor(_ctx){}\n    async get(..._args) {\n        return {};\n    }\n    async set(..._args) {}\n    async revalidateTag(..._args) {}\n    resetRequestCache() {}\n}\nexport class IncrementalCache {\n    constructor({ fs, dev, dynamicIO, flushToDisk, fetchCache, minimalMode, serverDistDir, requestHeaders, requestProtocol, maxMemoryCacheSize, getPrerenderManifest, fetchCacheKeyPrefix, CurCacheHandler, allowedRevalidateHeaderKeys }){\n        var _this_prerenderManifest_preview, _this_prerenderManifest, _this_prerenderManifest_preview1, _this_prerenderManifest1;\n        this.locks = new Map();\n        const debug = !!process.env.NEXT_PRIVATE_DEBUG_CACHE;\n        this.hasCustomCacheHandler = Boolean(CurCacheHandler);\n        const cacheHandlersSymbol = Symbol.for('@next/cache-handlers');\n        const _globalThis = globalThis;\n        if (!CurCacheHandler) {\n            // if we have a global cache handler available leverage it\n            const globalCacheHandler = _globalThis[cacheHandlersSymbol];\n            if (globalCacheHandler == null ? void 0 : globalCacheHandler.FetchCache) {\n                CurCacheHandler = globalCacheHandler.FetchCache;\n            } else {\n                if (fs && serverDistDir) {\n                    if (debug) {\n                        console.log('using filesystem cache handler');\n                    }\n                    CurCacheHandler = FileSystemCache;\n                }\n                if (FetchCache.isAvailable({\n                    _requestHeaders: requestHeaders\n                }) && minimalMode && fetchCache) {\n                    if (debug) {\n                        console.log('using fetch cache handler');\n                    }\n                    CurCacheHandler = FetchCache;\n                }\n            }\n        } else if (debug) {\n            console.log('using custom cache handler', CurCacheHandler.name);\n        }\n        if (process.env.__NEXT_TEST_MAX_ISR_CACHE) {\n            // Allow cache size to be overridden for testing purposes\n            maxMemoryCacheSize = parseInt(process.env.__NEXT_TEST_MAX_ISR_CACHE, 10);\n        }\n        this.dev = dev;\n        this.hasDynamicIO = dynamicIO;\n        this.disableForTestmode = process.env.NEXT_PRIVATE_TEST_PROXY === 'true';\n        // this is a hack to avoid Webpack knowing this is equal to this.minimalMode\n        // because we replace this.minimalMode to true in production bundles.\n        const minimalModeKey = 'minimalMode';\n        this[minimalModeKey] = minimalMode;\n        this.requestHeaders = requestHeaders;\n        this.requestProtocol = requestProtocol;\n        this.allowedRevalidateHeaderKeys = allowedRevalidateHeaderKeys;\n        this.prerenderManifest = getPrerenderManifest();\n        this.revalidateTimings = new SharedRevalidateTimings(this.prerenderManifest);\n        this.fetchCacheKeyPrefix = fetchCacheKeyPrefix;\n        let revalidatedTags = [];\n        if (requestHeaders[PRERENDER_REVALIDATE_HEADER] === ((_this_prerenderManifest = this.prerenderManifest) == null ? void 0 : (_this_prerenderManifest_preview = _this_prerenderManifest.preview) == null ? void 0 : _this_prerenderManifest_preview.previewModeId)) {\n            this.isOnDemandRevalidate = true;\n        }\n        if (minimalMode && typeof requestHeaders[NEXT_CACHE_REVALIDATED_TAGS_HEADER] === 'string' && requestHeaders[NEXT_CACHE_REVALIDATE_TAG_TOKEN_HEADER] === ((_this_prerenderManifest1 = this.prerenderManifest) == null ? void 0 : (_this_prerenderManifest_preview1 = _this_prerenderManifest1.preview) == null ? void 0 : _this_prerenderManifest_preview1.previewModeId)) {\n            revalidatedTags = requestHeaders[NEXT_CACHE_REVALIDATED_TAGS_HEADER].split(',');\n        }\n        if (CurCacheHandler) {\n            this.cacheHandler = new CurCacheHandler({\n                dev,\n                fs,\n                flushToDisk,\n                serverDistDir,\n                revalidatedTags,\n                maxMemoryCacheSize,\n                _requestHeaders: requestHeaders,\n                fetchCacheKeyPrefix\n            });\n        }\n    }\n    calculateRevalidate(pathname, fromTime, dev, isFallback) {\n        // in development we don't have a prerender-manifest\n        // and default to always revalidating to allow easier debugging\n        if (dev) return Math.floor(performance.timeOrigin + performance.now() - 1000);\n        // if an entry isn't present in routes we fallback to a default\n        // of revalidating after 1 second unless it's a fallback request.\n        const initialRevalidateSeconds = this.revalidateTimings.get(toRoute(pathname)) ?? (isFallback ? false : 1);\n        const revalidateAfter = typeof initialRevalidateSeconds === 'number' ? initialRevalidateSeconds * 1000 + fromTime : initialRevalidateSeconds;\n        return revalidateAfter;\n    }\n    _getPathname(pathname, fetchCache) {\n        return fetchCache ? pathname : normalizePagePath(pathname);\n    }\n    resetRequestCache() {\n        var _this_cacheHandler_resetRequestCache, _this_cacheHandler;\n        (_this_cacheHandler = this.cacheHandler) == null ? void 0 : (_this_cacheHandler_resetRequestCache = _this_cacheHandler.resetRequestCache) == null ? void 0 : _this_cacheHandler_resetRequestCache.call(_this_cacheHandler);\n    }\n    async lock(cacheKey) {\n        let unlockNext = ()=>Promise.resolve();\n        const existingLock = this.locks.get(cacheKey);\n        if (existingLock) {\n            await existingLock;\n        }\n        const newLock = new Promise((resolve)=>{\n            unlockNext = async ()=>{\n                resolve();\n                this.locks.delete(cacheKey) // Remove the lock upon release\n                ;\n            };\n        });\n        this.locks.set(cacheKey, newLock);\n        return unlockNext;\n    }\n    async revalidateTag(tags) {\n        var _this_cacheHandler_revalidateTag, _this_cacheHandler;\n        return (_this_cacheHandler = this.cacheHandler) == null ? void 0 : (_this_cacheHandler_revalidateTag = _this_cacheHandler.revalidateTag) == null ? void 0 : _this_cacheHandler_revalidateTag.call(_this_cacheHandler, tags);\n    }\n    // x-ref: https://github.com/facebook/react/blob/2655c9354d8e1c54ba888444220f63e836925caa/packages/react/src/ReactFetch.js#L23\n    async generateCacheKey(url, init = {}) {\n        // this should be bumped anytime a fix is made to cache entries\n        // that should bust the cache\n        const MAIN_KEY_PREFIX = 'v3';\n        const bodyChunks = [];\n        const encoder = new TextEncoder();\n        const decoder = new TextDecoder();\n        if (init.body) {\n            // handle ReadableStream body\n            if (typeof init.body.getReader === 'function') {\n                const readableBody = init.body;\n                const chunks = [];\n                try {\n                    await readableBody.pipeTo(new WritableStream({\n                        write (chunk) {\n                            if (typeof chunk === 'string') {\n                                chunks.push(encoder.encode(chunk));\n                                bodyChunks.push(chunk);\n                            } else {\n                                chunks.push(chunk);\n                                bodyChunks.push(decoder.decode(chunk, {\n                                    stream: true\n                                }));\n                            }\n                        }\n                    }));\n                    // Flush the decoder.\n                    bodyChunks.push(decoder.decode());\n                    // Create a new buffer with all the chunks.\n                    const length = chunks.reduce((total, arr)=>total + arr.length, 0);\n                    const arrayBuffer = new Uint8Array(length);\n                    // Push each of the chunks into the new array buffer.\n                    let offset = 0;\n                    for (const chunk of chunks){\n                        arrayBuffer.set(chunk, offset);\n                        offset += chunk.length;\n                    }\n                    ;\n                    init._ogBody = arrayBuffer;\n                } catch (err) {\n                    console.error('Problem reading body', err);\n                }\n            } else if (typeof init.body.keys === 'function') {\n                const formData = init.body;\n                init._ogBody = init.body;\n                for (const key of new Set([\n                    ...formData.keys()\n                ])){\n                    const values = formData.getAll(key);\n                    bodyChunks.push(`${key}=${(await Promise.all(values.map(async (val)=>{\n                        if (typeof val === 'string') {\n                            return val;\n                        } else {\n                            return await val.text();\n                        }\n                    }))).join(',')}`);\n                }\n            // handle blob body\n            } else if (typeof init.body.arrayBuffer === 'function') {\n                const blob = init.body;\n                const arrayBuffer = await blob.arrayBuffer();\n                bodyChunks.push(await blob.text());\n                init._ogBody = new Blob([\n                    arrayBuffer\n                ], {\n                    type: blob.type\n                });\n            } else if (typeof init.body === 'string') {\n                bodyChunks.push(init.body);\n                init._ogBody = init.body;\n            }\n        }\n        const headers = typeof (init.headers || {}).keys === 'function' ? Object.fromEntries(init.headers) : Object.assign({}, init.headers);\n        if ('traceparent' in headers) delete headers['traceparent'];\n        const cacheString = JSON.stringify([\n            MAIN_KEY_PREFIX,\n            this.fetchCacheKeyPrefix || '',\n            url,\n            init.method,\n            headers,\n            init.mode,\n            init.redirect,\n            init.credentials,\n            init.referrer,\n            init.referrerPolicy,\n            init.integrity,\n            init.cache,\n            bodyChunks\n        ]);\n        if (process.env.NEXT_RUNTIME === 'edge') {\n            function bufferToHex(buffer) {\n                return Array.prototype.map.call(new Uint8Array(buffer), (b)=>b.toString(16).padStart(2, '0')).join('');\n            }\n            const buffer = encoder.encode(cacheString);\n            return bufferToHex(await crypto.subtle.digest('SHA-256', buffer));\n        } else {\n            const crypto1 = require('crypto');\n            return crypto1.createHash('sha256').update(cacheString).digest('hex');\n        }\n    }\n    // get data from cache if available\n    async get(cacheKey, ctx) {\n        var _this_cacheHandler, _cacheData_value;\n        // unlike other caches if we have a cacheScope we use it even if\n        // testmode would normally disable it or if requestHeaders say 'no-cache'.\n        if (this.hasDynamicIO && ctx.kind === IncrementalCacheKind.FETCH) {\n            const workUnitStore = workUnitAsyncStorageInstance.getStore();\n            const resumeDataCache = workUnitStore ? getRenderResumeDataCache(workUnitStore) : null;\n            if (resumeDataCache) {\n                const memoryCacheData = resumeDataCache.fetch.get(cacheKey);\n                if ((memoryCacheData == null ? void 0 : memoryCacheData.kind) === CachedRouteKind.FETCH) {\n                    return {\n                        isStale: false,\n                        value: memoryCacheData,\n                        revalidateAfter: false,\n                        isFallback: false\n                    };\n                }\n            }\n        }\n        // we don't leverage the prerender cache in dev mode\n        // so that getStaticProps is always called for easier debugging\n        if (this.disableForTestmode || this.dev && (ctx.kind !== IncrementalCacheKind.FETCH || this.requestHeaders['cache-control'] === 'no-cache')) {\n            return null;\n        }\n        const { isFallback } = ctx;\n        cacheKey = this._getPathname(cacheKey, ctx.kind === IncrementalCacheKind.FETCH);\n        let entry = null;\n        let revalidate = ctx.revalidate;\n        const cacheData = await ((_this_cacheHandler = this.cacheHandler) == null ? void 0 : _this_cacheHandler.get(cacheKey, ctx));\n        if ((cacheData == null ? void 0 : (_cacheData_value = cacheData.value) == null ? void 0 : _cacheData_value.kind) === CachedRouteKind.FETCH) {\n            const combinedTags = [\n                ...ctx.tags || [],\n                ...ctx.softTags || []\n            ];\n            // if a tag was revalidated we don't return stale data\n            if (combinedTags.some((tag)=>{\n                var _this_revalidatedTags;\n                return (_this_revalidatedTags = this.revalidatedTags) == null ? void 0 : _this_revalidatedTags.includes(tag);\n            })) {\n                return null;\n            }\n            revalidate = revalidate || cacheData.value.revalidate;\n            const age = (performance.timeOrigin + performance.now() - (cacheData.lastModified || 0)) / 1000;\n            const isStale = age > revalidate;\n            const data = cacheData.value.data;\n            return {\n                isStale: isStale,\n                value: {\n                    kind: CachedRouteKind.FETCH,\n                    data,\n                    revalidate: revalidate\n                },\n                revalidateAfter: performance.timeOrigin + performance.now() + revalidate * 1000,\n                isFallback\n            };\n        }\n        const curRevalidate = this.revalidateTimings.get(toRoute(cacheKey));\n        let isStale;\n        let revalidateAfter;\n        if ((cacheData == null ? void 0 : cacheData.lastModified) === -1) {\n            isStale = -1;\n            revalidateAfter = -1 * CACHE_ONE_YEAR;\n        } else {\n            revalidateAfter = this.calculateRevalidate(cacheKey, (cacheData == null ? void 0 : cacheData.lastModified) || performance.timeOrigin + performance.now(), this.dev ? ctx.kind !== IncrementalCacheKind.FETCH : false, ctx.isFallback);\n            isStale = revalidateAfter !== false && revalidateAfter < performance.timeOrigin + performance.now() ? true : undefined;\n        }\n        if (cacheData) {\n            entry = {\n                isStale,\n                curRevalidate,\n                revalidateAfter,\n                value: cacheData.value,\n                isFallback\n            };\n        }\n        if (!cacheData && this.prerenderManifest.notFoundRoutes.includes(cacheKey)) {\n            // for the first hit after starting the server the cache\n            // may not have a way to save notFound: true so if\n            // the prerender-manifest marks this as notFound then we\n            // return that entry and trigger a cache set to give it a\n            // chance to update in-memory entries\n            entry = {\n                isStale,\n                value: null,\n                curRevalidate,\n                revalidateAfter,\n                isFallback\n            };\n            this.set(cacheKey, entry.value, ctx);\n        }\n        return entry;\n    }\n    // populate the incremental cache with new data\n    async set(pathname, data, ctx) {\n        // Even if we otherwise disable caching for testMode or if no fetchCache is configured\n        // we still always stash results in the cacheScope if one exists. This is because this\n        // is a transient in memory cache that populates caches ahead of a dynamic render in dev mode\n        // to allow the RSC debug info to have the right environment associated to it.\n        if (this.hasDynamicIO && (data == null ? void 0 : data.kind) === CachedRouteKind.FETCH) {\n            const workUnitStore = workUnitAsyncStorageInstance.getStore();\n            const prerenderResumeDataCache = workUnitStore ? getPrerenderResumeDataCache(workUnitStore) : null;\n            if (prerenderResumeDataCache) {\n                prerenderResumeDataCache.fetch.set(pathname, data);\n            }\n        }\n        if (this.disableForTestmode || this.dev && !ctx.fetchCache) return;\n        pathname = this._getPathname(pathname, ctx.fetchCache);\n        // FetchCache has upper limit of 2MB per-entry currently\n        const itemSize = JSON.stringify(data).length;\n        if (ctx.fetchCache && // we don't show this error/warning when a custom cache handler is being used\n        // as it might not have this limit\n        !this.hasCustomCacheHandler && itemSize > 2 * 1024 * 1024) {\n            if (this.dev) {\n                throw new Error(`Failed to set Next.js data cache, items over 2MB can not be cached (${itemSize} bytes)`);\n            }\n            return;\n        }\n        try {\n            var _this_cacheHandler;\n            // Set the value for the revalidate seconds so if it changes we can\n            // update the cache with the new value.\n            if (typeof ctx.revalidate !== 'undefined' && !ctx.fetchCache) {\n                this.revalidateTimings.set(toRoute(pathname), ctx.revalidate);\n            }\n            await ((_this_cacheHandler = this.cacheHandler) == null ? void 0 : _this_cacheHandler.set(pathname, data, ctx));\n        } catch (error) {\n            console.warn('Failed to update prerender cache for', pathname, error);\n        }\n    }\n}\n\n//# sourceMappingURL=index.js.map", "import { isDynamicRoute } from '../../shared/lib/router/utils';\nimport { getRouteMatcher } from '../../shared/lib/router/utils/route-matcher';\nimport { getRouteRegex } from '../../shared/lib/router/utils/route-regex';\nexport class RouteMatcher {\n    constructor(definition){\n        this.definition = definition;\n        if (isDynamicRoute(definition.pathname)) {\n            this.dynamic = getRouteMatcher(getRouteRegex(definition.pathname));\n        }\n    }\n    /**\n   * Identity returns the identity part of the matcher. This is used to compare\n   * a unique matcher to another. This is also used when sorting dynamic routes,\n   * so it must contain the pathname part.\n   */ get identity() {\n        return this.definition.pathname;\n    }\n    get isDynamic() {\n        return this.dynamic !== undefined;\n    }\n    match(pathname) {\n        const result = this.test(pathname);\n        if (!result) return null;\n        return {\n            definition: this.definition,\n            params: result.params\n        };\n    }\n    test(pathname) {\n        if (this.dynamic) {\n            const params = this.dynamic(pathname);\n            if (!params) return null;\n            return {\n                params\n            };\n        }\n        if (pathname === this.definition.pathname) {\n            return {};\n        }\n        return null;\n    }\n}\n\n//# sourceMappingURL=route-matcher.js.map", "// An internal module to expose the \"waitUntil\" API to Edge SSR and Edge Route Handler functions.\n// This is highly experimental and subject to change.\n// We still need a global key to bypass Webpack's layering of modules.\nconst GLOBAL_KEY = Symbol.for('__next_internal_waitUntil__');\nconst state = // @ts-ignore\nglobalThis[GLOBAL_KEY] || // @ts-ignore\n(globalThis[GLOBAL_KEY] = {\n    waitUntilCounter: 0,\n    waitUntilResolve: undefined,\n    waitUntilPromise: null\n});\n// No matter how many concurrent requests are being handled, we want to make sure\n// that the final promise is only resolved once all of the waitUntil promises have\n// settled.\nfunction resolveOnePromise() {\n    state.waitUntilCounter--;\n    if (state.waitUntilCounter === 0) {\n        state.waitUntilResolve();\n        state.waitUntilPromise = null;\n    }\n}\nexport function internal_getCurrentFunctionWaitUntil() {\n    return state.waitUntilPromise;\n}\nexport function internal_runWithWaitUntil(fn) {\n    const result = fn();\n    if (result && typeof result === 'object' && 'then' in result && 'finally' in result && typeof result.then === 'function' && typeof result.finally === 'function') {\n        if (!state.waitUntilCounter) {\n            // Create the promise for the next batch of waitUntil calls.\n            state.waitUntilPromise = new Promise((resolve)=>{\n                state.waitUntilResolve = resolve;\n            });\n        }\n        state.waitUntilCounter++;\n        return result.finally(()=>{\n            resolveOnePromise();\n        });\n    }\n    return result;\n}\n\n//# sourceMappingURL=internal-edge-wait-until.js.map", "import './globals';\nimport { adapter } from './adapter';\nimport { IncrementalCache } from '../lib/incremental-cache';\nimport { RouteMatcher } from '../route-matchers/route-matcher';\nimport { internal_getCurrentFunctionWaitUntil } from './internal-edge-wait-until';\nimport { getUtils } from '../server-utils';\nimport { searchParamsToUrlQuery } from '../../shared/lib/router/utils/querystring';\nimport { CloseController, trackStreamConsumed } from './web-on-close';\nimport { getEdgePreviewProps } from './get-edge-preview-props';\n/**\n * EdgeRouteModuleWrapper is a wrapper around a route module.\n *\n * Note that this class should only be used in the edge runtime.\n */ export class EdgeRouteModuleWrapper {\n    /**\n   * The constructor is wrapped with private to ensure that it can only be\n   * constructed by the static wrap method.\n   *\n   * @param routeModule the route module to wrap\n   */ constructor(routeModule, nextConfig){\n        this.routeModule = routeModule;\n        this.nextConfig = nextConfig;\n        // TODO: (wyattjoh) possibly allow the module to define it's own matcher\n        this.matcher = new RouteMatcher(routeModule.definition);\n    }\n    /**\n   * This will wrap a module with the EdgeModuleWrapper and return a function\n   * that can be used as a handler for the edge runtime.\n   *\n   * @param module the module to wrap\n   * @param options any options that should be passed to the adapter and\n   *                override the ones passed from the runtime\n   * @returns a function that can be used as a handler for the edge runtime\n   */ static wrap(routeModule, options) {\n        // Create the module wrapper.\n        const wrapper = new EdgeRouteModuleWrapper(routeModule, options.nextConfig);\n        // Return the wrapping function.\n        return (opts)=>{\n            return adapter({\n                ...opts,\n                IncrementalCache,\n                // Bind the handler method to the wrapper so it still has context.\n                handler: wrapper.handler.bind(wrapper)\n            });\n        };\n    }\n    async handler(request, evt) {\n        const utils = getUtils({\n            pageIsDynamic: this.matcher.isDynamic,\n            page: this.matcher.definition.pathname,\n            basePath: request.nextUrl.basePath,\n            // We don't need the `handleRewrite` util, so can just pass an empty object\n            rewrites: {},\n            // only used for rewrites, so setting an arbitrary default value here\n            caseSensitive: false\n        });\n        const { params } = utils.normalizeDynamicRouteParams(searchParamsToUrlQuery(request.nextUrl.searchParams));\n        const waitUntil = evt.waitUntil.bind(evt);\n        const closeController = new CloseController();\n        const previewProps = getEdgePreviewProps();\n        // Create the context for the handler. This contains the params from the\n        // match (if any).\n        const context = {\n            params,\n            prerenderManifest: {\n                version: 4,\n                routes: {},\n                dynamicRoutes: {},\n                preview: previewProps,\n                notFoundRoutes: []\n            },\n            renderOpts: {\n                supportsDynamicResponse: true,\n                waitUntil,\n                onClose: closeController.onClose.bind(closeController),\n                onAfterTaskError: undefined,\n                experimental: {\n                    dynamicIO: !!process.env.__NEXT_DYNAMIC_IO,\n                    authInterrupts: !!process.env.__NEXT_EXPERIMENTAL_AUTH_INTERRUPTS\n                },\n                buildId: '',\n                cacheLifeProfiles: this.nextConfig.experimental.cacheLife\n            }\n        };\n        // Get the response from the handler.\n        let res = await this.routeModule.handle(request, context);\n        const waitUntilPromises = [\n            internal_getCurrentFunctionWaitUntil()\n        ];\n        if (context.renderOpts.pendingWaitUntil) {\n            waitUntilPromises.push(context.renderOpts.pendingWaitUntil);\n        }\n        evt.waitUntil(Promise.all(waitUntilPromises));\n        if (!res.body) {\n            // we can delay running it until a bit later --\n            // if it's needed, we'll have a `waitUntil` lock anyway.\n            setTimeout(()=>closeController.dispatchClose(), 0);\n        } else {\n            // NOTE: if this is a streaming response, onClose may be called later,\n            // so we can't rely on `closeController.listeners` -- it might be 0 at this point.\n            const trackedBody = trackStreamConsumed(res.body, ()=>closeController.dispatchClose());\n            res = new Response(trackedBody, {\n                status: res.status,\n                statusText: res.statusText,\n                headers: res.headers\n            });\n        }\n        return res;\n    }\n}\n\n//# sourceMappingURL=edge-route-module-wrapper.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\n0 && (module.exports = {\n    getTestReqInfo: null,\n    withRequest: null\n});\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    getTestReqInfo: function() {\n        return getTestReqInfo;\n    },\n    withRequest: function() {\n        return withRequest;\n    }\n});\nconst _nodeasync_hooks = require(\"node:async_hooks\");\nconst testStorage = new _nodeasync_hooks.AsyncLocalStorage();\nfunction extractTestInfoFromRequest(req, reader) {\n    const proxyPortHeader = reader.header(req, 'next-test-proxy-port');\n    if (!proxyPortHeader) {\n        return undefined;\n    }\n    const url = reader.url(req);\n    const proxyPort = Number(proxyPortHeader);\n    const testData = reader.header(req, 'next-test-data') || '';\n    return {\n        url,\n        proxyPort,\n        testData\n    };\n}\nfunction withRequest(req, reader, fn) {\n    const testReqInfo = extractTestInfoFromRequest(req, reader);\n    if (!testReqInfo) {\n        return fn();\n    }\n    return testStorage.run(testReqInfo, fn);\n}\nfunction getTestReqInfo(req, reader) {\n    const testReqInfo = testStorage.getStore();\n    if (testReqInfo) {\n        return testReqInfo;\n    }\n    if (req && reader) {\n        return extractTestInfoFromRequest(req, reader);\n    }\n    return undefined;\n}\n\n//# sourceMappingURL=context.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\n0 && (module.exports = {\n    handleFetch: null,\n    interceptFetch: null,\n    reader: null\n});\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    handleFetch: function() {\n        return handleFetch;\n    },\n    interceptFetch: function() {\n        return interceptFetch;\n    },\n    reader: function() {\n        return reader;\n    }\n});\nconst _context = require(\"./context\");\nconst reader = {\n    url (req) {\n        return req.url;\n    },\n    header (req, name) {\n        return req.headers.get(name);\n    }\n};\nfunction getTestStack() {\n    let stack = (new Error().stack ?? '').split('\\n');\n    // Skip the first line and find first non-empty line.\n    for(let i = 1; i < stack.length; i++){\n        if (stack[i].length > 0) {\n            stack = stack.slice(i);\n            break;\n        }\n    }\n    // Filter out franmework lines.\n    stack = stack.filter((f)=>!f.includes('/next/dist/'));\n    // At most 5 lines.\n    stack = stack.slice(0, 5);\n    // Cleanup some internal info and trim.\n    stack = stack.map((s)=>s.replace('webpack-internal:///(rsc)/', '').trim());\n    return stack.join('    ');\n}\nasync function buildProxyRequest(testData, request) {\n    const { url, method, headers, body, cache, credentials, integrity, mode, redirect, referrer, referrerPolicy } = request;\n    return {\n        testData,\n        api: 'fetch',\n        request: {\n            url,\n            method,\n            headers: [\n                ...Array.from(headers),\n                [\n                    'next-test-stack',\n                    getTestStack()\n                ]\n            ],\n            body: body ? Buffer.from(await request.arrayBuffer()).toString('base64') : null,\n            cache,\n            credentials,\n            integrity,\n            mode,\n            redirect,\n            referrer,\n            referrerPolicy\n        }\n    };\n}\nfunction buildResponse(proxyResponse) {\n    const { status, headers, body } = proxyResponse.response;\n    return new Response(body ? Buffer.from(body, 'base64') : null, {\n        status,\n        headers: new Headers(headers)\n    });\n}\nasync function handleFetch(originalFetch, request) {\n    const testInfo = (0, _context.getTestReqInfo)(request, reader);\n    if (!testInfo) {\n        // Passthrough non-test requests.\n        return originalFetch(request);\n    }\n    const { testData, proxyPort } = testInfo;\n    const proxyRequest = await buildProxyRequest(testData, request);\n    const resp = await originalFetch(`http://localhost:${proxyPort}`, {\n        method: 'POST',\n        body: JSON.stringify(proxyRequest),\n        next: {\n            // @ts-ignore\n            internal: true\n        }\n    });\n    if (!resp.ok) {\n        throw new Error(`Proxy request failed: ${resp.status}`);\n    }\n    const proxyResponse = await resp.json();\n    const { api } = proxyResponse;\n    switch(api){\n        case 'continue':\n            return originalFetch(request);\n        case 'abort':\n        case 'unhandled':\n            throw new Error(`Proxy request aborted [${request.method} ${request.url}]`);\n        default:\n            break;\n    }\n    return buildResponse(proxyResponse);\n}\nfunction interceptFetch(originalFetch) {\n    global.fetch = function testFetch(input, init) {\n        var _init_next;\n        // Passthrough internal requests.\n        // @ts-ignore\n        if (init == null ? void 0 : (_init_next = init.next) == null ? void 0 : _init_next.internal) {\n            return originalFetch(input, init);\n        }\n        return handleFetch(originalFetch, new Request(input, init));\n    };\n    return ()=>{\n        global.fetch = originalFetch;\n    };\n}\n\n//# sourceMappingURL=fetch.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\n0 && (module.exports = {\n    interceptTestApis: null,\n    wrapRequestHandler: null\n});\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    interceptTestApis: function() {\n        return interceptTestApis;\n    },\n    wrapRequestHandler: function() {\n        return wrapRequestHandler;\n    }\n});\nconst _context = require(\"./context\");\nconst _fetch = require(\"./fetch\");\nfunction interceptTestApis() {\n    return (0, _fetch.interceptFetch)(global.fetch);\n}\nfunction wrapRequestHandler(handler) {\n    return (req, fn)=>(0, _context.withRequest)(req, _fetch.reader, ()=>handler(req, fn));\n}\n\n//# sourceMappingURL=server-edge.js.map"], "names": ["SERVER_ACTION_MANIFESTS_SINGLETON", "Symbol", "for", "setReferenceManifestsSingleton", "page", "clientReferenceManifest", "serverActionsManifest", "serverModuleMap", "globalThis", "clientReferenceManifestsPerPage", "normalizeAppPath", "relativizeURL", "url", "base", "baseURL", "URL", "relative", "origin", "protocol", "host", "toString", "replace", "TEST_ROUTE", "isDynamicRoute", "route", "isInterceptionRouteAppPath", "extractInterceptionRouteInformation", "interceptedRoute", "test", "ensureLeadingSlash"], "sourceRoot": "", "ignoreList": [0, 1, 2, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 28, 29, 30, 31, 32, 33, 34, 35, 38, 39, 40, 41, 42, 43, 44, 45, 46]}