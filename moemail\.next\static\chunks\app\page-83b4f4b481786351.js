(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[974],{6370:(e,t,s)=>{Promise.resolve().then(s.bind(s,3515)),Promise.resolve().then(s.bind(s,2435)),Promise.resolve().then(s.bind(s,1665)),Promise.resolve().then(s.bind(s,5368))},3515:(e,t,s)=>{"use strict";s.d(t,{SignButton:()=>h});var r=s(5155),a=s(9393),n=s(5565),l=s(4822),i=s(8427),o=s(6046),c=s(8173),d=s.n(c),m=s(2558);function h(e){let{size:t="default"}=e,s=(0,o.useRouter)(),{data:c,status:h}=(0,l.wV)();return"loading"===h?(0,r.jsx)("div",{className:"h-9"}):(null==c?void 0:c.user)?(0,r.jsxs)("div",{className:"flex items-center gap-4",children:[(0,r.jsxs)(d(),{href:"/profile",className:"flex items-center gap-2 hover:opacity-80 transition-opacity",children:[c.user.image&&(0,r.jsx)(n.default,{src:c.user.image,alt:c.user.name||"用户头像",width:24,height:24,className:"rounded-full"}),(0,r.jsx)("span",{className:"text-sm",children:c.user.name})]}),(0,r.jsx)(a.$,{onClick:()=>(0,l.CI)({callbackUrl:"/"}),variant:"outline",className:(0,m.cn)("flex-shrink-0","lg"===t?"px-8":""),size:t,children:"登出"})]}):(0,r.jsxs)(a.$,{onClick:()=>s.push("/login"),className:(0,m.cn)("gap-2","lg"===t?"px-8":""),size:t,children:[(0,r.jsx)(i.A,{className:"lg"===t?"w-5 h-5":"w-4 h-4"}),"登录/注册"]})}},2435:(e,t,s)=>{"use strict";s.d(t,{ActionButton:()=>o});var r=s(5155),a=s(9393),n=s(6462),l=s(6046),i=s(3515);function o(e){let{isLoggedIn:t}=e,s=(0,l.useRouter)();return t?(0,r.jsxs)(a.$,{size:"lg",onClick:()=>s.push("/moe"),className:"gap-2 bg-primary hover:bg-primary/90 text-white px-8",children:[(0,r.jsx)(n.A,{className:"w-5 h-5"}),"进入邮箱"]}):(0,r.jsx)(i.SignButton,{size:"lg"})}},1665:(e,t,s)=>{"use strict";s.d(t,{ThemeToggle:()=>o});var r=s(5155),a=s(7725),n=s(2104),l=s(8872),i=s(9393);function o(){let{theme:e,setTheme:t}=(0,l.D)();return(0,r.jsxs)(i.$,{variant:"ghost",size:"icon",onClick:()=>t("light"===e?"dark":"light"),className:"rounded-full",children:[(0,r.jsx)(a.A,{className:"h-5 w-5 rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0"}),(0,r.jsx)(n.A,{className:"absolute h-5 w-5 rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100"}),(0,r.jsx)("span",{className:"sr-only",children:"切换主题"})]})}},9393:(e,t,s)=>{"use strict";s.d(t,{$:()=>c,r:()=>o});var r=s(5155),a=s(2115),n=s(2317),l=s(1027),i=s(2558);let o=(0,l.F)("inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground shadow hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90",outline:"border border-input bg-transparent shadow-sm hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2",sm:"h-8 rounded-md px-3 text-xs",lg:"h-10 rounded-md px-8",icon:"h-9 w-9"}},defaultVariants:{variant:"default",size:"default"}}),c=a.forwardRef((e,t)=>{let{className:s,variant:a,size:l,asChild:c=!1,...d}=e,m=c?n.DX:"button";return(0,r.jsx)(m,{className:(0,i.cn)(o({variant:a,size:l,className:s})),ref:t,...d})});c.displayName="Button"},5368:(e,t,s)=>{"use strict";s.d(t,{Logo:()=>l});var r=s(5155),a=s(8173),n=s.n(a);function l(){return(0,r.jsxs)(n(),{href:"/",className:"flex items-center gap-2 hover:opacity-80 transition-opacity",children:[(0,r.jsx)("div",{className:"relative w-8 h-8",children:(0,r.jsx)("div",{className:"absolute inset-0 grid grid-cols-8 grid-rows-8 gap-px",children:(0,r.jsxs)("svg",{width:"32",height:"32",viewBox:"0 0 32 32",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:"text-primary",children:[(0,r.jsx)("path",{d:"M4 8h24v16H4V8z",className:"fill-primary/20"}),(0,r.jsx)("path",{d:"M4 8h24v2H4V8zM4 22h24v2H4v-2z",className:"fill-primary"}),(0,r.jsx)("path",{d:"M14 12h4v4h-4v-4zM12 14h2v4h-2v-4zM18 14h2v4h-2v-4zM14 18h4v2h-4v-2z",className:"fill-primary"}),(0,r.jsx)("path",{d:"M4 8l12 8 12-8",className:"stroke-primary stroke-2",fill:"none"}),(0,r.jsx)("path",{d:"M8 18h2v2H8v-2zM22 18h2v2h-2v-2z",className:"fill-primary/60"}),(0,r.jsx)("path",{d:"M8 14h2v2H8v-2zM22 14h2v2h-2v-2z",className:"fill-primary/40"})]})})}),(0,r.jsx)("span",{className:"font-bold tracking-wider bg-clip-text text-transparent bg-gradient-to-r from-primary to-purple-600",children:"MoeMail"})]})}},2558:(e,t,s)=>{"use strict";s.d(t,{cn:()=>n});var r=s(3463),a=s(9795);function n(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return(0,a.QP)((0,r.$)(t))}s(2818)},8872:(e,t,s)=>{"use strict";s.d(t,{D:()=>c,N:()=>d});var r=s(2115);let a=["light","dark"],n="(prefers-color-scheme: dark)",l="undefined"==typeof window,i=(0,r.createContext)(void 0),o={setTheme:e=>{},themes:[]},c=()=>{var e;return null!==(e=(0,r.useContext)(i))&&void 0!==e?e:o},d=e=>(0,r.useContext)(i)?r.createElement(r.Fragment,null,e.children):r.createElement(h,e),m=["light","dark"],h=({forcedTheme:e,disableTransitionOnChange:t=!1,enableSystem:s=!0,enableColorScheme:l=!0,storageKey:o="theme",themes:c=m,defaultTheme:d=s?"system":"light",attribute:h="data-theme",value:p,children:x,nonce:y})=>{let[b,w]=(0,r.useState)(()=>v(o,d)),[N,$]=(0,r.useState)(()=>v(o)),k=p?Object.values(p):c,j=(0,r.useCallback)(e=>{let r=e;if(!r)return;"system"===e&&s&&(r=f());let n=p?p[r]:r,i=t?g():null,o=document.documentElement;if("class"===h?(o.classList.remove(...k),n&&o.classList.add(n)):n?o.setAttribute(h,n):o.removeAttribute(h),l){let e=a.includes(d)?d:null,t=a.includes(r)?r:e;o.style.colorScheme=t}null==i||i()},[]),C=(0,r.useCallback)(e=>{w(e);try{localStorage.setItem(o,e)}catch(e){}},[e]),S=(0,r.useCallback)(t=>{$(f(t)),"system"===b&&s&&!e&&j("system")},[b,e]);(0,r.useEffect)(()=>{let e=window.matchMedia(n);return e.addListener(S),S(e),()=>e.removeListener(S)},[S]),(0,r.useEffect)(()=>{let e=e=>{e.key===o&&C(e.newValue||d)};return window.addEventListener("storage",e),()=>window.removeEventListener("storage",e)},[C]),(0,r.useEffect)(()=>{j(null!=e?e:b)},[e,b]);let z=(0,r.useMemo)(()=>({theme:b,setTheme:C,forcedTheme:e,resolvedTheme:"system"===b?N:b,themes:s?[...c,"system"]:c,systemTheme:s?N:void 0}),[b,C,e,N,s,c]);return r.createElement(i.Provider,{value:z},r.createElement(u,{forcedTheme:e,disableTransitionOnChange:t,enableSystem:s,enableColorScheme:l,storageKey:o,themes:c,defaultTheme:d,attribute:h,value:p,children:x,attrs:k,nonce:y}),x)},u=(0,r.memo)(({forcedTheme:e,storageKey:t,attribute:s,enableSystem:l,enableColorScheme:i,defaultTheme:o,value:c,attrs:d,nonce:m})=>{let h="system"===o,u="class"===s?`var d=document.documentElement,c=d.classList;c.remove(${d.map(e=>`'${e}'`).join(",")});`:`var d=document.documentElement,n='${s}',s='setAttribute';`,v=i?a.includes(o)&&o?`if(e==='light'||e==='dark'||!e)d.style.colorScheme=e||'${o}'`:"if(e==='light'||e==='dark')d.style.colorScheme=e":"",g=(e,t=!1,r=!0)=>{let n=c?c[e]:e,l=t?e+"|| ''":`'${n}'`,o="";return i&&r&&!t&&a.includes(e)&&(o+=`d.style.colorScheme = '${e}';`),"class"===s?o+=t||n?`c.add(${l})`:"null":n&&(o+=`d[s](n,${l})`),o},f=e?`!function(){${u}${g(e)}}()`:l?`!function(){try{${u}var e=localStorage.getItem('${t}');if('system'===e||(!e&&${h})){var t='${n}',m=window.matchMedia(t);if(m.media!==t||m.matches){${g("dark")}}else{${g("light")}}}else if(e){${c?`var x=${JSON.stringify(c)};`:""}${g(c?"x[e]":"e",!0)}}${h?"":"else{"+g(o,!1,!1)+"}"}${v}}catch(e){}}()`:`!function(){try{${u}var e=localStorage.getItem('${t}');if(e){${c?`var x=${JSON.stringify(c)};`:""}${g(c?"x[e]":"e",!0)}}else{${g(o,!1,!1)};}${v}}catch(t){}}();`;return r.createElement("script",{nonce:m,dangerouslySetInnerHTML:{__html:f}})},()=>!0),v=(e,t)=>{let s;if(!l){try{s=localStorage.getItem(e)||void 0}catch(e){}return s||t}},g=()=>{let e=document.createElement("style");return e.appendChild(document.createTextNode("*{-webkit-transition:none!important;-moz-transition:none!important;-o-transition:none!important;-ms-transition:none!important;transition:none!important}")),document.head.appendChild(e),()=>{window.getComputedStyle(document.body),setTimeout(()=>{document.head.removeChild(e)},1)}},f=e=>(e||(e=window.matchMedia(n)),e.matches?"dark":"light")}},e=>{var t=t=>e(e.s=t);e.O(0,[630,568,441,517,358],()=>t(6370)),_N_E=e.O()}]);