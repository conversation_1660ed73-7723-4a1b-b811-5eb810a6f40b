import { drizzle } from 'drizzle-orm/better-sqlite3'
import Database from 'better-sqlite3'
import { migrate } from 'drizzle-orm/better-sqlite3/migrator'
import * as schema from '../app/lib/schema'

// 创建本地开发数据库
const sqlite = new Database('./dev.db')
const db = drizzle(sqlite, { schema })

async function runMigration() {
  console.log('Running database migration...')
  
  try {
    await migrate(db, { migrationsFolder: './drizzle' })
    console.log('Migration completed successfully!')
  } catch (error) {
    console.error('Migration failed:', error)
    process.exit(1)
  } finally {
    sqlite.close()
  }
}

runMigration()
