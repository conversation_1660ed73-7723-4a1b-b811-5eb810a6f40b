(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[268],{65521:e=>{"use strict";e.exports=require("node:async_hooks")},25356:e=>{"use strict";e.exports=require("node:buffer")},74640:(e,t,i)=>{"use strict";i.r(t),i.d(t,{ComponentMod:()=>I,default:()=>P});var s,r={};i.r(r),i.d(r,{DELETE:()=>v,GET:()=>y,runtime:()=>x});var a={};i.r(a),i.d(a,{patchFetch:()=>b,routeModule:()=>w,serverHooks:()=>R,workAsyncStorage:()=>S,workUnitAsyncStorage:()=>E});var o=i(26312),n=i(35471),c=i(22680),l=i(65954),m=i(14139),d=i(43113),u=i(25601),f=i(85885),p=i(43144),g=i(13091),h=i(5120);let x="edge";async function v(e,{params:t}){let i=await (0,h.F6)();if(!i)return u.Rp.json({error:"未授权"},{status:401});try{let e=(0,f.d)(),{id:s,messageId:r}=await t;if(!await e.query.emails.findFirst({where:(0,g.Uo)((0,g.eq)(p.emails.id,s),(0,g.eq)(p.emails.userId,i))}))return u.Rp.json({error:"Email not found or no permission to view"},{status:403});if(!await e.query.messages.findFirst({where:(0,g.Uo)((0,g.eq)(p.messages.emailId,s),(0,g.eq)(p.messages.id,r))}))return u.Rp.json({error:"Message not found or already deleted"},{status:404});return await e.delete(p.messages).where((0,g.eq)(p.messages.id,r)),u.Rp.json({success:!0})}catch(e){return console.error("Failed to delete email:",e),u.Rp.json({error:"Failed to delete message"},{status:500})}}async function y(e,{params:t}){try{let{id:e,messageId:i}=await t,s=(0,f.d)(),r=await (0,h.F6)();if(!r)return u.Rp.json({error:"未授权"},{status:401});let a=await s.query.emails.findFirst({where:(0,g.eq)(p.emails.id,e)});if(!a)return u.Rp.json({error:"邮箱不存在"},{status:404});let o=a.userId===r,n=!1;if(o||(n=!!await s.query.userEmailBindings.findFirst({where:(0,g.Uo)((0,g.eq)(p.userEmailBindings.userId,r),(0,g.eq)(p.userEmailBindings.emailId,e))})),!o&&!n)return u.Rp.json({error:"无权限查看"},{status:403});let c=await s.query.messages.findFirst({where:(0,g.Uo)((0,g.eq)(p.messages.id,i),(0,g.eq)(p.messages.emailId,e))});if(!c)return u.Rp.json({error:"Message not found"},{status:404});return u.Rp.json({message:{id:c.id,from_address:c.fromAddress,subject:c.subject,content:c.content,html:c.html,received_at:c.receivedAt.getTime()}})}catch(e){return console.error("Failed to fetch message:",e),u.Rp.json({error:"Failed to fetch message"},{status:500})}}let w=new l.AppRouteRouteModule({definition:{kind:m.A.APP_ROUTE,page:"/api/emails/[id]/[messageId]/route",pathname:"/api/emails/[id]/[messageId]",filename:"route",bundlePath:"app/api/emails/[id]/[messageId]/route"},resolvedPagePath:"F:\\CODE\\Project\\tempmail\\moemail\\app\\api\\emails\\[id]\\[messageId]\\route.ts",nextConfigOutput:"",userland:r}),{workAsyncStorage:S,workUnitAsyncStorage:E,serverHooks:R}=w;function b(){return(0,d.V5)({workAsyncStorage:S,workUnitAsyncStorage:E})}let q=null==(s=self.__RSC_MANIFEST)?void 0:s["/api/emails/[id]/[messageId]/route"],C=(e=>e?JSON.parse(e):void 0)(self.__RSC_SERVER_MANIFEST);q&&C&&(0,n.fQ)({page:"/api/emails/[id]/[messageId]/route",clientReferenceManifest:q,serverActionsManifest:C,serverModuleMap:(0,o.e)({serverActionsManifest:C})});let I=a,P=c.s.wrap(w,{nextConfig:{env:{},eslint:{ignoreDuringBuilds:!1},typescript:{ignoreBuildErrors:!1,tsconfigPath:"tsconfig.json"},distDir:".next",cleanDistDir:!0,assetPrefix:"",cacheMaxMemorySize:0x3200000,configOrigin:"next.config.ts",useFileSystemPublicRoutes:!0,generateEtags:!0,pageExtensions:["tsx","ts","jsx","js"],poweredByHeader:!0,compress:!0,images:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",loaderFile:"",domains:[],disableStaticImages:!1,minimumCacheTTL:60,formats:["image/webp"],dangerouslyAllowSVG:!1,contentSecurityPolicy:"script-src 'none'; frame-src 'none'; sandbox;",contentDispositionType:"attachment",remotePatterns:[{protocol:"https",hostname:"avatars.githubusercontent.com"}],unoptimized:!1},devIndicators:{appIsrStatus:!0,buildActivity:!0,buildActivityPosition:"bottom-right"},onDemandEntries:{maxInactiveAge:6e4,pagesBufferLength:5},amp:{canonicalBase:""},basePath:"",sassOptions:{},trailingSlash:!1,i18n:null,productionBrowserSourceMaps:!1,excludeDefaultMomentLocales:!0,serverRuntimeConfig:{},publicRuntimeConfig:{},reactProductionProfiling:!1,reactStrictMode:null,reactMaxHeadersLength:6e3,httpAgentOptions:{keepAlive:!0},logging:{},expireTime:31536e3,staticPageGenerationTimeout:60,modularizeImports:{"@mui/icons-material":{transform:"@mui/icons-material/{{member}}"},lodash:{transform:"lodash/{{member}}"}},outputFileTracingRoot:"F:\\CODE\\Project\\tempmail\\moemail",experimental:{cacheLife:{default:{stale:300,revalidate:900,expire:0xfffffffe},seconds:{stale:0,revalidate:1,expire:60},minutes:{stale:300,revalidate:60,expire:3600},hours:{stale:300,revalidate:3600,expire:86400},days:{stale:300,revalidate:86400,expire:604800},weeks:{stale:300,revalidate:604800,expire:2592e3},max:{stale:300,revalidate:2592e3,expire:0xfffffffe}},cacheHandlers:{},cssChunking:!0,multiZoneDraftMode:!1,appNavFailHandling:!1,prerenderEarlyExit:!0,serverMinification:!0,serverSourceMaps:!1,linkNoTouchStart:!1,caseSensitiveRoutes:!1,clientSegmentCache:!1,preloadEntriesOnStart:!0,clientRouterFilter:!0,clientRouterFilterRedirects:!1,fetchCacheKeyPrefix:"",middlewarePrefetch:"flexible",optimisticClientCache:!0,manualClientBasePath:!1,cpus:11,memoryBasedWorkersCount:!1,imgOptConcurrency:null,imgOptTimeoutInSeconds:7,imgOptMaxInputPixels:0xfff8001,imgOptSequentialRead:null,isrFlushToDisk:!0,workerThreads:!1,optimizeCss:!1,nextScriptWorkers:!1,scrollRestoration:!1,externalDir:!1,disableOptimizedLoading:!1,gzipSize:!0,craCompat:!1,esmExternals:!0,fullySpecified:!1,swcTraceProfiling:!1,forceSwcTransforms:!1,largePageDataBytes:128e3,turbo:{root:"F:\\CODE\\Project\\tempmail\\moemail"},typedRoutes:!1,typedEnv:!1,parallelServerCompiles:!1,parallelServerBuildTraces:!1,ppr:!1,authInterrupts:!1,reactOwnerStack:!1,webpackMemoryOptimizations:!1,optimizeServerReact:!0,useEarlyImport:!1,staleTimes:{dynamic:0,static:300},serverComponentsHmrCache:!0,staticGenerationMaxConcurrency:8,staticGenerationMinPagesPerWorker:25,dynamicIO:!1,inlineCss:!1,optimizePackageImports:["lucide-react","date-fns","lodash-es","ramda","antd","react-bootstrap","ahooks","@ant-design/icons","@headlessui/react","@headlessui-float/react","@heroicons/react/20/solid","@heroicons/react/24/solid","@heroicons/react/24/outline","@visx/visx","@tremor/react","rxjs","@mui/material","@mui/icons-material","recharts","react-use","effect","@effect/schema","@effect/platform","@effect/platform-node","@effect/platform-browser","@effect/platform-bun","@effect/sql","@effect/sql-mssql","@effect/sql-mysql2","@effect/sql-pg","@effect/sql-squlite-node","@effect/sql-squlite-bun","@effect/sql-squlite-wasm","@effect/sql-squlite-react-native","@effect/rpc","@effect/rpc-http","@effect/typeclass","@effect/experimental","@effect/opentelemetry","@material-ui/core","@material-ui/icons","@tabler/icons-react","mui-core","react-icons/ai","react-icons/bi","react-icons/bs","react-icons/cg","react-icons/ci","react-icons/di","react-icons/fa","react-icons/fa6","react-icons/fc","react-icons/fi","react-icons/gi","react-icons/go","react-icons/gr","react-icons/hi","react-icons/hi2","react-icons/im","react-icons/io","react-icons/io5","react-icons/lia","react-icons/lib","react-icons/lu","react-icons/md","react-icons/pi","react-icons/ri","react-icons/rx","react-icons/si","react-icons/sl","react-icons/tb","react-icons/tfi","react-icons/ti","react-icons/vsc","react-icons/wi"]},bundlePagesRouterDependencies:!1,configFile:"F:\\CODE\\Project\\tempmail\\moemail\\next.config.ts",configFileName:"next.config.ts"}})},96487:()=>{},78335:()=>{}},e=>{var t=t=>e(e.s=t);e.O(0,[730,752,899,498,220,156],()=>t(74640));var i=e.O();(_ENTRIES="undefined"==typeof _ENTRIES?{}:_ENTRIES)["middleware_app/api/emails/[id]/[messageId]/route"]=i}]);
//# sourceMappingURL=route.js.map