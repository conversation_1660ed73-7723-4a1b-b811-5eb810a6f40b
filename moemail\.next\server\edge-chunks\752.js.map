{"version": 3, "file": "edge-chunks/752.js", "mappings": "4FACA,oBACA,wBACA,kCACA,6BACA,wBACA,kCAQA,cACA,+CACA,kBACA,oBACA,OAA6B,kDAA4F,EAEzH,QACA,EAQA,GAtBA,kBACA,6BAA2D,YAAa,sBACxE,EAoBA,CACA,wDAEA,sCAAmD,SAAa,YAChE,YACA,gDAAsE,WAAc,IACpF,2FAAyG,KAAM,iDAC/G,4DACA,qFACA,CACA,2BACA,yBACA,sCACA,MACA,kBACA,YACA,CACA,CAAO,YACP,QACA,oBACA,yCACA,CAAS,YACT,CACA,oCACA,CACA,CACA,CAAC,EAGD,KAhDA,SACA,eACA,OAA8B,uBAAkC,CAChE,GA8CA,GACA,gCACA,uBACA,CAAC,EACD,UArCA,MAAoD,eAAkB,SAAa,EAqCnF,GAGyB,EAAQ,KAAa,EAC9C,MA7CA,+BAAmG,GACnG,oBAAuG,EAAvG,eAAyE,sBAA8B,EACvG,EACA,GA0CA,KACA,aACA,kCAEA,aACA,oBACA,aAmCA,iCACA,SAEA,MADA,EApCA;;;;;EAKA,GAEA,QACA,CACA,aACA,UACA,OAEA,GADA,oDAEA;;;GAGA,EAYA,aAVA,qDAWA,CACA,QACA,sKErGA,SACA,eACA,YACA,CACA,QAAU,GAAU,4BACpB,eACA,YACA,WAEA,IACA,CACA,CACA,QACA,iBACA,aACA,0BACA,CACA,QAAU,GAAU,0BACpB,UACA,OAAiB,IAAK,gBACtB,SAEA,OAAiB,IAAK,cAGtB,8BAA6C,IAAK,qBAFlD,kBAKA,OAAiB,GAAc,CAC/B,OACA,KAAkB,GAAc,EAChC,gBACA,UACA,EAEA,OAAiB,IAAK,iBACtB,QAA6B,IAAK,iBAClC,MACA,SAEA,SAOA,OANA,uBACA,eACA,KACA,yBAEA,CAAO,EACP,CACA,CACA,iBACA,CAAQ,OAAE,GAAQ,GAAM,EACxB,sCAEA,CACA,CACA,CACA,QACA,eACA,YACA,CACA,QAAU,GAAU,EAAI,IAAgC,CACxD,eACA,kBACA,GAHwD,CAGxD,wBAEA,KAEA,CACA,gBACA,+BACA,CAIA,gBACA,iBACA,EACA,sCAEA,CACA,gBACA,WAAa,IAAG,iCAChB,CACA,gBACA,OAAS,IAAG,2BACZ,CAAQ,OAAE,GAAI,GAAM,EACpB,OAEQ,QAAE,GAAI,IAAG,EACjB,OAEQ,QAAE,GAAI,IAAG,UACjB,OAEA,GAEA,2ECnGA,cAEA,MADA,kFACA,iCACA,CACA,cAEA,OADA,iFACA,gBAEA,EADA,0BAA4D,mBAAsB,EAAE,YAAc,EAE/F,GACH,CACA,cACA,QACA,CACA,QACA,QAAU,GAAU,gBAEpB,SACA,gBACA,oBACA,GACA,mDACA,CACA,mBACA,gBACA,cACA,cAAgC,IAAK,0BACrC,UAAmC,IAAK,sBACxC,KAAmB,EAAO,GAAG,EAAU,GAAG,OAAY,EAItD,OAHA,eACA,yBAEA,cAEA,cACA,QAAyB,IAAK,0BAC9B,IAA4B,IAAK,sBACjC,KAAwB,EAAO,GAAG,EAAU,EAC5C,0BACA,6BAA+C,IAAK,mBACpD,SAA6B,EAAS,GAAG,OAAY,EACrD,kCACA,CACA,uBACA,CACA,CACA,aACA,cACA,oBACA,CACA,+DCnDA,SACA,QAAU,GAAU,kBACpB,mBACA,OACA,aACA,OACA,iBACA,WACA,eACA,cACA,cACA,YACA,kBACA,kBACA,WACA,aACA,gBACA,CACA,CAYA,QACA,YAOA,UAEA,OADA,uBACA,KASA,WAGA,OAFA,sBACA,0BACA,KAQA,cAGA,OAFA,wBACA,0BACA,KAKA,yBAQA,eAGA,OAFA,yBACA,0BACA,KAKA,2BAMA,aAGA,OAFA,0BACA,uBACA,KAGA,WACA,uBAEA,oBACA,CACA,+DCrGA,SACA,iBACA,aACA,cACA,iBACA,2BACA,uBACA,uBACA,2BACA,6BACA,6BACA,0BACA,yBACA,6BACA,6BACA,yBACA,6BACA,2BACA,2CAEA,QAAU,GAAU,WACpB,IACA,WACA,OACA,SACA,QACA,UACA,WACA,WACA,SACA,WACA,WACA,SACA,WACA,kBACA,iBACA,yBACA,OACA,sBACA,QACA,CACA,oBACA,QACA,CAEA,sBACA,+EACA,CACA,+DChDA,SACA,QAAU,GAAU,qBACpB,SACA,cACA,CACA,CACA,QACA,QAAU,GAAU,kBACpB,mBACA,GACA,4BACA,CACA,cACA,gBACA,IACA,wBACA,CAAQ,MACR,gBACA,CACA,CAAK,EACL,2BAAiE,aAA6B,MAC9F,4BAAgC,EAAM,EAAE,EAAU,EAClD,CACA,CACA,QACA,QAAU,GAAU,eACpB,WACA,CACA,sECzBA,iBAAgC,GAAY,CAC5C,eACA,QACA,eACA,CACA,QAAU,GAAU,4BACpB,UACA,sBACA,CACA,OACA,sBACA,CACA,CACA,QACA,mBACA,YACA,qBACA,YACA,CACA,QAAU,GAAU,kBAEpB,oBACA,WACA,kBAEA,kBACA,QACA,CACA,kBACA,8BACA,CACA,kBACA,8BACA,CACA,iBACA,oBACA,4BAEA,sCACA,CACA,eACA,2BACA,UACA,6BAEA,WACA,6BAEA,WACA,6BAEA,CACA,CACA,CACA,QACA,eACA,cACA,CACA,QAAU,GAAU,iBACpB,8BACA,iCACA,CACA,OACA,iCACA,IACA,wDACA,CAAM,SACN,UAAgB,GAAY,EAAG,4CAAiD,MAAgB,GAAI,CACpG,CACA,CAEA,qCACA,QACA,CACA,OACA,iFACA,CAEA,qCACA,8BACA,CACA,OACA,iFACA,CAEA,qCACA,8BACA,CACA,UACA,oFACA,CACA,eAEA,MADA,sBACA,OAGA,wCACA,8BACA,CACA,CACA,gBAAgC,GAAkB,CAClD,yBACA,eACA,cACA,kBACA,CACA,QAAU,GAAU,qBACpB,YACA,UAAc,GAAwB,CAEtC,eC5GA,iBAA8B,EAC9B,WAD2C,CAC3C,UAAmD,EACnD,SACA,cACA,cACA,eACA,0BAAwC,CACxC,CACA,QAFkD,EAExC,CAAU,oBACpB,OACA,wBAEA,aADA,2BAGA,EACA,YACA,EACA,EACA,EACA,EAEA,CACA,eACA,SACA,KACA,gBACA,mBACA,eAEA,GADA,UACA,kBACA,qCACQ,CACR,mBACA,OACA,6CAEA,CACA,CAEA,MADA,6BACA,gCACA,CACA,qCACA,iBAEA,qCACA,oBAEA,wCACA,mBACA,CACA,uBACA,kDACA,gBAAmB,IAAG,aAAa,8BAA8C,IACjF,IACA,iBAEA,OADA,eAAqB,QAAG,UACxB,CACA,CAAM,SAEN,MADA,eAAqB,QAAG,YACxB,CACA,CACA,CACA,CACA,gBAA4B,EAC5B,QAAU,GAAU,IADyB,aACzB,OACpB,eACA,WAA+B,iBAAiB,EAChD,yEACA,wBAA2B,IAAG,kBAAkB,EAAc,IAC9D,IACA,iBAEA,OADA,uBAA6B,IAAG,0BAA0B,EAAc,IACxE,CACA,CAAM,SAEN,MADA,uBAA6B,IAAG,8BAA8B,EAAc,IAC5E,CACA,CACA,CACA,CACA,cACA,SACA,gBACA,kCACA,SACA,CACA,QACA,CACA,gBAA8B,EAC9B,2BACA,mBACA,cACA,8BACA,0BACA,cACA,WACA,CACA,QAAU,GAAU,oBAEpB,mBAEA,OAEA,KACA,OACA,MAAmB,QAAgB,wBAA2C,EAE9E,OADA,uCACA,0BACA,CACA,aACA,IAAY,uDAAkD,KAC9D,WACA,MAAqB,QAAgB,eAAsC,EAE3E,OADA,oBACA,0BAAgD,UAAS,wBACzD,CACA,2BACA,2BACA,CACA,wBAIA,CAHA,GACA,iBAEA,sCAGA,wBACA,2BAEA,SAA6B,QAAY,0CALzC,CAMA,CACA,aACA,WAAY,sEAAuE,KACnF,WACA,MAAqB,QAAgB,eAAsC,EAE3E,OADA,oBACA,kCAAgD,EAAS,QACzD,CACA,kCACA,KAGA,EACA,KAEW,QAAY,WALvB,MAMA,CACA,wBAIA,CAHA,GACA,oBAEA,sCAGA,wBACA,6BAEW,QAAY,yCALvB,CAMA,CACA,UACA,MAAmB,QAAgB,wBAA2C,EAE9E,OADA,uCACA,0BACA,CAEA,wBACA,mCAEA,CCpKA,gBAAgC,GAAkB,CAClD,QAAU,GAAU,qBACpB,SACA,4BACA,CACA,CACA,iBAAoC,MAEpC,EAMA,EAPA,UAAsB,IAAkB,EAAG,gBAAuB,EAQlE,GANA,cACA,MAAiB,EACb,WAD0B,EAC1B,EACJ,aAGA,UACA,MAAyB,QAA6B,CACtD,SACM,IAA2B,EAEjC,GACA,oBACA,gBACA,8BAEA,CACA,UAAsB,EAAe,cAA4B,EAAQ,EACzE,uBAEA,OADA,YACA,CACA,0DCvCA,uCAEA,gBACA,0BACA,SAEA,kBACA,SAEA,8CACA,YACA,UAAgB,oBAAyB,gIAGzC,2CACA,KACA,SACA,uBACA,SAEA,0BACA,CAEA,QACA,CAvBA,4GCAA,uBACA,QAAU,GAAU,6BACpB,SAAgB,UAAgB,EAChC,SACA,yBACA,YACA,CACA,CACA,kBACA,QAAU,GAAU,4BACpB,eACA,OAAY,mBAAqB,CACjC,CACA,2ECZA,iBAA+B,IAAe,CAC9C,QAAU,GAAU,iCACpB,GACA,8BACA,CAEA,SACA,2BACA,CACA,CACA,gBAAwB,IAAQ,CAChC,QAAU,GAAU,cACpB,aACA,eACA,CACA,CACA,cACA,mBACA,0GClBA,SACA,QAAU,GAAU,wBAEpB,UAEA,sBAEA,kCACA,KACA,oBACA,SAAc,8BAAgC,IAC9C,YAAe,qDACf,EACA,IACA,0BACA,0BAEA,CACA,YAEA,OADA,wCACA,IACA,CACA,YAEA,OADA,wCACA,KAGA,SACA,oBACA,CACA,CACA,QACA,iBACA,aACA,2BACA,0BACA,0BAEA,QAAU,GAAU,iBACpB,UACA,SACA,SACA,UACA,SAAY,8BAAgC,iBAC5C,mBACA,mBACA,GACA,WAAiB,GAAS,KAC1B,EACA,WAA8B,GAAS,KACvC,EACA,CACA,aAAsB,YAAiB,KAEvC,gBCnDA,gBACA,SAAY,EAAM,GAAS,EAAE,GAAG,YAAkB,SAElD,QACA,iBACA,YACA,cACA,CACA,QAAU,GAAU,EAAI,IAA2B,CAEnD,QAEA,uBAJmD,CAInD,CACA,oBAEA,OADA,+BACA,KAGA,SACA,kEACA,CACA,CACA,QACA,QAAU,GAAU,EAAI,IAA6B,CAErD,iBACA,GACA,WACA,CACA,CANqD,EAMrD,MACA,yBACA,CACA,CACA,QACA,qBACA,aACA,eACA,uDACA,uBACA,CACA,QAAU,GAAU,EAAI,IACxB,SACA,KACA,WAH4C,MAG5C,GACA,UACA,iBAEA,CCpDA,kBACA,YAA0B,WAAwB,KAClD,WACA,aACA,IACA,QACA,CACA,WACA,2CAEA,OAGA,iBAAmC,EACnC,yCAGA,8CCVA,gBAA8B,GAAa,CAC3C,4BACA,CAAU,GAAU,oBACpB,SACA,qCACA,CACA,iBAA8B,EAE9B,OADA,iCAAkC,YAAc,EAChD,KAEA,YAIA,OAHA,wBACA,yBACA,gCACA,IACA,CACA,qBAMA,OALA,uBACA,KACA,cACA,aACA,EACA,KAGA,sBACA,wCAAyC,YAAc,GAC1C,OAAI,CACjB,QACA,UAA8B,EAAiB,IAE/C,EAAqB,4BADrB,IACqB,IAQrB,OANA,YACA,uBAEA,YACA,uBAEA,UACA,CAAS,CACT,EACA,GAGA,CAEA,0BACA,2BACA,CACA,CACA,gBAAuB,GAAM,CAC7B,iBACA,cACA,cAA0B,EAAa,aAEvC,WACA,YACA,CACA,QAAU,GAAU,aAEpB,kBACA,QAAU,GAAU,sBACpB,aACA,wBACA,CACA,aACA,+BACA,gCACA,2BACA,EACA,eACA,YACA,aACA,cACA,EACA,MAEA,OADA,6BACA,KAEA,OAEA,OADA,8BACA,KAEA,aAEA,OADA,+BACA,KAEA,YAEA,OADA,8BACA,KA+BA,MAEA,OADA,2BACA,KAEA,CACA,QACA,QAAU,GAAU,EAAI,IAAe,aACvC,OADuC,CACvC,CACA,YACA,iBACA,YACA,kBACA,CACA,KACA,UACA,KACA,YAEA,kBACA,QAAU,GAAU,+BACpB,OACA,2BACA,0BACA,kBACA,CAEA,SACA,uCACA,aACA,EACA,YACA,EAEA,CACA,CACA,kBACA,qBACA,WACA,kBACA,aACA,iBAEA,YACA,CAAU,GAAU,YACpB,aACA,SAAc,6BAA6B,GAAG,wCAA+C,GAE7F,sBAIA,MAHA,oBACA,GDtHA,YACA,gBAzCA,SACA,SACA,IACA,KACA,kBACA,WACA,YACA,YACA,WAEA,KACA,IACA,QACA,CAEA,GADA,KACA,UACA,KACA,QACA,CACA,YACA,qBACA,UACA,IACA,QACA,CACA,KAAmB,IAAnB,EACA,cAEA,KAAmB,IAAnB,EAAmB,CACnB,kBACA,UACA,IACA,QACA,CACA,mBACA,UACA,GACA,CACA,aAGA,KACA,QACA,ECmH0B,IAE1B,+CACA,CACA,yBACA,YACA,iBAAiC,QAAE,uGAEnC,EACA,EACW,SD5HX,EC4HsB,CD5HtB,EACA,QAAW,EAAE,SACb,iBACA,KAEA,mBACA,IAAiB,4CAAiD,GAElE,GAAc,EAAK,GAChB,WAAa,CAChB,ECkHsB,EACtB,CACA,qFC1LA,iBAAsC,IAAe,CACrD,QAAU,GAAU,4BACpB,aACA,oBAAwB,QAAG,QAC3B,CACA,mHCJA,iBAA4B,GAAuB,CACnD,QAAU,GAAU,iBACpB,gBACA,wBACA,CAEA,SACA,2BACA,CACA,CACA,gBAAqB,IAAQ,CAC7B,QAAU,GAAU,WACpB,aACA,YACA,CACA,sBACA,kBACA,CACA,oBACA,sBACA,CACA,CACA,gBAAkC,GAAuB,CACzD,QAAU,GAAU,uBACpB,gBACA,gCACA,CAEA,SACA,aACA,EACA,YAEA,CACA,CACA,gBAA2B,IAAQ,CACnC,QAAU,GAAU,iBACpB,aACA,YACA,CACA,CACA,gBACA,SAAU,YAAe,CAAE,OAAsB,aACjD,iBACA,SAEA,QACA,0ECjDA,iBAAqC,IAAe,CACpD,QAAU,GAAU,2BACpB,6BACA,MACA,SAAc,QAAmB,CACjC,gCACA,cACA,eACA,iBACA,CACA,EAAM,IACN,+BACA,aACA,EAIA,OAFA,0BACA,uBACA,KAEA,gCACA,MACA,SAAc,QAAmB,CACjC,gCACA,iBACA,eACA,iBACA,CACA,EAAM,IACN,+BACA,gBACA,EAIA,OAFA,0BACA,uBACA,KAEA,sFCnCA,iBAA+B,GAAsB,CACrD,QAAU,GAAU,iCACpB,GACA,6BACA,CAEA,SACA,2BACA,CACA,CACA,gBAAwB,IAAQ,CAChC,QAAU,GAAU,cACpB,aACA,eACA,CACA,4BACA,mBACA,mBAEA,CACA,CACA,CACA,cACA,mBACA,oFCzBA,iBAA4B,IAAe,CAC3C,QAAU,GAAU,8BACpB,GACA,wBACA,CAEA,SACA,2BACA,CACA,CACA,gBAAqB,IAAQ,CAC7B,QAAU,GAAU,uBACpB,KACA,UACA,CACA,aACA,YACA,CACA,oBACA,wBACA,CACA,sBACA,sBACA,IACA,oBACA,CAAQ,MAER,CAEA,QACA,CACA,CACA,cACA,mBACA,oFClCA,iBAA6B,IAAe,CAC5C,QAAU,GAAU,+BACpB,GACA,yBACA,CAEA,SACA,2BACA,CACA,CACA,gBAAsB,IAAQ,CAC9B,QAAU,GAAU,wBACpB,KACA,UACA,CACA,aACA,aACA,CACA,oBACA,wBACA,CACA,sBACA,sBACA,IACA,oBACA,CAAQ,MAER,CAEA,QACA,CACA,CACA,cACA,mBACA,+FCjCA,iBAA+B,IAAe,CAC9C,QAAU,GAAU,iCACpB,OACA,8BACA,wBACA,mBACA,CAEA,SACA,2BACA,CACA,CACA,gBAAwB,IAAQ,CAChC,QAAU,GAAU,cACpB,UACA,kBACA,KACA,WACA,2BACA,kBACA,CACA,oBACA,6CACA,WAAwB,eAAe,IAAI,WAAW,GAChD,wBACN,UAEA,WAAwB,eAAe,GAGvC,CACA,gBACA,SAAU,YAAe,CAAE,OAAsB,MACjD,qCACA,sFClCA,iBAA4B,IAAe,CAC3C,QAAU,GAAU,8BACpB,KACA,2BACA,8BAGA,SACA,2BACA,CACA,CACA,gBAAqB,IAAQ,CAC7B,QAAU,GAAU,WACpB,kCACA,aACA,YACA,CACA,CACA,iBAAuB,EACvB,SAAU,YAAe,CAAE,OAAsB,MACjD,iBACA,wGCpBA,iBAA4B,GAAuB,CACnD,mBACA,2BACA,oBACA,iBACA,2BACA,uBACA,CACA,QAAU,GAAU,kBAEpB,SACA,2BACA,CACA,CACA,gBAAqB,IAAQ,CAC7B,QAAU,GAAU,WACpB,aACA,sBACA,KACA,WACA,iCACA,0BACA,CACA,aACA,qCAA2D,eAAe,GAC1E,aAAkB,EAAU,EAAE,uCAA2C,EAEzE,CACA,iBAAuB,EACvB,SAAU,YAAe,CAAE,OAAsB,MACjD,8CACA,mHC/BA,iBAAiC,GAAuB,CACxD,QAAU,GAAU,mCACpB,OACA,8BACA,2BACA,uBACA,CAEA,SACA,2BACA,CACA,CACA,gBAA0B,IAAQ,CAClC,QAAU,GAAU,gBACpB,aACA,sBACA,KACA,WACA,iCACA,2BAEA,aACA,sCAA4D,eAAe,GAC3E,kBAAuB,EAAU,EAAE,uCAA2C,EAE9E,sBACA,uCACA,CACA,oBACA,eACA,CAEA,gBAAuC,GAAuB,CAC9D,QAAU,GAAU,yCACpB,OACA,sCACA,2BACA,uBACA,CAEA,SACA,aACA,EACA,YAEA,CACA,CACA,gBAAgC,IAAQ,CACxC,QAAU,GAAU,sBACpB,aACA,SACA,kBACA,WACA,iCACA,2BAEA,aACA,qCAA2D,eAAe,GAC1E,kBAAuB,EAAU,EAAE,uCAA2C,EAE9E,CACA,iBAA4B,EAC5B,SAAU,YAAiB,QAAsB,aACjD,mBACA,wCAEA,yCACA,+FCpEA,iBAA4B,IAAe,CAC3C,QAAU,GAAU,8BACpB,GACA,0BACA,CAIA,gBACA,oBAAwB,QAAG,oBAC3B,CAEA,SACA,2BACA,CACA,CACA,gBAAqB,IAAQ,CAC7B,QAAU,GAAU,WACpB,aACA,YACA,CACA,CACA,cACA,mBACA,qFCzBA,wBACA,aACA,8BAEA,QACA,CACA,QACA,QAAU,GAAU,wBAEpB,OAEA,kBACA,KACA,eACA,WACA,CAEA,SACA,sCACA,CACA,CACA,QACA,mBACA,aACA,eACA,WACA,CACA,QAAU,GAAU,gBACpB,SACA,KACA,UACA,qBAA2B,WAAW,IAAO,cAAc,GAAG,sCAAoD,KAElH,uHC/BA,iBAAgC,GAAsB,CACtD,QAAU,GAAU,kCACpB,GACA,8BACA,CAEA,SACA,2BACA,CACA,CACA,gBAAyB,IAAQ,CACjC,QAAU,GAAU,eACpB,aACA,cACA,CACA,4BACA,mBACA,EAEA,SACA,CACA,CACA,gBAAgC,GAAsB,CACtD,QAAU,GAAU,kCACpB,GACA,8BACA,CAEA,SACA,aACA,EACA,YAEA,CACA,CACA,gBAAyB,IAAQ,CACjC,QAAU,GAAU,eACpB,aACA,cACA,CAEA,sBACA,gBACA,CACA,CACA,gBACA,SAAU,YAAe,CAAE,OAAsB,YACjD,kBACA,SAEA,QACA,CCpDA,gBAAmC,IAAe,CAClD,QAAU,GAAU,qCACpB,GACA,kCACA,0BACA,sBACA,CAEA,SACA,aACA,EACA,YAEA,CACA,CACA,gBAA4B,IAAQ,CACpC,QAAU,GAAU,kBACpB,aACA,iBACA,CACA,4BACA,mBACA,EAEA,SACA,CACA,CACA,gBAAmC,IAAe,CAClD,QAAU,GAAU,qCACpB,GACA,kCACA,yBACA,CAEA,SACA,aACA,EACA,YAEA,CACA,CACA,gBAA4B,IAAQ,CACpC,QAAU,GAAU,kBACpB,aACA,iBACA,CAEA,sBACA,gBACA,CACA,CACA,gBACA,SAAU,YAAe,CAAE,OAAsB,YACjD,kBACA,SAEA,QACA,eCzDA,iBAA4B,IAAe,CAC3C,QAAU,GAAU,8BACpB,KACA,2BACA,4BACA,8BAGA,SACA,2BACA,CACA,CACA,gBAAqB,IAAQ,CAC7B,QAAU,GAAU,WACpB,yBACA,mCACA,aACA,2CAAqD,YAAY,GAEjE,CACA,SAAS,EAAI,MAAU,CAAV,CACb,SAAU,YAAe,CAAE,OAAsB,MACjD,iBACA,CCxBA,gBAA4B,IAAe,CAC3C,QAAU,GAAU,8BACpB,GACA,0BACA,CAEA,SACA,2BACA,CACA,CACA,gBAAqB,IAAQ,CAC7B,QAAU,GAAU,UACpB,cACA,YACA,CACA,CACA,cACA,mBACA,CCjBA,gBAAoC,IAAe,CACnD,QAAU,GAAU,sCACpB,OACA,mCACA,0BACA,8BACA,CAEA,SACA,aACA,EACA,YAEA,CACA,CACA,gBAA6B,IAAQ,CACrC,QAAU,GAAU,mBACpB,QACA,MACA,OACA,kBACA,WACA,wDACA,uCACA,2CAEA,aACA,oBAEA,sBACA,uDACA,CACA,oBACA,mDACA,CACA,CACA,cACA,cACA,SAAY,YAAiB,QAAsB,MACnD,mBACA,CACA,eC1CA,iBAAuC,IAAe,CACtD,QAAU,GAAU,yCACpB,GACA,qCACA,CAEA,SACA,aACA,EACA,YAEA,CACA,CACA,gBAAgC,IAAQ,CACxC,QAAU,GAAU,sBACpB,aACA,wBACA,CACA,4BACA,mBACA,qBAEA,CACA,CACA,CACA,cACA,mBACA,CC3BA,gBAA4B,IAAe,CAC3C,QAAU,GAAU,iBACpB,gBACA,0BACA,CAEA,SACA,2BACA,CACA,CACA,gBAAqB,IAAQ,CAC7B,QAAU,GAAU,WACpB,aACA,YACA,CACA,CACA,cACA,mBACA,eCjBA,iBAAgC,IAAe,CAC/C,QAAU,GAAU,kCACpB,KACA,+BACA,4BACA,CAEA,SACA,2BACA,CACA,CACA,gBAAyB,IAAQ,CACjC,QAAU,GAAU,eACpB,yCACA,+CACA,aACA,sBAAqC,YAAY,KACjD,qBAA2C,eAAe,MAC1D,iBAAsB,EAAO,EAAE,EAAU,EAEzC,CACA,iBAA2B,EAC3B,SAAU,YAAe,CAAE,OAAsB,MACjD,iBACA,0BCxBA,iBAA4B,IAAe,CAC3C,QAAU,GAAU,iBACpB,gBACA,yBACA,CAEA,SACA,aACA,EACA,YAEA,CACA,CACA,gBAA0B,IAAQ,CAClC,QAAU,GAAU,WACpB,aACA,YACA,CACA,sBACA,oCACA,sEACA,CACA,oBACA,QAAa,EAAE,KAAS,GAAG,KAAS,GAAG,MAAU,EAEjD,CACA,gBAA+B,IAAe,CAC9C,QAAU,GAAU,iCACpB,GACA,2BACA,CAEA,SACA,aACA,EACA,YAEA,CACA,CACA,gBAAwB,IAAQ,CAChC,QAAU,GAAU,cACpB,aACA,YACA,CACA,sBACA,oCACA,OAAa,qEACb,CACA,oBACA,QAAa,EAAE,IAAQ,GAAG,IAAQ,GAAG,KAAS,CAC9C,CACA,CACA,gBACA,SAAU,YAAe,CAAE,OAAsB,aACjD,0BAGA,SAFA,QAGA,CC3DA,gBAA+B,IAAe,CAC9C,QAAU,GAAU,iCACpB,GACA,6BACA,CAEA,SACA,2BACA,CACA,CACA,gBAAwB,IAAQ,CAChC,QAAU,GAAU,cACpB,aACA,eACA,CACA,CACA,cACA,mBACA,CClBA,gBAAgC,IAAe,CAC/C,QAAU,GAAU,kCACpB,GACA,8BACA,CAEA,SACA,2BACA,CACA,CACA,gBAAyB,IAAQ,CACjC,QAAU,GAAU,eACpB,aACA,gBACA,CACA,CACA,cACA,mBACA,eCjBA,iBAAkC,IAAe,CACjD,QAAU,GAAU,oCACpB,GACA,+BACA,CAEA,SACA,cACA,EACA,YAEA,CACA,CACA,iBAA2B,IAAQ,CACnC,QAAU,GAAU,iBACpB,aACA,aACA,CACA,sBACA,uBACA,kCACA,kDAEA,gBAEA,oBACA,UAAe,KAAS,GAAG,KAAS,GAEpC,CACA,iBAAmC,IAAe,CAClD,QAAU,GAAU,qCACpB,GACA,+BACA,CAEA,SACA,cACA,EACA,YAEA,CACA,CACA,iBAA4B,IAAQ,CACpC,QAAU,GAAU,kBACpB,aACA,aACA,CACA,sBACA,uBACA,kCACA,OAAe,8CACf,CACA,QACA,CACA,oBACA,UAAe,IAAQ,GAAG,IAAQ,GAElC,CACA,iBACA,SAAU,YAAe,CAAE,OAAsB,aACjD,0BAGA,UAFA,QAGA,CC5DA,iBAEA,mBADA,oBAEA,YAAkB,IAAO,IACzB,qBAEA,yBACA,CACA,eACA,MAhBA,YACA,SACA,YAAkB,WAAgB,KAClC,2CAEA,wBACA,EAUA,GACA,IACA,IADA,EACA,CACA,KACA,6BACA,uBAOA,GANA,KAEA,eACA,qBACA,MAEA,cACA,cAEA,OADA,MAGA,OADA,KACA,MAEA,wCACA,CChCA,iBAAgC,IAAe,CAC/C,QAAU,GAAU,kCACpB,GACA,6BACA,CAEA,SACA,cACA,EACA,YAEA,CACA,CACA,iBAAyB,IAAQ,CACjC,QAAU,GAAU,eACpB,aACA,uBACA,CACA,sBACA,OAAW,GAAS,EACpB,CACA,GAFoB,cAEpB,GACA,eAAoB,MAAU,EAAE,KAAS,GAEzC,CACA,iBAAsC,IAAe,CACrD,QAAU,GAAU,2BACpB,gBACA,kCACA,CAEA,SACA,cACA,EACA,YAEA,CACA,CACA,iBAA+B,IAAQ,CACvC,QAAU,GAAU,qBACpB,aACA,uBACA,CACA,sBACA,MAAmB,GAAS,GAC5B,GAD4B,GAC5B,CAAa,cACb,CACA,oBACA,eAAoB,KAAS,EAAE,IAAQ,EACvC,CACA,CACA,iBACA,SAAU,YAAe,CAAE,OAAsB,aACjD,0BAGA,UAFA,SAGA,CC3DA,iBAA4B,IAAe,CAC3C,QAAU,GAAU,8BACpB,KACA,2BACA,oBACA,CAEA,SACA,4BACA,CACA,CACA,iBAAqB,IAAQ,CAC7B,QAAU,GAAU,UACpB,kBACA,UACA,CACA,aACA,YACA,CACA,sBACA,mBACA,qBAEA,CACA,CAEA,eACA,oBACA,CC5BA,iBAA8B,IAAe,CAC7C,QAAU,GAAU,gCACpB,GACA,6BACA,0BACA,sBACA,CAEA,SACA,4BACA,CACA,CACA,iBAAuB,IAAQ,CAC/B,QAAU,GAAU,aACpB,aACA,cACA,CACA,CACA,eACA,oBACA,CCnBA,iBAAgC,GAAsB,CACtD,QAAU,GAAU,kCACpB,GACA,8BACA,CAEA,SACA,4BACA,CACA,CACA,iBAAyB,IAAQ,CACjC,QAAU,GAAU,eACpB,aACA,gBACA,CACA,sBACA,mBACA,UAEA,CAEA,CACA,eACA,oBACA,CCzBA,iBAAmC,IAAe,CAClD,QAAU,GAAU,qCACpB,GACA,kCACA,0BACA,sBACA,CAEA,SACA,cACA,EACA,YAEA,CACA,CACA,iBAA4B,IAAQ,CACpC,QAAU,GAAU,kBACpB,aACA,mBACA,CACA,CACA,eACA,oBACA,kDCtBA,kBAA+B,IAAe,CAC9C,QAAU,GAAU,iCACpB,KACA,8BACA,4BACA,8BAGA,SACA,4BACA,CACA,CACA,iBAAwB,IAAQ,CAChC,QAAU,GAAU,cACpB,0BACA,kCACA,aACA,iDAA2D,YAAY,GAEvE,CACA,kBAA0B,EAC1B,SAAU,YAAe,CAAE,OAAsB,MACjD,kBACA,CCvBA,iBAAoC,IAAe,CACnD,QAAU,GAAU,sCACpB,KACA,mCACA,oCAGA,SACA,cACA,EACA,YAEA,CACA,CACA,iBAA6B,IAAQ,CACrC,QAAU,GAAU,mBACpB,kCACA,aACA,aAAkB,gBAAgB,GAElC,CACA,iBACA,SAAU,YAAe,CAAE,OAAsB,MACjD,kBACA,CCxBA,iBAAkC,IAAe,CACjD,QAAU,GAAU,oCACpB,KACA,gCACA,oCAGA,SACA,cACA,EACA,YAEA,CACA,CACA,iBAA2B,IAAQ,CACnC,QAAU,GAAU,iBACpB,iCACA,cACA,iBAAsB,gBAAgB,GAEtC,oBACA,wBACA,CACA,sBACA,4DACA,CACA,CACA,iBACA,SAAU,YAAiB,QAAsB,MACjD,kBACA,CC9BA,iBAAoC,IAAe,CACnD,QAAU,GAAU,sCACpB,KACA,mCACA,oCAGA,SACA,cACA,EACA,YAEA,CACA,CACA,iBAA6B,IAAQ,CACrC,QAAU,GAAU,mBACpB,kCACA,aACA,mBAAwB,gBAAgB,GAExC,CACA,iBACA,SAAU,YAAiB,QAAsB,MACjD,kBACA,CCxBA,iBAA8B,IAAe,CAC7C,QAAU,GAAU,gCACpB,KACA,4BACA,oCAGA,SACA,4BACA,CACA,CACA,iBAAuB,IAAQ,CAC/B,QAAU,GAAU,aACpB,iCACA,cACA,gBAAqB,gBAAgB,GAErC,oBACA,wBACA,CACA,sBACA,4DACA,CACA,CACA,iBACA,SAAU,YAAe,CAAE,OAAsB,MACjD,kBACA,CE3BA,iDACA,kCACA,kBAAsB,IAAK,CAC3B,QAAU,GAAU,mBAEpB,uBAAkC,CAAE,IAAK,SACzC,qBACA,YACA,CAAG,GAEH,QAEA,QAEG,IAAK,mCAmCR,gBACA,UAlCA,aACA,oBACA,yBDYA,CACA,KCbgE,CDatD,GACV,SAAa,GACb,GCfmF,IDexE,MACX,IAAQ,GACR,IAAQ,GACR,UAAc,GACd,IAAQ,MACR,eAAmB,GACnB,IAAQ,GACR,OAAW,MACX,QAAY,GACZ,IAAQ,MACR,KAAS,MACT,IAAQ,GACR,OAAW,GACX,QAAY,GACZ,OAAW,MACX,KAAS,IACT,QAAY,IACZ,IAAQ,IACR,MAAU,IACV,QAAY,IACZ,WAAe,IACf,IAAQ,OACR,IAAQ,OACR,SAAa,OACb,IAAQ,OACR,OAAW,IACX,GAAO,IACP,OAAW,IACX,SAAa,IACb,MAAU,GACV,GC7CmF,EACnF,qBACA,gCACA,EACA,WACA,MAFA,EAEA,SAEA,OADA,uCACA,MACK,GAEL,qBACA,gCACA,EACA,WAEA,GAHA,EAEA,0BACA,IAGA,qBAMA,OALA,EAAQ,IAAK,mBACb,EAAQ,IAAK,8BACb,GACA,oCAEA,iBACA,eACA,0BACA,EAEA,CAAG,EACH,EAEA,2ECpDA,SACA,QAAU,GAAU,sBAEpB,oBACA,6BAEA,+DCNA,SACA,QAAU,GAAU,kBACpB,mCACA,SACA,0BACA,CACA,WACA,iBACA,IACA,MACA,GAEA,IAEA,MADA,MACA,CACA,EAEA,CACA,UACA,+BACA,CACA,uDC6OA,2BACA,SACA,QACA,EACA,EACA,eACA,aACA,2BACA,OACA,oCACA,YAAoC,QAAE,WACtC,EACA,wBACA,EACA,YACA,GACA,MACA,KACA,EACA,wBACA,EACA,YACA,GAGA,EAAM,QAGN,EAFA,cACA,UAGA,EADU,QAAE,GAAQ,GAAM,EAC1B,EACiB,QAAE,GAAQ,IAAG,EAC9B,UAEA,cAEA,gDACA,CAEA,QACA,mJA7QA,SACA,mBACA,mBACA,uBACA,oBACA,2BAA+C,IAAK,cAEpD,QAAU,GAAU,aACpB,oBACA,UAEA,QACA,iBACA,aACA,aACA,CACA,QAAU,GAAU,cAEpB,kBACA,qBACA,2BACA,cACA,iBACA,CACA,QAAU,GAAU,QACpB,iBACA,YACA,iBACA,qBACA,YACA,iBAGA,OADA,cACA,CACA,CACA,CACA,kBACA,mBACA,2BACA,aACA,CACA,QAAU,GAAU,SACpB,iBACA,YACA,iBACA,qBACA,aAGA,OADA,cACA,CACA,CACA,CACA,aACA,OACA,GAAO,MACP,OAAW,MACX,EAAM,MACN,MAAU,MACV,EAAM,MACN,GAAO,MACP,KAAS,MACT,OAAW,MACX,MAAU,MACV,SAAa,MACb,IAAQ,MACR,EAAM,MACN,GAAO,MACP,EAAM,MACN,GAAO,MACP,UAAc,MACd,SAAa,MACb,OAAW,MACX,QAAY,MACZ,UAAc,MACd,EAAM,MACN,GAAO,MAEP,CACA,aACA,OACA,GAAO,MACP,GAAO,KACP,IAAQ,KAER,CACA,gBACA,2CAAkE,QAAE,WAAoB,IAAK,GAC7F,cAEA,SACA,KACA,KACA,iCACA,GAAQ,QAAE,GAAQ,IAAK,GACvB,MAAqB,QAAkB,IACvC,OAUA,aATA,OACA,MACA,SACA,SAAsB,IAAK,cAC3B,SAAsB,IAAK,gBAC3B,UAAuB,IAAK,iBAC5B,0BAAqD,CACrD,8BAEA,cACA,EAAc,IAAK,mBAEnB,WACA,wBAGA,QAAgC,IAAK,gCAAoC,IAAK,6BAC9E,KACA,8BACc,QAAE,GAAc,IAAiB,GAC/C,kCAIA,EAAM,OAAS,QAAE,WAMjB,EALA,MAAqB,QAAkB,UACvC,OAKA,8BAJA,SACA,aAIA,MACA,WACA,iBACA,GACA,uBAEA,EAAU,IACV,QACA,OACA,YAA2B,CAC3B,YACA,GAEA,mBAGA,CAEA,OAAW,yBACX,CACA,gBACA,aACA,EACA,sBACA,mCACA,EACA,mBACA,GAGA,CAgBA,kBACA,GAAM,QAAE,gBACR,OACA,uBACA,gCAGA,QAA8C,QAAkB,qBAChE,MACA,YACA,UAAgB,kBAAyB,IAAK,cAAc,wBAG5D,WACA,MACA,sBAA8B,EAAsB,wBAEpD,oBACA,IAA0C,QAAkB,KAC5D,MACA,YACA,UAAgB,EAAY,IAAK,cAAc,wBAG/C,SACA,2BACA,aAEA,8GACA,UAGA,cACA,2BACA,2CAAiD,eAAsB,cAAc,EAAsB,IAC3G,MACA,yCAA+C,EAAsB,SAAS,cAAqB,IAAK,cAAc,kCAGtH,SAA6B,QAAE,sBAC/B,OACA,8BACA,8BAGA,aACA,sDAA0D,EAAkB,GAAG,YAAmB,GAElG,CACA,cACA,OACA,aAjEA,KACA,aAgEA,EA9DA,EACA,EACA,6CAEA,EA2DA,cAxDA,KACA,aAuDA,EAvDA,IACA,CAuDA,CACA,sHC5PA,SACA,QAAU,GAAU,0BACpB,mBACA,GACA,aAAoB,KACpB,CACA,SACA,WACA,OACA,OACA,yBACA,mBACA,KAEA,EAEA,OAAiB,GAAc,CAC/B,OACA,KAAoB,GAAc,EAClC,yBACA,EAAmB,GAAc,iBACjC,KAEA,EAEA,sBACA,YAGA,OADoB,QAAE,GAAW,GAAQ,qBAAgC,QAAE,GAAW,IAAI,IAAa,GAAc,mBACrH,IACA,GAAQ,QAAE,GAAQ,IAAG,WACrB,+DACA,aAEA,gBAEA,OADA,sBACA,CACA,CACA,GAAQ,QAAE,GAAQ,IAAG,GACrB,mCACA,QAEA,aACA,2BAAmC,EAAK,yJAExC,OACA,CAAQ,OAAE,GAAQ,GAAM,EACxB,kBACA,UACA,EACA,IAAc,IAAuB,CACrC,UACA,QACA,IAAkB,IAAsB,2DAKxC,EAEA,6BACA,EAEA,+BACA,CACA,sRC1DA,sBACA,CAAM,QAAoB,KAAa,QAAY,KAAY,QAAE,GAAQ,IAAK,GAAM,QAAE,GAAQ,IAAW,GAAM,QAAE,GAAQ,GAAM,GAAM,QAAE,GAAQ,IAAK,GAAM,QAAE,GAAQ,IAAI,EAGxK,EAFA,IAAe,IAAK,KAGpB,CACA,aACS,QAAG,GAAG,GAAM,IAAI,OAAyB,EAElD,SACS,QAAG,GAAG,GAAM,KAAK,OAAyB,EAEnD,iBACA,eACA,sBAEA,aACA,WAGe,IAAG,CADlB,aACkB,EAEF,CAChB,IAAQ,IAAW,MACf,IAAG,YAAsB,IAAW,WACxC,IAAQ,IAAW,MACnB,CACA,CACA,iBACA,eACA,sBAEA,aACA,WAGe,IAAG,CADlB,aACkB,EAEF,CAChB,IAAQ,IAAW,MACf,IAAG,YAAsB,IAAW,UACxC,IAAQ,IAAW,MACnB,CACA,CACA,cACA,MAAS,QAAG,OAAO,EAAU,EAE7B,aACS,QAAG,GAAG,GAAM,IAAI,OAAyB,EAElD,SACS,QAAG,GAAG,GAAM,KAAK,OAAyB,EAEnD,SACS,QAAG,GAAG,GAAM,IAAI,OAAyB,EAElD,SACS,QAAG,GAAG,GAAM,KAAK,OAAyB,EAEnD,uBACA,iBACA,aACa,QAAG,QAEL,QAAG,GAAG,GAAQ,KAAK,iBAA0C,EAE/D,QAAG,GAAG,GAAQ,KAAK,OAA4B,EAExD,uBACA,iBACA,aACa,QAAG,OAEL,QAAG,GAAG,GAAQ,SAAS,iBAA0C,EAEnE,QAAG,GAAG,GAAQ,SAAS,OAA4B,EAE5D,cACA,MAAS,QAAG,GAAG,GAAO,SAEtB,cACA,MAAS,QAAG,GAAG,GAAO,aAEtB,cACA,MAAS,QAAG,UAAU,EAAS,EAE/B,cACA,MAAS,QAAG,cAAc,EAAS,EAEnC,kBACA,MAAS,QAAG,GAAG,GAAQ,UAAU,QAA0B,MAAM,EACjE,EACA,GACI,EAEJ,kBACA,MAAS,QAAG,GAAG,GAAQ,cAAc,EACrC,EACA,GACA,CAAK,MAAM,OAAyB,EAEpC,gBACA,MAAS,QAAG,GAAG,GAAQ,OAAO,EAAM,EAEpC,gBACA,MAAS,QAAG,GAAG,GAAQ,WAAW,EAAM,EAExC,gBACA,MAAS,QAAG,GAAG,GAAQ,QAAQ,EAAM,EAErC,gBACA,MAAS,QAAG,GAAG,GAAQ,YAAY,EAAM,yEC3HzC,cACA,MAAS,QAAG,GAAG,GAAQ,KAEvB,cACA,MAAS,QAAG,GAAG,GAAQ,yJCHvB,oCAIA,iBAAkC,IAAe,CACjD,QAAU,GAAU,uBACpB,kBACA,iCACA,kBACA,CAEA,SACA,aACA,EACA,YAEA,CACA,CACA,gBAA2B,IAAQ,CACnC,QAAU,GAAU,iBACpB,sBACA,mDACA,KACA,WACA,gBACA,CACA,aACA,0BAEA,2DCxBA,SACA,QAAU,GAAU,EAAI,IAAoB,CAE5C,cACA,UAH4C,GAG5C,+BAgBA,QACA,QAAU,GAAU,gBACpB,kBACA,GACA,kCAEA,SACA,oBACA,CACA,CACA,QACA,eACA,kBACA,CACA,QAAU,GAAU,QAEpB,SACA,uBACA,UAEA,OADA,wCACA,KAEA,WACA,OAAW,GAAM,wCACjB,0DAKA,OAJA,kBACA,2BACA,+CACA,CAAO,EACP,CACA,CAAK,CACL,CACA,gCACA,sBAAmC,IACnC,qDACA,oCAAoD,QACpD,CAAK,EACL,CACA,SACA,aACA,cACA,gBACA,eACA,kBACA,CAAM,EACN,gBA3DA,GACA,OAAmB,kBACnB,eACA,aACA,2BACA,oBACA,WACA,eAEA,8BAGA,QACA,EA8CA,UACA,GAAU,QAAE,MACZ,OAAiB,gCAEjB,GAAU,QAAE,MACZ,OAAiB,0BAEjB,cACA,OAAiB,kBAEjB,qBACA,mBACA,2BACA,UACA,cACA,oBAIA,OADA,mBACA,oCACA,CACA,GAAU,QAAE,MACZ,sDACA,KACA,qCACS,EAET,GAAU,QAAE,GAAQ,IAAK,GACzB,QAAiC,IAAK,gBACtC,IAAgC,IAAK,cACrC,OACA,kCACA,UAEA,CACA,GAAU,QAAE,GAAQ,GAAM,GAC1B,2BACA,8BACA,OAAmB,oBAEnB,cAAuC,IAAK,gBAC5C,OACA,YAA2B,IAAO,wBAAoD,IAAK,2CAAsG,IAAK,wBACtM,UAEA,CACA,GAAU,QAAE,OACZ,QAAiC,GAAc,SAC/C,IAA+B,GAAc,OAC7C,OACA,kCACA,UAEA,CACA,GAAU,QAAE,OACZ,GAAY,QAAE,YACd,OAAmB,gDAEnB,8DACA,GAAY,QAAE,MACd,8CAEA,KACA,OAAmB,wCAEnB,eAIA,OAHA,GACA,mBAEA,CAAiB,wCACjB,OACA,CAAU,OAAE,MACZ,CAAiB,gDAEP,QAAE,qCACZ,CAAiB,+BAEP,QAAE,GAAQ,GAAQ,EAC5B,WACA,CAAmB,4BAEnB,iCACA,WACA,QACA,YACA,iBACA,ID1JA,sBC4JkB,GD5JlB,KC4JkB,GD5JlB,UC6JA,SACA,CAAmB,6CAEnB,CAAiB,6BAEjB,KACA,0BACA,gDAEA,iCACA,WACA,WACA,WACA,IAEA,EACA,CAAiB,wCAEjB,CAAe,+CACf,CAAK,EACL,CACA,+BAA0B,EAAc,EACxC,YACA,aAEA,2CACA,oBAEA,sBACA,YAEA,uBACA,yBACA,sBACA,qBAEA,IACA,CACA,yCACA,CACA,SACA,WACA,CACA,aACA,WACA,KAEA,qBACA,CACA,WAEA,OADA,mCAAqD,sBAA8B,EACnF,KAEA,eAEA,OADA,2BACA,KAQA,MACA,oBACA,CACA,CACA,QACA,eACA,YACA,CACA,QAAU,GAAU,SACpB,MACA,SACA,oBACA,CACA,CAIA,cACA,iGACA,CACA,OACA,uBACA,EACA,GACA,qBACA,EACA,EACA,KACA,MAEA,SAKA,mBACA,aACA,cACA,CACA,QAAU,GAAU,UACpB,KACA,UACA,oBACA,CACA,CAIA,mBACA,SAIA,eAHA,qCACA,oBAEA,aACA,wBAEA,eACA,CACA,KAIA,QAHA,WACA,gBACA,EAKA,WAHA,YACA,eACA,EAKA,MAHA,YACA,wBACA,EAYA,OAVA,cACA,SACA,2BACA,iBACA,UAEA,UAEA,eACA,EAKA,aAHA,YACA,eACA,EAKA,cAHA,YACA,eACA,EAKA,QAHA,cACA,iBACA,EAEA,CAAC,UAAkB,EACnB,KACA,QACA,iBACA,WACA,iBACA,CACA,QAAY,GAAU,gBAEtB,oBACA,SACA,gBAGA,QACA,sCACA,CACA,CACA,YACA,CAAC,UAAkB,CACnB,SACA,eACA,WACA,CACA,QAAU,GAAU,gBACpB,SACA,oBACA,CACA,CAIA,gBACA,iBACA,GAAQ,QAAE,OACV,kBACA,yCAAqD,OAAO,iBAE5D,iBAEA,GAAQ,QAAE,OAAc,QAAE,aAC1B,wBACA,yCAAqD,aAAa,iBAElE,kDACA,CACA,QACA,CAAG,CACH,CACA,QACA,QAAU,GAAU,UAEjB,GAAc,cACjB,CAAgB,yCAA4C,EAC5D,KAAS,GAAc,GACvB,OACA,eACA,SACA,iBACA,QACA,cACA,UACA,CACA,CACA,SACA,oBACA,CACA,CACA,GAAM,6BACN,oBACA,EACA,IAAK,6BACL,oBACA,EACA,GAAQ,6BACR,oBACA,wGCxYA,SACA,QAAU,GAAU,2BAEpB,UAEA,UAEA,uBACA,KACA,oBACA,IAAc,mCAAgC,IAC9C,YAAe,qDACf,EACA,IACA,0BACA,0BAEA,CACA,YAEA,OADA,iBACA,KAEA,YAEA,OADA,iBACA,KAGA,SACA,oBACA,CACA,CACA,QACA,iBACA,aACA,2BACA,0BACA,0BAEA,QAAU,GAAU,qBACpB,UACA,QACA,UACA,UACA,IAAY,mCAAgC,iBAC5C,mBACA,mBACA,GACA,WAAiB,GAAS,KAC1B,EACA,WAA8B,GAAS,KACvC,EACA,CACA,aAAsB,YAAiB,KAEvC,CCtDA,gBACA,SAAY,EAAM,GAAS,EAAE,GAAG,YAAkB,SAKlD,QACA,iBACA,YACA,cACA,CACA,QAAU,GAAU,EAAI,IAA+B,CAEvD,QAEA,SACA,kBALuD,CAKvD,mBACA,CACA,CACA,QACA,QAAU,GAAU,EAAI,IAAiC,CAEzD,iBACA,GACA,WACA,CACA,KANyD,CAMzD,GACA,yBACA,CACA,CACA,QACA,mBACA,aACA,eACA,sDACA,CACA,QAAU,GAAU,EAAI,IACxB,SACA,KACA,UACA,KAJgD,EAIhD,SACA,CACA,CCvCA,gBAAkC,GAAa,CAC/C,QAAU,GAAU,wBACpB,qBACA,iBAA8B,EAE9B,OADA,6BAAkC,gBAAc,EAChD,KAEA,UAGA,OAFA,wBACA,yBACA,KAEA,uBAMA,OALA,uBACA,KACA,cACA,uBACA,EACA,KAGA,sBACA,wCAAyC,YAAc,GACvD,SACA,UAA4B,EAAiB,IAE7C,EAAmB,SAF0B,EAE1B,iBADnB,IACmB,IAQnB,OANA,YACA,uBAEA,YACA,uBAEA,UACA,EAAO,MAEP,CACA,CACA,gBAA2B,GAAM,CACjC,iBACA,cACA,cAA0B,EAAa,aAEvC,WACA,YACA,CACA,QAAU,GAAU,iHChDpB,iBAAuC,GAAmB,CAC1D,QAAU,GAAU,yCACpB,OACA,aACA,4BACA,CACA,cAKA,OAJA,kBACA,+BAEA,0BACA,kBACA,CACA,CACA,gBAAgC,GAAY,CAC5C,QAAU,GAAU,sBACpB,wCACA,aACA,eACA,CACA,CACA,kBACA,QAAU,GAAU,qCACpB,GACA,iCACA,CACA,SACA,aACA,EACA,YAEA,CACA,CACA,kBACA,QAAU,GAAU,kBAEpB,kBACA,QAAU,GAAU,uCACpB,KACA,kCACA,kBACA,CAMA,aACA,oBAAwB,QAAG,6DAC3B,CACA,SACA,aACA,EACA,YAEA,CACA,CACA,kBACA,QAAU,GAAU,oBACpB,sBACA,iCAEA,KADA,+BACA,MAEA,EACA,CACA,oBACA,wBACA,+BACA,kBAEA,CACA,CACA,CACA,kBACA,QAAU,GAAU,qCACpB,KACA,mCACA,kBACA,CACA,SACA,aACA,EACA,YAEA,CACA,CACA,kBACA,QAAU,GAAU,kBACpB,sBACA,sBACA,oBACA,CACA,oBACA,YACA,CACA,CACA,gBACA,SAAU,YAAe,CAAE,OAAsB,aACjD,gDACA,gBAEA,oBACA,gBAEA,QACA,sFC3GA,iBAAgC,GAAmB,CACnD,QAAU,GAAU,kCACpB,KACA,+BACA,8BACA,2BACA,CAEA,SACA,2BACA,CACA,CACA,gBAAyB,GAAY,CACrC,QAAU,GAAU,eACpB,kCACA,sCACA,KACA,UACA,CACA,aACA,aAAkB,uBAAyB,mBAAmB,MAAQ,CACtE,CACA,CACA,gBAAoC,GAAmB,CACvD,QAAU,GAAU,sCACpB,GACA,gCACA,CAEA,SACA,aACA,EACA,YAEA,CACA,CACA,gBAA6B,GAAY,CACzC,QAAU,GAAU,kBACpB,cACA,YACA,CACA,sBACA,oBACA,CACA,oBACA,wBACA,CACA,CACA,iBAAuB,EACvB,SAAU,YAAe,CAAE,OAAsB,YACjD,gBACA,SAEA,UACA,6KCzCA,SACA,QAAU,GAAU,wBACpB,OACA,QACA,QACA,SACA,qBACA,GACA,qBACA,uBACA,uBACA,yBACA,yBAEA,YAEA,EADA,oBAeA,OAZA,EADA,YACA,YACe,QAAE,GAAS,GAAQ,EAClC,mBACA,kDAEe,QAAE,GAAS,GAAc,EACxC,EAAsB,GAAc,iBACrB,QAAE,GAAS,IAAG,EAC7B,GAEe,QAAe,IAE9B,OACA,QACA,SACA,kBACA,qBACA,qBACA,uBACA,uBACK,CACL,CACA,CACA,gBAA2C,GAAiB,CAC5D,QAAU,GAAU,6BACpB,EAEA,OACA,oBACA,UACA,gBACA,QACA,oBACA,OAAgB,uEAAsE,EACtF,QACA,aACA,WACA,QACA,QAAgB,KAAW,CAC3B,WACA,iBAEA,uBACA,eACA,eACA,QACA,gBACA,EACA,eAAqB,QAAgB,IACrC,0DAAsE,qBAAyB,EAC/F,CACA,cACA,cACA,qBACA,EAAwB,QAAgB,IACxC,+DACA,sBAAkC,EAAU,kCAE5C,2BACA,sEACA,qBACA,uBACA,EAEA,qBAA8C,QAAE,GAAQ,IAAG,IAC3D,MAA4B,QAAE,GAAQ,GAAQ,qBAA6B,QAAE,GAAQ,IAAI,IAAU,GAAc,mBAAyB,IAAK,iBAC/I,uBACA,CAcA,GAZA,sBACA,KACA,UACA,mBACA,IAAgB,GAAqB,EAAG,2CAA+C,GAEvF,EAEA,mBACA,uBAEA,2BAA+B,6BAAuC,EACtE,mBACA,UACA,WACA,+BACA,KAEA,aACA,4CACA,6DAEA,+BACA,KAEA,aACA,+BACA,KAEA,YACA,4CACA,6DAEA,8BAGA,CAEA,YAEA,CA4BA,iCA4BA,mCA4BA,mCA4BA,iCACA,uBACA,WACA,oCACA,IAAW,QAAY,iDACvB,YACA,iHAIA,OADA,oCAAsC,wBAA0B,EAChE,KAEA,CA0BA,wCA0BA,6CA0BA,iDA0BA,0CAEA,oBAEA,OADA,oCACA,KA+BA,SAUA,MATA,sBACA,KACA,UACA,mBACA,IAAc,GAAqB,EAAG,2CAA+C,GAErF,EAEA,oBACA,IACA,CAuBA,UAUA,MATA,sBACA,KACA,UACA,mBACA,IAAc,GAAqB,EAAG,2CAA+C,GAErF,EAEA,qBACA,IACA,CACA,cACA,4BACA,WACA,UACA,mBACA,IAAc,GAAqB,EAAG,6CAAiD,GAGvF,2CACA,EAAM,IACN,sBAEA,YAEA,cACA,4BACA,WACA,UACA,mBACA,IAAc,GAAqB,EAAG,6CAAiD,IAGvF,yBACA,kCACA,0CAEA,qBAEA,EAAM,IAEN,kCACA,wCAFA,EAIA,oBAJA,EAOA,WACA,CAiBA,SAMA,OALA,kCACA,wCAEA,oBAEA,KAkBA,UAMA,OALA,kCACA,yCAEA,qBAEA,KAGA,SACA,iDACA,CACA,QACA,IAAY,gBAA6B,uCACzC,QACA,CACA,MACA,iBACA,IAAU,GAAQ,qCAClB,IAAU,GAAqB,QAAG,iDAA0D,EAE5F,CAEA,oBACA,iBACA,mBACA,IAAU,GAAqB,EAAG,oEAA0E,EAE5G,CACA,WACA,YAEA,CACA,kBACA,QAAU,GAAU,iBAEpB,eACA,iBACA,kGAEA,MAAuB,QAAmB,qBAC1C,uDACA,uCACA,EACA,MACA,IAGA,OADA,+CACA,CACA,CACA,UACA,wBACA,CACA,OACA,sBACA,CACA,OACA,sBACA,CACA,OACA,sBAEA,WACA,yBACA,OACA,UACA,iBACA,CACA,CAEA,gBACA,mBACA,wBACA,OACA,QACA,aACA,EAAK,EACL,eACA,IAAW,QAAY,0DACvB,YACA,iHAIA,2BACA,CACA,CAjBA,QAAW,IAAoB,GAAY,GAkB3C,YACA,QACA,WACA,YACA,QACA,CAAC,EACD,gBACA,gBACA,oBACA,gBC7mBA,SACA,QAAU,GAAU,uBACpB,QACA,0BACA,GACA,aAAmB,QAAE,GAAU,IAAa,WAC5C,mBAAyB,QAAE,GAAU,IAAa,UAClD,CACA,SACA,WACA,OACA,OACA,sBACA,SAEA,UACA,IAAc,GAAY,wCAC1B,IAAc,GAAqB,QAAG,iDAA0D,GAGhG,CACA,CACA,WACA,WAkBA,OAAa,OAjBb,YACA,WAAiB,EAAmB,CACpC,gBADoC,CAEpC,eACA,uBACA,UACA,CAAO,CACP,EAUa,eATb,YACA,WAAiB,EAAmB,CACpC,gBADoC,CAEpC,eACA,uBACA,WACA,WACA,CAAO,CACP,CACa,CACb,CACA,UACA,WAAe,EAAmB,CAAG,gBAAH,CAAG,yCAAuE,CAC5G,CACA,kBACA,WAAe,EAAmB,CAClC,gBADkC,CAElC,eACA,0BACA,WACA,CAAK,CACL,CAEA,aAIA,OAHA,cACA,kBAAyB,IAAiB,sBAE1C,aAEA,eCrDA,SACA,qBACA,aACA,eACA,eACA,eACA,CACA,QAAU,GAAU,wBACpB,OACA,aACA,WACM,OAAY,eAClB,aACA,aACA,cAEA,CACA,CACA,gBAA+B,GAAY,CAC3C,uBACA,QACA,eACA,eACA,iBAAoB,8BACpB,CACA,QAAU,GAAU,iBAEpB,OACA,QAEA,OADA,mBACA,KAEA,cACA,cACA,MAAwB,QAAgB,IACxC,8DACA,sBAAkC,EAAU,kCAE5C,yBACA,uBAAwC,QAAE,GAAQ,IAAW,IAAU,IAAK,iBAAmB,QAAE,GAAQ,GAAQ,qBAA6B,QAAE,GAAQ,GAAc,IAAU,GAAc,+BAC9L,IACA,UACA,kBAA8B,IAAK,iBACnC,IAAgB,GAAqB,EAAG,2CAA+C,GAEvF,aACA,EACA,IAAgB,GAAqB,EAAG,2CAA+C,GAGvF,CAEA,OADA,2BAA+B,6BAAuC,EACtE,KAEA,CACA,iCACA,mCACA,mCACA,iCAkCA,SAEA,OADA,oBACA,KAEA,cACA,4BACA,WACA,UACA,kBAA4B,IAAK,iBACjC,IAAc,GAAqB,EAAG,6CAAiD,IAGvF,yBACA,qBACA,EAAM,IAEN,oBADA,EAGA,YAEA,SAEA,OADA,oBACA,KAEA,8BAAuC,IAAW,kBAElD,OADA,sBAA4B,QAAmB,IAC/C,IACA,CAEA,SACA,iDACA,CACA,QACA,IAAY,gBAA6B,uCACzC,QACA,CAEA,eACA,4DACA,uCACA,sBACA,kCACA,GAEA,CACA,UACA,wBACA,CACA,OACA,sBACA,CACA,OACA,sBACA,CACA,OACA,sBACA,CACA,UACA,yBACA,OACA,UACA,kDACA,CACA,WACA,YAEA,CCpKA,QACA,qBACA,aACA,eACA,eACA,eACA,CACA,QAAU,GAAU,wBACpB,UAEA,QADA,0BACA,OACA,+DAEA,gBACA,SACA,aAA8B,IAAK,iBACnC,6BACA,WACA,KAAyB,QAAE,GAAW,IAAG,QAAmB,IAAK,QACjE,CACA,QACA,CAAK,EACL,kEACA,CACA,UACA,iCAAuE,GAAY,EACnF,IAAS,GAD0E,EAC1E,GAAE,GAAS,IAAG,IAAM,QAAY,YAAY,GAAO,sBAC5D,YACA,sHAGA,qEACA,CACA,CACA,gBAA+B,GAAY,CAC3C,yBACA,QACA,eACA,eACA,mBAAoB,+BACpB,CACA,QAAU,GAAU,iBAEpB,OACA,8BAAuC,IAAW,kBAElD,OADA,sBAA4B,QAAmB,IAC/C,KAwBA,wBAAiC,EACjC,qBACA,uBAA+B,QAAG,iBAC5B,CACN,8BAAuD,QAAG,GAAG,SAAc,EAAI,QAAG,GAAG,WAAgB,EACrG,UAAsC,QAAG,UAAU,QAAa,EAAI,QAAG,GACvE,uBAA+B,QAAG,GAAG,GAAW,YAAY,EAAS,EAErE,YA+BA,sBACA,wCACA,YACA,+IAGA,cAAoC,QAAG,UAAU,QAAa,SAC9D,gBAAgD,QAAG,UAAU,cAAmB,SAChF,aAA0C,QAAG,UAAU,WAAgB,SACvE,0BAAqD,QAAG,GAAG,SAAc,EAAI,QAAG,GAAG,WAAgB,EACnG,gDAAkE,OAAY,2BAE9E,OADA,uBAA6B,QAAG,GAAG,EAAU,EAAE,GAAgB,gBAAgB,EAAO,EAAE,EAAS,EAAE,EAAY,EAC/G,KAGA,SACA,iDACA,CACA,QACA,IAAY,gBAA6B,uCACzC,QACA,CAEA,eACA,4DACA,uCACA,sBACA,kCACA,GAEA,CACA,UACA,wBACA,CACA,OACA,sBACA,CACA,OACA,sBACA,CACA,OACA,sBACA,CACA,UACA,yBACA,OACA,UACA,kDACA,CACA,WACA,YAEA,CClKA,gBAA+B,GAAY,CAC3C,qBACA,QACA,aACA,eACA,eACA,mBAAoB,aACpB,CACA,QAAU,GAAU,iBAEpB,OA8BA,SAEA,OADA,oBACA,IACA,CACA,cACA,4BACA,WACA,UACA,kBAA4B,IAAK,iBACjC,IAAc,GAAqB,EAAG,6CAAiD,IAGvF,yBACA,qBACA,EAAM,IAEN,oBADA,EAGA,YAEA,SAEA,OADA,oBACA,KAEA,uBAAgC,IAAW,kBAE3C,OADA,sBAA4B,QAAmB,IAC/C,IACA,CAEA,SACA,iDACA,CACA,QACA,IAAY,gBAA6B,uCACzC,QACA,CAEA,eACA,4DACA,uCACA,sBACA,kCACA,GAEA,CACA,UACA,wBACA,CACA,OACA,sBACA,CACA,OACA,sBACA,CACA,OACA,sBACA,CACA,UACA,yBACA,OACA,WACA,iCACA,CACA,WACA,YAEA,CC9GA,gBAAiC,IAAG,CACpC,eACA,4DACA,cACA,uBACA,sBACA,SACA,UAEA,CACA,WACA,CAAU,GAAU,2BACpB,gDACA,eACA,wBACA,MAAW,QAAG,yBAAyB,EAAO,EAAE,IAAG,sBAA4B,EAAE,EAAQ,GAEzF,uBACA,MAAW,QAAG,wBAAwB,EAAO,EAAE,IAAG,sBAA4B,EAAE,EAAQ,EAExF,UACA,0DACA,EACA,EAEA,CACA,SACA,0BACA,CACA,WACA,iBACA,IACA,MACA,GAEA,IAEA,MADA,MACA,CACA,EAEA,CACA,eCtCA,SACA,6BACA,YACA,kBACA,cACA,qBACA,aACA,mBACA,eACA,cACA,CACA,QAAU,GAAU,sCACpB,YACA,+BACA,gBACA,YACA,mBACA,WACA,iBACA,aACA,aACA,KAA0B,CAC1B,QACA,MACA,gBACA,YACA,mBACA,WACA,iBACA,aACA,aACA,KAA0B,CAC1B,OAEA,CACA,aACA,+BACA,gBACA,YACA,mBACA,WACA,iBACA,aACA,aACA,GAAiB,cAAsB,CAAI,QAAU,CACrD,SACA,MACA,gBACA,YACA,mBACA,WACA,iBACA,aACA,aACA,GAAiB,cAAsB,CAAI,QAAU,CACrD,QAEA,CACA,CACA,gBAAoC,GAAY,CAChD,+BACA,QACA,kBACA,cACA,qBACA,aACA,mBACA,eACA,eACA,cACA,WACA,CACA,QAAU,GAAU,+BAEpB,KAEA,SACA,0CACA,2BACA,mBACA,iCACA,iBACA,6BACA,wBACA,kCACA,CAAK,KACL,CAEA,eACA,UAAY,gBAAoB,cAChC,4DACA,EACA,OACA,gCACA,GACA,QACA,YACA,GAAmB,QAAgB,sDAEnC,oBACA,KAEA,CACA,EAEA,CACA,UACA,wBACA,CACA,SACA,yCACA,2BACA,mBACA,iCACA,iBACA,6BACA,wBACA,mCACK,EACL,iCACA,aAAa,eACb,CACA,QACA,gCAGA,mBACA,oBACA,wBAEA,uBACA,CACA,gBACA,wBACA,CACA,CACA,kBACA,QAAU,GAAU,8BACpB,OACA,wBACA,CACA,CChJA,gBAAwB,GAAY,CACpC,uBACA,QACA,eACA,cACA,eACA,sBACA,oBAAoB,EACpB,CACA,QAAU,GAAU,cAEpB,OACA,WACA,OAAa,oEACb,CACA,eACA,iCACA,CACA,WACA,YAGA,wBACA,QACA,CACA,CCbA,QACA,qBACA,kBACA,eACA,eACA,UACA,gBACA,wBACA,+BACM,CACN,cACA,aAAoB,CACpB,gBACA,EACA,cACA,iBACA,iBACA,6CACA,SAA+B,EAC/B,EACA,aACA,KAHqD,CAGrD,QACA,qBACA,gBACA,EACA,EACA,EAIA,CACA,QAAU,GAAU,uBACpB,KAiCA,UACA,WACA,OACA,OACA,sBACA,SAAsB,EAAY,aAElC,UACA,IAAc,GAAY,wCAC1B,IAAc,GAAqB,QAAG,iDAA0D,GAGhG,CACA,CACA,YACA,WAAe,EAAkB,CAAG,eAAH,EAAG,uBAAwC,CAC5E,CAoBA,WACA,WA2BA,OAAa,OA1Bb,YACA,WAAiB,EAAmB,CACpC,gBADoC,CAEpC,kBACA,kBACA,UACA,CAAO,CACP,EAmBa,eAlBb,YACA,WAAiB,EAAmB,CACpC,gBADoC,CAEpC,kBACA,kBACA,WACA,WACA,CAAO,CACP,EAUa,OATb,YACA,WAAiB,EAAmB,wBACpC,EAOa,OANb,YACA,WAAiB,EAAmB,wBACpC,EAIa,OAHb,YACA,WAAiB,EAAgB,wBACjC,CACa,CACb,CACA,UACA,WAAe,EAAmB,CAAG,gBAAH,CAAG,0CAAwE,CAC7G,CACA,kBACA,WAAe,EAAmB,CAClC,gBADkC,CAElC,qBACA,qBACA,WACA,CAAK,CACL,CA4BA,UACA,WAAe,EAAmB,4BAClC,CAyBA,UACA,WAAe,EAAmB,4BAClC,CAyBA,UACA,WAAe,EAAgB,4BAC/B,CACA,OACA,yBAA+C,IAAG,yBAClD,0BACA,IAAiB,EACjB,OAD0B,EAC1B,oBACA,MACA,MACA,aACA,mEAGA,mBACA,CACA,OACA,yBAA+C,IAAG,yBAClD,0BACA,IAAiB,EACjB,OAD0B,EAC1B,oBACA,MACA,MACA,aACA,mEAGA,mBACA,CACA,OACA,yBAA+C,IAAG,yBAClD,0BACA,IAAiB,EACjB,OAD0B,EAC1B,oBACA,MACA,MACA,aACA,mEAGA,mBACA,CACA,UACA,yBAA+C,IAAG,yBAClD,0BACA,IAAiB,EACjB,OAD0B,EAC1B,uBACA,MACA,SACA,aACA,sEAGA,sBACA,CACA,iBACA,oCACA,CACA,2OClRA,SACA,QAAU,GAAU,kBAEpB,mBACA,GACA,gBAAsB,IAAW,WACjC,CACA,cACA,UAAe,EAAK,EACpB,CACA,eACA,SACA,CACA,gBACA,UAAe,qBAAwB,GAEvC,gBACA,cACA,OACA,OAA2B,QAAG,SAC9B,2BACA,OAAyB,QAAG,GAAG,IAAG,wBAAwB,MAAM,QAAQ,IACxE,cACA,OAA2B,QAAG,MAI9B,OADA,OAAuB,QAAG,KACf,IAAG,QACd,CACA,wBAAqB,mDAAmD,EACxE,2BACA,IAAqC,QAAG,cAAc,uBAAiC,iBAAqB,EAAE,SAC9G,IAA6B,QAAG,UAAU,EAAM,SAChD,uBACA,qBACA,MAAW,QAAG,GAAG,EAAQ,cAAc,EAAM,EAAE,EAAS,EAAE,EAAa,EAAE,EAAW,EAAE,EAAS,EAE/F,oBACA,QAA+B,IAAK,iBACpC,wBACA,6CAEA,WACA,OAAW,IAAG,wBACd,WACA,QAAoC,IAAG,yBACvC,EAAkB,QAAG,GAAG,IAAG,6CAA+C,IAAI,EAAM,SACpF,MACA,GAAqB,IAAG,YAExB,IACK,EACL,CACA,wBAAqB,wEAAqE,EAC1F,2BACA,2BACA,KAA4B,IAAG,OAAO,IAAG,wCACzC,qBACA,IAAqC,QAAG,cAAc,uBAAiC,iBAAqB,EAAE,SAC9G,IAA6B,QAAG,UAAU,EAAM,SAChD,uBACA,qBACA,MAAW,QAAG,GAAG,EAAQ,SAAS,GAAO,MAAM,EAAO,EAAE,EAAQ,EAAE,EAAS,EAAE,EAAS,EAAE,EAAa,EAAE,EAAW,EAAE,EAAS,EAa7H,gCAA2B,MAAwB,EAAI,EACvD,eACA,oBAAqC,EAAO,MAC5C,SACA,GAAU,QAAE,GAAQ,IAAG,8BACvB,OAAmB,IAAG,gCACd,GAAS,QAAE,GAAQ,IAAG,WAAa,QAAE,GAAQ,IAAG,GACxD,MAAsB,QAAE,GAAQ,IAAG,kBACnC,EACA,OACA,IAAgB,IAAG,CACnB,qBACA,CAAoB,OAAE,GAAI,GAAM,EACP,IAAG,4CAE5B,KAKA,UAEY,QAAE,GAAQ,IAAG,WACzB,OAAqB,QAAG,OAAO,IAAG,0BAA8B,EAEhE,EAAQ,OAAS,QAAE,GAAQ,GAAM,GACjC,cAAsC,IAAK,cAC3C,EACA,OAAqB,IAAG,6CAExB,OAAqB,QAAG,GAAG,IAAG,eAAuB,GAAG,IAAG,4CAAgD,EAE3G,CAIA,OAHA,OACA,OAAmB,QAAG,MAEtB,CACA,CAAK,EACL,OAAW,IAAG,QACd,CACA,cACA,oBACA,OAEA,SACA,KACA,4BACA,OACA,OAA0B,QAAG,KAE7B,cACA,GAAY,QAAE,GAAQ,IAAW,GACjC,QAAkC,IAAW,cAC7C,IAAoC,IAAW,gBAC/C,IAAsC,IAAW,sBACjD,uBACA,OACY,QAAG,GAAG,IAAG,kBAAyB,OAAO,EAAc,QAAG,GAAG,IAAG,eAAyB,UAAY,EAAE,IAAG,eAA2B,EAAE,GAAS,QAAG,IAAI,IAAG,eAAmB,GAAG,KAAK,KAAY,EAE7M,EAAU,IACV,OACY,QAAG,GAAG,IAAG,kBAAyB,OAAO,GAAO,KAAK,KAAY,GAG7E,cACA,OAA0B,QAAG,IAE7B,CAEA,OAAW,IAAG,QACd,CACA,cACA,mDAAkF,QAAG,UAAU,EAAM,QACrG,CACA,gBACA,SACA,KACA,2BACA,UACA,cACA,OAA2B,QAAG,MAI9B,kBAAoC,QAAG,aAAa,IAAG,SAAmB,QAC1E,CACA,wBACA,CAAQ,OAAE,GAAQ,IAAK,KAAW,IAAK,0BAAgC,IAAK,cAC/D,QAAG,GAAG,IAAG,cAAkB,IAAK,wBAAwB,EAAE,IAAG,cAAkB,IAAK,eAAe,EAEhH,CACA,CACA,kBACA,WACA,SACA,aACA,QACA,SACA,QACA,QACA,UACA,UACA,QACA,SACA,WACA,eACG,EACH,SAAqC,QAAmB,IACxD,oBACuM,EAAvM,GAAU,QAAE,SAAU,GAAM,GAAK,QAAY,mBAAqB,QAAE,GAAQ,GAAQ,YAAoB,QAAE,GAAQ,GAAc,IAAU,GAAc,OAAS,QAAE,GAAQ,IAAG,SAAa,QAAY,SAEvM,eAFuM,QACvM,EAAW,QAAO,UAAuB,IAAK,iBAAmB,KAAY,KAAkB,IAAK,qBACpG,CACA,MAA0B,QAAY,eACtC,aACA,SAAmB,kBAAkB,+BAA+B,EAAU,KAAK,aAAa,oBAAoB,EAAU,wDAE9H,CACA,CACA,uBACA,uBACA,IAAmC,QAAG,mBACtC,uCAAwD,EAAe,EACvE,yBACA,qBACA,IAA6B,QAAG,UAAU,EAAM,SAChD,IAA+B,QAAG,WAAW,EAAO,SACpD,KACA,KACA,2BACA,UACA,cACA,OAA2B,QAAG,MAI9B,iBAAgD,QAAG,aAAa,IAAG,SAAmB,SACtF,uBACA,qBACA,IAA+B,QAAG,WAAW,EAAO,SACpD,EAAuB,QAAG,GAAG,EAAQ,QAAQ,GAAa,EAAE,GAAW,OAAO,EAAS,EAAE,EAAS,EAAE,EAAS,EAAE,EAAW,EAAE,EAAU,EAAE,EAAW,EAAE,EAAS,EAAE,EAAU,SAC1K,WACA,6BAEA,CACA,CACA,wBACA,cACA,MACA,uEAEA,aACA,wCAA2C,gBAAyB,EAEpE,wBACA,6BAAoC,2BAAyB,EAC7D,EAEA,CACA,wBACA,aACA,kBAAmB,oDAChB,MAGH,EAFA,MAAsB,QAAG,GAAG,YAAqB,EACjD,EAAuB,QAAG,GAAG,WAAqB,EAElD,kBACA,SACA,eACA,GAAY,QAAE,GAAgB,GAAY,EAC1C,OAA6B,IAAG,0BACtB,GAAS,QAAE,GAAgB,IAAG,GACxC,YAA0B,uBAAsC,KAChE,uBACgB,QAAE,GAAQ,GAAY,GACtC,kBAA6C,IAAG,4CAEhD,CACA,OAA6B,QAAG,GAAG,EAAc,EACjD,EAAU,IACV,OAA6B,QAAG,GAAG,EAAc,GAGjD,EAAmB,QAAG,aAAa,IAAG,QAAqB,QAAG,MAAM,EAEpE,mDAA4F,QAAG,UAAU,EAAM,SAC/G,EAA0B,IAAG,QAAQ,GAAM,EAAE,YAAoB,GACjE,IAA+B,QAAG,WAAW,EAAO,SACpD,MAAW,QAAG,GAAG,EAAU,EAAE,EAAc,EAAE,EAAW,EAAE,EAAW,EAAE,EAAS,EAAE,EAAU,EAE5F,kBAAqB,8DAAwE,EAC7F,SAEA,iBADA,EAA0B,IAAK,kBAC/B,OACA,mCAEA,gBAAuD,IAAG,6CAC1D,KAEU,QAAE,CADZ,EACsB,IAAG,EACzB,OAFA,GAIA,wBAKA,eADA,OAAyB,IAAG,iBAD5B,EAEA,YACA,SACA,kBACA,WACA,eAAqC,QAAE,GAAW,IAAK,qBACvD,MACA,wCACA,EAA6B,QAAE,WAAc,IAAG,YAAkB,IAAG,yBACvD,yBACd,oBACA,EAA6B,QAAE,GAAkB,IAAG,IAAsB,IAAG,WAC7E,EAAc,wCAId,EAA6B,QAAG,WAJlB,CACd,qBACA,EAA6B,QAAE,GAAmB,IAAG,IAAuB,IAAG,WAC/E,CAGA,CAHc,CAGd,OACA,EAAY,IACZ,SAEA,CACA,UACA,cACA,OAA6B,QAAG,KAEhC,CAEA,2BACA,EAAsB,IAAG,SACzB,IAAqC,QAAG,cAAc,uBAAiC,iBAAqB,EAAE,SAC9G,IAAuC,QAAG,gBAAgB,EAAW,SACrE,MAAW,QAAG,GAAG,EAAQ,cAAc,GAAO,EAAE,GAAa,EAAE,EAAU,EAAE,EAAc,EAAE,EAAa,CACxG,CACA,gBACA,kBACA,mBACA,2BACA,6BACA,+BACA,cACA,CAAK,CACL,CACA,sBACA,aACA,SACA,gBACA,QACA,cACA,cACA,aACA,sBACA,SACG,EACH,IA8HA,EA9HA,KACA,WACA,KACA,UAEA,EADA,0BACA,eACA,aACA,QACA,MAAe,QAAkB,MACjC,0BACA,UACA,aACA,CAAO,MACD,CACN,yBACA,0CAAwE,QAAkB,SAE1F,YACA,2CAA2F,QAAY,YACvG,KAA4B,QAAsB,KAClD,CACA,SACA,KACA,cACA,SACA,yCACA,YAGA,iBACA,WACA,OAEA,UAGA,aACA,uFAEA,EAAQ,IACR,yBAEA,gBACA,mBACA,QAA+B,gBAA6B,CAC5D,CACA,SAKA,GAJA,QACA,iEAAuH,yCAA4D,GAGnL,SAEA,8BADA,wCAAuF,GAAG,MAAE,YAE5F,QACA,QACA,MAAmB,QAA6B,KAChD,CAAW,EAGX,cAAmB,WAAe,IAClC,QACA,MAAiB,QAAE,GAAQ,IAAG,yCAC9B,QACA,MAAiB,QAAE,GAAQ,GAAM,EAAI,QAAkB,QACvD,0BACA,UACA,aACS,EAET,+CAA8F,QAAmB,kBAYjH,QACA,QACA,cACA,WACA,CAAQ,EAfR,kBACA,QAEA,WACA,CAAY,OAAE,GAAe,GAAM,EAClB,QAAkB,MAEpB,QAAsB,OAErC,UACA,WAKQ,IACR,MAAmC,QAAiB,QAEpD,IADkC,QAAkB,oBACpD,CACA,KAAsC,EAAW,GAAG,EAAsB,EAC1E,EAAwB,QAAG,IAC3B,aACA,OAA2B,QAAE,CACf,QAAkB,oBAClB,QAAkB,SAIhC,6BACA,aACA,SACA,gBACA,WACA,iBACA,YAAuB,QAAE,GAAW,IAAG,UAA6C,SAAW,CAAI,cAA2C,EAC9I,aACA,SACA,qBACA,CAAS,EACT,EAAsB,QAAG,IAAI,MAAkB,SAC/C,QACA,QACA,QACA,QACA,qBACA,UACA,sBACS,CACT,CACA,CACA,gBACA,UAAgB,GAAY,EAC5B,yCAAkD,SAAmB,MAAM,EAAW,4JAC/E,EAIP,GADA,EAAY,QAAG,MACf,GACA,MAAkB,QAAG,cAAc,IAAG,MACtC,MACA,EAAa,QAAe,GAAK,QAAE,GAAS,GAAY,EAAI,IAAG,4CAAmD,QAAE,GAAS,IAAG,mBAExH,QAAG,MACH,GACE,QAAE,GAAsB,IAAI,GACtC,GAAgB,QAAG,6BAA6B,EAAM,mBAEtD,QACA,aACA,aACA,mBACA,UACA,4BACA,WACA,CAAO,CACP,qCAEA,yBACA,MAAiB,QAAY,MAC7B,SAAoB,CACpB,YACA,CACA,QACA,MAAqB,IAAG,SACxB,EACA,CACA,QACA,QACA,SACA,UACA,gBACS,EACT,SACA,SACA,SACA,UAEA,EAAiB,QAAY,MAE7B,yBACA,MAAe,QAAE,GAAS,IAAW,QAAiB,GAAQ,KAAW,IACzE,SAAkB,CAClB,mBAA2C,QAAe,KAC1D,QACA,MAAiB,QAAE,GAAS,GAAM,EAAI,QAAkB,OACxD,EAAS,EACT,QACA,QACA,QACA,SACA,UACA,gBACO,CACP,EAAM,IACN,yBACA,MAAe,QAAY,MAC3B,SAAkB,CAClB,yBAAqC,EAAO,KAC5C,QACA,MAAiB,QAAE,GAAQ,GAAM,EAAI,QAAkB,OACvD,EAAS,EACT,QACA,QACA,QACA,SACA,UACA,gBACO,EAEP,OACA,oBACA,MACA,WACA,CACA,CACA,CACA,kBACA,QAAU,GAAU,sBACpB,eACA,4HACA,EAAiC,QAAG;8BACpC,EAAgC,IAAG,gBAA8B;;;;;EAKjE,EACA,SAIA,QAHA,OACM,QAAG,oCAAoC,IAAG,gBAA8B,kCAE9E,YACA,MAAgB,QAAG,SACnB,IACA,eACA,oCACA,mBACA,MAAwB,IAAG,SAE3B,MACY,QAAG,eAAe,IAAG,gBAA8B,gCAAgC,OAAe,IAAI,eAAuB,GAEzI,CAEA,MAAkB,QAAG,SACrB,CAAM,SAEN,MADA,MAAkB,QAAG,YACrB,CACA,CACA,CACA,CACA,kBACA,QAAU,GAAU,6BACpB,eACA,4HACA,EAAiC,QAAG;8BACpC,EAAgC,IAAG,gBAA8B;;;;;EAKjE,QACA,SAIA,MAHA,gBACM,QAAG,oCAAoC,IAAG,gBAA8B,kCAC9E,CACA,WACA,+BACA,eACA,oCACA,mBACA,YAAyB,IAAG,QAE5B,aACY,QAAG,eAAe,IAAG,gBAA8B,gCAAgC,OAAe,IAAI,eAAuB,GAEzI,CAEA,CAAK,CACL,CACA,yEClnBA,SACA,iBACA,YACA,aACA,CACA,QAAU,GAAU,yBACpB,SACA,qCACA,CACA,CACA,QACA,QAAU,GAAU,sBAEpB,oBACA,OACA,aACA,OACA,UACA,SACA,YACA,CACA,CAIA,SAEA,OADA,oBACA,KAGA,SACA,2BACA,CACA,CACA,QACA,QAAU,GAAU,gBACpB,mBACA,KACA,aAAoB,aACpB,CACA,CACA,cACA,kBACA,CACA,cACA,kBACA,4EC7CA,wBACA,aACA,8BAEA,QACA,CACA,QACA,QAAU,GAAU,4BAEpB,QAEA,iBACA,KACA,eACA,WACA,CAEA,SACA,sCACA,CACA,CACA,QACA,mBACA,aACA,eACA,WACA,CACA,QAAU,GAAU,qBACpB,QACA,KACA,UACA,qBAA2B,WAAW,IAAW,cAAc,GAAG,sCAAoD,KAEtH,2HChCA,iBAAkC,GAAmB,CACrD,QAAU,GAAU,oCACpB,GACA,gCACA,CAEA,SACA,2BACA,CACA,CACA,gBAA2B,GAAY,CACvC,QAAU,GAAU,iBACpB,aACA,YACA,CACA,sBACA,cAAkB,EAAM,oDACxB,CACA,oBACA,OAAW,EAAM,kBACjB,CACA,CACA,gBAAoC,GAAmB,CACvD,QAAU,GAAU,sCACpB,GACA,gCACA,CAEA,SACA,aACA,EACA,YAEA,CACA,CACA,gBAA6B,GAAY,CACzC,QAAU,GAAU,mBACpB,aACA,YACA,CACA,sBACA,kBAAsB,EAAM,oDAC5B,CACA,oBACA,OAAW,EAAM,uBACjB,CACA,CACA,gBAAsC,GAAmB,CACzD,QAAU,GAAU,wCACpB,GACA,oCACA,CAEA,SACA,2BACA,CACA,CACA,gBAA+B,GAAY,CAC3C,QAAU,GAAU,qBACpB,aACA,YACA,CACA,CACA,gBACA,SAAU,YAAiB,QAAsB,aACjD,iBACA,SAEA,mBACA,SAEA,QACA,CCxEA,gBAAwC,GAAmB,CAC3D,QAAU,GAAU,6BACpB,oBACA,uCACA,0BACA,8BACA,CAEA,SACA,aACA,EACA,YAEA,CACA,CACA,gBAAiC,GAAY,CAC7C,QAAU,GAAU,uBACpB,QACA,MACA,oBACA,KACA,WACA,wDACA,uCACA,2CAEA,aACA,oBAEA,sBACA,uDACA,CACA,oBACA,mDACA,CACA,CACA,cACA,cACA,IAAY,iBAAe,CAAE,OAAsB,MACnD,aACA,EACA,EACA,EAEA,CACA,eC9CA,iBAAmC,GAAmB,CACtD,QAAU,GAAU,qCACpB,GACA,iCACA,CAEA,SACA,aACA,EACA,YAEA,CACA,CACA,gBAA4B,GAAY,CACxC,QAAU,GAAU,kBACpB,aACA,eACA,CACA,CACA,cACA,mBACA,CCrBA,gBAAgC,GAAmB,CACnD,QAAU,GAAU,kCACpB,GACA,8BACA,CAEA,SACA,2BACA,CACA,CACA,gBAAyB,GAAY,CACrC,QAAU,GAAU,eACpB,aACA,YACA,CACA,CACA,cACA,mBACA,gBEjBA,mDACA,iBAA0B,IAAK,CAC/B,QAAU,GAAU,uBAEpB,uBAAkC,CAAE,IAAK,SACzC,mBACA,CAAG,GAEA,IAAK,kBAER,OAEG,IAAK,mCAsBR,eACA,CArBA,sBACA,cADA,OACA,GAEA,qBACA,eAFA,uBDZA,CACA,IAAQ,CCWwD,EDVhE,UAAc,GACd,OAAW,MACX,OAAW,GACX,IAAQ,GACR,IAAQ,KACR,GCKuF,GAEvF,cACA,EACA,WACA,iBAEA,OADA,aAHA,EAGA,uBACA,MACK,GAEL,qBAMA,OALA,EAAQ,IAAK,mBACb,EAAQ,IAAK,8BACb,GACA,mCAEA,EACA,EAEA,+ECpCA,iBAA6B,IAAI,CACjC,QAAU,GAAU,yFCFpB,SACA,QAAU,GAAU,yBACpB,YACA,QACA,iBACA,MACA,iBACA,QACA,QACA,CACA,CAIA,CACA,kBACA,QAAU,GAAU,+HCfpB,mCACA,gCACA,2CACA,qCACA,iCACA,gCACA,2CACA,sCACA,SACA,QAAU,GAAU,iBAEpB,QACA,KAAU,GAAS,CACnB,SACA,eACA,UACA,qBACA,WACA,UACA,oBACA,GAKG,GAAS,CAKZ,MAEA,IAEA,IAEA,IAKA,IAEA,OAEA,OAEA,sBACA,OACA,KAAS,GAAS,YAClB,UACA,SACA,CACA,CAIA,cACA,SAAe,GAAS,EAExB,cACA,SAAY,eAA0B,GAAG,EAAM,GAAS,EAAE,kDC/D1D,+DCAA,mBACA,cACA,oDEAA,EACA,kCACA,OACA,uBACA,GAGA,GACA,mCDTA,CCSsD,QAAU,EAErD,OAAI,CACf,yBACA,EACA,IACA,IACA,WACA,CAAY,SAMZ,MALA,aACA,4BACA,oDAEA,CAAa,EACb,CACA,EAAY,OACZ,OACA,CACA,GAEA,EACA,IAxBA,GA2BA,uDCiBA,gBACA,4CACA,sBACA,SAEA,qBAQA,MAPQ,QAAE,GAAQ,GAAM,GAAK,QAAE,GAAQ,IAAG,GAAK,QAAE,GAAQ,IAAG,UAC5D,QAAoB,eAAsB,EAC3B,QAAE,GAAQ,IAAK,EAC9B,cAA+C,IAAK,qBAEpD,kBAEA,CACA,CAAG,IACH,+HA5DA,kBACA,SACA,WACA,SAAgB,UAAa,UAC7B,EAEA,EADU,QAAE,GAAQ,GAAM,EAC1B,EACiB,QAAE,GAAQ,IAAG,EAC9B,UAEA,cAEA,QACA,2BACA,gBACA,QACA,UAEA,WACU,CACV,WACA,6CACA,MAAqC,QAAE,GAAQ,GAAM,iBACrD,WACA,OAEc,8BAAkF,QAAY,WAC5G,UAFA,eAAwD,QAAY,SAIpE,CACA,CAEA,QACA,CAAK,CACL,IAEA,8BACA,iCACA,0BACA,YAIA,QACA,CAiBA,gBACA,qBACA,iBACA,uBACA,SAEA,2BACA,YACA,SAGA,QACA,CACA,gBACA,gEACA,CAAQ,OAAE,GAAQ,IAAG,GAAK,QAAE,GAAQ,GAAM,EAC1C,MAEA,OAAuB,IAAK,KAAc,IAAK,uBAG/C,gBACA,gCAEA,4BACA,CACA,gBACA,eACA,qDACA,mBAEA,sBACA,YACA,EACA,oEAIA,CACA,cACA,SAAe,IAAK,iBAEpB,cACA,MAAS,QAAE,GAAQ,GAAQ,YAAoB,QAAE,GAAQ,IAAI,IAAU,GAAc,OAAS,QAAE,GAAQ,IAAG,WAAmB,IAAK,mBAAyB,IAAK,gBAAsB,IAAK,kBAE5L,gBACA,OACA,yCACA,6BACA,CACA,iDCrHA", "sources": ["webpack://_N_E/./node_modules/@cloudflare/next-on-pages/dist/api/index.js", "webpack://_N_E/./node_modules/next/dist/compiled/server-only/empty.js", "webpack://_N_E/./node_modules/drizzle-orm/alias.js", "webpack://_N_E/./node_modules/drizzle-orm/casing.js", "webpack://_N_E/./node_modules/drizzle-orm/column-builder.js", "webpack://_N_E/./node_modules/drizzle-orm/column.js", "webpack://_N_E/./node_modules/drizzle-orm/logger.js", "webpack://_N_E/./node_modules/drizzle-orm/sqlite-core/session.js", "webpack://_N_E/./node_modules/drizzle-orm/d1/session.js", "webpack://_N_E/./node_modules/drizzle-orm/d1/driver.js", "webpack://_N_E/./node_modules/drizzle-orm/entity.js", "webpack://_N_E/./node_modules/drizzle-orm/errors.js", "webpack://_N_E/./node_modules/drizzle-orm/pg-core/columns/boolean.js", "webpack://_N_E/./node_modules/drizzle-orm/pg-core/foreign-keys.js", "webpack://_N_E/./node_modules/drizzle-orm/pg-core/unique-constraint.js", "webpack://_N_E/./node_modules/drizzle-orm/pg-core/utils/array.js", "webpack://_N_E/./node_modules/drizzle-orm/pg-core/columns/common.js", "webpack://_N_E/./node_modules/drizzle-orm/pg-core/columns/date.common.js", "webpack://_N_E/./node_modules/drizzle-orm/pg-core/columns/date.js", "webpack://_N_E/./node_modules/drizzle-orm/pg-core/columns/int.common.js", "webpack://_N_E/./node_modules/drizzle-orm/pg-core/columns/integer.js", "webpack://_N_E/./node_modules/drizzle-orm/pg-core/columns/json.js", "webpack://_N_E/./node_modules/drizzle-orm/pg-core/columns/jsonb.js", "webpack://_N_E/./node_modules/drizzle-orm/pg-core/columns/numeric.js", "webpack://_N_E/./node_modules/drizzle-orm/pg-core/columns/text.js", "webpack://_N_E/./node_modules/drizzle-orm/pg-core/columns/time.js", "webpack://_N_E/./node_modules/drizzle-orm/pg-core/columns/timestamp.js", "webpack://_N_E/./node_modules/drizzle-orm/pg-core/columns/uuid.js", "webpack://_N_E/./node_modules/drizzle-orm/pg-core/primary-keys.js", "webpack://_N_E/./node_modules/drizzle-orm/pg-core/columns/bigint.js", "webpack://_N_E/./node_modules/drizzle-orm/pg-core/columns/bigserial.js", "webpack://_N_E/./node_modules/drizzle-orm/pg-core/columns/char.js", "webpack://_N_E/./node_modules/drizzle-orm/pg-core/columns/cidr.js", "webpack://_N_E/./node_modules/drizzle-orm/pg-core/columns/custom.js", "webpack://_N_E/./node_modules/drizzle-orm/pg-core/columns/double-precision.js", "webpack://_N_E/./node_modules/drizzle-orm/pg-core/columns/inet.js", "webpack://_N_E/./node_modules/drizzle-orm/pg-core/columns/interval.js", "webpack://_N_E/./node_modules/drizzle-orm/pg-core/columns/line.js", "webpack://_N_E/./node_modules/drizzle-orm/pg-core/columns/macaddr.js", "webpack://_N_E/./node_modules/drizzle-orm/pg-core/columns/macaddr8.js", "webpack://_N_E/./node_modules/drizzle-orm/pg-core/columns/point.js", "webpack://_N_E/./node_modules/drizzle-orm/pg-core/columns/postgis_extension/utils.js", "webpack://_N_E/./node_modules/drizzle-orm/pg-core/columns/postgis_extension/geometry.js", "webpack://_N_E/./node_modules/drizzle-orm/pg-core/columns/real.js", "webpack://_N_E/./node_modules/drizzle-orm/pg-core/columns/serial.js", "webpack://_N_E/./node_modules/drizzle-orm/pg-core/columns/smallint.js", "webpack://_N_E/./node_modules/drizzle-orm/pg-core/columns/smallserial.js", "webpack://_N_E/./node_modules/drizzle-orm/pg-core/columns/varchar.js", "webpack://_N_E/./node_modules/drizzle-orm/pg-core/columns/vector_extension/bit.js", "webpack://_N_E/./node_modules/drizzle-orm/pg-core/columns/vector_extension/halfvec.js", "webpack://_N_E/./node_modules/drizzle-orm/pg-core/columns/vector_extension/sparsevec.js", "webpack://_N_E/./node_modules/drizzle-orm/pg-core/columns/vector_extension/vector.js", "webpack://_N_E/./node_modules/drizzle-orm/pg-core/columns/all.js", "webpack://_N_E/./node_modules/drizzle-orm/pg-core/table.js", "webpack://_N_E/./node_modules/drizzle-orm/query-builders/query-builder.js", "webpack://_N_E/./node_modules/drizzle-orm/query-promise.js", "webpack://_N_E/./node_modules/drizzle-orm/relations.js", "webpack://_N_E/./node_modules/drizzle-orm/selection-proxy.js", "webpack://_N_E/./node_modules/drizzle-orm/sql/expressions/conditions.js", "webpack://_N_E/./node_modules/drizzle-orm/sql/expressions/select.js", "webpack://_N_E/./node_modules/drizzle-orm/pg-core/columns/enum.js", "webpack://_N_E/./node_modules/drizzle-orm/sql/sql.js", "webpack://_N_E/./node_modules/drizzle-orm/sqlite-core/foreign-keys.js", "webpack://_N_E/./node_modules/drizzle-orm/sqlite-core/unique-constraint.js", "webpack://_N_E/./node_modules/drizzle-orm/sqlite-core/columns/common.js", "webpack://_N_E/./node_modules/drizzle-orm/sqlite-core/columns/integer.js", "webpack://_N_E/./node_modules/drizzle-orm/sqlite-core/columns/text.js", "webpack://_N_E/./node_modules/drizzle-orm/sqlite-core/query-builders/select.js", "webpack://_N_E/./node_modules/drizzle-orm/sqlite-core/query-builders/query-builder.js", "webpack://_N_E/./node_modules/drizzle-orm/sqlite-core/query-builders/update.js", "webpack://_N_E/./node_modules/drizzle-orm/sqlite-core/query-builders/insert.js", "webpack://_N_E/./node_modules/drizzle-orm/sqlite-core/query-builders/delete.js", "webpack://_N_E/./node_modules/drizzle-orm/sqlite-core/query-builders/count.js", "webpack://_N_E/./node_modules/drizzle-orm/sqlite-core/query-builders/query.js", "webpack://_N_E/./node_modules/drizzle-orm/sqlite-core/query-builders/raw.js", "webpack://_N_E/./node_modules/drizzle-orm/sqlite-core/db.js", "webpack://_N_E/./node_modules/drizzle-orm/sqlite-core/dialect.js", "webpack://_N_E/./node_modules/drizzle-orm/sqlite-core/indexes.js", "webpack://_N_E/./node_modules/drizzle-orm/sqlite-core/primary-keys.js", "webpack://_N_E/./node_modules/drizzle-orm/sqlite-core/columns/blob.js", "webpack://_N_E/./node_modules/drizzle-orm/sqlite-core/columns/custom.js", "webpack://_N_E/./node_modules/drizzle-orm/sqlite-core/columns/numeric.js", "webpack://_N_E/./node_modules/drizzle-orm/sqlite-core/columns/real.js", "webpack://_N_E/./node_modules/drizzle-orm/sqlite-core/columns/all.js", "webpack://_N_E/./node_modules/drizzle-orm/sqlite-core/table.js", "webpack://_N_E/./node_modules/drizzle-orm/sqlite-core/view-base.js", "webpack://_N_E/./node_modules/drizzle-orm/subquery.js", "webpack://_N_E/./node_modules/drizzle-orm/table.js", "webpack://_N_E/./node_modules/drizzle-orm/table.utils.js", "webpack://_N_E/./node_modules/drizzle-orm/tracing-utils.js", "webpack://_N_E/./node_modules/drizzle-orm/version.js", "webpack://_N_E/./node_modules/drizzle-orm/tracing.js", "webpack://_N_E/./node_modules/drizzle-orm/utils.js", "webpack://_N_E/./node_modules/drizzle-orm/view-common.js"], "sourcesContent": ["\"use strict\";\nvar __create = Object.create;\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __getProtoOf = Object.getPrototypeOf;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __commonJS = (cb, mod) => function __require() {\n  return mod || (0, cb[__getOwnPropNames(cb)[0]])((mod = { exports: {} }).exports, mod), mod.exports;\n};\nvar __export = (target, all) => {\n  for (var name in all)\n    __defProp(target, name, { get: all[name], enumerable: true });\n};\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(\n  isNodeMode || !mod || !mod.__esModule ? __defProp(target, \"default\", { value: mod, enumerable: true }) : target,\n  mod\n));\nvar __toCommonJS = (mod) => __copyProps(__defProp({}, \"__esModule\", { value: true }), mod);\n\n// ../../node_modules/dedent-tabs/dist/dedent-tabs.js\nvar require_dedent_tabs = __commonJS({\n  \"../../node_modules/dedent-tabs/dist/dedent-tabs.js\"(exports) {\n    \"use strict\";\n    Object.defineProperty(exports, \"__esModule\", { value: true }), exports[\"default\"] = dedent2;\n    function dedent2(a) {\n      for (var b = \"string\" == typeof a ? [a] : a.raw, c = \"\", d = 0; d < b.length; d++)\n        if (c += b[d].replace(/\\\\\\n[ \\t]*/g, \"\").replace(/\\\\`/g, \"`\").replace(/\\\\\\$/g, \"$\").replace(/\\\\\\{/g, \"{\"), d < (1 >= arguments.length ? 0 : arguments.length - 1)) {\n          var e = c.substring(c.lastIndexOf(\"\\n\") + 1), f = e.match(/^(\\s*)\\S?/);\n          c += ((1 > d + 1 || arguments.length <= d + 1 ? void 0 : arguments[d + 1]) + \"\").replace(/\\n/g, \"\\n\" + f[1]);\n        }\n      var g = c.split(\"\\n\"), h = null;\n      if (g.forEach(function(a2) {\n        var b2 = Math.min, c2 = a2.match(/^(\\s+)\\S+/);\n        if (c2) {\n          var d2 = c2[1].length;\n          h = h ? b2(h, d2) : d2;\n        }\n      }), null !== h) {\n        var j = h;\n        c = g.map(function(a2) {\n          return \" \" === a2[0] || \"\t\" === a2[0] ? a2.slice(j) : a2;\n        }).join(\"\\n\");\n      }\n      return c.trim().replace(/\\\\n/g, \"\\n\");\n    }\n  }\n});\n\n// src/api/index.ts\nvar api_exports = {};\n__export(api_exports, {\n  getOptionalRequestContext: () => getOptionalRequestContext,\n  getRequestContext: () => getRequestContext\n});\nmodule.exports = __toCommonJS(api_exports);\n\n// src/api/getRequestContext.ts\nvar import_server_only = require(\"server-only\");\nvar import_dedent_tabs = __toESM(require_dedent_tabs());\nvar cloudflareRequestContextSymbol = Symbol.for(\n  \"__cloudflare-request-context__\"\n);\nfunction getOptionalRequestContext() {\n  const cloudflareRequestContext = globalThis[cloudflareRequestContextSymbol];\n  if (inferRuntime() === \"nodejs\") {\n    throw new Error(import_dedent_tabs.default`\n\t\t\t\\`getRequestContext\\` and \\`getOptionalRequestContext\\` can only be run\n\t\t\tinside the edge runtime, so please make sure to have included\n\t\t\t\\`export const runtime = 'edge'\\` in all the routes using such functions\n\t\t\t(regardless of whether they are used directly or indirectly through imports).\n\t\t`);\n  }\n  return cloudflareRequestContext;\n}\nfunction getRequestContext() {\n  const cloudflareRequestContext = getOptionalRequestContext();\n  if (!cloudflareRequestContext) {\n    const isPrerendering = process?.env?.[\"NEXT_PHASE\"] === \"phase-production-build\";\n    if (isPrerendering) {\n      throw new Error(import_dedent_tabs.default`\n\t\t\t\t\\n\\`getRequestContext\\` is being called at the top level of a route file, this is not supported\n\t\t\t\tfor more details see https://developers.cloudflare.com/pages/framework-guides/nextjs/ssr/troubleshooting/#top-level-getrequestcontext \\n\n\t\t\t`);\n    }\n    let errorMessage = \"Failed to retrieve the Cloudflare request context.\";\n    if (process.env.NODE_ENV === \"development\") {\n      errorMessage += \"\\n\\n\" + import_dedent_tabs.default`\n\t\t\t\t\tFor local development (using the Next.js dev server) remember to include\n\t\t\t\t\ta call to the \\`setupDevPlatform\\` function in your config file.\n\n\t\t\t\t\tFor more details visit:\n\t\t\t\t\t  https://github.com/cloudflare/next-on-pages/tree/3846730c/internal-packages/next-dev\n\t\t\t\t` + \"\\n\\n\";\n    }\n    throw new Error(errorMessage);\n  }\n  return cloudflareRequestContext;\n}\nfunction inferRuntime() {\n  if (process?.release?.name === \"node\") {\n    return \"nodejs\";\n  }\n  return \"edge\";\n}\n// Annotate the CommonJS export names for ESM import in node:\n0 && (module.exports = {\n  getOptionalRequestContext,\n  getRequestContext\n});\n", null, "import { Column } from \"./column.js\";\nimport { entityKind, is } from \"./entity.js\";\nimport { SQL, sql } from \"./sql/sql.js\";\nimport { Table } from \"./table.js\";\nimport { ViewBaseConfig } from \"./view-common.js\";\nclass ColumnAliasProxyHandler {\n  constructor(table) {\n    this.table = table;\n  }\n  static [entityKind] = \"ColumnAliasProxyHandler\";\n  get(columnObj, prop) {\n    if (prop === \"table\") {\n      return this.table;\n    }\n    return columnObj[prop];\n  }\n}\nclass TableAliasProxyHandler {\n  constructor(alias, replaceOriginalName) {\n    this.alias = alias;\n    this.replaceOriginalName = replaceOriginalName;\n  }\n  static [entityKind] = \"TableAliasProxyHandler\";\n  get(target, prop) {\n    if (prop === Table.Symbol.IsAlias) {\n      return true;\n    }\n    if (prop === Table.Symbol.Name) {\n      return this.alias;\n    }\n    if (this.replaceOriginalName && prop === Table.Symbol.OriginalName) {\n      return this.alias;\n    }\n    if (prop === ViewBaseConfig) {\n      return {\n        ...target[ViewBaseConfig],\n        name: this.alias,\n        isAlias: true\n      };\n    }\n    if (prop === Table.Symbol.Columns) {\n      const columns = target[Table.Symbol.Columns];\n      if (!columns) {\n        return columns;\n      }\n      const proxiedColumns = {};\n      Object.keys(columns).map((key) => {\n        proxiedColumns[key] = new Proxy(\n          columns[key],\n          new ColumnAliasProxyHandler(new Proxy(target, this))\n        );\n      });\n      return proxiedColumns;\n    }\n    const value = target[prop];\n    if (is(value, Column)) {\n      return new Proxy(value, new ColumnAliasProxyHandler(new Proxy(target, this)));\n    }\n    return value;\n  }\n}\nclass RelationTableAliasProxyHandler {\n  constructor(alias) {\n    this.alias = alias;\n  }\n  static [entityKind] = \"RelationTableAliasProxyHandler\";\n  get(target, prop) {\n    if (prop === \"sourceTable\") {\n      return aliasedTable(target.sourceTable, this.alias);\n    }\n    return target[prop];\n  }\n}\nfunction aliasedTable(table, tableAlias) {\n  return new Proxy(table, new TableAliasProxyHandler(tableAlias, false));\n}\nfunction aliasedRelation(relation, tableAlias) {\n  return new Proxy(relation, new RelationTableAliasProxyHandler(tableAlias));\n}\nfunction aliasedTableColumn(column, tableAlias) {\n  return new Proxy(\n    column,\n    new ColumnAliasProxyHandler(new Proxy(column.table, new TableAliasProxyHandler(tableAlias, false)))\n  );\n}\nfunction mapColumnsInAliasedSQLToAlias(query, alias) {\n  return new SQL.Aliased(mapColumnsInSQLToAlias(query.sql, alias), query.fieldAlias);\n}\nfunction mapColumnsInSQLToAlias(query, alias) {\n  return sql.join(query.queryChunks.map((c) => {\n    if (is(c, Column)) {\n      return aliasedTableColumn(c, alias);\n    }\n    if (is(c, SQL)) {\n      return mapColumnsInSQLToAlias(c, alias);\n    }\n    if (is(c, SQL.Aliased)) {\n      return mapColumnsInAliasedSQLToAlias(c, alias);\n    }\n    return c;\n  }));\n}\nexport {\n  ColumnAliasProxyHandler,\n  RelationTableAliasProxyHandler,\n  TableAliasProxyHandler,\n  aliasedRelation,\n  aliasedTable,\n  aliasedTableColumn,\n  mapColumnsInAliasedSQLToAlias,\n  mapColumnsInSQLToAlias\n};\n//# sourceMappingURL=alias.js.map", "import { entityKind } from \"./entity.js\";\nimport { Table } from \"./table.js\";\nfunction toSnakeCase(input) {\n  const words = input.replace(/['\\u2019]/g, \"\").match(/[\\da-z]+|[A-Z]+(?![a-z])|[A-Z][\\da-z]+/g) ?? [];\n  return words.map((word) => word.toLowerCase()).join(\"_\");\n}\nfunction toCamelCase(input) {\n  const words = input.replace(/['\\u2019]/g, \"\").match(/[\\da-z]+|[A-Z]+(?![a-z])|[A-Z][\\da-z]+/g) ?? [];\n  return words.reduce((acc, word, i) => {\n    const formattedWord = i === 0 ? word.toLowerCase() : `${word[0].toUpperCase()}${word.slice(1)}`;\n    return acc + formattedWord;\n  }, \"\");\n}\nfunction noopCase(input) {\n  return input;\n}\nclass CasingCache {\n  static [entityKind] = \"CasingCache\";\n  /** @internal */\n  cache = {};\n  cachedTables = {};\n  convert;\n  constructor(casing) {\n    this.convert = casing === \"snake_case\" ? toSnakeCase : casing === \"camelCase\" ? toCamelCase : noopCase;\n  }\n  getColumnCasing(column) {\n    if (!column.keyAsName)\n      return column.name;\n    const schema = column.table[Table.Symbol.Schema] ?? \"public\";\n    const tableName = column.table[Table.Symbol.OriginalName];\n    const key = `${schema}.${tableName}.${column.name}`;\n    if (!this.cache[key]) {\n      this.cacheTable(column.table);\n    }\n    return this.cache[key];\n  }\n  cacheTable(table) {\n    const schema = table[Table.Symbol.Schema] ?? \"public\";\n    const tableName = table[Table.Symbol.OriginalName];\n    const tableKey = `${schema}.${tableName}`;\n    if (!this.cachedTables[tableKey]) {\n      for (const column of Object.values(table[Table.Symbol.Columns])) {\n        const columnKey = `${tableKey}.${column.name}`;\n        this.cache[columnKey] = this.convert(column.name);\n      }\n      this.cachedTables[tableKey] = true;\n    }\n  }\n  clearCache() {\n    this.cache = {};\n    this.cachedTables = {};\n  }\n}\nexport {\n  CasingCache,\n  toCamelCase,\n  toSnakeCase\n};\n//# sourceMappingURL=casing.js.map", "import { entityKind } from \"./entity.js\";\nclass ColumnBuilder {\n  static [entityKind] = \"ColumnBuilder\";\n  config;\n  constructor(name, dataType, columnType) {\n    this.config = {\n      name,\n      keyAsName: name === \"\",\n      notNull: false,\n      default: void 0,\n      hasDefault: false,\n      primaryKey: false,\n      isUnique: false,\n      uniqueName: void 0,\n      uniqueType: void 0,\n      dataType,\n      columnType,\n      generated: void 0\n    };\n  }\n  /**\n   * Changes the data type of the column. Commonly used with `json` columns. Also, useful for branded types.\n   *\n   * @example\n   * ```ts\n   * const users = pgTable('users', {\n   * \tid: integer('id').$type<UserId>().primaryKey(),\n   * \tdetails: json('details').$type<UserDetails>().notNull(),\n   * });\n   * ```\n   */\n  $type() {\n    return this;\n  }\n  /**\n   * Adds a `not null` clause to the column definition.\n   *\n   * Affects the `select` model of the table - columns *without* `not null` will be nullable on select.\n   */\n  notNull() {\n    this.config.notNull = true;\n    return this;\n  }\n  /**\n   * Adds a `default <value>` clause to the column definition.\n   *\n   * Affects the `insert` model of the table - columns *with* `default` are optional on insert.\n   *\n   * If you need to set a dynamic default value, use {@link $defaultFn} instead.\n   */\n  default(value) {\n    this.config.default = value;\n    this.config.hasDefault = true;\n    return this;\n  }\n  /**\n   * Adds a dynamic default value to the column.\n   * The function will be called when the row is inserted, and the returned value will be used as the column value.\n   *\n   * **Note:** This value does not affect the `drizzle-kit` behavior, it is only used at runtime in `drizzle-orm`.\n   */\n  $defaultFn(fn) {\n    this.config.defaultFn = fn;\n    this.config.hasDefault = true;\n    return this;\n  }\n  /**\n   * Alias for {@link $defaultFn}.\n   */\n  $default = this.$defaultFn;\n  /**\n   * Adds a dynamic update value to the column.\n   * The function will be called when the row is updated, and the returned value will be used as the column value if none is provided.\n   * If no `default` (or `$defaultFn`) value is provided, the function will be called when the row is inserted as well, and the returned value will be used as the column value.\n   *\n   * **Note:** This value does not affect the `drizzle-kit` behavior, it is only used at runtime in `drizzle-orm`.\n   */\n  $onUpdateFn(fn) {\n    this.config.onUpdateFn = fn;\n    this.config.hasDefault = true;\n    return this;\n  }\n  /**\n   * Alias for {@link $onUpdateFn}.\n   */\n  $onUpdate = this.$onUpdateFn;\n  /**\n   * Adds a `primary key` clause to the column definition. This implicitly makes the column `not null`.\n   *\n   * In SQLite, `integer primary key` implicitly makes the column auto-incrementing.\n   */\n  primaryKey() {\n    this.config.primaryKey = true;\n    this.config.notNull = true;\n    return this;\n  }\n  /** @internal Sets the name of the column to the key within the table definition if a name was not given. */\n  setName(name) {\n    if (this.config.name !== \"\")\n      return;\n    this.config.name = name;\n  }\n}\nexport {\n  ColumnBuilder\n};\n//# sourceMappingURL=column-builder.js.map", "import { entityKind } from \"./entity.js\";\nclass Column {\n  constructor(table, config) {\n    this.table = table;\n    this.config = config;\n    this.name = config.name;\n    this.keyAsName = config.keyAsName;\n    this.notNull = config.notNull;\n    this.default = config.default;\n    this.defaultFn = config.defaultFn;\n    this.onUpdateFn = config.onUpdateFn;\n    this.hasDefault = config.hasDefault;\n    this.primary = config.primaryKey;\n    this.isUnique = config.isUnique;\n    this.uniqueName = config.uniqueName;\n    this.uniqueType = config.uniqueType;\n    this.dataType = config.dataType;\n    this.columnType = config.columnType;\n    this.generated = config.generated;\n    this.generatedIdentity = config.generatedIdentity;\n  }\n  static [entityKind] = \"Column\";\n  name;\n  keyAsName;\n  primary;\n  notNull;\n  default;\n  defaultFn;\n  onUpdateFn;\n  hasDefault;\n  isUnique;\n  uniqueName;\n  uniqueType;\n  dataType;\n  columnType;\n  enumValues = void 0;\n  generated = void 0;\n  generatedIdentity = void 0;\n  config;\n  mapFromDriverValue(value) {\n    return value;\n  }\n  mapToDriverValue(value) {\n    return value;\n  }\n  // ** @internal */\n  shouldDisableInsert() {\n    return this.config.generated !== void 0 && this.config.generated.type !== \"byDefault\";\n  }\n}\nexport {\n  Column\n};\n//# sourceMappingURL=column.js.map", "import { entityKind } from \"./entity.js\";\nclass ConsoleLogWriter {\n  static [entityKind] = \"ConsoleLogWriter\";\n  write(message) {\n    console.log(message);\n  }\n}\nclass DefaultLogger {\n  static [entityKind] = \"DefaultLogger\";\n  writer;\n  constructor(config) {\n    this.writer = config?.writer ?? new ConsoleLogWriter();\n  }\n  logQuery(query, params) {\n    const stringifiedParams = params.map((p) => {\n      try {\n        return JSON.stringify(p);\n      } catch {\n        return String(p);\n      }\n    });\n    const paramsStr = stringifiedParams.length ? ` -- params: [${stringifiedParams.join(\", \")}]` : \"\";\n    this.writer.write(`Query: ${query}${paramsStr}`);\n  }\n}\nclass NoopLogger {\n  static [entityKind] = \"NoopLogger\";\n  logQuery() {\n  }\n}\nexport {\n  ConsoleLogWriter,\n  DefaultLogger,\n  NoopLogger\n};\n//# sourceMappingURL=logger.js.map", "import { entityKind } from \"../entity.js\";\nimport { DrizzleError, TransactionRollbackError } from \"../errors.js\";\nimport { QueryPromise } from \"../query-promise.js\";\nimport { BaseSQLiteDatabase } from \"./db.js\";\nclass ExecuteResultSync extends QueryPromise {\n  constructor(resultCb) {\n    super();\n    this.resultCb = resultCb;\n  }\n  static [entityKind] = \"ExecuteResultSync\";\n  async execute() {\n    return this.resultCb();\n  }\n  sync() {\n    return this.resultCb();\n  }\n}\nclass SQLitePreparedQuery {\n  constructor(mode, executeMethod, query) {\n    this.mode = mode;\n    this.executeMethod = executeMethod;\n    this.query = query;\n  }\n  static [entityKind] = \"PreparedQuery\";\n  /** @internal */\n  joinsNotNullableMap;\n  getQuery() {\n    return this.query;\n  }\n  mapRunResult(result, _isFromBatch) {\n    return result;\n  }\n  mapAllResult(_result, _isFromBatch) {\n    throw new Error(\"Not implemented\");\n  }\n  mapGetResult(_result, _isFromBatch) {\n    throw new Error(\"Not implemented\");\n  }\n  execute(placeholderValues) {\n    if (this.mode === \"async\") {\n      return this[this.executeMethod](placeholderValues);\n    }\n    return new ExecuteResultSync(() => this[this.executeMethod](placeholderValues));\n  }\n  mapResult(response, isFromBatch) {\n    switch (this.executeMethod) {\n      case \"run\": {\n        return this.mapRunResult(response, isFromBatch);\n      }\n      case \"all\": {\n        return this.mapAllResult(response, isFromBatch);\n      }\n      case \"get\": {\n        return this.mapGetResult(response, isFromBatch);\n      }\n    }\n  }\n}\nclass SQLiteSession {\n  constructor(dialect) {\n    this.dialect = dialect;\n  }\n  static [entityKind] = \"SQLiteSession\";\n  prepareOneTimeQuery(query, fields, executeMethod, isResponseInArrayMode) {\n    return this.prepareQuery(query, fields, executeMethod, isResponseInArrayMode);\n  }\n  run(query) {\n    const staticQuery = this.dialect.sqlToQuery(query);\n    try {\n      return this.prepareOneTimeQuery(staticQuery, void 0, \"run\", false).run();\n    } catch (err) {\n      throw new DrizzleError({ cause: err, message: `Failed to run the query '${staticQuery.sql}'` });\n    }\n  }\n  /** @internal */\n  extractRawRunValueFromBatchResult(result) {\n    return result;\n  }\n  all(query) {\n    return this.prepareOneTimeQuery(this.dialect.sqlToQuery(query), void 0, \"run\", false).all();\n  }\n  /** @internal */\n  extractRawAllValueFromBatchResult(_result) {\n    throw new Error(\"Not implemented\");\n  }\n  get(query) {\n    return this.prepareOneTimeQuery(this.dialect.sqlToQuery(query), void 0, \"run\", false).get();\n  }\n  /** @internal */\n  extractRawGetValueFromBatchResult(_result) {\n    throw new Error(\"Not implemented\");\n  }\n  values(query) {\n    return this.prepareOneTimeQuery(this.dialect.sqlToQuery(query), void 0, \"run\", false).values();\n  }\n  async count(sql) {\n    const result = await this.values(sql);\n    return result[0][0];\n  }\n  /** @internal */\n  extractRawValuesValueFromBatchResult(_result) {\n    throw new Error(\"Not implemented\");\n  }\n}\nclass SQLiteTransaction extends BaseSQLiteDatabase {\n  constructor(resultType, dialect, session, schema, nestedIndex = 0) {\n    super(resultType, dialect, session, schema);\n    this.schema = schema;\n    this.nestedIndex = nestedIndex;\n  }\n  static [entityKind] = \"SQLiteTransaction\";\n  rollback() {\n    throw new TransactionRollbackError();\n  }\n}\nexport {\n  ExecuteResultSync,\n  SQLitePreparedQuery,\n  SQLiteSession,\n  SQLiteTransaction\n};\n//# sourceMappingURL=session.js.map", "import { entityKind } from \"../entity.js\";\nimport { NoopLogger } from \"../logger.js\";\nimport { fillPlaceholders, sql } from \"../sql/sql.js\";\nimport { SQLiteTransaction } from \"../sqlite-core/index.js\";\nimport { SQLitePreparedQuery, SQLiteSession } from \"../sqlite-core/session.js\";\nimport { mapResultRow } from \"../utils.js\";\nclass SQLiteD1Session extends SQLiteSession {\n  constructor(client, dialect, schema, options = {}) {\n    super(dialect);\n    this.client = client;\n    this.schema = schema;\n    this.options = options;\n    this.logger = options.logger ?? new NoopLogger();\n  }\n  static [entityKind] = \"SQLiteD1Session\";\n  logger;\n  prepareQuery(query, fields, executeMethod, isResponseInArrayMode, customResultMapper) {\n    const stmt = this.client.prepare(query.sql);\n    return new D1PreparedQuery(\n      stmt,\n      query,\n      this.logger,\n      fields,\n      executeMethod,\n      isResponseInArrayMode,\n      customResultMapper\n    );\n  }\n  async batch(queries) {\n    const preparedQueries = [];\n    const builtQueries = [];\n    for (const query of queries) {\n      const preparedQuery = query._prepare();\n      const builtQuery = preparedQuery.getQuery();\n      preparedQueries.push(preparedQuery);\n      if (builtQuery.params.length > 0) {\n        builtQueries.push(preparedQuery.stmt.bind(...builtQuery.params));\n      } else {\n        const builtQuery2 = preparedQuery.getQuery();\n        builtQueries.push(\n          this.client.prepare(builtQuery2.sql).bind(...builtQuery2.params)\n        );\n      }\n    }\n    const batchResults = await this.client.batch(builtQueries);\n    return batchResults.map((result, i) => preparedQueries[i].mapResult(result, true));\n  }\n  extractRawAllValueFromBatchResult(result) {\n    return result.results;\n  }\n  extractRawGetValueFromBatchResult(result) {\n    return result.results[0];\n  }\n  extractRawValuesValueFromBatchResult(result) {\n    return d1ToRawMapping(result.results);\n  }\n  async transaction(transaction, config) {\n    const tx = new D1Transaction(\"async\", this.dialect, this, this.schema);\n    await this.run(sql.raw(`begin${config?.behavior ? \" \" + config.behavior : \"\"}`));\n    try {\n      const result = await transaction(tx);\n      await this.run(sql`commit`);\n      return result;\n    } catch (err) {\n      await this.run(sql`rollback`);\n      throw err;\n    }\n  }\n}\nclass D1Transaction extends SQLiteTransaction {\n  static [entityKind] = \"D1Transaction\";\n  async transaction(transaction) {\n    const savepointName = `sp${this.nestedIndex}`;\n    const tx = new D1Transaction(\"async\", this.dialect, this.session, this.schema, this.nestedIndex + 1);\n    await this.session.run(sql.raw(`savepoint ${savepointName}`));\n    try {\n      const result = await transaction(tx);\n      await this.session.run(sql.raw(`release savepoint ${savepointName}`));\n      return result;\n    } catch (err) {\n      await this.session.run(sql.raw(`rollback to savepoint ${savepointName}`));\n      throw err;\n    }\n  }\n}\nfunction d1ToRawMapping(results) {\n  const rows = [];\n  for (const row of results) {\n    const entry = Object.keys(row).map((k) => row[k]);\n    rows.push(entry);\n  }\n  return rows;\n}\nclass D1PreparedQuery extends SQLitePreparedQuery {\n  constructor(stmt, query, logger, fields, executeMethod, _isResponseInArrayMode, customResultMapper) {\n    super(\"async\", executeMethod, query);\n    this.logger = logger;\n    this._isResponseInArrayMode = _isResponseInArrayMode;\n    this.customResultMapper = customResultMapper;\n    this.fields = fields;\n    this.stmt = stmt;\n  }\n  static [entityKind] = \"D1PreparedQuery\";\n  /** @internal */\n  customResultMapper;\n  /** @internal */\n  fields;\n  /** @internal */\n  stmt;\n  run(placeholderValues) {\n    const params = fillPlaceholders(this.query.params, placeholderValues ?? {});\n    this.logger.logQuery(this.query.sql, params);\n    return this.stmt.bind(...params).run();\n  }\n  async all(placeholderValues) {\n    const { fields, query, logger, stmt, customResultMapper } = this;\n    if (!fields && !customResultMapper) {\n      const params = fillPlaceholders(query.params, placeholderValues ?? {});\n      logger.logQuery(query.sql, params);\n      return stmt.bind(...params).all().then(({ results }) => this.mapAllResult(results));\n    }\n    const rows = await this.values(placeholderValues);\n    return this.mapAllResult(rows);\n  }\n  mapAllResult(rows, isFromBatch) {\n    if (isFromBatch) {\n      rows = d1ToRawMapping(rows.results);\n    }\n    if (!this.fields && !this.customResultMapper) {\n      return rows;\n    }\n    if (this.customResultMapper) {\n      return this.customResultMapper(rows);\n    }\n    return rows.map((row) => mapResultRow(this.fields, row, this.joinsNotNullableMap));\n  }\n  async get(placeholderValues) {\n    const { fields, joinsNotNullableMap, query, logger, stmt, customResultMapper } = this;\n    if (!fields && !customResultMapper) {\n      const params = fillPlaceholders(query.params, placeholderValues ?? {});\n      logger.logQuery(query.sql, params);\n      return stmt.bind(...params).all().then(({ results }) => results[0]);\n    }\n    const rows = await this.values(placeholderValues);\n    if (!rows[0]) {\n      return void 0;\n    }\n    if (customResultMapper) {\n      return customResultMapper(rows);\n    }\n    return mapResultRow(fields, rows[0], joinsNotNullableMap);\n  }\n  mapGetResult(result, isFromBatch) {\n    if (isFromBatch) {\n      result = d1ToRawMapping(result.results)[0];\n    }\n    if (!this.fields && !this.customResultMapper) {\n      return result;\n    }\n    if (this.customResultMapper) {\n      return this.customResultMapper([result]);\n    }\n    return mapResultRow(this.fields, result, this.joinsNotNullableMap);\n  }\n  values(placeholderValues) {\n    const params = fillPlaceholders(this.query.params, placeholderValues ?? {});\n    this.logger.logQuery(this.query.sql, params);\n    return this.stmt.bind(...params).raw();\n  }\n  /** @internal */\n  isResponseInArrayMode() {\n    return this._isResponseInArrayMode;\n  }\n}\nexport {\n  D1PreparedQuery,\n  D1Transaction,\n  SQLiteD1Session\n};\n//# sourceMappingURL=session.js.map", "import { entityKind } from \"../entity.js\";\nimport { DefaultLogger } from \"../logger.js\";\nimport {\n  createTableRelationsHelpers,\n  extractTablesRelationalConfig\n} from \"../relations.js\";\nimport { BaseSQLiteDatabase } from \"../sqlite-core/db.js\";\nimport { SQLiteAsyncDialect } from \"../sqlite-core/dialect.js\";\nimport { SQLiteD1Session } from \"./session.js\";\nclass DrizzleD1Database extends BaseSQLiteDatabase {\n  static [entityKind] = \"D1Database\";\n  async batch(batch) {\n    return this.session.batch(batch);\n  }\n}\nfunction drizzle(client, config = {}) {\n  const dialect = new SQLiteAsyncDialect({ casing: config.casing });\n  let logger;\n  if (config.logger === true) {\n    logger = new DefaultLogger();\n  } else if (config.logger !== false) {\n    logger = config.logger;\n  }\n  let schema;\n  if (config.schema) {\n    const tablesConfig = extractTablesRelationalConfig(\n      config.schema,\n      createTableRelationsHelpers\n    );\n    schema = {\n      fullSchema: config.schema,\n      schema: tablesConfig.tables,\n      tableNamesMap: tablesConfig.tableNamesMap\n    };\n  }\n  const session = new SQLiteD1Session(client, dialect, schema, { logger });\n  const db = new DrizzleD1Database(\"async\", dialect, session, schema);\n  db.$client = client;\n  return db;\n}\nexport {\n  DrizzleD1Database,\n  drizzle\n};\n//# sourceMappingURL=driver.js.map", "const entityKind = Symbol.for(\"drizzle:entityKind\");\nconst hasOwnEntityKind = Symbol.for(\"drizzle:hasOwnEntityKind\");\nfunction is(value, type) {\n  if (!value || typeof value !== \"object\") {\n    return false;\n  }\n  if (value instanceof type) {\n    return true;\n  }\n  if (!Object.prototype.hasOwnProperty.call(type, entityKind)) {\n    throw new Error(\n      `Class \"${type.name ?? \"<unknown>\"}\" doesn't look like a Drizzle entity. If this is incorrect and the class is provided by Drizzle, please report this as a bug.`\n    );\n  }\n  let cls = Object.getPrototypeOf(value).constructor;\n  if (cls) {\n    while (cls) {\n      if (entityKind in cls && cls[entityKind] === type[entityKind]) {\n        return true;\n      }\n      cls = Object.getPrototypeOf(cls);\n    }\n  }\n  return false;\n}\nexport {\n  entityKind,\n  hasOwnEntityKind,\n  is\n};\n//# sourceMappingURL=entity.js.map", "import { entityKind } from \"./entity.js\";\nclass DrizzleError extends Error {\n  static [entityKind] = \"DrizzleError\";\n  constructor({ message, cause }) {\n    super(message);\n    this.name = \"DrizzleError\";\n    this.cause = cause;\n  }\n}\nclass TransactionRollbackError extends DrizzleError {\n  static [entityKind] = \"TransactionRollbackError\";\n  constructor() {\n    super({ message: \"Rollback\" });\n  }\n}\nexport {\n  DrizzleError,\n  TransactionRollbackError\n};\n//# sourceMappingURL=errors.js.map", "import { entityKind } from \"../../entity.js\";\nimport { PgColumn, PgColumnBuilder } from \"./common.js\";\nclass PgBooleanBuilder extends PgColumnBuilder {\n  static [entityKind] = \"PgBooleanBuilder\";\n  constructor(name) {\n    super(name, \"boolean\", \"PgBoolean\");\n  }\n  /** @internal */\n  build(table) {\n    return new PgBoolean(table, this.config);\n  }\n}\nclass PgBoolean extends PgColumn {\n  static [entityKind] = \"PgBoolean\";\n  getSQLType() {\n    return \"boolean\";\n  }\n}\nfunction boolean(name) {\n  return new PgBooleanBuilder(name ?? \"\");\n}\nexport {\n  PgBoolean,\n  PgBooleanBuilder,\n  boolean\n};\n//# sourceMappingURL=boolean.js.map", "import { entityKind } from \"../entity.js\";\nimport { TableName } from \"../table.utils.js\";\nclass ForeignKeyBuilder {\n  static [entityKind] = \"PgForeignKeyBuilder\";\n  /** @internal */\n  reference;\n  /** @internal */\n  _onUpdate = \"no action\";\n  /** @internal */\n  _onDelete = \"no action\";\n  constructor(config, actions) {\n    this.reference = () => {\n      const { name, columns, foreignColumns } = config();\n      return { name, columns, foreignTable: foreignColumns[0].table, foreignColumns };\n    };\n    if (actions) {\n      this._onUpdate = actions.onUpdate;\n      this._onDelete = actions.onDelete;\n    }\n  }\n  onUpdate(action) {\n    this._onUpdate = action === void 0 ? \"no action\" : action;\n    return this;\n  }\n  onDelete(action) {\n    this._onDelete = action === void 0 ? \"no action\" : action;\n    return this;\n  }\n  /** @internal */\n  build(table) {\n    return new ForeignKey(table, this);\n  }\n}\nclass ForeignKey {\n  constructor(table, builder) {\n    this.table = table;\n    this.reference = builder.reference;\n    this.onUpdate = builder._onUpdate;\n    this.onDelete = builder._onDelete;\n  }\n  static [entityKind] = \"PgForeignKey\";\n  reference;\n  onUpdate;\n  onDelete;\n  getName() {\n    const { name, columns, foreignColumns } = this.reference();\n    const columnNames = columns.map((column) => column.name);\n    const foreignColumnNames = foreignColumns.map((column) => column.name);\n    const chunks = [\n      this.table[TableName],\n      ...columnNames,\n      foreignColumns[0].table[TableName],\n      ...foreignColumnNames\n    ];\n    return name ?? `${chunks.join(\"_\")}_fk`;\n  }\n}\nfunction foreignKey(config) {\n  function mappedConfig() {\n    const { name, columns, foreignColumns } = config;\n    return {\n      name,\n      columns,\n      foreignColumns\n    };\n  }\n  return new ForeignKeyBuilder(mappedConfig);\n}\nexport {\n  ForeignKey,\n  ForeignKeyBuilder,\n  foreignKey\n};\n//# sourceMappingURL=foreign-keys.js.map", "import { entityKind } from \"../entity.js\";\nimport { TableName } from \"../table.utils.js\";\nfunction unique(name) {\n  return new UniqueOnConstraintBuilder(name);\n}\nfunction uniqueKeyName(table, columns) {\n  return `${table[TableName]}_${columns.join(\"_\")}_unique`;\n}\nclass UniqueConstraintBuilder {\n  constructor(columns, name) {\n    this.name = name;\n    this.columns = columns;\n  }\n  static [entityKind] = \"PgUniqueConstraintBuilder\";\n  /** @internal */\n  columns;\n  /** @internal */\n  nullsNotDistinctConfig = false;\n  nullsNotDistinct() {\n    this.nullsNotDistinctConfig = true;\n    return this;\n  }\n  /** @internal */\n  build(table) {\n    return new UniqueConstraint(table, this.columns, this.nullsNotDistinctConfig, this.name);\n  }\n}\nclass UniqueOnConstraintBuilder {\n  static [entityKind] = \"PgUniqueOnConstraintBuilder\";\n  /** @internal */\n  name;\n  constructor(name) {\n    this.name = name;\n  }\n  on(...columns) {\n    return new UniqueConstraintBuilder(columns, this.name);\n  }\n}\nclass UniqueConstraint {\n  constructor(table, columns, nullsNotDistinct, name) {\n    this.table = table;\n    this.columns = columns;\n    this.name = name ?? uniqueKeyName(this.table, this.columns.map((column) => column.name));\n    this.nullsNotDistinct = nullsNotDistinct;\n  }\n  static [entityKind] = \"PgUniqueConstraint\";\n  columns;\n  name;\n  nullsNotDistinct = false;\n  getName() {\n    return this.name;\n  }\n}\nexport {\n  UniqueConstraint,\n  UniqueConstraintBuilder,\n  UniqueOnConstraintBuilder,\n  unique,\n  uniqueKeyName\n};\n//# sourceMappingURL=unique-constraint.js.map", "function parsePgArrayValue(arrayString, startFrom, inQuotes) {\n  for (let i = startFrom; i < arrayString.length; i++) {\n    const char = arrayString[i];\n    if (char === \"\\\\\") {\n      i++;\n      continue;\n    }\n    if (char === '\"') {\n      return [arrayString.slice(startFrom, i).replace(/\\\\/g, \"\"), i + 1];\n    }\n    if (inQuotes) {\n      continue;\n    }\n    if (char === \",\" || char === \"}\") {\n      return [arrayString.slice(startFrom, i).replace(/\\\\/g, \"\"), i];\n    }\n  }\n  return [arrayString.slice(startFrom).replace(/\\\\/g, \"\"), arrayString.length];\n}\nfunction parsePgNestedArray(arrayString, startFrom = 0) {\n  const result = [];\n  let i = startFrom;\n  let lastCharIsComma = false;\n  while (i < arrayString.length) {\n    const char = arrayString[i];\n    if (char === \",\") {\n      if (lastCharIsComma || i === startFrom) {\n        result.push(\"\");\n      }\n      lastCharIsComma = true;\n      i++;\n      continue;\n    }\n    lastCharIsComma = false;\n    if (char === \"\\\\\") {\n      i += 2;\n      continue;\n    }\n    if (char === '\"') {\n      const [value2, startFrom2] = parsePgArrayValue(arrayString, i + 1, true);\n      result.push(value2);\n      i = startFrom2;\n      continue;\n    }\n    if (char === \"}\") {\n      return [result, i + 1];\n    }\n    if (char === \"{\") {\n      const [value2, startFrom2] = parsePgNestedArray(arrayString, i + 1);\n      result.push(value2);\n      i = startFrom2;\n      continue;\n    }\n    const [value, newStartFrom] = parsePgArrayValue(arrayString, i, false);\n    result.push(value);\n    i = newStartFrom;\n  }\n  return [result, i];\n}\nfunction parsePgArray(arrayString) {\n  const [result] = parsePgNestedArray(arrayString, 1);\n  return result;\n}\nfunction makePgArray(array) {\n  return `{${array.map((item) => {\n    if (Array.isArray(item)) {\n      return makePgArray(item);\n    }\n    if (typeof item === \"string\") {\n      return `\"${item.replace(/\\\\/g, \"\\\\\\\\\").replace(/\"/g, '\\\\\"')}\"`;\n    }\n    return `${item}`;\n  }).join(\",\")}}`;\n}\nexport {\n  makePgArray,\n  parsePgArray,\n  parsePgNestedArray\n};\n//# sourceMappingURL=array.js.map", "import { ColumnBuilder } from \"../../column-builder.js\";\nimport { Column } from \"../../column.js\";\nimport { entityKind, is } from \"../../entity.js\";\nimport { ForeignKeyBuilder } from \"../foreign-keys.js\";\nimport { iife } from \"../../tracing-utils.js\";\nimport { uniqueKeyName } from \"../unique-constraint.js\";\nimport { makePgArray, parsePgArray } from \"../utils/array.js\";\nclass PgColumnBuilder extends ColumnBuilder {\n  foreignKeyConfigs = [];\n  static [entityKind] = \"PgColumnBuilder\";\n  array(size) {\n    return new PgArrayBuilder(this.config.name, this, size);\n  }\n  references(ref, actions = {}) {\n    this.foreignKeyConfigs.push({ ref, actions });\n    return this;\n  }\n  unique(name, config) {\n    this.config.isUnique = true;\n    this.config.uniqueName = name;\n    this.config.uniqueType = config?.nulls;\n    return this;\n  }\n  generatedAlwaysAs(as) {\n    this.config.generated = {\n      as,\n      type: \"always\",\n      mode: \"stored\"\n    };\n    return this;\n  }\n  /** @internal */\n  buildForeignKeys(column, table) {\n    return this.foreignKeyConfigs.map(({ ref, actions }) => {\n      return iife(\n        (ref2, actions2) => {\n          const builder = new ForeignKeyBuilder(() => {\n            const foreignColumn = ref2();\n            return { columns: [column], foreignColumns: [foreignColumn] };\n          });\n          if (actions2.onUpdate) {\n            builder.onUpdate(actions2.onUpdate);\n          }\n          if (actions2.onDelete) {\n            builder.onDelete(actions2.onDelete);\n          }\n          return builder.build(table);\n        },\n        ref,\n        actions\n      );\n    });\n  }\n  /** @internal */\n  buildExtraConfigColumn(table) {\n    return new ExtraConfigColumn(table, this.config);\n  }\n}\nclass PgColumn extends Column {\n  constructor(table, config) {\n    if (!config.uniqueName) {\n      config.uniqueName = uniqueKeyName(table, [config.name]);\n    }\n    super(table, config);\n    this.table = table;\n  }\n  static [entityKind] = \"PgColumn\";\n}\nclass ExtraConfigColumn extends PgColumn {\n  static [entityKind] = \"ExtraConfigColumn\";\n  getSQLType() {\n    return this.getSQLType();\n  }\n  indexConfig = {\n    order: this.config.order ?? \"asc\",\n    nulls: this.config.nulls ?? \"last\",\n    opClass: this.config.opClass\n  };\n  defaultConfig = {\n    order: \"asc\",\n    nulls: \"last\",\n    opClass: void 0\n  };\n  asc() {\n    this.indexConfig.order = \"asc\";\n    return this;\n  }\n  desc() {\n    this.indexConfig.order = \"desc\";\n    return this;\n  }\n  nullsFirst() {\n    this.indexConfig.nulls = \"first\";\n    return this;\n  }\n  nullsLast() {\n    this.indexConfig.nulls = \"last\";\n    return this;\n  }\n  /**\n   * ### PostgreSQL documentation quote\n   *\n   * > An operator class with optional parameters can be specified for each column of an index.\n   * The operator class identifies the operators to be used by the index for that column.\n   * For example, a B-tree index on four-byte integers would use the int4_ops class;\n   * this operator class includes comparison functions for four-byte integers.\n   * In practice the default operator class for the column's data type is usually sufficient.\n   * The main point of having operator classes is that for some data types, there could be more than one meaningful ordering.\n   * For example, we might want to sort a complex-number data type either by absolute value or by real part.\n   * We could do this by defining two operator classes for the data type and then selecting the proper class when creating an index.\n   * More information about operator classes check:\n   *\n   * ### Useful links\n   * https://www.postgresql.org/docs/current/sql-createindex.html\n   *\n   * https://www.postgresql.org/docs/current/indexes-opclass.html\n   *\n   * https://www.postgresql.org/docs/current/xindex.html\n   *\n   * ### Additional types\n   * If you have the `pg_vector` extension installed in your database, you can use the\n   * `vector_l2_ops`, `vector_ip_ops`, `vector_cosine_ops`, `vector_l1_ops`, `bit_hamming_ops`, `bit_jaccard_ops`, `halfvec_l2_ops`, `sparsevec_l2_ops` options, which are predefined types.\n   *\n   * **You can always specify any string you want in the operator class, in case Drizzle doesn't have it natively in its types**\n   *\n   * @param opClass\n   * @returns\n   */\n  op(opClass) {\n    this.indexConfig.opClass = opClass;\n    return this;\n  }\n}\nclass IndexedColumn {\n  static [entityKind] = \"IndexedColumn\";\n  constructor(name, keyAsName, type, indexConfig) {\n    this.name = name;\n    this.keyAsName = keyAsName;\n    this.type = type;\n    this.indexConfig = indexConfig;\n  }\n  name;\n  keyAsName;\n  type;\n  indexConfig;\n}\nclass PgArrayBuilder extends PgColumnBuilder {\n  static [entityKind] = \"PgArrayBuilder\";\n  constructor(name, baseBuilder, size) {\n    super(name, \"array\", \"PgArray\");\n    this.config.baseBuilder = baseBuilder;\n    this.config.size = size;\n  }\n  /** @internal */\n  build(table) {\n    const baseColumn = this.config.baseBuilder.build(table);\n    return new PgArray(\n      table,\n      this.config,\n      baseColumn\n    );\n  }\n}\nclass PgArray extends PgColumn {\n  constructor(table, config, baseColumn, range) {\n    super(table, config);\n    this.baseColumn = baseColumn;\n    this.range = range;\n    this.size = config.size;\n  }\n  size;\n  static [entityKind] = \"PgArray\";\n  getSQLType() {\n    return `${this.baseColumn.getSQLType()}[${typeof this.size === \"number\" ? this.size : \"\"}]`;\n  }\n  mapFromDriverValue(value) {\n    if (typeof value === \"string\") {\n      value = parsePgArray(value);\n    }\n    return value.map((v) => this.baseColumn.mapFromDriverValue(v));\n  }\n  mapToDriverValue(value, isNestedArray = false) {\n    const a = value.map(\n      (v) => v === null ? null : is(this.baseColumn, PgArray) ? this.baseColumn.mapToDriverValue(v, true) : this.baseColumn.mapToDriverValue(v)\n    );\n    if (isNestedArray)\n      return a;\n    return makePgArray(a);\n  }\n}\nexport {\n  ExtraConfigColumn,\n  IndexedColumn,\n  PgArray,\n  PgArrayBuilder,\n  PgColumn,\n  PgColumnBuilder\n};\n//# sourceMappingURL=common.js.map", "import { entityKind } from \"../../entity.js\";\nimport { sql } from \"../../sql/sql.js\";\nimport { PgColumnBuilder } from \"./common.js\";\nclass PgDateColumnBaseBuilder extends PgColumnBuilder {\n  static [entityKind] = \"PgDateColumnBaseBuilder\";\n  defaultNow() {\n    return this.default(sql`now()`);\n  }\n}\nexport {\n  PgDateColumnBaseBuilder\n};\n//# sourceMappingURL=date.common.js.map", "import { entityKind } from \"../../entity.js\";\nimport { getColumnNameAndConfig } from \"../../utils.js\";\nimport { PgColumn } from \"./common.js\";\nimport { PgDateColumnBaseBuilder } from \"./date.common.js\";\nclass PgDateBuilder extends PgDateColumnBaseBuilder {\n  static [entityKind] = \"PgDateBuilder\";\n  constructor(name) {\n    super(name, \"date\", \"PgDate\");\n  }\n  /** @internal */\n  build(table) {\n    return new PgDate(table, this.config);\n  }\n}\nclass PgDate extends PgColumn {\n  static [entityKind] = \"PgDate\";\n  getSQLType() {\n    return \"date\";\n  }\n  mapFromDriverValue(value) {\n    return new Date(value);\n  }\n  mapToDriverValue(value) {\n    return value.toISOString();\n  }\n}\nclass PgDateStringBuilder extends PgDateColumnBaseBuilder {\n  static [entityKind] = \"PgDateStringBuilder\";\n  constructor(name) {\n    super(name, \"string\", \"PgDateString\");\n  }\n  /** @internal */\n  build(table) {\n    return new PgDateString(\n      table,\n      this.config\n    );\n  }\n}\nclass PgDateString extends PgColumn {\n  static [entityKind] = \"PgDateString\";\n  getSQLType() {\n    return \"date\";\n  }\n}\nfunction date(a, b) {\n  const { name, config } = getColumnNameAndConfig(a, b);\n  if (config?.mode === \"date\") {\n    return new PgDateBuilder(name);\n  }\n  return new PgDateStringBuilder(name);\n}\nexport {\n  PgDate,\n  PgDateBuilder,\n  PgDateString,\n  PgDateStringBuilder,\n  date\n};\n//# sourceMappingURL=date.js.map", "import { entityKind } from \"../../entity.js\";\nimport { PgColumnBuilder } from \"./common.js\";\nclass PgIntColumnBaseBuilder extends PgColumnBuilder {\n  static [entityKind] = \"PgIntColumnBaseBuilder\";\n  generatedAlwaysAsIdentity(sequence) {\n    if (sequence) {\n      const { name, ...options } = sequence;\n      this.config.generatedIdentity = {\n        type: \"always\",\n        sequenceName: name,\n        sequenceOptions: options\n      };\n    } else {\n      this.config.generatedIdentity = {\n        type: \"always\"\n      };\n    }\n    this.config.hasDefault = true;\n    this.config.notNull = true;\n    return this;\n  }\n  generatedByDefaultAsIdentity(sequence) {\n    if (sequence) {\n      const { name, ...options } = sequence;\n      this.config.generatedIdentity = {\n        type: \"byDefault\",\n        sequenceName: name,\n        sequenceOptions: options\n      };\n    } else {\n      this.config.generatedIdentity = {\n        type: \"byDefault\"\n      };\n    }\n    this.config.hasDefault = true;\n    this.config.notNull = true;\n    return this;\n  }\n}\nexport {\n  PgIntColumnBaseBuilder\n};\n//# sourceMappingURL=int.common.js.map", "import { entityKind } from \"../../entity.js\";\nimport { PgColumn } from \"./common.js\";\nimport { PgIntColumnBaseBuilder } from \"./int.common.js\";\nclass PgIntegerBuilder extends PgIntColumnBaseBuilder {\n  static [entityKind] = \"PgIntegerBuilder\";\n  constructor(name) {\n    super(name, \"number\", \"PgInteger\");\n  }\n  /** @internal */\n  build(table) {\n    return new PgInteger(table, this.config);\n  }\n}\nclass PgInteger extends PgColumn {\n  static [entityKind] = \"PgInteger\";\n  getSQLType() {\n    return \"integer\";\n  }\n  mapFromDriverValue(value) {\n    if (typeof value === \"string\") {\n      return Number.parseInt(value);\n    }\n    return value;\n  }\n}\nfunction integer(name) {\n  return new PgIntegerBuilder(name ?? \"\");\n}\nexport {\n  PgInteger,\n  PgIntegerBuilder,\n  integer\n};\n//# sourceMappingURL=integer.js.map", "import { entityKind } from \"../../entity.js\";\nimport { PgColumn, PgColumnBuilder } from \"./common.js\";\nclass PgJsonBuilder extends PgColumnBuilder {\n  static [entityKind] = \"PgJsonBuilder\";\n  constructor(name) {\n    super(name, \"json\", \"Pg<PERSON><PERSON>\");\n  }\n  /** @internal */\n  build(table) {\n    return new PgJson(table, this.config);\n  }\n}\nclass PgJson extends PgColumn {\n  static [entityKind] = \"PgJson\";\n  constructor(table, config) {\n    super(table, config);\n  }\n  getSQLType() {\n    return \"json\";\n  }\n  mapToDriverValue(value) {\n    return JSON.stringify(value);\n  }\n  mapFromDriverValue(value) {\n    if (typeof value === \"string\") {\n      try {\n        return JSON.parse(value);\n      } catch {\n        return value;\n      }\n    }\n    return value;\n  }\n}\nfunction json(name) {\n  return new PgJsonBuilder(name ?? \"\");\n}\nexport {\n  Pg<PERSON><PERSON>,\n  PgJsonBuilder,\n  json\n};\n//# sourceMappingURL=json.js.map", "import { entityKind } from \"../../entity.js\";\nimport { PgColumn, PgColumnBuilder } from \"./common.js\";\nclass PgJsonbBuilder extends PgColumnBuilder {\n  static [entityKind] = \"PgJsonbBuilder\";\n  constructor(name) {\n    super(name, \"json\", \"PgJsonb\");\n  }\n  /** @internal */\n  build(table) {\n    return new PgJsonb(table, this.config);\n  }\n}\nclass PgJsonb extends PgColumn {\n  static [entityKind] = \"PgJsonb\";\n  constructor(table, config) {\n    super(table, config);\n  }\n  getSQLType() {\n    return \"jsonb\";\n  }\n  mapToDriverValue(value) {\n    return JSON.stringify(value);\n  }\n  mapFromDriverValue(value) {\n    if (typeof value === \"string\") {\n      try {\n        return JSON.parse(value);\n      } catch {\n        return value;\n      }\n    }\n    return value;\n  }\n}\nfunction jsonb(name) {\n  return new PgJsonbBuilder(name ?? \"\");\n}\nexport {\n  Pg<PERSON><PERSON><PERSON>,\n  PgJsonbBuilder,\n  jsonb\n};\n//# sourceMappingURL=jsonb.js.map", "import { entityKind } from \"../../entity.js\";\nimport { getColumnNameAndConfig } from \"../../utils.js\";\nimport { PgColumn, PgColumnBuilder } from \"./common.js\";\nclass PgNumericBuilder extends PgColumnBuilder {\n  static [entityKind] = \"PgNumericBuilder\";\n  constructor(name, precision, scale) {\n    super(name, \"string\", \"PgNumeric\");\n    this.config.precision = precision;\n    this.config.scale = scale;\n  }\n  /** @internal */\n  build(table) {\n    return new PgNumeric(table, this.config);\n  }\n}\nclass PgNumeric extends PgColumn {\n  static [entityKind] = \"PgNumeric\";\n  precision;\n  scale;\n  constructor(table, config) {\n    super(table, config);\n    this.precision = config.precision;\n    this.scale = config.scale;\n  }\n  getSQLType() {\n    if (this.precision !== void 0 && this.scale !== void 0) {\n      return `numeric(${this.precision}, ${this.scale})`;\n    } else if (this.precision === void 0) {\n      return \"numeric\";\n    } else {\n      return `numeric(${this.precision})`;\n    }\n  }\n}\nfunction numeric(a, b) {\n  const { name, config } = getColumnNameAndConfig(a, b);\n  return new PgNumericBuilder(name, config?.precision, config?.scale);\n}\nconst decimal = numeric;\nexport {\n  PgNumeric,\n  PgNumericBuilder,\n  decimal,\n  numeric\n};\n//# sourceMappingURL=numeric.js.map", "import { entityKind } from \"../../entity.js\";\nimport { getColumnNameAndConfig } from \"../../utils.js\";\nimport { PgColumn, PgColumnBuilder } from \"./common.js\";\nclass PgTextBuilder extends PgColumnBuilder {\n  static [entityKind] = \"PgTextBuilder\";\n  constructor(name, config) {\n    super(name, \"string\", \"PgText\");\n    this.config.enumValues = config.enum;\n  }\n  /** @internal */\n  build(table) {\n    return new PgText(table, this.config);\n  }\n}\nclass PgText extends PgColumn {\n  static [entityKind] = \"PgText\";\n  enumValues = this.config.enumValues;\n  getSQLType() {\n    return \"text\";\n  }\n}\nfunction text(a, b = {}) {\n  const { name, config } = getColumnNameAndConfig(a, b);\n  return new PgTextBuilder(name, config);\n}\nexport {\n  PgText,\n  PgTextBuilder,\n  text\n};\n//# sourceMappingURL=text.js.map", "import { entityKind } from \"../../entity.js\";\nimport { getColumnNameAndConfig } from \"../../utils.js\";\nimport { PgColumn } from \"./common.js\";\nimport { PgDateColumnBaseBuilder } from \"./date.common.js\";\nclass PgTimeBuilder extends PgDateColumnBaseBuilder {\n  constructor(name, withTimezone, precision) {\n    super(name, \"string\", \"PgTime\");\n    this.withTimezone = withTimezone;\n    this.precision = precision;\n    this.config.withTimezone = withTimezone;\n    this.config.precision = precision;\n  }\n  static [entityKind] = \"PgTimeBuilder\";\n  /** @internal */\n  build(table) {\n    return new PgTime(table, this.config);\n  }\n}\nclass PgTime extends PgColumn {\n  static [entityKind] = \"PgTime\";\n  withTimezone;\n  precision;\n  constructor(table, config) {\n    super(table, config);\n    this.withTimezone = config.withTimezone;\n    this.precision = config.precision;\n  }\n  getSQLType() {\n    const precision = this.precision === void 0 ? \"\" : `(${this.precision})`;\n    return `time${precision}${this.withTimezone ? \" with time zone\" : \"\"}`;\n  }\n}\nfunction time(a, b = {}) {\n  const { name, config } = getColumnNameAndConfig(a, b);\n  return new PgTimeBuilder(name, config.withTimezone ?? false, config.precision);\n}\nexport {\n  PgTime,\n  PgTimeBuilder,\n  time\n};\n//# sourceMappingURL=time.js.map", "import { entityKind } from \"../../entity.js\";\nimport { getColumnNameAndConfig } from \"../../utils.js\";\nimport { PgColumn } from \"./common.js\";\nimport { PgDateColumnBaseBuilder } from \"./date.common.js\";\nclass PgTimestampBuilder extends PgDateColumnBaseBuilder {\n  static [entityKind] = \"PgTimestampBuilder\";\n  constructor(name, withTimezone, precision) {\n    super(name, \"date\", \"PgTimestamp\");\n    this.config.withTimezone = withTimezone;\n    this.config.precision = precision;\n  }\n  /** @internal */\n  build(table) {\n    return new PgTimestamp(table, this.config);\n  }\n}\nclass PgTimestamp extends PgColumn {\n  static [entityKind] = \"PgTimestamp\";\n  withTimezone;\n  precision;\n  constructor(table, config) {\n    super(table, config);\n    this.withTimezone = config.withTimezone;\n    this.precision = config.precision;\n  }\n  getSQLType() {\n    const precision = this.precision === void 0 ? \"\" : ` (${this.precision})`;\n    return `timestamp${precision}${this.withTimezone ? \" with time zone\" : \"\"}`;\n  }\n  mapFromDriverValue = (value) => {\n    return new Date(this.withTimezone ? value : value + \"+0000\");\n  };\n  mapToDriverValue = (value) => {\n    return value.toISOString();\n  };\n}\nclass PgTimestampStringBuilder extends PgDateColumnBaseBuilder {\n  static [entityKind] = \"PgTimestampStringBuilder\";\n  constructor(name, withTimezone, precision) {\n    super(name, \"string\", \"PgTimestampString\");\n    this.config.withTimezone = withTimezone;\n    this.config.precision = precision;\n  }\n  /** @internal */\n  build(table) {\n    return new PgTimestampString(\n      table,\n      this.config\n    );\n  }\n}\nclass PgTimestampString extends PgColumn {\n  static [entityKind] = \"PgTimestampString\";\n  withTimezone;\n  precision;\n  constructor(table, config) {\n    super(table, config);\n    this.withTimezone = config.withTimezone;\n    this.precision = config.precision;\n  }\n  getSQLType() {\n    const precision = this.precision === void 0 ? \"\" : `(${this.precision})`;\n    return `timestamp${precision}${this.withTimezone ? \" with time zone\" : \"\"}`;\n  }\n}\nfunction timestamp(a, b = {}) {\n  const { name, config } = getColumnNameAndConfig(a, b);\n  if (config?.mode === \"string\") {\n    return new PgTimestampStringBuilder(name, config.withTimezone ?? false, config.precision);\n  }\n  return new PgTimestampBuilder(name, config?.withTimezone ?? false, config?.precision);\n}\nexport {\n  PgTimestamp,\n  PgTimestampBuilder,\n  PgTimestampString,\n  PgTimestampStringBuilder,\n  timestamp\n};\n//# sourceMappingURL=timestamp.js.map", "import { entityKind } from \"../../entity.js\";\nimport { sql } from \"../../sql/sql.js\";\nimport { PgColumn, PgColumnBuilder } from \"./common.js\";\nclass PgUUIDBuilder extends PgColumnBuilder {\n  static [entityKind] = \"PgUUIDBuilder\";\n  constructor(name) {\n    super(name, \"string\", \"PgUUID\");\n  }\n  /**\n   * Adds `default gen_random_uuid()` to the column definition.\n   */\n  defaultRandom() {\n    return this.default(sql`gen_random_uuid()`);\n  }\n  /** @internal */\n  build(table) {\n    return new PgUUID(table, this.config);\n  }\n}\nclass PgUUID extends PgColumn {\n  static [entityKind] = \"PgUUID\";\n  getSQLType() {\n    return \"uuid\";\n  }\n}\nfunction uuid(name) {\n  return new PgUUIDBuilder(name ?? \"\");\n}\nexport {\n  PgUUID,\n  PgUUIDBuilder,\n  uuid\n};\n//# sourceMappingURL=uuid.js.map", "import { entityKind } from \"../entity.js\";\nimport { PgTable } from \"./table.js\";\nfunction primaryKey(...config) {\n  if (config[0].columns) {\n    return new PrimaryKeyBuilder(config[0].columns, config[0].name);\n  }\n  return new PrimaryKeyBuilder(config);\n}\nclass PrimaryKeyBuilder {\n  static [entityKind] = \"PgPrimaryKeyBuilder\";\n  /** @internal */\n  columns;\n  /** @internal */\n  name;\n  constructor(columns, name) {\n    this.columns = columns;\n    this.name = name;\n  }\n  /** @internal */\n  build(table) {\n    return new PrimaryKey(table, this.columns, this.name);\n  }\n}\nclass PrimaryKey {\n  constructor(table, columns, name) {\n    this.table = table;\n    this.columns = columns;\n    this.name = name;\n  }\n  static [entityKind] = \"PgPrimaryKey\";\n  columns;\n  name;\n  getName() {\n    return this.name ?? `${this.table[PgTable.Symbol.Name]}_${this.columns.map((column) => column.name).join(\"_\")}_pk`;\n  }\n}\nexport {\n  PrimaryK<PERSON>,\n  PrimaryKeyBuilder,\n  primaryKey\n};\n//# sourceMappingURL=primary-keys.js.map", "import { entityKind } from \"../../entity.js\";\nimport { getColumnNameAndConfig } from \"../../utils.js\";\nimport { PgColumn } from \"./common.js\";\nimport { PgIntColumnBaseBuilder } from \"./int.common.js\";\nclass PgBigInt53Builder extends PgIntColumnBaseBuilder {\n  static [entityKind] = \"PgBigInt53Builder\";\n  constructor(name) {\n    super(name, \"number\", \"PgBigInt53\");\n  }\n  /** @internal */\n  build(table) {\n    return new PgBigInt53(table, this.config);\n  }\n}\nclass PgBigInt53 extends PgColumn {\n  static [entityKind] = \"PgBigInt53\";\n  getSQLType() {\n    return \"bigint\";\n  }\n  mapFromDriverValue(value) {\n    if (typeof value === \"number\") {\n      return value;\n    }\n    return Number(value);\n  }\n}\nclass PgBigInt64Builder extends PgIntColumnBaseBuilder {\n  static [entityKind] = \"PgBigInt64Builder\";\n  constructor(name) {\n    super(name, \"bigint\", \"PgBigInt64\");\n  }\n  /** @internal */\n  build(table) {\n    return new PgBigInt64(\n      table,\n      this.config\n    );\n  }\n}\nclass PgBigInt64 extends PgColumn {\n  static [entityKind] = \"PgBigInt64\";\n  getSQLType() {\n    return \"bigint\";\n  }\n  // eslint-disable-next-line unicorn/prefer-native-coercion-functions\n  mapFromDriverValue(value) {\n    return BigInt(value);\n  }\n}\nfunction bigint(a, b) {\n  const { name, config } = getColumnNameAndConfig(a, b);\n  if (config.mode === \"number\") {\n    return new PgBigInt53Builder(name);\n  }\n  return new PgBigInt64Builder(name);\n}\nexport {\n  PgBigInt53,\n  PgBigInt53Builder,\n  PgBigInt64,\n  PgBigInt64Builder,\n  bigint\n};\n//# sourceMappingURL=bigint.js.map", "import { entityKind } from \"../../entity.js\";\nimport { getColumnNameAndConfig } from \"../../utils.js\";\nimport { PgColumn, PgColumnBuilder } from \"./common.js\";\nclass PgBigSerial53Builder extends PgColumnBuilder {\n  static [entityKind] = \"PgBigSerial53Builder\";\n  constructor(name) {\n    super(name, \"number\", \"PgBigSerial53\");\n    this.config.hasDefault = true;\n    this.config.notNull = true;\n  }\n  /** @internal */\n  build(table) {\n    return new PgBigSerial53(\n      table,\n      this.config\n    );\n  }\n}\nclass PgBigSerial53 extends PgColumn {\n  static [entityKind] = \"PgBigSerial53\";\n  getSQLType() {\n    return \"bigserial\";\n  }\n  mapFromDriverValue(value) {\n    if (typeof value === \"number\") {\n      return value;\n    }\n    return Number(value);\n  }\n}\nclass PgBigSerial64Builder extends PgColumnBuilder {\n  static [entityKind] = \"PgBigSerial64Builder\";\n  constructor(name) {\n    super(name, \"bigint\", \"PgBigSerial64\");\n    this.config.hasDefault = true;\n  }\n  /** @internal */\n  build(table) {\n    return new PgBigSerial64(\n      table,\n      this.config\n    );\n  }\n}\nclass PgBigSerial64 extends PgColumn {\n  static [entityKind] = \"PgBigSerial64\";\n  getSQLType() {\n    return \"bigserial\";\n  }\n  // eslint-disable-next-line unicorn/prefer-native-coercion-functions\n  mapFromDriverValue(value) {\n    return BigInt(value);\n  }\n}\nfunction bigserial(a, b) {\n  const { name, config } = getColumnNameAndConfig(a, b);\n  if (config.mode === \"number\") {\n    return new PgBigSerial53Builder(name);\n  }\n  return new PgBigSerial64Builder(name);\n}\nexport {\n  PgBigSerial53,\n  PgBigSerial53Builder,\n  PgBigSerial64,\n  PgBigSerial64Builder,\n  bigserial\n};\n//# sourceMappingURL=bigserial.js.map", "import { entityKind } from \"../../entity.js\";\nimport { getColumnNameAndConfig } from \"../../utils.js\";\nimport { PgColumn, PgColumnBuilder } from \"./common.js\";\nclass PgCharBuilder extends PgColumnBuilder {\n  static [entityKind] = \"PgCharBuilder\";\n  constructor(name, config) {\n    super(name, \"string\", \"PgChar\");\n    this.config.length = config.length;\n    this.config.enumValues = config.enum;\n  }\n  /** @internal */\n  build(table) {\n    return new PgChar(table, this.config);\n  }\n}\nclass PgChar extends PgColumn {\n  static [entityKind] = \"PgChar\";\n  length = this.config.length;\n  enumValues = this.config.enumValues;\n  getSQLType() {\n    return this.length === void 0 ? `char` : `char(${this.length})`;\n  }\n}\nfunction char(a, b = {}) {\n  const { name, config } = getColumnNameAndConfig(a, b);\n  return new PgCharBuilder(name, config);\n}\nexport {\n  PgChar,\n  PgCharBuilder,\n  char\n};\n//# sourceMappingURL=char.js.map", "import { entityKind } from \"../../entity.js\";\nimport { PgColumn, PgColumnBuilder } from \"./common.js\";\nclass PgCidrBuilder extends PgColumnBuilder {\n  static [entityKind] = \"PgCidrBuilder\";\n  constructor(name) {\n    super(name, \"string\", \"PgCidr\");\n  }\n  /** @internal */\n  build(table) {\n    return new PgCidr(table, this.config);\n  }\n}\nclass PgCidr extends PgColumn {\n  static [entityKind] = \"PgCidr\";\n  getSQLType() {\n    return \"cidr\";\n  }\n}\nfunction cidr(name) {\n  return new PgCidrBuilder(name ?? \"\");\n}\nexport {\n  PgCidr,\n  PgCidrBuilder,\n  cidr\n};\n//# sourceMappingURL=cidr.js.map", "import { entityKind } from \"../../entity.js\";\nimport { getColumnNameAndConfig } from \"../../utils.js\";\nimport { PgColumn, PgColumnBuilder } from \"./common.js\";\nclass PgCustomColumnBuilder extends PgColumnBuilder {\n  static [entityKind] = \"PgCustomColumnBuilder\";\n  constructor(name, fieldConfig, customTypeParams) {\n    super(name, \"custom\", \"PgCustomColumn\");\n    this.config.fieldConfig = fieldConfig;\n    this.config.customTypeParams = customTypeParams;\n  }\n  /** @internal */\n  build(table) {\n    return new PgCustomColumn(\n      table,\n      this.config\n    );\n  }\n}\nclass PgCustomColumn extends PgColumn {\n  static [entityKind] = \"PgCustomColumn\";\n  sqlName;\n  mapTo;\n  mapFrom;\n  constructor(table, config) {\n    super(table, config);\n    this.sqlName = config.customTypeParams.dataType(config.fieldConfig);\n    this.mapTo = config.customTypeParams.toDriver;\n    this.mapFrom = config.customTypeParams.fromDriver;\n  }\n  getSQLType() {\n    return this.sqlName;\n  }\n  mapFromDriverValue(value) {\n    return typeof this.mapFrom === \"function\" ? this.mapFrom(value) : value;\n  }\n  mapToDriverValue(value) {\n    return typeof this.mapTo === \"function\" ? this.mapTo(value) : value;\n  }\n}\nfunction customType(customTypeParams) {\n  return (a, b) => {\n    const { name, config } = getColumnNameAndConfig(a, b);\n    return new PgCustomColumnBuilder(name, config, customTypeParams);\n  };\n}\nexport {\n  PgCustomColumn,\n  PgCustomColumnBuilder,\n  customType\n};\n//# sourceMappingURL=custom.js.map", "import { entityKind } from \"../../entity.js\";\nimport { PgColumn, PgColumnBuilder } from \"./common.js\";\nclass PgDoublePrecisionBuilder extends PgColumnBuilder {\n  static [entityKind] = \"PgDoublePrecisionBuilder\";\n  constructor(name) {\n    super(name, \"number\", \"PgDoublePrecision\");\n  }\n  /** @internal */\n  build(table) {\n    return new PgDoublePrecision(\n      table,\n      this.config\n    );\n  }\n}\nclass PgDoublePrecision extends PgColumn {\n  static [entityKind] = \"PgDoublePrecision\";\n  getSQLType() {\n    return \"double precision\";\n  }\n  mapFromDriverValue(value) {\n    if (typeof value === \"string\") {\n      return Number.parseFloat(value);\n    }\n    return value;\n  }\n}\nfunction doublePrecision(name) {\n  return new PgDoublePrecisionBuilder(name ?? \"\");\n}\nexport {\n  PgDoublePrecision,\n  PgDoublePrecisionBuilder,\n  doublePrecision\n};\n//# sourceMappingURL=double-precision.js.map", "import { entityKind } from \"../../entity.js\";\nimport { PgColumn, PgColumnBuilder } from \"./common.js\";\nclass PgInetBuilder extends PgColumnBuilder {\n  static [entityKind] = \"PgInetBuilder\";\n  constructor(name) {\n    super(name, \"string\", \"PgInet\");\n  }\n  /** @internal */\n  build(table) {\n    return new PgInet(table, this.config);\n  }\n}\nclass PgInet extends PgColumn {\n  static [entityKind] = \"PgInet\";\n  getSQLType() {\n    return \"inet\";\n  }\n}\nfunction inet(name) {\n  return new PgInetBuilder(name ?? \"\");\n}\nexport {\n  PgInet,\n  PgInetBuilder,\n  inet\n};\n//# sourceMappingURL=inet.js.map", "import { entityKind } from \"../../entity.js\";\nimport { getColumnNameAndConfig } from \"../../utils.js\";\nimport { PgColumn, PgColumnBuilder } from \"./common.js\";\nclass PgIntervalBuilder extends PgColumnBuilder {\n  static [entityKind] = \"PgIntervalBuilder\";\n  constructor(name, intervalConfig) {\n    super(name, \"string\", \"PgInterval\");\n    this.config.intervalConfig = intervalConfig;\n  }\n  /** @internal */\n  build(table) {\n    return new PgInterval(table, this.config);\n  }\n}\nclass PgInterval extends PgColumn {\n  static [entityKind] = \"PgInterval\";\n  fields = this.config.intervalConfig.fields;\n  precision = this.config.intervalConfig.precision;\n  getSQLType() {\n    const fields = this.fields ? ` ${this.fields}` : \"\";\n    const precision = this.precision ? `(${this.precision})` : \"\";\n    return `interval${fields}${precision}`;\n  }\n}\nfunction interval(a, b = {}) {\n  const { name, config } = getColumnNameAndConfig(a, b);\n  return new PgIntervalBuilder(name, config);\n}\nexport {\n  PgInterval,\n  PgIntervalBuilder,\n  interval\n};\n//# sourceMappingURL=interval.js.map", "import { entityKind } from \"../../entity.js\";\nimport { getColumnNameAndConfig } from \"../../utils.js\";\nimport { PgColumn, PgColumnBuilder } from \"./common.js\";\nclass PgLineBuilder extends PgColumnBuilder {\n  static [entityKind] = \"PgLineBuilder\";\n  constructor(name) {\n    super(name, \"array\", \"PgLine\");\n  }\n  /** @internal */\n  build(table) {\n    return new PgLineTuple(\n      table,\n      this.config\n    );\n  }\n}\nclass PgLineTuple extends PgColumn {\n  static [entityKind] = \"PgLine\";\n  getSQLType() {\n    return \"line\";\n  }\n  mapFromDriverValue(value) {\n    const [a, b, c] = value.slice(1, -1).split(\",\");\n    return [Number.parseFloat(a), Number.parseFloat(b), Number.parseFloat(c)];\n  }\n  mapToDriverValue(value) {\n    return `{${value[0]},${value[1]},${value[2]}}`;\n  }\n}\nclass PgLineABCBuilder extends PgColumnBuilder {\n  static [entityKind] = \"PgLineABCBuilder\";\n  constructor(name) {\n    super(name, \"json\", \"PgLineABC\");\n  }\n  /** @internal */\n  build(table) {\n    return new PgLineABC(\n      table,\n      this.config\n    );\n  }\n}\nclass PgLineABC extends PgColumn {\n  static [entityKind] = \"PgLineABC\";\n  getSQLType() {\n    return \"line\";\n  }\n  mapFromDriverValue(value) {\n    const [a, b, c] = value.slice(1, -1).split(\",\");\n    return { a: Number.parseFloat(a), b: Number.parseFloat(b), c: Number.parseFloat(c) };\n  }\n  mapToDriverValue(value) {\n    return `{${value.a},${value.b},${value.c}}`;\n  }\n}\nfunction line(a, b) {\n  const { name, config } = getColumnNameAndConfig(a, b);\n  if (!config?.mode || config.mode === \"tuple\") {\n    return new PgLineBuilder(name);\n  }\n  return new PgLineABCBuilder(name);\n}\nexport {\n  PgLineABC,\n  PgLineABCBuilder,\n  PgLineBuilder,\n  PgLineTuple,\n  line\n};\n//# sourceMappingURL=line.js.map", "import { entityKind } from \"../../entity.js\";\nimport { PgColumn, PgColumnBuilder } from \"./common.js\";\nclass PgMacaddrBuilder extends PgColumnBuilder {\n  static [entityKind] = \"PgMacaddrBuilder\";\n  constructor(name) {\n    super(name, \"string\", \"PgMacaddr\");\n  }\n  /** @internal */\n  build(table) {\n    return new PgMacaddr(table, this.config);\n  }\n}\nclass PgMacaddr extends PgColumn {\n  static [entityKind] = \"PgMacaddr\";\n  getSQLType() {\n    return \"macaddr\";\n  }\n}\nfunction macaddr(name) {\n  return new PgMacaddrBuilder(name ?? \"\");\n}\nexport {\n  PgMacaddr,\n  PgMacaddrBuilder,\n  macaddr\n};\n//# sourceMappingURL=macaddr.js.map", "import { entityKind } from \"../../entity.js\";\nimport { PgColumn, PgColumnBuilder } from \"./common.js\";\nclass PgMacaddr8Builder extends PgColumnBuilder {\n  static [entityKind] = \"PgMacaddr8Builder\";\n  constructor(name) {\n    super(name, \"string\", \"PgMacaddr8\");\n  }\n  /** @internal */\n  build(table) {\n    return new PgMacaddr8(table, this.config);\n  }\n}\nclass PgMacaddr8 extends PgColumn {\n  static [entityKind] = \"PgMacaddr8\";\n  getSQLType() {\n    return \"macaddr8\";\n  }\n}\nfunction macaddr8(name) {\n  return new PgMacaddr8Builder(name ?? \"\");\n}\nexport {\n  PgMacaddr8,\n  PgMacaddr8Builder,\n  macaddr8\n};\n//# sourceMappingURL=macaddr8.js.map", "import { entityKind } from \"../../entity.js\";\nimport { getColumnNameAndConfig } from \"../../utils.js\";\nimport { PgColumn, PgColumnBuilder } from \"./common.js\";\nclass PgPointTupleBuilder extends PgColumnBuilder {\n  static [entityKind] = \"PgPointTupleBuilder\";\n  constructor(name) {\n    super(name, \"array\", \"PgPointTuple\");\n  }\n  /** @internal */\n  build(table) {\n    return new PgPointTuple(\n      table,\n      this.config\n    );\n  }\n}\nclass PgPointTuple extends PgColumn {\n  static [entityKind] = \"PgPointTuple\";\n  getSQLType() {\n    return \"point\";\n  }\n  mapFromDriverValue(value) {\n    if (typeof value === \"string\") {\n      const [x, y] = value.slice(1, -1).split(\",\");\n      return [Number.parseFloat(x), Number.parseFloat(y)];\n    }\n    return [value.x, value.y];\n  }\n  mapToDriverValue(value) {\n    return `(${value[0]},${value[1]})`;\n  }\n}\nclass PgPointObjectBuilder extends PgColumnBuilder {\n  static [entityKind] = \"PgPointObjectBuilder\";\n  constructor(name) {\n    super(name, \"json\", \"PgPointObject\");\n  }\n  /** @internal */\n  build(table) {\n    return new PgPointObject(\n      table,\n      this.config\n    );\n  }\n}\nclass PgPointObject extends PgColumn {\n  static [entityKind] = \"PgPointObject\";\n  getSQLType() {\n    return \"point\";\n  }\n  mapFromDriverValue(value) {\n    if (typeof value === \"string\") {\n      const [x, y] = value.slice(1, -1).split(\",\");\n      return { x: Number.parseFloat(x), y: Number.parseFloat(y) };\n    }\n    return value;\n  }\n  mapToDriverValue(value) {\n    return `(${value.x},${value.y})`;\n  }\n}\nfunction point(a, b) {\n  const { name, config } = getColumnNameAndConfig(a, b);\n  if (!config?.mode || config.mode === \"tuple\") {\n    return new PgPointTupleBuilder(name);\n  }\n  return new PgPointObjectBuilder(name);\n}\nexport {\n  PgPointObject,\n  PgPointObjectBuilder,\n  PgPointTuple,\n  PgPointTupleBuilder,\n  point\n};\n//# sourceMappingURL=point.js.map", "function hexToBytes(hex) {\n  const bytes = [];\n  for (let c = 0; c < hex.length; c += 2) {\n    bytes.push(Number.parseInt(hex.slice(c, c + 2), 16));\n  }\n  return new Uint8Array(bytes);\n}\nfunction bytesToFloat64(bytes, offset) {\n  const buffer = new ArrayBuffer(8);\n  const view = new DataView(buffer);\n  for (let i = 0; i < 8; i++) {\n    view.setUint8(i, bytes[offset + i]);\n  }\n  return view.getFloat64(0, true);\n}\nfunction parseEWKB(hex) {\n  const bytes = hexToBytes(hex);\n  let offset = 0;\n  const byteOrder = bytes[offset];\n  offset += 1;\n  const view = new DataView(bytes.buffer);\n  const geomType = view.getUint32(offset, byteOrder === 1);\n  offset += 4;\n  let _srid;\n  if (geomType & 536870912) {\n    _srid = view.getUint32(offset, byteOrder === 1);\n    offset += 4;\n  }\n  if ((geomType & 65535) === 1) {\n    const x = bytesToFloat64(bytes, offset);\n    offset += 8;\n    const y = bytesToFloat64(bytes, offset);\n    offset += 8;\n    return [x, y];\n  }\n  throw new Error(\"Unsupported geometry type\");\n}\nexport {\n  parseEWKB\n};\n//# sourceMappingURL=utils.js.map", "import { entityKind } from \"../../../entity.js\";\nimport { getColumnNameAndConfig } from \"../../../utils.js\";\nimport { PgColumn, PgColumnBuilder } from \"../common.js\";\nimport { parseEWKB } from \"./utils.js\";\nclass PgGeometryBuilder extends PgColumnBuilder {\n  static [entityKind] = \"PgGeometryBuilder\";\n  constructor(name) {\n    super(name, \"array\", \"PgGeometry\");\n  }\n  /** @internal */\n  build(table) {\n    return new PgGeometry(\n      table,\n      this.config\n    );\n  }\n}\nclass PgGeometry extends PgColumn {\n  static [entityKind] = \"PgGeometry\";\n  getSQLType() {\n    return \"geometry(point)\";\n  }\n  mapFromDriverValue(value) {\n    return parseEWKB(value);\n  }\n  mapToDriverValue(value) {\n    return `point(${value[0]} ${value[1]})`;\n  }\n}\nclass PgGeometryObjectBuilder extends PgColumnBuilder {\n  static [entityKind] = \"PgGeometryObjectBuilder\";\n  constructor(name) {\n    super(name, \"json\", \"PgGeometryObject\");\n  }\n  /** @internal */\n  build(table) {\n    return new PgGeometryObject(\n      table,\n      this.config\n    );\n  }\n}\nclass PgGeometryObject extends PgColumn {\n  static [entityKind] = \"PgGeometryObject\";\n  getSQLType() {\n    return \"geometry(point)\";\n  }\n  mapFromDriverValue(value) {\n    const parsed = parseEWKB(value);\n    return { x: parsed[0], y: parsed[1] };\n  }\n  mapToDriverValue(value) {\n    return `point(${value.x} ${value.y})`;\n  }\n}\nfunction geometry(a, b) {\n  const { name, config } = getColumnNameAndConfig(a, b);\n  if (!config?.mode || config.mode === \"tuple\") {\n    return new PgGeometryBuilder(name);\n  }\n  return new PgGeometryObjectBuilder(name);\n}\nexport {\n  PgGeometry,\n  PgGeometryBuilder,\n  PgGeometryObject,\n  PgGeometryObjectBuilder,\n  geometry\n};\n//# sourceMappingURL=geometry.js.map", "import { entityKind } from \"../../entity.js\";\nimport { PgColumn, PgColumnBuilder } from \"./common.js\";\nclass PgRealBuilder extends PgColumnBuilder {\n  static [entityKind] = \"PgRealBuilder\";\n  constructor(name, length) {\n    super(name, \"number\", \"PgReal\");\n    this.config.length = length;\n  }\n  /** @internal */\n  build(table) {\n    return new PgReal(table, this.config);\n  }\n}\nclass PgReal extends PgColumn {\n  static [entityKind] = \"PgReal\";\n  constructor(table, config) {\n    super(table, config);\n  }\n  getSQLType() {\n    return \"real\";\n  }\n  mapFromDriverValue = (value) => {\n    if (typeof value === \"string\") {\n      return Number.parseFloat(value);\n    }\n    return value;\n  };\n}\nfunction real(name) {\n  return new PgRealBuilder(name ?? \"\");\n}\nexport {\n  PgReal,\n  PgRealBuilder,\n  real\n};\n//# sourceMappingURL=real.js.map", "import { entityKind } from \"../../entity.js\";\nimport { PgColumn, PgColumnBuilder } from \"./common.js\";\nclass PgSerialBuilder extends PgColumnBuilder {\n  static [entityKind] = \"PgSerialBuilder\";\n  constructor(name) {\n    super(name, \"number\", \"PgSerial\");\n    this.config.hasDefault = true;\n    this.config.notNull = true;\n  }\n  /** @internal */\n  build(table) {\n    return new PgSerial(table, this.config);\n  }\n}\nclass PgSerial extends PgColumn {\n  static [entityKind] = \"PgSerial\";\n  getSQLType() {\n    return \"serial\";\n  }\n}\nfunction serial(name) {\n  return new PgSerialBuilder(name ?? \"\");\n}\nexport {\n  PgSerial,\n  PgSerialBuilder,\n  serial\n};\n//# sourceMappingURL=serial.js.map", "import { entityKind } from \"../../entity.js\";\nimport { PgColumn } from \"./common.js\";\nimport { PgIntColumnBaseBuilder } from \"./int.common.js\";\nclass PgSmallIntBuilder extends PgIntColumnBaseBuilder {\n  static [entityKind] = \"PgSmallIntBuilder\";\n  constructor(name) {\n    super(name, \"number\", \"PgSmallInt\");\n  }\n  /** @internal */\n  build(table) {\n    return new PgSmallInt(table, this.config);\n  }\n}\nclass PgSmallInt extends PgColumn {\n  static [entityKind] = \"PgSmallInt\";\n  getSQLType() {\n    return \"smallint\";\n  }\n  mapFromDriverValue = (value) => {\n    if (typeof value === \"string\") {\n      return Number(value);\n    }\n    return value;\n  };\n}\nfunction smallint(name) {\n  return new PgSmallIntBuilder(name ?? \"\");\n}\nexport {\n  PgSmallInt,\n  PgSmallIntBuilder,\n  smallint\n};\n//# sourceMappingURL=smallint.js.map", "import { entityKind } from \"../../entity.js\";\nimport { PgColumn, PgColumnBuilder } from \"./common.js\";\nclass PgSmallSerialBuilder extends PgColumnBuilder {\n  static [entityKind] = \"PgSmallSerialBuilder\";\n  constructor(name) {\n    super(name, \"number\", \"PgSmallSerial\");\n    this.config.hasDefault = true;\n    this.config.notNull = true;\n  }\n  /** @internal */\n  build(table) {\n    return new PgSmallSerial(\n      table,\n      this.config\n    );\n  }\n}\nclass PgSmallSerial extends PgColumn {\n  static [entityKind] = \"PgSmallSerial\";\n  getSQLType() {\n    return \"smallserial\";\n  }\n}\nfunction smallserial(name) {\n  return new PgSmallSerialBuilder(name ?? \"\");\n}\nexport {\n  PgSmallSerial,\n  PgSmallSerialBuilder,\n  smallserial\n};\n//# sourceMappingURL=smallserial.js.map", "import { entityKind } from \"../../entity.js\";\nimport { getColumnNameAndConfig } from \"../../utils.js\";\nimport { PgColumn, PgColumnBuilder } from \"./common.js\";\nclass PgVarcharBuilder extends PgColumnBuilder {\n  static [entityKind] = \"PgVarcharBuilder\";\n  constructor(name, config) {\n    super(name, \"string\", \"PgVarchar\");\n    this.config.length = config.length;\n    this.config.enumValues = config.enum;\n  }\n  /** @internal */\n  build(table) {\n    return new PgVarchar(table, this.config);\n  }\n}\nclass PgVarchar extends PgColumn {\n  static [entityKind] = \"PgVarchar\";\n  length = this.config.length;\n  enumValues = this.config.enumValues;\n  getSQLType() {\n    return this.length === void 0 ? `varchar` : `varchar(${this.length})`;\n  }\n}\nfunction varchar(a, b = {}) {\n  const { name, config } = getColumnNameAndConfig(a, b);\n  return new PgVarcharBuilder(name, config);\n}\nexport {\n  PgVarchar,\n  PgVarcharBuilder,\n  varchar\n};\n//# sourceMappingURL=varchar.js.map", "import { entityKind } from \"../../../entity.js\";\nimport { getColumnNameAndConfig } from \"../../../utils.js\";\nimport { PgColumn, PgColumnBuilder } from \"../common.js\";\nclass PgBinaryVectorBuilder extends PgColumnBuilder {\n  static [entityKind] = \"PgBinaryVectorBuilder\";\n  constructor(name, config) {\n    super(name, \"string\", \"PgBinaryVector\");\n    this.config.dimensions = config.dimensions;\n  }\n  /** @internal */\n  build(table) {\n    return new PgBinaryVector(\n      table,\n      this.config\n    );\n  }\n}\nclass PgBinaryVector extends PgColumn {\n  static [entityKind] = \"PgBinaryVector\";\n  dimensions = this.config.dimensions;\n  getSQLType() {\n    return `bit(${this.dimensions})`;\n  }\n}\nfunction bit(a, b) {\n  const { name, config } = getColumnNameAndConfig(a, b);\n  return new PgBinaryVectorBuilder(name, config);\n}\nexport {\n  PgBinaryVector,\n  PgBinaryVectorBuilder,\n  bit\n};\n//# sourceMappingURL=bit.js.map", "import { entityKind } from \"../../../entity.js\";\nimport { getColumnNameAndConfig } from \"../../../utils.js\";\nimport { PgColumn, PgColumnBuilder } from \"../common.js\";\nclass PgHalfVectorBuilder extends PgColumnBuilder {\n  static [entityKind] = \"PgHalfVectorBuilder\";\n  constructor(name, config) {\n    super(name, \"array\", \"PgHalfVector\");\n    this.config.dimensions = config.dimensions;\n  }\n  /** @internal */\n  build(table) {\n    return new PgHalfVector(\n      table,\n      this.config\n    );\n  }\n}\nclass PgHalfVector extends PgColumn {\n  static [entityKind] = \"PgHalfVector\";\n  dimensions = this.config.dimensions;\n  getSQLType() {\n    return `halfvec(${this.dimensions})`;\n  }\n  mapToDriverValue(value) {\n    return JSON.stringify(value);\n  }\n  mapFromDriverValue(value) {\n    return value.slice(1, -1).split(\",\").map((v) => Number.parseFloat(v));\n  }\n}\nfunction halfvec(a, b) {\n  const { name, config } = getColumnNameAndConfig(a, b);\n  return new PgHalfVectorBuilder(name, config);\n}\nexport {\n  PgHalfVector,\n  PgHalfVectorBuilder,\n  halfvec\n};\n//# sourceMappingURL=halfvec.js.map", "import { entityKind } from \"../../../entity.js\";\nimport { getColumnNameAndConfig } from \"../../../utils.js\";\nimport { PgColumn, PgColumnBuilder } from \"../common.js\";\nclass PgSparseVectorBuilder extends PgColumnBuilder {\n  static [entityKind] = \"PgSparseVectorBuilder\";\n  constructor(name, config) {\n    super(name, \"string\", \"PgSparseVector\");\n    this.config.dimensions = config.dimensions;\n  }\n  /** @internal */\n  build(table) {\n    return new PgSparseVector(\n      table,\n      this.config\n    );\n  }\n}\nclass PgSparseVector extends PgColumn {\n  static [entityKind] = \"PgSparseVector\";\n  dimensions = this.config.dimensions;\n  getSQLType() {\n    return `sparsevec(${this.dimensions})`;\n  }\n}\nfunction sparsevec(a, b) {\n  const { name, config } = getColumnNameAndConfig(a, b);\n  return new PgSparseVectorBuilder(name, config);\n}\nexport {\n  PgSparseVector,\n  PgSparseVectorBuilder,\n  sparsevec\n};\n//# sourceMappingURL=sparsevec.js.map", "import { entityKind } from \"../../../entity.js\";\nimport { getColumnNameAndConfig } from \"../../../utils.js\";\nimport { PgColumn, PgColumnBuilder } from \"../common.js\";\nclass PgVectorBuilder extends PgColumnBuilder {\n  static [entityKind] = \"PgVectorBuilder\";\n  constructor(name, config) {\n    super(name, \"array\", \"PgVector\");\n    this.config.dimensions = config.dimensions;\n  }\n  /** @internal */\n  build(table) {\n    return new PgVector(table, this.config);\n  }\n}\nclass PgVector extends PgColumn {\n  static [entityKind] = \"PgVector\";\n  dimensions = this.config.dimensions;\n  getSQLType() {\n    return `vector(${this.dimensions})`;\n  }\n  mapToDriverValue(value) {\n    return JSON.stringify(value);\n  }\n  mapFromDriverValue(value) {\n    return value.slice(1, -1).split(\",\").map((v) => Number.parseFloat(v));\n  }\n}\nfunction vector(a, b) {\n  const { name, config } = getColumnNameAndConfig(a, b);\n  return new PgVectorBuilder(name, config);\n}\nexport {\n  PgVector,\n  PgVectorBuilder,\n  vector\n};\n//# sourceMappingURL=vector.js.map", "import { bigint } from \"./bigint.js\";\nimport { bigserial } from \"./bigserial.js\";\nimport { boolean } from \"./boolean.js\";\nimport { char } from \"./char.js\";\nimport { cidr } from \"./cidr.js\";\nimport { customType } from \"./custom.js\";\nimport { date } from \"./date.js\";\nimport { doublePrecision } from \"./double-precision.js\";\nimport { inet } from \"./inet.js\";\nimport { integer } from \"./integer.js\";\nimport { interval } from \"./interval.js\";\nimport { json } from \"./json.js\";\nimport { jsonb } from \"./jsonb.js\";\nimport { line } from \"./line.js\";\nimport { macaddr } from \"./macaddr.js\";\nimport { macaddr8 } from \"./macaddr8.js\";\nimport { numeric } from \"./numeric.js\";\nimport { point } from \"./point.js\";\nimport { geometry } from \"./postgis_extension/geometry.js\";\nimport { real } from \"./real.js\";\nimport { serial } from \"./serial.js\";\nimport { smallint } from \"./smallint.js\";\nimport { smallserial } from \"./smallserial.js\";\nimport { text } from \"./text.js\";\nimport { time } from \"./time.js\";\nimport { timestamp } from \"./timestamp.js\";\nimport { uuid } from \"./uuid.js\";\nimport { varchar } from \"./varchar.js\";\nimport { bit } from \"./vector_extension/bit.js\";\nimport { halfvec } from \"./vector_extension/halfvec.js\";\nimport { sparsevec } from \"./vector_extension/sparsevec.js\";\nimport { vector } from \"./vector_extension/vector.js\";\nfunction getPgColumnBuilders() {\n  return {\n    bigint,\n    bigserial,\n    boolean,\n    char,\n    cidr,\n    customType,\n    date,\n    doublePrecision,\n    inet,\n    integer,\n    interval,\n    json,\n    jsonb,\n    line,\n    macaddr,\n    macaddr8,\n    numeric,\n    point,\n    geometry,\n    real,\n    serial,\n    smallint,\n    smallserial,\n    text,\n    time,\n    timestamp,\n    uuid,\n    varchar,\n    bit,\n    halfvec,\n    sparsevec,\n    vector\n  };\n}\nexport {\n  getPgColumnBuilders\n};\n//# sourceMappingURL=all.js.map", "import { entityKind } from \"../entity.js\";\nimport { Table } from \"../table.js\";\nimport { getPgColumnBuilders } from \"./columns/all.js\";\nconst InlineForeignKeys = Symbol.for(\"drizzle:PgInlineForeignKeys\");\nconst EnableRLS = Symbol.for(\"drizzle:EnableRLS\");\nclass PgTable extends Table {\n  static [entityKind] = \"PgTable\";\n  /** @internal */\n  static Symbol = Object.assign({}, Table.Symbol, {\n    InlineForeignKeys,\n    EnableRLS\n  });\n  /**@internal */\n  [InlineForeignKeys] = [];\n  /** @internal */\n  [EnableRLS] = false;\n  /** @internal */\n  [Table.Symbol.ExtraConfigBuilder] = void 0;\n}\nfunction pgTableWithSchema(name, columns, extraConfig, schema, baseName = name) {\n  const rawTable = new PgTable(name, schema, baseName);\n  const parsedColumns = typeof columns === \"function\" ? columns(getPgColumnBuilders()) : columns;\n  const builtColumns = Object.fromEntries(\n    Object.entries(parsedColumns).map(([name2, colBuilderBase]) => {\n      const colBuilder = colBuilderBase;\n      colBuilder.setName(name2);\n      const column = colBuilder.build(rawTable);\n      rawTable[InlineForeignKeys].push(...colBuilder.buildForeignKeys(column, rawTable));\n      return [name2, column];\n    })\n  );\n  const builtColumnsForExtraConfig = Object.fromEntries(\n    Object.entries(parsedColumns).map(([name2, colBuilderBase]) => {\n      const colBuilder = colBuilderBase;\n      colBuilder.setName(name2);\n      const column = colBuilder.buildExtraConfigColumn(rawTable);\n      return [name2, column];\n    })\n  );\n  const table = Object.assign(rawTable, builtColumns);\n  table[Table.Symbol.Columns] = builtColumns;\n  table[Table.Symbol.ExtraConfigColumns] = builtColumnsForExtraConfig;\n  if (extraConfig) {\n    table[PgTable.Symbol.ExtraConfigBuilder] = extraConfig;\n  }\n  return Object.assign(table, {\n    enableRLS: () => {\n      table[PgTable.Symbol.EnableRLS] = true;\n      return table;\n    }\n  });\n}\nconst pgTable = (name, columns, extraConfig) => {\n  return pgTableWithSchema(name, columns, extraConfig, void 0);\n};\nfunction pgTableCreator(customizeTableName) {\n  return (name, columns, extraConfig) => {\n    return pgTableWithSchema(customizeTableName(name), columns, extraConfig, void 0, name);\n  };\n}\nexport {\n  EnableRLS,\n  InlineForeignKeys,\n  PgTable,\n  pgTable,\n  pgTableCreator,\n  pgTableWithSchema\n};\n//# sourceMappingURL=table.js.map", "import { entityKind } from \"../entity.js\";\nclass TypedQueryBuilder {\n  static [entityKind] = \"TypedQueryBuilder\";\n  /** @internal */\n  getSelectedFields() {\n    return this._.selectedFields;\n  }\n}\nexport {\n  TypedQueryBuilder\n};\n//# sourceMappingURL=query-builder.js.map", "import { entityKind } from \"./entity.js\";\nclass QueryPromise {\n  static [entityKind] = \"QueryPromise\";\n  [Symbol.toStringTag] = \"QueryPromise\";\n  catch(onRejected) {\n    return this.then(void 0, onRejected);\n  }\n  finally(onFinally) {\n    return this.then(\n      (value) => {\n        onFinally?.();\n        return value;\n      },\n      (reason) => {\n        onFinally?.();\n        throw reason;\n      }\n    );\n  }\n  then(onFulfilled, onRejected) {\n    return this.execute().then(onFulfilled, onRejected);\n  }\n}\nexport {\n  QueryPromise\n};\n//# sourceMappingURL=query-promise.js.map", "import { getTableUniqueName, Table } from \"./table.js\";\nimport { Column } from \"./column.js\";\nimport { entityKind, is } from \"./entity.js\";\nimport { PrimaryKeyBuilder } from \"./pg-core/primary-keys.js\";\nimport {\n  and,\n  asc,\n  between,\n  desc,\n  eq,\n  exists,\n  gt,\n  gte,\n  ilike,\n  inArray,\n  isNotNull,\n  isNull,\n  like,\n  lt,\n  lte,\n  ne,\n  not,\n  notBetween,\n  notExists,\n  notIlike,\n  notInArray,\n  notLike,\n  or\n} from \"./sql/expressions/index.js\";\nimport { SQL, sql } from \"./sql/sql.js\";\nclass Relation {\n  constructor(sourceTable, referencedTable, relationName) {\n    this.sourceTable = sourceTable;\n    this.referencedTable = referencedTable;\n    this.relationName = relationName;\n    this.referencedTableName = referencedTable[Table.Symbol.Name];\n  }\n  static [entityKind] = \"Relation\";\n  referencedTableName;\n  fieldName;\n}\nclass Relations {\n  constructor(table, config) {\n    this.table = table;\n    this.config = config;\n  }\n  static [entityKind] = \"Relations\";\n}\nclass One extends Relation {\n  constructor(sourceTable, referencedTable, config, isNullable) {\n    super(sourceTable, referencedTable, config?.relationName);\n    this.config = config;\n    this.isNullable = isNullable;\n  }\n  static [entityKind] = \"One\";\n  withFieldName(fieldName) {\n    const relation = new One(\n      this.sourceTable,\n      this.referencedTable,\n      this.config,\n      this.isNullable\n    );\n    relation.fieldName = fieldName;\n    return relation;\n  }\n}\nclass Many extends Relation {\n  constructor(sourceTable, referencedTable, config) {\n    super(sourceTable, referencedTable, config?.relationName);\n    this.config = config;\n  }\n  static [entityKind] = \"Many\";\n  withFieldName(fieldName) {\n    const relation = new Many(\n      this.sourceTable,\n      this.referencedTable,\n      this.config\n    );\n    relation.fieldName = fieldName;\n    return relation;\n  }\n}\nfunction getOperators() {\n  return {\n    and,\n    between,\n    eq,\n    exists,\n    gt,\n    gte,\n    ilike,\n    inArray,\n    isNull,\n    isNotNull,\n    like,\n    lt,\n    lte,\n    ne,\n    not,\n    notBetween,\n    notExists,\n    notLike,\n    notIlike,\n    notInArray,\n    or,\n    sql\n  };\n}\nfunction getOrderByOperators() {\n  return {\n    sql,\n    asc,\n    desc\n  };\n}\nfunction extractTablesRelationalConfig(schema, configHelpers) {\n  if (Object.keys(schema).length === 1 && \"default\" in schema && !is(schema[\"default\"], Table)) {\n    schema = schema[\"default\"];\n  }\n  const tableNamesMap = {};\n  const relationsBuffer = {};\n  const tablesConfig = {};\n  for (const [key, value] of Object.entries(schema)) {\n    if (is(value, Table)) {\n      const dbName = getTableUniqueName(value);\n      const bufferedRelations = relationsBuffer[dbName];\n      tableNamesMap[dbName] = key;\n      tablesConfig[key] = {\n        tsName: key,\n        dbName: value[Table.Symbol.Name],\n        schema: value[Table.Symbol.Schema],\n        columns: value[Table.Symbol.Columns],\n        relations: bufferedRelations?.relations ?? {},\n        primaryKey: bufferedRelations?.primaryKey ?? []\n      };\n      for (const column of Object.values(\n        value[Table.Symbol.Columns]\n      )) {\n        if (column.primary) {\n          tablesConfig[key].primaryKey.push(column);\n        }\n      }\n      const extraConfig = value[Table.Symbol.ExtraConfigBuilder]?.(value[Table.Symbol.ExtraConfigColumns]);\n      if (extraConfig) {\n        for (const configEntry of Object.values(extraConfig)) {\n          if (is(configEntry, PrimaryKeyBuilder)) {\n            tablesConfig[key].primaryKey.push(...configEntry.columns);\n          }\n        }\n      }\n    } else if (is(value, Relations)) {\n      const dbName = getTableUniqueName(value.table);\n      const tableName = tableNamesMap[dbName];\n      const relations2 = value.config(\n        configHelpers(value.table)\n      );\n      let primaryKey;\n      for (const [relationName, relation] of Object.entries(relations2)) {\n        if (tableName) {\n          const tableConfig = tablesConfig[tableName];\n          tableConfig.relations[relationName] = relation;\n          if (primaryKey) {\n            tableConfig.primaryKey.push(...primaryKey);\n          }\n        } else {\n          if (!(dbName in relationsBuffer)) {\n            relationsBuffer[dbName] = {\n              relations: {},\n              primaryKey\n            };\n          }\n          relationsBuffer[dbName].relations[relationName] = relation;\n        }\n      }\n    }\n  }\n  return { tables: tablesConfig, tableNamesMap };\n}\nfunction relations(table, relations2) {\n  return new Relations(\n    table,\n    (helpers) => Object.fromEntries(\n      Object.entries(relations2(helpers)).map(([key, value]) => [\n        key,\n        value.withFieldName(key)\n      ])\n    )\n  );\n}\nfunction createOne(sourceTable) {\n  return function one(table, config) {\n    return new One(\n      sourceTable,\n      table,\n      config,\n      config?.fields.reduce((res, f) => res && f.notNull, true) ?? false\n    );\n  };\n}\nfunction createMany(sourceTable) {\n  return function many(referencedTable, config) {\n    return new Many(sourceTable, referencedTable, config);\n  };\n}\nfunction normalizeRelation(schema, tableNamesMap, relation) {\n  if (is(relation, One) && relation.config) {\n    return {\n      fields: relation.config.fields,\n      references: relation.config.references\n    };\n  }\n  const referencedTableTsName = tableNamesMap[getTableUniqueName(relation.referencedTable)];\n  if (!referencedTableTsName) {\n    throw new Error(\n      `Table \"${relation.referencedTable[Table.Symbol.Name]}\" not found in schema`\n    );\n  }\n  const referencedTableConfig = schema[referencedTableTsName];\n  if (!referencedTableConfig) {\n    throw new Error(`Table \"${referencedTableTsName}\" not found in schema`);\n  }\n  const sourceTable = relation.sourceTable;\n  const sourceTableTsName = tableNamesMap[getTableUniqueName(sourceTable)];\n  if (!sourceTableTsName) {\n    throw new Error(\n      `Table \"${sourceTable[Table.Symbol.Name]}\" not found in schema`\n    );\n  }\n  const reverseRelations = [];\n  for (const referencedTableRelation of Object.values(\n    referencedTableConfig.relations\n  )) {\n    if (relation.relationName && relation !== referencedTableRelation && referencedTableRelation.relationName === relation.relationName || !relation.relationName && referencedTableRelation.referencedTable === relation.sourceTable) {\n      reverseRelations.push(referencedTableRelation);\n    }\n  }\n  if (reverseRelations.length > 1) {\n    throw relation.relationName ? new Error(\n      `There are multiple relations with name \"${relation.relationName}\" in table \"${referencedTableTsName}\"`\n    ) : new Error(\n      `There are multiple relations between \"${referencedTableTsName}\" and \"${relation.sourceTable[Table.Symbol.Name]}\". Please specify relation name`\n    );\n  }\n  if (reverseRelations[0] && is(reverseRelations[0], One) && reverseRelations[0].config) {\n    return {\n      fields: reverseRelations[0].config.references,\n      references: reverseRelations[0].config.fields\n    };\n  }\n  throw new Error(\n    `There is not enough information to infer relation \"${sourceTableTsName}.${relation.fieldName}\"`\n  );\n}\nfunction createTableRelationsHelpers(sourceTable) {\n  return {\n    one: createOne(sourceTable),\n    many: createMany(sourceTable)\n  };\n}\nfunction mapRelationalRow(tablesConfig, tableConfig, row, buildQueryResultSelection, mapColumnValue = (value) => value) {\n  const result = {};\n  for (const [\n    selectionItemIndex,\n    selectionItem\n  ] of buildQueryResultSelection.entries()) {\n    if (selectionItem.isJson) {\n      const relation = tableConfig.relations[selectionItem.tsKey];\n      const rawSubRows = row[selectionItemIndex];\n      const subRows = typeof rawSubRows === \"string\" ? JSON.parse(rawSubRows) : rawSubRows;\n      result[selectionItem.tsKey] = is(relation, One) ? subRows && mapRelationalRow(\n        tablesConfig,\n        tablesConfig[selectionItem.relationTableTsKey],\n        subRows,\n        selectionItem.selection,\n        mapColumnValue\n      ) : subRows.map(\n        (subRow) => mapRelationalRow(\n          tablesConfig,\n          tablesConfig[selectionItem.relationTableTsKey],\n          subRow,\n          selectionItem.selection,\n          mapColumnValue\n        )\n      );\n    } else {\n      const value = mapColumnValue(row[selectionItemIndex]);\n      const field = selectionItem.field;\n      let decoder;\n      if (is(field, Column)) {\n        decoder = field;\n      } else if (is(field, SQL)) {\n        decoder = field.decoder;\n      } else {\n        decoder = field.sql.decoder;\n      }\n      result[selectionItem.tsKey] = value === null ? null : decoder.mapFromDriverValue(value);\n    }\n  }\n  return result;\n}\nexport {\n  Many,\n  One,\n  Relation,\n  Relations,\n  createMany,\n  createOne,\n  createTableRelationsHelpers,\n  extractTablesRelationalConfig,\n  getOperators,\n  getOrderByOperators,\n  mapRelationalRow,\n  normalizeRelation,\n  relations\n};\n//# sourceMappingURL=relations.js.map", "import { ColumnAliasProxy<PERSON>and<PERSON>, TableAliasProxyHandler } from \"./alias.js\";\nimport { Column } from \"./column.js\";\nimport { entityKind, is } from \"./entity.js\";\nimport { SQL, View } from \"./sql/sql.js\";\nimport { Subquery } from \"./subquery.js\";\nimport { ViewBaseConfig } from \"./view-common.js\";\nclass SelectionProxyHandler {\n  static [entityKind] = \"SelectionProxyHandler\";\n  config;\n  constructor(config) {\n    this.config = { ...config };\n  }\n  get(subquery, prop) {\n    if (prop === \"_\") {\n      return {\n        ...subquery[\"_\"],\n        selectedFields: new Proxy(\n          subquery._.selectedFields,\n          this\n        )\n      };\n    }\n    if (prop === ViewBaseConfig) {\n      return {\n        ...subquery[ViewBaseConfig],\n        selectedFields: new Proxy(\n          subquery[ViewBaseConfig].selectedFields,\n          this\n        )\n      };\n    }\n    if (typeof prop === \"symbol\") {\n      return subquery[prop];\n    }\n    const columns = is(subquery, Subquery) ? subquery._.selectedFields : is(subquery, View) ? subquery[ViewBaseConfig].selectedFields : subquery;\n    const value = columns[prop];\n    if (is(value, SQL.Aliased)) {\n      if (this.config.sqlAliasedBehavior === \"sql\" && !value.isSelectionField) {\n        return value.sql;\n      }\n      const newValue = value.clone();\n      newValue.isSelectionField = true;\n      return newValue;\n    }\n    if (is(value, SQL)) {\n      if (this.config.sqlBehavior === \"sql\") {\n        return value;\n      }\n      throw new Error(\n        `You tried to reference \"${prop}\" field from a subquery, which is a raw SQL field, but it doesn't have an alias declared. Please add an alias to the field using \".as('alias')\" method.`\n      );\n    }\n    if (is(value, Column)) {\n      if (this.config.alias) {\n        return new Proxy(\n          value,\n          new ColumnAliasProxyHandler(\n            new Proxy(\n              value.table,\n              new TableAliasProxyHandler(this.config.alias, this.config.replaceOriginalName ?? false)\n            )\n          )\n        );\n      }\n      return value;\n    }\n    if (typeof value !== \"object\" || value === null) {\n      return value;\n    }\n    return new Proxy(value, new SelectionProxyHandler(this.config));\n  }\n}\nexport {\n  SelectionProxyHandler\n};\n//# sourceMappingURL=selection-proxy.js.map", "import { Column } from \"../../column.js\";\nimport { is } from \"../../entity.js\";\nimport { Table } from \"../../table.js\";\nimport {\n  isDriverValueEncoder,\n  isSQLWrapper,\n  Param,\n  Placeholder,\n  SQL,\n  sql,\n  StringChunk,\n  View\n} from \"../sql.js\";\nfunction bindIfParam(value, column) {\n  if (isDriverValueEncoder(column) && !isSQLWrapper(value) && !is(value, Param) && !is(value, Placeholder) && !is(value, Column) && !is(value, Table) && !is(value, View)) {\n    return new Param(value, column);\n  }\n  return value;\n}\nconst eq = (left, right) => {\n  return sql`${left} = ${bindIfParam(right, left)}`;\n};\nconst ne = (left, right) => {\n  return sql`${left} <> ${bindIfParam(right, left)}`;\n};\nfunction and(...unfilteredConditions) {\n  const conditions = unfilteredConditions.filter(\n    (c) => c !== void 0\n  );\n  if (conditions.length === 0) {\n    return void 0;\n  }\n  if (conditions.length === 1) {\n    return new SQL(conditions);\n  }\n  return new SQL([\n    new StringChunk(\"(\"),\n    sql.join(conditions, new StringChunk(\" and \")),\n    new StringChunk(\")\")\n  ]);\n}\nfunction or(...unfilteredConditions) {\n  const conditions = unfilteredConditions.filter(\n    (c) => c !== void 0\n  );\n  if (conditions.length === 0) {\n    return void 0;\n  }\n  if (conditions.length === 1) {\n    return new SQL(conditions);\n  }\n  return new SQL([\n    new StringChunk(\"(\"),\n    sql.join(conditions, new StringChunk(\" or \")),\n    new StringChunk(\")\")\n  ]);\n}\nfunction not(condition) {\n  return sql`not ${condition}`;\n}\nconst gt = (left, right) => {\n  return sql`${left} > ${bindIfParam(right, left)}`;\n};\nconst gte = (left, right) => {\n  return sql`${left} >= ${bindIfParam(right, left)}`;\n};\nconst lt = (left, right) => {\n  return sql`${left} < ${bindIfParam(right, left)}`;\n};\nconst lte = (left, right) => {\n  return sql`${left} <= ${bindIfParam(right, left)}`;\n};\nfunction inArray(column, values) {\n  if (Array.isArray(values)) {\n    if (values.length === 0) {\n      return sql`false`;\n    }\n    return sql`${column} in ${values.map((v) => bindIfParam(v, column))}`;\n  }\n  return sql`${column} in ${bindIfParam(values, column)}`;\n}\nfunction notInArray(column, values) {\n  if (Array.isArray(values)) {\n    if (values.length === 0) {\n      return sql`true`;\n    }\n    return sql`${column} not in ${values.map((v) => bindIfParam(v, column))}`;\n  }\n  return sql`${column} not in ${bindIfParam(values, column)}`;\n}\nfunction isNull(value) {\n  return sql`${value} is null`;\n}\nfunction isNotNull(value) {\n  return sql`${value} is not null`;\n}\nfunction exists(subquery) {\n  return sql`exists ${subquery}`;\n}\nfunction notExists(subquery) {\n  return sql`not exists ${subquery}`;\n}\nfunction between(column, min, max) {\n  return sql`${column} between ${bindIfParam(min, column)} and ${bindIfParam(\n    max,\n    column\n  )}`;\n}\nfunction notBetween(column, min, max) {\n  return sql`${column} not between ${bindIfParam(\n    min,\n    column\n  )} and ${bindIfParam(max, column)}`;\n}\nfunction like(column, value) {\n  return sql`${column} like ${value}`;\n}\nfunction notLike(column, value) {\n  return sql`${column} not like ${value}`;\n}\nfunction ilike(column, value) {\n  return sql`${column} ilike ${value}`;\n}\nfunction notIlike(column, value) {\n  return sql`${column} not ilike ${value}`;\n}\nfunction arrayContains(column, values) {\n  if (Array.isArray(values)) {\n    if (values.length === 0) {\n      throw new Error(\"arrayContains requires at least one value\");\n    }\n    const array = sql`${bindIfParam(values, column)}`;\n    return sql`${column} @> ${array}`;\n  }\n  return sql`${column} @> ${bindIfParam(values, column)}`;\n}\nfunction arrayContained(column, values) {\n  if (Array.isArray(values)) {\n    if (values.length === 0) {\n      throw new Error(\"arrayContained requires at least one value\");\n    }\n    const array = sql`${bindIfParam(values, column)}`;\n    return sql`${column} <@ ${array}`;\n  }\n  return sql`${column} <@ ${bindIfParam(values, column)}`;\n}\nfunction arrayOverlaps(column, values) {\n  if (Array.isArray(values)) {\n    if (values.length === 0) {\n      throw new Error(\"arrayOverlaps requires at least one value\");\n    }\n    const array = sql`${bindIfParam(values, column)}`;\n    return sql`${column} && ${array}`;\n  }\n  return sql`${column} && ${bindIfParam(values, column)}`;\n}\nexport {\n  and,\n  arrayContained,\n  arrayContains,\n  arrayOverlaps,\n  between,\n  bindIfParam,\n  eq,\n  exists,\n  gt,\n  gte,\n  ilike,\n  inArray,\n  isNotNull,\n  isNull,\n  like,\n  lt,\n  lte,\n  ne,\n  not,\n  notBetween,\n  notExists,\n  notIlike,\n  notInArray,\n  notLike,\n  or\n};\n//# sourceMappingURL=conditions.js.map", "import { sql } from \"../sql.js\";\nfunction asc(column) {\n  return sql`${column} asc`;\n}\nfunction desc(column) {\n  return sql`${column} desc`;\n}\nexport {\n  asc,\n  desc\n};\n//# sourceMappingURL=select.js.map", "import { entityKind } from \"../../entity.js\";\nimport { PgColumn, PgColumnBuilder } from \"./common.js\";\nconst isPgEnumSym = Symbol.for(\"drizzle:isPgEnum\");\nfunction isPgEnum(obj) {\n  return !!obj && typeof obj === \"function\" && isPgEnumSym in obj && obj[isPgEnumSym] === true;\n}\nclass PgEnumColumnBuilder extends PgColumnBuilder {\n  static [entityKind] = \"PgEnumColumnBuilder\";\n  constructor(name, enumInstance) {\n    super(name, \"string\", \"PgEnumColumn\");\n    this.config.enum = enumInstance;\n  }\n  /** @internal */\n  build(table) {\n    return new PgEnumColumn(\n      table,\n      this.config\n    );\n  }\n}\nclass PgEnumColumn extends PgColumn {\n  static [entityKind] = \"PgEnumColumn\";\n  enum = this.config.enum;\n  enumValues = this.config.enum.enumValues;\n  constructor(table, config) {\n    super(table, config);\n    this.enum = config.enum;\n  }\n  getSQLType() {\n    return this.enum.enumName;\n  }\n}\nfunction pgEnum(enumName, values) {\n  return pgEnumWithSchema(enumName, values, void 0);\n}\nfunction pgEnumWithSchema(enumName, values, schema) {\n  const enumInstance = Object.assign(\n    (name) => new PgEnumColumnBuilder(name ?? \"\", enumInstance),\n    {\n      enumName,\n      enumValues: values,\n      schema,\n      [isPgEnumSym]: true\n    }\n  );\n  return enumInstance;\n}\nexport {\n  PgEnumColumn,\n  PgEnumColumnBuilder,\n  isPgEnum,\n  pgEnum,\n  pgEnumWithSchema\n};\n//# sourceMappingURL=enum.js.map", "import { entityKind, is } from \"../entity.js\";\nimport { isPgEnum } from \"../pg-core/columns/enum.js\";\nimport { Subquery } from \"../subquery.js\";\nimport { tracer } from \"../tracing.js\";\nimport { ViewBaseConfig } from \"../view-common.js\";\nimport { Column } from \"../column.js\";\nimport { IsAlias, Table } from \"../table.js\";\nclass FakePrimitiveParam {\n  static [entityKind] = \"FakePrimitiveParam\";\n}\nfunction isSQLWrapper(value) {\n  return value !== null && value !== void 0 && typeof value.getSQL === \"function\";\n}\nfunction mergeQueries(queries) {\n  const result = { sql: \"\", params: [] };\n  for (const query of queries) {\n    result.sql += query.sql;\n    result.params.push(...query.params);\n    if (query.typings?.length) {\n      if (!result.typings) {\n        result.typings = [];\n      }\n      result.typings.push(...query.typings);\n    }\n  }\n  return result;\n}\nclass StringChunk {\n  static [entityKind] = \"StringChunk\";\n  value;\n  constructor(value) {\n    this.value = Array.isArray(value) ? value : [value];\n  }\n  getSQL() {\n    return new SQL([this]);\n  }\n}\nclass SQL {\n  constructor(queryChunks) {\n    this.queryChunks = queryChunks;\n  }\n  static [entityKind] = \"SQL\";\n  /** @internal */\n  decoder = noopDecoder;\n  shouldInlineParams = false;\n  append(query) {\n    this.queryChunks.push(...query.queryChunks);\n    return this;\n  }\n  toQuery(config) {\n    return tracer.startActiveSpan(\"drizzle.buildSQL\", (span) => {\n      const query = this.buildQueryFromSourceParams(this.queryChunks, config);\n      span?.setAttributes({\n        \"drizzle.query.text\": query.sql,\n        \"drizzle.query.params\": JSON.stringify(query.params)\n      });\n      return query;\n    });\n  }\n  buildQueryFromSourceParams(chunks, _config) {\n    const config = Object.assign({}, _config, {\n      inlineParams: _config.inlineParams || this.shouldInlineParams,\n      paramStartIndex: _config.paramStartIndex || { value: 0 }\n    });\n    const {\n      casing,\n      escapeName,\n      escapeParam,\n      prepareTyping,\n      inlineParams,\n      paramStartIndex\n    } = config;\n    return mergeQueries(chunks.map((chunk) => {\n      if (is(chunk, StringChunk)) {\n        return { sql: chunk.value.join(\"\"), params: [] };\n      }\n      if (is(chunk, Name)) {\n        return { sql: escapeName(chunk.value), params: [] };\n      }\n      if (chunk === void 0) {\n        return { sql: \"\", params: [] };\n      }\n      if (Array.isArray(chunk)) {\n        const result = [new StringChunk(\"(\")];\n        for (const [i, p] of chunk.entries()) {\n          result.push(p);\n          if (i < chunk.length - 1) {\n            result.push(new StringChunk(\", \"));\n          }\n        }\n        result.push(new StringChunk(\")\"));\n        return this.buildQueryFromSourceParams(result, config);\n      }\n      if (is(chunk, SQL)) {\n        return this.buildQueryFromSourceParams(chunk.queryChunks, {\n          ...config,\n          inlineParams: inlineParams || chunk.shouldInlineParams\n        });\n      }\n      if (is(chunk, Table)) {\n        const schemaName = chunk[Table.Symbol.Schema];\n        const tableName = chunk[Table.Symbol.Name];\n        return {\n          sql: schemaName === void 0 ? escapeName(tableName) : escapeName(schemaName) + \".\" + escapeName(tableName),\n          params: []\n        };\n      }\n      if (is(chunk, Column)) {\n        const columnName = casing.getColumnCasing(chunk);\n        if (_config.invokeSource === \"indexes\") {\n          return { sql: escapeName(columnName), params: [] };\n        }\n        const schemaName = chunk.table[Table.Symbol.Schema];\n        return {\n          sql: chunk.table[IsAlias] || schemaName === void 0 ? escapeName(chunk.table[Table.Symbol.Name]) + \".\" + escapeName(columnName) : escapeName(schemaName) + \".\" + escapeName(chunk.table[Table.Symbol.Name]) + \".\" + escapeName(columnName),\n          params: []\n        };\n      }\n      if (is(chunk, View)) {\n        const schemaName = chunk[ViewBaseConfig].schema;\n        const viewName = chunk[ViewBaseConfig].name;\n        return {\n          sql: schemaName === void 0 ? escapeName(viewName) : escapeName(schemaName) + \".\" + escapeName(viewName),\n          params: []\n        };\n      }\n      if (is(chunk, Param)) {\n        if (is(chunk.value, Placeholder)) {\n          return { sql: escapeParam(paramStartIndex.value++, chunk), params: [chunk], typings: [\"none\"] };\n        }\n        const mappedValue = chunk.value === null ? null : chunk.encoder.mapToDriverValue(chunk.value);\n        if (is(mappedValue, SQL)) {\n          return this.buildQueryFromSourceParams([mappedValue], config);\n        }\n        if (inlineParams) {\n          return { sql: this.mapInlineParam(mappedValue, config), params: [] };\n        }\n        let typings = [\"none\"];\n        if (prepareTyping) {\n          typings = [prepareTyping(chunk.encoder)];\n        }\n        return { sql: escapeParam(paramStartIndex.value++, mappedValue), params: [mappedValue], typings };\n      }\n      if (is(chunk, Placeholder)) {\n        return { sql: escapeParam(paramStartIndex.value++, chunk), params: [chunk], typings: [\"none\"] };\n      }\n      if (is(chunk, SQL.Aliased) && chunk.fieldAlias !== void 0) {\n        return { sql: escapeName(chunk.fieldAlias), params: [] };\n      }\n      if (is(chunk, Subquery)) {\n        if (chunk._.isWith) {\n          return { sql: escapeName(chunk._.alias), params: [] };\n        }\n        return this.buildQueryFromSourceParams([\n          new StringChunk(\"(\"),\n          chunk._.sql,\n          new StringChunk(\") \"),\n          new Name(chunk._.alias)\n        ], config);\n      }\n      if (isPgEnum(chunk)) {\n        if (chunk.schema) {\n          return { sql: escapeName(chunk.schema) + \".\" + escapeName(chunk.enumName), params: [] };\n        }\n        return { sql: escapeName(chunk.enumName), params: [] };\n      }\n      if (isSQLWrapper(chunk)) {\n        if (chunk.shouldOmitSQLParens?.()) {\n          return this.buildQueryFromSourceParams([chunk.getSQL()], config);\n        }\n        return this.buildQueryFromSourceParams([\n          new StringChunk(\"(\"),\n          chunk.getSQL(),\n          new StringChunk(\")\")\n        ], config);\n      }\n      if (inlineParams) {\n        return { sql: this.mapInlineParam(chunk, config), params: [] };\n      }\n      return { sql: escapeParam(paramStartIndex.value++, chunk), params: [chunk], typings: [\"none\"] };\n    }));\n  }\n  mapInlineParam(chunk, { escapeString }) {\n    if (chunk === null) {\n      return \"null\";\n    }\n    if (typeof chunk === \"number\" || typeof chunk === \"boolean\") {\n      return chunk.toString();\n    }\n    if (typeof chunk === \"string\") {\n      return escapeString(chunk);\n    }\n    if (typeof chunk === \"object\") {\n      const mappedValueAsString = chunk.toString();\n      if (mappedValueAsString === \"[object Object]\") {\n        return escapeString(JSON.stringify(chunk));\n      }\n      return escapeString(mappedValueAsString);\n    }\n    throw new Error(\"Unexpected param value: \" + chunk);\n  }\n  getSQL() {\n    return this;\n  }\n  as(alias) {\n    if (alias === void 0) {\n      return this;\n    }\n    return new SQL.Aliased(this, alias);\n  }\n  mapWith(decoder) {\n    this.decoder = typeof decoder === \"function\" ? { mapFromDriverValue: decoder } : decoder;\n    return this;\n  }\n  inlineParams() {\n    this.shouldInlineParams = true;\n    return this;\n  }\n  /**\n   * This method is used to conditionally include a part of the query.\n   *\n   * @param condition - Condition to check\n   * @returns itself if the condition is `true`, otherwise `undefined`\n   */\n  if(condition) {\n    return condition ? this : void 0;\n  }\n}\nclass Name {\n  constructor(value) {\n    this.value = value;\n  }\n  static [entityKind] = \"Name\";\n  brand;\n  getSQL() {\n    return new SQL([this]);\n  }\n}\nfunction name(value) {\n  return new Name(value);\n}\nfunction isDriverValueEncoder(value) {\n  return typeof value === \"object\" && value !== null && \"mapToDriverValue\" in value && typeof value.mapToDriverValue === \"function\";\n}\nconst noopDecoder = {\n  mapFromDriverValue: (value) => value\n};\nconst noopEncoder = {\n  mapToDriverValue: (value) => value\n};\nconst noopMapper = {\n  ...noopDecoder,\n  ...noopEncoder\n};\nclass Param {\n  /**\n   * @param value - Parameter value\n   * @param encoder - Encoder to convert the value to a driver parameter\n   */\n  constructor(value, encoder = noopEncoder) {\n    this.value = value;\n    this.encoder = encoder;\n  }\n  static [entityKind] = \"Param\";\n  brand;\n  getSQL() {\n    return new SQL([this]);\n  }\n}\nfunction param(value, encoder) {\n  return new Param(value, encoder);\n}\nfunction sql(strings, ...params) {\n  const queryChunks = [];\n  if (params.length > 0 || strings.length > 0 && strings[0] !== \"\") {\n    queryChunks.push(new StringChunk(strings[0]));\n  }\n  for (const [paramIndex, param2] of params.entries()) {\n    queryChunks.push(param2, new StringChunk(strings[paramIndex + 1]));\n  }\n  return new SQL(queryChunks);\n}\n((sql2) => {\n  function empty() {\n    return new SQL([]);\n  }\n  sql2.empty = empty;\n  function fromList(list) {\n    return new SQL(list);\n  }\n  sql2.fromList = fromList;\n  function raw(str) {\n    return new SQL([new StringChunk(str)]);\n  }\n  sql2.raw = raw;\n  function join(chunks, separator) {\n    const result = [];\n    for (const [i, chunk] of chunks.entries()) {\n      if (i > 0 && separator !== void 0) {\n        result.push(separator);\n      }\n      result.push(chunk);\n    }\n    return new SQL(result);\n  }\n  sql2.join = join;\n  function identifier(value) {\n    return new Name(value);\n  }\n  sql2.identifier = identifier;\n  function placeholder2(name2) {\n    return new Placeholder(name2);\n  }\n  sql2.placeholder = placeholder2;\n  function param2(value, encoder) {\n    return new Param(value, encoder);\n  }\n  sql2.param = param2;\n})(sql || (sql = {}));\n((SQL2) => {\n  class Aliased {\n    constructor(sql2, fieldAlias) {\n      this.sql = sql2;\n      this.fieldAlias = fieldAlias;\n    }\n    static [entityKind] = \"SQL.Aliased\";\n    /** @internal */\n    isSelectionField = false;\n    getSQL() {\n      return this.sql;\n    }\n    /** @internal */\n    clone() {\n      return new Aliased(this.sql, this.fieldAlias);\n    }\n  }\n  SQL2.Aliased = Aliased;\n})(SQL || (SQL = {}));\nclass Placeholder {\n  constructor(name2) {\n    this.name = name2;\n  }\n  static [entityKind] = \"Placeholder\";\n  getSQL() {\n    return new SQL([this]);\n  }\n}\nfunction placeholder(name2) {\n  return new Placeholder(name2);\n}\nfunction fillPlaceholders(params, values) {\n  return params.map((p) => {\n    if (is(p, Placeholder)) {\n      if (!(p.name in values)) {\n        throw new Error(`No value for placeholder \"${p.name}\" was provided`);\n      }\n      return values[p.name];\n    }\n    if (is(p, Param) && is(p.value, Placeholder)) {\n      if (!(p.value.name in values)) {\n        throw new Error(`No value for placeholder \"${p.value.name}\" was provided`);\n      }\n      return p.encoder.mapToDriverValue(values[p.value.name]);\n    }\n    return p;\n  });\n}\nclass View {\n  static [entityKind] = \"View\";\n  /** @internal */\n  [ViewBaseConfig];\n  constructor({ name: name2, schema, selectedFields, query }) {\n    this[ViewBaseConfig] = {\n      name: name2,\n      originalName: name2,\n      schema,\n      selectedFields,\n      query,\n      isExisting: !query,\n      isAlias: false\n    };\n  }\n  getSQL() {\n    return new SQL([this]);\n  }\n}\nColumn.prototype.getSQL = function() {\n  return new SQL([this]);\n};\nTable.prototype.getSQL = function() {\n  return new SQL([this]);\n};\nSubquery.prototype.getSQL = function() {\n  return new SQL([this]);\n};\nexport {\n  FakePrimitiveParam,\n  Name,\n  Param,\n  Placeholder,\n  SQL,\n  StringChunk,\n  View,\n  fillPlaceholders,\n  isDriverValueEncoder,\n  isSQLWrapper,\n  name,\n  noopDecoder,\n  noopEncoder,\n  noopMapper,\n  param,\n  placeholder,\n  sql\n};\n//# sourceMappingURL=sql.js.map", "import { entityKind } from \"../entity.js\";\nimport { TableName } from \"../table.utils.js\";\nclass ForeignKeyBuilder {\n  static [entityKind] = \"SQLiteForeignKeyBuilder\";\n  /** @internal */\n  reference;\n  /** @internal */\n  _onUpdate;\n  /** @internal */\n  _onDelete;\n  constructor(config, actions) {\n    this.reference = () => {\n      const { name, columns, foreignColumns } = config();\n      return { name, columns, foreignTable: foreignColumns[0].table, foreignColumns };\n    };\n    if (actions) {\n      this._onUpdate = actions.onUpdate;\n      this._onDelete = actions.onDelete;\n    }\n  }\n  onUpdate(action) {\n    this._onUpdate = action;\n    return this;\n  }\n  onDelete(action) {\n    this._onDelete = action;\n    return this;\n  }\n  /** @internal */\n  build(table) {\n    return new ForeignKey(table, this);\n  }\n}\nclass ForeignKey {\n  constructor(table, builder) {\n    this.table = table;\n    this.reference = builder.reference;\n    this.onUpdate = builder._onUpdate;\n    this.onDelete = builder._onDelete;\n  }\n  static [entityKind] = \"SQLiteForeignKey\";\n  reference;\n  onUpdate;\n  onDelete;\n  getName() {\n    const { name, columns, foreignColumns } = this.reference();\n    const columnNames = columns.map((column) => column.name);\n    const foreignColumnNames = foreignColumns.map((column) => column.name);\n    const chunks = [\n      this.table[TableName],\n      ...columnNames,\n      foreignColumns[0].table[TableName],\n      ...foreignColumnNames\n    ];\n    return name ?? `${chunks.join(\"_\")}_fk`;\n  }\n}\nfunction foreignKey(config) {\n  function mappedConfig() {\n    if (typeof config === \"function\") {\n      const { name, columns, foreignColumns } = config();\n      return {\n        name,\n        columns,\n        foreignColumns\n      };\n    }\n    return config;\n  }\n  return new ForeignKeyBuilder(mappedConfig);\n}\nexport {\n  ForeignKey,\n  ForeignKeyBuilder,\n  foreignKey\n};\n//# sourceMappingURL=foreign-keys.js.map", "import { entityKind } from \"../entity.js\";\nimport { TableName } from \"../table.utils.js\";\nfunction uniqueKeyName(table, columns) {\n  return `${table[TableName]}_${columns.join(\"_\")}_unique`;\n}\nfunction unique(name) {\n  return new UniqueOnConstraintBuilder(name);\n}\nclass UniqueConstraintBuilder {\n  constructor(columns, name) {\n    this.name = name;\n    this.columns = columns;\n  }\n  static [entityKind] = \"SQLiteUniqueConstraintBuilder\";\n  /** @internal */\n  columns;\n  /** @internal */\n  build(table) {\n    return new UniqueConstraint(table, this.columns, this.name);\n  }\n}\nclass UniqueOnConstraintBuilder {\n  static [entityKind] = \"SQLiteUniqueOnConstraintBuilder\";\n  /** @internal */\n  name;\n  constructor(name) {\n    this.name = name;\n  }\n  on(...columns) {\n    return new UniqueConstraintBuilder(columns, this.name);\n  }\n}\nclass UniqueConstraint {\n  constructor(table, columns, name) {\n    this.table = table;\n    this.columns = columns;\n    this.name = name ?? uniqueKeyName(this.table, this.columns.map((column) => column.name));\n  }\n  static [entityKind] = \"SQLiteUniqueConstraint\";\n  columns;\n  name;\n  getName() {\n    return this.name;\n  }\n}\nexport {\n  UniqueConstraint,\n  UniqueConstraintBuilder,\n  UniqueOnConstraintBuilder,\n  unique,\n  uniqueKeyName\n};\n//# sourceMappingURL=unique-constraint.js.map", "import { ColumnBuilder } from \"../../column-builder.js\";\nimport { Column } from \"../../column.js\";\nimport { entityKind } from \"../../entity.js\";\nimport { ForeignKeyBuilder } from \"../foreign-keys.js\";\nimport { uniqueKeyName } from \"../unique-constraint.js\";\nclass SQLiteColumnBuilder extends ColumnBuilder {\n  static [entityKind] = \"SQLiteColumnBuilder\";\n  foreignKeyConfigs = [];\n  references(ref, actions = {}) {\n    this.foreignKeyConfigs.push({ ref, actions });\n    return this;\n  }\n  unique(name) {\n    this.config.isUnique = true;\n    this.config.uniqueName = name;\n    return this;\n  }\n  generatedAlwaysAs(as, config) {\n    this.config.generated = {\n      as,\n      type: \"always\",\n      mode: config?.mode ?? \"virtual\"\n    };\n    return this;\n  }\n  /** @internal */\n  buildForeignKeys(column, table) {\n    return this.foreignKeyConfigs.map(({ ref, actions }) => {\n      return ((ref2, actions2) => {\n        const builder = new ForeignKeyBuilder(() => {\n          const foreignColumn = ref2();\n          return { columns: [column], foreignColumns: [foreignColumn] };\n        });\n        if (actions2.onUpdate) {\n          builder.onUpdate(actions2.onUpdate);\n        }\n        if (actions2.onDelete) {\n          builder.onDelete(actions2.onDelete);\n        }\n        return builder.build(table);\n      })(ref, actions);\n    });\n  }\n}\nclass SQLiteColumn extends Column {\n  constructor(table, config) {\n    if (!config.uniqueName) {\n      config.uniqueName = uniqueKeyName(table, [config.name]);\n    }\n    super(table, config);\n    this.table = table;\n  }\n  static [entityKind] = \"SQLiteColumn\";\n}\nexport {\n  SQLiteColumn,\n  SQLiteColumnBuilder\n};\n//# sourceMappingURL=common.js.map", "import { entityKind } from \"../../entity.js\";\nimport { sql } from \"../../sql/sql.js\";\nimport { getColumnNameAndConfig } from \"../../utils.js\";\nimport { SQLiteColumn, SQLiteColumnBuilder } from \"./common.js\";\nclass SQLiteBaseIntegerBuilder extends SQLiteColumnBuilder {\n  static [entityKind] = \"SQLiteBaseIntegerBuilder\";\n  constructor(name, dataType, columnType) {\n    super(name, dataType, columnType);\n    this.config.autoIncrement = false;\n  }\n  primaryKey(config) {\n    if (config?.autoIncrement) {\n      this.config.autoIncrement = true;\n    }\n    this.config.hasDefault = true;\n    return super.primaryKey();\n  }\n}\nclass SQLiteBaseInteger extends SQLiteColumn {\n  static [entityKind] = \"SQLiteBaseInteger\";\n  autoIncrement = this.config.autoIncrement;\n  getSQLType() {\n    return \"integer\";\n  }\n}\nclass SQLiteIntegerBuilder extends SQLiteBaseIntegerBuilder {\n  static [entityKind] = \"SQLiteIntegerBuilder\";\n  constructor(name) {\n    super(name, \"number\", \"SQLiteInteger\");\n  }\n  build(table) {\n    return new SQLiteInteger(\n      table,\n      this.config\n    );\n  }\n}\nclass SQLiteInteger extends SQLiteBaseInteger {\n  static [entityKind] = \"SQLiteInteger\";\n}\nclass SQLiteTimestampBuilder extends SQLiteBaseIntegerBuilder {\n  static [entityKind] = \"SQLiteTimestampBuilder\";\n  constructor(name, mode) {\n    super(name, \"date\", \"SQLiteTimestamp\");\n    this.config.mode = mode;\n  }\n  /**\n   * @deprecated Use `default()` with your own expression instead.\n   *\n   * Adds `DEFAULT (cast((julianday('now') - 2440587.5)*86400000 as integer))` to the column, which is the current epoch timestamp in milliseconds.\n   */\n  defaultNow() {\n    return this.default(sql`(cast((julianday('now') - 2440587.5)*86400000 as integer))`);\n  }\n  build(table) {\n    return new SQLiteTimestamp(\n      table,\n      this.config\n    );\n  }\n}\nclass SQLiteTimestamp extends SQLiteBaseInteger {\n  static [entityKind] = \"SQLiteTimestamp\";\n  mode = this.config.mode;\n  mapFromDriverValue(value) {\n    if (this.config.mode === \"timestamp\") {\n      return new Date(value * 1e3);\n    }\n    return new Date(value);\n  }\n  mapToDriverValue(value) {\n    const unix = value.getTime();\n    if (this.config.mode === \"timestamp\") {\n      return Math.floor(unix / 1e3);\n    }\n    return unix;\n  }\n}\nclass SQLiteBooleanBuilder extends SQLiteBaseIntegerBuilder {\n  static [entityKind] = \"SQLiteBooleanBuilder\";\n  constructor(name, mode) {\n    super(name, \"boolean\", \"SQLiteBoolean\");\n    this.config.mode = mode;\n  }\n  build(table) {\n    return new SQLiteBoolean(\n      table,\n      this.config\n    );\n  }\n}\nclass SQLiteBoolean extends SQLiteBaseInteger {\n  static [entityKind] = \"SQLiteBoolean\";\n  mode = this.config.mode;\n  mapFromDriverValue(value) {\n    return Number(value) === 1;\n  }\n  mapToDriverValue(value) {\n    return value ? 1 : 0;\n  }\n}\nfunction integer(a, b) {\n  const { name, config } = getColumnNameAndConfig(a, b);\n  if (config?.mode === \"timestamp\" || config?.mode === \"timestamp_ms\") {\n    return new SQLiteTimestampBuilder(name, config.mode);\n  }\n  if (config?.mode === \"boolean\") {\n    return new SQLiteBooleanBuilder(name, config.mode);\n  }\n  return new SQLiteIntegerBuilder(name);\n}\nconst int = integer;\nexport {\n  SQLiteBaseInteger,\n  SQLiteBaseIntegerBuilder,\n  SQLiteBoolean,\n  SQLiteBooleanBuilder,\n  SQLiteInteger,\n  SQLiteIntegerBuilder,\n  SQLiteTimestamp,\n  SQLiteTimestampBuilder,\n  int,\n  integer\n};\n//# sourceMappingURL=integer.js.map", "import { entityKind } from \"../../entity.js\";\nimport { getColumnNameAndConfig } from \"../../utils.js\";\nimport { SQLiteColumn, SQLiteColumnBuilder } from \"./common.js\";\nclass SQLiteTextBuilder extends SQLiteColumnBuilder {\n  static [entityKind] = \"SQLiteTextBuilder\";\n  constructor(name, config) {\n    super(name, \"string\", \"SQLiteText\");\n    this.config.enumValues = config.enum;\n    this.config.length = config.length;\n  }\n  /** @internal */\n  build(table) {\n    return new SQLiteText(table, this.config);\n  }\n}\nclass SQLiteText extends SQLiteColumn {\n  static [entityKind] = \"SQLiteText\";\n  enumValues = this.config.enumValues;\n  length = this.config.length;\n  constructor(table, config) {\n    super(table, config);\n  }\n  getSQLType() {\n    return `text${this.config.length ? `(${this.config.length})` : \"\"}`;\n  }\n}\nclass SQLiteTextJsonBuilder extends SQLiteColumnBuilder {\n  static [entityKind] = \"SQLiteTextJsonBuilder\";\n  constructor(name) {\n    super(name, \"json\", \"SQLiteTextJson\");\n  }\n  /** @internal */\n  build(table) {\n    return new SQLiteTextJson(\n      table,\n      this.config\n    );\n  }\n}\nclass SQLiteTextJson extends SQLiteColumn {\n  static [entityKind] = \"SQLiteTextJson\";\n  getSQLType() {\n    return \"text\";\n  }\n  mapFromDriverValue(value) {\n    return JSON.parse(value);\n  }\n  mapToDriverValue(value) {\n    return JSON.stringify(value);\n  }\n}\nfunction text(a, b = {}) {\n  const { name, config } = getColumnNameAndConfig(a, b);\n  if (config.mode === \"json\") {\n    return new SQLiteTextJsonBuilder(name);\n  }\n  return new SQLiteTextBuilder(name, config);\n}\nexport {\n  SQLiteText,\n  SQLiteTextBuilder,\n  SQLiteTextJson,\n  SQLiteTextJsonBuilder,\n  text\n};\n//# sourceMappingURL=text.js.map", "import { entityKind, is } from \"../../entity.js\";\nimport { TypedQueryBuilder } from \"../../query-builders/query-builder.js\";\nimport { QueryPromise } from \"../../query-promise.js\";\nimport { SelectionProxyHandler } from \"../../selection-proxy.js\";\nimport { SQL, View } from \"../../sql/sql.js\";\nimport { Subquery } from \"../../subquery.js\";\nimport { Table } from \"../../table.js\";\nimport {\n  applyMixins,\n  getTableColumns,\n  getTableLikeName,\n  haveSameKeys,\n  orderSelectedFields\n} from \"../../utils.js\";\nimport { ViewBaseConfig } from \"../../view-common.js\";\nimport { SQLiteViewBase } from \"../view-base.js\";\nclass SQLiteSelectBuilder {\n  static [entityKind] = \"SQLiteSelectBuilder\";\n  fields;\n  session;\n  dialect;\n  withList;\n  distinct;\n  constructor(config) {\n    this.fields = config.fields;\n    this.session = config.session;\n    this.dialect = config.dialect;\n    this.withList = config.withList;\n    this.distinct = config.distinct;\n  }\n  from(source) {\n    const isPartialSelect = !!this.fields;\n    let fields;\n    if (this.fields) {\n      fields = this.fields;\n    } else if (is(source, Subquery)) {\n      fields = Object.fromEntries(\n        Object.keys(source._.selectedFields).map((key) => [key, source[key]])\n      );\n    } else if (is(source, SQLiteViewBase)) {\n      fields = source[ViewBaseConfig].selectedFields;\n    } else if (is(source, SQL)) {\n      fields = {};\n    } else {\n      fields = getTableColumns(source);\n    }\n    return new SQLiteSelectBase({\n      table: source,\n      fields,\n      isPartialSelect,\n      session: this.session,\n      dialect: this.dialect,\n      withList: this.withList,\n      distinct: this.distinct\n    });\n  }\n}\nclass SQLiteSelectQueryBuilderBase extends TypedQueryBuilder {\n  static [entityKind] = \"SQLiteSelectQueryBuilder\";\n  _;\n  /** @internal */\n  config;\n  joinsNotNullableMap;\n  tableName;\n  isPartialSelect;\n  session;\n  dialect;\n  constructor({ table, fields, isPartialSelect, session, dialect, withList, distinct }) {\n    super();\n    this.config = {\n      withList,\n      table,\n      fields: { ...fields },\n      distinct,\n      setOperators: []\n    };\n    this.isPartialSelect = isPartialSelect;\n    this.session = session;\n    this.dialect = dialect;\n    this._ = {\n      selectedFields: fields\n    };\n    this.tableName = getTableLikeName(table);\n    this.joinsNotNullableMap = typeof this.tableName === \"string\" ? { [this.tableName]: true } : {};\n  }\n  createJoin(joinType) {\n    return (table, on) => {\n      const baseTableName = this.tableName;\n      const tableName = getTableLikeName(table);\n      if (typeof tableName === \"string\" && this.config.joins?.some((join) => join.alias === tableName)) {\n        throw new Error(`Alias \"${tableName}\" is already used in this query`);\n      }\n      if (!this.isPartialSelect) {\n        if (Object.keys(this.joinsNotNullableMap).length === 1 && typeof baseTableName === \"string\") {\n          this.config.fields = {\n            [baseTableName]: this.config.fields\n          };\n        }\n        if (typeof tableName === \"string\" && !is(table, SQL)) {\n          const selection = is(table, Subquery) ? table._.selectedFields : is(table, View) ? table[ViewBaseConfig].selectedFields : table[Table.Symbol.Columns];\n          this.config.fields[tableName] = selection;\n        }\n      }\n      if (typeof on === \"function\") {\n        on = on(\n          new Proxy(\n            this.config.fields,\n            new SelectionProxyHandler({ sqlAliasedBehavior: \"sql\", sqlBehavior: \"sql\" })\n          )\n        );\n      }\n      if (!this.config.joins) {\n        this.config.joins = [];\n      }\n      this.config.joins.push({ on, table, joinType, alias: tableName });\n      if (typeof tableName === \"string\") {\n        switch (joinType) {\n          case \"left\": {\n            this.joinsNotNullableMap[tableName] = false;\n            break;\n          }\n          case \"right\": {\n            this.joinsNotNullableMap = Object.fromEntries(\n              Object.entries(this.joinsNotNullableMap).map(([key]) => [key, false])\n            );\n            this.joinsNotNullableMap[tableName] = true;\n            break;\n          }\n          case \"inner\": {\n            this.joinsNotNullableMap[tableName] = true;\n            break;\n          }\n          case \"full\": {\n            this.joinsNotNullableMap = Object.fromEntries(\n              Object.entries(this.joinsNotNullableMap).map(([key]) => [key, false])\n            );\n            this.joinsNotNullableMap[tableName] = false;\n            break;\n          }\n        }\n      }\n      return this;\n    };\n  }\n  /**\n   * Executes a `left join` operation by adding another table to the current query.\n   *\n   * Calling this method associates each row of the table with the corresponding row from the joined table, if a match is found. If no matching row exists, it sets all columns of the joined table to null.\n   *\n   * See docs: {@link https://orm.drizzle.team/docs/joins#left-join}\n   *\n   * @param table the table to join.\n   * @param on the `on` clause.\n   *\n   * @example\n   *\n   * ```ts\n   * // Select all users and their pets\n   * const usersWithPets: { user: User; pets: Pet | null }[] = await db.select()\n   *   .from(users)\n   *   .leftJoin(pets, eq(users.id, pets.ownerId))\n   *\n   * // Select userId and petId\n   * const usersIdsAndPetIds: { userId: number; petId: number | null }[] = await db.select({\n   *   userId: users.id,\n   *   petId: pets.id,\n   * })\n   *   .from(users)\n   *   .leftJoin(pets, eq(users.id, pets.ownerId))\n   * ```\n   */\n  leftJoin = this.createJoin(\"left\");\n  /**\n   * Executes a `right join` operation by adding another table to the current query.\n   *\n   * Calling this method associates each row of the joined table with the corresponding row from the main table, if a match is found. If no matching row exists, it sets all columns of the main table to null.\n   *\n   * See docs: {@link https://orm.drizzle.team/docs/joins#right-join}\n   *\n   * @param table the table to join.\n   * @param on the `on` clause.\n   *\n   * @example\n   *\n   * ```ts\n   * // Select all users and their pets\n   * const usersWithPets: { user: User | null; pets: Pet }[] = await db.select()\n   *   .from(users)\n   *   .rightJoin(pets, eq(users.id, pets.ownerId))\n   *\n   * // Select userId and petId\n   * const usersIdsAndPetIds: { userId: number | null; petId: number }[] = await db.select({\n   *   userId: users.id,\n   *   petId: pets.id,\n   * })\n   *   .from(users)\n   *   .rightJoin(pets, eq(users.id, pets.ownerId))\n   * ```\n   */\n  rightJoin = this.createJoin(\"right\");\n  /**\n   * Executes an `inner join` operation, creating a new table by combining rows from two tables that have matching values.\n   *\n   * Calling this method retrieves rows that have corresponding entries in both joined tables. Rows without matching entries in either table are excluded, resulting in a table that includes only matching pairs.\n   *\n   * See docs: {@link https://orm.drizzle.team/docs/joins#inner-join}\n   *\n   * @param table the table to join.\n   * @param on the `on` clause.\n   *\n   * @example\n   *\n   * ```ts\n   * // Select all users and their pets\n   * const usersWithPets: { user: User; pets: Pet }[] = await db.select()\n   *   .from(users)\n   *   .innerJoin(pets, eq(users.id, pets.ownerId))\n   *\n   * // Select userId and petId\n   * const usersIdsAndPetIds: { userId: number; petId: number }[] = await db.select({\n   *   userId: users.id,\n   *   petId: pets.id,\n   * })\n   *   .from(users)\n   *   .innerJoin(pets, eq(users.id, pets.ownerId))\n   * ```\n   */\n  innerJoin = this.createJoin(\"inner\");\n  /**\n   * Executes a `full join` operation by combining rows from two tables into a new table.\n   *\n   * Calling this method retrieves all rows from both main and joined tables, merging rows with matching values and filling in `null` for non-matching columns.\n   *\n   * See docs: {@link https://orm.drizzle.team/docs/joins#full-join}\n   *\n   * @param table the table to join.\n   * @param on the `on` clause.\n   *\n   * @example\n   *\n   * ```ts\n   * // Select all users and their pets\n   * const usersWithPets: { user: User | null; pets: Pet | null }[] = await db.select()\n   *   .from(users)\n   *   .fullJoin(pets, eq(users.id, pets.ownerId))\n   *\n   * // Select userId and petId\n   * const usersIdsAndPetIds: { userId: number | null; petId: number | null }[] = await db.select({\n   *   userId: users.id,\n   *   petId: pets.id,\n   * })\n   *   .from(users)\n   *   .fullJoin(pets, eq(users.id, pets.ownerId))\n   * ```\n   */\n  fullJoin = this.createJoin(\"full\");\n  createSetOperator(type, isAll) {\n    return (rightSelection) => {\n      const rightSelect = typeof rightSelection === \"function\" ? rightSelection(getSQLiteSetOperators()) : rightSelection;\n      if (!haveSameKeys(this.getSelectedFields(), rightSelect.getSelectedFields())) {\n        throw new Error(\n          \"Set operator error (union / intersect / except): selected fields are not the same or are in a different order\"\n        );\n      }\n      this.config.setOperators.push({ type, isAll, rightSelect });\n      return this;\n    };\n  }\n  /**\n   * Adds `union` set operator to the query.\n   *\n   * Calling this method will combine the result sets of the `select` statements and remove any duplicate rows that appear across them.\n   *\n   * See docs: {@link https://orm.drizzle.team/docs/set-operations#union}\n   *\n   * @example\n   *\n   * ```ts\n   * // Select all unique names from customers and users tables\n   * await db.select({ name: users.name })\n   *   .from(users)\n   *   .union(\n   *     db.select({ name: customers.name }).from(customers)\n   *   );\n   * // or\n   * import { union } from 'drizzle-orm/sqlite-core'\n   *\n   * await union(\n   *   db.select({ name: users.name }).from(users),\n   *   db.select({ name: customers.name }).from(customers)\n   * );\n   * ```\n   */\n  union = this.createSetOperator(\"union\", false);\n  /**\n   * Adds `union all` set operator to the query.\n   *\n   * Calling this method will combine the result-set of the `select` statements and keep all duplicate rows that appear across them.\n   *\n   * See docs: {@link https://orm.drizzle.team/docs/set-operations#union-all}\n   *\n   * @example\n   *\n   * ```ts\n   * // Select all transaction ids from both online and in-store sales\n   * await db.select({ transaction: onlineSales.transactionId })\n   *   .from(onlineSales)\n   *   .unionAll(\n   *     db.select({ transaction: inStoreSales.transactionId }).from(inStoreSales)\n   *   );\n   * // or\n   * import { unionAll } from 'drizzle-orm/sqlite-core'\n   *\n   * await unionAll(\n   *   db.select({ transaction: onlineSales.transactionId }).from(onlineSales),\n   *   db.select({ transaction: inStoreSales.transactionId }).from(inStoreSales)\n   * );\n   * ```\n   */\n  unionAll = this.createSetOperator(\"union\", true);\n  /**\n   * Adds `intersect` set operator to the query.\n   *\n   * Calling this method will retain only the rows that are present in both result sets and eliminate duplicates.\n   *\n   * See docs: {@link https://orm.drizzle.team/docs/set-operations#intersect}\n   *\n   * @example\n   *\n   * ```ts\n   * // Select course names that are offered in both departments A and B\n   * await db.select({ courseName: depA.courseName })\n   *   .from(depA)\n   *   .intersect(\n   *     db.select({ courseName: depB.courseName }).from(depB)\n   *   );\n   * // or\n   * import { intersect } from 'drizzle-orm/sqlite-core'\n   *\n   * await intersect(\n   *   db.select({ courseName: depA.courseName }).from(depA),\n   *   db.select({ courseName: depB.courseName }).from(depB)\n   * );\n   * ```\n   */\n  intersect = this.createSetOperator(\"intersect\", false);\n  /**\n   * Adds `except` set operator to the query.\n   *\n   * Calling this method will retrieve all unique rows from the left query, except for the rows that are present in the result set of the right query.\n   *\n   * See docs: {@link https://orm.drizzle.team/docs/set-operations#except}\n   *\n   * @example\n   *\n   * ```ts\n   * // Select all courses offered in department A but not in department B\n   * await db.select({ courseName: depA.courseName })\n   *   .from(depA)\n   *   .except(\n   *     db.select({ courseName: depB.courseName }).from(depB)\n   *   );\n   * // or\n   * import { except } from 'drizzle-orm/sqlite-core'\n   *\n   * await except(\n   *   db.select({ courseName: depA.courseName }).from(depA),\n   *   db.select({ courseName: depB.courseName }).from(depB)\n   * );\n   * ```\n   */\n  except = this.createSetOperator(\"except\", false);\n  /** @internal */\n  addSetOperators(setOperators) {\n    this.config.setOperators.push(...setOperators);\n    return this;\n  }\n  /**\n   * Adds a `where` clause to the query.\n   *\n   * Calling this method will select only those rows that fulfill a specified condition.\n   *\n   * See docs: {@link https://orm.drizzle.team/docs/select#filtering}\n   *\n   * @param where the `where` clause.\n   *\n   * @example\n   * You can use conditional operators and `sql function` to filter the rows to be selected.\n   *\n   * ```ts\n   * // Select all cars with green color\n   * await db.select().from(cars).where(eq(cars.color, 'green'));\n   * // or\n   * await db.select().from(cars).where(sql`${cars.color} = 'green'`)\n   * ```\n   *\n   * You can logically combine conditional operators with `and()` and `or()` operators:\n   *\n   * ```ts\n   * // Select all BMW cars with a green color\n   * await db.select().from(cars).where(and(eq(cars.color, 'green'), eq(cars.brand, 'BMW')));\n   *\n   * // Select all cars with the green or blue color\n   * await db.select().from(cars).where(or(eq(cars.color, 'green'), eq(cars.color, 'blue')));\n   * ```\n   */\n  where(where) {\n    if (typeof where === \"function\") {\n      where = where(\n        new Proxy(\n          this.config.fields,\n          new SelectionProxyHandler({ sqlAliasedBehavior: \"sql\", sqlBehavior: \"sql\" })\n        )\n      );\n    }\n    this.config.where = where;\n    return this;\n  }\n  /**\n   * Adds a `having` clause to the query.\n   *\n   * Calling this method will select only those rows that fulfill a specified condition. It is typically used with aggregate functions to filter the aggregated data based on a specified condition.\n   *\n   * See docs: {@link https://orm.drizzle.team/docs/select#aggregations}\n   *\n   * @param having the `having` clause.\n   *\n   * @example\n   *\n   * ```ts\n   * // Select all brands with more than one car\n   * await db.select({\n   * \tbrand: cars.brand,\n   * \tcount: sql<number>`cast(count(${cars.id}) as int)`,\n   * })\n   *   .from(cars)\n   *   .groupBy(cars.brand)\n   *   .having(({ count }) => gt(count, 1));\n   * ```\n   */\n  having(having) {\n    if (typeof having === \"function\") {\n      having = having(\n        new Proxy(\n          this.config.fields,\n          new SelectionProxyHandler({ sqlAliasedBehavior: \"sql\", sqlBehavior: \"sql\" })\n        )\n      );\n    }\n    this.config.having = having;\n    return this;\n  }\n  groupBy(...columns) {\n    if (typeof columns[0] === \"function\") {\n      const groupBy = columns[0](\n        new Proxy(\n          this.config.fields,\n          new SelectionProxyHandler({ sqlAliasedBehavior: \"alias\", sqlBehavior: \"sql\" })\n        )\n      );\n      this.config.groupBy = Array.isArray(groupBy) ? groupBy : [groupBy];\n    } else {\n      this.config.groupBy = columns;\n    }\n    return this;\n  }\n  orderBy(...columns) {\n    if (typeof columns[0] === \"function\") {\n      const orderBy = columns[0](\n        new Proxy(\n          this.config.fields,\n          new SelectionProxyHandler({ sqlAliasedBehavior: \"alias\", sqlBehavior: \"sql\" })\n        )\n      );\n      const orderByArray = Array.isArray(orderBy) ? orderBy : [orderBy];\n      if (this.config.setOperators.length > 0) {\n        this.config.setOperators.at(-1).orderBy = orderByArray;\n      } else {\n        this.config.orderBy = orderByArray;\n      }\n    } else {\n      const orderByArray = columns;\n      if (this.config.setOperators.length > 0) {\n        this.config.setOperators.at(-1).orderBy = orderByArray;\n      } else {\n        this.config.orderBy = orderByArray;\n      }\n    }\n    return this;\n  }\n  /**\n   * Adds a `limit` clause to the query.\n   *\n   * Calling this method will set the maximum number of rows that will be returned by this query.\n   *\n   * See docs: {@link https://orm.drizzle.team/docs/select#limit--offset}\n   *\n   * @param limit the `limit` clause.\n   *\n   * @example\n   *\n   * ```ts\n   * // Get the first 10 people from this query.\n   * await db.select().from(people).limit(10);\n   * ```\n   */\n  limit(limit) {\n    if (this.config.setOperators.length > 0) {\n      this.config.setOperators.at(-1).limit = limit;\n    } else {\n      this.config.limit = limit;\n    }\n    return this;\n  }\n  /**\n   * Adds an `offset` clause to the query.\n   *\n   * Calling this method will skip a number of rows when returning results from this query.\n   *\n   * See docs: {@link https://orm.drizzle.team/docs/select#limit--offset}\n   *\n   * @param offset the `offset` clause.\n   *\n   * @example\n   *\n   * ```ts\n   * // Get the 10th-20th people from this query.\n   * await db.select().from(people).offset(10).limit(10);\n   * ```\n   */\n  offset(offset) {\n    if (this.config.setOperators.length > 0) {\n      this.config.setOperators.at(-1).offset = offset;\n    } else {\n      this.config.offset = offset;\n    }\n    return this;\n  }\n  /** @internal */\n  getSQL() {\n    return this.dialect.buildSelectQuery(this.config);\n  }\n  toSQL() {\n    const { typings: _typings, ...rest } = this.dialect.sqlToQuery(this.getSQL());\n    return rest;\n  }\n  as(alias) {\n    return new Proxy(\n      new Subquery(this.getSQL(), this.config.fields, alias),\n      new SelectionProxyHandler({ alias, sqlAliasedBehavior: \"alias\", sqlBehavior: \"error\" })\n    );\n  }\n  /** @internal */\n  getSelectedFields() {\n    return new Proxy(\n      this.config.fields,\n      new SelectionProxyHandler({ alias: this.tableName, sqlAliasedBehavior: \"alias\", sqlBehavior: \"error\" })\n    );\n  }\n  $dynamic() {\n    return this;\n  }\n}\nclass SQLiteSelectBase extends SQLiteSelectQueryBuilderBase {\n  static [entityKind] = \"SQLiteSelect\";\n  /** @internal */\n  _prepare(isOneTimeQuery = true) {\n    if (!this.session) {\n      throw new Error(\"Cannot execute a query on a query builder. Please use a database instance instead.\");\n    }\n    const fieldsList = orderSelectedFields(this.config.fields);\n    const query = this.session[isOneTimeQuery ? \"prepareOneTimeQuery\" : \"prepareQuery\"](\n      this.dialect.sqlToQuery(this.getSQL()),\n      fieldsList,\n      \"all\",\n      true\n    );\n    query.joinsNotNullableMap = this.joinsNotNullableMap;\n    return query;\n  }\n  prepare() {\n    return this._prepare(false);\n  }\n  run = (placeholderValues) => {\n    return this._prepare().run(placeholderValues);\n  };\n  all = (placeholderValues) => {\n    return this._prepare().all(placeholderValues);\n  };\n  get = (placeholderValues) => {\n    return this._prepare().get(placeholderValues);\n  };\n  values = (placeholderValues) => {\n    return this._prepare().values(placeholderValues);\n  };\n  async execute() {\n    return this.all();\n  }\n}\napplyMixins(SQLiteSelectBase, [QueryPromise]);\nfunction createSetOperator(type, isAll) {\n  return (leftSelect, rightSelect, ...restSelects) => {\n    const setOperators = [rightSelect, ...restSelects].map((select) => ({\n      type,\n      isAll,\n      rightSelect: select\n    }));\n    for (const setOperator of setOperators) {\n      if (!haveSameKeys(leftSelect.getSelectedFields(), setOperator.rightSelect.getSelectedFields())) {\n        throw new Error(\n          \"Set operator error (union / intersect / except): selected fields are not the same or are in a different order\"\n        );\n      }\n    }\n    return leftSelect.addSetOperators(setOperators);\n  };\n}\nconst getSQLiteSetOperators = () => ({\n  union,\n  unionAll,\n  intersect,\n  except\n});\nconst union = createSetOperator(\"union\", false);\nconst unionAll = createSetOperator(\"union\", true);\nconst intersect = createSetOperator(\"intersect\", false);\nconst except = createSetOperator(\"except\", false);\nexport {\n  SQLiteSelectBase,\n  SQLiteSelectBuilder,\n  SQLiteSelectQueryBuilderBase,\n  except,\n  intersect,\n  union,\n  unionAll\n};\n//# sourceMappingURL=select.js.map", "import { entityKind, is } from \"../../entity.js\";\nimport { SelectionProxyHandler } from \"../../selection-proxy.js\";\nimport { SQLiteDialect, SQLiteSyncDialect } from \"../dialect.js\";\nimport { WithSubquery } from \"../../subquery.js\";\nimport { SQLiteSelectBuilder } from \"./select.js\";\nclass QueryBuilder {\n  static [entityKind] = \"SQLiteQueryBuilder\";\n  dialect;\n  dialectConfig;\n  constructor(dialect) {\n    this.dialect = is(dialect, SQLiteDialect) ? dialect : void 0;\n    this.dialectConfig = is(dialect, SQLiteDialect) ? void 0 : dialect;\n  }\n  $with(alias) {\n    const queryBuilder = this;\n    return {\n      as(qb) {\n        if (typeof qb === \"function\") {\n          qb = qb(queryBuilder);\n        }\n        return new Proxy(\n          new WithSubquery(qb.getSQL(), qb.getSelectedFields(), alias, true),\n          new SelectionProxyHandler({ alias, sqlAliasedBehavior: \"alias\", sqlBehavior: \"error\" })\n        );\n      }\n    };\n  }\n  with(...queries) {\n    const self = this;\n    function select(fields) {\n      return new SQLiteSelectBuilder({\n        fields: fields ?? void 0,\n        session: void 0,\n        dialect: self.getDialect(),\n        withList: queries\n      });\n    }\n    function selectDistinct(fields) {\n      return new SQLiteSelectBuilder({\n        fields: fields ?? void 0,\n        session: void 0,\n        dialect: self.getDialect(),\n        withList: queries,\n        distinct: true\n      });\n    }\n    return { select, selectDistinct };\n  }\n  select(fields) {\n    return new SQLiteSelectBuilder({ fields: fields ?? void 0, session: void 0, dialect: this.getDialect() });\n  }\n  selectDistinct(fields) {\n    return new SQLiteSelectBuilder({\n      fields: fields ?? void 0,\n      session: void 0,\n      dialect: this.getDialect(),\n      distinct: true\n    });\n  }\n  // Lazy load dialect to avoid circular dependency\n  getDialect() {\n    if (!this.dialect) {\n      this.dialect = new SQLiteSyncDialect(this.dialectConfig);\n    }\n    return this.dialect;\n  }\n}\nexport {\n  QueryBuilder\n};\n//# sourceMappingURL=query-builder.js.map", "import { entityKind, is } from \"../../entity.js\";\nimport { QueryPromise } from \"../../query-promise.js\";\nimport { SelectionProxyHandler } from \"../../selection-proxy.js\";\nimport { SQLiteTable } from \"../table.js\";\nimport { Subquery } from \"../../subquery.js\";\nimport { Table } from \"../../table.js\";\nimport {\n  getTableLikeName,\n  mapUpdateSet,\n  orderSelectedFields\n} from \"../../utils.js\";\nimport { ViewBaseConfig } from \"../../view-common.js\";\nimport { SQLiteViewBase } from \"../view-base.js\";\nclass SQLiteUpdateBuilder {\n  constructor(table, session, dialect, withList) {\n    this.table = table;\n    this.session = session;\n    this.dialect = dialect;\n    this.withList = withList;\n  }\n  static [entityKind] = \"SQLiteUpdateBuilder\";\n  set(values) {\n    return new SQLiteUpdateBase(\n      this.table,\n      mapUpdateSet(this.table, values),\n      this.session,\n      this.dialect,\n      this.withList\n    );\n  }\n}\nclass SQLiteUpdateBase extends QueryPromise {\n  constructor(table, set, session, dialect, withList) {\n    super();\n    this.session = session;\n    this.dialect = dialect;\n    this.config = { set, table, withList, joins: [] };\n  }\n  static [entityKind] = \"SQLiteUpdate\";\n  /** @internal */\n  config;\n  from(source) {\n    this.config.from = source;\n    return this;\n  }\n  createJoin(joinType) {\n    return (table, on) => {\n      const tableName = getTableLikeName(table);\n      if (typeof tableName === \"string\" && this.config.joins.some((join) => join.alias === tableName)) {\n        throw new Error(`Alias \"${tableName}\" is already used in this query`);\n      }\n      if (typeof on === \"function\") {\n        const from = this.config.from ? is(table, SQLiteTable) ? table[Table.Symbol.Columns] : is(table, Subquery) ? table._.selectedFields : is(table, SQLiteViewBase) ? table[ViewBaseConfig].selectedFields : void 0 : void 0;\n        on = on(\n          new Proxy(\n            this.config.table[Table.Symbol.Columns],\n            new SelectionProxyHandler({ sqlAliasedBehavior: \"sql\", sqlBehavior: \"sql\" })\n          ),\n          from && new Proxy(\n            from,\n            new SelectionProxyHandler({ sqlAliasedBehavior: \"sql\", sqlBehavior: \"sql\" })\n          )\n        );\n      }\n      this.config.joins.push({ on, table, joinType, alias: tableName });\n      return this;\n    };\n  }\n  leftJoin = this.createJoin(\"left\");\n  rightJoin = this.createJoin(\"right\");\n  innerJoin = this.createJoin(\"inner\");\n  fullJoin = this.createJoin(\"full\");\n  /**\n   * Adds a 'where' clause to the query.\n   *\n   * Calling this method will update only those rows that fulfill a specified condition.\n   *\n   * See docs: {@link https://orm.drizzle.team/docs/update}\n   *\n   * @param where the 'where' clause.\n   *\n   * @example\n   * You can use conditional operators and `sql function` to filter the rows to be updated.\n   *\n   * ```ts\n   * // Update all cars with green color\n   * db.update(cars).set({ color: 'red' })\n   *   .where(eq(cars.color, 'green'));\n   * // or\n   * db.update(cars).set({ color: 'red' })\n   *   .where(sql`${cars.color} = 'green'`)\n   * ```\n   *\n   * You can logically combine conditional operators with `and()` and `or()` operators:\n   *\n   * ```ts\n   * // Update all BMW cars with a green color\n   * db.update(cars).set({ color: 'red' })\n   *   .where(and(eq(cars.color, 'green'), eq(cars.brand, 'BMW')));\n   *\n   * // Update all cars with the green or blue color\n   * db.update(cars).set({ color: 'red' })\n   *   .where(or(eq(cars.color, 'green'), eq(cars.color, 'blue')));\n   * ```\n   */\n  where(where) {\n    this.config.where = where;\n    return this;\n  }\n  orderBy(...columns) {\n    if (typeof columns[0] === \"function\") {\n      const orderBy = columns[0](\n        new Proxy(\n          this.config.table[Table.Symbol.Columns],\n          new SelectionProxyHandler({ sqlAliasedBehavior: \"alias\", sqlBehavior: \"sql\" })\n        )\n      );\n      const orderByArray = Array.isArray(orderBy) ? orderBy : [orderBy];\n      this.config.orderBy = orderByArray;\n    } else {\n      const orderByArray = columns;\n      this.config.orderBy = orderByArray;\n    }\n    return this;\n  }\n  limit(limit) {\n    this.config.limit = limit;\n    return this;\n  }\n  returning(fields = this.config.table[SQLiteTable.Symbol.Columns]) {\n    this.config.returning = orderSelectedFields(fields);\n    return this;\n  }\n  /** @internal */\n  getSQL() {\n    return this.dialect.buildUpdateQuery(this.config);\n  }\n  toSQL() {\n    const { typings: _typings, ...rest } = this.dialect.sqlToQuery(this.getSQL());\n    return rest;\n  }\n  /** @internal */\n  _prepare(isOneTimeQuery = true) {\n    return this.session[isOneTimeQuery ? \"prepareOneTimeQuery\" : \"prepareQuery\"](\n      this.dialect.sqlToQuery(this.getSQL()),\n      this.config.returning,\n      this.config.returning ? \"all\" : \"run\",\n      true\n    );\n  }\n  prepare() {\n    return this._prepare(false);\n  }\n  run = (placeholderValues) => {\n    return this._prepare().run(placeholderValues);\n  };\n  all = (placeholderValues) => {\n    return this._prepare().all(placeholderValues);\n  };\n  get = (placeholderValues) => {\n    return this._prepare().get(placeholderValues);\n  };\n  values = (placeholderValues) => {\n    return this._prepare().values(placeholderValues);\n  };\n  async execute() {\n    return this.config.returning ? this.all() : this.run();\n  }\n  $dynamic() {\n    return this;\n  }\n}\nexport {\n  SQLiteUpdateBase,\n  SQLiteUpdateBuilder\n};\n//# sourceMappingURL=update.js.map", "import { entityKind, is } from \"../../entity.js\";\nimport { QueryPromise } from \"../../query-promise.js\";\nimport { Param, SQL, sql } from \"../../sql/sql.js\";\nimport { SQLiteTable } from \"../table.js\";\nimport { Columns, Table } from \"../../table.js\";\nimport { haveSameKeys, mapUpdateSet, orderSelectedFields } from \"../../utils.js\";\nimport { QueryBuilder } from \"./query-builder.js\";\nclass SQLiteInsertBuilder {\n  constructor(table, session, dialect, withList) {\n    this.table = table;\n    this.session = session;\n    this.dialect = dialect;\n    this.withList = withList;\n  }\n  static [entityKind] = \"SQLiteInsertBuilder\";\n  values(values) {\n    values = Array.isArray(values) ? values : [values];\n    if (values.length === 0) {\n      throw new Error(\"values() must be called with at least one value\");\n    }\n    const mappedValues = values.map((entry) => {\n      const result = {};\n      const cols = this.table[Table.Symbol.Columns];\n      for (const colKey of Object.keys(entry)) {\n        const colValue = entry[colKey];\n        result[colKey] = is(colValue, SQL) ? colValue : new Param(colValue, cols[colKey]);\n      }\n      return result;\n    });\n    return new SQLiteInsertBase(this.table, mappedValues, this.session, this.dialect, this.withList);\n  }\n  select(selectQuery) {\n    const select = typeof selectQuery === \"function\" ? selectQuery(new QueryBuilder()) : selectQuery;\n    if (!is(select, SQL) && !haveSameKeys(this.table[Columns], select._.selectedFields)) {\n      throw new Error(\n        \"Insert select error: selected fields are not the same or are in a different order compared to the table definition\"\n      );\n    }\n    return new SQLiteInsertBase(this.table, select, this.session, this.dialect, this.withList, true);\n  }\n}\nclass SQLiteInsertBase extends QueryPromise {\n  constructor(table, values, session, dialect, withList, select) {\n    super();\n    this.session = session;\n    this.dialect = dialect;\n    this.config = { table, values, withList, select };\n  }\n  static [entityKind] = \"SQLiteInsert\";\n  /** @internal */\n  config;\n  returning(fields = this.config.table[SQLiteTable.Symbol.Columns]) {\n    this.config.returning = orderSelectedFields(fields);\n    return this;\n  }\n  /**\n   * Adds an `on conflict do nothing` clause to the query.\n   *\n   * Calling this method simply avoids inserting a row as its alternative action.\n   *\n   * See docs: {@link https://orm.drizzle.team/docs/insert#on-conflict-do-nothing}\n   *\n   * @param config The `target` and `where` clauses.\n   *\n   * @example\n   * ```ts\n   * // Insert one row and cancel the insert if there's a conflict\n   * await db.insert(cars)\n   *   .values({ id: 1, brand: 'BMW' })\n   *   .onConflictDoNothing();\n   *\n   * // Explicitly specify conflict target\n   * await db.insert(cars)\n   *   .values({ id: 1, brand: 'BMW' })\n   *   .onConflictDoNothing({ target: cars.id });\n   * ```\n   */\n  onConflictDoNothing(config = {}) {\n    if (config.target === void 0) {\n      this.config.onConflict = sql`do nothing`;\n    } else {\n      const targetSql = Array.isArray(config.target) ? sql`${config.target}` : sql`${[config.target]}`;\n      const whereSql = config.where ? sql` where ${config.where}` : sql``;\n      this.config.onConflict = sql`${targetSql} do nothing${whereSql}`;\n    }\n    return this;\n  }\n  /**\n   * Adds an `on conflict do update` clause to the query.\n   *\n   * Calling this method will update the existing row that conflicts with the row proposed for insertion as its alternative action.\n   *\n   * See docs: {@link https://orm.drizzle.team/docs/insert#upserts-and-conflicts}\n   *\n   * @param config The `target`, `set` and `where` clauses.\n   *\n   * @example\n   * ```ts\n   * // Update the row if there's a conflict\n   * await db.insert(cars)\n   *   .values({ id: 1, brand: 'BMW' })\n   *   .onConflictDoUpdate({\n   *     target: cars.id,\n   *     set: { brand: 'Porsche' }\n   *   });\n   *\n   * // Upsert with 'where' clause\n   * await db.insert(cars)\n   *   .values({ id: 1, brand: 'BMW' })\n   *   .onConflictDoUpdate({\n   *     target: cars.id,\n   *     set: { brand: 'newBMW' },\n   *     where: sql`${cars.createdAt} > '2023-01-01'::date`,\n   *   });\n   * ```\n   */\n  onConflictDoUpdate(config) {\n    if (config.where && (config.targetWhere || config.setWhere)) {\n      throw new Error(\n        'You cannot use both \"where\" and \"targetWhere\"/\"setWhere\" at the same time - \"where\" is deprecated, use \"targetWhere\" or \"setWhere\" instead.'\n      );\n    }\n    const whereSql = config.where ? sql` where ${config.where}` : void 0;\n    const targetWhereSql = config.targetWhere ? sql` where ${config.targetWhere}` : void 0;\n    const setWhereSql = config.setWhere ? sql` where ${config.setWhere}` : void 0;\n    const targetSql = Array.isArray(config.target) ? sql`${config.target}` : sql`${[config.target]}`;\n    const setSql = this.dialect.buildUpdateSet(this.config.table, mapUpdateSet(this.config.table, config.set));\n    this.config.onConflict = sql`${targetSql}${targetWhereSql} do update set ${setSql}${whereSql}${setWhereSql}`;\n    return this;\n  }\n  /** @internal */\n  getSQL() {\n    return this.dialect.buildInsertQuery(this.config);\n  }\n  toSQL() {\n    const { typings: _typings, ...rest } = this.dialect.sqlToQuery(this.getSQL());\n    return rest;\n  }\n  /** @internal */\n  _prepare(isOneTimeQuery = true) {\n    return this.session[isOneTimeQuery ? \"prepareOneTimeQuery\" : \"prepareQuery\"](\n      this.dialect.sqlToQuery(this.getSQL()),\n      this.config.returning,\n      this.config.returning ? \"all\" : \"run\",\n      true\n    );\n  }\n  prepare() {\n    return this._prepare(false);\n  }\n  run = (placeholderValues) => {\n    return this._prepare().run(placeholderValues);\n  };\n  all = (placeholderValues) => {\n    return this._prepare().all(placeholderValues);\n  };\n  get = (placeholderValues) => {\n    return this._prepare().get(placeholderValues);\n  };\n  values = (placeholderValues) => {\n    return this._prepare().values(placeholderValues);\n  };\n  async execute() {\n    return this.config.returning ? this.all() : this.run();\n  }\n  $dynamic() {\n    return this;\n  }\n}\nexport {\n  SQLiteInsertBase,\n  SQLiteInsertBuilder\n};\n//# sourceMappingURL=insert.js.map", "import { entityKind } from \"../../entity.js\";\nimport { QueryPromise } from \"../../query-promise.js\";\nimport { SelectionProxyHandler } from \"../../selection-proxy.js\";\nimport { SQLiteTable } from \"../table.js\";\nimport { Table } from \"../../table.js\";\nimport { orderSelectedFields } from \"../../utils.js\";\nclass SQLiteDeleteBase extends QueryPromise {\n  constructor(table, session, dialect, withList) {\n    super();\n    this.table = table;\n    this.session = session;\n    this.dialect = dialect;\n    this.config = { table, withList };\n  }\n  static [entityKind] = \"SQLiteDelete\";\n  /** @internal */\n  config;\n  /**\n   * Adds a `where` clause to the query.\n   *\n   * Calling this method will delete only those rows that fulfill a specified condition.\n   *\n   * See docs: {@link https://orm.drizzle.team/docs/delete}\n   *\n   * @param where the `where` clause.\n   *\n   * @example\n   * You can use conditional operators and `sql function` to filter the rows to be deleted.\n   *\n   * ```ts\n   * // Delete all cars with green color\n   * db.delete(cars).where(eq(cars.color, 'green'));\n   * // or\n   * db.delete(cars).where(sql`${cars.color} = 'green'`)\n   * ```\n   *\n   * You can logically combine conditional operators with `and()` and `or()` operators:\n   *\n   * ```ts\n   * // Delete all BMW cars with a green color\n   * db.delete(cars).where(and(eq(cars.color, 'green'), eq(cars.brand, 'BMW')));\n   *\n   * // Delete all cars with the green or blue color\n   * db.delete(cars).where(or(eq(cars.color, 'green'), eq(cars.color, 'blue')));\n   * ```\n   */\n  where(where) {\n    this.config.where = where;\n    return this;\n  }\n  orderBy(...columns) {\n    if (typeof columns[0] === \"function\") {\n      const orderBy = columns[0](\n        new Proxy(\n          this.config.table[Table.Symbol.Columns],\n          new SelectionProxyHandler({ sqlAliasedBehavior: \"alias\", sqlBehavior: \"sql\" })\n        )\n      );\n      const orderByArray = Array.isArray(orderBy) ? orderBy : [orderBy];\n      this.config.orderBy = orderByArray;\n    } else {\n      const orderByArray = columns;\n      this.config.orderBy = orderByArray;\n    }\n    return this;\n  }\n  limit(limit) {\n    this.config.limit = limit;\n    return this;\n  }\n  returning(fields = this.table[SQLiteTable.Symbol.Columns]) {\n    this.config.returning = orderSelectedFields(fields);\n    return this;\n  }\n  /** @internal */\n  getSQL() {\n    return this.dialect.buildDeleteQuery(this.config);\n  }\n  toSQL() {\n    const { typings: _typings, ...rest } = this.dialect.sqlToQuery(this.getSQL());\n    return rest;\n  }\n  /** @internal */\n  _prepare(isOneTimeQuery = true) {\n    return this.session[isOneTimeQuery ? \"prepareOneTimeQuery\" : \"prepareQuery\"](\n      this.dialect.sqlToQuery(this.getSQL()),\n      this.config.returning,\n      this.config.returning ? \"all\" : \"run\",\n      true\n    );\n  }\n  prepare() {\n    return this._prepare(false);\n  }\n  run = (placeholderValues) => {\n    return this._prepare().run(placeholderValues);\n  };\n  all = (placeholderValues) => {\n    return this._prepare().all(placeholderValues);\n  };\n  get = (placeholderValues) => {\n    return this._prepare().get(placeholderValues);\n  };\n  values = (placeholderValues) => {\n    return this._prepare().values(placeholderValues);\n  };\n  async execute(placeholderValues) {\n    return this._prepare().execute(placeholderValues);\n  }\n  $dynamic() {\n    return this;\n  }\n}\nexport {\n  SQLiteDeleteBase\n};\n//# sourceMappingURL=delete.js.map", "import { entityKind } from \"../../entity.js\";\nimport { SQL, sql } from \"../../sql/sql.js\";\nclass SQLiteCountBuilder extends SQL {\n  constructor(params) {\n    super(SQLiteCountBuilder.buildEmbeddedCount(params.source, params.filters).queryChunks);\n    this.params = params;\n    this.session = params.session;\n    this.sql = SQLiteCountBuilder.buildCount(\n      params.source,\n      params.filters\n    );\n  }\n  sql;\n  static [entityKind] = \"SQLiteCountBuilderAsync\";\n  [Symbol.toStringTag] = \"SQLiteCountBuilderAsync\";\n  session;\n  static buildEmbeddedCount(source, filters) {\n    return sql`(select count(*) from ${source}${sql.raw(\" where \").if(filters)}${filters})`;\n  }\n  static buildCount(source, filters) {\n    return sql`select count(*) from ${source}${sql.raw(\" where \").if(filters)}${filters}`;\n  }\n  then(onfulfilled, onrejected) {\n    return Promise.resolve(this.session.count(this.sql)).then(\n      onfulfilled,\n      onrejected\n    );\n  }\n  catch(onRejected) {\n    return this.then(void 0, onRejected);\n  }\n  finally(onFinally) {\n    return this.then(\n      (value) => {\n        onFinally?.();\n        return value;\n      },\n      (reason) => {\n        onFinally?.();\n        throw reason;\n      }\n    );\n  }\n}\nexport {\n  SQLiteCountBuilder\n};\n//# sourceMappingURL=count.js.map", "import { entityKind } from \"../../entity.js\";\nimport { QueryPromise } from \"../../query-promise.js\";\nimport {\n  mapRelationalRow\n} from \"../../relations.js\";\nclass RelationalQueryBuilder {\n  constructor(mode, fullSchema, schema, tableNamesMap, table, tableConfig, dialect, session) {\n    this.mode = mode;\n    this.fullSchema = fullSchema;\n    this.schema = schema;\n    this.tableNamesMap = tableNamesMap;\n    this.table = table;\n    this.tableConfig = tableConfig;\n    this.dialect = dialect;\n    this.session = session;\n  }\n  static [entityKind] = \"SQLiteAsyncRelationalQueryBuilder\";\n  findMany(config) {\n    return this.mode === \"sync\" ? new SQLiteSyncRelationalQuery(\n      this.fullSchema,\n      this.schema,\n      this.tableNamesMap,\n      this.table,\n      this.tableConfig,\n      this.dialect,\n      this.session,\n      config ? config : {},\n      \"many\"\n    ) : new SQLiteRelationalQuery(\n      this.fullSchema,\n      this.schema,\n      this.tableNamesMap,\n      this.table,\n      this.tableConfig,\n      this.dialect,\n      this.session,\n      config ? config : {},\n      \"many\"\n    );\n  }\n  findFirst(config) {\n    return this.mode === \"sync\" ? new SQLiteSyncRelationalQuery(\n      this.fullSchema,\n      this.schema,\n      this.tableNamesMap,\n      this.table,\n      this.tableConfig,\n      this.dialect,\n      this.session,\n      config ? { ...config, limit: 1 } : { limit: 1 },\n      \"first\"\n    ) : new SQLiteRelationalQuery(\n      this.fullSchema,\n      this.schema,\n      this.tableNamesMap,\n      this.table,\n      this.tableConfig,\n      this.dialect,\n      this.session,\n      config ? { ...config, limit: 1 } : { limit: 1 },\n      \"first\"\n    );\n  }\n}\nclass SQLiteRelationalQuery extends QueryPromise {\n  constructor(fullSchema, schema, tableNamesMap, table, tableConfig, dialect, session, config, mode) {\n    super();\n    this.fullSchema = fullSchema;\n    this.schema = schema;\n    this.tableNamesMap = tableNamesMap;\n    this.table = table;\n    this.tableConfig = tableConfig;\n    this.dialect = dialect;\n    this.session = session;\n    this.config = config;\n    this.mode = mode;\n  }\n  static [entityKind] = \"SQLiteAsyncRelationalQuery\";\n  /** @internal */\n  mode;\n  /** @internal */\n  getSQL() {\n    return this.dialect.buildRelationalQuery({\n      fullSchema: this.fullSchema,\n      schema: this.schema,\n      tableNamesMap: this.tableNamesMap,\n      table: this.table,\n      tableConfig: this.tableConfig,\n      queryConfig: this.config,\n      tableAlias: this.tableConfig.tsName\n    }).sql;\n  }\n  /** @internal */\n  _prepare(isOneTimeQuery = false) {\n    const { query, builtQuery } = this._toSQL();\n    return this.session[isOneTimeQuery ? \"prepareOneTimeQuery\" : \"prepareQuery\"](\n      builtQuery,\n      void 0,\n      this.mode === \"first\" ? \"get\" : \"all\",\n      true,\n      (rawRows, mapColumnValue) => {\n        const rows = rawRows.map(\n          (row) => mapRelationalRow(this.schema, this.tableConfig, row, query.selection, mapColumnValue)\n        );\n        if (this.mode === \"first\") {\n          return rows[0];\n        }\n        return rows;\n      }\n    );\n  }\n  prepare() {\n    return this._prepare(false);\n  }\n  _toSQL() {\n    const query = this.dialect.buildRelationalQuery({\n      fullSchema: this.fullSchema,\n      schema: this.schema,\n      tableNamesMap: this.tableNamesMap,\n      table: this.table,\n      tableConfig: this.tableConfig,\n      queryConfig: this.config,\n      tableAlias: this.tableConfig.tsName\n    });\n    const builtQuery = this.dialect.sqlToQuery(query.sql);\n    return { query, builtQuery };\n  }\n  toSQL() {\n    return this._toSQL().builtQuery;\n  }\n  /** @internal */\n  executeRaw() {\n    if (this.mode === \"first\") {\n      return this._prepare(false).get();\n    }\n    return this._prepare(false).all();\n  }\n  async execute() {\n    return this.executeRaw();\n  }\n}\nclass SQLiteSyncRelationalQuery extends SQLiteRelationalQuery {\n  static [entityKind] = \"SQLiteSyncRelationalQuery\";\n  sync() {\n    return this.executeRaw();\n  }\n}\nexport {\n  RelationalQueryBuilder,\n  SQLiteRelationalQuery,\n  SQLiteSyncRelationalQuery\n};\n//# sourceMappingURL=query.js.map", "import { entityKind } from \"../../entity.js\";\nimport { QueryPromise } from \"../../query-promise.js\";\nclass SQLiteRaw extends QueryPromise {\n  constructor(execute, getSQL, action, dialect, mapBatchResult) {\n    super();\n    this.execute = execute;\n    this.getSQL = getSQL;\n    this.dialect = dialect;\n    this.mapBatchResult = mapBatchResult;\n    this.config = { action };\n  }\n  static [entityKind] = \"SQLiteRaw\";\n  /** @internal */\n  config;\n  getQuery() {\n    return { ...this.dialect.sqlToQuery(this.getSQL()), method: this.config.action };\n  }\n  mapResult(result, isFromBatch) {\n    return isFromBatch ? this.mapBatchResult(result) : result;\n  }\n  _prepare() {\n    return this;\n  }\n  /** @internal */\n  isResponseInArrayMode() {\n    return false;\n  }\n}\nexport {\n  SQLiteRaw\n};\n//# sourceMappingURL=raw.js.map", "import { entityKind } from \"../entity.js\";\nimport { SelectionProxyHandler } from \"../selection-proxy.js\";\nimport { sql } from \"../sql/sql.js\";\nimport {\n  QueryBuilder,\n  SQLiteDeleteBase,\n  SQLiteInsertBuilder,\n  SQLiteSelectBuilder,\n  SQLiteUpdateBuilder\n} from \"./query-builders/index.js\";\nimport { WithSubquery } from \"../subquery.js\";\nimport { SQLiteCountBuilder } from \"./query-builders/count.js\";\nimport { RelationalQueryBuilder } from \"./query-builders/query.js\";\nimport { SQLiteRaw } from \"./query-builders/raw.js\";\nclass BaseSQLiteDatabase {\n  constructor(resultKind, dialect, session, schema) {\n    this.resultKind = resultKind;\n    this.dialect = dialect;\n    this.session = session;\n    this._ = schema ? {\n      schema: schema.schema,\n      fullSchema: schema.fullSchema,\n      tableNamesMap: schema.tableNamesMap\n    } : {\n      schema: void 0,\n      fullSchema: {},\n      tableNamesMap: {}\n    };\n    this.query = {};\n    const query = this.query;\n    if (this._.schema) {\n      for (const [tableName, columns] of Object.entries(this._.schema)) {\n        query[tableName] = new RelationalQueryBuilder(\n          resultKind,\n          schema.fullSchema,\n          this._.schema,\n          this._.tableNamesMap,\n          schema.fullSchema[tableName],\n          columns,\n          dialect,\n          session\n        );\n      }\n    }\n  }\n  static [entityKind] = \"BaseSQLiteDatabase\";\n  query;\n  /**\n   * Creates a subquery that defines a temporary named result set as a CTE.\n   *\n   * It is useful for breaking down complex queries into simpler parts and for reusing the result set in subsequent parts of the query.\n   *\n   * See docs: {@link https://orm.drizzle.team/docs/select#with-clause}\n   *\n   * @param alias The alias for the subquery.\n   *\n   * Failure to provide an alias will result in a DrizzleTypeError, preventing the subquery from being referenced in other queries.\n   *\n   * @example\n   *\n   * ```ts\n   * // Create a subquery with alias 'sq' and use it in the select query\n   * const sq = db.$with('sq').as(db.select().from(users).where(eq(users.id, 42)));\n   *\n   * const result = await db.with(sq).select().from(sq);\n   * ```\n   *\n   * To select arbitrary SQL values as fields in a CTE and reference them in other CTEs or in the main query, you need to add aliases to them:\n   *\n   * ```ts\n   * // Select an arbitrary SQL value as a field in a CTE and reference it in the main query\n   * const sq = db.$with('sq').as(db.select({\n   *   name: sql<string>`upper(${users.name})`.as('name'),\n   * })\n   * .from(users));\n   *\n   * const result = await db.with(sq).select({ name: sq.name }).from(sq);\n   * ```\n   */\n  $with(alias) {\n    const self = this;\n    return {\n      as(qb) {\n        if (typeof qb === \"function\") {\n          qb = qb(new QueryBuilder(self.dialect));\n        }\n        return new Proxy(\n          new WithSubquery(qb.getSQL(), qb.getSelectedFields(), alias, true),\n          new SelectionProxyHandler({ alias, sqlAliasedBehavior: \"alias\", sqlBehavior: \"error\" })\n        );\n      }\n    };\n  }\n  $count(source, filters) {\n    return new SQLiteCountBuilder({ source, filters, session: this.session });\n  }\n  /**\n   * Incorporates a previously defined CTE (using `$with`) into the main query.\n   *\n   * This method allows the main query to reference a temporary named result set.\n   *\n   * See docs: {@link https://orm.drizzle.team/docs/select#with-clause}\n   *\n   * @param queries The CTEs to incorporate into the main query.\n   *\n   * @example\n   *\n   * ```ts\n   * // Define a subquery 'sq' as a CTE using $with\n   * const sq = db.$with('sq').as(db.select().from(users).where(eq(users.id, 42)));\n   *\n   * // Incorporate the CTE 'sq' into the main query and select from it\n   * const result = await db.with(sq).select().from(sq);\n   * ```\n   */\n  with(...queries) {\n    const self = this;\n    function select(fields) {\n      return new SQLiteSelectBuilder({\n        fields: fields ?? void 0,\n        session: self.session,\n        dialect: self.dialect,\n        withList: queries\n      });\n    }\n    function selectDistinct(fields) {\n      return new SQLiteSelectBuilder({\n        fields: fields ?? void 0,\n        session: self.session,\n        dialect: self.dialect,\n        withList: queries,\n        distinct: true\n      });\n    }\n    function update(table) {\n      return new SQLiteUpdateBuilder(table, self.session, self.dialect, queries);\n    }\n    function insert(into) {\n      return new SQLiteInsertBuilder(into, self.session, self.dialect, queries);\n    }\n    function delete_(from) {\n      return new SQLiteDeleteBase(from, self.session, self.dialect, queries);\n    }\n    return { select, selectDistinct, update, insert, delete: delete_ };\n  }\n  select(fields) {\n    return new SQLiteSelectBuilder({ fields: fields ?? void 0, session: this.session, dialect: this.dialect });\n  }\n  selectDistinct(fields) {\n    return new SQLiteSelectBuilder({\n      fields: fields ?? void 0,\n      session: this.session,\n      dialect: this.dialect,\n      distinct: true\n    });\n  }\n  /**\n   * Creates an update query.\n   *\n   * Calling this method without `.where()` clause will update all rows in a table. The `.where()` clause specifies which rows should be updated.\n   *\n   * Use `.set()` method to specify which values to update.\n   *\n   * See docs: {@link https://orm.drizzle.team/docs/update}\n   *\n   * @param table The table to update.\n   *\n   * @example\n   *\n   * ```ts\n   * // Update all rows in the 'cars' table\n   * await db.update(cars).set({ color: 'red' });\n   *\n   * // Update rows with filters and conditions\n   * await db.update(cars).set({ color: 'red' }).where(eq(cars.brand, 'BMW'));\n   *\n   * // Update with returning clause\n   * const updatedCar: Car[] = await db.update(cars)\n   *   .set({ color: 'red' })\n   *   .where(eq(cars.id, 1))\n   *   .returning();\n   * ```\n   */\n  update(table) {\n    return new SQLiteUpdateBuilder(table, this.session, this.dialect);\n  }\n  /**\n   * Creates an insert query.\n   *\n   * Calling this method will create new rows in a table. Use `.values()` method to specify which values to insert.\n   *\n   * See docs: {@link https://orm.drizzle.team/docs/insert}\n   *\n   * @param table The table to insert into.\n   *\n   * @example\n   *\n   * ```ts\n   * // Insert one row\n   * await db.insert(cars).values({ brand: 'BMW' });\n   *\n   * // Insert multiple rows\n   * await db.insert(cars).values([{ brand: 'BMW' }, { brand: 'Porsche' }]);\n   *\n   * // Insert with returning clause\n   * const insertedCar: Car[] = await db.insert(cars)\n   *   .values({ brand: 'BMW' })\n   *   .returning();\n   * ```\n   */\n  insert(into) {\n    return new SQLiteInsertBuilder(into, this.session, this.dialect);\n  }\n  /**\n   * Creates a delete query.\n   *\n   * Calling this method without `.where()` clause will delete all rows in a table. The `.where()` clause specifies which rows should be deleted.\n   *\n   * See docs: {@link https://orm.drizzle.team/docs/delete}\n   *\n   * @param table The table to delete from.\n   *\n   * @example\n   *\n   * ```ts\n   * // Delete all rows in the 'cars' table\n   * await db.delete(cars);\n   *\n   * // Delete rows with filters and conditions\n   * await db.delete(cars).where(eq(cars.color, 'green'));\n   *\n   * // Delete with returning clause\n   * const deletedCar: Car[] = await db.delete(cars)\n   *   .where(eq(cars.id, 1))\n   *   .returning();\n   * ```\n   */\n  delete(from) {\n    return new SQLiteDeleteBase(from, this.session, this.dialect);\n  }\n  run(query) {\n    const sequel = typeof query === \"string\" ? sql.raw(query) : query.getSQL();\n    if (this.resultKind === \"async\") {\n      return new SQLiteRaw(\n        async () => this.session.run(sequel),\n        () => sequel,\n        \"run\",\n        this.dialect,\n        this.session.extractRawRunValueFromBatchResult.bind(this.session)\n      );\n    }\n    return this.session.run(sequel);\n  }\n  all(query) {\n    const sequel = typeof query === \"string\" ? sql.raw(query) : query.getSQL();\n    if (this.resultKind === \"async\") {\n      return new SQLiteRaw(\n        async () => this.session.all(sequel),\n        () => sequel,\n        \"all\",\n        this.dialect,\n        this.session.extractRawAllValueFromBatchResult.bind(this.session)\n      );\n    }\n    return this.session.all(sequel);\n  }\n  get(query) {\n    const sequel = typeof query === \"string\" ? sql.raw(query) : query.getSQL();\n    if (this.resultKind === \"async\") {\n      return new SQLiteRaw(\n        async () => this.session.get(sequel),\n        () => sequel,\n        \"get\",\n        this.dialect,\n        this.session.extractRawGetValueFromBatchResult.bind(this.session)\n      );\n    }\n    return this.session.get(sequel);\n  }\n  values(query) {\n    const sequel = typeof query === \"string\" ? sql.raw(query) : query.getSQL();\n    if (this.resultKind === \"async\") {\n      return new SQLiteRaw(\n        async () => this.session.values(sequel),\n        () => sequel,\n        \"values\",\n        this.dialect,\n        this.session.extractRawValuesValueFromBatchResult.bind(this.session)\n      );\n    }\n    return this.session.values(sequel);\n  }\n  transaction(transaction, config) {\n    return this.session.transaction(transaction, config);\n  }\n}\nconst withReplicas = (primary, replicas, getReplica = () => replicas[Math.floor(Math.random() * replicas.length)]) => {\n  const select = (...args) => getReplica(replicas).select(...args);\n  const selectDistinct = (...args) => getReplica(replicas).selectDistinct(...args);\n  const $with = (...args) => getReplica(replicas).with(...args);\n  const update = (...args) => primary.update(...args);\n  const insert = (...args) => primary.insert(...args);\n  const $delete = (...args) => primary.delete(...args);\n  const run = (...args) => primary.run(...args);\n  const all = (...args) => primary.all(...args);\n  const get = (...args) => primary.get(...args);\n  const values = (...args) => primary.values(...args);\n  const transaction = (...args) => primary.transaction(...args);\n  return {\n    ...primary,\n    update,\n    insert,\n    delete: $delete,\n    run,\n    all,\n    get,\n    values,\n    transaction,\n    $primary: primary,\n    select,\n    selectDistinct,\n    with: $with,\n    get query() {\n      return getReplica(replicas).query;\n    }\n  };\n};\nexport {\n  BaseSQLiteDatabase,\n  withReplicas\n};\n//# sourceMappingURL=db.js.map", "import { aliasedTable, aliasedTableColumn, mapColumnsInAliasedSQLToAlias, mapColumnsInSQLToAlias } from \"../alias.js\";\nimport { CasingCache } from \"../casing.js\";\nimport { Column } from \"../column.js\";\nimport { entityKind, is } from \"../entity.js\";\nimport { DrizzleError } from \"../errors.js\";\nimport {\n  getOperators,\n  getOrderByOperators,\n  Many,\n  normalizeRelation,\n  One\n} from \"../relations.js\";\nimport { and, eq } from \"../sql/index.js\";\nimport { Param, SQL, sql } from \"../sql/sql.js\";\nimport { SQLiteColumn } from \"./columns/index.js\";\nimport { SQLiteTable } from \"./table.js\";\nimport { Subquery } from \"../subquery.js\";\nimport { getTableName, getTableUniqueName, Table } from \"../table.js\";\nimport { orderSelectedFields } from \"../utils.js\";\nimport { ViewBaseConfig } from \"../view-common.js\";\nimport { SQLiteViewBase } from \"./view-base.js\";\nclass SQLiteDialect {\n  static [entityKind] = \"SQLiteDialect\";\n  /** @internal */\n  casing;\n  constructor(config) {\n    this.casing = new CasingCache(config?.casing);\n  }\n  escapeName(name) {\n    return `\"${name}\"`;\n  }\n  escapeParam(_num) {\n    return \"?\";\n  }\n  escapeString(str) {\n    return `'${str.replace(/'/g, \"''\")}'`;\n  }\n  buildWithCTE(queries) {\n    if (!queries?.length)\n      return void 0;\n    const withSqlChunks = [sql`with `];\n    for (const [i, w] of queries.entries()) {\n      withSqlChunks.push(sql`${sql.identifier(w._.alias)} as (${w._.sql})`);\n      if (i < queries.length - 1) {\n        withSqlChunks.push(sql`, `);\n      }\n    }\n    withSqlChunks.push(sql` `);\n    return sql.join(withSqlChunks);\n  }\n  buildDeleteQuery({ table, where, returning, withList, limit, orderBy }) {\n    const withSql = this.buildWithCTE(withList);\n    const returningSql = returning ? sql` returning ${this.buildSelection(returning, { isSingleTable: true })}` : void 0;\n    const whereSql = where ? sql` where ${where}` : void 0;\n    const orderBySql = this.buildOrderBy(orderBy);\n    const limitSql = this.buildLimit(limit);\n    return sql`${withSql}delete from ${table}${whereSql}${returningSql}${orderBySql}${limitSql}`;\n  }\n  buildUpdateSet(table, set) {\n    const tableColumns = table[Table.Symbol.Columns];\n    const columnNames = Object.keys(tableColumns).filter(\n      (colName) => set[colName] !== void 0 || tableColumns[colName]?.onUpdateFn !== void 0\n    );\n    const setSize = columnNames.length;\n    return sql.join(columnNames.flatMap((colName, i) => {\n      const col = tableColumns[colName];\n      const value = set[colName] ?? sql.param(col.onUpdateFn(), col);\n      const res = sql`${sql.identifier(this.casing.getColumnCasing(col))} = ${value}`;\n      if (i < setSize - 1) {\n        return [res, sql.raw(\", \")];\n      }\n      return [res];\n    }));\n  }\n  buildUpdateQuery({ table, set, where, returning, withList, joins, from, limit, orderBy }) {\n    const withSql = this.buildWithCTE(withList);\n    const setSql = this.buildUpdateSet(table, set);\n    const fromSql = from && sql.join([sql.raw(\" from \"), this.buildFromTable(from)]);\n    const joinsSql = this.buildJoins(joins);\n    const returningSql = returning ? sql` returning ${this.buildSelection(returning, { isSingleTable: true })}` : void 0;\n    const whereSql = where ? sql` where ${where}` : void 0;\n    const orderBySql = this.buildOrderBy(orderBy);\n    const limitSql = this.buildLimit(limit);\n    return sql`${withSql}update ${table} set ${setSql}${fromSql}${joinsSql}${whereSql}${returningSql}${orderBySql}${limitSql}`;\n  }\n  /**\n   * Builds selection SQL with provided fields/expressions\n   *\n   * Examples:\n   *\n   * `select <selection> from`\n   *\n   * `insert ... returning <selection>`\n   *\n   * If `isSingleTable` is true, then columns won't be prefixed with table name\n   */\n  buildSelection(fields, { isSingleTable = false } = {}) {\n    const columnsLen = fields.length;\n    const chunks = fields.flatMap(({ field }, i) => {\n      const chunk = [];\n      if (is(field, SQL.Aliased) && field.isSelectionField) {\n        chunk.push(sql.identifier(field.fieldAlias));\n      } else if (is(field, SQL.Aliased) || is(field, SQL)) {\n        const query = is(field, SQL.Aliased) ? field.sql : field;\n        if (isSingleTable) {\n          chunk.push(\n            new SQL(\n              query.queryChunks.map((c) => {\n                if (is(c, Column)) {\n                  return sql.identifier(this.casing.getColumnCasing(c));\n                }\n                return c;\n              })\n            )\n          );\n        } else {\n          chunk.push(query);\n        }\n        if (is(field, SQL.Aliased)) {\n          chunk.push(sql` as ${sql.identifier(field.fieldAlias)}`);\n        }\n      } else if (is(field, Column)) {\n        const tableName = field.table[Table.Symbol.Name];\n        if (isSingleTable) {\n          chunk.push(sql.identifier(this.casing.getColumnCasing(field)));\n        } else {\n          chunk.push(sql`${sql.identifier(tableName)}.${sql.identifier(this.casing.getColumnCasing(field))}`);\n        }\n      }\n      if (i < columnsLen - 1) {\n        chunk.push(sql`, `);\n      }\n      return chunk;\n    });\n    return sql.join(chunks);\n  }\n  buildJoins(joins) {\n    if (!joins || joins.length === 0) {\n      return void 0;\n    }\n    const joinsArray = [];\n    if (joins) {\n      for (const [index, joinMeta] of joins.entries()) {\n        if (index === 0) {\n          joinsArray.push(sql` `);\n        }\n        const table = joinMeta.table;\n        if (is(table, SQLiteTable)) {\n          const tableName = table[SQLiteTable.Symbol.Name];\n          const tableSchema = table[SQLiteTable.Symbol.Schema];\n          const origTableName = table[SQLiteTable.Symbol.OriginalName];\n          const alias = tableName === origTableName ? void 0 : joinMeta.alias;\n          joinsArray.push(\n            sql`${sql.raw(joinMeta.joinType)} join ${tableSchema ? sql`${sql.identifier(tableSchema)}.` : void 0}${sql.identifier(origTableName)}${alias && sql` ${sql.identifier(alias)}`} on ${joinMeta.on}`\n          );\n        } else {\n          joinsArray.push(\n            sql`${sql.raw(joinMeta.joinType)} join ${table} on ${joinMeta.on}`\n          );\n        }\n        if (index < joins.length - 1) {\n          joinsArray.push(sql` `);\n        }\n      }\n    }\n    return sql.join(joinsArray);\n  }\n  buildLimit(limit) {\n    return typeof limit === \"object\" || typeof limit === \"number\" && limit >= 0 ? sql` limit ${limit}` : void 0;\n  }\n  buildOrderBy(orderBy) {\n    const orderByList = [];\n    if (orderBy) {\n      for (const [index, orderByValue] of orderBy.entries()) {\n        orderByList.push(orderByValue);\n        if (index < orderBy.length - 1) {\n          orderByList.push(sql`, `);\n        }\n      }\n    }\n    return orderByList.length > 0 ? sql` order by ${sql.join(orderByList)}` : void 0;\n  }\n  buildFromTable(table) {\n    if (is(table, Table) && table[Table.Symbol.OriginalName] !== table[Table.Symbol.Name]) {\n      return sql`${sql.identifier(table[Table.Symbol.OriginalName])} ${sql.identifier(table[Table.Symbol.Name])}`;\n    }\n    return table;\n  }\n  buildSelectQuery({\n    withList,\n    fields,\n    fieldsFlat,\n    where,\n    having,\n    table,\n    joins,\n    orderBy,\n    groupBy,\n    limit,\n    offset,\n    distinct,\n    setOperators\n  }) {\n    const fieldsList = fieldsFlat ?? orderSelectedFields(fields);\n    for (const f of fieldsList) {\n      if (is(f.field, Column) && getTableName(f.field.table) !== (is(table, Subquery) ? table._.alias : is(table, SQLiteViewBase) ? table[ViewBaseConfig].name : is(table, SQL) ? void 0 : getTableName(table)) && !((table2) => joins?.some(\n        ({ alias }) => alias === (table2[Table.Symbol.IsAlias] ? getTableName(table2) : table2[Table.Symbol.BaseName])\n      ))(f.field.table)) {\n        const tableName = getTableName(f.field.table);\n        throw new Error(\n          `Your \"${f.path.join(\"->\")}\" field references a column \"${tableName}\".\"${f.field.name}\", but the table \"${tableName}\" is not part of the query! Did you forget to join it?`\n        );\n      }\n    }\n    const isSingleTable = !joins || joins.length === 0;\n    const withSql = this.buildWithCTE(withList);\n    const distinctSql = distinct ? sql` distinct` : void 0;\n    const selection = this.buildSelection(fieldsList, { isSingleTable });\n    const tableSql = this.buildFromTable(table);\n    const joinsSql = this.buildJoins(joins);\n    const whereSql = where ? sql` where ${where}` : void 0;\n    const havingSql = having ? sql` having ${having}` : void 0;\n    const groupByList = [];\n    if (groupBy) {\n      for (const [index, groupByValue] of groupBy.entries()) {\n        groupByList.push(groupByValue);\n        if (index < groupBy.length - 1) {\n          groupByList.push(sql`, `);\n        }\n      }\n    }\n    const groupBySql = groupByList.length > 0 ? sql` group by ${sql.join(groupByList)}` : void 0;\n    const orderBySql = this.buildOrderBy(orderBy);\n    const limitSql = this.buildLimit(limit);\n    const offsetSql = offset ? sql` offset ${offset}` : void 0;\n    const finalQuery = sql`${withSql}select${distinctSql} ${selection} from ${tableSql}${joinsSql}${whereSql}${groupBySql}${havingSql}${orderBySql}${limitSql}${offsetSql}`;\n    if (setOperators.length > 0) {\n      return this.buildSetOperations(finalQuery, setOperators);\n    }\n    return finalQuery;\n  }\n  buildSetOperations(leftSelect, setOperators) {\n    const [setOperator, ...rest] = setOperators;\n    if (!setOperator) {\n      throw new Error(\"Cannot pass undefined values to any set operator\");\n    }\n    if (rest.length === 0) {\n      return this.buildSetOperationQuery({ leftSelect, setOperator });\n    }\n    return this.buildSetOperations(\n      this.buildSetOperationQuery({ leftSelect, setOperator }),\n      rest\n    );\n  }\n  buildSetOperationQuery({\n    leftSelect,\n    setOperator: { type, isAll, rightSelect, limit, orderBy, offset }\n  }) {\n    const leftChunk = sql`${leftSelect.getSQL()} `;\n    const rightChunk = sql`${rightSelect.getSQL()}`;\n    let orderBySql;\n    if (orderBy && orderBy.length > 0) {\n      const orderByValues = [];\n      for (const singleOrderBy of orderBy) {\n        if (is(singleOrderBy, SQLiteColumn)) {\n          orderByValues.push(sql.identifier(singleOrderBy.name));\n        } else if (is(singleOrderBy, SQL)) {\n          for (let i = 0; i < singleOrderBy.queryChunks.length; i++) {\n            const chunk = singleOrderBy.queryChunks[i];\n            if (is(chunk, SQLiteColumn)) {\n              singleOrderBy.queryChunks[i] = sql.identifier(this.casing.getColumnCasing(chunk));\n            }\n          }\n          orderByValues.push(sql`${singleOrderBy}`);\n        } else {\n          orderByValues.push(sql`${singleOrderBy}`);\n        }\n      }\n      orderBySql = sql` order by ${sql.join(orderByValues, sql`, `)}`;\n    }\n    const limitSql = typeof limit === \"object\" || typeof limit === \"number\" && limit >= 0 ? sql` limit ${limit}` : void 0;\n    const operatorChunk = sql.raw(`${type} ${isAll ? \"all \" : \"\"}`);\n    const offsetSql = offset ? sql` offset ${offset}` : void 0;\n    return sql`${leftChunk}${operatorChunk}${rightChunk}${orderBySql}${limitSql}${offsetSql}`;\n  }\n  buildInsertQuery({ table, values: valuesOrSelect, onConflict, returning, withList, select }) {\n    const valuesSqlList = [];\n    const columns = table[Table.Symbol.Columns];\n    const colEntries = Object.entries(columns).filter(\n      ([_, col]) => !col.shouldDisableInsert()\n    );\n    const insertOrder = colEntries.map(([, column]) => sql.identifier(this.casing.getColumnCasing(column)));\n    if (select) {\n      const select2 = valuesOrSelect;\n      if (is(select2, SQL)) {\n        valuesSqlList.push(select2);\n      } else {\n        valuesSqlList.push(select2.getSQL());\n      }\n    } else {\n      const values = valuesOrSelect;\n      valuesSqlList.push(sql.raw(\"values \"));\n      for (const [valueIndex, value] of values.entries()) {\n        const valueList = [];\n        for (const [fieldName, col] of colEntries) {\n          const colValue = value[fieldName];\n          if (colValue === void 0 || is(colValue, Param) && colValue.value === void 0) {\n            let defaultValue;\n            if (col.default !== null && col.default !== void 0) {\n              defaultValue = is(col.default, SQL) ? col.default : sql.param(col.default, col);\n            } else if (col.defaultFn !== void 0) {\n              const defaultFnResult = col.defaultFn();\n              defaultValue = is(defaultFnResult, SQL) ? defaultFnResult : sql.param(defaultFnResult, col);\n            } else if (!col.default && col.onUpdateFn !== void 0) {\n              const onUpdateFnResult = col.onUpdateFn();\n              defaultValue = is(onUpdateFnResult, SQL) ? onUpdateFnResult : sql.param(onUpdateFnResult, col);\n            } else {\n              defaultValue = sql`null`;\n            }\n            valueList.push(defaultValue);\n          } else {\n            valueList.push(colValue);\n          }\n        }\n        valuesSqlList.push(valueList);\n        if (valueIndex < values.length - 1) {\n          valuesSqlList.push(sql`, `);\n        }\n      }\n    }\n    const withSql = this.buildWithCTE(withList);\n    const valuesSql = sql.join(valuesSqlList);\n    const returningSql = returning ? sql` returning ${this.buildSelection(returning, { isSingleTable: true })}` : void 0;\n    const onConflictSql = onConflict ? sql` on conflict ${onConflict}` : void 0;\n    return sql`${withSql}insert into ${table} ${insertOrder} ${valuesSql}${onConflictSql}${returningSql}`;\n  }\n  sqlToQuery(sql2, invokeSource) {\n    return sql2.toQuery({\n      casing: this.casing,\n      escapeName: this.escapeName,\n      escapeParam: this.escapeParam,\n      escapeString: this.escapeString,\n      invokeSource\n    });\n  }\n  buildRelationalQuery({\n    fullSchema,\n    schema,\n    tableNamesMap,\n    table,\n    tableConfig,\n    queryConfig: config,\n    tableAlias,\n    nestedQueryRelation,\n    joinOn\n  }) {\n    let selection = [];\n    let limit, offset, orderBy = [], where;\n    const joins = [];\n    if (config === true) {\n      const selectionEntries = Object.entries(tableConfig.columns);\n      selection = selectionEntries.map(([key, value]) => ({\n        dbKey: value.name,\n        tsKey: key,\n        field: aliasedTableColumn(value, tableAlias),\n        relationTableTsKey: void 0,\n        isJson: false,\n        selection: []\n      }));\n    } else {\n      const aliasedColumns = Object.fromEntries(\n        Object.entries(tableConfig.columns).map(([key, value]) => [key, aliasedTableColumn(value, tableAlias)])\n      );\n      if (config.where) {\n        const whereSql = typeof config.where === \"function\" ? config.where(aliasedColumns, getOperators()) : config.where;\n        where = whereSql && mapColumnsInSQLToAlias(whereSql, tableAlias);\n      }\n      const fieldsSelection = [];\n      let selectedColumns = [];\n      if (config.columns) {\n        let isIncludeMode = false;\n        for (const [field, value] of Object.entries(config.columns)) {\n          if (value === void 0) {\n            continue;\n          }\n          if (field in tableConfig.columns) {\n            if (!isIncludeMode && value === true) {\n              isIncludeMode = true;\n            }\n            selectedColumns.push(field);\n          }\n        }\n        if (selectedColumns.length > 0) {\n          selectedColumns = isIncludeMode ? selectedColumns.filter((c) => config.columns?.[c] === true) : Object.keys(tableConfig.columns).filter((key) => !selectedColumns.includes(key));\n        }\n      } else {\n        selectedColumns = Object.keys(tableConfig.columns);\n      }\n      for (const field of selectedColumns) {\n        const column = tableConfig.columns[field];\n        fieldsSelection.push({ tsKey: field, value: column });\n      }\n      let selectedRelations = [];\n      if (config.with) {\n        selectedRelations = Object.entries(config.with).filter((entry) => !!entry[1]).map(([tsKey, queryConfig]) => ({ tsKey, queryConfig, relation: tableConfig.relations[tsKey] }));\n      }\n      let extras;\n      if (config.extras) {\n        extras = typeof config.extras === \"function\" ? config.extras(aliasedColumns, { sql }) : config.extras;\n        for (const [tsKey, value] of Object.entries(extras)) {\n          fieldsSelection.push({\n            tsKey,\n            value: mapColumnsInAliasedSQLToAlias(value, tableAlias)\n          });\n        }\n      }\n      for (const { tsKey, value } of fieldsSelection) {\n        selection.push({\n          dbKey: is(value, SQL.Aliased) ? value.fieldAlias : tableConfig.columns[tsKey].name,\n          tsKey,\n          field: is(value, Column) ? aliasedTableColumn(value, tableAlias) : value,\n          relationTableTsKey: void 0,\n          isJson: false,\n          selection: []\n        });\n      }\n      let orderByOrig = typeof config.orderBy === \"function\" ? config.orderBy(aliasedColumns, getOrderByOperators()) : config.orderBy ?? [];\n      if (!Array.isArray(orderByOrig)) {\n        orderByOrig = [orderByOrig];\n      }\n      orderBy = orderByOrig.map((orderByValue) => {\n        if (is(orderByValue, Column)) {\n          return aliasedTableColumn(orderByValue, tableAlias);\n        }\n        return mapColumnsInSQLToAlias(orderByValue, tableAlias);\n      });\n      limit = config.limit;\n      offset = config.offset;\n      for (const {\n        tsKey: selectedRelationTsKey,\n        queryConfig: selectedRelationConfigValue,\n        relation\n      } of selectedRelations) {\n        const normalizedRelation = normalizeRelation(schema, tableNamesMap, relation);\n        const relationTableName = getTableUniqueName(relation.referencedTable);\n        const relationTableTsName = tableNamesMap[relationTableName];\n        const relationTableAlias = `${tableAlias}_${selectedRelationTsKey}`;\n        const joinOn2 = and(\n          ...normalizedRelation.fields.map(\n            (field2, i) => eq(\n              aliasedTableColumn(normalizedRelation.references[i], relationTableAlias),\n              aliasedTableColumn(field2, tableAlias)\n            )\n          )\n        );\n        const builtRelation = this.buildRelationalQuery({\n          fullSchema,\n          schema,\n          tableNamesMap,\n          table: fullSchema[relationTableTsName],\n          tableConfig: schema[relationTableTsName],\n          queryConfig: is(relation, One) ? selectedRelationConfigValue === true ? { limit: 1 } : { ...selectedRelationConfigValue, limit: 1 } : selectedRelationConfigValue,\n          tableAlias: relationTableAlias,\n          joinOn: joinOn2,\n          nestedQueryRelation: relation\n        });\n        const field = sql`(${builtRelation.sql})`.as(selectedRelationTsKey);\n        selection.push({\n          dbKey: selectedRelationTsKey,\n          tsKey: selectedRelationTsKey,\n          field,\n          relationTableTsKey: relationTableTsName,\n          isJson: true,\n          selection: builtRelation.selection\n        });\n      }\n    }\n    if (selection.length === 0) {\n      throw new DrizzleError({\n        message: `No fields selected for table \"${tableConfig.tsName}\" (\"${tableAlias}\"). You need to have at least one item in \"columns\", \"with\" or \"extras\". If you need to select all columns, omit the \"columns\" key or set it to undefined.`\n      });\n    }\n    let result;\n    where = and(joinOn, where);\n    if (nestedQueryRelation) {\n      let field = sql`json_array(${sql.join(\n        selection.map(\n          ({ field: field2 }) => is(field2, SQLiteColumn) ? sql.identifier(this.casing.getColumnCasing(field2)) : is(field2, SQL.Aliased) ? field2.sql : field2\n        ),\n        sql`, `\n      )})`;\n      if (is(nestedQueryRelation, Many)) {\n        field = sql`coalesce(json_group_array(${field}), json_array())`;\n      }\n      const nestedSelection = [{\n        dbKey: \"data\",\n        tsKey: \"data\",\n        field: field.as(\"data\"),\n        isJson: true,\n        relationTableTsKey: tableConfig.tsName,\n        selection\n      }];\n      const needsSubquery = limit !== void 0 || offset !== void 0 || orderBy.length > 0;\n      if (needsSubquery) {\n        result = this.buildSelectQuery({\n          table: aliasedTable(table, tableAlias),\n          fields: {},\n          fieldsFlat: [\n            {\n              path: [],\n              field: sql.raw(\"*\")\n            }\n          ],\n          where,\n          limit,\n          offset,\n          orderBy,\n          setOperators: []\n        });\n        where = void 0;\n        limit = void 0;\n        offset = void 0;\n        orderBy = void 0;\n      } else {\n        result = aliasedTable(table, tableAlias);\n      }\n      result = this.buildSelectQuery({\n        table: is(result, SQLiteTable) ? result : new Subquery(result, {}, tableAlias),\n        fields: {},\n        fieldsFlat: nestedSelection.map(({ field: field2 }) => ({\n          path: [],\n          field: is(field2, Column) ? aliasedTableColumn(field2, tableAlias) : field2\n        })),\n        joins,\n        where,\n        limit,\n        offset,\n        orderBy,\n        setOperators: []\n      });\n    } else {\n      result = this.buildSelectQuery({\n        table: aliasedTable(table, tableAlias),\n        fields: {},\n        fieldsFlat: selection.map(({ field }) => ({\n          path: [],\n          field: is(field, Column) ? aliasedTableColumn(field, tableAlias) : field\n        })),\n        joins,\n        where,\n        limit,\n        offset,\n        orderBy,\n        setOperators: []\n      });\n    }\n    return {\n      tableTsKey: tableConfig.tsName,\n      sql: result,\n      selection\n    };\n  }\n}\nclass SQLiteSyncDialect extends SQLiteDialect {\n  static [entityKind] = \"SQLiteSyncDialect\";\n  migrate(migrations, session, config) {\n    const migrationsTable = config === void 0 ? \"__drizzle_migrations\" : typeof config === \"string\" ? \"__drizzle_migrations\" : config.migrationsTable ?? \"__drizzle_migrations\";\n    const migrationTableCreate = sql`\n\t\t\tCREATE TABLE IF NOT EXISTS ${sql.identifier(migrationsTable)} (\n\t\t\t\tid SERIAL PRIMARY KEY,\n\t\t\t\thash text NOT NULL,\n\t\t\t\tcreated_at numeric\n\t\t\t)\n\t\t`;\n    session.run(migrationTableCreate);\n    const dbMigrations = session.values(\n      sql`SELECT id, hash, created_at FROM ${sql.identifier(migrationsTable)} ORDER BY created_at DESC LIMIT 1`\n    );\n    const lastDbMigration = dbMigrations[0] ?? void 0;\n    session.run(sql`BEGIN`);\n    try {\n      for (const migration of migrations) {\n        if (!lastDbMigration || Number(lastDbMigration[2]) < migration.folderMillis) {\n          for (const stmt of migration.sql) {\n            session.run(sql.raw(stmt));\n          }\n          session.run(\n            sql`INSERT INTO ${sql.identifier(migrationsTable)} (\"hash\", \"created_at\") VALUES(${migration.hash}, ${migration.folderMillis})`\n          );\n        }\n      }\n      session.run(sql`COMMIT`);\n    } catch (e) {\n      session.run(sql`ROLLBACK`);\n      throw e;\n    }\n  }\n}\nclass SQLiteAsyncDialect extends SQLiteDialect {\n  static [entityKind] = \"SQLiteAsyncDialect\";\n  async migrate(migrations, session, config) {\n    const migrationsTable = config === void 0 ? \"__drizzle_migrations\" : typeof config === \"string\" ? \"__drizzle_migrations\" : config.migrationsTable ?? \"__drizzle_migrations\";\n    const migrationTableCreate = sql`\n\t\t\tCREATE TABLE IF NOT EXISTS ${sql.identifier(migrationsTable)} (\n\t\t\t\tid SERIAL PRIMARY KEY,\n\t\t\t\thash text NOT NULL,\n\t\t\t\tcreated_at numeric\n\t\t\t)\n\t\t`;\n    await session.run(migrationTableCreate);\n    const dbMigrations = await session.values(\n      sql`SELECT id, hash, created_at FROM ${sql.identifier(migrationsTable)} ORDER BY created_at DESC LIMIT 1`\n    );\n    const lastDbMigration = dbMigrations[0] ?? void 0;\n    await session.transaction(async (tx) => {\n      for (const migration of migrations) {\n        if (!lastDbMigration || Number(lastDbMigration[2]) < migration.folderMillis) {\n          for (const stmt of migration.sql) {\n            await tx.run(sql.raw(stmt));\n          }\n          await tx.run(\n            sql`INSERT INTO ${sql.identifier(migrationsTable)} (\"hash\", \"created_at\") VALUES(${migration.hash}, ${migration.folderMillis})`\n          );\n        }\n      }\n    });\n  }\n}\nexport {\n  SQLiteAsyncDialect,\n  SQLiteDialect,\n  SQLiteSyncDialect\n};\n//# sourceMappingURL=dialect.js.map", "import { entityKind } from \"../entity.js\";\nclass IndexBuilderOn {\n  constructor(name, unique) {\n    this.name = name;\n    this.unique = unique;\n  }\n  static [entityKind] = \"SQLiteIndexBuilderOn\";\n  on(...columns) {\n    return new IndexBuilder(this.name, columns, this.unique);\n  }\n}\nclass IndexBuilder {\n  static [entityKind] = \"SQLiteIndexBuilder\";\n  /** @internal */\n  config;\n  constructor(name, columns, unique) {\n    this.config = {\n      name,\n      columns,\n      unique,\n      where: void 0\n    };\n  }\n  /**\n   * Condition for partial index.\n   */\n  where(condition) {\n    this.config.where = condition;\n    return this;\n  }\n  /** @internal */\n  build(table) {\n    return new Index(this.config, table);\n  }\n}\nclass Index {\n  static [entityKind] = \"SQLiteIndex\";\n  config;\n  constructor(config, table) {\n    this.config = { ...config, table };\n  }\n}\nfunction index(name) {\n  return new IndexBuilderOn(name, false);\n}\nfunction uniqueIndex(name) {\n  return new IndexBuilderOn(name, true);\n}\nexport {\n  Index,\n  IndexBuilder,\n  IndexBuilderOn,\n  index,\n  uniqueIndex\n};\n//# sourceMappingURL=indexes.js.map", "import { entityKind } from \"../entity.js\";\nimport { SQLiteTable } from \"./table.js\";\nfunction primaryKey(...config) {\n  if (config[0].columns) {\n    return new PrimaryKeyBuilder(config[0].columns, config[0].name);\n  }\n  return new PrimaryKeyBuilder(config);\n}\nclass PrimaryKeyBuilder {\n  static [entityKind] = \"SQLitePrimaryKeyBuilder\";\n  /** @internal */\n  columns;\n  /** @internal */\n  name;\n  constructor(columns, name) {\n    this.columns = columns;\n    this.name = name;\n  }\n  /** @internal */\n  build(table) {\n    return new PrimaryKey(table, this.columns, this.name);\n  }\n}\nclass PrimaryKey {\n  constructor(table, columns, name) {\n    this.table = table;\n    this.columns = columns;\n    this.name = name;\n  }\n  static [entityKind] = \"SQLitePrimaryKey\";\n  columns;\n  name;\n  getName() {\n    return this.name ?? `${this.table[SQLiteTable.Symbol.Name]}_${this.columns.map((column) => column.name).join(\"_\")}_pk`;\n  }\n}\nexport {\n  PrimaryKey,\n  PrimaryKeyBuilder,\n  primaryKey\n};\n//# sourceMappingURL=primary-keys.js.map", "import { entityKind } from \"../../entity.js\";\nimport { getColumnNameAndConfig } from \"../../utils.js\";\nimport { SQLiteColumn, SQLiteColumnBuilder } from \"./common.js\";\nclass SQLiteBigIntBuilder extends SQLiteColumnBuilder {\n  static [entityKind] = \"SQLiteBigIntBuilder\";\n  constructor(name) {\n    super(name, \"bigint\", \"SQLiteBigInt\");\n  }\n  /** @internal */\n  build(table) {\n    return new SQLiteBigInt(table, this.config);\n  }\n}\nclass SQLiteBigInt extends SQLiteColumn {\n  static [entityKind] = \"SQLiteBigInt\";\n  getSQLType() {\n    return \"blob\";\n  }\n  mapFromDriverValue(value) {\n    return BigInt(Buffer.isBuffer(value) ? value.toString() : String.fromCodePoint(...value));\n  }\n  mapToDriverValue(value) {\n    return Buffer.from(value.toString());\n  }\n}\nclass SQLiteBlobJsonBuilder extends SQLiteColumnBuilder {\n  static [entityKind] = \"SQLiteBlobJsonBuilder\";\n  constructor(name) {\n    super(name, \"json\", \"SQLiteBlobJson\");\n  }\n  /** @internal */\n  build(table) {\n    return new SQLiteBlobJson(\n      table,\n      this.config\n    );\n  }\n}\nclass SQLiteBlobJson extends SQLiteColumn {\n  static [entityKind] = \"SQLiteBlobJson\";\n  getSQLType() {\n    return \"blob\";\n  }\n  mapFromDriverValue(value) {\n    return JSON.parse(Buffer.isBuffer(value) ? value.toString() : String.fromCodePoint(...value));\n  }\n  mapToDriverValue(value) {\n    return Buffer.from(JSON.stringify(value));\n  }\n}\nclass SQLiteBlobBufferBuilder extends SQLiteColumnBuilder {\n  static [entityKind] = \"SQLiteBlobBufferBuilder\";\n  constructor(name) {\n    super(name, \"buffer\", \"SQLiteBlobBuffer\");\n  }\n  /** @internal */\n  build(table) {\n    return new SQLiteBlobBuffer(table, this.config);\n  }\n}\nclass SQLiteBlobBuffer extends SQLiteColumn {\n  static [entityKind] = \"SQLiteBlobBuffer\";\n  getSQLType() {\n    return \"blob\";\n  }\n}\nfunction blob(a, b) {\n  const { name, config } = getColumnNameAndConfig(a, b);\n  if (config?.mode === \"json\") {\n    return new SQLiteBlobJsonBuilder(name);\n  }\n  if (config?.mode === \"bigint\") {\n    return new SQLiteBigIntBuilder(name);\n  }\n  return new SQLiteBlobBufferBuilder(name);\n}\nexport {\n  SQLiteBigInt,\n  SQLiteBigIntBuilder,\n  SQLiteBlobBuffer,\n  SQLiteBlobBufferBuilder,\n  SQLiteBlobJson,\n  SQLiteBlobJsonBuilder,\n  blob\n};\n//# sourceMappingURL=blob.js.map", "import { entityKind } from \"../../entity.js\";\nimport { getColumnNameAndConfig } from \"../../utils.js\";\nimport { SQLiteColumn, SQLiteColumnBuilder } from \"./common.js\";\nclass SQLiteCustomColumnBuilder extends SQLiteColumnBuilder {\n  static [entityKind] = \"SQLiteCustomColumnBuilder\";\n  constructor(name, fieldConfig, customTypeParams) {\n    super(name, \"custom\", \"SQLiteCustomColumn\");\n    this.config.fieldConfig = fieldConfig;\n    this.config.customTypeParams = customTypeParams;\n  }\n  /** @internal */\n  build(table) {\n    return new SQLiteCustomColumn(\n      table,\n      this.config\n    );\n  }\n}\nclass SQLiteCustomColumn extends SQLiteColumn {\n  static [entityKind] = \"SQLiteCustomColumn\";\n  sqlName;\n  mapTo;\n  mapFrom;\n  constructor(table, config) {\n    super(table, config);\n    this.sqlName = config.customTypeParams.dataType(config.fieldConfig);\n    this.mapTo = config.customTypeParams.toDriver;\n    this.mapFrom = config.customTypeParams.fromDriver;\n  }\n  getSQLType() {\n    return this.sqlName;\n  }\n  mapFromDriverValue(value) {\n    return typeof this.mapFrom === \"function\" ? this.mapFrom(value) : value;\n  }\n  mapToDriverValue(value) {\n    return typeof this.mapTo === \"function\" ? this.mapTo(value) : value;\n  }\n}\nfunction customType(customTypeParams) {\n  return (a, b) => {\n    const { name, config } = getColumnNameAndConfig(a, b);\n    return new SQLiteCustomColumnBuilder(\n      name,\n      config,\n      customTypeParams\n    );\n  };\n}\nexport {\n  SQLiteCustomColumn,\n  SQLiteCustomColumnBuilder,\n  customType\n};\n//# sourceMappingURL=custom.js.map", "import { entityKind } from \"../../entity.js\";\nimport { SQLiteColumn, SQLiteColumnBuilder } from \"./common.js\";\nclass SQLiteNumericBuilder extends SQLiteColumnBuilder {\n  static [entityKind] = \"SQLiteNumericBuilder\";\n  constructor(name) {\n    super(name, \"string\", \"SQLiteNumeric\");\n  }\n  /** @internal */\n  build(table) {\n    return new SQLiteNumeric(\n      table,\n      this.config\n    );\n  }\n}\nclass SQLiteNumeric extends SQLiteColumn {\n  static [entityKind] = \"SQLiteNumeric\";\n  getSQLType() {\n    return \"numeric\";\n  }\n}\nfunction numeric(name) {\n  return new SQLiteNumericBuilder(name ?? \"\");\n}\nexport {\n  SQLiteNumeric,\n  SQLiteNumericBuilder,\n  numeric\n};\n//# sourceMappingURL=numeric.js.map", "import { entityKind } from \"../../entity.js\";\nimport { SQLiteColumn, SQLiteColumnBuilder } from \"./common.js\";\nclass SQLiteRealBuilder extends SQLiteColumnBuilder {\n  static [entityKind] = \"SQLiteRealBuilder\";\n  constructor(name) {\n    super(name, \"number\", \"SQLiteReal\");\n  }\n  /** @internal */\n  build(table) {\n    return new SQLiteReal(table, this.config);\n  }\n}\nclass SQLiteReal extends SQLiteColumn {\n  static [entityKind] = \"SQLiteReal\";\n  getSQLType() {\n    return \"real\";\n  }\n}\nfunction real(name) {\n  return new SQLiteRealBuilder(name ?? \"\");\n}\nexport {\n  SQLiteReal,\n  SQLiteRealBuilder,\n  real\n};\n//# sourceMappingURL=real.js.map", "import { blob } from \"./blob.js\";\nimport { customType } from \"./custom.js\";\nimport { integer } from \"./integer.js\";\nimport { numeric } from \"./numeric.js\";\nimport { real } from \"./real.js\";\nimport { text } from \"./text.js\";\nfunction getSQLiteColumnBuilders() {\n  return {\n    blob,\n    customType,\n    integer,\n    numeric,\n    real,\n    text\n  };\n}\nexport {\n  getSQLiteColumnBuilders\n};\n//# sourceMappingURL=all.js.map", "import { entityKind } from \"../entity.js\";\nimport { Table } from \"../table.js\";\nimport { getSQLiteColumnBuilders } from \"./columns/all.js\";\nconst InlineForeignKeys = Symbol.for(\"drizzle:SQLiteInlineForeignKeys\");\nclass SQLiteTable extends Table {\n  static [entityKind] = \"SQLiteTable\";\n  /** @internal */\n  static Symbol = Object.assign({}, Table.Symbol, {\n    InlineForeignKeys\n  });\n  /** @internal */\n  [Table.Symbol.Columns];\n  /** @internal */\n  [InlineForeignKeys] = [];\n  /** @internal */\n  [Table.Symbol.ExtraConfigBuilder] = void 0;\n}\nfunction sqliteTableBase(name, columns, extraConfig, schema, baseName = name) {\n  const rawTable = new SQLiteTable(name, schema, baseName);\n  const parsedColumns = typeof columns === \"function\" ? columns(getSQLiteColumnBuilders()) : columns;\n  const builtColumns = Object.fromEntries(\n    Object.entries(parsedColumns).map(([name2, colBuilderBase]) => {\n      const colBuilder = colBuilderBase;\n      colBuilder.setName(name2);\n      const column = colBuilder.build(rawTable);\n      rawTable[InlineForeignKeys].push(...colBuilder.buildForeignKeys(column, rawTable));\n      return [name2, column];\n    })\n  );\n  const table = Object.assign(rawTable, builtColumns);\n  table[Table.Symbol.Columns] = builtColumns;\n  table[Table.Symbol.ExtraConfigColumns] = builtColumns;\n  if (extraConfig) {\n    table[SQLiteTable.Symbol.ExtraConfigBuilder] = extraConfig;\n  }\n  return table;\n}\nconst sqliteTable = (name, columns, extraConfig) => {\n  return sqliteTableBase(name, columns, extraConfig);\n};\nfunction sqliteTableCreator(customizeTableName) {\n  return (name, columns, extraConfig) => {\n    return sqliteTableBase(customizeTableName(name), columns, extraConfig, void 0, name);\n  };\n}\nexport {\n  InlineForeignKeys,\n  SQLiteTable,\n  sqliteTable,\n  sqliteTableCreator\n};\n//# sourceMappingURL=table.js.map", "import { entityKind } from \"../entity.js\";\nimport { View } from \"../sql/sql.js\";\nclass SQLiteViewBase extends View {\n  static [entityKind] = \"SQLiteViewBase\";\n}\nexport {\n  SQLiteViewBase\n};\n//# sourceMappingURL=view-base.js.map", "import { entityKind } from \"./entity.js\";\nclass Subquery {\n  static [entityKind] = \"Subquery\";\n  constructor(sql, selection, alias, isWith = false) {\n    this._ = {\n      brand: \"Subquery\",\n      sql,\n      selectedFields: selection,\n      alias,\n      isWith\n    };\n  }\n  // getSQL(): SQL<unknown> {\n  // \treturn new SQL([this]);\n  // }\n}\nclass WithSubquery extends Subquery {\n  static [entityKind] = \"WithSubquery\";\n}\nexport {\n  Subquery,\n  WithSubquery\n};\n//# sourceMappingURL=subquery.js.map", "import { entityKind } from \"./entity.js\";\nimport { TableName } from \"./table.utils.js\";\nconst Schema = Symbol.for(\"drizzle:Schema\");\nconst Columns = Symbol.for(\"drizzle:Columns\");\nconst ExtraConfigColumns = Symbol.for(\"drizzle:ExtraConfigColumns\");\nconst OriginalName = Symbol.for(\"drizzle:OriginalName\");\nconst BaseName = Symbol.for(\"drizzle:BaseName\");\nconst IsAlias = Symbol.for(\"drizzle:IsAlias\");\nconst ExtraConfigBuilder = Symbol.for(\"drizzle:ExtraConfigBuilder\");\nconst IsDrizzleTable = Symbol.for(\"drizzle:IsDrizzleTable\");\nclass Table {\n  static [entityKind] = \"Table\";\n  /** @internal */\n  static Symbol = {\n    Name: TableName,\n    Schema,\n    OriginalName,\n    Columns,\n    ExtraConfigColumns,\n    BaseName,\n    IsAlias,\n    ExtraConfigBuilder\n  };\n  /**\n   * @internal\n   * Can be changed if the table is aliased.\n   */\n  [TableName];\n  /**\n   * @internal\n   * Used to store the original name of the table, before any aliasing.\n   */\n  [OriginalName];\n  /** @internal */\n  [Schema];\n  /** @internal */\n  [Columns];\n  /** @internal */\n  [ExtraConfigColumns];\n  /**\n   *  @internal\n   * Used to store the table name before the transformation via the `tableCreator` functions.\n   */\n  [BaseName];\n  /** @internal */\n  [IsAlias] = false;\n  /** @internal */\n  [IsDrizzleTable] = true;\n  /** @internal */\n  [ExtraConfigBuilder] = void 0;\n  constructor(name, schema, baseName) {\n    this[TableName] = this[OriginalName] = name;\n    this[Schema] = schema;\n    this[BaseName] = baseName;\n  }\n}\nfunction isTable(table) {\n  return typeof table === \"object\" && table !== null && IsDrizzleTable in table;\n}\nfunction getTableName(table) {\n  return table[TableName];\n}\nfunction getTableUniqueName(table) {\n  return `${table[Schema] ?? \"public\"}.${table[TableName]}`;\n}\nexport {\n  BaseName,\n  Columns,\n  ExtraConfigBuilder,\n  ExtraConfigColumns,\n  IsAlias,\n  OriginalName,\n  Schema,\n  Table,\n  getTableName,\n  getTableUniqueName,\n  isTable\n};\n//# sourceMappingURL=table.js.map", "const TableName = Symbol.for(\"drizzle:Name\");\nexport {\n  TableName\n};\n//# sourceMappingURL=table.utils.js.map", "function iife(fn, ...args) {\n  return fn(...args);\n}\nexport {\n  iife\n};\n//# sourceMappingURL=tracing-utils.js.map", "// package.json\nvar version = \"0.36.4\";\n\n// src/version.ts\nvar compatibilityVersion = 10;\nexport {\n  compatibilityVersion,\n  version as npmVersion\n};\n", "import { iife } from \"./tracing-utils.js\";\nimport { npmVersion } from \"./version.js\";\nlet otel;\nlet rawTracer;\nconst tracer = {\n  startActiveSpan(name, fn) {\n    if (!otel) {\n      return fn();\n    }\n    if (!rawTracer) {\n      rawTracer = otel.trace.getTracer(\"drizzle-orm\", npmVersion);\n    }\n    return iife(\n      (otel2, rawTracer2) => rawTracer2.startActiveSpan(\n        name,\n        (span) => {\n          try {\n            return fn(span);\n          } catch (e) {\n            span.setStatus({\n              code: otel2.SpanStatusCode.ERROR,\n              message: e instanceof Error ? e.message : \"Unknown error\"\n              // eslint-disable-line no-instanceof/no-instanceof\n            });\n            throw e;\n          } finally {\n            span.end();\n          }\n        }\n      ),\n      otel,\n      rawTracer\n    );\n  }\n};\nexport {\n  tracer\n};\n//# sourceMappingURL=tracing.js.map", "import { Column } from \"./column.js\";\nimport { is } from \"./entity.js\";\nimport { Param, SQL, View } from \"./sql/sql.js\";\nimport { Subquery } from \"./subquery.js\";\nimport { getTableName, Table } from \"./table.js\";\nimport { ViewBaseConfig } from \"./view-common.js\";\nfunction mapResultRow(columns, row, joinsNotNullableMap) {\n  const nullifyMap = {};\n  const result = columns.reduce(\n    (result2, { path, field }, columnIndex) => {\n      let decoder;\n      if (is(field, Column)) {\n        decoder = field;\n      } else if (is(field, SQL)) {\n        decoder = field.decoder;\n      } else {\n        decoder = field.sql.decoder;\n      }\n      let node = result2;\n      for (const [pathChunkIndex, pathChunk] of path.entries()) {\n        if (pathChunkIndex < path.length - 1) {\n          if (!(pathChunk in node)) {\n            node[pathChunk] = {};\n          }\n          node = node[pathChunk];\n        } else {\n          const rawValue = row[columnIndex];\n          const value = node[pathChunk] = rawValue === null ? null : decoder.mapFromDriverValue(rawValue);\n          if (joinsNotNullableMap && is(field, Column) && path.length === 2) {\n            const objectName = path[0];\n            if (!(objectName in nullifyMap)) {\n              nullifyMap[objectName] = value === null ? getTableName(field.table) : false;\n            } else if (typeof nullifyMap[objectName] === \"string\" && nullifyMap[objectName] !== getTableName(field.table)) {\n              nullifyMap[objectName] = false;\n            }\n          }\n        }\n      }\n      return result2;\n    },\n    {}\n  );\n  if (joinsNotNullableMap && Object.keys(nullifyMap).length > 0) {\n    for (const [objectName, tableName] of Object.entries(nullifyMap)) {\n      if (typeof tableName === \"string\" && !joinsNotNullableMap[tableName]) {\n        result[objectName] = null;\n      }\n    }\n  }\n  return result;\n}\nfunction orderSelectedFields(fields, pathPrefix) {\n  return Object.entries(fields).reduce((result, [name, field]) => {\n    if (typeof name !== \"string\") {\n      return result;\n    }\n    const newPath = pathPrefix ? [...pathPrefix, name] : [name];\n    if (is(field, Column) || is(field, SQL) || is(field, SQL.Aliased)) {\n      result.push({ path: newPath, field });\n    } else if (is(field, Table)) {\n      result.push(...orderSelectedFields(field[Table.Symbol.Columns], newPath));\n    } else {\n      result.push(...orderSelectedFields(field, newPath));\n    }\n    return result;\n  }, []);\n}\nfunction haveSameKeys(left, right) {\n  const leftKeys = Object.keys(left);\n  const rightKeys = Object.keys(right);\n  if (leftKeys.length !== rightKeys.length) {\n    return false;\n  }\n  for (const [index, key] of leftKeys.entries()) {\n    if (key !== rightKeys[index]) {\n      return false;\n    }\n  }\n  return true;\n}\nfunction mapUpdateSet(table, values) {\n  const entries = Object.entries(values).filter(([, value]) => value !== void 0).map(([key, value]) => {\n    if (is(value, SQL) || is(value, Column)) {\n      return [key, value];\n    } else {\n      return [key, new Param(value, table[Table.Symbol.Columns][key])];\n    }\n  });\n  if (entries.length === 0) {\n    throw new Error(\"No values to set\");\n  }\n  return Object.fromEntries(entries);\n}\nfunction applyMixins(baseClass, extendedClasses) {\n  for (const extendedClass of extendedClasses) {\n    for (const name of Object.getOwnPropertyNames(extendedClass.prototype)) {\n      if (name === \"constructor\")\n        continue;\n      Object.defineProperty(\n        baseClass.prototype,\n        name,\n        Object.getOwnPropertyDescriptor(extendedClass.prototype, name) || /* @__PURE__ */ Object.create(null)\n      );\n    }\n  }\n}\nfunction getTableColumns(table) {\n  return table[Table.Symbol.Columns];\n}\nfunction getTableLikeName(table) {\n  return is(table, Subquery) ? table._.alias : is(table, View) ? table[ViewBaseConfig].name : is(table, SQL) ? void 0 : table[Table.Symbol.IsAlias] ? table[Table.Symbol.Name] : table[Table.Symbol.BaseName];\n}\nfunction getColumnNameAndConfig(a, b) {\n  return {\n    name: typeof a === \"string\" && a.length > 0 ? a : \"\",\n    config: typeof a === \"object\" ? a : b\n  };\n}\nconst _ = {};\nconst __ = {};\nfunction isConfig(data) {\n  if (typeof data !== \"object\" || data === null)\n    return false;\n  if (data.constructor.name !== \"Object\")\n    return false;\n  if (\"logger\" in data) {\n    const type = typeof data[\"logger\"];\n    if (type !== \"boolean\" && (type !== \"object\" || typeof data[\"logger\"][\"logQuery\"] !== \"function\") && type !== \"undefined\")\n      return false;\n    return true;\n  }\n  if (\"schema\" in data) {\n    const type = typeof data[\"logger\"];\n    if (type !== \"object\" && type !== \"undefined\")\n      return false;\n    return true;\n  }\n  if (\"casing\" in data) {\n    const type = typeof data[\"logger\"];\n    if (type !== \"string\" && type !== \"undefined\")\n      return false;\n    return true;\n  }\n  if (\"mode\" in data) {\n    if (data[\"mode\"] !== \"default\" || data[\"mode\"] !== \"planetscale\" || data[\"mode\"] !== void 0)\n      return false;\n    return true;\n  }\n  if (\"connection\" in data) {\n    const type = typeof data[\"connection\"];\n    if (type !== \"string\" && type !== \"object\" && type !== \"undefined\")\n      return false;\n    return true;\n  }\n  if (\"client\" in data) {\n    const type = typeof data[\"client\"];\n    if (type !== \"object\" && type !== \"function\" && type !== \"undefined\")\n      return false;\n    return true;\n  }\n  if (Object.keys(data).length === 0)\n    return true;\n  return false;\n}\nexport {\n  applyMixins,\n  getColumnNameAndConfig,\n  getTableColumns,\n  getTableLikeName,\n  haveSameKeys,\n  isConfig,\n  mapResultRow,\n  mapUpdateSet,\n  orderSelectedFields\n};\n//# sourceMappingURL=utils.js.map", "const ViewBaseConfig = Symbol.for(\"drizzle:ViewBaseConfig\");\nexport {\n  ViewBaseConfig\n};\n//# sourceMappingURL=view-common.js.map"], "names": [], "sourceRoot": "", "ignoreList": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93]}