(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[75],{65521:e=>{"use strict";e.exports=require("node:async_hooks")},25356:e=>{"use strict";e.exports=require("node:buffer")},42776:(e,t,i)=>{"use strict";i.r(t),i.d(t,{ComponentMod:()=>B,default:()=>I});var r,a={};i.r(a),i.d(a,{GET:()=>E,POST:()=>C,runtime:()=>w});var s={};i.r(s),i.d(s,{patchFetch:()=>q,routeModule:()=>S,serverHooks:()=>b,workAsyncStorage:()=>v,workUnitAsyncStorage:()=>x});var n=i(26312),l=i(35471),o=i(22680),c=i(65954),d=i(14139),u=i(43113),m=i(83553),f=i(25601),p=i(17451),g=i(11950),h=i(8899);let w="edge",y=h.z.object({jwt:h.z.string().min(1,"邮箱凭证不能为空")});async function E(){if(!await (0,m.Yj)(p.Jj.MANAGE_WEBHOOK))return f.Rp.json({error:"权限不足"},{status:403});let e=await (0,m.j2)();if(!e?.user?.id)return f.Rp.json({error:"未授权"},{status:401});try{let e=await g.EmailCredentialManager.getAllCredentials();return f.Rp.json({credentials:e.map(e=>({id:e.id,emailAddress:e.emailAddress,jwt:e.jwt,enabled:e.enabled,createdAt:e.createdAt.toISOString(),lastUsedAt:e.lastUsedAt?.toISOString()||null,bindingCount:e.bindingCount||0}))})}catch(e){return console.error("Failed to fetch email credentials:",e),f.Rp.json({error:"获取邮箱凭证失败"},{status:500})}}async function C(e){let t=await (0,m.j2)();if(!t?.user?.id)return f.Rp.json({error:"未授权"},{status:401});try{let i=await e.json(),r=y.parse(i),a=await g.EmailCredentialManager.bindEmailByCredential(t.user.id,r.jwt);return f.Rp.json({success:!0,message:"邮箱绑定成功",binding:{id:a.id,emailAddress:a.emailAddress,createdAt:a.createdAt.toISOString()}})}catch(e){if(console.error("Failed to bind email:",e),e instanceof h.z.ZodError)return f.Rp.json({error:e.errors[0].message},{status:400});if(e instanceof Error)return f.Rp.json({error:e.message},{status:400});return f.Rp.json({error:"绑定邮箱失败"},{status:500})}}let S=new c.AppRouteRouteModule({definition:{kind:d.A.APP_ROUTE,page:"/api/email-credentials/route",pathname:"/api/email-credentials",filename:"route",bundlePath:"app/api/email-credentials/route"},resolvedPagePath:"F:\\CODE\\Project\\tempmail\\moemail\\app\\api\\email-credentials\\route.ts",nextConfigOutput:"",userland:a}),{workAsyncStorage:v,workUnitAsyncStorage:x,serverHooks:b}=S;function q(){return(0,u.V5)({workAsyncStorage:v,workUnitAsyncStorage:x})}let A=null==(r=self.__RSC_MANIFEST)?void 0:r["/api/email-credentials/route"],R=(e=>e?JSON.parse(e):void 0)(self.__RSC_SERVER_MANIFEST);A&&R&&(0,l.fQ)({page:"/api/email-credentials/route",clientReferenceManifest:A,serverActionsManifest:R,serverModuleMap:(0,n.e)({serverActionsManifest:R})});let B=s,I=o.s.wrap(S,{nextConfig:{env:{},eslint:{ignoreDuringBuilds:!1},typescript:{ignoreBuildErrors:!1,tsconfigPath:"tsconfig.json"},distDir:".next",cleanDistDir:!0,assetPrefix:"",cacheMaxMemorySize:0x3200000,configOrigin:"next.config.ts",useFileSystemPublicRoutes:!0,generateEtags:!0,pageExtensions:["tsx","ts","jsx","js"],poweredByHeader:!0,compress:!0,images:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",loaderFile:"",domains:[],disableStaticImages:!1,minimumCacheTTL:60,formats:["image/webp"],dangerouslyAllowSVG:!1,contentSecurityPolicy:"script-src 'none'; frame-src 'none'; sandbox;",contentDispositionType:"attachment",remotePatterns:[{protocol:"https",hostname:"avatars.githubusercontent.com"}],unoptimized:!1},devIndicators:{appIsrStatus:!0,buildActivity:!0,buildActivityPosition:"bottom-right"},onDemandEntries:{maxInactiveAge:6e4,pagesBufferLength:5},amp:{canonicalBase:""},basePath:"",sassOptions:{},trailingSlash:!1,i18n:null,productionBrowserSourceMaps:!1,excludeDefaultMomentLocales:!0,serverRuntimeConfig:{},publicRuntimeConfig:{},reactProductionProfiling:!1,reactStrictMode:null,reactMaxHeadersLength:6e3,httpAgentOptions:{keepAlive:!0},logging:{},expireTime:31536e3,staticPageGenerationTimeout:60,modularizeImports:{"@mui/icons-material":{transform:"@mui/icons-material/{{member}}"},lodash:{transform:"lodash/{{member}}"}},outputFileTracingRoot:"F:\\CODE\\Project\\tempmail\\moemail",experimental:{cacheLife:{default:{stale:300,revalidate:900,expire:0xfffffffe},seconds:{stale:0,revalidate:1,expire:60},minutes:{stale:300,revalidate:60,expire:3600},hours:{stale:300,revalidate:3600,expire:86400},days:{stale:300,revalidate:86400,expire:604800},weeks:{stale:300,revalidate:604800,expire:2592e3},max:{stale:300,revalidate:2592e3,expire:0xfffffffe}},cacheHandlers:{},cssChunking:!0,multiZoneDraftMode:!1,appNavFailHandling:!1,prerenderEarlyExit:!0,serverMinification:!0,serverSourceMaps:!1,linkNoTouchStart:!1,caseSensitiveRoutes:!1,clientSegmentCache:!1,preloadEntriesOnStart:!0,clientRouterFilter:!0,clientRouterFilterRedirects:!1,fetchCacheKeyPrefix:"",middlewarePrefetch:"flexible",optimisticClientCache:!0,manualClientBasePath:!1,cpus:11,memoryBasedWorkersCount:!1,imgOptConcurrency:null,imgOptTimeoutInSeconds:7,imgOptMaxInputPixels:0xfff8001,imgOptSequentialRead:null,isrFlushToDisk:!0,workerThreads:!1,optimizeCss:!1,nextScriptWorkers:!1,scrollRestoration:!1,externalDir:!1,disableOptimizedLoading:!1,gzipSize:!0,craCompat:!1,esmExternals:!0,fullySpecified:!1,swcTraceProfiling:!1,forceSwcTransforms:!1,largePageDataBytes:128e3,turbo:{root:"F:\\CODE\\Project\\tempmail\\moemail"},typedRoutes:!1,typedEnv:!1,parallelServerCompiles:!1,parallelServerBuildTraces:!1,ppr:!1,authInterrupts:!1,reactOwnerStack:!1,webpackMemoryOptimizations:!1,optimizeServerReact:!0,useEarlyImport:!1,staleTimes:{dynamic:0,static:300},serverComponentsHmrCache:!0,staticGenerationMaxConcurrency:8,staticGenerationMinPagesPerWorker:25,dynamicIO:!1,inlineCss:!1,optimizePackageImports:["lucide-react","date-fns","lodash-es","ramda","antd","react-bootstrap","ahooks","@ant-design/icons","@headlessui/react","@headlessui-float/react","@heroicons/react/20/solid","@heroicons/react/24/solid","@heroicons/react/24/outline","@visx/visx","@tremor/react","rxjs","@mui/material","@mui/icons-material","recharts","react-use","effect","@effect/schema","@effect/platform","@effect/platform-node","@effect/platform-browser","@effect/platform-bun","@effect/sql","@effect/sql-mssql","@effect/sql-mysql2","@effect/sql-pg","@effect/sql-squlite-node","@effect/sql-squlite-bun","@effect/sql-squlite-wasm","@effect/sql-squlite-react-native","@effect/rpc","@effect/rpc-http","@effect/typeclass","@effect/experimental","@effect/opentelemetry","@material-ui/core","@material-ui/icons","@tabler/icons-react","mui-core","react-icons/ai","react-icons/bi","react-icons/bs","react-icons/cg","react-icons/ci","react-icons/di","react-icons/fa","react-icons/fa6","react-icons/fc","react-icons/fi","react-icons/gi","react-icons/go","react-icons/gr","react-icons/hi","react-icons/hi2","react-icons/im","react-icons/io","react-icons/io5","react-icons/lia","react-icons/lib","react-icons/lu","react-icons/md","react-icons/pi","react-icons/ri","react-icons/rx","react-icons/si","react-icons/sl","react-icons/tb","react-icons/tfi","react-icons/ti","react-icons/vsc","react-icons/wi"]},bundlePagesRouterDependencies:!1,configFile:"F:\\CODE\\Project\\tempmail\\moemail\\next.config.ts",configFileName:"next.config.ts"}})},96487:()=>{},78335:()=>{},11950:(e,t,i)=>{"use strict";i.d(t,{EmailCredentialManager:()=>u});var r=i(85885),a=i(43144),s=i(13091),n=i(66369),l=i(12012),o=i(53719),c=i(91585);class d{static getSecret(){let e=process.env.JWT_SECRET||process.env.AUTH_SECRET;if(!e)throw Error("JWT_SECRET or AUTH_SECRET environment variable is required");return new TextEncoder().encode(e)}static async signEmailCredential(e){let t=Math.floor(Date.now()/1e3);return await new o.P({...e,iat:t}).setProtectedHeader({alg:"HS256"}).setIssuedAt(t).sign(this.getSecret())}static async signUser(e){let t=Math.floor(Date.now()/1e3),i=t+7776e3;return await new o.P({...e,exp:i,iat:t}).setProtectedHeader({alg:"HS256"}).setIssuedAt(t).setExpirationTime(i).sign(this.getSecret())}static async verifyEmailCredential(e){try{let{payload:t}=await (0,c.V)(e,this.getSecret());return t}catch(e){return console.error("JWT verification failed:",e),null}}static async verifyUser(e){try{let{payload:t}=await (0,c.V)(e,this.getSecret()),i=Math.floor(Date.now()/1e3);if(t.exp&&t.exp<i)return null;return t}catch(e){return console.error("JWT verification failed:",e),null}}static generateVerificationCode(){return Math.floor(1e5+9e5*Math.random()).toString()}}class u{static async createCredentialForEmail(e,t,i){let n=(0,r.d)(),l=await n.query.emailCredentials.findFirst({where:(0,s.eq)(a.emailCredentials.emailAddress,e)});if(l)return l;let o=await n.query.emails.findFirst({where:(0,s.Uo)((0,s.eq)(a.emails.address,e),(0,s.eq)(a.emails.userId,t))});if(!o)throw Error("邮箱地址不存在或不属于当前用户");let c=crypto.randomUUID(),u=await d.signEmailCredential({emailAddress:e,emailId:o.id,credentialId:c}),[m]=await n.insert(a.emailCredentials).values({id:c,userId:t,emailAddress:e,name:i||`${e} 的凭证`,jwt:u,enabled:!0,createdAt:new Date}).returning();return m}static async getAllCredentials(){let e=(0,r.d)(),t=await e.query.emailCredentials.findMany({orderBy:(0,n.i)(a.emailCredentials.createdAt)});return await Promise.all(t.map(async e=>{let t=await this.getBindingCount(e.emailAddress);return{...e,bindingCount:t}}))}static async getCredentialByEmail(e){let t=(0,r.d)(),i=await t.query.emailCredentials.findFirst({where:(0,s.eq)(a.emailCredentials.emailAddress,e)});if(!i)return null;let n=await this.getBindingCount(e);return{...i,bindingCount:n}}static async getBindingCount(e){let t=(0,r.d)(),i=await t.select({count:(0,l.U9)()}).from(a.userEmailBindings).leftJoin(a.users,(0,s.eq)(a.userEmailBindings.userId,a.users.id)).where((0,s.Uo)((0,s.eq)(a.userEmailBindings.emailAddress,e),(0,s.eq)(a.userEmailBindings.enabled,!0),(0,s.ne)(a.users.role,"admin")));return i[0]?.count||0}static async verifyCredential(e){let t=await d.verifyEmailCredential(e);if(!t)return null;let i=(0,r.d)(),n=await i.query.emailCredentials.findFirst({where:(0,s.Uo)((0,s.eq)(a.emailCredentials.id,t.credentialId),(0,s.eq)(a.emailCredentials.enabled,!0))});return n?(await i.update(a.emailCredentials).set({lastUsedAt:new Date}).where((0,s.eq)(a.emailCredentials.id,n.id)),n):null}static async bindEmailByCredential(e,t){let i=(0,r.d)(),n=await this.verifyCredential(t);if(!n)throw Error("无效的邮箱凭证");let l=await i.query.emails.findFirst({where:(0,s.eq)(a.emails.address,n.emailAddress)});if(!l)throw Error("邮箱记录不存在");if(await i.query.userEmailBindings.findFirst({where:(0,s.Uo)((0,s.eq)(a.userEmailBindings.userId,e),(0,s.eq)(a.userEmailBindings.emailId,l.id))}))throw Error("您已绑定此邮箱");let[o]=await i.insert(a.userEmailBindings).values({id:crypto.randomUUID(),userId:e,emailId:l.id,credentialId:n.id,createdAt:new Date}).returning();return{...o,emailAddress:n.emailAddress,enabled:!0}}static async getUserBindings(e){let t=(0,r.d)();return(await t.query.userEmailBindings.findMany({where:(0,s.eq)(a.userEmailBindings.userId,e),orderBy:(0,n.i)(a.userEmailBindings.createdAt),with:{email:!0}})).map(e=>({...e,emailAddress:e.email?.address,enabled:!0}))}static async unbindEmail(e,t){let i=(0,r.d)(),n=await i.query.emails.findFirst({where:(0,s.eq)(a.emails.address,t)});return!!n&&(await i.delete(a.userEmailBindings).where((0,s.Uo)((0,s.eq)(a.userEmailBindings.userId,e),(0,s.eq)(a.userEmailBindings.emailId,n.id)))).changes>0}static async getCredentialById(e){let t=(0,r.d)();return await t.query.emailCredentials.findFirst({where:(0,s.eq)(a.emailCredentials.id,e)})}}}},e=>{var t=t=>e(e.s=t);e.O(0,[730,752,899,498,220,514,156],()=>t(42776));var i=e.O();(_ENTRIES="undefined"==typeof _ENTRIES?{}:_ENTRIES)["middleware_app/api/email-credentials/route"]=i}]);
//# sourceMappingURL=route.js.map