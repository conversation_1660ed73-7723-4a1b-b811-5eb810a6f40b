CREATE TABLE IF NOT EXISTS `email_credentials` (
	`id` text PRIMARY KEY NOT NULL,
	`user_id` text NOT NULL,
	`email_address` text NOT NULL,
	`name` text NOT NULL,
	`jwt` text NOT NULL,
	`created_at` integer,
	`expires_at` integer,
	`enabled` integer DEFAULT true NOT NULL,
	`last_used_at` integer,
	FOREIGN KEY (`user_id`) REFERENCES `user`(`id`) ON UPDATE no action ON DELETE cascade
);
--> statement-breakpoint
CREATE UNIQUE INDEX IF NOT EXISTS `email_address_unique` ON `email_credentials` (`email_address`);--> statement-breakpoint
CREATE UNIQUE INDEX IF NOT EXISTS `email_credential_name_user_id_unique` ON `email_credentials` (`name`,`user_id`);--> statement-breakpoint
CREATE TABLE IF NOT EXISTS `email_verification_codes` (
	`id` text PRIMARY KEY NOT NULL,
	`email` text NOT NULL,
	`code` text NOT NULL,
	`created_at` integer,
	`expires_at` integer NOT NULL,
	`used` integer DEFAULT false NOT NULL
);
--> statement-breakpoint
CREATE INDEX IF NOT EXISTS `email_verification_email_idx` ON `email_verification_codes` (`email`);--> statement-breakpoint
CREATE INDEX IF NOT EXISTS `email_verification_expires_at_idx` ON `email_verification_codes` (`expires_at`);--> statement-breakpoint
CREATE TABLE IF NOT EXISTS `user_email_bindings` (
	`id` text PRIMARY KEY NOT NULL,
	`user_id` text NOT NULL,
	`email_id` text NOT NULL,
	`credential_id` text,
	`created_at` integer,
	FOREIGN KEY (`user_id`) REFERENCES `user`(`id`) ON UPDATE no action ON DELETE cascade,
	FOREIGN KEY (`email_id`) REFERENCES `email`(`id`) ON UPDATE no action ON DELETE cascade,
	FOREIGN KEY (`credential_id`) REFERENCES `email_credentials`(`id`) ON UPDATE no action ON DELETE set null
);
--> statement-breakpoint
CREATE UNIQUE INDEX IF NOT EXISTS `user_email_binding_unique` ON `user_email_bindings` (`user_id`,`email_id`);