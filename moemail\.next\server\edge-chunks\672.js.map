{"version": 3, "file": "edge-chunks/672.js", "mappings": "8GAAA,IAOA,cACA,cACA,KACA,IACA,cACA,mCACA,EAwBA,oBACA,MAvBA,CAuBA,wBAtBA,gBACA,GAqBA,EArBA,YACA,SAEA,kBACA,iBACA,GAEA,wDAcA,EAdA,mBACA,KACA,CAAK,EACL,mBAA+B,UAAoB,CAYnD,OACA,mBAEA,WACA,KACA,UACA,aACA,cACA,eAGA,SACA,gBACA,EACA,aACA,kBACA,cAGA,oDACA,YACA,UAGA,IACA,wBACA,wBACA,kBACA,kBACA,WACA,WACA,UACA,UACA,YAEA,OACA,yBAEA,GACA,wBAEA,CACA,SACA,oDACA,CAEA,CAAS,CACT,EAIA,OAHA,KACA,UACA,IACA,WACA,sBACA,iBACA,aACA,WACA,WACA,IACA,UACA,qBAEA,aAEA,GACA,oBAEA,CAAS,IAET,IAEA,cACA,cACA,cACA,KAEA,CACA,EAQO,kBACP,YAAiC,uBACjC,IA7HA,EA6HA,qCACA,MA9HA,EA8HA,EA7HA,6BACA,KAGA,CADA,yBACA,2BA0HA,GAIA,8DACA,wBAJA,WAA6B,YAK7B,oCCxHA,CAAM,KAAQ,eAAiB,OAAS,EAAC,CAAC,OAAQ,CAAE,EAAG,iBAAmB,KAAK,CAAS,QAAC,CAAC,CAAC,oCCArF,MAAc,cAAgB,CAAC,aAAe,EAClD,CAAC,MAAQ,EAAE,EAAG,CAAgB,kBAAK,SAAU,EAC9C,oCCFK,MAAO,cAAgB,CAAC,MAAQ,EACpC,CAAC,MAAQ,EAAE,KAAO,MAAM,OAAQ,IAAM,GAAG,CAAK,KAAG,IAAK,CAAI,MAAK,GAAI,GAAK,KAAK,SAAU,EACvF,CAAC,MAAQ,EAAE,EAAG,CAA2D,6DAAK,SAAU,EACzF,oCCHK,MAAM,cAAgB,CAAC,KAAO,EAClC,CAAC,MAAQ,EAAE,EAAG,CAAkE,oEAAK,SAAU,EAC/F,CAAC,MAAQ,EAAE,EAAG,CAAiB,mBAAK,SAAU,EAC9C,CAAC,QAAU,EAAE,EAAI,OAAO,CAAI,SAAQ,CAAG,OAAO,GAAK,UAAU,EAC9D,oCCJK,MAAe,cAAgB,CAAC,cAAgB,EACpD,CAAC,MAAQ,EAAE,EAAG,CAA+B,iCAAK,SAAU,EAC7D,oCCFK,MAAO,cAAgB,CAAC,MAAQ,EACpC,CAAC,MAAQ,EAAE,EAAG,CAAY,cAAK,SAAU,EACzC,CAAC,MAAQ,EAAE,EAAG,CAAY,cAAK,SAAU,EAC1C,oCCHK,MAAY,cAAgB,CAAC,WAAa,EAC9C,CAAC,MAAQ,EAAE,EAAG,CAAsD,wDAAK,SAAU,EACnF,CAAC,MAAQ,EAAE,EAAG,CAAc,gBAAK,SAAU,EAC3C,CAAC,MAAQ,EAAE,EAAG,CAAuD,yDAAK,SAAU,EACpF,CAAC,MAAQ,EAAE,EAAG,CAAa,eAAK,SAAU,EAC3C,oCCLK,MAAS,cAAgB,CAAC,QAAU,EACxC,CAAC,MAAQ,EAAE,EAAG,CAAW,aAAK,SAAU,EACxC,CAAC,MAAQ,EAAE,EAAG,CAAyC,2CAAK,SAAU,EACtE,CAAC,MAAQ,EAAE,EAAG,CAAsC,wCAAK,SAAU,EACnE,CAAC,OAAQ,CAAE,GAAI,CAAM,OAAI,CAAM,OAAI,IAAM,IAAI,IAAM,KAAK,SAAU,EAClE,CAAC,OAAQ,CAAE,GAAI,CAAM,OAAI,CAAM,OAAI,IAAM,IAAI,IAAM,KAAK,QAAS,EAClE,oCCHD,IQFO,ECdP,ET+BO,aAQP,MAPA,8BACA,iCAA+C,IAAO,IAEtD,aADA,eACA,uDAEA,QACA,GACA,qBACA,EAEO,gBACP,SACA,0EACA,YACA,4DACA,8CAA2D,WAAc,IACzE,uEACA,kBAEA,QACA,CAoGO,cA2GP,qBAoEA,8DCvUO,8BACA,4BCYA,gBAOP,MANA,qBACA,KAEA,GACA,cAEA,CACA,CElBA,iCAAgE,iBAAqB,CAAG,WAAe,CACvG,cCHA,cACA,QACA,CCFO,MDuEA,YACP,OCxE0C,EDwE1C,IAA8B,MAC9B,IAtEA,EAEA,EACA,EAmEA,GArEA,aAAiC,KACjC,KACA,KACA,CACA,gBACA,KACA,uHAEA,SACA,cA4DA,IAzDA,CAAS,CACT,sBACA,aAEA,OADA,UACA,WACA,uBAAsD,aAAoB,CAC1E,CACA,CAAS,CACT,6BAEA,IADA,KACA,WACA,QACA,KACA,YACA,CACA,GACA,iBAAqC,YAAe,CACpD,kBAAsC,SAAgB,CAEtD,CAAS,CACT,yBACA,KACA,SACA,aACA,QACA,KACA,aACA,GACA,CACA,iBACA,QACA,KACA,YACA,EACA,aAAsC,kCACtC,IACA,GACA,iBACA,UACA,GACA,CAAiB,CACjB,mBAEA,OADA,cACA,CACA,CAAiB,CAEjB,CAAS,GAaT,OADA,UAAqB,EAAQ,CAAG,KAAH,CAAG,UAAyB,IACzD,CACA,IExEA,aAEA,EAIA,EAAmB,YAAgB,eACnC,IHOO,EDJA,EACP,ECIA,EGRA,EAAc,QAAY,OAC1B,EAAa,UAAc,EAC3B,kBACA,iBACA,oBACA,CAAK,gBACL,oLAAwa,EAAM,6JAE9a,GHDO,EGC4B,MJL5B,ECK6B,EGAb,OHAa,GACpC,EGDmC,KHCnC,sBAA6C,OAAO,EAAS,KAAkB,CAC/E,CAAK,CDcL,CApBA,EAAc,cAAQ,YAAe,OAErC,MCEoC,KDApC,WAEA,QACA,cACA,cACA,CAAa,CACb,mBACA,cACA,YACA,cACA,oBAEA,CAAa,CAEb,CAAK,CAAI,MAET,WChBA,EDiBA,SCbA,GAJsB,UAKtB,IALoC,EAKpC,SACA,MACA,iBACA,aACA,YACA,sBACA,UACoB,EAAS,QAEhB,EACb,sBACA,UACoB,EAAS,IAE7B,CAAa,CACb,CAH6B,EAI7B,QACA,CAAK,MACL,GGtBA,EAAyB,EAAS,EAAQ,EAAG,EAAZ,EAAY,GAC7C,OAAY,eAAmB,CAAC,UAAc,MAC9C,GAAoB,eAAmB,CAJvC,EAIuC,CAAY,QAAS,EAAS,oHAAkN,EACvR,EAAwB,cAAkB,CAAC,UAAc,SAAiB,EAAS,EAAQ,EAAG,EAAZ,EAAS,CAAwB,MAAmB,GAAO,eAAmB,CANhK,mBAM4K,EAAQ,EAAG,IAAH,UAAuB,QAAyC,KACpP,CAAC,EACD,gBACA,WACA,mBACA,QACA,EACA,cACA,UAAe,EACf,UAAe,CACf,EChCA,GD8BiC,CC9BjC,WD+BiC,CC/BjC,EACA,kBAAqC,EAAM,eAC3C,MACA,kFAEA,eACA,MACA,wCAEA,OAAW,eAAmB,GAAS,EAAQ,EAAG,IAAH,EAE/C,qBEaO,iBACP,QACA,OACA,OACA,gBACA,SACA,GA/BA,WACA,aACA,YACA,qCACA,mBACA,MDDA,GAIe,GCHC,CDGgB,CCChC,MAJwB,CACxB,GACA,0BAEA,CACA,GAqBA,OApBA,EAUA,CARA,EAFA,EAqBA,GAnBA,WAEA,qBAiBA,EAdA,sCAcA,IAXA,EAYA,EAVA,CADA,yDACA,cAWA,CAEA,GACA,CAAS,CACT,oBAEA,QACA,0CACA,OAEA,CAAS,CAET,ECpCO,aACP,MAAgB,IAChB,eADmC,CACnC,KACQ,WAAe,YAEvB,OADA,SACA,WACA,UACA,CACA,CAAS,QACT,CACA,ECdO,aACP,MAAmB,IAMnB,OALA,OADqC,EACrC,GAGA,OADA,EADA,oBAEA,IACA,CAEA,EEfO,GACP,OACA,MACA,QACA,KACA,EACA,cAA2B,8BAC3B,cACA,6CACA,8CACA,4CACA,gDACA,wBAEO,cAEP,GADA,aAA8B,YAC9B,2BACA,SAEA,WACA,uCACA,oBACA,OACA,UACA,SACA,WACA,6BACA,CACA,ECxBA,EAAY,IACL,UADmB,aAK1B,oBACA,uCAEA,OADA,YAA8B,aAC9B,edVO,qBcUwC,QAAK,oCAA6C,kDAA8D,KAAK,yBAAsC,qCAA8C,mCAAmC,iBAC3R,oCAAmE,GACnE,cACA,oCAAoD,mCAAsC,qCAA0C,oBAAoB,mBAAmB,kDAA8D,SACzO,4DAA0F,GAC1F,CACA,gBACA,eAAyB,mBAAmB,EAAkB,GAAK,aAAL,GAAK,2BAAuD,KAAK,mBAAmB,EAAkB,GAAK,aAAL,UAAK,2BAA8D,KAAK,mBAAmB,EAAkB,aAAe,EAAkB,CAAjC,EAAsC,aAAL,KAAK,WAAsC,KAAK,mBAAmB,EAAkB,aAAe,EAAkB,CAAjC,EAAsC,aAAL,YAAK,WAA6C,KAAK,6BAA0C,gBdZlgB,sBcYwiB,8BAAwB,KAAK,IAC5kB,EACA,aACA,sDACA,sBACA,EACO,aACH,WAAe,YAEnB,OADA,iDACA,WACA,WACA,MACA,iCAGA,0CAEA,CACA,CAAK,IACL,EAIO,cACP,uEACA,IAMA,MAAc,SAAa,YAAe,OAAO,EAAW,GAAY,MAAZ,OACjD,eAAmB,IAAU,mCAAgF,CACxH,EEpDA,KACA,8BACA,IACA,8BAA8C,YAC9C,eAEA,OADA,KACA,EACA,CAAa,CACJ,EAET,oCAEA,sCACA,CACA,SACA,IACA,CAEO,YAAsC,YAAiB,ECd9D,cACA,2BACA,SAEA,iCACA,MAEA,iBAEA,6BAXA,wBAWA,iBACA,EAGO,gBACP,sBACA,IACA,GAMA,GAJA,yDACA,WAEA,OACA,CACA,aACA,GADA,UAEA,QAEA,CACA,gBACM,oBACN,QACA,EAiBA,gBACA,cAtCgD,EAsChD,EAtCgD,aACA,EAqChD,EArCgD,YAsChD,EACA,gBACA,cAlBA,CADA,0CAKA,CAIA,CADA,yCAKA,EAgBO,sBACP,IATA,EASA,GATA,EASA,qCAHA,MAGA,GAHA,gBAIA,MAEA,WACA,gBACA,KACA,MACA,IACA,IACA,GACA,MACA,MAEA,oBACA,EADA,UACA,IACA,QACA,SACA,KACA,MAGA,mBAGA,sDACA,EAAM,KAEN,uBAEA,2BAUA,OARA,GACA,4BACA,KAEA,IACA,8BACA,OAEA,CACA,ECrGO,cACP,4FAEO,cAAoC,2BAC3C,cACA,mCACA,EAGA,IACA,KCbA,OZWO,EWGA,MCdQ,GDcR,GACP,MAA6B,CCfD,CDeC,MAAY,KACzC,EAAwB,QAAY,QACpC,EAAqB,QAAY,GACjC,EAAa,UAAc,SAC3B,EAAgB,UAAc,CAAC,EAAc,IAC7C,EAAoB,MADyB,EACb,IAC5B,WAAe,YACnB,WACA,CAAK,MACD,WAAe,YACnB,YACA,8DACA,MAA0B,CnBuLnB,YmBvLgC,CnBuLhC,GACP,mDAA2E,IAAO,KAClF,YACA,yCACA,WAGA,kDACA,EmB/LuC,8DAEvC,OADA,sBAA4C,yDAA6D,EACzG,WACA,iEACA,sBAAgD,4DAAgE,CAChH,CACA,CAEA,CAAK,uCACL,MAA4B,aAAiB,eAC7C,oEACA,gCAEA,IAIA,EAJA,OACA,YACA,kCACA,kCAEA,WACA,kCAEA,4CACA,SAEA,MAA2C,EAAuB,KAClE,MACA,SAUA,CAZkE,EAIlE,EACA,KAGA,kBACA,EAA2C,EAAuB,MAGlE,GACA,SAKA,GATkE,CAMlE,yCACA,cAEA,GACA,SAEA,mBACA,OAAe,EAAY,qBAC3B,CAAK,KACL,EAAwB,aAAiB,aAEzC,gCAIA,iBALA,EAKA,EALA,GAKA,EALA,GAMA,mCAxEA,EAwE2E,gBAN3E,EAM2E,kBAN3E,EAM2E,QAN3E,EAM2E,0BAxEtC,CAArC,EAwE2E,MAxEtC,CAwEsC,CAxEtC,MAwEsC,CAxEtC,YAwEsC,CAxEtC,GAwEsC,CAAiI,KAE5M,gBACA,cACA,mBAEA,MACA,CAEA,OACA,6BACA,OACA,gBACA,mBAA0C,kBAnB1C,EAmB0C,QAAqC,EAC/E,cApBA,EAoBA,+BApBA,EAsBA,YACA,kBAGA,EACA,CAAK,KACL,EAAuB,aAAiB,mBACxC,OAAsB,uDAsCtB,GAEA,IADA,WACA,UACA,0BACA,SACA,UAEA,eAEA,QACA,EAhDsB,IACtB,kBACA,sBACA,uCAA0F,aAAqB,CAC/G,CAAS,GACT,CAAK,KACL,EAA2B,aAAiB,aAC5C,eACA,gBACA,CAAK,KACL,EAAsB,aAAiB,aACvC,8CACA,CAAK,KACL,EAA0B,aAAiB,aAC3C,8CACA,CAAK,KACD,WAAe,YAUnB,OATA,UACA,gBACA,kBACA,iBACA,oBACA,CAAS,EACT,oCAA0D,GAC1D,OADoE,EACpE,+BAA8D,GAC9D,OADwE,EACxE,gCAAkE,GAClE,OAD4E,IAE5E,uBAA2D,aAAwB,EACnF,uCAAiE,GACjE,OAD2E,EAC3E,kCAAqE,GACrE,OAD+E,EAC/E,mCAAyE,EACzE,CACA,CAAK,KACL,CAHmF,GAGnF,8BACA,OAAY,eAAmB,CAAC,UAAc,MAC9C,EAAgB,eAAmB,IAAU,OAjIT,mCAiIS,EAjIT,GAAiD,sBAAsB,mCAiI9D,EAjI8D,GAA0C,qBAAqB,IAiI7H,CAA2B,OACxE,EAA0B,eAAmB,CAAC,EAAe,CAAI,YAAJ,CAAI,6BAAsD,OACvH,EC9I6B,EZY7B,OYZsC,EAAE,CZYxC,GACA,GaZA,MAAwB,MDDmC,CAAC,EAAC,GCCrB,eAAyB,OAAQ,eAAmB,CAAC,EAAc,EAAQ,EAAG,IAAH,EAAV,EAAwB,UAAmB,CAAO,CAAE,GAAM,EACnK,aAA+B,EAAY,WAC3C,MAAe,iBAAiB,EAAC,gHWU7B,wBVfJ,yCAAuE,MAAkC,EAAI,EAC7G,mBAEA,GADA,OACA,4BACA,aAEA,CACA,CCNA,gBACA,wBACA,kBACI,GACJ,aAEA,CACA,iBACA,WACA,SACA,YACA,aAIA,OAHA,yBACA,OAEA,CACA,CAAK,EACL,KACA,WACA,YAAwB,WAAqB,KAC7C,UACA,sBACA,IAEA,YAEA,CACA,CAEA,CACA,CACA,iBACA,OAAS,aAAiB,WAC1B,gBEjCA,uBAA8C,iBAAqB,MACnE,ECAA,EAAiB,CAAK,4CACtB,IACA,cACA,SAAsB,UAAc,MAIpC,OAHE,EAAe,KACjB,SADiB,EACjB,YACA,CAAG,MACH,eAA2C,EAAG,KAC9C,CCRA,MAAyB,CAAK,4CAA8C,KAoE5E,WApE2F,0BCE3F,cACA,eAwBA,GACA,MAAoB,YAAgB,SACpC,IAAY,iBAAyB,EACrC,GAAQ,gBAAoB,SAoD5B,EACA,EApDA,MAoDA,CADA,wDACA,uCAEA,MAEA,mCAxDA,EAwDA,cACA,uCAEA,YA3DA,EA6DA,WA7DA,EA6DA,IA5DA,WAyBA,KACA,OAA0B,MAC1B,gBACA,WACA,OACA,mBAEA,KACA,cACA,cAEA,OADA,QACA,CACA,EACQ,GACR,SAEM,YACN,MAAkC,WAC5B,iBACN,sCAEA,CACA,OAAW,UACX,EAhDA,WAIA,OAHA,SAA4B,UAAc,EAC1C,SAAoC,EAAW,QAElC,CAFkC,CAElC,YAAkB,KAC/B,CACA,OAAW,UAAc,YAAuB,UAAc,gBAC9D,CAAG,EAEH,OADA,iBAA6B,EAAU,YACvC,CACA,EAvCA,GACA,EAAgB,YAAgB,SAChC,aAAY,QAAyB,EACrC,EAA0B,UAAc,YACxC,YACA,MACA,uBACA,WACA,MAIA,EAHA,EAAc,QAAc,YAA+B,UAAc,YACxD,gBAAoB,2BAKrC,MAA6B,SAAG,IAAc,oBAA2C,gBAAoB,IAAe,cAAkB,kBAA0C,CACxL,CACA,MAA2B,SAAG,IAAc,sBAA2C,CACvF,CAAG,EAEH,OADA,iBAAyB,EAAU,OACnC,CACA,CAmBA,gCAWA,cACA,OAAS,gBAAoB,0EAC7B,CCnCA,MAnBA,CACA,IACA,SACA,MACA,OACA,KACA,KACA,MACA,QACA,QACA,KACA,MACA,KACA,IACA,SACA,OACA,MACA,KACA,CACA,eACA,MAAe,EAAU,aAAc,EAAK,GAC5C,EAAe,YAAgB,SAC/B,YAAY,QAA6B,EACzC,QAIA,MAHA,4BACA,oCAE2B,SAAG,IAAS,WAAsC,CAC7E,CAAG,EAEH,OADA,2BAAkC,EAAK,EACvC,CAAW,WACX,CAAC,GAAI,EClCL,cACA,MAAsB,QAAY,IAIlC,OAHE,WAAe,MACjB,WACA,CAAG,EACM,SAAa,kCACtB,CEIA,IAAM,EAAiB,0BAMjB,EAAgC,gBAAc,CAClD,OAAQ,IAAI,IAA6B,uCACD,IAAI,IAA6B,SAC/D,IAAI,GAChB,CADmD,EAuC7C,EAAyB,aAC7B,CAAC,EAAO,KACN,GAAM,6BACJ,GAA8B,kBAC9B,uBACA,iBACA,oBACA,YACA,EACA,GAAG,EACL,CAAI,EACE,EAAgB,aAAW,GAC3B,CAAC,EAAM,EAAO,CAAU,IAAV,MAAU,CAAyC,GADf,CACmB,EACrE,EAAgB,GAAM,eAAiB,YAAY,SACnD,CAAC,CAAE,EAAK,CAAU,EAAV,QAAU,CAAS,CAAC,CAAC,EAC7B,EAAe,EAAgB,EAAeA,GAAS,EAAQA,IAC/D,CADmE,CAAC,MACrD,KAAK,EAAQ,MAAM,EAClC,CAAC,EAA4C,CAAI,CAAC,GAAG,EAAQ,mCAAhB,GAAsD,EAAE,MAAM,EAAE,EAC7G,EAAoD,EAAO,QAAQ,GACnE,EAAQ,EAAO,EAAO,QAAQ,GAAQ,CAAJ,EAClC,EAA8B,EAAQ,iBAF0E,qBAE1E,CAAuC,KAAO,EACpF,EAAyB,GAAS,EAElC,EAAqB,SAyItB,CACP,CACA,EAA0B,YAAY,UACtC,IACM,EAA2B,EAAe,GAC1C,EAAoC,OADK,CACL,EAAO,GAC3C,EADgD,EACzB,OAAO,KAAO,CAAD,EAiE1C,OA/DM,YAAU,KACd,IAAM,EAAoB,IACxB,GAAI,EAAM,QAAU,CAAC,EAA4B,QAAS,CAGxD,IAASC,EAAT,WAAoD,EA5N/B,2BA8NjB,WACA,EACA,EACA,CAAE,UAAU,CAAK,EAErB,EATM,EAAc,CAAE,cAAe,CAAM,EAuBjB,SAAS,CAA/B,EAAM,aACR,EAAc,oBAAoB,QAAS,EAAe,OAAO,EACjE,EAAe,QAAUA,EACzB,EAAc,iBAAiB,QAAS,EAAe,QAAS,CAAE,MAAM,CAAK,CAAC,GAE9EA,GAEJ,MAGE,CAHK,CAGS,oBAAoB,QAAS,EAAe,CALf,MAKsB,CAEnE,GAA4B,SAAU,CACxC,EAcM,EAAU,OAAO,WAAW,KAChC,EAAc,iBAAiB,cAAe,EAChD,EAAG,CAAC,EACJ,MAAO,IAF0D,CAG/D,OAAO,aAAa,GACpB,EAAc,EADa,iBACb,CAAoB,cAAe,GACjD,EAAc,YADoD,OACpD,CAAoB,QAAS,EAAe,OAAO,CACnE,CACF,EAAG,CAAC,EAAe,EAAyB,EAErC,CAEL,mBAJyC,EAInB,IAAO,EAA4B,QAAU,EACrE,CACF,EApNqD,IAC/C,IAAM,EAAS,EAAM,OACf,EAAwB,CAAC,GAAG,EAAQ,QAAQ,EAAE,KAAK,GAAY,EAAO,SAAS,KAChF,CADsF,CAAC,CAC7D,IAC/B,IAAuB,GACvB,EAD4B,EACR,GACf,EADoB,EAF6B,cAG3C,CAAkB,OAC/B,EAAG,GAEG,CAHqC,CAGtB,QAFL,CAmNX,CACP,CACA,EAA0B,YAAY,UACtC,IACM,EAAqB,EAAe,GACpC,EAAkC,OADC,CACD,CADgB,IAgBxD,CAfoD,MAE9C,YAAU,KACd,IAAM,EAAc,IACd,EAAM,QAAU,CAAC,EAA0B,SAAS,EAxSxC,2BA0Se,KAAe,EADxB,CAAE,cAAe,CAAM,EACqB,CAC9D,UAAU,CACZ,CAAC,CAEL,EAEA,OADA,EAAc,iBAAiB,UAAW,GACnC,IAAM,EAAc,EAD0B,iBAC1B,CAAoB,UAAW,EAC5D,EAAG,CAAC,EAAe,EAAmB,EADiC,CAIrE,aAHmC,EAGnB,IAAO,EAA0B,SAAU,EAC3D,cAAe,IAAO,EAA0B,SAAU,CAC5D,CACF,EAzOyC,IACnC,IAAM,EAAS,EAAM,OACG,CAAC,GAAG,EAAQ,QAAQ,EAAE,KAAK,GAAY,EAAO,SAAS,MAAM,CAAC,GAErE,GACjB,EADsB,EACF,GACf,EADoB,gBACd,CAAkB,OAC/B,EAAG,GAsDH,CAvD2C,MAG3C,GAFgB,MDhGpB,OCkGoB,SDlGpB,UACA,MAA0B,EAAc,GACtC,SADsC,EACvB,MACjB,UACA,kBACA,IAEA,EAEA,OADA,gCAA+D,WAAe,EAC9E,uCAA+E,WAAe,CAC9F,CAAG,OACH,ECuFqB,IACQ,IAAU,EAAQ,OAAO,KAAO,IAEvD,IAAkB,GACd,CAAC,CADkB,CACZ,kBAAoB,IAC7B,EAAM,KADkC,SAClC,CAAe,EACrB,KAEJ,EAAG,GAFW,EAIR,QAFU,CAEV,CAAU,KACd,GAAK,CAAD,CAUJ,GAVW,IACP,IAC0D,GAAG,CAA3D,EAAQ,mBADmB,mBACnB,CAAuC,OACjD,EAA4B,EAAc,KAAK,MAAM,cACrD,EAAc,KAAK,MAAM,cAAgB,QAE3C,EAAQ,uCAAuC,IAAI,IAAI,EAEjD,OAAO,IAAI,GACnB,CADuB,GAEhB,KAEH,GACwD,GAJ7C,CAIX,EAAQ,uCAAuC,MAE/C,GAAc,KAAK,MAAM,cAAgB,EAE7C,CACF,EAAG,CAAC,EAAM,EAAe,EAA6B,EAAQ,EAQxD,GARuD,QAQvD,CAAU,IACP,KACA,IACL,CADW,CACH,OAAO,OAAO,GACtB,CAD0B,CAClB,uCAAuC,OAAO,GACtD,CAD0D,GAE5D,EACC,CAAC,EAAM,EAAQ,EAEZ,GAFW,QAEX,CAAU,KACd,IAAM,EAAe,IAAM,EAAM,CAAC,CAAC,EAEnC,OADA,SAAS,iBAAiB,EAAgB,GACnC,IAAM,KADyC,IAChC,oBAAoB,EAAgB,EAC5D,EAAG,CAAC,CAAC,EAGH,IAJsE,CAItE,KAAC,EAAU,IAAV,CACE,EADO,CACJ,EACJ,IAAK,EACL,MAAO,CACL,cAAe,EACX,EACE,OACA,OACF,OACJ,GAAG,EAAM,OAEX,eAAgB,EAAqB,EAAM,eAAgB,CAAvB,CAAoC,cAAc,EACtF,cAAe,EAAqB,EAAM,cAAe,EAAtB,aAAgD,EACnF,qBAAsB,EACpB,EAAM,gBADkC,IAClC,CACN,EAAmB,qBACrB,EAGN,GA0JF,SAAS,IACP,IAAM,EAAQ,IAAI,GADM,SACM,GAC9B,SAAS,EADmC,WACnC,CAAc,EACzB,CAEA,EAH8B,OAGrB,EACP,EACA,EACA,EACA,UAAE,EAAS,EACX,IACM,EAAS,EAAO,cAAc,OAC9B,EAAQ,IAAI,YAAY,EAAM,CAAE,SAAS,EAAO,YAAY,SAAM,CAAO,CAAC,GAC5E,GAAS,EAAO,GAAP,aAAO,CAAiB,EAAM,EAA0B,CAAE,MAAM,CAAK,CAAC,EAE/E,GAC0B,GHjTlB,IGgTE,OHhTgB,qBGiTQ,IAEpC,CAFyC,CAElC,cAAc,EAEzB,CA3KA,EAAiB,YArKc,EAqKA,iBAgC/B,EArBqC,WAGnC,CAAC,EAAO,KACR,IAAM,EAAgB,aAAW,GAC3B,EAAY,SAAsC,IAAI,EACtD,EAAe,CAFmC,CAEnB,EAAc,GAYnD,OAAO,CAZ6B,CAE9B,UAAU,KACd,IAAM,EAAO,EAAI,QACjB,GAAI,EAEF,IAFQ,GACR,EAAQ,SAAS,IAAI,GACd,CADkB,IAEvB,EAAQ,SAAS,OAAO,EAC1B,CAEJ,CAHkC,CAG/B,CAAC,EAAQ,QAAQ,CAAC,EAEd,UAAC,EAAU,IAAV,CAAe,EAAN,CAAS,EAAO,IAAK,EAAc,CACtD,CAAC,EAEsB,YA1BH,EA0BiB,sCC5L/B,EAAe,aAAuC,CAAC,EAAO,KAClE,GAAM,CAAE,UAAW,EAAe,GAAG,EAAY,CAAI,EAC/C,CAAC,EAAS,EAAU,CAAU,CADa,CACb,KAAV,GAAU,CAAS,IAC7C,CADkD,CAClC,IAAM,GAAW,GAAO,CAAH,CAAK,EAA3B,IACT,EAAY,GAAkB,GAAW,YAAY,UAAU,KACrE,OAAO,EACH,cAAS,CAAa,UAAC,EAAU,IAAV,CAAe,EAAN,CAAS,EAAa,IAAK,EAAc,EAAI,GAC7E,IACN,CAAC,CAF2F,CAI5F,EAAO,YArBa,EAqBC,OCpBrB,IAAM,EAAoC,IACxC,GAAM,SAAE,WAAS,EAAS,CAAI,EACxB,EAmBR,SAAS,CAAY,EAAkB,ICnBrC,IDoBA,GAAM,CAAC,EAAM,EAAO,CAAU,IAAV,MAAU,CAAsB,EAC9C,EAAkB,SAAmC,IAAI,EACzD,EAAuB,SAAO,GAC9B,EAA6B,EADQ,MACR,CAAe,MAAM,EAElD,CAAC,EAAO,EAAI,GAAI,CADD,EAAU,UAAY,GACL,SCxBtC,EDwBoD,CAClD,MCxBF,EDwBW,CACP,QAAS,YACT,cAAe,kBACjB,EACA,iBAAkB,CAChB,MAAO,UACP,cAAe,WACjB,EACA,UAAW,CACT,MAAO,SACT,CACF,CAAC,CClCY,aAAW,CAAC,EAAwB,IAExC,CADY,CAAQ,EAAK,CAAU,EAAV,EACZ,CAD2B,CAE9C,IDsIH,OArGM,CCjCS,CDiCT,UAAU,KACd,IAAM,EAAuB,EAAiB,EAAU,OAAO,EAC/D,EAAqB,QAAU,cAAsB,EAAuB,MAC9E,EAAG,CAAC,EAAM,EAEV,CAFS,CAEO,KACd,IAAM,EAAS,EAAU,CADZ,MACY,CACnB,EAAa,EAAe,QAGlC,GAF0B,CAEtB,GAFqC,EAElB,CACrB,IAAM,EAAoB,EAAqB,QACzC,EAAuB,EAAiB,GAE1C,EACF,CAHkD,CAG7C,KADM,EACC,EACsB,SAAzB,GAAmC,GAAQ,UAAY,OAGhE,CAHwE,CAGnE,SAAS,EAUV,GAFgB,IAAsB,EAGxC,EAAK,GADW,YACI,CADS,CAG7B,EAAK,SAAS,EAIlB,EAAe,QAAU,CAC3B,CACF,EAAG,CAAC,EAAS,EAAK,EAAD,EAED,KACd,GAAI,EAAM,IADG,CAEP,EACJ,IAAM,EAAc,EAAK,cAAc,aAAe,OAMhD,EAAsB,IAE1B,IAAM,EADuB,EAAiB,EAAU,OAAO,EACf,SAAS,EAAM,aAAa,EAC5E,GAAI,EAAM,SAAW,GAAQ,IAW3B,EAAK,cAX0C,CAW3B,EAChB,CAAC,EAAe,SAAS,CAC3B,IAAM,EAAkB,EAAK,MAAM,kBACnC,EAAK,MAAM,kBAAoB,WAK/B,EAAY,EAAY,WAAW,KACI,YAAY,CAA7C,EAAK,MAAM,oBACb,EAAK,MAAM,kBAAoB,EAEnC,CAAC,CACH,CAEJ,EACM,EAAuB,IACvB,EAAM,SAAW,IAEnB,EAFyB,OAEJ,CAAU,EAAiB,EAAU,QAAO,CAErE,EAIA,OAHA,EAAK,iBAAiB,iBAAkB,GACxC,EAAK,eADuD,CACvD,CAAiB,kBAAmB,GACzC,EAAK,aADsD,GACtD,CAAiB,eAAgB,GAC/B,KACL,EAAY,QAF0C,IAE1C,CAAa,GACzB,EAAK,IAD6B,eAC7B,CAAoB,iBAAkB,GAC3C,EAAK,eAD0D,IAC1D,CAAoB,kBAAmB,GAC5C,EAAK,aADyD,MACzD,CAAoB,eAAgB,EAC3C,CACF,CAGE,EAAK,IAHA,QAFwD,GAKzC,CAExB,EAAG,CAAC,EAAM,EAAK,EAER,CACL,UAAW,CAAC,UAAW,kBAAkB,EAAE,SAAS,GACpD,EADyD,EAC9C,cAAY,IACrB,EAAU,QAAUD,EAAO,iBAAiBA,GAAQ,EAAJ,GAChD,EAAQA,EACV,EAAG,CADW,CACT,CACP,CACF,EAnJ+B,GAEvB,EACgB,EAHc,UAGlC,OAAO,EACH,EAAS,CAAE,QAAS,EAAS,UAAW,EAClC,WAAS,KAAK,GAGpB,EAAM,EAAgB,CAHM,CAGG,IAwJvC,OAxJ6B,EAwJpB,CAAc,EAA2D,IAE5E,EA1JmD,OA0JnC,EAFA,sBAEA,CAAyB,EAAQ,MAAO,KAAK,GAAG,IAChE,EAAU,GAAU,mBAAoB,GAAU,EAAO,sBAC7D,EACU,EAAgB,KAK1B,EAAU,CADV,EAAS,OAAO,yBAAyB,EAAS,KAAK,GAAG,MACtC,mBAAoB,GAAU,EAAO,gBAEhD,EAAQ,MAAM,IAIhB,EAAQ,MAAM,KAAQ,EAAgB,GAC/C,EAzK0D,IAExD,CAF6D,CAAC,IACvB,YAApB,OAAO,GACL,EAAS,UAAkB,eAAa,EAAO,KAAE,CAAI,CAAC,EAAI,IACjF,EA4IA,SAAS,EAAiB,GAAoC,OACrD,GAAQ,eAAiB,MAClC,CA5IA,EAAS,YAAc,+CEHjB,EAAc,SAGd,CAAC,EAAqB,EAAiB,CZN7C,cYM6C,CZN7C,EACA,CYKmE,GZLnE,KAqBA,OACA,eACa,eAAmB,KAEhC,mBACA,gBACA,OAAa,SAAa,CAC1B,MAAiB,WAAW,EAAU,IAAM,aAAmC,CAC/E,MAEA,CACA,EAEA,OADA,cACA,CAjCA,cACA,MAAwB,eAAmB,IAC3C,WACA,WACA,UACA,UAAc,mBAA8B,EAC5C,iBACA,EAAoB,SAAa,yBACjC,MAA6B,SAAG,mBAAqB,aAAiB,CACtE,SACA,2BAQA,GAPA,cACA,qBACA,EAAsB,YAAgB,IACtC,cACA,sBACA,kBAA2B,EAAa,2BAA2B,EAAkB,IACrF,EACA,EAeA,SAEA,MACA,WACA,yBACA,WACA,iBACA,aACA,sBACA,CAAK,EACL,mBACA,mBAA2D,uBAAqB,IAEhF,MADA,IACA,WAAkD,EAAU,GAC5D,OAAiB,UACjB,CAAO,GAAI,EACX,OAAa,SAAa,OAAU,WAAW,YAAoB,MAAgB,KACnF,CACA,EAEA,OADA,wBACA,CACA,EArBA,QACA,EY9BoE,GAc9D,CAAC,EAAgB,EAAgB,CAAI,EAdoC,GAwBzE,EAAgC,IACpC,EAX4F,CAWtF,eACJ,WACA,EACA,KAAM,cACN,EACA,qBACA,GAAQ,EACV,CAAI,EACE,EAAmB,SAA0B,IAAI,EACjD,EAAmB,SAA6B,IAAI,EACpD,CAAC,EAAM,EAAO,CAAI,IAAJ,KTxDtB,CACA,OACA,GSsD8C,STtD9C,EACA,gBACA,CAAG,CACH,SACC,EACD,WAmCA,UACA,cACA,WACC,EACD,SAA4B,UAAc,IAC1C,EAAuB,QAAY,IACnC,EAAsB,QAAY,IAUlC,OATA,OACA,WACA,CAAG,MACD,WAAe,MACjB,gBACA,eACA,YAEA,CAAG,QACH,OACA,EApDA,CACA,cACA,UACA,CAAG,EACH,aACA,OACU,EACV,MAA4B,QAAY,aACpC,WAAe,MACnB,gBACA,UAEA,oCACA,aACA,GAAa,GAAQ,mBAAmB,EAHxC,4BAGwC,CAAM,KAAK,EAAG,4KAEtD,CACA,WACA,CAAK,OACL,CAcA,SAbmB,aAAiB,CACpC,IACA,MACA,kBA+BA,OA/BA,SACA,OACA,cAEA,EAAQ,IACR,IAEA,CAAK,CACL,WAGA,ESe+C,CAC3C,KAAM,EACN,YAAa,IAAe,EAC5B,SAAU,EACV,OAAQ,CACV,CAAC,EAED,MACE,UAAC,GACC,MAAO,aACP,aACA,EACA,UAAW,IACX,CADgB,CAAC,MACR,IACT,CADc,CAAC,YACA,KAAK,CAAC,GACrB,EACA,aAAc,EACd,aAAoB,cAAY,IAAM,EAAQ,GAAc,CAAC,GAAW,CAAC,EAAQ,EAAZ,GAAW,GAChF,WAEC,GAGP,EAEA,EAAO,YAAc,EAMrB,IAAM,EAAe,gBAMf,EAAsB,aAC1B,CAAC,EAAwC,KACvC,GAAM,CAAE,gBAAe,GAAG,EAAa,CAAI,EACrC,EAAU,EAAiB,EAAc,EADR,CAEjC,EAAqB,EAAgB,EAAc,EAAQ,EADL,OAClB,CAAiC,EAC3E,MACE,UAAC,EAAU,OAAD,CACR,KAAK,SACL,gBAAc,SACd,gBAAe,EAAQ,KACvB,gBAAe,EAAQ,UACvB,aAAY,GAAS,EAAQ,IAAI,EAChC,GAAG,EACJ,IAAK,EACL,QAAS,EAAqB,EAAM,QAAS,EAAQ,MAAxB,MAAoC,GAGvE,GAGF,EAAc,YAAc,EAM5B,IAAM,EAAc,cAAH,CAGX,CAAC,EAAgB,EAAgB,CAAI,EAAwC,EAAa,CAC9F,QADqC,GACzB,EADgF,GAChF,CACd,CAAC,EAgBK,EAA4C,IAChD,GAAM,eAAE,aAAe,WAAY,YAAU,EAAU,CAAI,EACrD,EAAU,EAAiB,EAAa,GAC9C,MACE,IAFyD,CAAf,GAE1C,EAAC,GAAe,MAAO,aAAe,EACnC,SAAM,WAAS,IAAI,EAAU,GAC5B,UAAC,GAAS,KAAD,GAAU,GAAc,EAAQ,KACvC,mBAAC,GAAgB,GAAD,MAAQ,YAAC,EACtB,WACH,EACF,CACD,EACH,CAEJ,EAEA,EAAa,YAAc,EAM3B,IAAM,EAAe,QANiB,QAiBhC,EAAsB,aAC1B,CAAC,EAAwC,KACvC,IAAM,EAAgB,EAAiB,EAAc,EAAM,aAAa,EAClE,YAAE,EAAa,EAAc,WAAY,GAAG,EAAa,CAAI,EAC7D,EAAU,EAAiB,EAAc,EADgB,aACG,EAClE,OAAO,EAAQ,MACb,UAAC,GAAS,KAAD,GAAU,GAAc,EAAQ,KACvC,mBAAC,GAAmB,GAAG,EAAc,IAAK,EAAc,EAC1D,EACE,IACN,GAGF,EAAc,YAAc,EAM5B,IAAM,EAAO,EAAW,KAAd,GAAa,oBAA6B,EAE9C,EAA0B,aAC9B,CAAC,EAA4C,KAC3C,GAAM,CAAE,gBAAe,GAAG,EAAa,CAAI,EACrC,EAAU,EAAiB,EAAc,EADR,CAEvC,MAGE,IAJ0D,CAI1D,KAAC,GAAY,CAAZ,CAAa,GAAI,EAAM,OAAF,SAAgB,EAAC,OAAQ,CAAC,EAAQ,UAAU,EAChE,mBAAC,EAAU,IAAV,CACC,EADQ,WACI,GAAS,EAAQ,IAAI,EAChC,GAAG,EACJ,IAAK,EAEL,MAAO,CAAE,cAAe,OAAQ,GAAG,EAAa,MAAM,EACxD,CACF,CAEJ,GAOI,EAAe,gBAWf,EAAsB,aAC1B,CAAC,EAAwC,KACvC,IAAM,EAAgB,EAAiB,EAAc,EAAM,aAAa,EAClE,YAAE,EAAa,EAAc,WAAY,GAAG,EAAa,CAAI,EAC7D,EAAU,EAAiB,EAAc,EADgB,aACG,EAClE,MACE,UAAC,GAAS,KAAD,GAAU,GAAc,EAAQ,KACtC,WAAQ,MACP,UAAC,GAAoB,GAAG,EAAc,IAAK,EAAc,EAEzD,UAAC,GAAuB,GAAG,EAAc,IAAK,EAAc,EAEhE,CAEJ,GAGF,EAAc,YAAc,EAQ5B,IAAM,EAA2B,aAC/B,CAAC,EAA4C,KAC3C,IAAM,EAAU,EAAiB,EAAc,EAAM,aAAa,EAC5D,EAAmB,SAAuB,IAAI,EAC9C,EAAe,EAAgB,EAAc,EAAQ,SAAvB,CAAuB,CAAY,GAQvE,OARiF,EAG3E,UAAU,KACd,IAAM,EAAU,EAAW,QAC3B,GAAI,EAAS,OAAO,OAAU,CAAC,EACjC,EAAG,CAAC,CAAC,CADmC,CAItC,UAAC,IACE,GAAG,EACJ,IAAK,EAGL,UAAW,EAAQ,KACnB,6BAA2B,EAC3B,iBAAkB,EAAqB,EAAM,gBAAP,CAAyB,IAC7D,EAAM,eAAe,EACrB,EAAQ,WAAW,SAAS,MAAM,CACpC,CAAC,EACD,qBAAsB,EAAqB,EAAM,gBAAP,IAAO,CAAsB,IACrE,IAAM,EAAgB,EAAM,OAAO,cAC7B,EAAyC,MAAX,SAA0C,IAA1B,EAAc,SAC7C,MAAc,QAAgB,IAIjC,EAAM,eAAe,CACzC,CAAC,EAGD,eAAgB,EAAqB,EAAM,eAAgB,CAAvB,EAClC,EAAM,eAAe,EACvB,EAGN,GAKI,EAA8B,aAClC,CAAC,EAA4C,KAC3C,IAAM,EAAU,EAAiB,EAAc,EAAM,aAAa,EAC5D,EAAgC,UAAO,GACvC,EAAiC,UAAO,GAE9C,EAFmD,IAGjD,UAAC,IACE,GAAG,EACJ,IAAK,EACL,WAAW,EACX,6BAA6B,EAC7B,iBAAkB,IAChB,EAAM,mBAAmB,GAEpB,EAFyB,gBAEnB,EAAkB,CACtB,EAAwB,QAAS,GAAQ,WAAW,SAAS,MAAM,EAExE,EAAM,eAAe,GAGvB,EAAwB,SAAU,EAClC,EAAyB,SAAU,CACrC,EACA,kBAAmB,IACjB,EAAM,oBAAoB,GAErB,EAF0B,gBAEpB,EAAkB,CAC3B,EAAwB,SAAU,EACM,eAAe,CAAnD,EAAM,OAAO,cAAc,OAC7B,EAAyB,QAAU,KAOvC,IAAM,EAAS,EAAM,OACG,EAAQ,WAAW,SAAS,SAAS,IACxC,EAD8C,cACxC,CAAe,EAMF,YAApC,EAAM,OAAO,cAAc,MAAsB,EAAyB,SAAS,EAC/E,eAAe,CAEzB,GAGN,GA6BI,GAA0B,aAC9B,CAAC,EAA4C,KAC3C,GAAM,eAAE,YAAe,kBAAW,mBAAiB,EAAkB,GAAG,EAAa,CAAI,EACnF,EAAU,EAAiB,EAAc,EADsC,CAE/E,EAAmB,QADmC,CACZ,IAAI,EAC9C,EAAe,EAAgB,EAAc,GAMnD,MAFA,EAJoC,CAIpC,KAAc,CAAC,EAGb,uBACE,oBAAC,GAAU,CAAV,CACC,SAAO,EACP,MAAI,EACJ,QAAS,EACT,iBAAkB,EAClB,mBAAoB,EAEpB,mBAAC,GACC,KAAK,QADU,CAEf,GAAI,EAAQ,UACZ,mBAAkB,EAAQ,cAC1B,kBAAiB,EAAQ,QACzB,aAAY,GAAS,EAAQ,IAAI,EAChC,GAAG,EACJ,IAAK,EACL,UAAW,IAAM,EAAQ,cAAa,EAAK,EAC7C,CAD6C,EAI7C,uBACE,oBAAC,IAAa,QAAS,EAAQ,QAAS,EACxC,UAAC,eAAmB,EAAwB,cAAe,EAAQ,cAAe,GACpF,GAEJ,CAEJ,GAOI,GAAa,cAMb,GAAoB,aACxB,CAAC,EAAsC,KACrC,GAAM,eAAE,EAAe,GAAG,EAAW,CAAI,EACnC,EAAU,EAAiB,EADI,CACQ,GAC7C,MAAO,IADmD,CACnD,KAAC,EAAU,GAAV,CAAa,GAAI,EAAQ,QAAU,GAAG,EAAY,IAAK,EAAc,CAC/E,EAGF,IAAY,YAAc,GAM1B,IAAM,GAAmB,oBAMnB,GAA0B,aAC9B,CAAC,EAA4C,KAC3C,GAAM,eAAE,EAAe,GAAG,EAAiB,CAAI,EACzC,EAAU,EAAiB,GAAkB,GACnD,EAF2C,IAEpC,IADyD,CACzD,KAAC,EAAU,EAAV,CAAY,GAAI,CAAP,CAAe,cAAgB,GAAG,EAAkB,IAAK,EAAc,CAC1F,GAGF,GAAkB,YAAc,GAMhC,IAAM,GAAa,cAKb,GAAoB,aACxB,CAAC,EAAsC,KACrC,GAAM,CAAE,gBAAe,GAAG,EAAW,CAAI,EACnC,EAAU,EAAiB,EADI,CACQ,GAC7C,MACE,IAFwD,CAExD,KAAC,EAAU,OAAD,CACR,KAAK,SACJ,GAAG,EACJ,IAAK,EACL,QAAS,EAAqB,EAAM,QAAS,IAAM,EAAQ,EAA9B,UAA8B,EAAa,GAAM,EAAD,CAAC,EAUtF,SAAS,GAAS,GAAe,OACxB,EAAO,OAAS,QACzB,CANA,GAAY,YAAc,GAQ1B,IAAM,GAAqB,qBAErB,CAAC,GAAiB,GAAiB,CAAI,SZhf7C,IYgfyC,CAAiB,IZ/e1D,EAAkB,eAAmB,IACrC,MACA,aAAY,QAAuB,EACnC,EAAkB,SAAa,yBAC/B,MAA2B,SAAG,mBAAqB,aAAiB,CACpE,SACA,2BAOA,GANA,YACA,MAAoB,YAAgB,IACpC,cACA,sBACA,kBAAyB,EAAa,2BAA2B,EAAkB,IACnF,EACA,EYke2D,GAAoB,CAC7E,YAAa,EACb,UAAW,GACX,SAAU,QACZ,CAAC,EAIK,GAA4C,CAAC,SAAE,EAAQ,IAC3D,IAAM,EAAsB,GAAkB,IAExC,EAAU,KAAK,EAAoB,KAFuB,MAEZ,mBAAmB,EAAoB,SAAS;;0BAAA,EAE1E,EAAoB,SAAS;;0EAAA,EAEmB,EAAoB,QAAQ,GAStG,OAPM,YAAU,KACV,GAEE,CAAC,KAFM,IAEI,cADW,CAAe,IAC1B,GADiC,KACzB,MAAM,EAEjC,EAAG,CAAC,EAAS,EAAQ,EAEd,GAFa,CAGtB,EASM,GAAwD,CAAC,YAAE,gBAAY,EAAc,IACzF,IAAM,EAA4B,GARH,eAQqB,aAC9C,EAAU,SAD4D,kEAC5D,EAA6E,EAA0B,WAAW,KAWlI,OATM,YAAU,KACd,IAAM,EAAgB,EAAW,SAAS,aAAa,kBAAkB,EAErE,GAAiB,GAEf,CADmB,SAAS,EADE,IAEb,QADW,CAAe,IAC1B,QAAQ,CAD+B,GAC/B,CAAK,EAEtC,EAAG,CAAC,EAFyC,EAEpB,EAAc,EAEhC,IACT,EAEM,GALkC,EAMlC,GAAU,EACV,GAAS,EACT,GAAU,EACV,CAFM,CAFF,CAIM,EACV,GAAQ,GACR,GAAc,GACd,GAAQ,oDCziBV,EAAQ,EAWZ,SAAS,IACD,YAAU,CADQ,IAEtB,IAAM,EAAa,SAAS,iBAAiB,0BAA0B,EAKvE,OAJA,SAAS,KAAK,sBAAsB,aAAc,EAAW,CAAC,GAAK,KACnE,SAAS,GAD2E,CAAC,CACvE,sBAAsB,YAAa,EAAW,CAAC,GAAK,KAClE,IAEO,KACS,GAJmE,CAI7E,GACF,SAAS,iBAAiB,0BAA0B,EAAE,QAAQ,GAAU,EAAK,OAAO,CAAC,EAEvF,GACF,CACF,EAAG,CAAC,CAAC,CACP,CAEA,SAAS,IACP,IAAM,EAAU,SAAS,cAAc,MAAM,EAO7C,OANA,EAAQ,aAAa,yBAA0B,EAAE,EACjD,EAAQ,SAAW,EACnB,EAAQ,MAAM,QAAU,OACxB,EAAQ,MAAM,QAAU,IACxB,EAAQ,MAAM,SAAW,QACzB,EAAQ,MAAM,cAAgB,OACvB,CACT,mDCrCA,gBACA,wBACA,kBACI,GACJ,aAEA,CACA,iBACA,WACA,SACA,YACA,aAIA,OAHA,yBACA,OAEA,CACA,CAAK,EACL,KACA,WACA,YAAwB,WAAqB,KAC7C,WACA,qBACA,IAEA,YAEA,CACA,CAEA,CACA,yBCcA,4BAWA,cACA,OAAS,gBAAoB,0EAC7B,CCnCA,MAnBA,CACA,IACA,SACA,MACA,OACA,KACA,KACA,MACA,QACA,QACA,KACA,MACA,KACA,IACA,SACA,OACA,MACA,KACA,CACA,eACA,MDpBA,YACA,eAwBA,GACA,MAAoB,YAAgB,SACpC,aAAY,QAAyB,EACrC,GAAQ,gBAAoB,SAoD5B,EACA,EApDA,MAoDA,CADA,wDACA,uCApDA,EAsDA,IAEA,mCAxDA,EAwDA,cACA,uCAEA,YAEA,mBA5DA,WAyBA,KACA,OAA0B,MAC1B,gBACA,WACA,OACA,mBAEA,KACA,cACA,cAEA,OADA,QACA,CACA,EACQ,GACR,SAEM,YACN,MAAkC,WAC5B,iBACN,sCAEA,CACA,OAAW,UACX,EAhDA,WAIA,OAHA,SAA4B,UAAc,EAC1C,SAAoC,EAAW,QAElC,CAFkC,CAElC,YAAkB,KAC/B,CACA,OAAW,UAAc,YAAuB,UAAc,gBAC9D,CAAG,EAEH,OADA,iBAA6B,EAAU,YACvC,CACA,EAvCA,GACA,EAAgB,YAAgB,SAChC,aAAY,QAAyB,EACrC,EAA0B,UAAc,YACxC,YACA,MACA,uBACA,WACA,MAIA,EAHA,EAAc,QAAc,YAA+B,UAAc,YACxD,gBAAoB,2BAKrC,MAA6B,SAAG,IAAc,oBAA2C,gBAAoB,IAAe,cAAkB,kBAA0C,CACxL,CACA,MAA2B,SAAG,IAAc,sBAA2C,CACvF,CAAG,EAEH,OADA,iBAAyB,EAAU,OACnC,CACA,ECFyB,aAAc,EAAK,GAC5C,EAAe,YAAgB,SAC/B,YAAY,QAA6B,EACzC,QAIA,MAHA,4BACA,oCAE2B,SAAG,IAAS,WAAsC,CAC7E,CAAG,EAEH,OADA,2BAAkC,EAAK,EACvC,CAAW,WACX,CAAC,GAAI,EClCL,cACA,MAAsB,QAAY,IAIlC,OAHE,WAAe,MACjB,WACA,CAAG,EACM,SAAa,kCACtB,CCHA,IAAM,EAAqB,8BACrB,EAAuB,gCACvB,EAAgB,CAAE,SAAS,EAAO,YAAY,CAAK,EAwCnD,EAAmB,aAA+C,CAAC,EAAO,KAC9E,GAAM,MACJ,GAAO,UACP,GAAU,EACV,iBAAkB,EAClB,mBAAoB,EACpB,GAAG,EACL,CAAI,EACE,CAAC,EAAW,EAAY,CAAU,SAAV,CAAU,CAA6B,IAAI,EACnE,EAAmB,EAAe,GAClC,EAAqB,EAAe,GACpC,EAFiC,EAEH,KADK,CADmB,CAEG,IAAI,EAC7D,EAF0D,SJxBlE,MACA,OAAS,aAAiB,WAC1B,EIwBuC,EAAc,GAAU,EAAa,IAAI,CAAC,CAEtD,SAAO,CAC9B,OAAQ,GACR,QAAQ,IACN,CAAK,QAAS,CAChB,EACA,SAAS,IACP,CAAK,QAAS,CAChB,CACF,CAAC,EAAE,QAGG,YAAU,KACd,GAAI,EAAS,CACX,IAASE,EAAT,SAAuB,GAAmB,GACpC,EAAW,QAAU,CAAC,EAAW,OACrC,CADqC,GAC/B,EAAS,EAAM,OACjB,EAAU,SAAS,GACrB,EAAsB,CADK,GAAG,GACR,CAAU,EAEhC,EAAM,EAAsB,MAAvB,CAAuB,CAAS,CAAE,QAAQ,CAAK,CAAC,CAEzD,EAESC,EAAT,SAAwB,GAAmB,GACrC,EAAW,QAAU,CAAC,EAAW,OACrC,CADqC,GAC/B,EAAgB,EAAM,kBAYA,GAAxB,GAIC,EAAU,SAAS,IACtB,EAAM,EAAsB,KADO,CAC9B,CAAuB,CADU,CACC,OAAQ,EAAK,CAAC,CAEzD,EAaA,CARSC,QAQA,iBAAiB,UAAWF,GACrC,SAAS,EADyC,cACzC,CAAiB,WAAYC,GACtC,IAAM,EAAmB,IAAI,EADuB,eACNC,SAVrB,GAA6B,GAC7B,CASoC,QAT3B,gBACT,SAAS,KAAM,IACtC,IAAW,KAAY,EACjB,EAAS,MADmB,MACnB,CAAa,OAAS,EAAG,GAAM,EAEhD,GAOA,GAT+C,CAAU,GAOrD,GAAW,EAAiB,KAAjB,EAAiB,CAAQ,EAAW,CAAE,WAAW,EAAM,SAAS,CAAK,CAAC,EAE9E,KACL,SAAS,oBAAoB,UAAWF,GACxC,SAAS,EAD4C,iBAC5C,CAAoB,WAAYC,GACzC,EAAiB,UADsC,CAC3B,CAC9B,CACF,CACF,EAAG,CAAC,EAAS,EAAW,EAAW,MAAM,CAAC,EAEpC,YAAU,KACd,GAAI,EAAW,CACb,EAAiB,IAAI,GACrB,IAAM,EAA2B,CADF,QACW,cAG1C,GAAI,CAFwB,EAAU,SAAS,GAErB,CACxB,IAAM,EAAa,IAAI,UAH8C,EAGlC,EAAoB,GACvD,EAAU,QAD0D,QAC1D,CAAiB,EAAoB,GAC/C,EAAU,WADqD,EACrD,CAAc,GACnB,EAAW,KADkB,WAClB,EAAkB,CAChC,SA2ED,CAAW,CA3EC,QA2E4B,GAAS,EAAM,CAAI,CAAC,GAAG,IAChE,EAA2B,SAAS,cAC1C,QAAW,KAAa,EAEtB,GADA,EAAM,EAAW,EADiB,IAC7B,EAAc,CAAO,CAAC,EACvB,SAAS,gBAAkB,EAA0B,MAE7D,EAsHS,EAvM8C,GAuMxC,MAvMiD,CAuM1C,GAAU,GAAoB,KAAf,SAvM+B,CAAE,QAAQ,CAAK,CAAC,EACtE,SAAS,gBAAkB,GAC7B,EAAM,GAGZ,CAEA,IALW,CAAU,CAKd,KACL,EAAU,IAPiD,eAOjD,CAAoB,EAAoB,GAKlD,WAAW,EALuD,GAMhE,IAAM,EAAe,IAAI,YAAY,EAAsB,GAC3D,EAAU,QAD8D,QAC9D,CAAiB,EAAsB,GACjD,EAAU,aADyD,CAC3C,GACnB,EAAa,OADkB,SAClB,EAAkB,EAC5B,GAA4B,KAA7B,IAAsC,KAAM,CAAE,QAAQ,CAAK,CAAC,EAGnE,EAAU,oBAAoB,EAAsB,GAEpD,EAAiB,OAAO,EAC1B,EAAG,CAAC,CAHoE,CAK5E,CACF,EAJ0C,CAItC,EAAW,EAAkB,EAAoB,EAAW,EAGhE,IAAM,EAHyD,EAGnC,YAC1B,IACE,GAAI,CAAC,GAAQ,CAAC,GACV,EAAW,GADQ,GACR,CAAQ,OAEvB,IAAM,EAAyB,QAAd,EAAM,KAAiB,CAAC,EAAM,QAAU,CAAC,EAAM,SAAW,CAAC,EAAM,QAC5E,EAAiB,SAAS,cAEhC,GAAI,GAAY,EAAgB,CAC9B,IAAME,EAAY,EAAM,cAClB,CAAC,EAAO,EAAI,CAAI,CAAJ,QA8CjB,CAAiB,EAAwB,IAC1C,EAAa,EAAsB,GAGzC,MAHkD,CACpC,EAAY,EAAY,GACzB,EAAY,EAAW,EADW,KACX,CAAQ,EAAG,GAC5B,EAlD0BA,GACL,CAgDgB,EAhDP,EAMrC,EAP0C,QAOnC,EAAY,IAAmB,EAG/B,EAAM,EAH+B,MAG/B,EAAY,IAAmB,IAC9C,EAAM,CAD+C,aAC/C,CAAe,EACjB,GAAM,EAAM,EAAM,CAAE,KAAT,GAAiB,CAAK,CAAC,IAJtC,EAAM,eAAe,EACjB,GAAM,IAAa,CAAE,KAAV,GAAkB,CAAK,CAAC,GAJrC,IAAmBA,GAAW,EAAM,MAAN,QAAM,CAAe,CAU3D,CACF,EACA,CAAC,EAAM,EAAS,EAAW,MAAM,GAGnC,MACE,UAAC,EAAU,IAAV,CAAc,EAAL,OAAe,GAAK,GAAG,EAAY,IAAK,EAAc,UAAW,EAAe,CAE9F,CAAC,EAwCD,SAAS,EAAsB,GAAwB,IAC/C,EAAuB,CAAC,EACxB,EAAS,SAAS,iBAAiB,EAAW,WAAW,aAAc,CAC3E,WAAY,IACV,IAAM,EAAiC,UAAjB,EAAK,SAAqC,WAAd,EAAK,YACvD,EAAS,UAAY,EAAK,QAAU,EAAsB,WAAW,CAAlB,UAAkB,CAI9D,EAAK,UAAY,EAAI,WAAW,cAAgB,WAAW,YAEtE,CAAC,EACD,KAAO,EAAO,SAAS,GAAG,EAAM,KAAK,EAAO,WAA0B,EAGtE,OAAO,CACT,CAMA,SAAS,EAAY,EAAyB,GAAwB,IACpE,IAAW,KAAW,EAEpB,GAAI,CAAC,GAFyB,MAMzB,CAAS,CAAmB,MAAE,EAAK,EAA2B,GAC3B,SAAU,EAAhD,iBAAiB,GAAM,CAAF,SAAE,CAAyB,OAAO,EAC3D,KAAO,IAEQ,SAAT,GAAsB,IAAS,GAAM,CAF9B,CAGX,GAAuC,GADS,IACD,EAA3C,iBAAiB,GAAM,CAAF,MAAE,CAAoB,OAAO,EACtD,EAAO,EAAK,cAEd,OAAO,CACT,EAbkB,EAAS,CAAE,KAAM,CAAU,CAAC,EAAG,OAAO,CAExD,CAiBA,SAAS,EAAM,EAAkC,MAAnC,EAAqC,GAAS,EAAM,CAAI,CAAC,GAAG,GAEpE,GAAW,EAAQ,MAAO,OAC5B,IAAM,EAA2B,SAAS,cAE1C,EAAQ,MAAM,CAAE,eAAe,CAAK,CAAC,EAEjC,IAAY,GAVX,CADkB,EAWuC,OAAO,MAV7C,SAUoB,SAVA,WAAY,GAUkB,GACxE,EAAQ,OAAO,CACnB,CACF,CA5FA,EAAW,YAhMc,EAgMA,WAmGzB,IAAM,EAEN,SAAS,EAEP,IAAI,EAAyB,CAAC,EAE9B,GAN8C,GAMvC,CACL,IAAI,GAA2B,IAEvB,EAAmB,EAAM,CAAC,EAC5B,IAAe,GACjB,GAAkB,MAAM,EAI1B,CADA,EAAQ,CAJ6B,CAIjB,EAAO,EAAU,EAC/B,QAAQ,EAChB,EAEA,MAH0B,CAGnB,GAA2B,EACxB,EAAY,EAAO,GAC3B,EAAM,CAAC,GAAG,CAD2B,MACpB,CACnB,CACF,CACF,IAEA,SAAS,EAAe,EAAY,GAAS,IACrC,EAAe,CAAC,GAAG,EAAK,CACxB,EAAQ,EAAa,QAAQ,GAInC,CAJuC,MACzB,IAAI,CAAd,GACF,EAAa,OAAO,EAAO,CAAC,EAEvB,CACT,yECrUM,EAAc,aAAqC,CAAC,EAAO,IAE7D,UAAC,IAAS,CAAC,MAAV,CACE,GAAG,EACJ,IAAK,EACL,YAAa,IAEI,EAAM,OACV,QAAQ,iCAAiC,EAAG,EAEvD,EAAM,cAAc,GAEhB,CAAC,CAFoB,CAEd,kBAAoB,EAAM,OAAS,EAAG,GAAM,eAAe,EACxE,KAKN,EAAM,YAxBO,EAwBO,MAIpB,IAAM,EAAO,oIXnBT,mCYfJ,oBACA,gCACA,CCFA,yCAAuE,MAAkC,EAAI,EAC7G,mBAEA,GADA,OACA,4BACA,aAEA,CACA,gBCWA,mBACA,SAqBA,OACA,eACa,eAAmB,KAEhC,mBACA,gBACA,OAAa,SAAa,CAC1B,MAAiB,WAAW,EAAU,IAAM,aAAmC,CAC/E,MAEA,CACA,EAEA,OADA,cACA,CAjCA,cACA,MAAwB,eAAmB,IAC3C,WACA,WACA,UACA,UAAc,mBAA8B,EAC5C,iBACA,EAAoB,SAAa,yBACjC,MAA6B,SAAG,mBAAqB,aAAiB,CACtE,SACA,2BAQA,GAPA,cACA,qBACA,EAAsB,YAAgB,IACtC,cACA,sBACA,kBAA2B,EAAa,2BAA2B,EAAkB,IACrF,EACA,EAeA,SAEA,MACA,WACA,yBACA,WACA,iBACA,aACA,sBACA,CAAK,EACL,mBACA,4BAA2D,cAAqB,IAEhF,MADA,IACA,WAAkD,EAAU,GAC5D,OAAiB,UACjB,CAAO,GAAI,EACX,OAAa,SAAa,OAAU,WAAW,YAAoB,MAAgB,KACnF,CACA,EAEA,OADA,wBACA,CACA,EArBA,SCpDA,gBACA,wBACA,kBACI,GACJ,aAEA,CACA,iBACA,WACA,SACA,YACA,aAIA,OAHA,yBACA,OAEA,CACA,CAAK,EACL,KACA,WACA,YAAwB,WAAqB,KAC7C,WACA,qBACA,IAEA,YAEA,CACA,CAEA,CACA,CACA,iBACA,OAAS,aAAiB,WAC1B,CC9BA,cACA,MAwBA,YACA,MAAoB,YAAgB,SACpC,aAAY,QAAyB,EACrC,GAAQ,gBAAoB,SAoD5B,EACA,EApDA,MAoDA,CADA,kCAnDA,EAmDA,oBACA,uCApDA,EAsDA,IAEA,mCAxDA,EAwDA,cACA,uCAEA,YA3DA,EA6DA,WA7DA,EA6DA,IA5DA,WAyBA,KACA,OAA0B,MAC1B,gBACA,WACA,OACA,mBAEA,KACA,cACA,cAEA,OADA,QACA,CACA,EACQ,GACR,SAEM,YACN,MAAkC,WAC5B,iBACN,sCAEA,CACA,OAAW,UACX,EAhDA,WAIA,OAHA,SAA4B,UAAc,EAC1C,SAAoC,EAAW,QAElC,CAFkC,CAElC,YAAkB,KAC/B,CACA,OAAW,UAAc,YAAuB,UAAc,gBAC9D,CAAG,EAEH,OADA,iBAA6B,EAAU,YACvC,CACA,EAvCA,GACA,EAAgB,YAAgB,SAChC,aAAY,QAAyB,EACrC,EAA0B,UAAc,YACxC,YACA,MACA,uBACA,WACA,MAIA,EAHA,EAAc,QAAc,YAA+B,UAAc,YACxD,gBAAoB,2BAKrC,MAA6B,SAAG,IAAc,oBAA2C,gBAAoB,IAAe,cAAkB,kBAA0C,CACxL,CACA,MAA2B,SAAG,IAAc,sBAA2C,CACvF,CAAG,EAEH,OADA,iBAAyB,EAAU,OACnC,CACA,CAmBA,gCAWA,cACA,OAAS,gBAAoB,0EAC7B,CIxDA,MAAuB,eAAmB,SCqB1C,EAnBA,CACA,IACA,SACA,MACA,OACA,KACA,KACA,MACA,QACA,QACA,KACA,MACA,KACA,IACA,SACA,OACA,MACA,KACA,CACA,eACA,MAAe,EAAU,aAAc,EAAK,GAC5C,EAAe,YAAgB,SAC/B,YAAY,QAA6B,EACzC,QAIA,MAHA,4BACA,oCAE2B,SAAG,IAAS,WAAsC,CAC7E,CAAG,EAEH,OADA,2BAAkC,EAAK,EACvC,CAAW,WACX,CAAC,GAAI,EClCL,cACA,MAAsB,QAAY,IAIlC,OAHE,WAAe,MACjB,WACA,CAAG,EACM,SAAa,kCACtB,CtBIA,IAAM,EAAiB,0BAMjB,EAAgC,gBAAc,CAClD,OAAQ,IAAI,IAA6B,uCACD,IAAI,IAA6B,SAC/D,IAAI,GAChB,CADmD,EAuC7C,EAAyB,aAC7B,CAAC,EAAO,KACN,GAAM,6BACJ,GAA8B,kBAC9B,uBACA,EACA,mCACA,YACA,EACA,GAAG,EACL,CAAI,EACE,EAAgB,aAAW,GAC3B,CAAC,EAAM,EAAO,CAAU,IAAV,MAAU,CAAyC,GADf,CACmB,EACrE,EAAgB,GAAM,eAAiB,YAAY,SACnD,CAAC,CAAE,EAAK,CAAU,WAAS,CAAC,CAAC,EAC7B,EAAe,EAAgB,EAAc,GAAU,EAAQL,IAC/D,CADmE,CAArC,MACf,KAAK,EAAQ,MAAM,EAClC,CAAC,EAA4C,CAAI,CAAC,GAAG,EAAQ,mCAAhB,GAAsD,EAAE,MAAM,EAAE,EAC7G,EAAoD,EAAO,QAAQ,GACnE,EAAQ,EAAO,EAAO,QAAQ,GAAQ,CAAJ,EAClC,EAA8B,EAAQ,iBAF0E,qBAE1E,CAAuC,KAAO,EACpF,EAAyB,GAAS,EAElC,EAAqB,SAyItB,CACP,CACA,EAA0B,YAAY,UACtC,IACM,EAA2B,EAAe,GAC1C,EAAoC,OADK,CACL,CAAO,IAC3C,CADgD,CADc,EAEvC,OAAO,KAAO,CAAD,EAiE1C,OA/DM,YAAU,KACd,IAAM,EAAoB,IACxB,GAAI,EAAM,QAAU,CAAC,EAA4B,QAAS,CAGxD,IAASC,EAAT,WAAoD,EA5N/B,2BA8NjB,WACA,EACA,EACA,CAAE,UAAU,CAAK,EAErB,EATM,EAAc,CAAE,cAAe,CAAM,EAuBjB,SAAS,CAA/B,EAAM,aACR,EAAc,oBAAoB,QAAS,EAAe,OAAO,EACjE,EAAe,QAAUA,EACzB,EAAc,iBAAiB,QAAS,EAAe,QAAS,CAAE,MAAM,CAAK,CAAC,GAE9EA,GAEJ,MAGE,CAHK,CAGS,oBAAoB,QAAS,EAAe,CALf,MAKsB,CAEnE,GAA4B,QAAU,EACxC,EAcM,EAAU,OAAO,WAAW,KAChC,EAAc,iBAAiB,cAAe,EAChD,EAAG,CAAC,EACJ,MAAO,IAF0D,CAG/D,OAAO,aAAa,GACpB,EAAc,EADa,iBACb,CAAoB,cAAe,GACjD,EAAc,YADoD,OACpD,CAAoB,QAAS,EAAe,OAAO,CACnE,CACF,EAAG,CAAC,EAAe,EAAyB,EAErC,CAEL,mBAJyC,EAInB,IAAO,EAA4B,SAAU,CACrE,CACF,EApNqD,IAC/C,IAAM,EAAS,EAAM,OACf,EAAwB,CAAC,GAAG,EAAQ,QAAQ,EAAE,KAAK,GAAY,EAAO,SAAS,KAChF,CADsF,CAAC,CAC7D,IAC/B,IAAuB,GACvB,EAD4B,EACR,GACf,EAAM,EAH2C,cAG3C,CAAkB,OAC/B,EAAG,GAEG,CAHqC,CAGtB,QAFL,CAmNX,CACP,CACA,EAA0B,YAAY,UAEtC,IAAM,EAAqB,EAAe,GACpC,EAAkC,OADC,CACD,CADgB,CACT,GAe/C,EAfoD,KAE9C,YAAU,KACd,IAAM,EAAc,IACd,EAAM,QAAU,CAAC,EAA0B,SAAS,EAxSxC,2BA0Se,KAAe,EADxB,CAAE,cAAe,CAAM,EACqB,CAC9D,UAAU,CACZ,CAAC,CAEL,EAEA,OADA,EAAc,iBAAiB,UAAW,GACnC,IAAM,EAAc,EAD0B,iBAC1B,CAAoB,UAAW,EAC5D,EAAG,CAAC,EAAe,EAAmB,EADiC,CAIrE,aAHmC,EAGnB,IAAO,EAA0B,SAAU,EAC3D,cAAe,IAAO,EAA0B,SAAU,CAC5D,CACF,EAzOyC,IACnC,IAAM,EAAS,EAAM,OACG,CAAC,GAAG,EAAQ,QAAQ,EAAE,KAAK,GAAY,EAAO,SAAS,MAAM,CAAC,GAErE,GACjB,EADsB,EACF,GACf,EADoB,gBACd,CAAkB,OAC/B,EAAG,GAsDH,CAvD2C,MAG3C,GAFgB,MuBhGpB,OvBkGoB,SuBlGpB,UACA,MAA0B,EAAc,GACtC,SADsC,EACvB,MACjB,UACA,kBACA,IAEA,EAEA,OADA,gCAA+D,WAAe,EAC9E,uCAA+E,WAAe,CAC9F,CAAG,OACH,EvBuFqB,IACQ,IAAU,EAAQ,OAAO,KAAO,IAEvD,IAAkB,GACd,CAAC,CADkB,CACZ,kBAAoB,IAC7B,EAAM,KADkC,SAClC,CAAe,EACrB,KAEJ,EAAG,GAFW,EAIR,QAFU,CAEV,CAAU,KACd,GAAK,CAAD,CAUJ,GAVW,IACP,IAC0D,GAAG,CAA3D,EAAQ,mBADmB,mBACnB,CAAuC,OACjD,EAA4B,EAAc,KAAK,MAAM,cACrD,EAAc,KAAK,MAAM,cAAgB,QAE3C,EAAQ,uCAAuC,IAAI,IAAI,EAEjD,OAAO,IAAI,GACnB,CADuB,GAEhB,KAEH,GACwD,GACxD,CADA,EAAQ,uCAAuC,OAE/C,EAAc,KAAK,MAAM,cAAgB,EAE7C,CACF,EAAG,CAAC,EAAM,EAAe,EAA6B,EAAQ,EAQxD,GARuD,QAQvD,CAAU,IACP,KACA,IACL,CADW,CACH,OAAO,OAAO,GACtB,CAD0B,CAClB,uCAAuC,OAAO,GACtD,CAD0D,GAE5D,EACC,CAAC,EAAM,EAAQ,EAEZ,EAJa,CAEF,QAEX,CAAU,KACd,IAAM,EAAe,IAAM,EAAM,CAAC,CAAC,EAEnC,OADA,SAAS,iBAAiB,EAAgB,GACnC,IAAM,KADyC,IAChC,oBAAoB,EAAgB,EAC5D,EAAG,CAAC,CAAC,EAGH,IAJsE,CAItE,KAAC,EAAU,IAAV,CACE,EADO,CACJ,EACJ,IAAK,EACL,MAAO,CACL,cAAe,EACX,EACE,OACA,OACF,OACJ,GAAG,EAAM,OAEX,eAAgB,EAAqB,EAAM,eAAgB,CAAvB,CAAoC,cAAc,EACtF,cAAe,EAAqB,EAAM,cAAe,EAAa,aAAa,EACnF,qBAAsB,EACpB,EAAM,gBADkC,IAClC,CACN,EAAmB,qBACrB,EAGN,GA0JF,SAAS,IACP,IAAM,EAAQ,IAAI,GADM,SACM,GAC9B,SAAS,EADmC,WACnC,CAAc,EACzB,CAEA,EAH8B,OAGrB,EACP,EACA,EACA,EACA,CAAE,WAAS,EACX,IACM,EAAS,EAAO,cAAc,OAC9B,EAAQ,IAAI,YAAY,EAAM,CAAE,SAAS,EAAO,YAAY,SAAM,CAAO,CAAC,GAC5E,GAAS,EAAO,GAAP,aAAO,CAAiB,EAAM,EAA0B,CAAE,MAAM,CAAK,CAAC,EAE/E,GAC0B,GqBjTlB,IrBgTE,OqBhTgB,qBrBiTQ,IAEpC,CAFyC,CAElC,cAAc,EAEzB,CA3KA,EAAiB,YArKc,EAqKA,iBAgC/B,EArBqC,WAGnC,CAAC,EAAO,KACR,IAAM,EAAgB,aAAW,GAC3B,EAAY,SAAsC,IAAI,EACtD,EAAe,CAFmC,CAEnB,EAAc,GAYnD,OAAO,CAZ6B,CAE9B,UAAU,KACd,IAAM,EAAO,EAAI,QACjB,GAAI,EAEF,IAFQ,GACR,EAAQ,SAAS,IAAI,GACd,CADkB,IAEvB,EAAQ,SAAS,OAAO,EAC1B,CAEJ,CAHkC,CAG/B,CAAC,EAAQ,QAAQ,CAAC,EAEd,UAAC,EAAU,IAAV,CAAe,EAAN,CAAS,EAAO,IAAK,EAAc,CACtD,CAAC,EAEsB,YA1BH,EA0BiB,gDwB9MrC,uBAA8C,iBAAqB,MACnE,ECAA,EAAiB,CAAK,4CACtB,IACA,cACA,SAAsB,UAAc,MAIpC,OAHE,EAAe,KACjB,SADiB,EACjB,YACA,CAAG,MACH,eAA2C,EAAG,KAC9C,2BCNA,EAAY,YAAgB,SAC5B,aAAU,8BAAkD,EAC5D,MAAyB,SAAG,CACxB,EAAS,IACb,CACA,EAFa,CAEb,EACA,MACA,QACA,SACA,oBACA,2BACA,qBAA2D,SAAG,YAAc,wBAA0B,CACtG,EAEA,CAAC,EACD,cAhBA,QE8BA,IAAM,EAAc,SAGd,CAAC,EAAqB,EAAiB,CAAI,EAAmB,GAM9D,CAAC,EAAgB,EAAgB,CAAI,EANoC,CAAlC,EAWvC,EAAgC,IACpC,EANqC,CAM/B,eAAE,WAAe,EAAS,CAAI,EAC9B,CAAC,EAAQ,EAAS,CAAU,MAAV,IAAU,CAA4B,IAAI,EAClE,MACE,UAAC,GAAe,MAAO,SAAe,EAAgB,eAAgB,WACnE,EACH,CAEJ,EAEA,EAAO,YAAc,EAMrB,IAAM,EAAc,eAQd,EAAqB,aACzB,CAAC,EAAuC,KACtC,GAAM,CAAE,2BAAe,EAAY,GAAG,EAAY,CAAI,EAChD,EAAU,EAAiB,EAAa,CADI,EAE5C,EAAY,QADyC,CACb,IAAI,EAC5C,EAAe,EAAgB,EAAc,GAAG,OAEhD,CAF8B,CAE9B,UAAU,KAId,EAAQ,eAAe,GAAY,SAAW,EAAI,OAAO,CAC3D,CAAC,EAEM,EAAa,KAAO,UAAC,EAAU,IAAV,CAAe,EAAN,CAAS,EAAa,IAAK,EAAc,CAChF,GAGF,EAAa,YAAc,EAM3B,IAAM,EAAe,gBAUf,CAAC,EAAuB,EAAiB,CAC7C,EAA+C,GAoB3C,EAAsB,OArBmB,KAqBnB,CAC1B,CAAC,EAAwC,KACvC,GAAM,eACJ,OACA,EAAO,oBACP,EAAa,QACb,EAAQ,qBACR,EAAc,eACd,EAAe,kBACf,GAAkB,oBAClB,EAAoB,CAAC,EACrB,iBAAkB,EAAuB,SACzC,EAAS,2BACT,GAAmB,yBACnB,EAAyB,YACzB,WACA,GAAG,EACL,CAAI,EAEE,EAAU,EAAiB,EAAc,GAEzC,CAAC,EAAS,EAAU,CAAU,IAFwB,GAElC,GAAU,CAAgC,IAAI,EAClE,EAAe,EAAgB,EAAe,GAAS,EAAW,IAAI,CAAC,CAAzC,CAEtB,EAAQ,CAAU,KAAV,KAAU,CAAiC,IAAI,EAC/D,EDrJV,OCqJ6B,EDrJ7B,GACA,SAA0B,UAAc,SA+BxC,OA9BE,EAAe,KACjB,MACA,GAAgB,0CAA0D,EAC1E,iCAQA,EACA,EARA,sBAGA,UAFA,OAKA,WAGA,wBACA,sBACA,0BACA,eACA,eACU,IACV,gBACA,iBAEA,GAAkB,iBAAe,CACjC,CAAO,EAEP,OADA,aAAwC,iBAAmB,EAC3D,kBACA,CACA,CADM,CACN,OAEA,CAAG,MACH,CACA,ECoH8B,GACpB,EAAa,GAAW,OAAS,EACjC,EAAc,GAAW,QAAU,EAInC,EAC4B,UAAhC,OAAO,EACH,EACA,CAAE,IAAK,EAAG,MAAO,EAAG,OAAQ,EAAG,KAAM,EAAG,GAAG,GAE3C,EAAW,MAAM,QAAQ,GAAqB,EAAoB,CAAC,EAAiB,CACpF,EAAwB,EAAS,IADS,EACT,CAAS,EAE1C,CAHoF,CAG5D,CAC5B,QAAS,EACT,SAAU,EAAS,OAAO,GAE1B,MAFmC,MAEtB,CACf,EAEM,MAAE,iBAAM,YAAgB,eAAW,iBAAc,EAAe,CAAI,QAAW,CAAC,CAEpF,SAAU,QACV,UApBwB,CAoBb,EApB+B,WAAV,EAAqB,IAAM,EAAQ,IAqBnE,qBAAsB,IAAI,IACR,QAAU,CAAC,GAAG,EAAM,CAClC,eAA2C,WAA3B,CAClB,CAAC,EAGH,SAAU,CACR,UAAW,EAAQ,MACrB,EACA,WAAY,CACV,QAAM,CAAC,CAAE,SAAU,EAAa,EAAa,cAAe,CAAY,CAAC,EACzE,GACE,QAAK,CAAC,CACJ,UAAU,EACV,WAAW,EACX,QAAoB,YAAX,EAAuB,QAAU,CAAC,EAAI,OAC/C,GAAG,EACJ,EACH,GAAmB,QAAI,CAAC,CAAE,GAAG,EAAuB,EACpD,QAAI,CAAC,CACH,GAAG,EACH,MAAO,CAAC,UAAE,QAAU,iBAAO,kBAAgB,EAAgB,IACzD,GAAM,CAAE,MAAO,EAAa,OAAQ,EAAa,CAAI,EAAM,UACrD,EAAe,EAAS,SAAS,MACvC,EAAa,YAAY,iCAAkC,GAAG,EAAc,GAAI,EAChF,EAAa,KAD+D,MAC/D,CAAY,kCAAmC,GAAG,EAAe,GAAI,EAClF,EAAa,MADiE,KACjE,CAAY,8BAA+B,GAAG,EAAW,GAAI,EAC1E,EAAa,EADyD,SACzD,CAAY,+BAAgC,GAAG,EAAY,GAAI,CAC9E,CACF,CAAC,EACD,EAH4E,CAGnE,QAAe,CAAC,CAAE,QAAS,EAAO,QAAS,CAAa,CAAC,EAClE,EAAgB,YAAE,cAAY,CAAY,CAAC,EAC3C,GAAoB,QAAI,CAAC,CAAE,SAAU,kBAAmB,GAAG,EAAuB,EACpF,CACD,EAEK,CAAC,EAAY,EAAW,CAAI,EAA6B,GAEzD,EAAe,CAFS,CAEM,EAFoC,CAGxE,EAAgB,GAD4B,EAEtC,EAF6B,CAG/B,KAEJ,CAJe,CAIZ,CAAC,EAAc,CAHE,CAGW,EAE/B,CAJmB,GAIb,EAAS,EAFe,KAEA,EAAO,EAC/B,EAAS,EAAe,OAAO,EAC/B,EAAoB,EAAe,OAAO,eAAiB,EAE3D,CAAC,GAAe,GAAgB,CAAU,WAAiB,CAA3B,CAKtC,OAJA,EAAgB,KACV,GAAS,GAAiB,EAAjB,CADA,IACwB,iBAAiB,GAAS,IAAF,EAAQ,CACvE,EAAG,CAAC,EAAQ,EAGV,GAHS,EAGT,KAAC,OACC,IAAK,EAAK,YACV,oCAAkC,GAClC,MAAO,CACL,GAAG,EACH,UAAW,EAAe,EAAe,UAAY,sBACrD,SAAU,cACV,OAAQ,GACP,iCAAwC,CAAG,CAC1C,EAAe,iBAAiB,EAChC,EAAe,iBAAiB,EAClC,CAAE,KAAK,GAAG,EAKV,GAAI,EAAe,MAAM,iBAAmB,CAC1C,WAAY,SACZ,cAAe,MACjB,GAKF,IAAK,EAAM,IAEX,mBAAC,GACC,MAAO,aACP,EACA,cAAe,SACf,SACA,EACA,gBAAiB,EAEjB,mBAAC,EAAU,IAAV,CACC,EADQ,UACG,EACX,aAAY,EACX,GAAG,EACJ,IAAK,EACL,MAAO,CACL,GAAG,EAAa,MAGhB,UAAW,EAAyB,OAAT,MAC7B,GACF,EACF,EAGN,GAGF,EAAc,YAAc,EAM5B,IAAM,EAAa,cAEb,EAAoC,CACxC,IAAK,SACL,MAAO,OACP,OAAQ,MACR,KAAM,OACR,EAMM,EAAoB,aAAiD,SAASK,CAClF,CACA,GACA,GACM,eAAE,EAAe,GAAG,EAAW,CAAI,EACnC,EAAiB,EAAkB,EAAY,GAC/C,EAAW,EAAc,EAAe,IADoB,MACV,EAExD,MAIE,UAAC,QACC,IAAK,EAAe,cACpB,MAAO,CACL,SAAU,WACV,KAAM,EAAe,OACrB,IAAK,EAAe,OACpB,CAAC,EAAQ,CAAG,EACZ,GADS,aACQ,CACf,IAAK,GACL,MAAO,MACP,OAAQ,WACR,KAAM,QACR,EAAE,EAAe,UAAU,EAC3B,UAAW,CACT,IAAK,mBACL,MAAO,iDACP,OAAQ,iBACR,KAAM,gDACR,EAAE,EAAe,UAAU,EAC3B,WAAY,EAAe,gBAAkB,SAAW,MAC1D,EAEA,mBAAgB,EAAf,CACE,GAAG,EACJ,IAAK,EACL,MAAO,CACL,GAAG,EAAW,MAEd,QAAS,OACX,GACF,EAGN,CAAC,EAMD,SAAS,EAAa,GAA6B,OAChC,OAAV,CACT,CANA,EAAY,YAAc,EAQ1B,IAAM,EAAkB,IAAuE,CAC7F,KAAM,0BACN,EACA,GAAG,GAAM,GACD,WAAE,EAAW,uBAAO,EAAe,CAAI,EAEvC,EAAoB,EAAe,OAAO,eAAiB,EAE3D,EAAa,EAAgB,EAAI,EAAQ,WACzC,EAAc,EAAgB,EAAI,EAAQ,YAE1C,CAAC,EAAY,EAAW,CAAI,EAA6B,GACzD,EAAe,CADS,GAA0C,GAC1C,KAAM,OAAQ,MAAO,IAAK,MAAO,EAAE,EAAW,CAEtE,GAAgB,EAAe,GAFuC,EAEvC,EAAO,MAAU,EAAa,EAC7D,GAAgB,EAAe,OAAO,KAAK,CAAK,EAAc,EAEhE,EAAI,GACJ,EAAI,GAeR,MAbmB,UAAU,CAAzB,GACF,EAAI,EAAgB,EAAe,GAAG,EAAY,IAClD,EAAI,GAAG,CAD2C,EAC/B,KACV,IADU,GACY,IAC/B,EAAI,EAAgB,EAAe,GAAG,EAAY,IAClD,EAAI,GAAG,CAD2C,CACrC,SAAS,OAAS,EAAW,KAClB,IADkB,KACT,CAAxB,GACT,EAAI,GAAG,CAAC,EAAW,IACnB,EAAI,EAAgB,CADD,CACgB,GAAG,EAAY,KAC1B,KAD0B,GAClB,CAAvB,IACT,EAAI,GAAG,EAAM,SAAS,MAAQ,EAAW,IACzC,EAAI,EAAgB,CADqB,CACN,GAAG,EAAY,KAE7C,CAAE,IAF2C,CAErC,GAAE,IAAG,CAAE,CAAE,CAC1B,GACF,CAEA,SAAS,EAA6B,GACpC,GAAM,CAAC,EAAM,EAAQ,QAAQ,EAAI,EAAU,MAAM,GAAG,EACpD,MAAO,CAAC,EAAc,EACxB,C3B1XA,E2ByXsC,E3BzXhC,EAAe,aAAuC,CAAC,EAAO,KAClE,GAAM,CAAE,UAAW,EAAe,GAAG,EAAY,CAAI,EAC/C,CAAC,EAAS,EAAU,CAAU,CADa,CACb,KAAV,GAAU,EAAS,GAC7C,EADkD,IAC5B,GAAW,GAAO,CAAH,CAAK,EAA3B,IACT,EAAY,GAAkB,GAAW,YAAY,UAAU,KACrE,OAAO,EACH,cAAS,CAAa,UAAC,EAAU,IAAV,CAAe,EAAN,CAAS,EAAa,IAAK,EAAc,EAAI,GAC7E,IACN,CAAC,CAF2F,CAI5F,EAAO,YArBa,EAqBC,O4B3BrB,MAAyB,CAAK,4CAA8C,EAC5E,aACA,CAF2F,IAE3F,EACA,cACA,gBACA,CAAG,CACH,SACC,EACD,oBAmCA,CACA,cACA,WACC,EACD,SAA4B,UAAc,IAC1C,EAAuB,QAAY,IACnC,EAAsB,QAAY,IAUlC,OATA,OACA,WACA,CAAG,MACD,WAAe,MACjB,gBACA,eACA,YAEA,CAAG,QACH,OACA,EApDA,CACA,cACA,UACA,CAAG,EACH,aACA,OACU,EACV,MAA4B,QAAY,aACpC,WAAe,MACnB,gBACA,UAEA,oCACA,aACA,GAAa,GAAQ,mBAAmB,EAHxC,4BAGwC,CAAM,KAAK,EAAG,4KAEtD,CACA,WACA,CAAK,OACL,CAcA,SAbmB,aAAiB,CACpC,IACA,MACA,kBA+BA,OA/BA,SACA,OACA,cAEA,EAAQ,IACR,IAEA,CAAK,CACL,WAEA,CA2BA,+BEnEA,kBAEA,oBACA,SACA,QACA,SACA,UACA,UACA,kBACA,wBACA,oBACA,iBACA,CAAC,CAcD,CAZqB,YAAgB,CACrC,OAC2B,SAAG,CACxB,EAAS,KACf,CACA,CAFe,EAEf,EACA,MACA,OAAiB,iBACjB,IAIA,YAbQ,EAaqB,SAAI,kCCD3B,GAAY,CAAC,IAAK,QAAS,UAAW,WAAW,EACjD,GAAiB,CAAC,IAAK,OAAO,EAM9B,GAAc,SAGd,CAAC,GAAY,GAAe,GAAqB,CfvBvD,SAAS,CAAiE,EAAc,IAKhF,CekB+C,CflB/B,EAAO,qBACvB,CAAC,EAAyB,EAAqB,CAAI,EAAmB,GAUtE,CAAC,EAAwB,EAAoB,CAAI,EACrD,EAXuF,CAYrF,EAZiD,UAUF,EAEhC,CAAE,QAAS,IAAK,EAAG,QAAS,IAAI,GAAM,CAAF,EAGjD,EAA2E,IAC/E,GAAM,OAAE,WAAO,EAAS,CAAI,EACtB,EAAM,QAAM,CAA0B,IAAI,EAC1C,EAAU,QAAM,CAAgC,IAAI,IAAI,CAAC,OAAE,CACjE,MACE,UAAC,SAAuB,UAAc,EAAkB,cAAe,WACpE,EACH,CAEJ,EAEA,EAAmB,YAAc,EAMjC,IAAM,EAAuB,EAAO,iBAE9B,EAAqB,EAAW,GAChC,EAAiB,GADc,SACR,CAC3B,CAAC,CAFuD,CAEhD,KACN,GAAM,OAAE,WAAO,EAAS,CAAI,EAEtB,EAAe,EAAgB,EADrB,EAAqB,EAAsB,GACA,EADK,EAC5B,SAAoC,EACxE,MAAO,UAAC,GAAmB,IAAK,WAAe,EAAS,CAC1D,GAGF,EAAe,YAAc,EAM7B,IAAM,EAAiB,EAAO,qBACxB,EAAiB,6BAOjB,EAAyB,EAAW,GACpC,EAAqB,GADc,MAAe,GACvB,CAC/B,CAAC,EAAO,KACN,GAAM,OAAE,WAAO,EAAU,GAAG,EAAS,CAAI,EACnC,EAAM,EADyB,MACnB,CAAoB,IAAI,EACpC,EAAe,EAAgB,EAAc,GAAG,EACtC,EAAqB,EAAgB,EADjB,CAQpC,EAP0D,KAE1D,WAAM,CAAU,KACd,EAAQ,QAAQ,IAAI,EAAK,KAAE,EAAK,GAAI,EAAkC,EAC/D,IAAM,KAAK,EAAQ,QAAQ,OAAO,GAAG,EAI5C,UAAC,GAA6B,CAAC,EAAc,CAAG,GAAM,IAAK,IAAd,OAC1C,EACH,CAEJ,UAGF,EAAmB,YAAc,EAuB1B,CACL,CAAE,SAAU,EAAoB,KAAM,EAAgB,SAAU,CAAmB,EAlBrF,SAAS,CAAc,EAAY,IAC3B,EAAU,EAAqB,EAAO,qBAAsB,GAalE,EAbuE,KAahE,EAXU,WAAM,CAAY,KACjC,IAAM,EAAiB,EAAQ,cAAc,QAC7C,GAAI,CAAC,EAAgB,MAAO,CAAC,EAC7B,IADqB,EACA,MAAM,KAAK,EAAe,iBAAiB,IAAI,EAAc,EAAG,CAAC,EAKtF,OAAO,MAJa,KAAK,EAAQ,QAAQ,OAAO,CAAC,EACtB,KACzB,CAAC,EAAG,IAAM,EAAa,QAAQ,EAAE,IAAI,OAAQ,EAAI,EAAa,QAAQ,EAAE,IAAI,OAAQ,EAGxF,EAAG,CAAC,EAAQ,cAAe,EAAQ,OAAO,CAAC,CAG7C,EAKE,EACF,EetFA,IAGI,CAAC,GAAqB,GAAiB,CAAI,EAAmB,GAAa,CAC/E,GACA,EACD,EAH4C,GAItB,EAJ4C,EAwB7D,CAAC,GAAgB,EAtBJ,CAsBoB,CAAI,GAAwC,EApB3C,CAAC,CA4BnC,CAAC,GAA6B,CARG,EAAuD,CAS5F,GAAqD,IAoDjD,GAAgC,IApD4B,GAqD1D,QAtDyD,OAuD7D,WACA,EACA,KAAM,cACN,eACA,EACA,MAAO,eACP,EACA,oBACA,OACA,eACA,EACA,oBACA,OACA,EACF,CAAI,EACE,EAAc,GAAe,GAC7B,CAAC,EAAS,EAAU,CAAU,IADY,GACtB,GAAU,CAAsC,IAAI,EACxE,CAAC,EAAW,EAAY,CAAU,SAAV,CAAU,CAAoC,IAAI,EAC1E,CAAC,EAAsB,EAAuB,CAAU,YAAS,GACjE,EAAY,GADkC,MZ9ItD,GACA,MAAoB,YAAgB,IACpC,kBACA,EY4IiC,GAAG,CAC3B,EAAM,EAAO,CAAI,GAAqB,CAAzB,KACZ,EACN,SAF0C,GAE7B,IAAe,EAC5B,SAAU,EACV,OAAQ,EACV,CAAC,EACK,CAAC,EAAO,EAAQ,CAAI,GAAqB,CAC7C,CADoB,IACd,EACN,SAF4C,GAE/B,EACb,SAAU,EACV,OAAQ,EACV,CAAC,EACK,EAAiC,SAAwC,IAAI,EAG7E,GAAgB,GAAU,GAAQ,CAAC,CAAC,EAAQ,QAAQ,MAAM,EAC1D,CAAC,CAD6D,CAC3C,EAAmB,CAAU,WAAS,IAAI,CAAvB,GAAyC,CAAC,EAO9D,MAAM,KAAK,GAChC,IAAI,GAAY,EAAO,IADyB,CACzB,CAAM,KAAK,EAClC,KAAK,GAAG,EAEX,MACE,UAAiB,EAAhB,CAAsB,GAAG,EACxB,oBAAC,IACC,WACA,MAAO,UACP,EACA,gBAAiB,YACjB,EACA,kBAAmB,uBACnB,EACA,6BAA8B,EAC9B,UAAW,KAAK,CAAC,IACjB,EACA,cAAe,OACf,EACA,aAAc,EACd,IAAK,EACL,2BACA,WAEA,oBAAC,GAAW,SAAX,CAAoB,MAAO,EAC1B,mBAAC,IACC,MAAO,EAAM,cACb,kBAAyB,cAAY,IACnC,EAAoB,GAAU,IAAI,IAAI,GAAM,CAAF,EAAE,CAAI,GAClD,EAAG,CADqD,CAAC,EAEzD,qBAA4B,cAAY,IACtC,EAAqB,IACnB,IAAM,EAAa,IAAI,IAAI,GAE3B,CAF+B,MAC/B,EAAW,OAAO,GACX,CACT,CAAC,CAFyB,EAGzB,CAAC,CAAC,WAEJ,GACH,CACF,EAEC,EACC,WAAC,IAEC,eAAW,WACX,EACA,SAAU,GACV,oBACA,QACA,EAEA,SAAU,GAAW,EAAS,EAAM,OAAO,KAAK,WAChD,OACA,EAEC,UAAU,SAAV,EAAsB,UAAC,UAAO,MAAM,GAAG,EAAK,KAC5C,MAAM,KAAK,GAAgB,EAbvB,GAeL,OACN,CACF,CAEJ,CAEA,IAAO,YAAc,GAMrB,IAAM,GAAe,gBAMf,GAAsB,aAC1B,CAAC,EAAwC,KACvC,GAAM,eAAE,WAAe,GAAW,EAAO,GAAG,EAAa,CAAI,EACvD,EAAc,GAAe,GAC7B,EAAU,GAAiB,GAAc,EADC,CAE1C,EAAa,EAAQ,MADiC,EACjC,EAAY,EACjC,EAAe,EAAgB,EAAc,EAAQ,SAAvB,MAAsC,EACpE,EAAW,GAAc,GACzB,EAAuB,QADe,CAC2B,OAAO,EAExE,CAAC,EAAW,EAAuB,EAAc,CAAI,GAAmB,IAC5E,IADqD,EAChC,IAAW,KAAF,CAAE,CAAO,GAAU,CAAC,EAAK,QAAQ,EACzD,EAAc,EAAa,KAAM,GAAS,EAAK,QAAU,EAAQ,KAAK,EACtE,EAAW,GAAa,EAAc,EAAQ,EACnC,SAD8C,CAC3D,GACF,EAAQ,cAAc,EAAS,KAAK,CAExC,CAAC,EAEK,EAAa,IACZ,IACH,EAAQ,MADO,MACP,EAAa,GAErB,CAFyB,IAKvB,IACF,EAAQ,IAJO,IAGC,gBACR,CAAyB,QAAU,CACzC,EAAG,KAAK,MAAM,EAAa,KAAK,EAChC,EAAG,KAAK,MAAM,EAAa,KAAK,EAClC,CAEJ,EAEA,MACE,UAAiB,EAAhB,CAAuB,SAAO,EAAE,GAAG,EAClC,mBAAC,EAAU,OAAD,CACR,KAAK,SACL,KAAK,WACL,gBAAe,EAAQ,UACvB,gBAAe,EAAQ,KACvB,gBAAe,EAAQ,SACvB,oBAAkB,OAClB,IAAK,EAAQ,IACb,aAAY,EAAQ,KAAO,OAAS,SACpC,SAAU,EACV,gBAAe,EAAa,GAAK,OACjC,mBAAkB,GAAsB,EAAQ,KAAK,EAAI,GAAK,OAC7D,GAAG,EACJ,IAAK,EAEL,QAAS,EAAqB,EAAa,QAAS,IAMlD,EAAM,EANqB,WAMrB,CAAc,MAAM,EAGK,SAAS,CAApC,EAAe,SACjB,EAAW,EAEf,CAAC,EACD,cAAe,EAAqB,EAAa,cAAe,EAA7B,EACjC,EAAe,QAAU,EAAM,YAI/B,IAAM,EAAS,EAAM,OACjB,EAAO,kBAAkB,EAAM,SAAS,GAAG,EACtC,sBAAsB,EAAM,SAAS,EAMzB,IAAjB,EAAM,SAAkC,IAAlB,EAAM,SAA2C,SAAS,CAA/B,EAAM,cACzD,EAAW,GAEX,EAFgB,cAEV,CAAe,EAEzB,CAAC,EACD,UAAW,EAAqB,EAAa,UAAW,IACtD,EAD6B,EACvB,EAAsC,KAAtB,EAAU,UACJ,SAAW,EAAM,QAAU,EAAM,SAClB,EAAG,EAAxB,EAAM,IAAI,QAAc,EAAsB,EAAM,GAAG,IACzE,GAA+B,MAAd,EAAM,KAAa,CACpC,GAAU,SAAS,EAAM,GAAG,GAAG,CACjC,IACA,EAAM,KADK,SACL,CAAe,EAEzB,CAAC,GACH,CACF,CAEJ,GAGF,GAAc,YAAc,GAM5B,IAAM,GAAa,cAQb,GAAoB,aACxB,CAAC,EAAsC,KAErC,GAAM,CAAE,0BAAe,QAAW,EAAO,uBAAU,EAAc,GAAI,GAAG,EAAW,CAAI,EACjF,EAAU,GAAiB,CADkD,EACtC,GACvC,UADoD,oBAClD,EAA6B,CAAI,EACnC,EAA2B,SAAb,EACd,EAAe,EAAgB,EAAc,EAAQ,SAAvB,QAAwC,EAM5E,OAJA,EAAgB,KACd,EAA6B,EAC/B,EAAG,CAAC,EAFW,EAE+B,EADJ,CAIxC,MAH2C,CAG3C,EAAC,EAAU,KAAV,CACE,CADO,EACJ,EACJ,IAAK,EAGL,MAAO,CAAE,cAAe,MAAO,EAE9B,YAAsB,EAAQ,KAAK,EAAI,sBAAG,WAAY,EAAM,GAGnE,GAGF,GAAY,YAAc,GAW1B,IAAM,GAAmB,aACvB,CAAC,EAAqC,KACpC,GAAM,eAAE,WAAe,EAAU,GAAG,EAAU,CAAI,EAClD,KAD8C,CAE5C,UAAC,EAAU,KAAV,CAAe,CAAN,cAAiB,EAAE,GAAG,EAAW,IAAK,EAC7C,YAAY,IACf,CAEJ,GAGF,GAAW,YAhBO,EAgBO,WAiBzB,IAAM,GAA4C,GACzC,UAAC,GAAgB,GAAD,MAAQ,EAAE,GAAG,EAAO,EAG7C,GAAa,YAfO,EAeO,aAM3B,GANsC,CAMhC,GAAe,cAAH,EAKZ,GAAsB,aAC1B,CAAC,EAAwC,KACvC,IAAM,EAAU,GAAiB,GAAc,EAAM,YAAR,CAAqB,EAC5D,CAAC,EAAU,EAAW,CAAU,QAAV,EAAU,CAA2B,QAOjE,CAJA,EAAgB,CAIZ,IAHF,EAAY,IAAI,GADH,cACoB,CAAC,CACjC,CAAC,CAAC,EAEA,EAAQ,MAAM,CAcZ,SAAC,IAAmB,GAAG,EAAO,IAAK,EAAc,EAbzC,EAEA,eACP,UAAC,IAAsB,MAAO,EAAM,cAClC,mBAAC,GAAW,KAAX,CAAgB,MAAO,EAAM,cAC5B,mBAAC,OAAK,WAAM,SAAS,EACvB,EACF,GACA,EAEF,IAIR,GAGF,GAAc,YAAc,GA2B5B,GAAM,CAAC,GAAuB,GAAuB,CACnD,GAA+C,IAgC3C,GAAO,EAAW,IAAd,GAjC2C,CAiC9B,oBAA6B,EAE9C,GAA0B,aAC9B,CAAC,EAA4C,KAC3C,GAAM,eACJ,WACA,EAAW,gCACX,kBACA,uBACA,OAGA,EACA,aACA,oBACA,eACA,EACA,qCACA,EACA,0BACA,kBACA,EAEA,GAAG,EACL,CAAI,EACE,EAAU,GAAiB,GAAc,GACzC,CAAC,EAAS,EAAU,CAAU,IADwB,CAAf,EACnB,GAAU,CAA0C,IAAI,EAC5E,CAAC,EAAU,EAAW,CAAU,QAAV,EAAU,CAAuC,IAAI,EAC3E,EAAe,EAAgB,EAAc,GAAU,EAAW,IAAI,CAAC,CAAzC,CACf,EAAe,CAAU,WAAmC,CAA7C,GAAiD,EAC/E,CAAC,EAAkB,EAAmB,CAAU,WACpD,KAD0C,CAGtC,EAAW,GAAc,GACzB,CAAC,EAAc,EAAe,CAAU,IADF,MACE,EAAV,GAC9B,EADsD,EACvB,QAAO,GAGtC,EAH2C,SAG3C,CAAU,KACd,GAAI,EAAS,OAAO,QAAU,CAAC,EACjC,EAAG,CAAC,EADoC,EAKxC,GAJW,EAIX,GAAc,CAAC,EAEf,IAAM,EAAmB,cACvB,IACE,GAAM,CAAC,EAAW,GAAG,EAAS,CAAI,IAAW,EAAf,CAAe,CAAI,CAAN,EAAgB,EAAK,IAAI,OAAO,EACrE,CAAC,EAAQ,CAAI,EAAU,GAAd,EAAc,CAAM,EAAE,EAE/B,EAA6B,SAAS,cAC5C,QAAW,KAAa,EAEtB,GAAI,IAAc,EAFgB,EAGlC,GAAW,eAAe,CAAE,IADkB,EACX,SAAU,CAAC,EAE1C,IAAc,GAAa,GAAU,GAAS,GAAT,MAAS,EAAY,EAC1D,IAAc,GAAY,GAAU,GAAS,GAAT,MAAS,CAAY,EAAS,cACtE,GAAW,MAAM,EACb,SAAS,gBAAkB,GANe,MAQlD,EACA,CAAC,EAAU,EAAQ,EAGf,EAA0B,EAHX,KAH4C,MAMjC,CAC9B,IAAM,EAAW,CAAC,EAAc,EAAQ,EACxC,CAAC,EADsC,EACZ,EAAO,EAK9B,GAL8B,QAK9B,CAAU,KACV,GACF,GAEJ,EAAG,CAAC,EAAc,EAAkB,CAHhB,CAOpB,GAAM,GANgB,OAEa,IAI3B,2BAAc,EAAyB,CAAI,EAC7C,YAAU,KACd,GAAI,EAAS,CACX,IAAI,EAAmB,CAAE,EAAG,EAAG,EAAG,CAAE,EAE9B,EAAqB,IACzB,EAAmB,CACjB,EAAG,KAAK,IAAI,KAAK,MAAM,EAAM,KAAK,GAAK,EAAyB,SAAS,KAAK,EAAE,EAC7E,KAAK,IAAI,KAAK,MAAM,EAAM,KAAK,GAAK,EAAyB,SAAS,KAAK,CAChF,CADkF,EAG9E,EAAkB,IAElB,EAAiB,GAAK,IAAM,EAAiB,GAAK,GACpD,CADwD,CAClD,eAAe,EAGhB,EAAQ,SAAS,EAAM,MAAqB,GAAG,GACrC,GAGjB,EAHsB,OAGb,oBAAoB,cAAe,GAC5C,EAAyB,QAAU,IACrC,EAOA,OALyC,MAAM,CAA3C,EAAyB,UAC3B,SAAS,iBAAiB,cAAe,GACzC,SAAS,KADiD,WACjD,CAAiB,YAAa,EAAiB,CAAE,QAAS,GAAM,MAAM,CAAK,CAAC,GAGhF,KACL,SAAS,oBAAoB,cAAe,GAC5C,SAAS,KADoD,cACpD,CAAoB,YAAa,EAAiB,CAAE,SAAS,CAAK,CAAC,CAC9E,CACF,CACF,EAAG,CAAC,EAAS,EAAc,EAAyB,EAE9C,YAAU,KACd,GAHiD,CAG3C,EAAQ,IAAM,GAAa,GAGjC,EAHsC,KACtC,OAAO,iBAAiB,OAAQ,GAChC,EADqC,KAC9B,iBAAiB,SAAU,GAC3B,EADgC,GAErC,OAAO,oBAAoB,OAAQ,GACnC,EADwC,KACjC,oBAAoB,SAAU,EACvC,CACF,EAF8C,CAE1C,EAAa,EAEjB,GAAM,CAAC,EAAW,EAAqB,CAAI,GAAmB,IAC5D,IAAM,EAAe,IAAW,CADK,IACP,CAAE,CAAO,GAAU,CAAC,EAAK,QAAQ,EACzD,EAAc,EAAa,KAAM,GAAS,EAAK,IAAI,UAAY,SAAS,aAAa,EACrF,EAAW,GAAa,EAAc,EAAQ,GAChD,GAKF,KAN6D,EACjD,IAKD,IAAO,EAAS,IAAI,QAAwB,MAAM,CAAC,CAElE,CAAC,EAEK,EAAwB,cAC5B,CAAC,EAAgC,EAAe,KAC9C,IAAM,EAAmB,CAAC,EAAuB,SAAW,CAAC,GACpB,SAAlB,EAAQ,OAAuB,EAAQ,QAAU,GAClD,IAAkB,CACtC,EAAgB,GACZ,CADgB,EACE,GAAuB,SAAU,EAAjC,CAE1B,EACA,CAAC,EAAQ,KAAK,GAEV,EAAwB,cAAY,IAAM,GAAS,MAAM,EAAG,CAAC,EAAQ,EACrE,EAA4B,CADwC,CACxC,YAChC,CAAC,EAAoC,EAAe,KAClD,IAAM,EAAmB,CAAC,EAAuB,SAAW,CAAC,GACpB,SAAlB,EAAQ,OAAuB,EAAQ,QAAU,GAClD,IAAkB,EAClB,EAExB,EAF4B,CAG3B,EAAQ,KAAK,GAGV,EAA8B,WAAb,EAAwB,GAAuB,GAGhE,EACJ,IAAmB,GACf,MACE,aACA,QACA,cACA,eACA,EACA,qCACA,SACA,mBACA,EACA,iBACF,EACA,CAAC,EAEP,MACE,UAAC,IACC,MAAO,UACP,WACA,EACA,iBAAkB,kBAClB,eACA,EACA,YAAa,sBACb,oBACA,mBACA,WACA,eACA,YACA,EAEA,mBAAC,IAAY,CAAZ,CAAa,GAAI,GAAM,MAAF,UAAgB,EACpC,mBAAC,GAAU,CAAV,CACC,SAAO,EAGP,QAAS,EAAQ,KACjB,iBAAkB,IAEhB,EAAM,eAAe,CACvB,EACA,mBAAoB,EAAqB,EAAkB,IACzD,EAAQ,SAAS,CADqB,KACf,CAAE,eAAe,CAAK,CAAC,EAC9C,EAAM,eAAe,CACvB,CAAC,EAED,mBAAC,GACC,SAAO,EACP,EAFe,2BAEY,kBAC3B,uBACA,EAGA,eAAgB,GAAW,EAAM,eAAe,EAChD,UAAW,IAAM,EAAQ,aAAa,IAEtC,CAF2C,QAE3C,UAAC,GACC,KAAK,UACL,GAAI,EAAQ,UACZ,aAAY,EAAQ,KAAO,OAAS,SACpC,IAAK,EAAQ,IACb,cAAe,GAAW,EAAM,eAAe,EAC9C,GAAG,EACH,GAAG,EACJ,SAAU,IAAM,GAAgB,GAChC,CADoC,GAC/B,EACL,MAAO,CAEL,QAAS,OACT,cAAe,SAEf,QAAS,OACT,GAAG,EAAa,OAElB,UAAW,EAAqB,EAAa,UAAW,IACtD,EAD6B,EACvB,EAAgB,EAAM,SAAW,EAAM,QAAU,EAAM,QAO7D,GAJkB,MAAO,EAArB,EAAM,KAAe,EAAM,eAAe,EAEzC,GAAsC,EAAG,EAAxB,EAAM,IAAI,QAAc,EAAsB,EAAM,GAAG,EAEzE,CAAC,UAAW,YAAa,OAAQ,KAAK,EAAE,SAAS,EAAM,GAAG,EAAG,CAE/D,IAAI,EAAiB,IADI,KAAF,CAAE,CAAO,GAAU,CAAC,EAAK,QAAQ,EAC7B,IAAI,GAAU,EAAK,IAAI,OAAQ,EAK1D,GAHI,CAAC,UAAW,KAAK,EAAE,SAAS,EAAM,GAAG,GAAG,CAC1C,EAAiB,EAAe,MAAM,EAAE,SAAQ,EAE9C,CAAC,UAAW,WAAW,EAAE,SAAS,EAAM,GAAG,EAAG,CAChD,IAAM,EAAiB,EAAM,OACvB,EAAe,EAAe,QAAQ,GAC5C,EAAiB,EAAe,MAAM,CADoB,CACL,CAAC,CACxD,CAMA,WAAW,IAAM,EAAW,IAE5B,EAAM,QAFoC,CAAC,KAErC,CAAe,CACvB,CACF,CAAC,GACH,EACF,EACF,CACF,GAGN,GAGF,GAAkB,YAvTQ,EAuTM,kBAWhC,IAAM,GAAkC,aAGtC,CAAC,EAAoD,KACrD,GAAM,eAAE,WAAe,EAAU,GAAG,EAAY,CAAI,EAC9C,EAAU,GAAiB,EADe,CACD,GACzC,EAAiB,GAAwB,GAAc,EADD,CAAf,CAEtC,EAAgB,EAAiB,CAAU,IADwB,CAAf,KACT,CAAgC,GAA1C,CAA8C,EAChF,CAAC,EAAS,EAAU,CAAU,OAAV,GAAU,CAAkD,IAAI,EACpF,EAAe,EAAgB,EAAc,GAAU,EAAW,IAAI,CAAC,CAAzC,GACL,GACzB,EAAgC,SAAO,IACvC,CAD4C,CAChB,UAAO,GAEnC,CAFuC,UAErC,cAAU,mBAAc,oBAAkB,EAAkB,CAAI,EAClE,EAAiB,cAAY,KACjC,GACE,EAAQ,SACR,EAAQ,WACR,GACA,GACA,GACA,GACA,EACA,CACA,IAAM,EAAc,EAAQ,QAAQ,sBAAsB,EAKpD,EAAc,EAAQ,sBAAsB,EAC5C,EAAgB,EAAQ,UAAU,sBAAsB,EACxD,EAAe,EAAiB,sBAAsB,EAE5D,GAAoB,QAAhB,EAAQ,IAAe,CACzB,IAAM,EAAiB,EAAa,KAAO,EAAY,KACjD,EAAO,EAAc,KAAO,EAC5B,EAAY,EAAY,KAAO,EAC/B,EAAkB,EAAY,MAAQ,EACtC,EAAe,KAAK,IAAI,EAAiB,EAAY,KAAK,EAE1D,EAAc,EAAM,EAAM,CAAP,GAOvB,KAAK,IAAI,GARO,OAAO,WAlYV,EAkYuB,CAQC,GACtC,EAED,EAAe,MAAM,SAAW,EAAkB,KAClD,EAAe,MAAM,KAAO,EAAc,IAC5C,KAAO,CACL,IAAM,EAAiB,EAAY,MAAQ,EAAa,MAClD,EAAQ,OAAO,WAAa,EAAc,MAAQ,EAClD,EAAa,OAAO,WAAa,EAAY,MAAQ,EACrD,EAAkB,EAAY,MAAQ,EACtC,EAAe,KAAK,IAAI,EAAiB,EAAY,KAAK,EAE1D,EAAe,EAAM,EAAO,CAAR,GAExB,KAAK,IAAI,GAAgB,OAHH,aAAa,CAGC,GACrC,EAED,EAAe,KAHmC,CAG7B,SAAW,EAAkB,KAClD,EAAe,MAAM,MAAQ,EAAe,IAC9C,CAKA,IAAM,EAAQ,IACR,EAAkB,GADD,IACQ,YAAc,GACvC,EAAc,EAAS,UADiC,EACjC,CAEvB,EAAgB,OAAO,iBAAiB,GACxC,EAAwB,EADuB,OACd,EAAc,eAAgB,EAAE,EACjE,EAAoB,SAAS,EAAc,WAAY,EAAE,EACzD,EAA2B,SAAS,EAAc,kBAAmB,EAAE,EAEvE,EAAoB,EAAwB,EAAoB,EADzC,SAAS,EAAc,CACgC,YADhC,CAAe,EAAE,EACsC,EACrG,EAAmB,KAAK,IAAgC,EAA5B,EAAa,aAAkB,GAE3D,EAAiB,OAAO,KAFoD,WAEpD,CAAiB,GACzC,EAAqB,GAD4B,MACnB,EAAe,WAAY,EAAE,EAC3D,EAAwB,SAAS,EAAe,cAAe,EAAE,EAEjE,EAAyB,EAAY,IAAM,EAAY,OAAS,IAAI,CAGpE,EAAyB,EAAa,aAAe,EAErD,EAAyB,EAAwB,GAD9B,EAAa,UAAY,GAMlD,EAL2E,CAGvC,CAEhC,EAF0D,EAE7B,CAC/B,IAAM,EACJ,EAAM,OAAS,GAAK,IAAiB,EAAM,EAAM,OAAS,CAAC,EAAG,IAAI,QACpE,EAAe,MAAM,OAAS,MAG9B,IAAM,EAAmC,KAAK,IAC5C,EAhBgD,EAiBhD,EAEG,GAAa,IAAwB,EALxC,EAAQ,aAAe,EAAS,UAAY,EAAS,cAOnD,GAGJ,EAAe,MAAM,OADN,EAAyB,EACD,IACzC,KAAO,CACL,IAAM,EAAc,EAAM,OAAS,GAAK,IAAiB,EAAM,CAAC,EAAG,IAAI,QACvE,EAAe,MAAM,IAAM,MAC3B,IAAM,EAAgC,KAAK,IACzC,EACA,EACE,EAAS,UAER,GAAc,IAAqB,CACpC,EAGJ,GAAe,MAAM,OADN,GA/BiB,EAAoB,GAgCb,KACvC,EAAS,UAAY,EAAyB,EAAyB,EAAS,CAFjC,QAEiC,CAGlF,EAAe,MAAM,OAAS,GAAG,MACjC,EAAe,MADgC,SAC1B,CAAY,EAAmB,KACpD,EAAe,MAAM,UAAY,EAAkB,KAGnD,MAIA,KAJW,iBAIW,IAAO,EAAwB,SAAU,EACjE,CACF,CAFwE,CAErE,CACD,EACA,EAAQ,QACR,EAAQ,UACR,EACA,EACA,EACA,EACA,EACA,EAAQ,IACR,EACD,EAED,EAAgB,IAAM,IAAY,CAAC,EAAS,EAAb,CAAhB,EAGT,CAHqC,EAGrB,EAAgB,CAAU,WAAiB,EACjE,EAAgB,KACV,GAAS,EAAiB,GAAjB,CADA,GACwB,iBAAiB,GAAS,IAAF,EAAQ,CACvE,EAAG,CAAC,EAAQ,EAMZ,GANW,CAML,EAAiC,cACrC,IACM,IAAwC,IAAhC,EAAoB,UAC9B,IACA,KADS,CAET,EAAoB,SAAU,EAElC,CAHwB,CAIxB,CAAC,EAAU,EAAiB,EAG9B,MACE,OAJ4B,CAI5B,EAAC,IACC,MAAO,EACP,yCACA,EACA,qBAAsB,EAEtB,mBAAC,OACC,IAAK,EACL,MAAO,CACL,QAAS,OACT,cAAe,SACf,SAAU,QACV,OAAQ,CACV,EAEA,mBAAC,EAAU,IAAV,CACE,EADO,CACJ,EACJ,IAAK,EACL,MAAO,CAGL,UAAW,aAEX,UAAW,OACX,GAAG,EAAY,MACjB,EACF,EACF,EAGN,CAAC,EAED,GAA0B,YAvNS,EAuNK,0BAYxC,IAAM,GAA6B,aAGjC,CAAC,EAA+C,KAChD,GAAM,eACJ,EACA,QAAQ,yBACR,IAAmB,CACnB,GAAG,EACL,CAAI,EACE,EAAc,GAAe,CAHd,EAKrB,MACE,IAH8C,CAG9C,KJnpBY,EImpBX,CACE,GAAG,EACH,GAAG,EACJ,IAAK,QACL,mBACA,EACA,MAAO,CAEL,UAAW,aACX,GAAG,EAAY,MAGb,0CAA2C,uCAC3C,yCAA0C,sCAC1C,0CAA2C,uCAC3C,+BAAgC,mCAChC,gCAAiC,mCAErC,GAGN,CAAC,EAED,GAAqB,YA1CQ,EA0CM,qBAYnC,GAAM,CAAC,GAAwB,GAAwB,CACrD,GAAgD,GAAc,CAAC,CAAC,EAE5D,GAAgB,OAFwC,UAUxD,GAAuB,aAC3B,CAAC,EAAyC,KACxC,GAAM,eAAE,QAAe,EAAO,GAAG,EAAc,CAAI,EAC7C,EAAiB,GAAwB,GAAe,CADf,EAEzC,EAAkB,GAAyB,GAAe,EADW,CAErE,EAAe,EAAgB,EAAc,EAAe,EADW,OACzC,OAA8C,EAC5E,EAAyB,SAAO,CAAC,EACvC,MACE,uBAEE,oBAAC,SACC,wBAAyB,CACvB,OAAQ,2KACV,EACA,UAEF,UAAC,GAAW,KAAX,CAAgB,MAAO,EACtB,mBAAC,EAAU,IAAV,CACC,EADQ,2BACmB,GAC3B,KAAK,eACJ,GAAG,EACJ,IAAK,EACL,MAAO,CAIL,SAAU,WACV,KAAM,EAKN,SAAU,cACV,GAAG,EAAc,OAEnB,SAAU,EAAqB,EAAc,SAAU,IACrD,GAD4B,CACtB,EAAW,EAAM,cACjB,CAAE,yCAAgB,EAAwB,CAAI,EACpD,GAAI,GAAyB,SAAW,EAAgB,CACtD,IAAM,EAAa,KAAK,IAAI,EAAiB,QAAU,EAAS,SAAS,EACzE,GAAI,EAAa,EAAG,CAClB,IAAM,EAAkB,OAAO,YAAc,GAGvC,EAAa,KAAK,IAFH,GADyC,QAC9B,EAAe,CAEL,IAFK,CAAM,IAEF,KAFW,EAC5C,WAAW,EAAe,MAAM,MAAM,GAGxD,GAAI,EAAa,EAAiB,CAChC,IAAM,EAAa,EAAa,EAC1B,EAAoB,KAAK,IAAI,EAAiB,GAC9C,EAAa,EAAa,EAEhC,CAH8D,CAG/C,MAAM,OAAS,EAAoB,KACd,OAAO,CAAvC,EAAe,MAAM,SACvB,EAAS,UAAY,EAAa,EAAI,EAAa,EAEnD,EAAe,MAAM,eAAiB,WAE1C,CACF,CACF,CACA,EAAiB,QAAU,EAAS,UACrC,GACH,CACF,GACF,CAEJ,GAGF,GAAe,YAAc,GAM7B,IAAM,GAAa,cAIb,CAAC,GAA4B,GAAqB,CACtD,GAA6C,GAiB/C,CAZ0B,MAL+B,IADD,EAM9B,CACxB,CAAC,EAAsC,KACrC,GAAM,CAAE,gBAAe,GAAG,EAAW,CAAI,EACnC,EAAU,IAChB,CADqB,CAAC,IAEpB,UAAC,IAA2B,MAAO,EAAe,GAAI,EACpD,mBAAC,EAAU,IAAV,CAAc,EAAL,GAAU,QAAQ,kBAAiB,EAAU,GAAG,EAAY,IAAK,EAAc,EAC3F,CAEJ,GAGU,YAAc,GAM1B,IAAM,GAAa,aAanB,CAR0B,aACxB,CAAC,EAAsC,KACrC,GAAM,eAAE,EAAe,GAAG,EAAW,CAAI,EACnC,EAAe,GAAsB,CADN,EACkB,GACvD,MAAO,IAD6D,CAC7D,KAAC,EAAU,IAAV,CAAc,EAAL,CAAS,EAAa,GAAK,GAAG,EAAY,IAAK,EAAc,CAChF,GAGU,YAAc,GAM1B,IAAM,GAAY,aAUZ,CAAC,GAA2B,GAAoB,CACpD,GAA4C,IASxC,GAAmB,MAV6B,MAU7B,CACvB,CAAC,EAAqC,KACpC,GAAM,eACJ,QACA,WACA,GAAW,EACX,UAAW,EACX,GAAG,EACL,CAAI,EACE,EAAU,GAAiB,GAAW,GACtC,EAAiB,GAAwB,GAAW,EADD,CAEnD,EAAa,EAAQ,MAD4C,EAClC,EAC/B,CAAC,EAAW,EAAY,CAAU,SAAV,CAAU,CAAS,GAAiB,EAAE,EAC9D,CAAC,EAAW,EAAY,CAAU,SAAV,CAAU,CAAS,IAC3C,CADgD,CACjC,EAAgB,EAAc,GACjD,EAAe,MADmB,SACnB,GAAkB,EAAM,EAAO,IAE1C,EAAS,EAFyC,EAGlD,CADc,CAAC,EACQ,OAA0C,OAAO,EAExE,EAAe,KACd,IACH,EAAQ,IADK,SACL,CAAc,GACtB,EAAQ,cAAa,GAEzB,EAF8B,GAIhB,IAAI,CAAd,EACF,MAAM,MACJ,yLAIJ,MACE,UAAC,IACC,MAAO,QACP,EACA,kBACA,aACA,EACA,iBAAwB,cAAY,IAClC,EAAa,GAAmB,IAAkB,GAAM,aAAe,IAAI,KAAK,CAAC,CACnF,EAAG,CAAC,CAAC,EAEL,mBAAC,GAAW,SAAX,CACC,MAAO,QACP,WACA,YACA,EAEA,mBAAC,EAAU,IAAV,CACC,EADQ,GACH,SACL,kBAAiB,EACjB,mBAAkB,EAAY,GAAK,OAEnC,gBAAe,GAAc,EAC7B,aAAY,EAAa,UAAY,YACrC,gBAAe,GAAY,OAC3B,gBAAe,EAAW,GAAK,OAC/B,SAAU,EAAW,OAAY,GAChC,GAAG,EACJ,IAAK,EACL,QAAS,EAAqB,EAAU,QAAS,IAAM,GAAa,CAAvC,GAA2C,CAAC,MACjE,EAAqB,EAAU,OAAQ,IAAM,GAAa,EAAtC,EAC5B,CADuE,CAAC,MAC/D,EAAqB,EAAU,QAAS,KAEhB,GAFJ,KAEa,EAApC,EAAe,SAAqB,GAC1C,CAAC,EACD,OAFuD,KAE1C,EAAqB,EAAU,YAAa,IAAxB,CAGA,QAAS,EAApC,EAAe,SAAqB,GAC1C,CAAC,EACD,OAFuD,OAExC,EAAqB,EAAU,cAAe,EAA1B,EACjC,EAAe,QAAU,EAAM,YAChC,EACD,cAAe,EAAqB,EAAU,cAAe,EAA1B,EAEjC,EAAe,QAAU,EAAM,YAC3B,EACF,EAAe,MADH,KACG,GAAc,EACO,SAAS,CAApC,EAAe,SAGxB,EAAM,cAAc,MAAM,CAAE,eAAe,CAAK,CAAC,CAErD,CAAC,EACD,eAAgB,EAAqB,EAAU,eAAgB,CAA3B,GAC9B,EAAM,gBAAkB,SAAS,eAAe,EACnC,cAAc,CAEjC,CAAC,EACD,UAAW,EAAqB,EAAU,UAAW,IAC7B,EADO,SACQ,EAAW,UAAY,IACvC,IAAmB,IAAb,MACvB,GAAe,SAAS,EAAM,GAAG,EAAG,KAEtB,IAAK,EAAnB,EAAM,CAF2C,EAE3C,EAAa,EAAM,eAAe,EAC9C,CAAC,GACH,EACF,EAGN,GAGF,GAAW,YAAc,GAMzB,IAAM,GAAiB,iBAKjB,GAAuB,aAC3B,CAAC,EAAyC,KAExC,GAAM,eAAE,EAAe,kBAAW,EAAO,GAAG,EAAc,CAAI,EACxD,EAAU,GAAiB,GAAgB,CADS,EAEpD,EAAiB,GAAwB,GAAgB,EADD,CAExD,EAAc,GAAqB,GAAgB,EADmB,CAEtE,EAAuB,GAA8B,GAAgB,EADL,CAEhE,CAAC,EAAc,EAAe,CAAU,IAD0C,MAC1C,CAAuC,CAAjD,GAAqD,EACnF,EAAe,EACnB,EACC,GAAS,EAAgB,GAC1B,CAD8B,CAClB,CAHsB,eAGtB,CACZ,GAAU,EAAe,sBAAsB,EAAM,EAAY,MAAO,EAAY,QAAQ,GAGxF,EAAc,GAAc,YAC5B,EAAqB,UACzB,IACE,UAAC,UAA+B,MAAO,EAAY,MAAO,SAAU,EAAY,SAC7E,YADU,EAAY,KAEzB,EAEF,CAAC,EAAY,SAAU,EAAY,MAAO,EAAW,EAGjD,OAHiD,YAG/C,uBAAmB,EAAqB,CAAI,EAMpD,OALA,EAAgB,KACd,EAAkB,GACX,IAAM,EAAqB,GADJ,CAE7B,CAAC,EAAmB,EAAsB,EAAa,CADV,CAI9C,QAHuD,CAGvD,cACE,oBAAC,EAAU,KAAV,CAAe,CAAN,EAAU,EAAY,OAAS,GAAG,EAAe,IAAK,EAAc,EAG7E,EAAY,YAAc,EAAQ,WAAa,CAAC,EAAQ,qBAC5C,eAAa,EAAc,SAAU,EAAQ,SAAS,EAC/D,MACN,CAEJ,GAGF,GAAe,YAAc,GAM7B,IAAM,GAAsB,sBAKtB,GAA4B,aAChC,CAAC,EAA8C,KAC7C,GAAM,eAAE,EAAe,GAAG,EAAmB,CAAI,EAEjD,OADoB,GAAqB,GAAqB,CADjB,EAE1B,UADwD,CAEzE,UAAC,EAAU,KAAV,CAAe,CAAN,cAAiB,EAAE,GAAG,EAAoB,IAAK,EAAc,EACrE,IACN,GAGF,GAAoB,YAAc,GAMlC,IAAM,GAAwB,sBAyC9B,CApCmC,aAGjC,CAAC,EAA+C,KAChD,IAAM,EAAiB,GAAwB,GAAuB,EAAM,aAAa,EACnF,EAAkB,GAAyB,GAAuB,EAAM,aAAa,EACrF,CAAC,EAAa,EAAc,CAAU,WAAV,CAAmB,GAC/C,EADoD,EACrB,EAAc,EAAgB,SAA/B,WAAmD,EAevF,OAbA,EAAgB,KACd,GAAI,EAAe,IADN,IACM,EAAY,EAAe,aAAc,CAE1D,IAASC,EAAT,WAAwB,EACF,EAAS,UAAY,CAC1BC,CACjB,EAJM,EAAW,EAAe,KAGJ,GAHI,CAOhC,OAFAD,IACA,EAAS,QADI,QACJ,CAAiB,SAAUA,GAC7B,IAAM,EAAS,IAD0B,eAC1B,CAAoB,SAAUA,EACtD,CACF,EAAG,CAAC,EAAe,KAFiD,GAEjD,CAAU,EAAe,YAAY,CAAC,EAElD,EACL,UAAC,EAAD,CAAC,CACE,GAAG,EACJ,IAAK,EACL,aAAc,KACZ,GAAM,UAAE,eAAU,EAAa,CAAI,EAC/B,GAAY,GACd,GAAS,QADmB,CACnB,CAAY,EAAS,UAAY,EAAa,aAE3D,IAEA,IACN,CAAC,EAEoB,YAAc,GAMnC,IAAM,GAA0B,wBA4ChC,CAvCqC,aAGnC,CAAC,EAAiD,KAClD,IAAM,EAAiB,GAAwB,GAAyB,EAAM,aAAa,EACrF,EAAkB,GAAyB,GAAyB,EAAM,aAAa,EACvF,CAAC,EAAe,EAAgB,CAAU,YAAS,CAAnB,EAChC,EAAe,EAAgB,EAAc,EAAgB,SAA/B,WAAmD,EAkBvF,OAhBA,EAAgB,KACd,GAAI,EAAe,IADN,IACM,EAAY,EAAe,aAAc,CAE1D,IAASA,EAAT,WAAwB,IAChB,EAAY,EAAS,aAAe,EAAS,aAInD,EADsB,KAAK,KAAK,EAAS,GACxBE,MADiC,EAAI,EAExD,EAPM,EAM0B,EANA,SAUhC,OAFAF,IACA,EAAS,QADI,QACJ,CAAiB,SAAUA,GAC7B,IAAM,EAAS,IAD0B,eAC1B,CAAoB,SAAUA,EACtD,CACF,EAAG,CAAC,EAAe,KAFiD,GAEjD,CAAU,EAAe,YAAY,CAAC,EAElD,EACL,UAAC,IAAD,GACM,EACJ,IAAK,EACL,aAAc,KACZ,GAAM,UAAE,eAAU,EAAa,CAAI,EAC/B,GAAY,IACd,EAAS,QADmB,CACnB,CAAY,EAAS,UAAY,EAAa,aAE3D,IAEA,IACN,CAAC,EAEsB,YAAc,GAOrC,IAAM,GAA+B,aAGnC,CAAC,EAAiD,KAClD,GAAM,eAAE,eAAe,EAAc,GAAG,EAAqB,CAAI,EAC3D,EAAiB,GAAwB,WADc,UACQ,GAC/D,EAA2B,QADiD,CAC3B,IAAI,EACrD,EAAW,GAAc,GAEzB,EAA6B,QAFS,KAET,CAAY,KACV,MAAM,CAArC,EAAmB,UACrB,OAAO,cAAc,EAAmB,OAAO,EAC/C,EAAmB,QAAU,KAEjC,EAAG,CAAC,CAAC,EAeL,OAbM,YAAU,IACP,IAAM,IACZ,CAAC,EAAqB,EAMzB,EAAgB,KACd,IAAM,CAR4B,CAQf,GAPG,CAOQ,KAAF,GAAiB,EAAK,IAAI,UAAY,SAAS,aAAa,EACxF,GAAY,IAAI,SAAS,eAAe,CAAE,MAAO,SAAU,CAAC,CAC9D,EAAG,CAAC,EAAS,EAGX,IAHU,CAGV,KAAC,EAAU,IAAV,CACC,EADQ,aACG,EACV,GAAG,EACJ,IAAK,EACL,MAAO,CAAE,WAAY,EAAG,GAAG,EAAqB,KAAM,EACtD,cAAe,EAAqB,EAAqB,cAAe,EAArC,GACE,MAAM,CAArC,EAAmB,UACrB,EAAmB,QAAU,OAAO,YAAY,EAAc,GAAE,CAEpE,CAAC,EACD,cAAe,EAAqB,EAAqB,cAAe,EAArC,GACjC,EAAe,cAAc,EACM,MAAM,CAArC,EAAmB,UACrB,EAAmB,QAAU,OAAO,YAAY,EAAc,GAAE,CAEpE,CAAC,EACD,eAAgB,EAAqB,EAAqB,eAAgB,CAAtC,IAClC,GACF,CAAC,GAGP,CAAC,CAkBD,CAP8B,WAfD,CAeC,CAC5B,CAAC,EAA0C,KACzC,GAAM,eAAE,EAAe,GAAG,EAAe,CAAI,EAC7C,MAAO,IADkC,CAClC,KAAC,EAAU,IAAV,CAAc,EAAL,aAAgB,EAAE,GAAG,EAAgB,IAAK,EAAc,CAC3E,GAGc,YAZO,EAYO,gBAM9B,IAAM,GAAa,YAAH,CAkBhB,CAZ0B,aACxB,CAAC,EAAsC,KACrC,GAAM,eAAE,EAAe,GAAG,EAAW,CAAI,EACnC,EAAc,GAAe,CADE,EAE/B,EAAU,GAAiB,GAAY,EADG,CAE1C,EAAiB,GAAwB,GAAY,CADhB,CAAe,CAE1D,OAAO,EADkD,CAAe,GACzD,EAAoC,WAA5B,EAAe,SACpC,UAAiB,EAAhB,CAAuB,GAAG,EAAc,GAAG,EAAY,IAAK,EAAc,EACzE,IACN,GAGU,YAAc,GAW1B,IAAM,GAA0B,KAXI,OAWJ,CAC9B,CAAC,eAAE,QAAe,EAAO,GAAG,EAAM,CAAwC,GAAxC,EAChC,IAAM,EAAY,SAA0B,IAAI,EAC1C,EAAe,EAAgB,EAAc,GAAG,EFzmD1D,MEymDwC,GFzmDxC,EE0mDiC,CFzmDjC,MAAc,QAAY,QAAG,aAAwB,EACrD,OAAS,SAAa,MACtB,sBACA,mCACA,mBAEA,oBACG,IACH,EEimDkC,GAgC9B,EAhCmC,KAG7B,YAAU,KACd,IAAM,EAAS,EAAI,QACnB,GAAI,CAAC,EAAQ,OAOb,IAAM,EAJa,OAAO,yBACxB,OAFyB,kBAAkB,UAG3C,SAE0B,IAC5B,GAAI,IAAc,GAAS,EAAU,CACnC,IAAM,EAAQ,IAAI,MAAM,SAAU,CAAE,SAAS,CAAK,CAAC,EACnD,EAAS,KAAK,EAAQ,GACtB,EAD2B,aACpB,CAAc,EACvB,CACF,EAF8B,CAE1B,EAAW,EAAM,EAenB,UAAC,EAAU,OAAD,CACP,GAAG,EACJ,MAAO,CAAE,GAAG,GAAwB,GAAG,EAAM,OAC7C,IAAK,EACL,aAAc,GAGpB,GAOF,SAAS,GAAsB,GAAgB,MAC5B,KAAV,GAA0B,SAAV,CACzB,CAEA,SAAS,GAAmB,GAC1B,IAAM,EAAqB,EAAe,GACpC,EAAkB,OADiB,CACjB,CADgC,EACvB,EAC3B,EAAiB,SAAO,CAAC,EAEzB,EAA8B,cAClC,IACE,IAAM,EAAS,EAAU,QAAU,EACnC,EAAmB,GAElB,GAFwB,MAEf,EAAa,GAAe,EAC1B,QAAU,EACpB,OAAO,aAAa,EAAS,OAAO,EAEtB,GAAI,EAAd,GAAc,GAAS,QAAU,OAAO,WAAW,IAAM,EAAa,EAAE,EAAG,IAAI,EACrF,CAAG,EACL,EACA,CAAC,CAFU,CAEQ,EAGf,EAAuB,YAHR,CAGQ,CAAY,KACvC,EAAU,QAAU,GACpB,OAAO,aAAa,EAAS,OAAO,CACtC,EAAG,CAAC,CAAC,EAML,OAJM,YAAU,IACP,IAAM,OAAO,aAAa,EAAS,OAAO,EAChD,CAAC,CAAC,EAEE,CAAC,EAAW,EAAuB,EAAc,CAoB1D,SAAS,EApBiD,CAqBxD,EACA,EACA,GACA,MAEA,IAAM,EADa,EAAO,OAAS,GAAK,MAAM,KAAK,GAAQ,GAAF,EAAE,CAAM,GAAU,IAAS,EAAO,CAAC,CAAC,EACvD,EAAO,CAAC,EAAK,EAE/C,GAa4B,EAbI,KAAK,IADhB,CACN,CADoB,EAAM,QAAQ,GAAe,GACL,CAAC,CAAC,CAc1D,EAfyD,GAenD,CAAO,CAAC,EAAG,IAAU,GAAO,EAAa,GAdzB,EAcwC,MAAM,CAAE,GAbtB,IAA5B,EAAiB,QACpB,GAAe,EAAa,OAAO,GAAO,IAAM,EAAW,EACnF,IAAM,EAAW,EAAa,KAAK,GACjC,EAAK,UAAU,YAAY,EAAE,WAAW,EAAiB,YAAY,CAAC,GAExE,OAAO,IAAa,EAAc,EAAW,MAC/C,CAxEA,GAAkB,YApDQ,EAoDM,kBAkFhC,IAAMG,GAAO,GACP,GAAU,CADVA,EAEA,GAAQ,GACR,GAAO,GACP,GAAS,GACTC,GAAU,EADJ,CAEN,GAAW,GAGX,GAAO,GACP,GAAW,GACX,GAAgB,oDClvDtB,cACA,MAAc,QAAY,QAAG,aAAwB,EACrD,OAAS,SAAa,MACtB,sBACA,mCACA,mBAEA,oBACG,IACH,mDCXA,cACA,EACA,cACA,UACA,kCACA,oBACA,QACA,6DAA8H,MAC9H,oBACA,CACA,EACA,QAMA,YAAgB,6BALhB,MAKgB,UAJhB,IACA,SACA,gBAEgB,EAChB,aACA,QACA,EACA,cClBA,OAUA,MACA,MAAc,EAAW,GACzB,MADyB,SAVzB,OACA,MAAgB,sBAA0B,CAC1C,YACA,oBACA,4BAGA,OADE,eAAmB,IACrB,EACA,EAGA,KAEA,OADA,mBACA,CACA,EACA", "sources": ["webpack://_N_E/./node_modules/aria-hidden/dist/es2015/index.js", "webpack://_N_E/../../../src/icons/check.ts", "webpack://_N_E/../../../src/icons/chevron-down.ts", "webpack://_N_E/../../../src/icons/copy.ts", "webpack://_N_E/../../../src/icons/key.ts", "webpack://_N_E/../../../src/icons/loader-circle.ts", "webpack://_N_E/../../../src/icons/plus.ts", "webpack://_N_E/../../../src/icons/refresh-cw.ts", "webpack://_N_E/../../../src/icons/trash-2.ts", "webpack://_N_E/./node_modules/tslib/tslib.es6.mjs", "webpack://_N_E/./node_modules/react-remove-scroll-bar/dist/es2015/constants.js", "webpack://_N_E/./node_modules/use-callback-ref/dist/es2015/assignRef.js", "webpack://_N_E/./node_modules/use-callback-ref/dist/es2015/useRef.js", "webpack://_N_E/./node_modules/use-callback-ref/dist/es2015/useMergeRef.js", "webpack://_N_E/./node_modules/use-sidecar/dist/es2015/medium.js", "webpack://_N_E/./node_modules/react-remove-scroll/dist/es2015/medium.js", "webpack://_N_E/./node_modules/react-remove-scroll/dist/es2015/UI.js", "webpack://_N_E/./node_modules/use-sidecar/dist/es2015/exports.js", "webpack://_N_E/./node_modules/get-nonce/dist/es2015/index.js", "webpack://_N_E/./node_modules/react-style-singleton/dist/es2015/singleton.js", "webpack://_N_E/./node_modules/react-style-singleton/dist/es2015/hook.js", "webpack://_N_E/./node_modules/react-style-singleton/dist/es2015/component.js", "webpack://_N_E/./node_modules/react-style-singleton/dist/es2015/index.js", "webpack://_N_E/./node_modules/react-remove-scroll-bar/dist/es2015/utils.js", "webpack://_N_E/./node_modules/react-remove-scroll-bar/dist/es2015/component.js", "webpack://_N_E/./node_modules/react-remove-scroll-bar/dist/es2015/index.js", "webpack://_N_E/./node_modules/react-remove-scroll/dist/es2015/aggresiveCapture.js", "webpack://_N_E/./node_modules/react-remove-scroll/dist/es2015/handleScroll.js", "webpack://_N_E/./node_modules/react-remove-scroll/dist/es2015/SideEffect.js", "webpack://_N_E/./node_modules/react-remove-scroll/dist/es2015/sidecar.js", "webpack://_N_E/./node_modules/react-remove-scroll/dist/es2015/Combination.js", "webpack://_N_E/./node_modules/@radix-ui/react-dialog/node_modules/@radix-ui/primitive/dist/index.mjs", "webpack://_N_E/./node_modules/@radix-ui/react-dialog/node_modules/@radix-ui/react-compose-refs/dist/index.mjs", "webpack://_N_E/./node_modules/@radix-ui/react-dialog/node_modules/@radix-ui/react-context/dist/index.mjs", "webpack://_N_E/./node_modules/@radix-ui/react-dialog/node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs", "webpack://_N_E/./node_modules/@radix-ui/react-dialog/node_modules/@radix-ui/react-id/dist/index.mjs", "webpack://_N_E/./node_modules/@radix-ui/react-dialog/node_modules/@radix-ui/react-use-controllable-state/dist/index.mjs", "webpack://_N_E/./node_modules/@radix-ui/react-dialog/node_modules/@radix-ui/react-slot/dist/index.mjs", "webpack://_N_E/./node_modules/@radix-ui/react-dialog/node_modules/@radix-ui/react-primitive/dist/index.mjs", "webpack://_N_E/./node_modules/@radix-ui/react-dialog/node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs", "webpack://_N_E/./node_modules/@radix-ui/react-dialog/node_modules/@radix-ui/react-use-escape-keydown/dist/index.mjs", "webpack://_N_E/../src/dismissable-layer.tsx", "webpack://_N_E/../src/portal.tsx", "webpack://_N_E/../src/presence.tsx", "webpack://_N_E/../src/use-state-machine.tsx", "webpack://_N_E/../src/dialog.tsx", "webpack://_N_E/../src/focus-guards.tsx", "webpack://_N_E/./node_modules/@radix-ui/react-focus-scope/node_modules/@radix-ui/react-compose-refs/dist/index.mjs", "webpack://_N_E/./node_modules/@radix-ui/react-focus-scope/node_modules/@radix-ui/react-slot/dist/index.mjs", "webpack://_N_E/./node_modules/@radix-ui/react-focus-scope/node_modules/@radix-ui/react-primitive/dist/index.mjs", "webpack://_N_E/./node_modules/@radix-ui/react-focus-scope/node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs", "webpack://_N_E/../src/focus-scope.tsx", "webpack://_N_E/../src/Label.tsx", "webpack://_N_E/./node_modules/@radix-ui/number/dist/index.mjs", "webpack://_N_E/./node_modules/@radix-ui/react-select/node_modules/@radix-ui/primitive/dist/index.mjs", "webpack://_N_E/./node_modules/@radix-ui/react-select/node_modules/@radix-ui/react-context/dist/index.mjs", "webpack://_N_E/./node_modules/@radix-ui/react-select/node_modules/@radix-ui/react-compose-refs/dist/index.mjs", "webpack://_N_E/./node_modules/@radix-ui/react-select/node_modules/@radix-ui/react-slot/dist/index.mjs", "webpack://_N_E/../src/collection-legacy.tsx", "webpack://_N_E/../src/collection.tsx", "webpack://_N_E/../src/ordered-dictionary.ts", "webpack://_N_E/./node_modules/@radix-ui/react-select/node_modules/@radix-ui/react-direction/dist/index.mjs", "webpack://_N_E/./node_modules/@radix-ui/react-select/node_modules/@radix-ui/react-primitive/dist/index.mjs", "webpack://_N_E/./node_modules/@radix-ui/react-select/node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs", "webpack://_N_E/./node_modules/@radix-ui/react-select/node_modules/@radix-ui/react-use-escape-keydown/dist/index.mjs", "webpack://_N_E/./node_modules/@radix-ui/react-select/node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs", "webpack://_N_E/./node_modules/@radix-ui/react-select/node_modules/@radix-ui/react-id/dist/index.mjs", "webpack://_N_E/./node_modules/@radix-ui/react-select/node_modules/@radix-ui/react-arrow/dist/index.mjs", "webpack://_N_E/./node_modules/@radix-ui/react-select/node_modules/@radix-ui/react-use-size/dist/index.mjs", "webpack://_N_E/../src/popper.tsx", "webpack://_N_E/./node_modules/@radix-ui/react-select/node_modules/@radix-ui/react-use-controllable-state/dist/index.mjs", "webpack://_N_E/./node_modules/@radix-ui/react-select/node_modules/@radix-ui/react-use-previous/dist/index.mjs", "webpack://_N_E/./node_modules/@radix-ui/react-select/node_modules/@radix-ui/react-visually-hidden/dist/index.mjs", "webpack://_N_E/../src/select.tsx", "webpack://_N_E/./node_modules/@radix-ui/react-use-previous/dist/index.mjs", "webpack://_N_E/./node_modules/zustand/esm/vanilla.mjs", "webpack://_N_E/./node_modules/zustand/esm/react.mjs"], "sourcesContent": ["var getDefaultParent = function (originalTarget) {\n    if (typeof document === 'undefined') {\n        return null;\n    }\n    var sampleTarget = Array.isArray(originalTarget) ? originalTarget[0] : originalTarget;\n    return sampleTarget.ownerDocument.body;\n};\nvar counterMap = new WeakMap();\nvar uncontrolledNodes = new WeakMap();\nvar markerMap = {};\nvar lockCount = 0;\nvar unwrapHost = function (node) {\n    return node && (node.host || unwrapHost(node.parentNode));\n};\nvar correctTargets = function (parent, targets) {\n    return targets\n        .map(function (target) {\n        if (parent.contains(target)) {\n            return target;\n        }\n        var correctedTarget = unwrapHost(target);\n        if (correctedTarget && parent.contains(correctedTarget)) {\n            return correctedTarget;\n        }\n        console.error('aria-hidden', target, 'in not contained inside', parent, '. Doing nothing');\n        return null;\n    })\n        .filter(function (x) { return Boolean(x); });\n};\n/**\n * Marks everything except given node(or nodes) as aria-hidden\n * @param {Element | Element[]} originalTarget - elements to keep on the page\n * @param [parentNode] - top element, defaults to document.body\n * @param {String} [markerName] - a special attribute to mark every node\n * @param {String} [controlAttribute] - html Attribute to control\n * @return {Undo} undo command\n */\nvar applyAttributeToOthers = function (originalTarget, parentNode, markerName, controlAttribute) {\n    var targets = correctTargets(parentNode, Array.isArray(originalTarget) ? originalTarget : [originalTarget]);\n    if (!markerMap[markerName]) {\n        markerMap[markerName] = new WeakMap();\n    }\n    var markerCounter = markerMap[markerName];\n    var hiddenNodes = [];\n    var elementsToKeep = new Set();\n    var elementsToStop = new Set(targets);\n    var keep = function (el) {\n        if (!el || elementsToKeep.has(el)) {\n            return;\n        }\n        elementsToKeep.add(el);\n        keep(el.parentNode);\n    };\n    targets.forEach(keep);\n    var deep = function (parent) {\n        if (!parent || elementsToStop.has(parent)) {\n            return;\n        }\n        Array.prototype.forEach.call(parent.children, function (node) {\n            if (elementsToKeep.has(node)) {\n                deep(node);\n            }\n            else {\n                try {\n                    var attr = node.getAttribute(controlAttribute);\n                    var alreadyHidden = attr !== null && attr !== 'false';\n                    var counterValue = (counterMap.get(node) || 0) + 1;\n                    var markerValue = (markerCounter.get(node) || 0) + 1;\n                    counterMap.set(node, counterValue);\n                    markerCounter.set(node, markerValue);\n                    hiddenNodes.push(node);\n                    if (counterValue === 1 && alreadyHidden) {\n                        uncontrolledNodes.set(node, true);\n                    }\n                    if (markerValue === 1) {\n                        node.setAttribute(markerName, 'true');\n                    }\n                    if (!alreadyHidden) {\n                        node.setAttribute(controlAttribute, 'true');\n                    }\n                }\n                catch (e) {\n                    console.error('aria-hidden: cannot operate on ', node, e);\n                }\n            }\n        });\n    };\n    deep(parentNode);\n    elementsToKeep.clear();\n    lockCount++;\n    return function () {\n        hiddenNodes.forEach(function (node) {\n            var counterValue = counterMap.get(node) - 1;\n            var markerValue = markerCounter.get(node) - 1;\n            counterMap.set(node, counterValue);\n            markerCounter.set(node, markerValue);\n            if (!counterValue) {\n                if (!uncontrolledNodes.has(node)) {\n                    node.removeAttribute(controlAttribute);\n                }\n                uncontrolledNodes.delete(node);\n            }\n            if (!markerValue) {\n                node.removeAttribute(markerName);\n            }\n        });\n        lockCount--;\n        if (!lockCount) {\n            // clear\n            counterMap = new WeakMap();\n            counterMap = new WeakMap();\n            uncontrolledNodes = new WeakMap();\n            markerMap = {};\n        }\n    };\n};\n/**\n * Marks everything except given node(or nodes) as aria-hidden\n * @param {Element | Element[]} originalTarget - elements to keep on the page\n * @param [parentNode] - top element, defaults to document.body\n * @param {String} [markerName] - a special attribute to mark every node\n * @return {Undo} undo command\n */\nexport var hideOthers = function (originalTarget, parentNode, markerName) {\n    if (markerName === void 0) { markerName = 'data-aria-hidden'; }\n    var targets = Array.from(Array.isArray(originalTarget) ? originalTarget : [originalTarget]);\n    var activeParentNode = parentNode || getDefaultParent(originalTarget);\n    if (!activeParentNode) {\n        return function () { return null; };\n    }\n    // we should not hide ariaLive elements - https://github.com/theKashey/aria-hidden/issues/10\n    targets.push.apply(targets, Array.from(activeParentNode.querySelectorAll('[aria-live]')));\n    return applyAttributeToOthers(targets, activeParentNode, markerName, 'aria-hidden');\n};\n/**\n * Marks everything except given node(or nodes) as inert\n * @param {Element | Element[]} originalTarget - elements to keep on the page\n * @param [parentNode] - top element, defaults to document.body\n * @param {String} [markerName] - a special attribute to mark every node\n * @return {Undo} undo command\n */\nexport var inertOthers = function (originalTarget, parentNode, markerName) {\n    if (markerName === void 0) { markerName = 'data-inert-ed'; }\n    var activeParentNode = parentNode || getDefaultParent(originalTarget);\n    if (!activeParentNode) {\n        return function () { return null; };\n    }\n    return applyAttributeToOthers(originalTarget, activeParentNode, markerName, 'inert');\n};\n/**\n * @returns if current browser supports inert\n */\nexport var supportsInert = function () {\n    return typeof HTMLElement !== 'undefined' && HTMLElement.prototype.hasOwnProperty('inert');\n};\n/**\n * Automatic function to \"suppress\" DOM elements - _hide_ or _inert_ in the best possible way\n * @param {Element | Element[]} originalTarget - elements to keep on the page\n * @param [parentNode] - top element, defaults to document.body\n * @param {String} [markerName] - a special attribute to mark every node\n * @return {Undo} undo command\n */\nexport var suppressOthers = function (originalTarget, parentNode, markerName) {\n    if (markerName === void 0) { markerName = 'data-suppressed'; }\n    return (supportsInert() ? inertOthers : hideOthers)(originalTarget, parentNode, markerName);\n};\n", "import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Check\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMjAgNiA5IDE3bC01LTUiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/check\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Check = createLucideIcon('Check', [['path', { d: 'M20 6 9 17l-5-5', key: '1gmf2c' }]]);\n\nexport default Check;\n", "import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name ChevronDown\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtNiA5IDYgNiA2LTYiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/chevron-down\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ChevronDown = createLucideIcon('ChevronDown', [\n  ['path', { d: 'm6 9 6 6 6-6', key: 'qrunsl' }],\n]);\n\nexport default ChevronDown;\n", "import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Copy\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cmVjdCB3aWR0aD0iMTQiIGhlaWdodD0iMTQiIHg9IjgiIHk9IjgiIHJ4PSIyIiByeT0iMiIgLz4KICA8cGF0aCBkPSJNNCAxNmMtMS4xIDAtMi0uOS0yLTJWNGMwLTEuMS45LTIgMi0yaDEwYzEuMSAwIDIgLjkgMiAyIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/copy\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Copy = createLucideIcon('Copy', [\n  ['rect', { width: '14', height: '14', x: '8', y: '8', rx: '2', ry: '2', key: '17jyea' }],\n  ['path', { d: 'M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2', key: 'zix9uf' }],\n]);\n\nexport default Copy;\n", "import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Key\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtMTUuNSA3LjUgMi4zIDIuM2ExIDEgMCAwIDAgMS40IDBsMi4xLTIuMWExIDEgMCAwIDAgMC0xLjRMMTkgNCIgLz4KICA8cGF0aCBkPSJtMjEgMi05LjYgOS42IiAvPgogIDxjaXJjbGUgY3g9IjcuNSIgY3k9IjE1LjUiIHI9IjUuNSIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/key\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Key = createLucideIcon('Key', [\n  ['path', { d: 'm15.5 7.5 2.3 2.3a1 1 0 0 0 1.4 0l2.1-2.1a1 1 0 0 0 0-1.4L19 4', key: 'g0fldk' }],\n  ['path', { d: 'm21 2-9.6 9.6', key: '1j0ho8' }],\n  ['circle', { cx: '7.5', cy: '15.5', r: '5.5', key: 'yqb3hr' }],\n]);\n\nexport default Key;\n", "import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name LoaderCircle\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMjEgMTJhOSA5IDAgMSAxLTYuMjE5LTguNTYiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/loader-circle\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst LoaderCircle = createLucideIcon('LoaderCircle', [\n  ['path', { d: 'M21 12a9 9 0 1 1-6.219-8.56', key: '13zald' }],\n]);\n\nexport default LoaderCircle;\n", "import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Plus\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNNSAxMmgxNCIgLz4KICA8cGF0aCBkPSJNMTIgNXYxNCIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/plus\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Plus = createLucideIcon('Plus', [\n  ['path', { d: 'M5 12h14', key: '1ays0h' }],\n  ['path', { d: 'M12 5v14', key: 's699le' }],\n]);\n\nexport default Plus;\n", "import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name RefreshCw\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMyAxMmE5IDkgMCAwIDEgOS05IDkuNzUgOS43NSAwIDAgMSA2Ljc0IDIuNzRMMjEgOCIgLz4KICA8cGF0aCBkPSJNMjEgM3Y1aC01IiAvPgogIDxwYXRoIGQ9Ik0yMSAxMmE5IDkgMCAwIDEtOSA5IDkuNzUgOS43NSAwIDAgMS02Ljc0LTIuNzRMMyAxNiIgLz4KICA8cGF0aCBkPSJNOCAxNkgzdjUiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/refresh-cw\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst RefreshCw = createLucideIcon('RefreshCw', [\n  ['path', { d: 'M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8', key: 'v9h5vc' }],\n  ['path', { d: 'M21 3v5h-5', key: '1q7to0' }],\n  ['path', { d: 'M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16', key: '3uifl3' }],\n  ['path', { d: 'M8 16H3v5', key: '1cv678' }],\n]);\n\nexport default RefreshCw;\n", "import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Trash2\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMyA2aDE4IiAvPgogIDxwYXRoIGQ9Ik0xOSA2djE0YzAgMS0xIDItMiAySDdjLTEgMC0yLTEtMi0yVjYiIC8+CiAgPHBhdGggZD0iTTggNlY0YzAtMSAxLTIgMi0yaDRjMSAwIDIgMSAyIDJ2MiIgLz4KICA8bGluZSB4MT0iMTAiIHgyPSIxMCIgeTE9IjExIiB5Mj0iMTciIC8+CiAgPGxpbmUgeDE9IjE0IiB4Mj0iMTQiIHkxPSIxMSIgeTI9IjE3IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/trash-2\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Trash2 = createLucideIcon('Trash2', [\n  ['path', { d: 'M3 6h18', key: 'd0wm0j' }],\n  ['path', { d: 'M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6', key: '4alrt4' }],\n  ['path', { d: 'M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2', key: 'v07s0e' }],\n  ['line', { x1: '10', x2: '10', y1: '11', y2: '17', key: '1uufr5' }],\n  ['line', { x1: '14', x2: '14', y1: '11', y2: '17', key: 'xtxkd' }],\n]);\n\nexport default Trash2;\n", "/******************************************************************************\nCopyright (c) Microsoft Corporation.\n\nPermission to use, copy, modify, and/or distribute this software for any\npurpose with or without fee is hereby granted.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\nPERFORMANCE OF THIS SOFTWARE.\n***************************************************************************** */\n/* global Reflect, Promise, SuppressedError, Symbol, Iterator */\n\nvar extendStatics = function(d, b) {\n  extendStatics = Object.setPrototypeOf ||\n      ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n      function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n  return extendStatics(d, b);\n};\n\nexport function __extends(d, b) {\n  if (typeof b !== \"function\" && b !== null)\n      throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n  extendStatics(d, b);\n  function __() { this.constructor = d; }\n  d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n}\n\nexport var __assign = function() {\n  __assign = Object.assign || function __assign(t) {\n      for (var s, i = 1, n = arguments.length; i < n; i++) {\n          s = arguments[i];\n          for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\n      }\n      return t;\n  }\n  return __assign.apply(this, arguments);\n}\n\nexport function __rest(s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\n      t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\n      for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n          if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\n              t[p[i]] = s[p[i]];\n      }\n  return t;\n}\n\nexport function __decorate(decorators, target, key, desc) {\n  var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\n  if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\n  else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n  return c > 3 && r && Object.defineProperty(target, key, r), r;\n}\n\nexport function __param(paramIndex, decorator) {\n  return function (target, key) { decorator(target, key, paramIndex); }\n}\n\nexport function __esDecorate(ctor, descriptorIn, decorators, contextIn, initializers, extraInitializers) {\n  function accept(f) { if (f !== void 0 && typeof f !== \"function\") throw new TypeError(\"Function expected\"); return f; }\n  var kind = contextIn.kind, key = kind === \"getter\" ? \"get\" : kind === \"setter\" ? \"set\" : \"value\";\n  var target = !descriptorIn && ctor ? contextIn[\"static\"] ? ctor : ctor.prototype : null;\n  var descriptor = descriptorIn || (target ? Object.getOwnPropertyDescriptor(target, contextIn.name) : {});\n  var _, done = false;\n  for (var i = decorators.length - 1; i >= 0; i--) {\n      var context = {};\n      for (var p in contextIn) context[p] = p === \"access\" ? {} : contextIn[p];\n      for (var p in contextIn.access) context.access[p] = contextIn.access[p];\n      context.addInitializer = function (f) { if (done) throw new TypeError(\"Cannot add initializers after decoration has completed\"); extraInitializers.push(accept(f || null)); };\n      var result = (0, decorators[i])(kind === \"accessor\" ? { get: descriptor.get, set: descriptor.set } : descriptor[key], context);\n      if (kind === \"accessor\") {\n          if (result === void 0) continue;\n          if (result === null || typeof result !== \"object\") throw new TypeError(\"Object expected\");\n          if (_ = accept(result.get)) descriptor.get = _;\n          if (_ = accept(result.set)) descriptor.set = _;\n          if (_ = accept(result.init)) initializers.unshift(_);\n      }\n      else if (_ = accept(result)) {\n          if (kind === \"field\") initializers.unshift(_);\n          else descriptor[key] = _;\n      }\n  }\n  if (target) Object.defineProperty(target, contextIn.name, descriptor);\n  done = true;\n};\n\nexport function __runInitializers(thisArg, initializers, value) {\n  var useValue = arguments.length > 2;\n  for (var i = 0; i < initializers.length; i++) {\n      value = useValue ? initializers[i].call(thisArg, value) : initializers[i].call(thisArg);\n  }\n  return useValue ? value : void 0;\n};\n\nexport function __propKey(x) {\n  return typeof x === \"symbol\" ? x : \"\".concat(x);\n};\n\nexport function __setFunctionName(f, name, prefix) {\n  if (typeof name === \"symbol\") name = name.description ? \"[\".concat(name.description, \"]\") : \"\";\n  return Object.defineProperty(f, \"name\", { configurable: true, value: prefix ? \"\".concat(prefix, \" \", name) : name });\n};\n\nexport function __metadata(metadataKey, metadataValue) {\n  if (typeof Reflect === \"object\" && typeof Reflect.metadata === \"function\") return Reflect.metadata(metadataKey, metadataValue);\n}\n\nexport function __awaiter(thisArg, _arguments, P, generator) {\n  function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n  return new (P || (P = Promise))(function (resolve, reject) {\n      function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n      function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n      function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n      step((generator = generator.apply(thisArg, _arguments || [])).next());\n  });\n}\n\nexport function __generator(thisArg, body) {\n  var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === \"function\" ? Iterator : Object).prototype);\n  return g.next = verb(0), g[\"throw\"] = verb(1), g[\"return\"] = verb(2), typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\n  function verb(n) { return function (v) { return step([n, v]); }; }\n  function step(op) {\n      if (f) throw new TypeError(\"Generator is already executing.\");\n      while (g && (g = 0, op[0] && (_ = 0)), _) try {\n          if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\n          if (y = 0, t) op = [op[0] & 2, t.value];\n          switch (op[0]) {\n              case 0: case 1: t = op; break;\n              case 4: _.label++; return { value: op[1], done: false };\n              case 5: _.label++; y = op[1]; op = [0]; continue;\n              case 7: op = _.ops.pop(); _.trys.pop(); continue;\n              default:\n                  if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\n                  if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\n                  if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\n                  if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\n                  if (t[2]) _.ops.pop();\n                  _.trys.pop(); continue;\n          }\n          op = body.call(thisArg, _);\n      } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\n      if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\n  }\n}\n\nexport var __createBinding = Object.create ? (function(o, m, k, k2) {\n  if (k2 === undefined) k2 = k;\n  var desc = Object.getOwnPropertyDescriptor(m, k);\n  if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n  }\n  Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n  if (k2 === undefined) k2 = k;\n  o[k2] = m[k];\n});\n\nexport function __exportStar(m, o) {\n  for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(o, p)) __createBinding(o, m, p);\n}\n\nexport function __values(o) {\n  var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n  if (m) return m.call(o);\n  if (o && typeof o.length === \"number\") return {\n      next: function () {\n          if (o && i >= o.length) o = void 0;\n          return { value: o && o[i++], done: !o };\n      }\n  };\n  throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n}\n\nexport function __read(o, n) {\n  var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n  if (!m) return o;\n  var i = m.call(o), r, ar = [], e;\n  try {\n      while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n  }\n  catch (error) { e = { error: error }; }\n  finally {\n      try {\n          if (r && !r.done && (m = i[\"return\"])) m.call(i);\n      }\n      finally { if (e) throw e.error; }\n  }\n  return ar;\n}\n\n/** @deprecated */\nexport function __spread() {\n  for (var ar = [], i = 0; i < arguments.length; i++)\n      ar = ar.concat(__read(arguments[i]));\n  return ar;\n}\n\n/** @deprecated */\nexport function __spreadArrays() {\n  for (var s = 0, i = 0, il = arguments.length; i < il; i++) s += arguments[i].length;\n  for (var r = Array(s), k = 0, i = 0; i < il; i++)\n      for (var a = arguments[i], j = 0, jl = a.length; j < jl; j++, k++)\n          r[k] = a[j];\n  return r;\n}\n\nexport function __spreadArray(to, from, pack) {\n  if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n      if (ar || !(i in from)) {\n          if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n          ar[i] = from[i];\n      }\n  }\n  return to.concat(ar || Array.prototype.slice.call(from));\n}\n\nexport function __await(v) {\n  return this instanceof __await ? (this.v = v, this) : new __await(v);\n}\n\nexport function __asyncGenerator(thisArg, _arguments, generator) {\n  if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\n  var g = generator.apply(thisArg, _arguments || []), i, q = [];\n  return i = Object.create((typeof AsyncIterator === \"function\" ? AsyncIterator : Object).prototype), verb(\"next\"), verb(\"throw\"), verb(\"return\", awaitReturn), i[Symbol.asyncIterator] = function () { return this; }, i;\n  function awaitReturn(f) { return function (v) { return Promise.resolve(v).then(f, reject); }; }\n  function verb(n, f) { if (g[n]) { i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; if (f) i[n] = f(i[n]); } }\n  function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\n  function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r); }\n  function fulfill(value) { resume(\"next\", value); }\n  function reject(value) { resume(\"throw\", value); }\n  function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\n}\n\nexport function __asyncDelegator(o) {\n  var i, p;\n  return i = {}, verb(\"next\"), verb(\"throw\", function (e) { throw e; }), verb(\"return\"), i[Symbol.iterator] = function () { return this; }, i;\n  function verb(n, f) { i[n] = o[n] ? function (v) { return (p = !p) ? { value: __await(o[n](v)), done: false } : f ? f(v) : v; } : f; }\n}\n\nexport function __asyncValues(o) {\n  if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\n  var m = o[Symbol.asyncIterator], i;\n  return m ? m.call(o) : (o = typeof __values === \"function\" ? __values(o) : o[Symbol.iterator](), i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i);\n  function verb(n) { i[n] = o[n] && function (v) { return new Promise(function (resolve, reject) { v = o[n](v), settle(resolve, reject, v.done, v.value); }); }; }\n  function settle(resolve, reject, d, v) { Promise.resolve(v).then(function(v) { resolve({ value: v, done: d }); }, reject); }\n}\n\nexport function __makeTemplateObject(cooked, raw) {\n  if (Object.defineProperty) { Object.defineProperty(cooked, \"raw\", { value: raw }); } else { cooked.raw = raw; }\n  return cooked;\n};\n\nvar __setModuleDefault = Object.create ? (function(o, v) {\n  Object.defineProperty(o, \"default\", { enumerable: true, value: v });\n}) : function(o, v) {\n  o[\"default\"] = v;\n};\n\nvar ownKeys = function(o) {\n  ownKeys = Object.getOwnPropertyNames || function (o) {\n    var ar = [];\n    for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;\n    return ar;\n  };\n  return ownKeys(o);\n};\n\nexport function __importStar(mod) {\n  if (mod && mod.__esModule) return mod;\n  var result = {};\n  if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== \"default\") __createBinding(result, mod, k[i]);\n  __setModuleDefault(result, mod);\n  return result;\n}\n\nexport function __importDefault(mod) {\n  return (mod && mod.__esModule) ? mod : { default: mod };\n}\n\nexport function __classPrivateFieldGet(receiver, state, kind, f) {\n  if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a getter\");\n  if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot read private member from an object whose class did not declare it\");\n  return kind === \"m\" ? f : kind === \"a\" ? f.call(receiver) : f ? f.value : state.get(receiver);\n}\n\nexport function __classPrivateFieldSet(receiver, state, value, kind, f) {\n  if (kind === \"m\") throw new TypeError(\"Private method is not writable\");\n  if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a setter\");\n  if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot write private member to an object whose class did not declare it\");\n  return (kind === \"a\" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value)), value;\n}\n\nexport function __classPrivateFieldIn(state, receiver) {\n  if (receiver === null || (typeof receiver !== \"object\" && typeof receiver !== \"function\")) throw new TypeError(\"Cannot use 'in' operator on non-object\");\n  return typeof state === \"function\" ? receiver === state : state.has(receiver);\n}\n\nexport function __addDisposableResource(env, value, async) {\n  if (value !== null && value !== void 0) {\n    if (typeof value !== \"object\" && typeof value !== \"function\") throw new TypeError(\"Object expected.\");\n    var dispose, inner;\n    if (async) {\n      if (!Symbol.asyncDispose) throw new TypeError(\"Symbol.asyncDispose is not defined.\");\n      dispose = value[Symbol.asyncDispose];\n    }\n    if (dispose === void 0) {\n      if (!Symbol.dispose) throw new TypeError(\"Symbol.dispose is not defined.\");\n      dispose = value[Symbol.dispose];\n      if (async) inner = dispose;\n    }\n    if (typeof dispose !== \"function\") throw new TypeError(\"Object not disposable.\");\n    if (inner) dispose = function() { try { inner.call(this); } catch (e) { return Promise.reject(e); } };\n    env.stack.push({ value: value, dispose: dispose, async: async });\n  }\n  else if (async) {\n    env.stack.push({ async: true });\n  }\n  return value;\n}\n\nvar _SuppressedError = typeof SuppressedError === \"function\" ? SuppressedError : function (error, suppressed, message) {\n  var e = new Error(message);\n  return e.name = \"SuppressedError\", e.error = error, e.suppressed = suppressed, e;\n};\n\nexport function __disposeResources(env) {\n  function fail(e) {\n    env.error = env.hasError ? new _SuppressedError(e, env.error, \"An error was suppressed during disposal.\") : e;\n    env.hasError = true;\n  }\n  var r, s = 0;\n  function next() {\n    while (r = env.stack.pop()) {\n      try {\n        if (!r.async && s === 1) return s = 0, env.stack.push(r), Promise.resolve().then(next);\n        if (r.dispose) {\n          var result = r.dispose.call(r.value);\n          if (r.async) return s |= 2, Promise.resolve(result).then(next, function(e) { fail(e); return next(); });\n        }\n        else s |= 1;\n      }\n      catch (e) {\n        fail(e);\n      }\n    }\n    if (s === 1) return env.hasError ? Promise.reject(env.error) : Promise.resolve();\n    if (env.hasError) throw env.error;\n  }\n  return next();\n}\n\nexport function __rewriteRelativeImportExtension(path, preserveJsx) {\n  if (typeof path === \"string\" && /^\\.\\.?\\//.test(path)) {\n      return path.replace(/\\.(tsx)$|((?:\\.d)?)((?:\\.[^./]+?)?)\\.([cm]?)ts$/i, function (m, tsx, d, ext, cm) {\n          return tsx ? preserveJsx ? \".jsx\" : \".js\" : d && (!ext || !cm) ? m : (d + ext + \".\" + cm.toLowerCase() + \"js\");\n      });\n  }\n  return path;\n}\n\nexport default {\n  __extends,\n  __assign,\n  __rest,\n  __decorate,\n  __param,\n  __esDecorate,\n  __runInitializers,\n  __propKey,\n  __setFunctionName,\n  __metadata,\n  __awaiter,\n  __generator,\n  __createBinding,\n  __exportStar,\n  __values,\n  __read,\n  __spread,\n  __spreadArrays,\n  __spreadArray,\n  __await,\n  __asyncGenerator,\n  __asyncDelegator,\n  __asyncValues,\n  __makeTemplateObject,\n  __importStar,\n  __importDefault,\n  __classPrivateFieldGet,\n  __classPrivateFieldSet,\n  __classPrivateFieldIn,\n  __addDisposableResource,\n  __disposeResources,\n  __rewriteRelativeImportExtension,\n};\n", "export var zeroRightClassName = 'right-scroll-bar-position';\nexport var fullWidthClassName = 'width-before-scroll-bar';\nexport var noScrollbarsClassName = 'with-scroll-bars-hidden';\n/**\n * Name of a CSS variable containing the amount of \"hidden\" scrollbar\n * ! might be undefined ! use will fallback!\n */\nexport var removedBarSizeVariable = '--removed-body-scroll-bar-size';\n", "/**\n * Assigns a value for a given ref, no matter of the ref format\n * @param {RefObject} ref - a callback function or ref object\n * @param value - a new value\n *\n * @see https://github.com/theKashey/use-callback-ref#assignref\n * @example\n * const refObject = useRef();\n * const refFn = (ref) => {....}\n *\n * assignRef(refObject, \"refValue\");\n * assignRef(refFn, \"refValue\");\n */\nexport function assignRef(ref, value) {\n    if (typeof ref === 'function') {\n        ref(value);\n    }\n    else if (ref) {\n        ref.current = value;\n    }\n    return ref;\n}\n", "import { useState } from 'react';\n/**\n * creates a MutableRef with ref change callback\n * @param initialValue - initial ref value\n * @param {Function} callback - a callback to run when value changes\n *\n * @example\n * const ref = useCallbackRef(0, (newValue, oldValue) => console.log(oldValue, '->', newValue);\n * ref.current = 1;\n * // prints 0 -> 1\n *\n * @see https://reactjs.org/docs/hooks-reference.html#useref\n * @see https://github.com/theKashey/use-callback-ref#usecallbackref---to-replace-reactuseref\n * @returns {MutableRefObject}\n */\nexport function useCallbackRef(initialValue, callback) {\n    var ref = useState(function () { return ({\n        // value\n        value: initialValue,\n        // last callback\n        callback: callback,\n        // \"memoized\" public interface\n        facade: {\n            get current() {\n                return ref.value;\n            },\n            set current(value) {\n                var last = ref.value;\n                if (last !== value) {\n                    ref.value = value;\n                    ref.callback(value, last);\n                }\n            },\n        },\n    }); })[0];\n    // update callback\n    ref.callback = callback;\n    return ref.facade;\n}\n", "import * as React from 'react';\nimport { assignRef } from './assignRef';\nimport { useCallbackRef } from './useRef';\nvar useIsomorphicLayoutEffect = typeof window !== 'undefined' ? React.useLayoutEffect : React.useEffect;\nvar currentValues = new WeakMap();\n/**\n * Merges two or more refs together providing a single interface to set their value\n * @param {RefObject|Ref} refs\n * @returns {MutableRefObject} - a new ref, which translates all changes to {refs}\n *\n * @see {@link mergeRefs} a version without buit-in memoization\n * @see https://github.com/theKashey/use-callback-ref#usemergerefs\n * @example\n * const Component = React.forwardRef((props, ref) => {\n *   const ownRef = useRef();\n *   const domRef = useMergeRefs([ref, ownRef]); // 👈 merge together\n *   return <div ref={domRef}>...</div>\n * }\n */\nexport function useMergeRefs(refs, defaultValue) {\n    var callbackRef = useCallbackRef(defaultValue || null, function (newValue) {\n        return refs.forEach(function (ref) { return assignRef(ref, newValue); });\n    });\n    // handle refs changes - added or removed\n    useIsomorphicLayoutEffect(function () {\n        var oldValue = currentValues.get(callbackRef);\n        if (oldValue) {\n            var prevRefs_1 = new Set(oldValue);\n            var nextRefs_1 = new Set(refs);\n            var current_1 = callbackRef.current;\n            prevRefs_1.forEach(function (ref) {\n                if (!nextRefs_1.has(ref)) {\n                    assignRef(ref, null);\n                }\n            });\n            nextRefs_1.forEach(function (ref) {\n                if (!prevRefs_1.has(ref)) {\n                    assignRef(ref, current_1);\n                }\n            });\n        }\n        currentValues.set(callbackRef, refs);\n    }, [refs]);\n    return callbackRef;\n}\n", "import { __assign } from \"tslib\";\nfunction ItoI(a) {\n    return a;\n}\nfunction innerCreateMedium(defaults, middleware) {\n    if (middleware === void 0) { middleware = ItoI; }\n    var buffer = [];\n    var assigned = false;\n    var medium = {\n        read: function () {\n            if (assigned) {\n                throw new Error('Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.');\n            }\n            if (buffer.length) {\n                return buffer[buffer.length - 1];\n            }\n            return defaults;\n        },\n        useMedium: function (data) {\n            var item = middleware(data, assigned);\n            buffer.push(item);\n            return function () {\n                buffer = buffer.filter(function (x) { return x !== item; });\n            };\n        },\n        assignSyncMedium: function (cb) {\n            assigned = true;\n            while (buffer.length) {\n                var cbs = buffer;\n                buffer = [];\n                cbs.forEach(cb);\n            }\n            buffer = {\n                push: function (x) { return cb(x); },\n                filter: function () { return buffer; },\n            };\n        },\n        assignMedium: function (cb) {\n            assigned = true;\n            var pendingQueue = [];\n            if (buffer.length) {\n                var cbs = buffer;\n                buffer = [];\n                cbs.forEach(cb);\n                pendingQueue = buffer;\n            }\n            var executeQueue = function () {\n                var cbs = pendingQueue;\n                pendingQueue = [];\n                cbs.forEach(cb);\n            };\n            var cycle = function () { return Promise.resolve().then(executeQueue); };\n            cycle();\n            buffer = {\n                push: function (x) {\n                    pendingQueue.push(x);\n                    cycle();\n                },\n                filter: function (filter) {\n                    pendingQueue = pendingQueue.filter(filter);\n                    return buffer;\n                },\n            };\n        },\n    };\n    return medium;\n}\nexport function createMedium(defaults, middleware) {\n    if (middleware === void 0) { middleware = ItoI; }\n    return innerCreateMedium(defaults, middleware);\n}\n// eslint-disable-next-line @typescript-eslint/ban-types\nexport function createSidecarMedium(options) {\n    if (options === void 0) { options = {}; }\n    var medium = innerCreateMedium(null);\n    medium.options = __assign({ async: true, ssr: false }, options);\n    return medium;\n}\n", "import { createSidecarMedium } from 'use-sidecar';\nexport var effectCar = createSidecarMedium();\n", "import { __assign, __rest } from \"tslib\";\nimport * as React from 'react';\nimport { fullWidthClassName, zeroRightClassName } from 'react-remove-scroll-bar/constants';\nimport { useMergeRefs } from 'use-callback-ref';\nimport { effectCar } from './medium';\nvar nothing = function () {\n    return;\n};\n/**\n * Removes scrollbar from the page and contain the scroll within the Lock\n */\nvar RemoveScroll = React.forwardRef(function (props, parentRef) {\n    var ref = React.useRef(null);\n    var _a = React.useState({\n        onScrollCapture: nothing,\n        onWheelCapture: nothing,\n        onTouchMoveCapture: nothing,\n    }), callbacks = _a[0], setCallbacks = _a[1];\n    var forwardProps = props.forwardProps, children = props.children, className = props.className, removeScrollBar = props.removeScrollBar, enabled = props.enabled, shards = props.shards, sideCar = props.sideCar, noRelative = props.noRelative, noIsolation = props.noIsolation, inert = props.inert, allowPinchZoom = props.allowPinchZoom, _b = props.as, Container = _b === void 0 ? 'div' : _b, gapMode = props.gapMode, rest = __rest(props, [\"forwardProps\", \"children\", \"className\", \"removeScrollBar\", \"enabled\", \"shards\", \"sideCar\", \"noRelative\", \"noIsolation\", \"inert\", \"allowPinchZoom\", \"as\", \"gapMode\"]);\n    var SideCar = sideCar;\n    var containerRef = useMergeRefs([ref, parentRef]);\n    var containerProps = __assign(__assign({}, rest), callbacks);\n    return (React.createElement(React.Fragment, null,\n        enabled && (React.createElement(SideCar, { sideCar: effectCar, removeScrollBar: removeScrollBar, shards: shards, noRelative: noRelative, noIsolation: noIsolation, inert: inert, setCallbacks: setCallbacks, allowPinchZoom: !!allowPinchZoom, lockRef: ref, gapMode: gapMode })),\n        forwardProps ? (React.cloneElement(React.Children.only(children), __assign(__assign({}, containerProps), { ref: containerRef }))) : (React.createElement(Container, __assign({}, containerProps, { className: className, ref: containerRef }), children))));\n});\nRemoveScroll.defaultProps = {\n    enabled: true,\n    removeScrollBar: true,\n    inert: false,\n};\nRemoveScroll.classNames = {\n    fullWidth: fullWidthClassName,\n    zeroRight: zeroRightClassName,\n};\nexport { RemoveScroll };\n", "import { __assign, __rest } from \"tslib\";\nimport * as React from 'react';\nvar SideCar = function (_a) {\n    var sideCar = _a.sideCar, rest = __rest(_a, [\"sideCar\"]);\n    if (!sideCar) {\n        throw new Error('Sidecar: please provide `sideCar` property to import the right car');\n    }\n    var Target = sideCar.read();\n    if (!Target) {\n        throw new Error('Sidecar medium not found');\n    }\n    return React.createElement(Target, __assign({}, rest));\n};\nSideCar.isSideCarExport = true;\nexport function exportSidecar(medium, exported) {\n    medium.useMedium(exported);\n    return SideCar;\n}\n", "var currentNonce;\nexport var setNonce = function (nonce) {\n    currentNonce = nonce;\n};\nexport var getNonce = function () {\n    if (currentNonce) {\n        return currentNonce;\n    }\n    if (typeof __webpack_nonce__ !== 'undefined') {\n        return __webpack_nonce__;\n    }\n    return undefined;\n};\n", "import { getNonce } from 'get-nonce';\nfunction makeStyleTag() {\n    if (!document)\n        return null;\n    var tag = document.createElement('style');\n    tag.type = 'text/css';\n    var nonce = getNonce();\n    if (nonce) {\n        tag.setAttribute('nonce', nonce);\n    }\n    return tag;\n}\nfunction injectStyles(tag, css) {\n    // @ts-ignore\n    if (tag.styleSheet) {\n        // @ts-ignore\n        tag.styleSheet.cssText = css;\n    }\n    else {\n        tag.appendChild(document.createTextNode(css));\n    }\n}\nfunction insertStyleTag(tag) {\n    var head = document.head || document.getElementsByTagName('head')[0];\n    head.appendChild(tag);\n}\nexport var stylesheetSingleton = function () {\n    var counter = 0;\n    var stylesheet = null;\n    return {\n        add: function (style) {\n            if (counter == 0) {\n                if ((stylesheet = makeStyleTag())) {\n                    injectStyles(stylesheet, style);\n                    insertStyleTag(stylesheet);\n                }\n            }\n            counter++;\n        },\n        remove: function () {\n            counter--;\n            if (!counter && stylesheet) {\n                stylesheet.parentNode && stylesheet.parentNode.removeChild(stylesheet);\n                stylesheet = null;\n            }\n        },\n    };\n};\n", "import * as React from 'react';\nimport { stylesheetSingleton } from './singleton';\n/**\n * creates a hook to control style singleton\n * @see {@link styleSingleton} for a safer component version\n * @example\n * ```tsx\n * const useStyle = styleHookSingleton();\n * ///\n * useStyle('body { overflow: hidden}');\n */\nexport var styleHookSingleton = function () {\n    var sheet = stylesheetSingleton();\n    return function (styles, isDynamic) {\n        React.useEffect(function () {\n            sheet.add(styles);\n            return function () {\n                sheet.remove();\n            };\n        }, [styles && isDynamic]);\n    };\n};\n", "import { styleHook<PERSON>ingleton } from './hook';\n/**\n * create a Component to add styles on demand\n * - styles are added when first instance is mounted\n * - styles are removed when the last instance is unmounted\n * - changing styles in runtime does nothing unless dynamic is set. But with multiple components that can lead to the undefined behavior\n */\nexport var styleSingleton = function () {\n    var useStyle = styleHookSingleton();\n    var Sheet = function (_a) {\n        var styles = _a.styles, dynamic = _a.dynamic;\n        useStyle(styles, dynamic);\n        return null;\n    };\n    return Sheet;\n};\n", "export { styleSingleton } from './component';\nexport { stylesheetSingleton } from './singleton';\nexport { styleHookSingleton } from './hook';\n", "export var zeroGap = {\n    left: 0,\n    top: 0,\n    right: 0,\n    gap: 0,\n};\nvar parse = function (x) { return parseInt(x || '', 10) || 0; };\nvar getOffset = function (gapMode) {\n    var cs = window.getComputedStyle(document.body);\n    var left = cs[gapMode === 'padding' ? 'paddingLeft' : 'marginLeft'];\n    var top = cs[gapMode === 'padding' ? 'paddingTop' : 'marginTop'];\n    var right = cs[gapMode === 'padding' ? 'paddingRight' : 'marginRight'];\n    return [parse(left), parse(top), parse(right)];\n};\nexport var getGapWidth = function (gapMode) {\n    if (gapMode === void 0) { gapMode = 'margin'; }\n    if (typeof window === 'undefined') {\n        return zeroGap;\n    }\n    var offsets = getOffset(gapMode);\n    var documentWidth = document.documentElement.clientWidth;\n    var windowWidth = window.innerWidth;\n    return {\n        left: offsets[0],\n        top: offsets[1],\n        right: offsets[2],\n        gap: Math.max(0, windowWidth - documentWidth + offsets[2] - offsets[0]),\n    };\n};\n", "import * as React from 'react';\nimport { styleSingleton } from 'react-style-singleton';\nimport { fullWidthClassName, zeroRightClassName, noScrollbarsClassName, removedBarSizeVariable } from './constants';\nimport { getGapWidth } from './utils';\nvar Style = styleSingleton();\nexport var lockAttribute = 'data-scroll-locked';\n// important tip - once we measure scrollBar width and remove them\n// we could not repeat this operation\n// thus we are using style-singleton - only the first \"yet correct\" style will be applied.\nvar getStyles = function (_a, allowRelative, gapMode, important) {\n    var left = _a.left, top = _a.top, right = _a.right, gap = _a.gap;\n    if (gapMode === void 0) { gapMode = 'margin'; }\n    return \"\\n  .\".concat(noScrollbarsClassName, \" {\\n   overflow: hidden \").concat(important, \";\\n   padding-right: \").concat(gap, \"px \").concat(important, \";\\n  }\\n  body[\").concat(lockAttribute, \"] {\\n    overflow: hidden \").concat(important, \";\\n    overscroll-behavior: contain;\\n    \").concat([\n        allowRelative && \"position: relative \".concat(important, \";\"),\n        gapMode === 'margin' &&\n            \"\\n    padding-left: \".concat(left, \"px;\\n    padding-top: \").concat(top, \"px;\\n    padding-right: \").concat(right, \"px;\\n    margin-left:0;\\n    margin-top:0;\\n    margin-right: \").concat(gap, \"px \").concat(important, \";\\n    \"),\n        gapMode === 'padding' && \"padding-right: \".concat(gap, \"px \").concat(important, \";\"),\n    ]\n        .filter(Boolean)\n        .join(''), \"\\n  }\\n  \\n  .\").concat(zeroRightClassName, \" {\\n    right: \").concat(gap, \"px \").concat(important, \";\\n  }\\n  \\n  .\").concat(fullWidthClassName, \" {\\n    margin-right: \").concat(gap, \"px \").concat(important, \";\\n  }\\n  \\n  .\").concat(zeroRightClassName, \" .\").concat(zeroRightClassName, \" {\\n    right: 0 \").concat(important, \";\\n  }\\n  \\n  .\").concat(fullWidthClassName, \" .\").concat(fullWidthClassName, \" {\\n    margin-right: 0 \").concat(important, \";\\n  }\\n  \\n  body[\").concat(lockAttribute, \"] {\\n    \").concat(removedBarSizeVariable, \": \").concat(gap, \"px;\\n  }\\n\");\n};\nvar getCurrentUseCounter = function () {\n    var counter = parseInt(document.body.getAttribute(lockAttribute) || '0', 10);\n    return isFinite(counter) ? counter : 0;\n};\nexport var useLockAttribute = function () {\n    React.useEffect(function () {\n        document.body.setAttribute(lockAttribute, (getCurrentUseCounter() + 1).toString());\n        return function () {\n            var newCounter = getCurrentUseCounter() - 1;\n            if (newCounter <= 0) {\n                document.body.removeAttribute(lockAttribute);\n            }\n            else {\n                document.body.setAttribute(lockAttribute, newCounter.toString());\n            }\n        };\n    }, []);\n};\n/**\n * Removes page scrollbar and blocks page scroll when mounted\n */\nexport var RemoveScrollBar = function (_a) {\n    var noRelative = _a.noRelative, noImportant = _a.noImportant, _b = _a.gapMode, gapMode = _b === void 0 ? 'margin' : _b;\n    useLockAttribute();\n    /*\n     gap will be measured on every component mount\n     however it will be used only by the \"first\" invocation\n     due to singleton nature of <Style\n     */\n    var gap = React.useMemo(function () { return getGapWidth(gapMode); }, [gapMode]);\n    return React.createElement(Style, { styles: getStyles(gap, !noRelative, gapMode, !noImportant ? '!important' : '') });\n};\n", "import { RemoveScrollBar } from './component';\nimport { zeroRightClassName, fullWidthClassName, noScrollbarsClassName, removedBarSizeVariable } from './constants';\nimport { getGapWidth } from './utils';\nexport { RemoveScrollBar, zeroRightClassName, fullWidthClassName, noScrollbarsClassName, removedBarSizeVariable, getGapWidth, };\n", "var passiveSupported = false;\nif (typeof window !== 'undefined') {\n    try {\n        var options = Object.defineProperty({}, 'passive', {\n            get: function () {\n                passiveSupported = true;\n                return true;\n            },\n        });\n        // @ts-ignore\n        window.addEventListener('test', options, options);\n        // @ts-ignore\n        window.removeEventListener('test', options, options);\n    }\n    catch (err) {\n        passiveSupported = false;\n    }\n}\nexport var nonPassive = passiveSupported ? { passive: false } : false;\n", "var alwaysContainsScroll = function (node) {\n    // textarea will always _contain_ scroll inside self. It only can be hidden\n    return node.tagName === 'TEXTAREA';\n};\nvar elementCanBeScrolled = function (node, overflow) {\n    if (!(node instanceof Element)) {\n        return false;\n    }\n    var styles = window.getComputedStyle(node);\n    return (\n    // not-not-scrollable\n    styles[overflow] !== 'hidden' &&\n        // contains scroll inside self\n        !(styles.overflowY === styles.overflowX && !alwaysContainsScroll(node) && styles[overflow] === 'visible'));\n};\nvar elementCouldBeVScrolled = function (node) { return elementCanBeScrolled(node, 'overflowY'); };\nvar elementCouldBeHScrolled = function (node) { return elementCanBeScrolled(node, 'overflowX'); };\nexport var locationCouldBeScrolled = function (axis, node) {\n    var ownerDocument = node.ownerDocument;\n    var current = node;\n    do {\n        // Skip over shadow root\n        if (typeof ShadowRoot !== 'undefined' && current instanceof ShadowRoot) {\n            current = current.host;\n        }\n        var isScrollable = elementCouldBeScrolled(axis, current);\n        if (isScrollable) {\n            var _a = getScrollVariables(axis, current), scrollHeight = _a[1], clientHeight = _a[2];\n            if (scrollHeight > clientHeight) {\n                return true;\n            }\n        }\n        current = current.parentNode;\n    } while (current && current !== ownerDocument.body);\n    return false;\n};\nvar getVScrollVariables = function (_a) {\n    var scrollTop = _a.scrollTop, scrollHeight = _a.scrollHeight, clientHeight = _a.clientHeight;\n    return [\n        scrollTop,\n        scrollHeight,\n        clientHeight,\n    ];\n};\nvar getHScrollVariables = function (_a) {\n    var scrollLeft = _a.scrollLeft, scrollWidth = _a.scrollWidth, clientWidth = _a.clientWidth;\n    return [\n        scrollLeft,\n        scrollWidth,\n        clientWidth,\n    ];\n};\nvar elementCouldBeScrolled = function (axis, node) {\n    return axis === 'v' ? elementCouldBeVScrolled(node) : elementCouldBeHScrolled(node);\n};\nvar getScrollVariables = function (axis, node) {\n    return axis === 'v' ? getVScrollVariables(node) : getHScrollVariables(node);\n};\nvar getDirectionFactor = function (axis, direction) {\n    /**\n     * If the element's direction is rtl (right-to-left), then scrollLeft is 0 when the scrollbar is at its rightmost position,\n     * and then increasingly negative as you scroll towards the end of the content.\n     * @see https://developer.mozilla.org/en-US/docs/Web/API/Element/scrollLeft\n     */\n    return axis === 'h' && direction === 'rtl' ? -1 : 1;\n};\nexport var handleScroll = function (axis, endTarget, event, sourceDelta, noOverscroll) {\n    var directionFactor = getDirectionFactor(axis, window.getComputedStyle(endTarget).direction);\n    var delta = directionFactor * sourceDelta;\n    // find scrollable target\n    var target = event.target;\n    var targetInLock = endTarget.contains(target);\n    var shouldCancelScroll = false;\n    var isDeltaPositive = delta > 0;\n    var availableScroll = 0;\n    var availableScrollTop = 0;\n    do {\n        if (!target) {\n            break;\n        }\n        var _a = getScrollVariables(axis, target), position = _a[0], scroll_1 = _a[1], capacity = _a[2];\n        var elementScroll = scroll_1 - capacity - directionFactor * position;\n        if (position || elementScroll) {\n            if (elementCouldBeScrolled(axis, target)) {\n                availableScroll += elementScroll;\n                availableScrollTop += position;\n            }\n        }\n        var parent_1 = target.parentNode;\n        // we will \"bubble\" from ShadowDom in case we are, or just to the parent in normal case\n        // this is the same logic used in focus-lock\n        target = (parent_1 && parent_1.nodeType === Node.DOCUMENT_FRAGMENT_NODE ? parent_1.host : parent_1);\n    } while (\n    // portaled content\n    (!targetInLock && target !== document.body) ||\n        // self content\n        (targetInLock && (endTarget.contains(target) || endTarget === target)));\n    // handle epsilon around 0 (non standard zoom levels)\n    if (isDeltaPositive &&\n        ((noOverscroll && Math.abs(availableScroll) < 1) || (!noOverscroll && delta > availableScroll))) {\n        shouldCancelScroll = true;\n    }\n    else if (!isDeltaPositive &&\n        ((noOverscroll && Math.abs(availableScrollTop) < 1) || (!noOverscroll && -delta > availableScrollTop))) {\n        shouldCancelScroll = true;\n    }\n    return shouldCancelScroll;\n};\n", "import { __spreadArray } from \"tslib\";\nimport * as React from 'react';\nimport { RemoveScrollBar } from 'react-remove-scroll-bar';\nimport { styleSingleton } from 'react-style-singleton';\nimport { nonPassive } from './aggresiveCapture';\nimport { handleScroll, locationCouldBeScrolled } from './handleScroll';\nexport var getTouchXY = function (event) {\n    return 'changedTouches' in event ? [event.changedTouches[0].clientX, event.changedTouches[0].clientY] : [0, 0];\n};\nexport var getDeltaXY = function (event) { return [event.deltaX, event.deltaY]; };\nvar extractRef = function (ref) {\n    return ref && 'current' in ref ? ref.current : ref;\n};\nvar deltaCompare = function (x, y) { return x[0] === y[0] && x[1] === y[1]; };\nvar generateStyle = function (id) { return \"\\n  .block-interactivity-\".concat(id, \" {pointer-events: none;}\\n  .allow-interactivity-\").concat(id, \" {pointer-events: all;}\\n\"); };\nvar idCounter = 0;\nvar lockStack = [];\nexport function RemoveScrollSideCar(props) {\n    var shouldPreventQueue = React.useRef([]);\n    var touchStartRef = React.useRef([0, 0]);\n    var activeAxis = React.useRef();\n    var id = React.useState(idCounter++)[0];\n    var Style = React.useState(styleSingleton)[0];\n    var lastProps = React.useRef(props);\n    React.useEffect(function () {\n        lastProps.current = props;\n    }, [props]);\n    React.useEffect(function () {\n        if (props.inert) {\n            document.body.classList.add(\"block-interactivity-\".concat(id));\n            var allow_1 = __spreadArray([props.lockRef.current], (props.shards || []).map(extractRef), true).filter(Boolean);\n            allow_1.forEach(function (el) { return el.classList.add(\"allow-interactivity-\".concat(id)); });\n            return function () {\n                document.body.classList.remove(\"block-interactivity-\".concat(id));\n                allow_1.forEach(function (el) { return el.classList.remove(\"allow-interactivity-\".concat(id)); });\n            };\n        }\n        return;\n    }, [props.inert, props.lockRef.current, props.shards]);\n    var shouldCancelEvent = React.useCallback(function (event, parent) {\n        if (('touches' in event && event.touches.length === 2) || (event.type === 'wheel' && event.ctrlKey)) {\n            return !lastProps.current.allowPinchZoom;\n        }\n        var touch = getTouchXY(event);\n        var touchStart = touchStartRef.current;\n        var deltaX = 'deltaX' in event ? event.deltaX : touchStart[0] - touch[0];\n        var deltaY = 'deltaY' in event ? event.deltaY : touchStart[1] - touch[1];\n        var currentAxis;\n        var target = event.target;\n        var moveDirection = Math.abs(deltaX) > Math.abs(deltaY) ? 'h' : 'v';\n        // allow horizontal touch move on Range inputs. They will not cause any scroll\n        if ('touches' in event && moveDirection === 'h' && target.type === 'range') {\n            return false;\n        }\n        var canBeScrolledInMainDirection = locationCouldBeScrolled(moveDirection, target);\n        if (!canBeScrolledInMainDirection) {\n            return true;\n        }\n        if (canBeScrolledInMainDirection) {\n            currentAxis = moveDirection;\n        }\n        else {\n            currentAxis = moveDirection === 'v' ? 'h' : 'v';\n            canBeScrolledInMainDirection = locationCouldBeScrolled(moveDirection, target);\n            // other axis might be not scrollable\n        }\n        if (!canBeScrolledInMainDirection) {\n            return false;\n        }\n        if (!activeAxis.current && 'changedTouches' in event && (deltaX || deltaY)) {\n            activeAxis.current = currentAxis;\n        }\n        if (!currentAxis) {\n            return true;\n        }\n        var cancelingAxis = activeAxis.current || currentAxis;\n        return handleScroll(cancelingAxis, parent, event, cancelingAxis === 'h' ? deltaX : deltaY, true);\n    }, []);\n    var shouldPrevent = React.useCallback(function (_event) {\n        var event = _event;\n        if (!lockStack.length || lockStack[lockStack.length - 1] !== Style) {\n            // not the last active\n            return;\n        }\n        var delta = 'deltaY' in event ? getDeltaXY(event) : getTouchXY(event);\n        var sourceEvent = shouldPreventQueue.current.filter(function (e) { return e.name === event.type && (e.target === event.target || event.target === e.shadowParent) && deltaCompare(e.delta, delta); })[0];\n        // self event, and should be canceled\n        if (sourceEvent && sourceEvent.should) {\n            if (event.cancelable) {\n                event.preventDefault();\n            }\n            return;\n        }\n        // outside or shard event\n        if (!sourceEvent) {\n            var shardNodes = (lastProps.current.shards || [])\n                .map(extractRef)\n                .filter(Boolean)\n                .filter(function (node) { return node.contains(event.target); });\n            var shouldStop = shardNodes.length > 0 ? shouldCancelEvent(event, shardNodes[0]) : !lastProps.current.noIsolation;\n            if (shouldStop) {\n                if (event.cancelable) {\n                    event.preventDefault();\n                }\n            }\n        }\n    }, []);\n    var shouldCancel = React.useCallback(function (name, delta, target, should) {\n        var event = { name: name, delta: delta, target: target, should: should, shadowParent: getOutermostShadowParent(target) };\n        shouldPreventQueue.current.push(event);\n        setTimeout(function () {\n            shouldPreventQueue.current = shouldPreventQueue.current.filter(function (e) { return e !== event; });\n        }, 1);\n    }, []);\n    var scrollTouchStart = React.useCallback(function (event) {\n        touchStartRef.current = getTouchXY(event);\n        activeAxis.current = undefined;\n    }, []);\n    var scrollWheel = React.useCallback(function (event) {\n        shouldCancel(event.type, getDeltaXY(event), event.target, shouldCancelEvent(event, props.lockRef.current));\n    }, []);\n    var scrollTouchMove = React.useCallback(function (event) {\n        shouldCancel(event.type, getTouchXY(event), event.target, shouldCancelEvent(event, props.lockRef.current));\n    }, []);\n    React.useEffect(function () {\n        lockStack.push(Style);\n        props.setCallbacks({\n            onScrollCapture: scrollWheel,\n            onWheelCapture: scrollWheel,\n            onTouchMoveCapture: scrollTouchMove,\n        });\n        document.addEventListener('wheel', shouldPrevent, nonPassive);\n        document.addEventListener('touchmove', shouldPrevent, nonPassive);\n        document.addEventListener('touchstart', scrollTouchStart, nonPassive);\n        return function () {\n            lockStack = lockStack.filter(function (inst) { return inst !== Style; });\n            document.removeEventListener('wheel', shouldPrevent, nonPassive);\n            document.removeEventListener('touchmove', shouldPrevent, nonPassive);\n            document.removeEventListener('touchstart', scrollTouchStart, nonPassive);\n        };\n    }, []);\n    var removeScrollBar = props.removeScrollBar, inert = props.inert;\n    return (React.createElement(React.Fragment, null,\n        inert ? React.createElement(Style, { styles: generateStyle(id) }) : null,\n        removeScrollBar ? React.createElement(RemoveScrollBar, { noRelative: props.noRelative, gapMode: props.gapMode }) : null));\n}\nfunction getOutermostShadowParent(node) {\n    var shadowParent = null;\n    while (node !== null) {\n        if (node instanceof ShadowRoot) {\n            shadowParent = node.host;\n            node = node.host;\n        }\n        node = node.parentNode;\n    }\n    return shadowParent;\n}\n", "import { exportSidecar } from 'use-sidecar';\nimport { RemoveScrollSideCar } from './SideEffect';\nimport { effectCar } from './medium';\nexport default exportSidecar(effectCar, RemoveScrollSideCar);\n", "import { __assign } from \"tslib\";\nimport * as React from 'react';\nimport { RemoveScroll } from './UI';\nimport SideCar from './sidecar';\nvar ReactRemoveScroll = React.forwardRef(function (props, ref) { return (React.createElement(RemoveScroll, __assign({}, props, { ref: ref, sideCar: SideCar }))); });\nReactRemoveScroll.classNames = RemoveScroll.classNames;\nexport default ReactRemoveScroll;\n", "// packages/core/primitive/src/primitive.tsx\nfunction composeEventHandlers(originalEventHandler, ourEventHandler, { checkForDefaultPrevented = true } = {}) {\n  return function handleEvent(event) {\n    originalEventHandler?.(event);\n    if (checkForDefaultPrevented === false || !event.defaultPrevented) {\n      return ourEventHandler?.(event);\n    }\n  };\n}\nexport {\n  composeEventHandlers\n};\n//# sourceMappingURL=index.mjs.map\n", "// packages/react/compose-refs/src/compose-refs.tsx\nimport * as React from \"react\";\nfunction setRef(ref, value) {\n  if (typeof ref === \"function\") {\n    return ref(value);\n  } else if (ref !== null && ref !== void 0) {\n    ref.current = value;\n  }\n}\nfunction composeRefs(...refs) {\n  return (node) => {\n    let hasCleanup = false;\n    const cleanups = refs.map((ref) => {\n      const cleanup = setRef(ref, node);\n      if (!hasCleanup && typeof cleanup == \"function\") {\n        hasCleanup = true;\n      }\n      return cleanup;\n    });\n    if (hasCleanup) {\n      return () => {\n        for (let i = 0; i < cleanups.length; i++) {\n          const cleanup = cleanups[i];\n          if (typeof cleanup == \"function\") {\n            cleanup();\n          } else {\n            setRef(refs[i], null);\n          }\n        }\n      };\n    }\n  };\n}\nfunction useComposedRefs(...refs) {\n  return React.useCallback(composeRefs(...refs), refs);\n}\nexport {\n  composeRefs,\n  useComposedRefs\n};\n//# sourceMappingURL=index.mjs.map\n", "// packages/react/context/src/create-context.tsx\nimport * as React from \"react\";\nimport { jsx } from \"react/jsx-runtime\";\nfunction createContext2(rootComponentName, defaultContext) {\n  const Context = React.createContext(defaultContext);\n  const Provider = (props) => {\n    const { children, ...context } = props;\n    const value = React.useMemo(() => context, Object.values(context));\n    return /* @__PURE__ */ jsx(Context.Provider, { value, children });\n  };\n  Provider.displayName = rootComponentName + \"Provider\";\n  function useContext2(consumerName) {\n    const context = React.useContext(Context);\n    if (context) return context;\n    if (defaultContext !== void 0) return defaultContext;\n    throw new Error(`\\`${consumerName}\\` must be used within \\`${rootComponentName}\\``);\n  }\n  return [Provider, useContext2];\n}\nfunction createContextScope(scopeName, createContextScopeDeps = []) {\n  let defaultContexts = [];\n  function createContext3(rootComponentName, defaultContext) {\n    const BaseContext = React.createContext(defaultContext);\n    const index = defaultContexts.length;\n    defaultContexts = [...defaultContexts, defaultContext];\n    const Provider = (props) => {\n      const { scope, children, ...context } = props;\n      const Context = scope?.[scopeName]?.[index] || BaseContext;\n      const value = React.useMemo(() => context, Object.values(context));\n      return /* @__PURE__ */ jsx(Context.Provider, { value, children });\n    };\n    Provider.displayName = rootComponentName + \"Provider\";\n    function useContext2(consumerName, scope) {\n      const Context = scope?.[scopeName]?.[index] || BaseContext;\n      const context = React.useContext(Context);\n      if (context) return context;\n      if (defaultContext !== void 0) return defaultContext;\n      throw new Error(`\\`${consumerName}\\` must be used within \\`${rootComponentName}\\``);\n    }\n    return [Provider, useContext2];\n  }\n  const createScope = () => {\n    const scopeContexts = defaultContexts.map((defaultContext) => {\n      return React.createContext(defaultContext);\n    });\n    return function useScope(scope) {\n      const contexts = scope?.[scopeName] || scopeContexts;\n      return React.useMemo(\n        () => ({ [`__scope${scopeName}`]: { ...scope, [scopeName]: contexts } }),\n        [scope, contexts]\n      );\n    };\n  };\n  createScope.scopeName = scopeName;\n  return [createContext3, composeContextScopes(createScope, ...createContextScopeDeps)];\n}\nfunction composeContextScopes(...scopes) {\n  const baseScope = scopes[0];\n  if (scopes.length === 1) return baseScope;\n  const createScope = () => {\n    const scopeHooks = scopes.map((createScope2) => ({\n      useScope: createScope2(),\n      scopeName: createScope2.scopeName\n    }));\n    return function useComposedScopes(overrideScopes) {\n      const nextScopes = scopeHooks.reduce((nextScopes2, { useScope, scopeName }) => {\n        const scopeProps = useScope(overrideScopes);\n        const currentScope = scopeProps[`__scope${scopeName}`];\n        return { ...nextScopes2, ...currentScope };\n      }, {});\n      return React.useMemo(() => ({ [`__scope${baseScope.scopeName}`]: nextScopes }), [nextScopes]);\n    };\n  };\n  createScope.scopeName = baseScope.scopeName;\n  return createScope;\n}\nexport {\n  createContext2 as createContext,\n  createContextScope\n};\n//# sourceMappingURL=index.mjs.map\n", "// packages/react/use-layout-effect/src/use-layout-effect.tsx\nimport * as React from \"react\";\nvar useLayoutEffect2 = globalThis?.document ? React.useLayoutEffect : () => {\n};\nexport {\n  useLayoutEffect2 as useLayoutEffect\n};\n//# sourceMappingURL=index.mjs.map\n", "// packages/react/id/src/id.tsx\nimport * as React from \"react\";\nimport { useLayoutEffect } from \"@radix-ui/react-use-layout-effect\";\nvar useReactId = React[\" useId \".trim().toString()] || (() => void 0);\nvar count = 0;\nfunction useId(deterministicId) {\n  const [id, setId] = React.useState(useReactId());\n  useLayoutEffect(() => {\n    if (!deterministicId) setId((reactId) => reactId ?? String(count++));\n  }, [deterministicId]);\n  return deterministicId || (id ? `radix-${id}` : \"\");\n}\nexport {\n  useId\n};\n//# sourceMappingURL=index.mjs.map\n", "// src/use-controllable-state.tsx\nimport * as React from \"react\";\nimport { useLayoutEffect } from \"@radix-ui/react-use-layout-effect\";\nvar useInsertionEffect = React[\" useInsertionEffect \".trim().toString()] || useLayoutEffect;\nfunction useControllableState({\n  prop,\n  defaultProp,\n  onChange = () => {\n  },\n  caller\n}) {\n  const [uncontrolledProp, setUncontrolledProp, onChangeRef] = useUncontrolledState({\n    defaultProp,\n    onChange\n  });\n  const isControlled = prop !== void 0;\n  const value = isControlled ? prop : uncontrolledProp;\n  if (true) {\n    const isControlledRef = React.useRef(prop !== void 0);\n    React.useEffect(() => {\n      const wasControlled = isControlledRef.current;\n      if (wasControlled !== isControlled) {\n        const from = wasControlled ? \"controlled\" : \"uncontrolled\";\n        const to = isControlled ? \"controlled\" : \"uncontrolled\";\n        console.warn(\n          `${caller} is changing from ${from} to ${to}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`\n        );\n      }\n      isControlledRef.current = isControlled;\n    }, [isControlled, caller]);\n  }\n  const setValue = React.useCallback(\n    (nextValue) => {\n      if (isControlled) {\n        const value2 = isFunction(nextValue) ? nextValue(prop) : nextValue;\n        if (value2 !== prop) {\n          onChangeRef.current?.(value2);\n        }\n      } else {\n        setUncontrolledProp(nextValue);\n      }\n    },\n    [isControlled, prop, setUncontrolledProp, onChangeRef]\n  );\n  return [value, setValue];\n}\nfunction useUncontrolledState({\n  defaultProp,\n  onChange\n}) {\n  const [value, setValue] = React.useState(defaultProp);\n  const prevValueRef = React.useRef(value);\n  const onChangeRef = React.useRef(onChange);\n  useInsertionEffect(() => {\n    onChangeRef.current = onChange;\n  }, [onChange]);\n  React.useEffect(() => {\n    if (prevValueRef.current !== value) {\n      onChangeRef.current?.(value);\n      prevValueRef.current = value;\n    }\n  }, [value, prevValueRef]);\n  return [value, setValue, onChangeRef];\n}\nfunction isFunction(value) {\n  return typeof value === \"function\";\n}\n\n// src/use-controllable-state-reducer.tsx\nimport * as React2 from \"react\";\nimport { useEffectEvent } from \"@radix-ui/react-use-effect-event\";\nvar SYNC_STATE = Symbol(\"RADIX:SYNC_STATE\");\nfunction useControllableStateReducer(reducer, userArgs, initialArg, init) {\n  const { prop: controlledState, defaultProp, onChange: onChangeProp, caller } = userArgs;\n  const isControlled = controlledState !== void 0;\n  const onChange = useEffectEvent(onChangeProp);\n  if (true) {\n    const isControlledRef = React2.useRef(controlledState !== void 0);\n    React2.useEffect(() => {\n      const wasControlled = isControlledRef.current;\n      if (wasControlled !== isControlled) {\n        const from = wasControlled ? \"controlled\" : \"uncontrolled\";\n        const to = isControlled ? \"controlled\" : \"uncontrolled\";\n        console.warn(\n          `${caller} is changing from ${from} to ${to}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`\n        );\n      }\n      isControlledRef.current = isControlled;\n    }, [isControlled, caller]);\n  }\n  const args = [{ ...initialArg, state: defaultProp }];\n  if (init) {\n    args.push(init);\n  }\n  const [internalState, dispatch] = React2.useReducer(\n    (state2, action) => {\n      if (action.type === SYNC_STATE) {\n        return { ...state2, state: action.state };\n      }\n      const next = reducer(state2, action);\n      if (isControlled && !Object.is(next.state, state2.state)) {\n        onChange(next.state);\n      }\n      return next;\n    },\n    ...args\n  );\n  const uncontrolledState = internalState.state;\n  const prevValueRef = React2.useRef(uncontrolledState);\n  React2.useEffect(() => {\n    if (prevValueRef.current !== uncontrolledState) {\n      prevValueRef.current = uncontrolledState;\n      if (!isControlled) {\n        onChange(uncontrolledState);\n      }\n    }\n  }, [onChange, uncontrolledState, prevValueRef, isControlled]);\n  const state = React2.useMemo(() => {\n    const isControlled2 = controlledState !== void 0;\n    if (isControlled2) {\n      return { ...internalState, state: controlledState };\n    }\n    return internalState;\n  }, [internalState, controlledState]);\n  React2.useEffect(() => {\n    if (isControlled && !Object.is(controlledState, internalState.state)) {\n      dispatch({ type: SYNC_STATE, state: controlledState });\n    }\n  }, [controlledState, internalState.state, isControlled]);\n  return [state, dispatch];\n}\nexport {\n  useControllableState,\n  useControllableStateReducer\n};\n//# sourceMappingURL=index.mjs.map\n", "// src/slot.tsx\nimport * as React from \"react\";\nimport { composeRefs } from \"@radix-ui/react-compose-refs\";\nimport { Fragment as Fragment2, jsx } from \"react/jsx-runtime\";\n// @__NO_SIDE_EFFECTS__\nfunction createSlot(ownerName) {\n  const SlotClone = /* @__PURE__ */ createSlotClone(ownerName);\n  const Slot2 = React.forwardRef((props, forwardedRef) => {\n    const { children, ...slotProps } = props;\n    const childrenArray = React.Children.toArray(children);\n    const slottable = childrenArray.find(isSlottable);\n    if (slottable) {\n      const newElement = slottable.props.children;\n      const newChildren = childrenArray.map((child) => {\n        if (child === slottable) {\n          if (React.Children.count(newElement) > 1) return React.Children.only(null);\n          return React.isValidElement(newElement) ? newElement.props.children : null;\n        } else {\n          return child;\n        }\n      });\n      return /* @__PURE__ */ jsx(SlotClone, { ...slotProps, ref: forwardedRef, children: React.isValidElement(newElement) ? React.cloneElement(newElement, void 0, newChildren) : null });\n    }\n    return /* @__PURE__ */ jsx(SlotClone, { ...slotProps, ref: forwardedRef, children });\n  });\n  Slot2.displayName = `${ownerName}.Slot`;\n  return Slot2;\n}\nvar Slot = /* @__PURE__ */ createSlot(\"Slot\");\n// @__NO_SIDE_EFFECTS__\nfunction createSlotClone(ownerName) {\n  const SlotClone = React.forwardRef((props, forwardedRef) => {\n    const { children, ...slotProps } = props;\n    if (React.isValidElement(children)) {\n      const childrenRef = getElementRef(children);\n      const props2 = mergeProps(slotProps, children.props);\n      if (children.type !== React.Fragment) {\n        props2.ref = forwardedRef ? composeRefs(forwardedRef, childrenRef) : childrenRef;\n      }\n      return React.cloneElement(children, props2);\n    }\n    return React.Children.count(children) > 1 ? React.Children.only(null) : null;\n  });\n  SlotClone.displayName = `${ownerName}.SlotClone`;\n  return SlotClone;\n}\nvar SLOTTABLE_IDENTIFIER = Symbol(\"radix.slottable\");\n// @__NO_SIDE_EFFECTS__\nfunction createSlottable(ownerName) {\n  const Slottable2 = ({ children }) => {\n    return /* @__PURE__ */ jsx(Fragment2, { children });\n  };\n  Slottable2.displayName = `${ownerName}.Slottable`;\n  Slottable2.__radixId = SLOTTABLE_IDENTIFIER;\n  return Slottable2;\n}\nvar Slottable = /* @__PURE__ */ createSlottable(\"Slottable\");\nfunction isSlottable(child) {\n  return React.isValidElement(child) && typeof child.type === \"function\" && \"__radixId\" in child.type && child.type.__radixId === SLOTTABLE_IDENTIFIER;\n}\nfunction mergeProps(slotProps, childProps) {\n  const overrideProps = { ...childProps };\n  for (const propName in childProps) {\n    const slotPropValue = slotProps[propName];\n    const childPropValue = childProps[propName];\n    const isHandler = /^on[A-Z]/.test(propName);\n    if (isHandler) {\n      if (slotPropValue && childPropValue) {\n        overrideProps[propName] = (...args) => {\n          const result = childPropValue(...args);\n          slotPropValue(...args);\n          return result;\n        };\n      } else if (slotPropValue) {\n        overrideProps[propName] = slotPropValue;\n      }\n    } else if (propName === \"style\") {\n      overrideProps[propName] = { ...slotPropValue, ...childPropValue };\n    } else if (propName === \"className\") {\n      overrideProps[propName] = [slotPropValue, childPropValue].filter(Boolean).join(\" \");\n    }\n  }\n  return { ...slotProps, ...overrideProps };\n}\nfunction getElementRef(element) {\n  let getter = Object.getOwnPropertyDescriptor(element.props, \"ref\")?.get;\n  let mayWarn = getter && \"isReactWarning\" in getter && getter.isReactWarning;\n  if (mayWarn) {\n    return element.ref;\n  }\n  getter = Object.getOwnPropertyDescriptor(element, \"ref\")?.get;\n  mayWarn = getter && \"isReactWarning\" in getter && getter.isReactWarning;\n  if (mayWarn) {\n    return element.props.ref;\n  }\n  return element.props.ref || element.ref;\n}\nexport {\n  Slot as Root,\n  Slot,\n  Slottable,\n  createSlot,\n  createSlottable\n};\n//# sourceMappingURL=index.mjs.map\n", "// src/primitive.tsx\nimport * as React from \"react\";\nimport * as ReactDOM from \"react-dom\";\nimport { createSlot } from \"@radix-ui/react-slot\";\nimport { jsx } from \"react/jsx-runtime\";\nvar NODES = [\n  \"a\",\n  \"button\",\n  \"div\",\n  \"form\",\n  \"h2\",\n  \"h3\",\n  \"img\",\n  \"input\",\n  \"label\",\n  \"li\",\n  \"nav\",\n  \"ol\",\n  \"p\",\n  \"select\",\n  \"span\",\n  \"svg\",\n  \"ul\"\n];\nvar Primitive = NODES.reduce((primitive, node) => {\n  const Slot = createSlot(`Primitive.${node}`);\n  const Node = React.forwardRef((props, forwardedRef) => {\n    const { asChild, ...primitiveProps } = props;\n    const Comp = asChild ? Slot : node;\n    if (typeof window !== \"undefined\") {\n      window[Symbol.for(\"radix-ui\")] = true;\n    }\n    return /* @__PURE__ */ jsx(Comp, { ...primitiveProps, ref: forwardedRef });\n  });\n  Node.displayName = `Primitive.${node}`;\n  return { ...primitive, [node]: Node };\n}, {});\nfunction dispatchDiscreteCustomEvent(target, event) {\n  if (target) ReactDOM.flushSync(() => target.dispatchEvent(event));\n}\nvar Root = Primitive;\nexport {\n  Primitive,\n  Root,\n  dispatchDiscreteCustomEvent\n};\n//# sourceMappingURL=index.mjs.map\n", "// packages/react/use-callback-ref/src/use-callback-ref.tsx\nimport * as React from \"react\";\nfunction useCallbackRef(callback) {\n  const callbackRef = React.useRef(callback);\n  React.useEffect(() => {\n    callbackRef.current = callback;\n  });\n  return React.useMemo(() => (...args) => callbackRef.current?.(...args), []);\n}\nexport {\n  useCallbackRef\n};\n//# sourceMappingURL=index.mjs.map\n", "// packages/react/use-escape-keydown/src/use-escape-keydown.tsx\nimport * as React from \"react\";\nimport { useCallbackRef } from \"@radix-ui/react-use-callback-ref\";\nfunction useEscapeKeydown(onEscapeKeyDownProp, ownerDocument = globalThis?.document) {\n  const onEscapeKeyDown = useCallbackRef(onEscapeKeyDownProp);\n  React.useEffect(() => {\n    const handleKeyDown = (event) => {\n      if (event.key === \"Escape\") {\n        onEscapeKeyDown(event);\n      }\n    };\n    ownerDocument.addEventListener(\"keydown\", handleKeyDown, { capture: true });\n    return () => ownerDocument.removeEventListener(\"keydown\", handleKeyDown, { capture: true });\n  }, [onEscapeKeyDown, ownerDocument]);\n}\nexport {\n  useEscapeKeydown\n};\n//# sourceMappingURL=index.mjs.map\n", "import * as React from 'react';\nimport { composeEventHandlers } from '@radix-ui/primitive';\nimport { Primitive, dispatchDiscreteCustomEvent } from '@radix-ui/react-primitive';\nimport { useComposedRefs } from '@radix-ui/react-compose-refs';\nimport { useCallbackRef } from '@radix-ui/react-use-callback-ref';\nimport { useEscapeKeydown } from '@radix-ui/react-use-escape-keydown';\n\n/* -------------------------------------------------------------------------------------------------\n * DismissableLayer\n * -----------------------------------------------------------------------------------------------*/\n\nconst DISMISSABLE_LAYER_NAME = 'DismissableLayer';\nconst CONTEXT_UPDATE = 'dismissableLayer.update';\nconst POINTER_DOWN_OUTSIDE = 'dismissableLayer.pointerDownOutside';\nconst FOCUS_OUTSIDE = 'dismissableLayer.focusOutside';\n\nlet originalBodyPointerEvents: string;\n\nconst DismissableLayerContext = React.createContext({\n  layers: new Set<DismissableLayerElement>(),\n  layersWithOutsidePointerEventsDisabled: new Set<DismissableLayerElement>(),\n  branches: new Set<DismissableLayerBranchElement>(),\n});\n\ntype DismissableLayerElement = React.ComponentRef<typeof Primitive.div>;\ntype PrimitiveDivProps = React.ComponentPropsWithoutRef<typeof Primitive.div>;\ninterface DismissableLayerProps extends PrimitiveDivProps {\n  /**\n   * When `true`, hover/focus/click interactions will be disabled on elements outside\n   * the `DismissableLayer`. Users will need to click twice on outside elements to\n   * interact with them: once to close the `DismissableLayer`, and again to trigger the element.\n   */\n  disableOutsidePointerEvents?: boolean;\n  /**\n   * Event handler called when the escape key is down.\n   * Can be prevented.\n   */\n  onEscapeKeyDown?: (event: KeyboardEvent) => void;\n  /**\n   * Event handler called when the a `pointerdown` event happens outside of the `DismissableLayer`.\n   * Can be prevented.\n   */\n  onPointerDownOutside?: (event: PointerDownOutsideEvent) => void;\n  /**\n   * Event handler called when the focus moves outside of the `DismissableLayer`.\n   * Can be prevented.\n   */\n  onFocusOutside?: (event: FocusOutsideEvent) => void;\n  /**\n   * Event handler called when an interaction happens outside the `DismissableLayer`.\n   * Specifically, when a `pointerdown` event happens outside or focus moves outside of it.\n   * Can be prevented.\n   */\n  onInteractOutside?: (event: PointerDownOutsideEvent | FocusOutsideEvent) => void;\n  /**\n   * Handler called when the `DismissableLayer` should be dismissed\n   */\n  onDismiss?: () => void;\n}\n\nconst DismissableLayer = React.forwardRef<DismissableLayerElement, DismissableLayerProps>(\n  (props, forwardedRef) => {\n    const {\n      disableOutsidePointerEvents = false,\n      onEscapeKeyDown,\n      onPointerDownOutside,\n      onFocusOutside,\n      onInteractOutside,\n      onDismiss,\n      ...layerProps\n    } = props;\n    const context = React.useContext(DismissableLayerContext);\n    const [node, setNode] = React.useState<DismissableLayerElement | null>(null);\n    const ownerDocument = node?.ownerDocument ?? globalThis?.document;\n    const [, force] = React.useState({});\n    const composedRefs = useComposedRefs(forwardedRef, (node) => setNode(node));\n    const layers = Array.from(context.layers);\n    const [highestLayerWithOutsidePointerEventsDisabled] = [...context.layersWithOutsidePointerEventsDisabled].slice(-1); // prettier-ignore\n    const highestLayerWithOutsidePointerEventsDisabledIndex = layers.indexOf(highestLayerWithOutsidePointerEventsDisabled!); // prettier-ignore\n    const index = node ? layers.indexOf(node) : -1;\n    const isBodyPointerEventsDisabled = context.layersWithOutsidePointerEventsDisabled.size > 0;\n    const isPointerEventsEnabled = index >= highestLayerWithOutsidePointerEventsDisabledIndex;\n\n    const pointerDownOutside = usePointerDownOutside((event) => {\n      const target = event.target as HTMLElement;\n      const isPointerDownOnBranch = [...context.branches].some((branch) => branch.contains(target));\n      if (!isPointerEventsEnabled || isPointerDownOnBranch) return;\n      onPointerDownOutside?.(event);\n      onInteractOutside?.(event);\n      if (!event.defaultPrevented) onDismiss?.();\n    }, ownerDocument);\n\n    const focusOutside = useFocusOutside((event) => {\n      const target = event.target as HTMLElement;\n      const isFocusInBranch = [...context.branches].some((branch) => branch.contains(target));\n      if (isFocusInBranch) return;\n      onFocusOutside?.(event);\n      onInteractOutside?.(event);\n      if (!event.defaultPrevented) onDismiss?.();\n    }, ownerDocument);\n\n    useEscapeKeydown((event) => {\n      const isHighestLayer = index === context.layers.size - 1;\n      if (!isHighestLayer) return;\n      onEscapeKeyDown?.(event);\n      if (!event.defaultPrevented && onDismiss) {\n        event.preventDefault();\n        onDismiss();\n      }\n    }, ownerDocument);\n\n    React.useEffect(() => {\n      if (!node) return;\n      if (disableOutsidePointerEvents) {\n        if (context.layersWithOutsidePointerEventsDisabled.size === 0) {\n          originalBodyPointerEvents = ownerDocument.body.style.pointerEvents;\n          ownerDocument.body.style.pointerEvents = 'none';\n        }\n        context.layersWithOutsidePointerEventsDisabled.add(node);\n      }\n      context.layers.add(node);\n      dispatchUpdate();\n      return () => {\n        if (\n          disableOutsidePointerEvents &&\n          context.layersWithOutsidePointerEventsDisabled.size === 1\n        ) {\n          ownerDocument.body.style.pointerEvents = originalBodyPointerEvents;\n        }\n      };\n    }, [node, ownerDocument, disableOutsidePointerEvents, context]);\n\n    /**\n     * We purposefully prevent combining this effect with the `disableOutsidePointerEvents` effect\n     * because a change to `disableOutsidePointerEvents` would remove this layer from the stack\n     * and add it to the end again so the layering order wouldn't be _creation order_.\n     * We only want them to be removed from context stacks when unmounted.\n     */\n    React.useEffect(() => {\n      return () => {\n        if (!node) return;\n        context.layers.delete(node);\n        context.layersWithOutsidePointerEventsDisabled.delete(node);\n        dispatchUpdate();\n      };\n    }, [node, context]);\n\n    React.useEffect(() => {\n      const handleUpdate = () => force({});\n      document.addEventListener(CONTEXT_UPDATE, handleUpdate);\n      return () => document.removeEventListener(CONTEXT_UPDATE, handleUpdate);\n    }, []);\n\n    return (\n      <Primitive.div\n        {...layerProps}\n        ref={composedRefs}\n        style={{\n          pointerEvents: isBodyPointerEventsDisabled\n            ? isPointerEventsEnabled\n              ? 'auto'\n              : 'none'\n            : undefined,\n          ...props.style,\n        }}\n        onFocusCapture={composeEventHandlers(props.onFocusCapture, focusOutside.onFocusCapture)}\n        onBlurCapture={composeEventHandlers(props.onBlurCapture, focusOutside.onBlurCapture)}\n        onPointerDownCapture={composeEventHandlers(\n          props.onPointerDownCapture,\n          pointerDownOutside.onPointerDownCapture\n        )}\n      />\n    );\n  }\n);\n\nDismissableLayer.displayName = DISMISSABLE_LAYER_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DismissableLayerBranch\n * -----------------------------------------------------------------------------------------------*/\n\nconst BRANCH_NAME = 'DismissableLayerBranch';\n\ntype DismissableLayerBranchElement = React.ComponentRef<typeof Primitive.div>;\ninterface DismissableLayerBranchProps extends PrimitiveDivProps {}\n\nconst DismissableLayerBranch = React.forwardRef<\n  DismissableLayerBranchElement,\n  DismissableLayerBranchProps\n>((props, forwardedRef) => {\n  const context = React.useContext(DismissableLayerContext);\n  const ref = React.useRef<DismissableLayerBranchElement>(null);\n  const composedRefs = useComposedRefs(forwardedRef, ref);\n\n  React.useEffect(() => {\n    const node = ref.current;\n    if (node) {\n      context.branches.add(node);\n      return () => {\n        context.branches.delete(node);\n      };\n    }\n  }, [context.branches]);\n\n  return <Primitive.div {...props} ref={composedRefs} />;\n});\n\nDismissableLayerBranch.displayName = BRANCH_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\ntype PointerDownOutsideEvent = CustomEvent<{ originalEvent: PointerEvent }>;\ntype FocusOutsideEvent = CustomEvent<{ originalEvent: FocusEvent }>;\n\n/**\n * Listens for `pointerdown` outside a react subtree. We use `pointerdown` rather than `pointerup`\n * to mimic layer dismissing behaviour present in OS.\n * Returns props to pass to the node we want to check for outside events.\n */\nfunction usePointerDownOutside(\n  onPointerDownOutside?: (event: PointerDownOutsideEvent) => void,\n  ownerDocument: Document = globalThis?.document\n) {\n  const handlePointerDownOutside = useCallbackRef(onPointerDownOutside) as EventListener;\n  const isPointerInsideReactTreeRef = React.useRef(false);\n  const handleClickRef = React.useRef(() => {});\n\n  React.useEffect(() => {\n    const handlePointerDown = (event: PointerEvent) => {\n      if (event.target && !isPointerInsideReactTreeRef.current) {\n        const eventDetail = { originalEvent: event };\n\n        function handleAndDispatchPointerDownOutsideEvent() {\n          handleAndDispatchCustomEvent(\n            POINTER_DOWN_OUTSIDE,\n            handlePointerDownOutside,\n            eventDetail,\n            { discrete: true }\n          );\n        }\n\n        /**\n         * On touch devices, we need to wait for a click event because browsers implement\n         * a ~350ms delay between the time the user stops touching the display and when the\n         * browser executres events. We need to ensure we don't reactivate pointer-events within\n         * this timeframe otherwise the browser may execute events that should have been prevented.\n         *\n         * Additionally, this also lets us deal automatically with cancellations when a click event\n         * isn't raised because the page was considered scrolled/drag-scrolled, long-pressed, etc.\n         *\n         * This is why we also continuously remove the previous listener, because we cannot be\n         * certain that it was raised, and therefore cleaned-up.\n         */\n        if (event.pointerType === 'touch') {\n          ownerDocument.removeEventListener('click', handleClickRef.current);\n          handleClickRef.current = handleAndDispatchPointerDownOutsideEvent;\n          ownerDocument.addEventListener('click', handleClickRef.current, { once: true });\n        } else {\n          handleAndDispatchPointerDownOutsideEvent();\n        }\n      } else {\n        // We need to remove the event listener in case the outside click has been canceled.\n        // See: https://github.com/radix-ui/primitives/issues/2171\n        ownerDocument.removeEventListener('click', handleClickRef.current);\n      }\n      isPointerInsideReactTreeRef.current = false;\n    };\n    /**\n     * if this hook executes in a component that mounts via a `pointerdown` event, the event\n     * would bubble up to the document and trigger a `pointerDownOutside` event. We avoid\n     * this by delaying the event listener registration on the document.\n     * This is not React specific, but rather how the DOM works, ie:\n     * ```\n     * button.addEventListener('pointerdown', () => {\n     *   console.log('I will log');\n     *   document.addEventListener('pointerdown', () => {\n     *     console.log('I will also log');\n     *   })\n     * });\n     */\n    const timerId = window.setTimeout(() => {\n      ownerDocument.addEventListener('pointerdown', handlePointerDown);\n    }, 0);\n    return () => {\n      window.clearTimeout(timerId);\n      ownerDocument.removeEventListener('pointerdown', handlePointerDown);\n      ownerDocument.removeEventListener('click', handleClickRef.current);\n    };\n  }, [ownerDocument, handlePointerDownOutside]);\n\n  return {\n    // ensures we check React component tree (not just DOM tree)\n    onPointerDownCapture: () => (isPointerInsideReactTreeRef.current = true),\n  };\n}\n\n/**\n * Listens for when focus happens outside a react subtree.\n * Returns props to pass to the root (node) of the subtree we want to check.\n */\nfunction useFocusOutside(\n  onFocusOutside?: (event: FocusOutsideEvent) => void,\n  ownerDocument: Document = globalThis?.document\n) {\n  const handleFocusOutside = useCallbackRef(onFocusOutside) as EventListener;\n  const isFocusInsideReactTreeRef = React.useRef(false);\n\n  React.useEffect(() => {\n    const handleFocus = (event: FocusEvent) => {\n      if (event.target && !isFocusInsideReactTreeRef.current) {\n        const eventDetail = { originalEvent: event };\n        handleAndDispatchCustomEvent(FOCUS_OUTSIDE, handleFocusOutside, eventDetail, {\n          discrete: false,\n        });\n      }\n    };\n    ownerDocument.addEventListener('focusin', handleFocus);\n    return () => ownerDocument.removeEventListener('focusin', handleFocus);\n  }, [ownerDocument, handleFocusOutside]);\n\n  return {\n    onFocusCapture: () => (isFocusInsideReactTreeRef.current = true),\n    onBlurCapture: () => (isFocusInsideReactTreeRef.current = false),\n  };\n}\n\nfunction dispatchUpdate() {\n  const event = new CustomEvent(CONTEXT_UPDATE);\n  document.dispatchEvent(event);\n}\n\nfunction handleAndDispatchCustomEvent<E extends CustomEvent, OriginalEvent extends Event>(\n  name: string,\n  handler: ((event: E) => void) | undefined,\n  detail: { originalEvent: OriginalEvent } & (E extends CustomEvent<infer D> ? D : never),\n  { discrete }: { discrete: boolean }\n) {\n  const target = detail.originalEvent.target;\n  const event = new CustomEvent(name, { bubbles: false, cancelable: true, detail });\n  if (handler) target.addEventListener(name, handler as EventListener, { once: true });\n\n  if (discrete) {\n    dispatchDiscreteCustomEvent(target, event);\n  } else {\n    target.dispatchEvent(event);\n  }\n}\n\nconst Root = DismissableLayer;\nconst Branch = DismissableLayerBranch;\n\nexport {\n  DismissableLayer,\n  DismissableLayerBranch,\n  //\n  Root,\n  Branch,\n};\nexport type { DismissableLayerProps };\n", "import * as React from 'react';\nimport ReactDOM from 'react-dom';\nimport { Primitive } from '@radix-ui/react-primitive';\nimport { useLayoutEffect } from '@radix-ui/react-use-layout-effect';\n\n/* -------------------------------------------------------------------------------------------------\n * Portal\n * -----------------------------------------------------------------------------------------------*/\n\nconst PORTAL_NAME = 'Portal';\n\ntype PortalElement = React.ComponentRef<typeof Primitive.div>;\ntype PrimitiveDivProps = React.ComponentPropsWithoutRef<typeof Primitive.div>;\ninterface PortalProps extends PrimitiveDivProps {\n  /**\n   * An optional container where the portaled content should be appended.\n   */\n  container?: Element | DocumentFragment | null;\n}\n\nconst Portal = React.forwardRef<PortalElement, PortalProps>((props, forwardedRef) => {\n  const { container: containerProp, ...portalProps } = props;\n  const [mounted, setMounted] = React.useState(false);\n  useLayoutEffect(() => setMounted(true), []);\n  const container = containerProp || (mounted && globalThis?.document?.body);\n  return container\n    ? ReactDOM.createPortal(<Primitive.div {...portalProps} ref={forwardedRef} />, container)\n    : null;\n});\n\nPortal.displayName = PORTAL_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\nconst Root = Portal;\n\nexport {\n  Portal,\n  //\n  Root,\n};\nexport type { PortalProps };\n", "import * as React from 'react';\nimport { useComposedRefs } from '@radix-ui/react-compose-refs';\nimport { useLayoutEffect } from '@radix-ui/react-use-layout-effect';\nimport { useStateMachine } from './use-state-machine';\n\ninterface PresenceProps {\n  children: React.ReactElement | ((props: { present: boolean }) => React.ReactElement);\n  present: boolean;\n}\n\nconst Presence: React.FC<PresenceProps> = (props) => {\n  const { present, children } = props;\n  const presence = usePresence(present);\n\n  const child = (\n    typeof children === 'function'\n      ? children({ present: presence.isPresent })\n      : React.Children.only(children)\n  ) as React.ReactElement<{ ref?: React.Ref<HTMLElement> }>;\n\n  const ref = useComposedRefs(presence.ref, getElementRef(child));\n  const forceMount = typeof children === 'function';\n  return forceMount || presence.isPresent ? React.cloneElement(child, { ref }) : null;\n};\n\nPresence.displayName = 'Presence';\n\n/* -------------------------------------------------------------------------------------------------\n * usePresence\n * -----------------------------------------------------------------------------------------------*/\n\nfunction usePresence(present: boolean) {\n  const [node, setNode] = React.useState<HTMLElement>();\n  const stylesRef = React.useRef<CSSStyleDeclaration | null>(null);\n  const prevPresentRef = React.useRef(present);\n  const prevAnimationNameRef = React.useRef<string>('none');\n  const initialState = present ? 'mounted' : 'unmounted';\n  const [state, send] = useStateMachine(initialState, {\n    mounted: {\n      UNMOUNT: 'unmounted',\n      ANIMATION_OUT: 'unmountSuspended',\n    },\n    unmountSuspended: {\n      MOUNT: 'mounted',\n      ANIMATION_END: 'unmounted',\n    },\n    unmounted: {\n      MOUNT: 'mounted',\n    },\n  });\n\n  React.useEffect(() => {\n    const currentAnimationName = getAnimationName(stylesRef.current);\n    prevAnimationNameRef.current = state === 'mounted' ? currentAnimationName : 'none';\n  }, [state]);\n\n  useLayoutEffect(() => {\n    const styles = stylesRef.current;\n    const wasPresent = prevPresentRef.current;\n    const hasPresentChanged = wasPresent !== present;\n\n    if (hasPresentChanged) {\n      const prevAnimationName = prevAnimationNameRef.current;\n      const currentAnimationName = getAnimationName(styles);\n\n      if (present) {\n        send('MOUNT');\n      } else if (currentAnimationName === 'none' || styles?.display === 'none') {\n        // If there is no exit animation or the element is hidden, animations won't run\n        // so we unmount instantly\n        send('UNMOUNT');\n      } else {\n        /**\n         * When `present` changes to `false`, we check changes to animation-name to\n         * determine whether an animation has started. We chose this approach (reading\n         * computed styles) because there is no `animationrun` event and `animationstart`\n         * fires after `animation-delay` has expired which would be too late.\n         */\n        const isAnimating = prevAnimationName !== currentAnimationName;\n\n        if (wasPresent && isAnimating) {\n          send('ANIMATION_OUT');\n        } else {\n          send('UNMOUNT');\n        }\n      }\n\n      prevPresentRef.current = present;\n    }\n  }, [present, send]);\n\n  useLayoutEffect(() => {\n    if (node) {\n      let timeoutId: number;\n      const ownerWindow = node.ownerDocument.defaultView ?? window;\n      /**\n       * Triggering an ANIMATION_OUT during an ANIMATION_IN will fire an `animationcancel`\n       * event for ANIMATION_IN after we have entered `unmountSuspended` state. So, we\n       * make sure we only trigger ANIMATION_END for the currently active animation.\n       */\n      const handleAnimationEnd = (event: AnimationEvent) => {\n        const currentAnimationName = getAnimationName(stylesRef.current);\n        const isCurrentAnimation = currentAnimationName.includes(event.animationName);\n        if (event.target === node && isCurrentAnimation) {\n          // With React 18 concurrency this update is applied a frame after the\n          // animation ends, creating a flash of visible content. By setting the\n          // animation fill mode to \"forwards\", we force the node to keep the\n          // styles of the last keyframe, removing the flash.\n          //\n          // Previously we flushed the update via ReactDom.flushSync, but with\n          // exit animations this resulted in the node being removed from the\n          // DOM before the synthetic animationEnd event was dispatched, meaning\n          // user-provided event handlers would not be called.\n          // https://github.com/radix-ui/primitives/pull/1849\n          send('ANIMATION_END');\n          if (!prevPresentRef.current) {\n            const currentFillMode = node.style.animationFillMode;\n            node.style.animationFillMode = 'forwards';\n            // Reset the style after the node had time to unmount (for cases\n            // where the component chooses not to unmount). Doing this any\n            // sooner than `setTimeout` (e.g. with `requestAnimationFrame`)\n            // still causes a flash.\n            timeoutId = ownerWindow.setTimeout(() => {\n              if (node.style.animationFillMode === 'forwards') {\n                node.style.animationFillMode = currentFillMode;\n              }\n            });\n          }\n        }\n      };\n      const handleAnimationStart = (event: AnimationEvent) => {\n        if (event.target === node) {\n          // if animation occurred, store its name as the previous animation.\n          prevAnimationNameRef.current = getAnimationName(stylesRef.current);\n        }\n      };\n      node.addEventListener('animationstart', handleAnimationStart);\n      node.addEventListener('animationcancel', handleAnimationEnd);\n      node.addEventListener('animationend', handleAnimationEnd);\n      return () => {\n        ownerWindow.clearTimeout(timeoutId);\n        node.removeEventListener('animationstart', handleAnimationStart);\n        node.removeEventListener('animationcancel', handleAnimationEnd);\n        node.removeEventListener('animationend', handleAnimationEnd);\n      };\n    } else {\n      // Transition to the unmounted state if the node is removed prematurely.\n      // We avoid doing so during cleanup as the node may change but still exist.\n      send('ANIMATION_END');\n    }\n  }, [node, send]);\n\n  return {\n    isPresent: ['mounted', 'unmountSuspended'].includes(state),\n    ref: React.useCallback((node: HTMLElement) => {\n      stylesRef.current = node ? getComputedStyle(node) : null;\n      setNode(node);\n    }, []),\n  };\n}\n\n/* -----------------------------------------------------------------------------------------------*/\n\nfunction getAnimationName(styles: CSSStyleDeclaration | null) {\n  return styles?.animationName || 'none';\n}\n\n// Before React 19 accessing `element.props.ref` will throw a warning and suggest using `element.ref`\n// After React 19 accessing `element.ref` does the opposite.\n// https://github.com/facebook/react/pull/28348\n//\n// Access the ref using the method that doesn't yield a warning.\nfunction getElementRef(element: React.ReactElement<{ ref?: React.Ref<unknown> }>) {\n  // React <=18 in DEV\n  let getter = Object.getOwnPropertyDescriptor(element.props, 'ref')?.get;\n  let mayWarn = getter && 'isReactWarning' in getter && getter.isReactWarning;\n  if (mayWarn) {\n    return (element as any).ref;\n  }\n\n  // React 19 in DEV\n  getter = Object.getOwnPropertyDescriptor(element, 'ref')?.get;\n  mayWarn = getter && 'isReactWarning' in getter && getter.isReactWarning;\n  if (mayWarn) {\n    return element.props.ref;\n  }\n\n  // Not DEV\n  return element.props.ref || (element as any).ref;\n}\n\nconst Root = Presence;\n\nexport {\n  Presence,\n  //\n  Root,\n};\nexport type { PresenceProps };\n", "import * as React from 'react';\n\ntype Machine<S> = { [k: string]: { [k: string]: S } };\ntype MachineState<T> = keyof T;\ntype MachineEvent<T> = keyof UnionToIntersection<T[keyof T]>;\n\n// 🤯 https://fettblog.eu/typescript-union-to-intersection/\ntype UnionToIntersection<T> = (T extends any ? (x: T) => any : never) extends (x: infer R) => any\n  ? R\n  : never;\n\nexport function useStateMachine<M>(\n  initialState: MachineState<M>,\n  machine: M & Machine<MachineState<M>>\n) {\n  return React.useReducer((state: MachineState<M>, event: MachineEvent<M>): MachineState<M> => {\n    const nextState = (machine[state] as any)[event];\n    return nextState ?? state;\n  }, initialState);\n}\n", "import * as React from 'react';\nimport { composeEventHandlers } from '@radix-ui/primitive';\nimport { useComposedRefs } from '@radix-ui/react-compose-refs';\nimport { createContext, createContextScope } from '@radix-ui/react-context';\nimport { useId } from '@radix-ui/react-id';\nimport { useControllableState } from '@radix-ui/react-use-controllable-state';\nimport { DismissableLayer } from '@radix-ui/react-dismissable-layer';\nimport { FocusScope } from '@radix-ui/react-focus-scope';\nimport { Portal as PortalPrimitive } from '@radix-ui/react-portal';\nimport { Presence } from '@radix-ui/react-presence';\nimport { Primitive } from '@radix-ui/react-primitive';\nimport { useFocusGuards } from '@radix-ui/react-focus-guards';\nimport { RemoveScroll } from 'react-remove-scroll';\nimport { hideOthers } from 'aria-hidden';\nimport { createSlot } from '@radix-ui/react-slot';\n\nimport type { Scope } from '@radix-ui/react-context';\n\n/* -------------------------------------------------------------------------------------------------\n * Dialog\n * -----------------------------------------------------------------------------------------------*/\n\nconst DIALOG_NAME = 'Dialog';\n\ntype ScopedProps<P> = P & { __scopeDialog?: Scope };\nconst [createDialogContext, createDialogScope] = createContextScope(DIALOG_NAME);\n\ntype DialogContextValue = {\n  triggerRef: React.RefObject<HTMLButtonElement | null>;\n  contentRef: React.RefObject<DialogContentElement | null>;\n  contentId: string;\n  titleId: string;\n  descriptionId: string;\n  open: boolean;\n  onOpenChange(open: boolean): void;\n  onOpenToggle(): void;\n  modal: boolean;\n};\n\nconst [DialogProvider, useDialogContext] = createDialogContext<DialogContextValue>(DIALOG_NAME);\n\ninterface DialogProps {\n  children?: React.ReactNode;\n  open?: boolean;\n  defaultOpen?: boolean;\n  onOpenChange?(open: boolean): void;\n  modal?: boolean;\n}\n\nconst Dialog: React.FC<DialogProps> = (props: ScopedProps<DialogProps>) => {\n  const {\n    __scopeDialog,\n    children,\n    open: openProp,\n    defaultOpen,\n    onOpenChange,\n    modal = true,\n  } = props;\n  const triggerRef = React.useRef<HTMLButtonElement>(null);\n  const contentRef = React.useRef<DialogContentElement>(null);\n  const [open, setOpen] = useControllableState({\n    prop: openProp,\n    defaultProp: defaultOpen ?? false,\n    onChange: onOpenChange,\n    caller: DIALOG_NAME,\n  });\n\n  return (\n    <DialogProvider\n      scope={__scopeDialog}\n      triggerRef={triggerRef}\n      contentRef={contentRef}\n      contentId={useId()}\n      titleId={useId()}\n      descriptionId={useId()}\n      open={open}\n      onOpenChange={setOpen}\n      onOpenToggle={React.useCallback(() => setOpen((prevOpen) => !prevOpen), [setOpen])}\n      modal={modal}\n    >\n      {children}\n    </DialogProvider>\n  );\n};\n\nDialog.displayName = DIALOG_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DialogTrigger\n * -----------------------------------------------------------------------------------------------*/\n\nconst TRIGGER_NAME = 'DialogTrigger';\n\ntype DialogTriggerElement = React.ComponentRef<typeof Primitive.button>;\ntype PrimitiveButtonProps = React.ComponentPropsWithoutRef<typeof Primitive.button>;\ninterface DialogTriggerProps extends PrimitiveButtonProps {}\n\nconst DialogTrigger = React.forwardRef<DialogTriggerElement, DialogTriggerProps>(\n  (props: ScopedProps<DialogTriggerProps>, forwardedRef) => {\n    const { __scopeDialog, ...triggerProps } = props;\n    const context = useDialogContext(TRIGGER_NAME, __scopeDialog);\n    const composedTriggerRef = useComposedRefs(forwardedRef, context.triggerRef);\n    return (\n      <Primitive.button\n        type=\"button\"\n        aria-haspopup=\"dialog\"\n        aria-expanded={context.open}\n        aria-controls={context.contentId}\n        data-state={getState(context.open)}\n        {...triggerProps}\n        ref={composedTriggerRef}\n        onClick={composeEventHandlers(props.onClick, context.onOpenToggle)}\n      />\n    );\n  }\n);\n\nDialogTrigger.displayName = TRIGGER_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DialogPortal\n * -----------------------------------------------------------------------------------------------*/\n\nconst PORTAL_NAME = 'DialogPortal';\n\ntype PortalContextValue = { forceMount?: true };\nconst [PortalProvider, usePortalContext] = createDialogContext<PortalContextValue>(PORTAL_NAME, {\n  forceMount: undefined,\n});\n\ntype PortalProps = React.ComponentPropsWithoutRef<typeof PortalPrimitive>;\ninterface DialogPortalProps {\n  children?: React.ReactNode;\n  /**\n   * Specify a container element to portal the content into.\n   */\n  container?: PortalProps['container'];\n  /**\n   * Used to force mounting when more control is needed. Useful when\n   * controlling animation with React animation libraries.\n   */\n  forceMount?: true;\n}\n\nconst DialogPortal: React.FC<DialogPortalProps> = (props: ScopedProps<DialogPortalProps>) => {\n  const { __scopeDialog, forceMount, children, container } = props;\n  const context = useDialogContext(PORTAL_NAME, __scopeDialog);\n  return (\n    <PortalProvider scope={__scopeDialog} forceMount={forceMount}>\n      {React.Children.map(children, (child) => (\n        <Presence present={forceMount || context.open}>\n          <PortalPrimitive asChild container={container}>\n            {child}\n          </PortalPrimitive>\n        </Presence>\n      ))}\n    </PortalProvider>\n  );\n};\n\nDialogPortal.displayName = PORTAL_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DialogOverlay\n * -----------------------------------------------------------------------------------------------*/\n\nconst OVERLAY_NAME = 'DialogOverlay';\n\ntype DialogOverlayElement = DialogOverlayImplElement;\ninterface DialogOverlayProps extends DialogOverlayImplProps {\n  /**\n   * Used to force mounting when more control is needed. Useful when\n   * controlling animation with React animation libraries.\n   */\n  forceMount?: true;\n}\n\nconst DialogOverlay = React.forwardRef<DialogOverlayElement, DialogOverlayProps>(\n  (props: ScopedProps<DialogOverlayProps>, forwardedRef) => {\n    const portalContext = usePortalContext(OVERLAY_NAME, props.__scopeDialog);\n    const { forceMount = portalContext.forceMount, ...overlayProps } = props;\n    const context = useDialogContext(OVERLAY_NAME, props.__scopeDialog);\n    return context.modal ? (\n      <Presence present={forceMount || context.open}>\n        <DialogOverlayImpl {...overlayProps} ref={forwardedRef} />\n      </Presence>\n    ) : null;\n  }\n);\n\nDialogOverlay.displayName = OVERLAY_NAME;\n\ntype DialogOverlayImplElement = React.ComponentRef<typeof Primitive.div>;\ntype PrimitiveDivProps = React.ComponentPropsWithoutRef<typeof Primitive.div>;\ninterface DialogOverlayImplProps extends PrimitiveDivProps {}\n\nconst Slot = createSlot('DialogOverlay.RemoveScroll');\n\nconst DialogOverlayImpl = React.forwardRef<DialogOverlayImplElement, DialogOverlayImplProps>(\n  (props: ScopedProps<DialogOverlayImplProps>, forwardedRef) => {\n    const { __scopeDialog, ...overlayProps } = props;\n    const context = useDialogContext(OVERLAY_NAME, __scopeDialog);\n    return (\n      // Make sure `Content` is scrollable even when it doesn't live inside `RemoveScroll`\n      // ie. when `Overlay` and `Content` are siblings\n      <RemoveScroll as={Slot} allowPinchZoom shards={[context.contentRef]}>\n        <Primitive.div\n          data-state={getState(context.open)}\n          {...overlayProps}\n          ref={forwardedRef}\n          // We re-enable pointer-events prevented by `Dialog.Content` to allow scrolling the overlay.\n          style={{ pointerEvents: 'auto', ...overlayProps.style }}\n        />\n      </RemoveScroll>\n    );\n  }\n);\n\n/* -------------------------------------------------------------------------------------------------\n * DialogContent\n * -----------------------------------------------------------------------------------------------*/\n\nconst CONTENT_NAME = 'DialogContent';\n\ntype DialogContentElement = DialogContentTypeElement;\ninterface DialogContentProps extends DialogContentTypeProps {\n  /**\n   * Used to force mounting when more control is needed. Useful when\n   * controlling animation with React animation libraries.\n   */\n  forceMount?: true;\n}\n\nconst DialogContent = React.forwardRef<DialogContentElement, DialogContentProps>(\n  (props: ScopedProps<DialogContentProps>, forwardedRef) => {\n    const portalContext = usePortalContext(CONTENT_NAME, props.__scopeDialog);\n    const { forceMount = portalContext.forceMount, ...contentProps } = props;\n    const context = useDialogContext(CONTENT_NAME, props.__scopeDialog);\n    return (\n      <Presence present={forceMount || context.open}>\n        {context.modal ? (\n          <DialogContentModal {...contentProps} ref={forwardedRef} />\n        ) : (\n          <DialogContentNonModal {...contentProps} ref={forwardedRef} />\n        )}\n      </Presence>\n    );\n  }\n);\n\nDialogContent.displayName = CONTENT_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\ntype DialogContentTypeElement = DialogContentImplElement;\ninterface DialogContentTypeProps\n  extends Omit<DialogContentImplProps, 'trapFocus' | 'disableOutsidePointerEvents'> {}\n\nconst DialogContentModal = React.forwardRef<DialogContentTypeElement, DialogContentTypeProps>(\n  (props: ScopedProps<DialogContentTypeProps>, forwardedRef) => {\n    const context = useDialogContext(CONTENT_NAME, props.__scopeDialog);\n    const contentRef = React.useRef<HTMLDivElement>(null);\n    const composedRefs = useComposedRefs(forwardedRef, context.contentRef, contentRef);\n\n    // aria-hide everything except the content (better supported equivalent to setting aria-modal)\n    React.useEffect(() => {\n      const content = contentRef.current;\n      if (content) return hideOthers(content);\n    }, []);\n\n    return (\n      <DialogContentImpl\n        {...props}\n        ref={composedRefs}\n        // we make sure focus isn't trapped once `DialogContent` has been closed\n        // (closed !== unmounted when animating out)\n        trapFocus={context.open}\n        disableOutsidePointerEvents\n        onCloseAutoFocus={composeEventHandlers(props.onCloseAutoFocus, (event) => {\n          event.preventDefault();\n          context.triggerRef.current?.focus();\n        })}\n        onPointerDownOutside={composeEventHandlers(props.onPointerDownOutside, (event) => {\n          const originalEvent = event.detail.originalEvent;\n          const ctrlLeftClick = originalEvent.button === 0 && originalEvent.ctrlKey === true;\n          const isRightClick = originalEvent.button === 2 || ctrlLeftClick;\n\n          // If the event is a right-click, we shouldn't close because\n          // it is effectively as if we right-clicked the `Overlay`.\n          if (isRightClick) event.preventDefault();\n        })}\n        // When focus is trapped, a `focusout` event may still happen.\n        // We make sure we don't trigger our `onDismiss` in such case.\n        onFocusOutside={composeEventHandlers(props.onFocusOutside, (event) =>\n          event.preventDefault()\n        )}\n      />\n    );\n  }\n);\n\n/* -----------------------------------------------------------------------------------------------*/\n\nconst DialogContentNonModal = React.forwardRef<DialogContentTypeElement, DialogContentTypeProps>(\n  (props: ScopedProps<DialogContentTypeProps>, forwardedRef) => {\n    const context = useDialogContext(CONTENT_NAME, props.__scopeDialog);\n    const hasInteractedOutsideRef = React.useRef(false);\n    const hasPointerDownOutsideRef = React.useRef(false);\n\n    return (\n      <DialogContentImpl\n        {...props}\n        ref={forwardedRef}\n        trapFocus={false}\n        disableOutsidePointerEvents={false}\n        onCloseAutoFocus={(event) => {\n          props.onCloseAutoFocus?.(event);\n\n          if (!event.defaultPrevented) {\n            if (!hasInteractedOutsideRef.current) context.triggerRef.current?.focus();\n            // Always prevent auto focus because we either focus manually or want user agent focus\n            event.preventDefault();\n          }\n\n          hasInteractedOutsideRef.current = false;\n          hasPointerDownOutsideRef.current = false;\n        }}\n        onInteractOutside={(event) => {\n          props.onInteractOutside?.(event);\n\n          if (!event.defaultPrevented) {\n            hasInteractedOutsideRef.current = true;\n            if (event.detail.originalEvent.type === 'pointerdown') {\n              hasPointerDownOutsideRef.current = true;\n            }\n          }\n\n          // Prevent dismissing when clicking the trigger.\n          // As the trigger is already setup to close, without doing so would\n          // cause it to close and immediately open.\n          const target = event.target as HTMLElement;\n          const targetIsTrigger = context.triggerRef.current?.contains(target);\n          if (targetIsTrigger) event.preventDefault();\n\n          // On Safari if the trigger is inside a container with tabIndex={0}, when clicked\n          // we will get the pointer down outside event on the trigger, but then a subsequent\n          // focus outside event on the container, we ignore any focus outside event when we've\n          // already had a pointer down outside event.\n          if (event.detail.originalEvent.type === 'focusin' && hasPointerDownOutsideRef.current) {\n            event.preventDefault();\n          }\n        }}\n      />\n    );\n  }\n);\n\n/* -----------------------------------------------------------------------------------------------*/\n\ntype DialogContentImplElement = React.ComponentRef<typeof DismissableLayer>;\ntype DismissableLayerProps = React.ComponentPropsWithoutRef<typeof DismissableLayer>;\ntype FocusScopeProps = React.ComponentPropsWithoutRef<typeof FocusScope>;\ninterface DialogContentImplProps extends Omit<DismissableLayerProps, 'onDismiss'> {\n  /**\n   * When `true`, focus cannot escape the `Content` via keyboard,\n   * pointer, or a programmatic focus.\n   * @defaultValue false\n   */\n  trapFocus?: FocusScopeProps['trapped'];\n\n  /**\n   * Event handler called when auto-focusing on open.\n   * Can be prevented.\n   */\n  onOpenAutoFocus?: FocusScopeProps['onMountAutoFocus'];\n\n  /**\n   * Event handler called when auto-focusing on close.\n   * Can be prevented.\n   */\n  onCloseAutoFocus?: FocusScopeProps['onUnmountAutoFocus'];\n}\n\nconst DialogContentImpl = React.forwardRef<DialogContentImplElement, DialogContentImplProps>(\n  (props: ScopedProps<DialogContentImplProps>, forwardedRef) => {\n    const { __scopeDialog, trapFocus, onOpenAutoFocus, onCloseAutoFocus, ...contentProps } = props;\n    const context = useDialogContext(CONTENT_NAME, __scopeDialog);\n    const contentRef = React.useRef<HTMLDivElement>(null);\n    const composedRefs = useComposedRefs(forwardedRef, contentRef);\n\n    // Make sure the whole tree has focus guards as our `Dialog` will be\n    // the last element in the DOM (because of the `Portal`)\n    useFocusGuards();\n\n    return (\n      <>\n        <FocusScope\n          asChild\n          loop\n          trapped={trapFocus}\n          onMountAutoFocus={onOpenAutoFocus}\n          onUnmountAutoFocus={onCloseAutoFocus}\n        >\n          <DismissableLayer\n            role=\"dialog\"\n            id={context.contentId}\n            aria-describedby={context.descriptionId}\n            aria-labelledby={context.titleId}\n            data-state={getState(context.open)}\n            {...contentProps}\n            ref={composedRefs}\n            onDismiss={() => context.onOpenChange(false)}\n          />\n        </FocusScope>\n        {process.env.NODE_ENV !== 'production' && (\n          <>\n            <TitleWarning titleId={context.titleId} />\n            <DescriptionWarning contentRef={contentRef} descriptionId={context.descriptionId} />\n          </>\n        )}\n      </>\n    );\n  }\n);\n\n/* -------------------------------------------------------------------------------------------------\n * DialogTitle\n * -----------------------------------------------------------------------------------------------*/\n\nconst TITLE_NAME = 'DialogTitle';\n\ntype DialogTitleElement = React.ComponentRef<typeof Primitive.h2>;\ntype PrimitiveHeading2Props = React.ComponentPropsWithoutRef<typeof Primitive.h2>;\ninterface DialogTitleProps extends PrimitiveHeading2Props {}\n\nconst DialogTitle = React.forwardRef<DialogTitleElement, DialogTitleProps>(\n  (props: ScopedProps<DialogTitleProps>, forwardedRef) => {\n    const { __scopeDialog, ...titleProps } = props;\n    const context = useDialogContext(TITLE_NAME, __scopeDialog);\n    return <Primitive.h2 id={context.titleId} {...titleProps} ref={forwardedRef} />;\n  }\n);\n\nDialogTitle.displayName = TITLE_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DialogDescription\n * -----------------------------------------------------------------------------------------------*/\n\nconst DESCRIPTION_NAME = 'DialogDescription';\n\ntype DialogDescriptionElement = React.ComponentRef<typeof Primitive.p>;\ntype PrimitiveParagraphProps = React.ComponentPropsWithoutRef<typeof Primitive.p>;\ninterface DialogDescriptionProps extends PrimitiveParagraphProps {}\n\nconst DialogDescription = React.forwardRef<DialogDescriptionElement, DialogDescriptionProps>(\n  (props: ScopedProps<DialogDescriptionProps>, forwardedRef) => {\n    const { __scopeDialog, ...descriptionProps } = props;\n    const context = useDialogContext(DESCRIPTION_NAME, __scopeDialog);\n    return <Primitive.p id={context.descriptionId} {...descriptionProps} ref={forwardedRef} />;\n  }\n);\n\nDialogDescription.displayName = DESCRIPTION_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DialogClose\n * -----------------------------------------------------------------------------------------------*/\n\nconst CLOSE_NAME = 'DialogClose';\n\ntype DialogCloseElement = React.ComponentRef<typeof Primitive.button>;\ninterface DialogCloseProps extends PrimitiveButtonProps {}\n\nconst DialogClose = React.forwardRef<DialogCloseElement, DialogCloseProps>(\n  (props: ScopedProps<DialogCloseProps>, forwardedRef) => {\n    const { __scopeDialog, ...closeProps } = props;\n    const context = useDialogContext(CLOSE_NAME, __scopeDialog);\n    return (\n      <Primitive.button\n        type=\"button\"\n        {...closeProps}\n        ref={forwardedRef}\n        onClick={composeEventHandlers(props.onClick, () => context.onOpenChange(false))}\n      />\n    );\n  }\n);\n\nDialogClose.displayName = CLOSE_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\nfunction getState(open: boolean) {\n  return open ? 'open' : 'closed';\n}\n\nconst TITLE_WARNING_NAME = 'DialogTitleWarning';\n\nconst [WarningProvider, useWarningContext] = createContext(TITLE_WARNING_NAME, {\n  contentName: CONTENT_NAME,\n  titleName: TITLE_NAME,\n  docsSlug: 'dialog',\n});\n\ntype TitleWarningProps = { titleId?: string };\n\nconst TitleWarning: React.FC<TitleWarningProps> = ({ titleId }) => {\n  const titleWarningContext = useWarningContext(TITLE_WARNING_NAME);\n\n  const MESSAGE = `\\`${titleWarningContext.contentName}\\` requires a \\`${titleWarningContext.titleName}\\` for the component to be accessible for screen reader users.\n\nIf you want to hide the \\`${titleWarningContext.titleName}\\`, you can wrap it with our VisuallyHidden component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/${titleWarningContext.docsSlug}`;\n\n  React.useEffect(() => {\n    if (titleId) {\n      const hasTitle = document.getElementById(titleId);\n      if (!hasTitle) console.error(MESSAGE);\n    }\n  }, [MESSAGE, titleId]);\n\n  return null;\n};\n\nconst DESCRIPTION_WARNING_NAME = 'DialogDescriptionWarning';\n\ntype DescriptionWarningProps = {\n  contentRef: React.RefObject<DialogContentElement | null>;\n  descriptionId?: string;\n};\n\nconst DescriptionWarning: React.FC<DescriptionWarningProps> = ({ contentRef, descriptionId }) => {\n  const descriptionWarningContext = useWarningContext(DESCRIPTION_WARNING_NAME);\n  const MESSAGE = `Warning: Missing \\`Description\\` or \\`aria-describedby={undefined}\\` for {${descriptionWarningContext.contentName}}.`;\n\n  React.useEffect(() => {\n    const describedById = contentRef.current?.getAttribute('aria-describedby');\n    // if we have an id and the user hasn't set aria-describedby={undefined}\n    if (descriptionId && describedById) {\n      const hasDescription = document.getElementById(descriptionId);\n      if (!hasDescription) console.warn(MESSAGE);\n    }\n  }, [MESSAGE, contentRef, descriptionId]);\n\n  return null;\n};\n\nconst Root = Dialog;\nconst Trigger = DialogTrigger;\nconst Portal = DialogPortal;\nconst Overlay = DialogOverlay;\nconst Content = DialogContent;\nconst Title = DialogTitle;\nconst Description = DialogDescription;\nconst Close = DialogClose;\n\nexport {\n  createDialogScope,\n  //\n  Dialog,\n  DialogTrigger,\n  DialogPortal,\n  DialogOverlay,\n  DialogContent,\n  DialogTitle,\n  DialogDescription,\n  DialogClose,\n  //\n  Root,\n  Trigger,\n  Portal,\n  Overlay,\n  Content,\n  Title,\n  Description,\n  Close,\n  //\n  WarningProvider,\n};\nexport type {\n  DialogProps,\n  DialogTriggerProps,\n  DialogPortalProps,\n  DialogOverlayProps,\n  DialogContentProps,\n  DialogTitleProps,\n  DialogDescriptionProps,\n  DialogCloseProps,\n};\n", "import * as React from 'react';\n\n/** Number of components which have requested interest to have focus guards */\nlet count = 0;\n\nfunction FocusGuards(props: any) {\n  useFocusGuards();\n  return props.children;\n}\n\n/**\n * Injects a pair of focus guards at the edges of the whole DOM tree\n * to ensure `focusin` & `focusout` events can be caught consistently.\n */\nfunction useFocusGuards() {\n  React.useEffect(() => {\n    const edgeGuards = document.querySelectorAll('[data-radix-focus-guard]');\n    document.body.insertAdjacentElement('afterbegin', edgeGuards[0] ?? createFocusGuard());\n    document.body.insertAdjacentElement('beforeend', edgeGuards[1] ?? createFocusGuard());\n    count++;\n\n    return () => {\n      if (count === 1) {\n        document.querySelectorAll('[data-radix-focus-guard]').forEach((node) => node.remove());\n      }\n      count--;\n    };\n  }, []);\n}\n\nfunction createFocusGuard() {\n  const element = document.createElement('span');\n  element.setAttribute('data-radix-focus-guard', '');\n  element.tabIndex = 0;\n  element.style.outline = 'none';\n  element.style.opacity = '0';\n  element.style.position = 'fixed';\n  element.style.pointerEvents = 'none';\n  return element;\n}\n\nconst Root = FocusGuards;\n\nexport {\n  FocusGuards,\n  //\n  Root,\n  //\n  useFocusGuards,\n};\n", "// packages/react/compose-refs/src/compose-refs.tsx\nimport * as React from \"react\";\nfunction setRef(ref, value) {\n  if (typeof ref === \"function\") {\n    return ref(value);\n  } else if (ref !== null && ref !== void 0) {\n    ref.current = value;\n  }\n}\nfunction composeRefs(...refs) {\n  return (node) => {\n    let hasCleanup = false;\n    const cleanups = refs.map((ref) => {\n      const cleanup = setRef(ref, node);\n      if (!hasCleanup && typeof cleanup == \"function\") {\n        hasCleanup = true;\n      }\n      return cleanup;\n    });\n    if (hasCleanup) {\n      return () => {\n        for (let i = 0; i < cleanups.length; i++) {\n          const cleanup = cleanups[i];\n          if (typeof cleanup == \"function\") {\n            cleanup();\n          } else {\n            setRef(refs[i], null);\n          }\n        }\n      };\n    }\n  };\n}\nfunction useComposedRefs(...refs) {\n  return React.useCallback(composeRefs(...refs), refs);\n}\nexport {\n  composeRefs,\n  useComposedRefs\n};\n//# sourceMappingURL=index.mjs.map\n", "// src/slot.tsx\nimport * as React from \"react\";\nimport { composeRefs } from \"@radix-ui/react-compose-refs\";\nimport { Fragment as Fragment2, jsx } from \"react/jsx-runtime\";\n// @__NO_SIDE_EFFECTS__\nfunction createSlot(ownerName) {\n  const SlotClone = /* @__PURE__ */ createSlotClone(ownerName);\n  const Slot2 = React.forwardRef((props, forwardedRef) => {\n    const { children, ...slotProps } = props;\n    const childrenArray = React.Children.toArray(children);\n    const slottable = childrenArray.find(isSlottable);\n    if (slottable) {\n      const newElement = slottable.props.children;\n      const newChildren = childrenArray.map((child) => {\n        if (child === slottable) {\n          if (React.Children.count(newElement) > 1) return React.Children.only(null);\n          return React.isValidElement(newElement) ? newElement.props.children : null;\n        } else {\n          return child;\n        }\n      });\n      return /* @__PURE__ */ jsx(SlotClone, { ...slotProps, ref: forwardedRef, children: React.isValidElement(newElement) ? React.cloneElement(newElement, void 0, newChildren) : null });\n    }\n    return /* @__PURE__ */ jsx(SlotClone, { ...slotProps, ref: forwardedRef, children });\n  });\n  Slot2.displayName = `${ownerName}.Slot`;\n  return Slot2;\n}\nvar Slot = /* @__PURE__ */ createSlot(\"Slot\");\n// @__NO_SIDE_EFFECTS__\nfunction createSlotClone(ownerName) {\n  const SlotClone = React.forwardRef((props, forwardedRef) => {\n    const { children, ...slotProps } = props;\n    if (React.isValidElement(children)) {\n      const childrenRef = getElementRef(children);\n      const props2 = mergeProps(slotProps, children.props);\n      if (children.type !== React.Fragment) {\n        props2.ref = forwardedRef ? composeRefs(forwardedRef, childrenRef) : childrenRef;\n      }\n      return React.cloneElement(children, props2);\n    }\n    return React.Children.count(children) > 1 ? React.Children.only(null) : null;\n  });\n  SlotClone.displayName = `${ownerName}.SlotClone`;\n  return SlotClone;\n}\nvar SLOTTABLE_IDENTIFIER = Symbol(\"radix.slottable\");\n// @__NO_SIDE_EFFECTS__\nfunction createSlottable(ownerName) {\n  const Slottable2 = ({ children }) => {\n    return /* @__PURE__ */ jsx(Fragment2, { children });\n  };\n  Slottable2.displayName = `${ownerName}.Slottable`;\n  Slottable2.__radixId = SLOTTABLE_IDENTIFIER;\n  return Slottable2;\n}\nvar Slottable = /* @__PURE__ */ createSlottable(\"Slottable\");\nfunction isSlottable(child) {\n  return React.isValidElement(child) && typeof child.type === \"function\" && \"__radixId\" in child.type && child.type.__radixId === SLOTTABLE_IDENTIFIER;\n}\nfunction mergeProps(slotProps, childProps) {\n  const overrideProps = { ...childProps };\n  for (const propName in childProps) {\n    const slotPropValue = slotProps[propName];\n    const childPropValue = childProps[propName];\n    const isHandler = /^on[A-Z]/.test(propName);\n    if (isHandler) {\n      if (slotPropValue && childPropValue) {\n        overrideProps[propName] = (...args) => {\n          const result = childPropValue(...args);\n          slotPropValue(...args);\n          return result;\n        };\n      } else if (slotPropValue) {\n        overrideProps[propName] = slotPropValue;\n      }\n    } else if (propName === \"style\") {\n      overrideProps[propName] = { ...slotPropValue, ...childPropValue };\n    } else if (propName === \"className\") {\n      overrideProps[propName] = [slotPropValue, childPropValue].filter(Boolean).join(\" \");\n    }\n  }\n  return { ...slotProps, ...overrideProps };\n}\nfunction getElementRef(element) {\n  let getter = Object.getOwnPropertyDescriptor(element.props, \"ref\")?.get;\n  let mayWarn = getter && \"isReactWarning\" in getter && getter.isReactWarning;\n  if (mayWarn) {\n    return element.ref;\n  }\n  getter = Object.getOwnPropertyDescriptor(element, \"ref\")?.get;\n  mayWarn = getter && \"isReactWarning\" in getter && getter.isReactWarning;\n  if (mayWarn) {\n    return element.props.ref;\n  }\n  return element.props.ref || element.ref;\n}\nexport {\n  Slot as Root,\n  Slot,\n  Slottable,\n  createSlot,\n  createSlottable\n};\n//# sourceMappingURL=index.mjs.map\n", "// src/primitive.tsx\nimport * as React from \"react\";\nimport * as ReactDOM from \"react-dom\";\nimport { createSlot } from \"@radix-ui/react-slot\";\nimport { jsx } from \"react/jsx-runtime\";\nvar NODES = [\n  \"a\",\n  \"button\",\n  \"div\",\n  \"form\",\n  \"h2\",\n  \"h3\",\n  \"img\",\n  \"input\",\n  \"label\",\n  \"li\",\n  \"nav\",\n  \"ol\",\n  \"p\",\n  \"select\",\n  \"span\",\n  \"svg\",\n  \"ul\"\n];\nvar Primitive = NODES.reduce((primitive, node) => {\n  const Slot = createSlot(`Primitive.${node}`);\n  const Node = React.forwardRef((props, forwardedRef) => {\n    const { asChild, ...primitiveProps } = props;\n    const Comp = asChild ? Slot : node;\n    if (typeof window !== \"undefined\") {\n      window[Symbol.for(\"radix-ui\")] = true;\n    }\n    return /* @__PURE__ */ jsx(Comp, { ...primitiveProps, ref: forwardedRef });\n  });\n  Node.displayName = `Primitive.${node}`;\n  return { ...primitive, [node]: Node };\n}, {});\nfunction dispatchDiscreteCustomEvent(target, event) {\n  if (target) ReactDOM.flushSync(() => target.dispatchEvent(event));\n}\nvar Root = Primitive;\nexport {\n  Primitive,\n  Root,\n  dispatchDiscreteCustomEvent\n};\n//# sourceMappingURL=index.mjs.map\n", "// packages/react/use-callback-ref/src/use-callback-ref.tsx\nimport * as React from \"react\";\nfunction useCallbackRef(callback) {\n  const callbackRef = React.useRef(callback);\n  React.useEffect(() => {\n    callbackRef.current = callback;\n  });\n  return React.useMemo(() => (...args) => callbackRef.current?.(...args), []);\n}\nexport {\n  useCallbackRef\n};\n//# sourceMappingURL=index.mjs.map\n", "import * as React from 'react';\nimport { useComposedRefs } from '@radix-ui/react-compose-refs';\nimport { Primitive } from '@radix-ui/react-primitive';\nimport { useCallbackRef } from '@radix-ui/react-use-callback-ref';\n\nconst AUTOFOCUS_ON_MOUNT = 'focusScope.autoFocusOnMount';\nconst AUTOFOCUS_ON_UNMOUNT = 'focusScope.autoFocusOnUnmount';\nconst EVENT_OPTIONS = { bubbles: false, cancelable: true };\n\ntype FocusableTarget = HTMLElement | { focus(): void };\n\n/* -------------------------------------------------------------------------------------------------\n * FocusScope\n * -----------------------------------------------------------------------------------------------*/\n\nconst FOCUS_SCOPE_NAME = 'FocusScope';\n\ntype FocusScopeElement = React.ComponentRef<typeof Primitive.div>;\ntype PrimitiveDivProps = React.ComponentPropsWithoutRef<typeof Primitive.div>;\ninterface FocusScopeProps extends PrimitiveDivProps {\n  /**\n   * When `true`, tabbing from last item will focus first tabbable\n   * and shift+tab from first item will focus last tababble.\n   * @defaultValue false\n   */\n  loop?: boolean;\n\n  /**\n   * When `true`, focus cannot escape the focus scope via keyboard,\n   * pointer, or a programmatic focus.\n   * @defaultValue false\n   */\n  trapped?: boolean;\n\n  /**\n   * Event handler called when auto-focusing on mount.\n   * Can be prevented.\n   */\n  onMountAutoFocus?: (event: Event) => void;\n\n  /**\n   * Event handler called when auto-focusing on unmount.\n   * Can be prevented.\n   */\n  onUnmountAutoFocus?: (event: Event) => void;\n}\n\nconst FocusScope = React.forwardRef<FocusScopeElement, FocusScopeProps>((props, forwardedRef) => {\n  const {\n    loop = false,\n    trapped = false,\n    onMountAutoFocus: onMountAutoFocusProp,\n    onUnmountAutoFocus: onUnmountAutoFocusProp,\n    ...scopeProps\n  } = props;\n  const [container, setContainer] = React.useState<HTMLElement | null>(null);\n  const onMountAutoFocus = useCallbackRef(onMountAutoFocusProp);\n  const onUnmountAutoFocus = useCallbackRef(onUnmountAutoFocusProp);\n  const lastFocusedElementRef = React.useRef<HTMLElement | null>(null);\n  const composedRefs = useComposedRefs(forwardedRef, (node) => setContainer(node));\n\n  const focusScope = React.useRef({\n    paused: false,\n    pause() {\n      this.paused = true;\n    },\n    resume() {\n      this.paused = false;\n    },\n  }).current;\n\n  // Takes care of trapping focus if focus is moved outside programmatically for example\n  React.useEffect(() => {\n    if (trapped) {\n      function handleFocusIn(event: FocusEvent) {\n        if (focusScope.paused || !container) return;\n        const target = event.target as HTMLElement | null;\n        if (container.contains(target)) {\n          lastFocusedElementRef.current = target;\n        } else {\n          focus(lastFocusedElementRef.current, { select: true });\n        }\n      }\n\n      function handleFocusOut(event: FocusEvent) {\n        if (focusScope.paused || !container) return;\n        const relatedTarget = event.relatedTarget as HTMLElement | null;\n\n        // A `focusout` event with a `null` `relatedTarget` will happen in at least two cases:\n        //\n        // 1. When the user switches app/tabs/windows/the browser itself loses focus.\n        // 2. In Google Chrome, when the focused element is removed from the DOM.\n        //\n        // We let the browser do its thing here because:\n        //\n        // 1. The browser already keeps a memory of what's focused for when the page gets refocused.\n        // 2. In Google Chrome, if we try to focus the deleted focused element (as per below), it\n        //    throws the CPU to 100%, so we avoid doing anything for this reason here too.\n        if (relatedTarget === null) return;\n\n        // If the focus has moved to an actual legitimate element (`relatedTarget !== null`)\n        // that is outside the container, we move focus to the last valid focused element inside.\n        if (!container.contains(relatedTarget)) {\n          focus(lastFocusedElementRef.current, { select: true });\n        }\n      }\n\n      // When the focused element gets removed from the DOM, browsers move focus\n      // back to the document.body. In this case, we move focus to the container\n      // to keep focus trapped correctly.\n      function handleMutations(mutations: MutationRecord[]) {\n        const focusedElement = document.activeElement as HTMLElement | null;\n        if (focusedElement !== document.body) return;\n        for (const mutation of mutations) {\n          if (mutation.removedNodes.length > 0) focus(container);\n        }\n      }\n\n      document.addEventListener('focusin', handleFocusIn);\n      document.addEventListener('focusout', handleFocusOut);\n      const mutationObserver = new MutationObserver(handleMutations);\n      if (container) mutationObserver.observe(container, { childList: true, subtree: true });\n\n      return () => {\n        document.removeEventListener('focusin', handleFocusIn);\n        document.removeEventListener('focusout', handleFocusOut);\n        mutationObserver.disconnect();\n      };\n    }\n  }, [trapped, container, focusScope.paused]);\n\n  React.useEffect(() => {\n    if (container) {\n      focusScopesStack.add(focusScope);\n      const previouslyFocusedElement = document.activeElement as HTMLElement | null;\n      const hasFocusedCandidate = container.contains(previouslyFocusedElement);\n\n      if (!hasFocusedCandidate) {\n        const mountEvent = new CustomEvent(AUTOFOCUS_ON_MOUNT, EVENT_OPTIONS);\n        container.addEventListener(AUTOFOCUS_ON_MOUNT, onMountAutoFocus);\n        container.dispatchEvent(mountEvent);\n        if (!mountEvent.defaultPrevented) {\n          focusFirst(removeLinks(getTabbableCandidates(container)), { select: true });\n          if (document.activeElement === previouslyFocusedElement) {\n            focus(container);\n          }\n        }\n      }\n\n      return () => {\n        container.removeEventListener(AUTOFOCUS_ON_MOUNT, onMountAutoFocus);\n\n        // We hit a react bug (fixed in v17) with focusing in unmount.\n        // We need to delay the focus a little to get around it for now.\n        // See: https://github.com/facebook/react/issues/17894\n        setTimeout(() => {\n          const unmountEvent = new CustomEvent(AUTOFOCUS_ON_UNMOUNT, EVENT_OPTIONS);\n          container.addEventListener(AUTOFOCUS_ON_UNMOUNT, onUnmountAutoFocus);\n          container.dispatchEvent(unmountEvent);\n          if (!unmountEvent.defaultPrevented) {\n            focus(previouslyFocusedElement ?? document.body, { select: true });\n          }\n          // we need to remove the listener after we `dispatchEvent`\n          container.removeEventListener(AUTOFOCUS_ON_UNMOUNT, onUnmountAutoFocus);\n\n          focusScopesStack.remove(focusScope);\n        }, 0);\n      };\n    }\n  }, [container, onMountAutoFocus, onUnmountAutoFocus, focusScope]);\n\n  // Takes care of looping focus (when tabbing whilst at the edges)\n  const handleKeyDown = React.useCallback(\n    (event: React.KeyboardEvent) => {\n      if (!loop && !trapped) return;\n      if (focusScope.paused) return;\n\n      const isTabKey = event.key === 'Tab' && !event.altKey && !event.ctrlKey && !event.metaKey;\n      const focusedElement = document.activeElement as HTMLElement | null;\n\n      if (isTabKey && focusedElement) {\n        const container = event.currentTarget as HTMLElement;\n        const [first, last] = getTabbableEdges(container);\n        const hasTabbableElementsInside = first && last;\n\n        // we can only wrap focus if we have tabbable edges\n        if (!hasTabbableElementsInside) {\n          if (focusedElement === container) event.preventDefault();\n        } else {\n          if (!event.shiftKey && focusedElement === last) {\n            event.preventDefault();\n            if (loop) focus(first, { select: true });\n          } else if (event.shiftKey && focusedElement === first) {\n            event.preventDefault();\n            if (loop) focus(last, { select: true });\n          }\n        }\n      }\n    },\n    [loop, trapped, focusScope.paused]\n  );\n\n  return (\n    <Primitive.div tabIndex={-1} {...scopeProps} ref={composedRefs} onKeyDown={handleKeyDown} />\n  );\n});\n\nFocusScope.displayName = FOCUS_SCOPE_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * Utils\n * -----------------------------------------------------------------------------------------------*/\n\n/**\n * Attempts focusing the first element in a list of candidates.\n * Stops when focus has actually moved.\n */\nfunction focusFirst(candidates: HTMLElement[], { select = false } = {}) {\n  const previouslyFocusedElement = document.activeElement;\n  for (const candidate of candidates) {\n    focus(candidate, { select });\n    if (document.activeElement !== previouslyFocusedElement) return;\n  }\n}\n\n/**\n * Returns the first and last tabbable elements inside a container.\n */\nfunction getTabbableEdges(container: HTMLElement) {\n  const candidates = getTabbableCandidates(container);\n  const first = findVisible(candidates, container);\n  const last = findVisible(candidates.reverse(), container);\n  return [first, last] as const;\n}\n\n/**\n * Returns a list of potential tabbable candidates.\n *\n * NOTE: This is only a close approximation. For example it doesn't take into account cases like when\n * elements are not visible. This cannot be worked out easily by just reading a property, but rather\n * necessitate runtime knowledge (computed styles, etc). We deal with these cases separately.\n *\n * See: https://developer.mozilla.org/en-US/docs/Web/API/TreeWalker\n * Credit: https://github.com/discord/focus-layers/blob/master/src/util/wrapFocus.tsx#L1\n */\nfunction getTabbableCandidates(container: HTMLElement) {\n  const nodes: HTMLElement[] = [];\n  const walker = document.createTreeWalker(container, NodeFilter.SHOW_ELEMENT, {\n    acceptNode: (node: any) => {\n      const isHiddenInput = node.tagName === 'INPUT' && node.type === 'hidden';\n      if (node.disabled || node.hidden || isHiddenInput) return NodeFilter.FILTER_SKIP;\n      // `.tabIndex` is not the same as the `tabindex` attribute. It works on the\n      // runtime's understanding of tabbability, so this automatically accounts\n      // for any kind of element that could be tabbed to.\n      return node.tabIndex >= 0 ? NodeFilter.FILTER_ACCEPT : NodeFilter.FILTER_SKIP;\n    },\n  });\n  while (walker.nextNode()) nodes.push(walker.currentNode as HTMLElement);\n  // we do not take into account the order of nodes with positive `tabIndex` as it\n  // hinders accessibility to have tab order different from visual order.\n  return nodes;\n}\n\n/**\n * Returns the first visible element in a list.\n * NOTE: Only checks visibility up to the `container`.\n */\nfunction findVisible(elements: HTMLElement[], container: HTMLElement) {\n  for (const element of elements) {\n    // we stop checking if it's hidden at the `container` level (excluding)\n    if (!isHidden(element, { upTo: container })) return element;\n  }\n}\n\nfunction isHidden(node: HTMLElement, { upTo }: { upTo?: HTMLElement }) {\n  if (getComputedStyle(node).visibility === 'hidden') return true;\n  while (node) {\n    // we stop at `upTo` (excluding it)\n    if (upTo !== undefined && node === upTo) return false;\n    if (getComputedStyle(node).display === 'none') return true;\n    node = node.parentElement as HTMLElement;\n  }\n  return false;\n}\n\nfunction isSelectableInput(element: any): element is FocusableTarget & { select: () => void } {\n  return element instanceof HTMLInputElement && 'select' in element;\n}\n\nfunction focus(element?: FocusableTarget | null, { select = false } = {}) {\n  // only focus if that element is focusable\n  if (element && element.focus) {\n    const previouslyFocusedElement = document.activeElement;\n    // NOTE: we prevent scrolling on focus, to minimize jarring transitions for users\n    element.focus({ preventScroll: true });\n    // only select if its not the same element, it supports selection and we need to select\n    if (element !== previouslyFocusedElement && isSelectableInput(element) && select)\n      element.select();\n  }\n}\n\n/* -------------------------------------------------------------------------------------------------\n * FocusScope stack\n * -----------------------------------------------------------------------------------------------*/\n\ntype FocusScopeAPI = { paused: boolean; pause(): void; resume(): void };\nconst focusScopesStack = createFocusScopesStack();\n\nfunction createFocusScopesStack() {\n  /** A stack of focus scopes, with the active one at the top */\n  let stack: FocusScopeAPI[] = [];\n\n  return {\n    add(focusScope: FocusScopeAPI) {\n      // pause the currently active focus scope (at the top of the stack)\n      const activeFocusScope = stack[0];\n      if (focusScope !== activeFocusScope) {\n        activeFocusScope?.pause();\n      }\n      // remove in case it already exists (because we'll re-add it at the top of the stack)\n      stack = arrayRemove(stack, focusScope);\n      stack.unshift(focusScope);\n    },\n\n    remove(focusScope: FocusScopeAPI) {\n      stack = arrayRemove(stack, focusScope);\n      stack[0]?.resume();\n    },\n  };\n}\n\nfunction arrayRemove<T>(array: T[], item: T) {\n  const updatedArray = [...array];\n  const index = updatedArray.indexOf(item);\n  if (index !== -1) {\n    updatedArray.splice(index, 1);\n  }\n  return updatedArray;\n}\n\nfunction removeLinks(items: HTMLElement[]) {\n  return items.filter((item) => item.tagName !== 'A');\n}\n\nconst Root = FocusScope;\n\nexport {\n  FocusScope,\n  //\n  Root,\n};\nexport type { FocusScopeProps };\n", "import * as React from 'react';\nimport { Primitive } from '@radix-ui/react-primitive';\n\n/* -------------------------------------------------------------------------------------------------\n * Label\n * -----------------------------------------------------------------------------------------------*/\n\nconst NAME = 'Label';\n\ntype LabelElement = React.ElementRef<typeof Primitive.label>;\ntype PrimitiveLabelProps = React.ComponentPropsWithoutRef<typeof Primitive.label>;\ninterface LabelProps extends PrimitiveLabelProps {}\n\nconst Label = React.forwardRef<LabelElement, LabelProps>((props, forwardedRef) => {\n  return (\n    <Primitive.label\n      {...props}\n      ref={forwardedRef}\n      onMouseDown={(event) => {\n        // only prevent text selection if clicking inside the label itself\n        const target = event.target as HTMLElement;\n        if (target.closest('button, input, select, textarea')) return;\n\n        props.onMouseDown?.(event);\n        // prevent text selection when double clicking label\n        if (!event.defaultPrevented && event.detail > 1) event.preventDefault();\n      }}\n    />\n  );\n});\n\nLabel.displayName = NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\nconst Root = Label;\n\nexport {\n  Label,\n  //\n  Root,\n};\nexport type { LabelProps };\n", "// packages/core/number/src/number.ts\nfunction clamp(value, [min, max]) {\n  return Math.min(max, Math.max(min, value));\n}\nexport {\n  clamp\n};\n//# sourceMappingURL=index.mjs.map\n", "// packages/core/primitive/src/primitive.tsx\nfunction composeEventHandlers(originalEventHandler, ourEventHandler, { checkForDefaultPrevented = true } = {}) {\n  return function handleEvent(event) {\n    originalEventHandler?.(event);\n    if (checkForDefaultPrevented === false || !event.defaultPrevented) {\n      return ourEventHandler?.(event);\n    }\n  };\n}\nexport {\n  composeEventHandlers\n};\n//# sourceMappingURL=index.mjs.map\n", "// packages/react/context/src/create-context.tsx\nimport * as React from \"react\";\nimport { jsx } from \"react/jsx-runtime\";\nfunction createContext2(rootComponentName, defaultContext) {\n  const Context = React.createContext(defaultContext);\n  const Provider = (props) => {\n    const { children, ...context } = props;\n    const value = React.useMemo(() => context, Object.values(context));\n    return /* @__PURE__ */ jsx(Context.Provider, { value, children });\n  };\n  Provider.displayName = rootComponentName + \"Provider\";\n  function useContext2(consumerName) {\n    const context = React.useContext(Context);\n    if (context) return context;\n    if (defaultContext !== void 0) return defaultContext;\n    throw new Error(`\\`${consumerName}\\` must be used within \\`${rootComponentName}\\``);\n  }\n  return [Provider, useContext2];\n}\nfunction createContextScope(scopeName, createContextScopeDeps = []) {\n  let defaultContexts = [];\n  function createContext3(rootComponentName, defaultContext) {\n    const BaseContext = React.createContext(defaultContext);\n    const index = defaultContexts.length;\n    defaultContexts = [...defaultContexts, defaultContext];\n    const Provider = (props) => {\n      const { scope, children, ...context } = props;\n      const Context = scope?.[scopeName]?.[index] || BaseContext;\n      const value = React.useMemo(() => context, Object.values(context));\n      return /* @__PURE__ */ jsx(Context.Provider, { value, children });\n    };\n    Provider.displayName = rootComponentName + \"Provider\";\n    function useContext2(consumerName, scope) {\n      const Context = scope?.[scopeName]?.[index] || BaseContext;\n      const context = React.useContext(Context);\n      if (context) return context;\n      if (defaultContext !== void 0) return defaultContext;\n      throw new Error(`\\`${consumerName}\\` must be used within \\`${rootComponentName}\\``);\n    }\n    return [Provider, useContext2];\n  }\n  const createScope = () => {\n    const scopeContexts = defaultContexts.map((defaultContext) => {\n      return React.createContext(defaultContext);\n    });\n    return function useScope(scope) {\n      const contexts = scope?.[scopeName] || scopeContexts;\n      return React.useMemo(\n        () => ({ [`__scope${scopeName}`]: { ...scope, [scopeName]: contexts } }),\n        [scope, contexts]\n      );\n    };\n  };\n  createScope.scopeName = scopeName;\n  return [createContext3, composeContextScopes(createScope, ...createContextScopeDeps)];\n}\nfunction composeContextScopes(...scopes) {\n  const baseScope = scopes[0];\n  if (scopes.length === 1) return baseScope;\n  const createScope = () => {\n    const scopeHooks = scopes.map((createScope2) => ({\n      useScope: createScope2(),\n      scopeName: createScope2.scopeName\n    }));\n    return function useComposedScopes(overrideScopes) {\n      const nextScopes = scopeHooks.reduce((nextScopes2, { useScope, scopeName }) => {\n        const scopeProps = useScope(overrideScopes);\n        const currentScope = scopeProps[`__scope${scopeName}`];\n        return { ...nextScopes2, ...currentScope };\n      }, {});\n      return React.useMemo(() => ({ [`__scope${baseScope.scopeName}`]: nextScopes }), [nextScopes]);\n    };\n  };\n  createScope.scopeName = baseScope.scopeName;\n  return createScope;\n}\nexport {\n  createContext2 as createContext,\n  createContextScope\n};\n//# sourceMappingURL=index.mjs.map\n", "// packages/react/compose-refs/src/compose-refs.tsx\nimport * as React from \"react\";\nfunction setRef(ref, value) {\n  if (typeof ref === \"function\") {\n    return ref(value);\n  } else if (ref !== null && ref !== void 0) {\n    ref.current = value;\n  }\n}\nfunction composeRefs(...refs) {\n  return (node) => {\n    let hasCleanup = false;\n    const cleanups = refs.map((ref) => {\n      const cleanup = setRef(ref, node);\n      if (!hasCleanup && typeof cleanup == \"function\") {\n        hasCleanup = true;\n      }\n      return cleanup;\n    });\n    if (hasCleanup) {\n      return () => {\n        for (let i = 0; i < cleanups.length; i++) {\n          const cleanup = cleanups[i];\n          if (typeof cleanup == \"function\") {\n            cleanup();\n          } else {\n            setRef(refs[i], null);\n          }\n        }\n      };\n    }\n  };\n}\nfunction useComposedRefs(...refs) {\n  return React.useCallback(composeRefs(...refs), refs);\n}\nexport {\n  composeRefs,\n  useComposedRefs\n};\n//# sourceMappingURL=index.mjs.map\n", "// src/slot.tsx\nimport * as React from \"react\";\nimport { composeRefs } from \"@radix-ui/react-compose-refs\";\nimport { Fragment as Fragment2, jsx } from \"react/jsx-runtime\";\n// @__NO_SIDE_EFFECTS__\nfunction createSlot(ownerName) {\n  const SlotClone = /* @__PURE__ */ createSlotClone(ownerName);\n  const Slot2 = React.forwardRef((props, forwardedRef) => {\n    const { children, ...slotProps } = props;\n    const childrenArray = React.Children.toArray(children);\n    const slottable = childrenArray.find(isSlottable);\n    if (slottable) {\n      const newElement = slottable.props.children;\n      const newChildren = childrenArray.map((child) => {\n        if (child === slottable) {\n          if (React.Children.count(newElement) > 1) return React.Children.only(null);\n          return React.isValidElement(newElement) ? newElement.props.children : null;\n        } else {\n          return child;\n        }\n      });\n      return /* @__PURE__ */ jsx(SlotClone, { ...slotProps, ref: forwardedRef, children: React.isValidElement(newElement) ? React.cloneElement(newElement, void 0, newChildren) : null });\n    }\n    return /* @__PURE__ */ jsx(SlotClone, { ...slotProps, ref: forwardedRef, children });\n  });\n  Slot2.displayName = `${ownerName}.Slot`;\n  return Slot2;\n}\nvar Slot = /* @__PURE__ */ createSlot(\"Slot\");\n// @__NO_SIDE_EFFECTS__\nfunction createSlotClone(ownerName) {\n  const SlotClone = React.forwardRef((props, forwardedRef) => {\n    const { children, ...slotProps } = props;\n    if (React.isValidElement(children)) {\n      const childrenRef = getElementRef(children);\n      const props2 = mergeProps(slotProps, children.props);\n      if (children.type !== React.Fragment) {\n        props2.ref = forwardedRef ? composeRefs(forwardedRef, childrenRef) : childrenRef;\n      }\n      return React.cloneElement(children, props2);\n    }\n    return React.Children.count(children) > 1 ? React.Children.only(null) : null;\n  });\n  SlotClone.displayName = `${ownerName}.SlotClone`;\n  return SlotClone;\n}\nvar SLOTTABLE_IDENTIFIER = Symbol(\"radix.slottable\");\n// @__NO_SIDE_EFFECTS__\nfunction createSlottable(ownerName) {\n  const Slottable2 = ({ children }) => {\n    return /* @__PURE__ */ jsx(Fragment2, { children });\n  };\n  Slottable2.displayName = `${ownerName}.Slottable`;\n  Slottable2.__radixId = SLOTTABLE_IDENTIFIER;\n  return Slottable2;\n}\nvar Slottable = /* @__PURE__ */ createSlottable(\"Slottable\");\nfunction isSlottable(child) {\n  return React.isValidElement(child) && typeof child.type === \"function\" && \"__radixId\" in child.type && child.type.__radixId === SLOTTABLE_IDENTIFIER;\n}\nfunction mergeProps(slotProps, childProps) {\n  const overrideProps = { ...childProps };\n  for (const propName in childProps) {\n    const slotPropValue = slotProps[propName];\n    const childPropValue = childProps[propName];\n    const isHandler = /^on[A-Z]/.test(propName);\n    if (isHandler) {\n      if (slotPropValue && childPropValue) {\n        overrideProps[propName] = (...args) => {\n          const result = childPropValue(...args);\n          slotPropValue(...args);\n          return result;\n        };\n      } else if (slotPropValue) {\n        overrideProps[propName] = slotPropValue;\n      }\n    } else if (propName === \"style\") {\n      overrideProps[propName] = { ...slotPropValue, ...childPropValue };\n    } else if (propName === \"className\") {\n      overrideProps[propName] = [slotPropValue, childPropValue].filter(Boolean).join(\" \");\n    }\n  }\n  return { ...slotProps, ...overrideProps };\n}\nfunction getElementRef(element) {\n  let getter = Object.getOwnPropertyDescriptor(element.props, \"ref\")?.get;\n  let mayWarn = getter && \"isReactWarning\" in getter && getter.isReactWarning;\n  if (mayWarn) {\n    return element.ref;\n  }\n  getter = Object.getOwnPropertyDescriptor(element, \"ref\")?.get;\n  mayWarn = getter && \"isReactWarning\" in getter && getter.isReactWarning;\n  if (mayWarn) {\n    return element.props.ref;\n  }\n  return element.props.ref || element.ref;\n}\nexport {\n  Slot as Root,\n  Slot,\n  Slottable,\n  createSlot,\n  createSlottable\n};\n//# sourceMappingURL=index.mjs.map\n", "import React from 'react';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { useComposedRefs } from '@radix-ui/react-compose-refs';\nimport { createSlot, type Slot } from '@radix-ui/react-slot';\n\ntype SlotProps = React.ComponentPropsWithoutRef<typeof Slot>;\ntype CollectionElement = HTMLElement;\ninterface CollectionProps extends SlotProps {\n  scope: any;\n}\n\n// We have resorted to returning slots directly rather than exposing primitives that can then\n// be slotted like `<CollectionItem as={Slot}>…</CollectionItem>`.\n// This is because we encountered issues with generic types that cannot be statically analysed\n// due to creating them dynamically via createCollection.\n\nfunction createCollection<ItemElement extends HTMLElement, ItemData = {}>(name: string) {\n  /* -----------------------------------------------------------------------------------------------\n   * CollectionProvider\n   * ---------------------------------------------------------------------------------------------*/\n\n  const PROVIDER_NAME = name + 'CollectionProvider';\n  const [createCollectionContext, createCollectionScope] = createContextScope(PROVIDER_NAME);\n\n  type ContextValue = {\n    collectionRef: React.RefObject<CollectionElement | null>;\n    itemMap: Map<\n      React.RefObject<ItemElement | null>,\n      { ref: React.RefObject<ItemElement | null> } & ItemData\n    >;\n  };\n\n  const [CollectionProviderImpl, useCollectionContext] = createCollectionContext<ContextValue>(\n    PROVIDER_NAME,\n    { collectionRef: { current: null }, itemMap: new Map() }\n  );\n\n  const CollectionProvider: React.FC<{ children?: React.ReactNode; scope: any }> = (props) => {\n    const { scope, children } = props;\n    const ref = React.useRef<CollectionElement>(null);\n    const itemMap = React.useRef<ContextValue['itemMap']>(new Map()).current;\n    return (\n      <CollectionProviderImpl scope={scope} itemMap={itemMap} collectionRef={ref}>\n        {children}\n      </CollectionProviderImpl>\n    );\n  };\n\n  CollectionProvider.displayName = PROVIDER_NAME;\n\n  /* -----------------------------------------------------------------------------------------------\n   * CollectionSlot\n   * ---------------------------------------------------------------------------------------------*/\n\n  const COLLECTION_SLOT_NAME = name + 'CollectionSlot';\n\n  const CollectionSlotImpl = createSlot(COLLECTION_SLOT_NAME);\n  const CollectionSlot = React.forwardRef<CollectionElement, CollectionProps>(\n    (props, forwardedRef) => {\n      const { scope, children } = props;\n      const context = useCollectionContext(COLLECTION_SLOT_NAME, scope);\n      const composedRefs = useComposedRefs(forwardedRef, context.collectionRef);\n      return <CollectionSlotImpl ref={composedRefs}>{children}</CollectionSlotImpl>;\n    }\n  );\n\n  CollectionSlot.displayName = COLLECTION_SLOT_NAME;\n\n  /* -----------------------------------------------------------------------------------------------\n   * CollectionItem\n   * ---------------------------------------------------------------------------------------------*/\n\n  const ITEM_SLOT_NAME = name + 'CollectionItemSlot';\n  const ITEM_DATA_ATTR = 'data-radix-collection-item';\n\n  type CollectionItemSlotProps = ItemData & {\n    children: React.ReactNode;\n    scope: any;\n  };\n\n  const CollectionItemSlotImpl = createSlot(ITEM_SLOT_NAME);\n  const CollectionItemSlot = React.forwardRef<ItemElement, CollectionItemSlotProps>(\n    (props, forwardedRef) => {\n      const { scope, children, ...itemData } = props;\n      const ref = React.useRef<ItemElement>(null);\n      const composedRefs = useComposedRefs(forwardedRef, ref);\n      const context = useCollectionContext(ITEM_SLOT_NAME, scope);\n\n      React.useEffect(() => {\n        context.itemMap.set(ref, { ref, ...(itemData as unknown as ItemData) });\n        return () => void context.itemMap.delete(ref);\n      });\n\n      return (\n        <CollectionItemSlotImpl {...{ [ITEM_DATA_ATTR]: '' }} ref={composedRefs}>\n          {children}\n        </CollectionItemSlotImpl>\n      );\n    }\n  );\n\n  CollectionItemSlot.displayName = ITEM_SLOT_NAME;\n\n  /* -----------------------------------------------------------------------------------------------\n   * useCollection\n   * ---------------------------------------------------------------------------------------------*/\n\n  function useCollection(scope: any) {\n    const context = useCollectionContext(name + 'CollectionConsumer', scope);\n\n    const getItems = React.useCallback(() => {\n      const collectionNode = context.collectionRef.current;\n      if (!collectionNode) return [];\n      const orderedNodes = Array.from(collectionNode.querySelectorAll(`[${ITEM_DATA_ATTR}]`));\n      const items = Array.from(context.itemMap.values());\n      const orderedItems = items.sort(\n        (a, b) => orderedNodes.indexOf(a.ref.current!) - orderedNodes.indexOf(b.ref.current!)\n      );\n      return orderedItems;\n    }, [context.collectionRef, context.itemMap]);\n\n    return getItems;\n  }\n\n  return [\n    { Provider: CollectionProvider, Slot: CollectionSlot, ItemSlot: CollectionItemSlot },\n    useCollection,\n    createCollectionScope,\n  ] as const;\n}\n\nexport { createCollection };\nexport type { CollectionProps };\n", "import React from 'react';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { useComposedRefs } from '@radix-ui/react-compose-refs';\nimport { createSlot, type Slot } from '@radix-ui/react-slot';\nimport type { EntryOf } from './ordered-dictionary';\nimport { OrderedDict } from './ordered-dictionary';\n\ntype SlotProps = React.ComponentPropsWithoutRef<typeof Slot>;\ntype CollectionElement = HTMLElement;\ninterface CollectionProps extends SlotProps {\n  scope: any;\n}\n\ninterface BaseItemData {\n  id?: string;\n}\n\ntype ItemDataWithElement<\n  ItemData extends BaseItemData,\n  ItemElement extends HTMLElement,\n> = ItemData & {\n  element: ItemElement;\n};\n\ntype ItemMap<ItemElement extends HTMLElement, ItemData extends BaseItemData> = OrderedDict<\n  ItemElement,\n  ItemDataWithElement<ItemData, ItemElement>\n>;\n\nfunction createCollection<\n  ItemElement extends HTMLElement,\n  ItemData extends BaseItemData = BaseItemData,\n>(name: string) {\n  /* -----------------------------------------------------------------------------------------------\n   * CollectionProvider\n   * ---------------------------------------------------------------------------------------------*/\n\n  const PROVIDER_NAME = name + 'CollectionProvider';\n  const [createCollectionContext, createCollectionScope] = createContextScope(PROVIDER_NAME);\n\n  type ContextValue = {\n    collectionElement: CollectionElement | null;\n    collectionRef: React.Ref<CollectionElement | null>;\n    collectionRefObject: React.RefObject<CollectionElement | null>;\n    itemMap: ItemMap<ItemElement, ItemData>;\n    setItemMap: React.Dispatch<React.SetStateAction<ItemMap<ItemElement, ItemData>>>;\n  };\n\n  const [CollectionContextProvider, useCollectionContext] = createCollectionContext<ContextValue>(\n    PROVIDER_NAME,\n    {\n      collectionElement: null,\n      collectionRef: { current: null },\n      collectionRefObject: { current: null },\n      itemMap: new OrderedDict(),\n      setItemMap: () => void 0,\n    }\n  );\n\n  type CollectionState = [\n    ItemMap: ItemMap<ItemElement, ItemData>,\n    SetItemMap: React.Dispatch<React.SetStateAction<ItemMap<ItemElement, ItemData>>>,\n  ];\n\n  const CollectionProvider: React.FC<{\n    children?: React.ReactNode;\n    scope: any;\n    state?: CollectionState;\n  }> = ({ state, ...props }) => {\n    return state ? (\n      <CollectionProviderImpl {...props} state={state} />\n    ) : (\n      <CollectionInit {...props} />\n    );\n  };\n  CollectionProvider.displayName = PROVIDER_NAME;\n\n  const CollectionInit: React.FC<{\n    children?: React.ReactNode;\n    scope: any;\n  }> = (props) => {\n    const state = useInitCollection();\n    return <CollectionProviderImpl {...props} state={state} />;\n  };\n  CollectionInit.displayName = PROVIDER_NAME + 'Init';\n\n  const CollectionProviderImpl: React.FC<{\n    children?: React.ReactNode;\n    scope: any;\n    state: CollectionState;\n  }> = (props) => {\n    const { scope, children, state } = props;\n    const ref = React.useRef<CollectionElement>(null);\n    const [collectionElement, setCollectionElement] = React.useState<CollectionElement | null>(\n      null\n    );\n    const composeRefs = useComposedRefs(ref, setCollectionElement);\n    const [itemMap, setItemMap] = state;\n\n    React.useEffect(() => {\n      if (!collectionElement) return;\n\n      const observer = getChildListObserver(() => {\n        // setItemMap((map) => {\n        //   const copy = new OrderedDict(map).toSorted(([, a], [, b]) =>\n        //     !a.element || !b.element ? 0 : isElementPreceding(a.element, b.element) ? -1 : 1\n        //   );\n        //   // check if the order has changed\n        //   let index = -1;\n        //   for (const entry of copy) {\n        //     index++;\n        //     const key = map.keyAt(index)!;\n        //     const [copyKey] = entry;\n        //     if (key !== copyKey) {\n        //       // order has changed!\n        //       return copy;\n        //     }\n        //   }\n        //   return map;\n        // });\n      });\n      observer.observe(collectionElement, {\n        childList: true,\n        subtree: true,\n      });\n      return () => {\n        observer.disconnect();\n      };\n    }, [collectionElement]);\n\n    return (\n      <CollectionContextProvider\n        scope={scope}\n        itemMap={itemMap}\n        setItemMap={setItemMap}\n        collectionRef={composeRefs}\n        collectionRefObject={ref}\n        collectionElement={collectionElement}\n      >\n        {children}\n      </CollectionContextProvider>\n    );\n  };\n\n  CollectionProviderImpl.displayName = PROVIDER_NAME + 'Impl';\n\n  /* -----------------------------------------------------------------------------------------------\n   * CollectionSlot\n   * ---------------------------------------------------------------------------------------------*/\n\n  const COLLECTION_SLOT_NAME = name + 'CollectionSlot';\n\n  const CollectionSlotImpl = createSlot(COLLECTION_SLOT_NAME);\n  const CollectionSlot = React.forwardRef<CollectionElement, CollectionProps>(\n    (props, forwardedRef) => {\n      const { scope, children } = props;\n      const context = useCollectionContext(COLLECTION_SLOT_NAME, scope);\n      const composedRefs = useComposedRefs(forwardedRef, context.collectionRef);\n      return <CollectionSlotImpl ref={composedRefs}>{children}</CollectionSlotImpl>;\n    }\n  );\n\n  CollectionSlot.displayName = COLLECTION_SLOT_NAME;\n\n  /* -----------------------------------------------------------------------------------------------\n   * CollectionItem\n   * ---------------------------------------------------------------------------------------------*/\n\n  const ITEM_SLOT_NAME = name + 'CollectionItemSlot';\n  const ITEM_DATA_ATTR = 'data-radix-collection-item';\n\n  type CollectionItemSlotProps = ItemData & {\n    children: React.ReactNode;\n    scope: any;\n  };\n\n  const CollectionItemSlotImpl = createSlot(ITEM_SLOT_NAME);\n  const CollectionItemSlot = React.forwardRef<ItemElement, CollectionItemSlotProps>(\n    (props, forwardedRef) => {\n      const { scope, children, ...itemData } = props;\n      const ref = React.useRef<ItemElement>(null);\n      const [element, setElement] = React.useState<ItemElement | null>(null);\n      const composedRefs = useComposedRefs(forwardedRef, ref, setElement);\n      const context = useCollectionContext(ITEM_SLOT_NAME, scope);\n\n      const { setItemMap } = context;\n\n      const itemDataRef = React.useRef(itemData);\n      if (!shallowEqual(itemDataRef.current, itemData)) {\n        itemDataRef.current = itemData;\n      }\n      const memoizedItemData = itemDataRef.current;\n\n      React.useEffect(() => {\n        const itemData = memoizedItemData;\n        setItemMap((map) => {\n          if (!element) {\n            return map;\n          }\n\n          if (!map.has(element)) {\n            map.set(element, { ...(itemData as unknown as ItemData), element });\n            return map.toSorted(sortByDocumentPosition);\n          }\n\n          return map\n            .set(element, { ...(itemData as unknown as ItemData), element })\n            .toSorted(sortByDocumentPosition);\n        });\n\n        return () => {\n          setItemMap((map) => {\n            if (!element || !map.has(element)) {\n              return map;\n            }\n            map.delete(element);\n            return new OrderedDict(map);\n          });\n        };\n      }, [element, memoizedItemData, setItemMap]);\n\n      return (\n        <CollectionItemSlotImpl {...{ [ITEM_DATA_ATTR]: '' }} ref={composedRefs as any}>\n          {children}\n        </CollectionItemSlotImpl>\n      );\n    }\n  );\n\n  CollectionItemSlot.displayName = ITEM_SLOT_NAME;\n\n  /* -----------------------------------------------------------------------------------------------\n   * useInitCollection\n   * ---------------------------------------------------------------------------------------------*/\n\n  function useInitCollection() {\n    return React.useState<ItemMap<ItemElement, ItemData>>(new OrderedDict());\n  }\n\n  /* -----------------------------------------------------------------------------------------------\n   * useCollection\n   * ---------------------------------------------------------------------------------------------*/\n\n  function useCollection(scope: any) {\n    const { itemMap } = useCollectionContext(name + 'CollectionConsumer', scope);\n\n    return itemMap;\n  }\n\n  const functions = {\n    createCollectionScope,\n    useCollection,\n    useInitCollection,\n  };\n\n  return [\n    { Provider: CollectionProvider, Slot: CollectionSlot, ItemSlot: CollectionItemSlot },\n    functions,\n  ] as const;\n}\n\nexport { createCollection };\nexport type { CollectionProps };\n\nfunction shallowEqual(a: any, b: any) {\n  if (a === b) return true;\n  if (typeof a !== 'object' || typeof b !== 'object') return false;\n  if (a == null || b == null) return false;\n  const keysA = Object.keys(a);\n  const keysB = Object.keys(b);\n  if (keysA.length !== keysB.length) return false;\n  for (const key of keysA) {\n    if (!Object.prototype.hasOwnProperty.call(b, key)) return false;\n    if (a[key] !== b[key]) return false;\n  }\n  return true;\n}\n\nfunction isElementPreceding(a: Element, b: Element) {\n  return !!(b.compareDocumentPosition(a) & Node.DOCUMENT_POSITION_PRECEDING);\n}\n\nfunction sortByDocumentPosition<E extends HTMLElement, T extends BaseItemData>(\n  a: EntryOf<ItemMap<E, T>>,\n  b: EntryOf<ItemMap<E, T>>\n) {\n  return !a[1].element || !b[1].element\n    ? 0\n    : isElementPreceding(a[1].element, b[1].element)\n      ? -1\n      : 1;\n}\n\nfunction getChildListObserver(callback: () => void) {\n  const observer = new MutationObserver((mutationsList) => {\n    for (const mutation of mutationsList) {\n      if (mutation.type === 'childList') {\n        callback();\n        return;\n      }\n    }\n  });\n\n  return observer;\n}\n", "// Not a real member because it shouldn't be accessible, but the super class\n// calls `set` which needs to read the instanciation state, so it can't be a\n// private member.\nconst __instanciated = new WeakMap<OrderedDict<any, any>, boolean>();\nexport class OrderedDict<K, V> extends Map<K, V> {\n  #keys: K[];\n\n  constructor(iterable?: Iterable<readonly [K, V]> | null | undefined);\n  constructor(entries?: readonly (readonly [K, V])[] | null) {\n    super(entries);\n    this.#keys = [...super.keys()];\n    __instanciated.set(this, true);\n  }\n\n  set(key: K, value: V) {\n    if (__instanciated.get(this)) {\n      if (this.has(key)) {\n        this.#keys[this.#keys.indexOf(key)] = key;\n      } else {\n        this.#keys.push(key);\n      }\n    }\n    super.set(key, value);\n    return this;\n  }\n\n  insert(index: number, key: K, value: V) {\n    const has = this.has(key);\n    const length = this.#keys.length;\n    const relativeIndex = toSafeInteger(index);\n    let actualIndex = relativeIndex >= 0 ? relativeIndex : length + relativeIndex;\n    const safeIndex = actualIndex < 0 || actualIndex >= length ? -1 : actualIndex;\n\n    if (safeIndex === this.size || (has && safeIndex === this.size - 1) || safeIndex === -1) {\n      this.set(key, value);\n      return this;\n    }\n\n    const size = this.size + (has ? 0 : 1);\n\n    // If you insert at, say, -2, without this bit you'd replace the\n    // second-to-last item and push the rest up one, which means the new item is\n    // 3rd to last. This isn't very intuitive; inserting at -2 is more like\n    // saying \"make this item the second to last\".\n    if (relativeIndex < 0) {\n      actualIndex++;\n    }\n\n    const keys = [...this.#keys];\n    let nextValue: V | undefined;\n    let shouldSkip = false;\n    for (let i = actualIndex; i < size; i++) {\n      if (actualIndex === i) {\n        let nextKey = keys[i]!;\n        if (keys[i] === key) {\n          nextKey = keys[i + 1]!;\n        }\n        if (has) {\n          // delete first to ensure that the item is moved to the end\n          this.delete(key);\n        }\n        nextValue = this.get(nextKey);\n        this.set(key, value);\n      } else {\n        if (!shouldSkip && keys[i - 1] === key) {\n          shouldSkip = true;\n        }\n        const currentKey = keys[shouldSkip ? i : i - 1]!;\n        const currentValue = nextValue!;\n        nextValue = this.get(currentKey);\n        this.delete(currentKey);\n        this.set(currentKey, currentValue);\n      }\n    }\n    return this;\n  }\n\n  with(index: number, key: K, value: V) {\n    const copy = new OrderedDict(this);\n    copy.insert(index, key, value);\n    return copy;\n  }\n\n  before(key: K) {\n    const index = this.#keys.indexOf(key) - 1;\n    if (index < 0) {\n      return undefined;\n    }\n    return this.entryAt(index);\n  }\n\n  /**\n   * Sets a new key-value pair at the position before the given key.\n   */\n  setBefore(key: K, newKey: K, value: V) {\n    const index = this.#keys.indexOf(key);\n    if (index === -1) {\n      return this;\n    }\n    return this.insert(index, newKey, value);\n  }\n\n  after(key: K) {\n    let index = this.#keys.indexOf(key);\n    index = index === -1 || index === this.size - 1 ? -1 : index + 1;\n    if (index === -1) {\n      return undefined;\n    }\n    return this.entryAt(index);\n  }\n\n  /**\n   * Sets a new key-value pair at the position after the given key.\n   */\n  setAfter(key: K, newKey: K, value: V) {\n    const index = this.#keys.indexOf(key);\n    if (index === -1) {\n      return this;\n    }\n    return this.insert(index + 1, newKey, value);\n  }\n\n  first() {\n    return this.entryAt(0);\n  }\n\n  last() {\n    return this.entryAt(-1);\n  }\n\n  clear() {\n    this.#keys = [];\n    return super.clear();\n  }\n\n  delete(key: K) {\n    const deleted = super.delete(key);\n    if (deleted) {\n      this.#keys.splice(this.#keys.indexOf(key), 1);\n    }\n    return deleted;\n  }\n\n  deleteAt(index: number) {\n    const key = this.keyAt(index);\n    if (key !== undefined) {\n      return this.delete(key);\n    }\n    return false;\n  }\n\n  at(index: number) {\n    const key = at(this.#keys, index);\n    if (key !== undefined) {\n      return this.get(key);\n    }\n  }\n\n  entryAt(index: number): [K, V] | undefined {\n    const key = at(this.#keys, index);\n    if (key !== undefined) {\n      return [key, this.get(key)!];\n    }\n  }\n\n  indexOf(key: K) {\n    return this.#keys.indexOf(key);\n  }\n\n  keyAt(index: number) {\n    return at(this.#keys, index);\n  }\n\n  from(key: K, offset: number) {\n    const index = this.indexOf(key);\n    if (index === -1) {\n      return undefined;\n    }\n    let dest = index + offset;\n    if (dest < 0) dest = 0;\n    if (dest >= this.size) dest = this.size - 1;\n    return this.at(dest);\n  }\n\n  keyFrom(key: K, offset: number) {\n    const index = this.indexOf(key);\n    if (index === -1) {\n      return undefined;\n    }\n    let dest = index + offset;\n    if (dest < 0) dest = 0;\n    if (dest >= this.size) dest = this.size - 1;\n    return this.keyAt(dest);\n  }\n\n  find(\n    predicate: (entry: [K, V], index: number, dictionary: OrderedDict<K, V>) => boolean,\n    thisArg?: any\n  ) {\n    let index = 0;\n    for (const entry of this) {\n      if (Reflect.apply(predicate, thisArg, [entry, index, this])) {\n        return entry;\n      }\n      index++;\n    }\n    return undefined;\n  }\n\n  findIndex(\n    predicate: (entry: [K, V], index: number, dictionary: OrderedDict<K, V>) => boolean,\n    thisArg?: any\n  ) {\n    let index = 0;\n    for (const entry of this) {\n      if (Reflect.apply(predicate, thisArg, [entry, index, this])) {\n        return index;\n      }\n      index++;\n    }\n    return -1;\n  }\n\n  filter<KK extends K, VV extends V>(\n    predicate: (entry: [K, V], index: number, dict: OrderedDict<K, V>) => entry is [KK, VV],\n    thisArg?: any\n  ): OrderedDict<KK, VV>;\n\n  filter(\n    predicate: (entry: [K, V], index: number, dictionary: OrderedDict<K, V>) => unknown,\n    thisArg?: any\n  ): OrderedDict<K, V>;\n\n  filter(\n    predicate: (entry: [K, V], index: number, dictionary: OrderedDict<K, V>) => unknown,\n    thisArg?: any\n  ) {\n    const entries: Array<[K, V]> = [];\n    let index = 0;\n    for (const entry of this) {\n      if (Reflect.apply(predicate, thisArg, [entry, index, this])) {\n        entries.push(entry);\n      }\n      index++;\n    }\n    return new OrderedDict(entries);\n  }\n\n  map<U>(\n    callbackfn: (entry: [K, V], index: number, dictionary: OrderedDict<K, V>) => U,\n    thisArg?: any\n  ): OrderedDict<K, U> {\n    const entries: [K, U][] = [];\n    let index = 0;\n    for (const entry of this) {\n      entries.push([entry[0], Reflect.apply(callbackfn, thisArg, [entry, index, this])]);\n      index++;\n    }\n    return new OrderedDict(entries);\n  }\n\n  reduce(\n    callbackfn: (\n      previousValue: [K, V],\n      currentEntry: [K, V],\n      currentIndex: number,\n      dictionary: OrderedDict<K, V>\n    ) => [K, V]\n  ): [K, V];\n  reduce(\n    callbackfn: (\n      previousValue: [K, V],\n      currentEntry: [K, V],\n      currentIndex: number,\n      dictionary: OrderedDict<K, V>\n    ) => [K, V],\n    initialValue: [K, V]\n  ): [K, V];\n  reduce<U>(\n    callbackfn: (\n      previousValue: U,\n      currentEntry: [K, V],\n      currentIndex: number,\n      dictionary: OrderedDict<K, V>\n    ) => U,\n    initialValue: U\n  ): U;\n\n  reduce<U>(\n    ...args: [\n      (\n        previousValue: U,\n        currentEntry: [K, V],\n        currentIndex: number,\n        dictionary: OrderedDict<K, V>\n      ) => U,\n      U?,\n    ]\n  ) {\n    const [callbackfn, initialValue] = args;\n    let index = 0;\n    let accumulator = initialValue ?? this.at(0)!;\n    for (const entry of this) {\n      if (index === 0 && args.length === 1) {\n        accumulator = entry as any;\n      } else {\n        accumulator = Reflect.apply(callbackfn, this, [accumulator, entry, index, this]);\n      }\n      index++;\n    }\n    return accumulator;\n  }\n\n  reduceRight(\n    callbackfn: (\n      previousValue: [K, V],\n      currentEntry: [K, V],\n      currentIndex: number,\n      dictionary: OrderedDict<K, V>\n    ) => [K, V]\n  ): [K, V];\n  reduceRight(\n    callbackfn: (\n      previousValue: [K, V],\n      currentEntry: [K, V],\n      currentIndex: number,\n      dictionary: OrderedDict<K, V>\n    ) => [K, V],\n    initialValue: [K, V]\n  ): [K, V];\n  reduceRight<U>(\n    callbackfn: (\n      previousValue: [K, V],\n      currentValue: U,\n      currentIndex: number,\n      dictionary: OrderedDict<K, V>\n    ) => U,\n    initialValue: U\n  ): U;\n\n  reduceRight<U>(\n    ...args: [\n      (\n        previousValue: U,\n        currentEntry: [K, V],\n        currentIndex: number,\n        dictionary: OrderedDict<K, V>\n      ) => U,\n      U?,\n    ]\n  ) {\n    const [callbackfn, initialValue] = args;\n    let accumulator = initialValue ?? this.at(-1)!;\n    for (let index = this.size - 1; index >= 0; index--) {\n      const entry = this.at(index)!;\n      if (index === this.size - 1 && args.length === 1) {\n        accumulator = entry as any;\n      } else {\n        accumulator = Reflect.apply(callbackfn, this, [accumulator, entry, index, this]);\n      }\n    }\n    return accumulator;\n  }\n\n  toSorted(compareFn?: (a: [K, V], b: [K, V]) => number): OrderedDict<K, V> {\n    const entries = [...this.entries()].sort(compareFn);\n    return new OrderedDict(entries);\n  }\n\n  toReversed(): OrderedDict<K, V> {\n    const reversed = new OrderedDict<K, V>();\n    for (let index = this.size - 1; index >= 0; index--) {\n      const key = this.keyAt(index)!;\n      const element = this.get(key)!;\n      reversed.set(key, element);\n    }\n    return reversed;\n  }\n\n  toSpliced(start: number, deleteCount?: number): OrderedDict<K, V>;\n  toSpliced(start: number, deleteCount: number, ...items: [K, V][]): OrderedDict<K, V>;\n\n  toSpliced(...args: [start: number, deleteCount: number, ...items: [K, V][]]) {\n    const entries = [...this.entries()];\n    entries.splice(...args);\n    return new OrderedDict(entries);\n  }\n\n  slice(start?: number, end?: number) {\n    const result = new OrderedDict<K, V>();\n    let stop = this.size - 1;\n\n    if (start === undefined) {\n      return result;\n    }\n\n    if (start < 0) {\n      start = start + this.size;\n    }\n\n    if (end !== undefined && end > 0) {\n      stop = end - 1;\n    }\n\n    for (let index = start; index <= stop; index++) {\n      const key = this.keyAt(index)!;\n      const element = this.get(key)!;\n      result.set(key, element);\n    }\n    return result;\n  }\n\n  every(\n    predicate: (entry: [K, V], index: number, dictionary: OrderedDict<K, V>) => unknown,\n    thisArg?: any\n  ) {\n    let index = 0;\n    for (const entry of this) {\n      if (!Reflect.apply(predicate, thisArg, [entry, index, this])) {\n        return false;\n      }\n      index++;\n    }\n    return true;\n  }\n\n  some(\n    predicate: (entry: [K, V], index: number, dictionary: OrderedDict<K, V>) => unknown,\n    thisArg?: any\n  ) {\n    let index = 0;\n    for (const entry of this) {\n      if (Reflect.apply(predicate, thisArg, [entry, index, this])) {\n        return true;\n      }\n      index++;\n    }\n    return false;\n  }\n}\n\nexport type KeyOf<D extends OrderedDict<any, any>> =\n  D extends OrderedDict<infer K, any> ? K : never;\nexport type ValueOf<D extends OrderedDict<any, any>> =\n  D extends OrderedDict<any, infer V> ? V : never;\nexport type EntryOf<D extends OrderedDict<any, any>> = [KeyOf<D>, ValueOf<D>];\nexport type KeyFrom<E extends EntryOf<any>> = E[0];\nexport type ValueFrom<E extends EntryOf<any>> = E[1];\n\nfunction at<T>(array: ArrayLike<T>, index: number): T | undefined {\n  if ('at' in Array.prototype) {\n    return Array.prototype.at.call(array, index);\n  }\n  const actualIndex = toSafeIndex(array, index);\n  return actualIndex === -1 ? undefined : array[actualIndex];\n}\n\nfunction toSafeIndex(array: ArrayLike<any>, index: number) {\n  const length = array.length;\n  const relativeIndex = toSafeInteger(index);\n  const actualIndex = relativeIndex >= 0 ? relativeIndex : length + relativeIndex;\n  return actualIndex < 0 || actualIndex >= length ? -1 : actualIndex;\n}\n\nfunction toSafeInteger(number: number) {\n  // eslint-disable-next-line no-self-compare\n  return number !== number || number === 0 ? 0 : Math.trunc(number);\n}\n", "// packages/react/direction/src/direction.tsx\nimport * as React from \"react\";\nimport { jsx } from \"react/jsx-runtime\";\nvar DirectionContext = React.createContext(void 0);\nvar DirectionProvider = (props) => {\n  const { dir, children } = props;\n  return /* @__PURE__ */ jsx(DirectionContext.Provider, { value: dir, children });\n};\nfunction useDirection(localDir) {\n  const globalDir = React.useContext(DirectionContext);\n  return localDir || globalDir || \"ltr\";\n}\nvar Provider = DirectionProvider;\nexport {\n  DirectionProvider,\n  Provider,\n  useDirection\n};\n//# sourceMappingURL=index.mjs.map\n", "// src/primitive.tsx\nimport * as React from \"react\";\nimport * as ReactDOM from \"react-dom\";\nimport { createSlot } from \"@radix-ui/react-slot\";\nimport { jsx } from \"react/jsx-runtime\";\nvar NODES = [\n  \"a\",\n  \"button\",\n  \"div\",\n  \"form\",\n  \"h2\",\n  \"h3\",\n  \"img\",\n  \"input\",\n  \"label\",\n  \"li\",\n  \"nav\",\n  \"ol\",\n  \"p\",\n  \"select\",\n  \"span\",\n  \"svg\",\n  \"ul\"\n];\nvar Primitive = NODES.reduce((primitive, node) => {\n  const Slot = createSlot(`Primitive.${node}`);\n  const Node = React.forwardRef((props, forwardedRef) => {\n    const { asChild, ...primitiveProps } = props;\n    const Comp = asChild ? Slot : node;\n    if (typeof window !== \"undefined\") {\n      window[Symbol.for(\"radix-ui\")] = true;\n    }\n    return /* @__PURE__ */ jsx(Comp, { ...primitiveProps, ref: forwardedRef });\n  });\n  Node.displayName = `Primitive.${node}`;\n  return { ...primitive, [node]: Node };\n}, {});\nfunction dispatchDiscreteCustomEvent(target, event) {\n  if (target) ReactDOM.flushSync(() => target.dispatchEvent(event));\n}\nvar Root = Primitive;\nexport {\n  Primitive,\n  Root,\n  dispatchDiscreteCustomEvent\n};\n//# sourceMappingURL=index.mjs.map\n", "// packages/react/use-callback-ref/src/use-callback-ref.tsx\nimport * as React from \"react\";\nfunction useCallbackRef(callback) {\n  const callbackRef = React.useRef(callback);\n  React.useEffect(() => {\n    callbackRef.current = callback;\n  });\n  return React.useMemo(() => (...args) => callbackRef.current?.(...args), []);\n}\nexport {\n  useCallbackRef\n};\n//# sourceMappingURL=index.mjs.map\n", "// packages/react/use-escape-keydown/src/use-escape-keydown.tsx\nimport * as React from \"react\";\nimport { useCallbackRef } from \"@radix-ui/react-use-callback-ref\";\nfunction useEscapeKeydown(onEscapeKeyDownProp, ownerDocument = globalThis?.document) {\n  const onEscapeKeyDown = useCallbackRef(onEscapeKeyDownProp);\n  React.useEffect(() => {\n    const handleKeyDown = (event) => {\n      if (event.key === \"Escape\") {\n        onEscapeKeyDown(event);\n      }\n    };\n    ownerDocument.addEventListener(\"keydown\", handleKeyDown, { capture: true });\n    return () => ownerDocument.removeEventListener(\"keydown\", handleKeyDown, { capture: true });\n  }, [onEscapeKeyDown, ownerDocument]);\n}\nexport {\n  useEscapeKeydown\n};\n//# sourceMappingURL=index.mjs.map\n", "// packages/react/use-layout-effect/src/use-layout-effect.tsx\nimport * as React from \"react\";\nvar useLayoutEffect2 = globalThis?.document ? React.useLayoutEffect : () => {\n};\nexport {\n  useLayoutEffect2 as useLayoutEffect\n};\n//# sourceMappingURL=index.mjs.map\n", "// packages/react/id/src/id.tsx\nimport * as React from \"react\";\nimport { useLayoutEffect } from \"@radix-ui/react-use-layout-effect\";\nvar useReactId = React[\" useId \".trim().toString()] || (() => void 0);\nvar count = 0;\nfunction useId(deterministicId) {\n  const [id, setId] = React.useState(useReactId());\n  useLayoutEffect(() => {\n    if (!deterministicId) setId((reactId) => reactId ?? String(count++));\n  }, [deterministicId]);\n  return deterministicId || (id ? `radix-${id}` : \"\");\n}\nexport {\n  useId\n};\n//# sourceMappingURL=index.mjs.map\n", "// src/arrow.tsx\nimport * as React from \"react\";\nimport { Primitive } from \"@radix-ui/react-primitive\";\nimport { jsx } from \"react/jsx-runtime\";\nvar NAME = \"Arrow\";\nvar Arrow = React.forwardRef((props, forwardedRef) => {\n  const { children, width = 10, height = 5, ...arrowProps } = props;\n  return /* @__PURE__ */ jsx(\n    Primitive.svg,\n    {\n      ...arrowProps,\n      ref: forwardedRef,\n      width,\n      height,\n      viewBox: \"0 0 30 10\",\n      preserveAspectRatio: \"none\",\n      children: props.asChild ? children : /* @__PURE__ */ jsx(\"polygon\", { points: \"0,0 30,0 15,10\" })\n    }\n  );\n});\nArrow.displayName = NAME;\nvar Root = Arrow;\nexport {\n  Arrow,\n  Root\n};\n//# sourceMappingURL=index.mjs.map\n", "// packages/react/use-size/src/use-size.tsx\nimport * as React from \"react\";\nimport { useLayoutEffect } from \"@radix-ui/react-use-layout-effect\";\nfunction useSize(element) {\n  const [size, setSize] = React.useState(void 0);\n  useLayoutEffect(() => {\n    if (element) {\n      setSize({ width: element.offsetWidth, height: element.offsetHeight });\n      const resizeObserver = new ResizeObserver((entries) => {\n        if (!Array.isArray(entries)) {\n          return;\n        }\n        if (!entries.length) {\n          return;\n        }\n        const entry = entries[0];\n        let width;\n        let height;\n        if (\"borderBoxSize\" in entry) {\n          const borderSizeEntry = entry[\"borderBoxSize\"];\n          const borderSize = Array.isArray(borderSizeEntry) ? borderSizeEntry[0] : borderSizeEntry;\n          width = borderSize[\"inlineSize\"];\n          height = borderSize[\"blockSize\"];\n        } else {\n          width = element.offsetWidth;\n          height = element.offsetHeight;\n        }\n        setSize({ width, height });\n      });\n      resizeObserver.observe(element, { box: \"border-box\" });\n      return () => resizeObserver.unobserve(element);\n    } else {\n      setSize(void 0);\n    }\n  }, [element]);\n  return size;\n}\nexport {\n  useSize\n};\n//# sourceMappingURL=index.mjs.map\n", "import * as React from 'react';\nimport {\n  useFloating,\n  autoUpdate,\n  offset,\n  shift,\n  limitShift,\n  hide,\n  arrow as floatingUIarrow,\n  flip,\n  size,\n} from '@floating-ui/react-dom';\nimport * as ArrowPrimitive from '@radix-ui/react-arrow';\nimport { useComposedRefs } from '@radix-ui/react-compose-refs';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { Primitive } from '@radix-ui/react-primitive';\nimport { useCallbackRef } from '@radix-ui/react-use-callback-ref';\nimport { useLayoutEffect } from '@radix-ui/react-use-layout-effect';\nimport { useSize } from '@radix-ui/react-use-size';\n\nimport type { Placement, Middleware } from '@floating-ui/react-dom';\nimport type { Scope } from '@radix-ui/react-context';\nimport type { Measurable } from '@radix-ui/rect';\n\nconst SIDE_OPTIONS = ['top', 'right', 'bottom', 'left'] as const;\nconst ALIGN_OPTIONS = ['start', 'center', 'end'] as const;\n\ntype Side = (typeof SIDE_OPTIONS)[number];\ntype Align = (typeof ALIGN_OPTIONS)[number];\n\n/* -------------------------------------------------------------------------------------------------\n * Popper\n * -----------------------------------------------------------------------------------------------*/\n\nconst POPPER_NAME = 'Popper';\n\ntype ScopedProps<P> = P & { __scopePopper?: Scope };\nconst [createPopperContext, createPopperScope] = createContextScope(POPPER_NAME);\n\ntype PopperContextValue = {\n  anchor: Measurable | null;\n  onAnchorChange(anchor: Measurable | null): void;\n};\nconst [PopperProvider, usePopperContext] = createPopperContext<PopperContextValue>(POPPER_NAME);\n\ninterface PopperProps {\n  children?: React.ReactNode;\n}\nconst Popper: React.FC<PopperProps> = (props: ScopedProps<PopperProps>) => {\n  const { __scopePopper, children } = props;\n  const [anchor, setAnchor] = React.useState<Measurable | null>(null);\n  return (\n    <PopperProvider scope={__scopePopper} anchor={anchor} onAnchorChange={setAnchor}>\n      {children}\n    </PopperProvider>\n  );\n};\n\nPopper.displayName = POPPER_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * PopperAnchor\n * -----------------------------------------------------------------------------------------------*/\n\nconst ANCHOR_NAME = 'PopperAnchor';\n\ntype PopperAnchorElement = React.ComponentRef<typeof Primitive.div>;\ntype PrimitiveDivProps = React.ComponentPropsWithoutRef<typeof Primitive.div>;\ninterface PopperAnchorProps extends PrimitiveDivProps {\n  virtualRef?: React.RefObject<Measurable>;\n}\n\nconst PopperAnchor = React.forwardRef<PopperAnchorElement, PopperAnchorProps>(\n  (props: ScopedProps<PopperAnchorProps>, forwardedRef) => {\n    const { __scopePopper, virtualRef, ...anchorProps } = props;\n    const context = usePopperContext(ANCHOR_NAME, __scopePopper);\n    const ref = React.useRef<PopperAnchorElement>(null);\n    const composedRefs = useComposedRefs(forwardedRef, ref);\n\n    React.useEffect(() => {\n      // Consumer can anchor the popper to something that isn't\n      // a DOM node e.g. pointer position, so we override the\n      // `anchorRef` with their virtual ref in this case.\n      context.onAnchorChange(virtualRef?.current || ref.current);\n    });\n\n    return virtualRef ? null : <Primitive.div {...anchorProps} ref={composedRefs} />;\n  }\n);\n\nPopperAnchor.displayName = ANCHOR_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * PopperContent\n * -----------------------------------------------------------------------------------------------*/\n\nconst CONTENT_NAME = 'PopperContent';\n\ntype PopperContentContextValue = {\n  placedSide: Side;\n  onArrowChange(arrow: HTMLSpanElement | null): void;\n  arrowX?: number;\n  arrowY?: number;\n  shouldHideArrow: boolean;\n};\n\nconst [PopperContentProvider, useContentContext] =\n  createPopperContext<PopperContentContextValue>(CONTENT_NAME);\n\ntype Boundary = Element | null;\n\ntype PopperContentElement = React.ComponentRef<typeof Primitive.div>;\ninterface PopperContentProps extends PrimitiveDivProps {\n  side?: Side;\n  sideOffset?: number;\n  align?: Align;\n  alignOffset?: number;\n  arrowPadding?: number;\n  avoidCollisions?: boolean;\n  collisionBoundary?: Boundary | Boundary[];\n  collisionPadding?: number | Partial<Record<Side, number>>;\n  sticky?: 'partial' | 'always';\n  hideWhenDetached?: boolean;\n  updatePositionStrategy?: 'optimized' | 'always';\n  onPlaced?: () => void;\n}\n\nconst PopperContent = React.forwardRef<PopperContentElement, PopperContentProps>(\n  (props: ScopedProps<PopperContentProps>, forwardedRef) => {\n    const {\n      __scopePopper,\n      side = 'bottom',\n      sideOffset = 0,\n      align = 'center',\n      alignOffset = 0,\n      arrowPadding = 0,\n      avoidCollisions = true,\n      collisionBoundary = [],\n      collisionPadding: collisionPaddingProp = 0,\n      sticky = 'partial',\n      hideWhenDetached = false,\n      updatePositionStrategy = 'optimized',\n      onPlaced,\n      ...contentProps\n    } = props;\n\n    const context = usePopperContext(CONTENT_NAME, __scopePopper);\n\n    const [content, setContent] = React.useState<HTMLDivElement | null>(null);\n    const composedRefs = useComposedRefs(forwardedRef, (node) => setContent(node));\n\n    const [arrow, setArrow] = React.useState<HTMLSpanElement | null>(null);\n    const arrowSize = useSize(arrow);\n    const arrowWidth = arrowSize?.width ?? 0;\n    const arrowHeight = arrowSize?.height ?? 0;\n\n    const desiredPlacement = (side + (align !== 'center' ? '-' + align : '')) as Placement;\n\n    const collisionPadding =\n      typeof collisionPaddingProp === 'number'\n        ? collisionPaddingProp\n        : { top: 0, right: 0, bottom: 0, left: 0, ...collisionPaddingProp };\n\n    const boundary = Array.isArray(collisionBoundary) ? collisionBoundary : [collisionBoundary];\n    const hasExplicitBoundaries = boundary.length > 0;\n\n    const detectOverflowOptions = {\n      padding: collisionPadding,\n      boundary: boundary.filter(isNotNull),\n      // with `strategy: 'fixed'`, this is the only way to get it to respect boundaries\n      altBoundary: hasExplicitBoundaries,\n    };\n\n    const { refs, floatingStyles, placement, isPositioned, middlewareData } = useFloating({\n      // default to `fixed` strategy so users don't have to pick and we also avoid focus scroll issues\n      strategy: 'fixed',\n      placement: desiredPlacement,\n      whileElementsMounted: (...args) => {\n        const cleanup = autoUpdate(...args, {\n          animationFrame: updatePositionStrategy === 'always',\n        });\n        return cleanup;\n      },\n      elements: {\n        reference: context.anchor,\n      },\n      middleware: [\n        offset({ mainAxis: sideOffset + arrowHeight, alignmentAxis: alignOffset }),\n        avoidCollisions &&\n          shift({\n            mainAxis: true,\n            crossAxis: false,\n            limiter: sticky === 'partial' ? limitShift() : undefined,\n            ...detectOverflowOptions,\n          }),\n        avoidCollisions && flip({ ...detectOverflowOptions }),\n        size({\n          ...detectOverflowOptions,\n          apply: ({ elements, rects, availableWidth, availableHeight }) => {\n            const { width: anchorWidth, height: anchorHeight } = rects.reference;\n            const contentStyle = elements.floating.style;\n            contentStyle.setProperty('--radix-popper-available-width', `${availableWidth}px`);\n            contentStyle.setProperty('--radix-popper-available-height', `${availableHeight}px`);\n            contentStyle.setProperty('--radix-popper-anchor-width', `${anchorWidth}px`);\n            contentStyle.setProperty('--radix-popper-anchor-height', `${anchorHeight}px`);\n          },\n        }),\n        arrow && floatingUIarrow({ element: arrow, padding: arrowPadding }),\n        transformOrigin({ arrowWidth, arrowHeight }),\n        hideWhenDetached && hide({ strategy: 'referenceHidden', ...detectOverflowOptions }),\n      ],\n    });\n\n    const [placedSide, placedAlign] = getSideAndAlignFromPlacement(placement);\n\n    const handlePlaced = useCallbackRef(onPlaced);\n    useLayoutEffect(() => {\n      if (isPositioned) {\n        handlePlaced?.();\n      }\n    }, [isPositioned, handlePlaced]);\n\n    const arrowX = middlewareData.arrow?.x;\n    const arrowY = middlewareData.arrow?.y;\n    const cannotCenterArrow = middlewareData.arrow?.centerOffset !== 0;\n\n    const [contentZIndex, setContentZIndex] = React.useState<string>();\n    useLayoutEffect(() => {\n      if (content) setContentZIndex(window.getComputedStyle(content).zIndex);\n    }, [content]);\n\n    return (\n      <div\n        ref={refs.setFloating}\n        data-radix-popper-content-wrapper=\"\"\n        style={{\n          ...floatingStyles,\n          transform: isPositioned ? floatingStyles.transform : 'translate(0, -200%)', // keep off the page when measuring\n          minWidth: 'max-content',\n          zIndex: contentZIndex,\n          ['--radix-popper-transform-origin' as any]: [\n            middlewareData.transformOrigin?.x,\n            middlewareData.transformOrigin?.y,\n          ].join(' '),\n\n          // hide the content if using the hide middleware and should be hidden\n          // set visibility to hidden and disable pointer events so the UI behaves\n          // as if the PopperContent isn't there at all\n          ...(middlewareData.hide?.referenceHidden && {\n            visibility: 'hidden',\n            pointerEvents: 'none',\n          }),\n        }}\n        // Floating UI interally calculates logical alignment based the `dir` attribute on\n        // the reference/floating node, we must add this attribute here to ensure\n        // this is calculated when portalled as well as inline.\n        dir={props.dir}\n      >\n        <PopperContentProvider\n          scope={__scopePopper}\n          placedSide={placedSide}\n          onArrowChange={setArrow}\n          arrowX={arrowX}\n          arrowY={arrowY}\n          shouldHideArrow={cannotCenterArrow}\n        >\n          <Primitive.div\n            data-side={placedSide}\n            data-align={placedAlign}\n            {...contentProps}\n            ref={composedRefs}\n            style={{\n              ...contentProps.style,\n              // if the PopperContent hasn't been placed yet (not all measurements done)\n              // we prevent animations so that users's animation don't kick in too early referring wrong sides\n              animation: !isPositioned ? 'none' : undefined,\n            }}\n          />\n        </PopperContentProvider>\n      </div>\n    );\n  }\n);\n\nPopperContent.displayName = CONTENT_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * PopperArrow\n * -----------------------------------------------------------------------------------------------*/\n\nconst ARROW_NAME = 'PopperArrow';\n\nconst OPPOSITE_SIDE: Record<Side, Side> = {\n  top: 'bottom',\n  right: 'left',\n  bottom: 'top',\n  left: 'right',\n};\n\ntype PopperArrowElement = React.ComponentRef<typeof ArrowPrimitive.Root>;\ntype ArrowProps = React.ComponentPropsWithoutRef<typeof ArrowPrimitive.Root>;\ninterface PopperArrowProps extends ArrowProps {}\n\nconst PopperArrow = React.forwardRef<PopperArrowElement, PopperArrowProps>(function PopperArrow(\n  props: ScopedProps<PopperArrowProps>,\n  forwardedRef\n) {\n  const { __scopePopper, ...arrowProps } = props;\n  const contentContext = useContentContext(ARROW_NAME, __scopePopper);\n  const baseSide = OPPOSITE_SIDE[contentContext.placedSide];\n\n  return (\n    // we have to use an extra wrapper because `ResizeObserver` (used by `useSize`)\n    // doesn't report size as we'd expect on SVG elements.\n    // it reports their bounding box which is effectively the largest path inside the SVG.\n    <span\n      ref={contentContext.onArrowChange}\n      style={{\n        position: 'absolute',\n        left: contentContext.arrowX,\n        top: contentContext.arrowY,\n        [baseSide]: 0,\n        transformOrigin: {\n          top: '',\n          right: '0 0',\n          bottom: 'center 0',\n          left: '100% 0',\n        }[contentContext.placedSide],\n        transform: {\n          top: 'translateY(100%)',\n          right: 'translateY(50%) rotate(90deg) translateX(-50%)',\n          bottom: `rotate(180deg)`,\n          left: 'translateY(50%) rotate(-90deg) translateX(50%)',\n        }[contentContext.placedSide],\n        visibility: contentContext.shouldHideArrow ? 'hidden' : undefined,\n      }}\n    >\n      <ArrowPrimitive.Root\n        {...arrowProps}\n        ref={forwardedRef}\n        style={{\n          ...arrowProps.style,\n          // ensures the element can be measured correctly (mostly for if SVG)\n          display: 'block',\n        }}\n      />\n    </span>\n  );\n});\n\nPopperArrow.displayName = ARROW_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\nfunction isNotNull<T>(value: T | null): value is T {\n  return value !== null;\n}\n\nconst transformOrigin = (options: { arrowWidth: number; arrowHeight: number }): Middleware => ({\n  name: 'transformOrigin',\n  options,\n  fn(data) {\n    const { placement, rects, middlewareData } = data;\n\n    const cannotCenterArrow = middlewareData.arrow?.centerOffset !== 0;\n    const isArrowHidden = cannotCenterArrow;\n    const arrowWidth = isArrowHidden ? 0 : options.arrowWidth;\n    const arrowHeight = isArrowHidden ? 0 : options.arrowHeight;\n\n    const [placedSide, placedAlign] = getSideAndAlignFromPlacement(placement);\n    const noArrowAlign = { start: '0%', center: '50%', end: '100%' }[placedAlign];\n\n    const arrowXCenter = (middlewareData.arrow?.x ?? 0) + arrowWidth / 2;\n    const arrowYCenter = (middlewareData.arrow?.y ?? 0) + arrowHeight / 2;\n\n    let x = '';\n    let y = '';\n\n    if (placedSide === 'bottom') {\n      x = isArrowHidden ? noArrowAlign : `${arrowXCenter}px`;\n      y = `${-arrowHeight}px`;\n    } else if (placedSide === 'top') {\n      x = isArrowHidden ? noArrowAlign : `${arrowXCenter}px`;\n      y = `${rects.floating.height + arrowHeight}px`;\n    } else if (placedSide === 'right') {\n      x = `${-arrowHeight}px`;\n      y = isArrowHidden ? noArrowAlign : `${arrowYCenter}px`;\n    } else if (placedSide === 'left') {\n      x = `${rects.floating.width + arrowHeight}px`;\n      y = isArrowHidden ? noArrowAlign : `${arrowYCenter}px`;\n    }\n    return { data: { x, y } };\n  },\n});\n\nfunction getSideAndAlignFromPlacement(placement: Placement) {\n  const [side, align = 'center'] = placement.split('-');\n  return [side as Side, align as Align] as const;\n}\n\nconst Root = Popper;\nconst Anchor = PopperAnchor;\nconst Content = PopperContent;\nconst Arrow = PopperArrow;\n\nexport {\n  createPopperScope,\n  //\n  Popper,\n  PopperAnchor,\n  PopperContent,\n  PopperArrow,\n  //\n  Root,\n  Anchor,\n  Content,\n  Arrow,\n  //\n  SIDE_OPTIONS,\n  ALIGN_OPTIONS,\n};\nexport type { PopperProps, PopperAnchorProps, PopperContentProps, PopperArrowProps };\n", "// src/use-controllable-state.tsx\nimport * as React from \"react\";\nimport { useLayoutEffect } from \"@radix-ui/react-use-layout-effect\";\nvar useInsertionEffect = React[\" useInsertionEffect \".trim().toString()] || useLayoutEffect;\nfunction useControllableState({\n  prop,\n  defaultProp,\n  onChange = () => {\n  },\n  caller\n}) {\n  const [uncontrolledProp, setUncontrolledProp, onChangeRef] = useUncontrolledState({\n    defaultProp,\n    onChange\n  });\n  const isControlled = prop !== void 0;\n  const value = isControlled ? prop : uncontrolledProp;\n  if (true) {\n    const isControlledRef = React.useRef(prop !== void 0);\n    React.useEffect(() => {\n      const wasControlled = isControlledRef.current;\n      if (wasControlled !== isControlled) {\n        const from = wasControlled ? \"controlled\" : \"uncontrolled\";\n        const to = isControlled ? \"controlled\" : \"uncontrolled\";\n        console.warn(\n          `${caller} is changing from ${from} to ${to}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`\n        );\n      }\n      isControlledRef.current = isControlled;\n    }, [isControlled, caller]);\n  }\n  const setValue = React.useCallback(\n    (nextValue) => {\n      if (isControlled) {\n        const value2 = isFunction(nextValue) ? nextValue(prop) : nextValue;\n        if (value2 !== prop) {\n          onChangeRef.current?.(value2);\n        }\n      } else {\n        setUncontrolledProp(nextValue);\n      }\n    },\n    [isControlled, prop, setUncontrolledProp, onChangeRef]\n  );\n  return [value, setValue];\n}\nfunction useUncontrolledState({\n  defaultProp,\n  onChange\n}) {\n  const [value, setValue] = React.useState(defaultProp);\n  const prevValueRef = React.useRef(value);\n  const onChangeRef = React.useRef(onChange);\n  useInsertionEffect(() => {\n    onChangeRef.current = onChange;\n  }, [onChange]);\n  React.useEffect(() => {\n    if (prevValueRef.current !== value) {\n      onChangeRef.current?.(value);\n      prevValueRef.current = value;\n    }\n  }, [value, prevValueRef]);\n  return [value, setValue, onChangeRef];\n}\nfunction isFunction(value) {\n  return typeof value === \"function\";\n}\n\n// src/use-controllable-state-reducer.tsx\nimport * as React2 from \"react\";\nimport { useEffectEvent } from \"@radix-ui/react-use-effect-event\";\nvar SYNC_STATE = Symbol(\"RADIX:SYNC_STATE\");\nfunction useControllableStateReducer(reducer, userArgs, initialArg, init) {\n  const { prop: controlledState, defaultProp, onChange: onChangeProp, caller } = userArgs;\n  const isControlled = controlledState !== void 0;\n  const onChange = useEffectEvent(onChangeProp);\n  if (true) {\n    const isControlledRef = React2.useRef(controlledState !== void 0);\n    React2.useEffect(() => {\n      const wasControlled = isControlledRef.current;\n      if (wasControlled !== isControlled) {\n        const from = wasControlled ? \"controlled\" : \"uncontrolled\";\n        const to = isControlled ? \"controlled\" : \"uncontrolled\";\n        console.warn(\n          `${caller} is changing from ${from} to ${to}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`\n        );\n      }\n      isControlledRef.current = isControlled;\n    }, [isControlled, caller]);\n  }\n  const args = [{ ...initialArg, state: defaultProp }];\n  if (init) {\n    args.push(init);\n  }\n  const [internalState, dispatch] = React2.useReducer(\n    (state2, action) => {\n      if (action.type === SYNC_STATE) {\n        return { ...state2, state: action.state };\n      }\n      const next = reducer(state2, action);\n      if (isControlled && !Object.is(next.state, state2.state)) {\n        onChange(next.state);\n      }\n      return next;\n    },\n    ...args\n  );\n  const uncontrolledState = internalState.state;\n  const prevValueRef = React2.useRef(uncontrolledState);\n  React2.useEffect(() => {\n    if (prevValueRef.current !== uncontrolledState) {\n      prevValueRef.current = uncontrolledState;\n      if (!isControlled) {\n        onChange(uncontrolledState);\n      }\n    }\n  }, [onChange, uncontrolledState, prevValueRef, isControlled]);\n  const state = React2.useMemo(() => {\n    const isControlled2 = controlledState !== void 0;\n    if (isControlled2) {\n      return { ...internalState, state: controlledState };\n    }\n    return internalState;\n  }, [internalState, controlledState]);\n  React2.useEffect(() => {\n    if (isControlled && !Object.is(controlledState, internalState.state)) {\n      dispatch({ type: SYNC_STATE, state: controlledState });\n    }\n  }, [controlledState, internalState.state, isControlled]);\n  return [state, dispatch];\n}\nexport {\n  useControllableState,\n  useControllableStateReducer\n};\n//# sourceMappingURL=index.mjs.map\n", "// packages/react/use-previous/src/use-previous.tsx\nimport * as React from \"react\";\nfunction usePrevious(value) {\n  const ref = React.useRef({ value, previous: value });\n  return React.useMemo(() => {\n    if (ref.current.value !== value) {\n      ref.current.previous = ref.current.value;\n      ref.current.value = value;\n    }\n    return ref.current.previous;\n  }, [value]);\n}\nexport {\n  usePrevious\n};\n//# sourceMappingURL=index.mjs.map\n", "// src/visually-hidden.tsx\nimport * as React from \"react\";\nimport { Primitive } from \"@radix-ui/react-primitive\";\nimport { jsx } from \"react/jsx-runtime\";\nvar VISUALLY_HIDDEN_STYLES = Object.freeze({\n  // See: https://github.com/twbs/bootstrap/blob/main/scss/mixins/_visually-hidden.scss\n  position: \"absolute\",\n  border: 0,\n  width: 1,\n  height: 1,\n  padding: 0,\n  margin: -1,\n  overflow: \"hidden\",\n  clip: \"rect(0, 0, 0, 0)\",\n  whiteSpace: \"nowrap\",\n  wordWrap: \"normal\"\n});\nvar NAME = \"VisuallyHidden\";\nvar VisuallyHidden = React.forwardRef(\n  (props, forwardedRef) => {\n    return /* @__PURE__ */ jsx(\n      Primitive.span,\n      {\n        ...props,\n        ref: forwardedRef,\n        style: { ...VISUALLY_HIDDEN_STYLES, ...props.style }\n      }\n    );\n  }\n);\nVisuallyHidden.displayName = NAME;\nvar Root = VisuallyHidden;\nexport {\n  Root,\n  VISUALLY_HIDDEN_STYLES,\n  VisuallyHidden\n};\n//# sourceMappingURL=index.mjs.map\n", "import * as React from 'react';\nimport * as ReactDOM from 'react-dom';\nimport { clamp } from '@radix-ui/number';\nimport { composeEventHandlers } from '@radix-ui/primitive';\nimport { createCollection } from '@radix-ui/react-collection';\nimport { useComposedRefs } from '@radix-ui/react-compose-refs';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { useDirection } from '@radix-ui/react-direction';\nimport { DismissableLayer } from '@radix-ui/react-dismissable-layer';\nimport { useFocusGuards } from '@radix-ui/react-focus-guards';\nimport { FocusScope } from '@radix-ui/react-focus-scope';\nimport { useId } from '@radix-ui/react-id';\nimport * as PopperPrimitive from '@radix-ui/react-popper';\nimport { createPopperScope } from '@radix-ui/react-popper';\nimport { Portal as PortalPrimitive } from '@radix-ui/react-portal';\nimport { Primitive } from '@radix-ui/react-primitive';\nimport { createSlot } from '@radix-ui/react-slot';\nimport { useCallbackRef } from '@radix-ui/react-use-callback-ref';\nimport { useControllableState } from '@radix-ui/react-use-controllable-state';\nimport { useLayoutEffect } from '@radix-ui/react-use-layout-effect';\nimport { usePrevious } from '@radix-ui/react-use-previous';\nimport { VISUALLY_HIDDEN_STYLES } from '@radix-ui/react-visually-hidden';\nimport { hideOthers } from 'aria-hidden';\nimport { RemoveScroll } from 'react-remove-scroll';\n\nimport type { Scope } from '@radix-ui/react-context';\n\ntype Direction = 'ltr' | 'rtl';\n\nconst OPEN_KEYS = [' ', 'Enter', 'ArrowUp', 'ArrowDown'];\nconst SELECTION_KEYS = [' ', 'Enter'];\n\n/* -------------------------------------------------------------------------------------------------\n * Select\n * -----------------------------------------------------------------------------------------------*/\n\nconst SELECT_NAME = 'Select';\n\ntype ItemData = { value: string; disabled: boolean; textValue: string };\nconst [Collection, useCollection, createCollectionScope] = createCollection<\n  SelectItemElement,\n  ItemData\n>(SELECT_NAME);\n\ntype ScopedProps<P> = P & { __scopeSelect?: Scope };\nconst [createSelectContext, createSelectScope] = createContextScope(SELECT_NAME, [\n  createCollectionScope,\n  createPopperScope,\n]);\nconst usePopperScope = createPopperScope();\n\ntype SelectContextValue = {\n  trigger: SelectTriggerElement | null;\n  onTriggerChange(node: SelectTriggerElement | null): void;\n  valueNode: SelectValueElement | null;\n  onValueNodeChange(node: SelectValueElement): void;\n  valueNodeHasChildren: boolean;\n  onValueNodeHasChildrenChange(hasChildren: boolean): void;\n  contentId: string;\n  value: string | undefined;\n  onValueChange(value: string): void;\n  open: boolean;\n  required?: boolean;\n  onOpenChange(open: boolean): void;\n  dir: SelectProps['dir'];\n  triggerPointerDownPosRef: React.MutableRefObject<{ x: number; y: number } | null>;\n  disabled?: boolean;\n};\n\nconst [SelectProvider, useSelectContext] = createSelectContext<SelectContextValue>(SELECT_NAME);\n\ntype NativeOption = React.ReactElement<React.ComponentProps<'option'>>;\n\ntype SelectNativeOptionsContextValue = {\n  onNativeOptionAdd(option: NativeOption): void;\n  onNativeOptionRemove(option: NativeOption): void;\n};\nconst [SelectNativeOptionsProvider, useSelectNativeOptionsContext] =\n  createSelectContext<SelectNativeOptionsContextValue>(SELECT_NAME);\n\ninterface ControlledClearableSelectProps {\n  value: string | undefined;\n  defaultValue?: never;\n  onValueChange: (value: string | undefined) => void;\n}\n\ninterface ControlledUnclearableSelectProps {\n  value: string;\n  defaultValue?: never;\n  onValueChange: (value: string) => void;\n}\n\ninterface UncontrolledSelectProps {\n  value?: never;\n  defaultValue?: string;\n  onValueChange?: {\n    (value: string): void;\n    (value: string | undefined): void;\n  };\n}\n\ntype SelectControlProps =\n  | ControlledClearableSelectProps\n  | ControlledUnclearableSelectProps\n  | UncontrolledSelectProps;\n\ninterface SelectSharedProps {\n  children?: React.ReactNode;\n  open?: boolean;\n  defaultOpen?: boolean;\n  onOpenChange?(open: boolean): void;\n  dir?: Direction;\n  name?: string;\n  autoComplete?: string;\n  disabled?: boolean;\n  required?: boolean;\n  form?: string;\n}\n\n// TODO: Should improve typing somewhat, but this would be a breaking change.\n// Consider using in the next major version (along with some testing to be sure\n// it works as expected and doesn't cause problems)\ntype _FutureSelectProps = SelectSharedProps & SelectControlProps;\n\ntype SelectProps = SelectSharedProps & {\n  value?: string;\n  defaultValue?: string;\n  onValueChange?(value: string): void;\n};\n\nconst Select: React.FC<SelectProps> = (props: ScopedProps<SelectProps>) => {\n  const {\n    __scopeSelect,\n    children,\n    open: openProp,\n    defaultOpen,\n    onOpenChange,\n    value: valueProp,\n    defaultValue,\n    onValueChange,\n    dir,\n    name,\n    autoComplete,\n    disabled,\n    required,\n    form,\n  } = props;\n  const popperScope = usePopperScope(__scopeSelect);\n  const [trigger, setTrigger] = React.useState<SelectTriggerElement | null>(null);\n  const [valueNode, setValueNode] = React.useState<SelectValueElement | null>(null);\n  const [valueNodeHasChildren, setValueNodeHasChildren] = React.useState(false);\n  const direction = useDirection(dir);\n  const [open, setOpen] = useControllableState({\n    prop: openProp,\n    defaultProp: defaultOpen ?? false,\n    onChange: onOpenChange,\n    caller: SELECT_NAME,\n  });\n  const [value, setValue] = useControllableState({\n    prop: valueProp,\n    defaultProp: defaultValue,\n    onChange: onValueChange as any,\n    caller: SELECT_NAME,\n  });\n  const triggerPointerDownPosRef = React.useRef<{ x: number; y: number } | null>(null);\n\n  // We set this to true by default so that events bubble to forms without JS (SSR)\n  const isFormControl = trigger ? form || !!trigger.closest('form') : true;\n  const [nativeOptionsSet, setNativeOptionsSet] = React.useState(new Set<NativeOption>());\n\n  // The native `select` only associates the correct default value if the corresponding\n  // `option` is rendered as a child **at the same time** as itself.\n  // Because it might take a few renders for our items to gather the information to build\n  // the native `option`(s), we generate a key on the `select` to make sure React re-builds it\n  // each time the options change.\n  const nativeSelectKey = Array.from(nativeOptionsSet)\n    .map((option) => option.props.value)\n    .join(';');\n\n  return (\n    <PopperPrimitive.Root {...popperScope}>\n      <SelectProvider\n        required={required}\n        scope={__scopeSelect}\n        trigger={trigger}\n        onTriggerChange={setTrigger}\n        valueNode={valueNode}\n        onValueNodeChange={setValueNode}\n        valueNodeHasChildren={valueNodeHasChildren}\n        onValueNodeHasChildrenChange={setValueNodeHasChildren}\n        contentId={useId()}\n        value={value}\n        onValueChange={setValue}\n        open={open}\n        onOpenChange={setOpen}\n        dir={direction}\n        triggerPointerDownPosRef={triggerPointerDownPosRef}\n        disabled={disabled}\n      >\n        <Collection.Provider scope={__scopeSelect}>\n          <SelectNativeOptionsProvider\n            scope={props.__scopeSelect}\n            onNativeOptionAdd={React.useCallback((option) => {\n              setNativeOptionsSet((prev) => new Set(prev).add(option));\n            }, [])}\n            onNativeOptionRemove={React.useCallback((option) => {\n              setNativeOptionsSet((prev) => {\n                const optionsSet = new Set(prev);\n                optionsSet.delete(option);\n                return optionsSet;\n              });\n            }, [])}\n          >\n            {children}\n          </SelectNativeOptionsProvider>\n        </Collection.Provider>\n\n        {isFormControl ? (\n          <SelectBubbleInput\n            key={nativeSelectKey}\n            aria-hidden\n            required={required}\n            tabIndex={-1}\n            name={name}\n            autoComplete={autoComplete}\n            value={value}\n            // enable form autofill\n            onChange={(event) => setValue(event.target.value)}\n            disabled={disabled}\n            form={form}\n          >\n            {value === undefined ? <option value=\"\" /> : null}\n            {Array.from(nativeOptionsSet)}\n          </SelectBubbleInput>\n        ) : null}\n      </SelectProvider>\n    </PopperPrimitive.Root>\n  );\n};\n\nSelect.displayName = SELECT_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectTrigger\n * -----------------------------------------------------------------------------------------------*/\n\nconst TRIGGER_NAME = 'SelectTrigger';\n\ntype SelectTriggerElement = React.ComponentRef<typeof Primitive.button>;\ntype PrimitiveButtonProps = React.ComponentPropsWithoutRef<typeof Primitive.button>;\ninterface SelectTriggerProps extends PrimitiveButtonProps {}\n\nconst SelectTrigger = React.forwardRef<SelectTriggerElement, SelectTriggerProps>(\n  (props: ScopedProps<SelectTriggerProps>, forwardedRef) => {\n    const { __scopeSelect, disabled = false, ...triggerProps } = props;\n    const popperScope = usePopperScope(__scopeSelect);\n    const context = useSelectContext(TRIGGER_NAME, __scopeSelect);\n    const isDisabled = context.disabled || disabled;\n    const composedRefs = useComposedRefs(forwardedRef, context.onTriggerChange);\n    const getItems = useCollection(__scopeSelect);\n    const pointerTypeRef = React.useRef<React.PointerEvent['pointerType']>('touch');\n\n    const [searchRef, handleTypeaheadSearch, resetTypeahead] = useTypeaheadSearch((search) => {\n      const enabledItems = getItems().filter((item) => !item.disabled);\n      const currentItem = enabledItems.find((item) => item.value === context.value);\n      const nextItem = findNextItem(enabledItems, search, currentItem);\n      if (nextItem !== undefined) {\n        context.onValueChange(nextItem.value);\n      }\n    });\n\n    const handleOpen = (pointerEvent?: React.MouseEvent | React.PointerEvent) => {\n      if (!isDisabled) {\n        context.onOpenChange(true);\n        // reset typeahead when we open\n        resetTypeahead();\n      }\n\n      if (pointerEvent) {\n        context.triggerPointerDownPosRef.current = {\n          x: Math.round(pointerEvent.pageX),\n          y: Math.round(pointerEvent.pageY),\n        };\n      }\n    };\n\n    return (\n      <PopperPrimitive.Anchor asChild {...popperScope}>\n        <Primitive.button\n          type=\"button\"\n          role=\"combobox\"\n          aria-controls={context.contentId}\n          aria-expanded={context.open}\n          aria-required={context.required}\n          aria-autocomplete=\"none\"\n          dir={context.dir}\n          data-state={context.open ? 'open' : 'closed'}\n          disabled={isDisabled}\n          data-disabled={isDisabled ? '' : undefined}\n          data-placeholder={shouldShowPlaceholder(context.value) ? '' : undefined}\n          {...triggerProps}\n          ref={composedRefs}\n          // Enable compatibility with native label or custom `Label` \"click\" for Safari:\n          onClick={composeEventHandlers(triggerProps.onClick, (event) => {\n            // Whilst browsers generally have no issue focusing the trigger when clicking\n            // on a label, Safari seems to struggle with the fact that there's no `onClick`.\n            // We force `focus` in this case. Note: this doesn't create any other side-effect\n            // because we are preventing default in `onPointerDown` so effectively\n            // this only runs for a label \"click\"\n            event.currentTarget.focus();\n\n            // Open on click when using a touch or pen device\n            if (pointerTypeRef.current !== 'mouse') {\n              handleOpen(event);\n            }\n          })}\n          onPointerDown={composeEventHandlers(triggerProps.onPointerDown, (event) => {\n            pointerTypeRef.current = event.pointerType;\n\n            // prevent implicit pointer capture\n            // https://www.w3.org/TR/pointerevents3/#implicit-pointer-capture\n            const target = event.target as HTMLElement;\n            if (target.hasPointerCapture(event.pointerId)) {\n              target.releasePointerCapture(event.pointerId);\n            }\n\n            // only call handler if it's the left button (mousedown gets triggered by all mouse buttons)\n            // but not when the control key is pressed (avoiding MacOS right click); also not for touch\n            // devices because that would open the menu on scroll. (pen devices behave as touch on iOS).\n            if (event.button === 0 && event.ctrlKey === false && event.pointerType === 'mouse') {\n              handleOpen(event);\n              // prevent trigger from stealing focus from the active item after opening.\n              event.preventDefault();\n            }\n          })}\n          onKeyDown={composeEventHandlers(triggerProps.onKeyDown, (event) => {\n            const isTypingAhead = searchRef.current !== '';\n            const isModifierKey = event.ctrlKey || event.altKey || event.metaKey;\n            if (!isModifierKey && event.key.length === 1) handleTypeaheadSearch(event.key);\n            if (isTypingAhead && event.key === ' ') return;\n            if (OPEN_KEYS.includes(event.key)) {\n              handleOpen();\n              event.preventDefault();\n            }\n          })}\n        />\n      </PopperPrimitive.Anchor>\n    );\n  }\n);\n\nSelectTrigger.displayName = TRIGGER_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectValue\n * -----------------------------------------------------------------------------------------------*/\n\nconst VALUE_NAME = 'SelectValue';\n\ntype SelectValueElement = React.ComponentRef<typeof Primitive.span>;\ntype PrimitiveSpanProps = React.ComponentPropsWithoutRef<typeof Primitive.span>;\ninterface SelectValueProps extends Omit<PrimitiveSpanProps, 'placeholder'> {\n  placeholder?: React.ReactNode;\n}\n\nconst SelectValue = React.forwardRef<SelectValueElement, SelectValueProps>(\n  (props: ScopedProps<SelectValueProps>, forwardedRef) => {\n    // We ignore `className` and `style` as this part shouldn't be styled.\n    const { __scopeSelect, className, style, children, placeholder = '', ...valueProps } = props;\n    const context = useSelectContext(VALUE_NAME, __scopeSelect);\n    const { onValueNodeHasChildrenChange } = context;\n    const hasChildren = children !== undefined;\n    const composedRefs = useComposedRefs(forwardedRef, context.onValueNodeChange);\n\n    useLayoutEffect(() => {\n      onValueNodeHasChildrenChange(hasChildren);\n    }, [onValueNodeHasChildrenChange, hasChildren]);\n\n    return (\n      <Primitive.span\n        {...valueProps}\n        ref={composedRefs}\n        // we don't want events from the portalled `SelectValue` children to bubble\n        // through the item they came from\n        style={{ pointerEvents: 'none' }}\n      >\n        {shouldShowPlaceholder(context.value) ? <>{placeholder}</> : children}\n      </Primitive.span>\n    );\n  }\n);\n\nSelectValue.displayName = VALUE_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectIcon\n * -----------------------------------------------------------------------------------------------*/\n\nconst ICON_NAME = 'SelectIcon';\n\ntype SelectIconElement = React.ComponentRef<typeof Primitive.span>;\ninterface SelectIconProps extends PrimitiveSpanProps {}\n\nconst SelectIcon = React.forwardRef<SelectIconElement, SelectIconProps>(\n  (props: ScopedProps<SelectIconProps>, forwardedRef) => {\n    const { __scopeSelect, children, ...iconProps } = props;\n    return (\n      <Primitive.span aria-hidden {...iconProps} ref={forwardedRef}>\n        {children || '▼'}\n      </Primitive.span>\n    );\n  }\n);\n\nSelectIcon.displayName = ICON_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectPortal\n * -----------------------------------------------------------------------------------------------*/\n\nconst PORTAL_NAME = 'SelectPortal';\n\ntype PortalProps = React.ComponentPropsWithoutRef<typeof PortalPrimitive>;\ninterface SelectPortalProps {\n  children?: React.ReactNode;\n  /**\n   * Specify a container element to portal the content into.\n   */\n  container?: PortalProps['container'];\n}\n\nconst SelectPortal: React.FC<SelectPortalProps> = (props: ScopedProps<SelectPortalProps>) => {\n  return <PortalPrimitive asChild {...props} />;\n};\n\nSelectPortal.displayName = PORTAL_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectContent\n * -----------------------------------------------------------------------------------------------*/\n\nconst CONTENT_NAME = 'SelectContent';\n\ntype SelectContentElement = SelectContentImplElement;\ninterface SelectContentProps extends SelectContentImplProps {}\n\nconst SelectContent = React.forwardRef<SelectContentElement, SelectContentProps>(\n  (props: ScopedProps<SelectContentProps>, forwardedRef) => {\n    const context = useSelectContext(CONTENT_NAME, props.__scopeSelect);\n    const [fragment, setFragment] = React.useState<DocumentFragment>();\n\n    // setting the fragment in `useLayoutEffect` as `DocumentFragment` doesn't exist on the server\n    useLayoutEffect(() => {\n      setFragment(new DocumentFragment());\n    }, []);\n\n    if (!context.open) {\n      const frag = fragment as Element | undefined;\n      return frag\n        ? ReactDOM.createPortal(\n            <SelectContentProvider scope={props.__scopeSelect}>\n              <Collection.Slot scope={props.__scopeSelect}>\n                <div>{props.children}</div>\n              </Collection.Slot>\n            </SelectContentProvider>,\n            frag\n          )\n        : null;\n    }\n\n    return <SelectContentImpl {...props} ref={forwardedRef} />;\n  }\n);\n\nSelectContent.displayName = CONTENT_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectContentImpl\n * -----------------------------------------------------------------------------------------------*/\n\nconst CONTENT_MARGIN = 10;\n\ntype SelectContentContextValue = {\n  content?: SelectContentElement | null;\n  viewport?: SelectViewportElement | null;\n  onViewportChange?: (node: SelectViewportElement | null) => void;\n  itemRefCallback?: (node: SelectItemElement | null, value: string, disabled: boolean) => void;\n  selectedItem?: SelectItemElement | null;\n  onItemLeave?: () => void;\n  itemTextRefCallback?: (\n    node: SelectItemTextElement | null,\n    value: string,\n    disabled: boolean\n  ) => void;\n  focusSelectedItem?: () => void;\n  selectedItemText?: SelectItemTextElement | null;\n  position?: SelectContentProps['position'];\n  isPositioned?: boolean;\n  searchRef?: React.RefObject<string>;\n};\n\nconst [SelectContentProvider, useSelectContentContext] =\n  createSelectContext<SelectContentContextValue>(CONTENT_NAME);\n\nconst CONTENT_IMPL_NAME = 'SelectContentImpl';\n\ntype SelectContentImplElement = SelectPopperPositionElement | SelectItemAlignedPositionElement;\ntype DismissableLayerProps = React.ComponentPropsWithoutRef<typeof DismissableLayer>;\ntype FocusScopeProps = React.ComponentPropsWithoutRef<typeof FocusScope>;\n\ntype SelectPopperPrivateProps = { onPlaced?: PopperContentProps['onPlaced'] };\n\ninterface SelectContentImplProps\n  extends Omit<SelectPopperPositionProps, keyof SelectPopperPrivateProps>,\n    Omit<SelectItemAlignedPositionProps, keyof SelectPopperPrivateProps> {\n  /**\n   * Event handler called when auto-focusing on close.\n   * Can be prevented.\n   */\n  onCloseAutoFocus?: FocusScopeProps['onUnmountAutoFocus'];\n  /**\n   * Event handler called when the escape key is down.\n   * Can be prevented.\n   */\n  onEscapeKeyDown?: DismissableLayerProps['onEscapeKeyDown'];\n  /**\n   * Event handler called when the a `pointerdown` event happens outside of the `DismissableLayer`.\n   * Can be prevented.\n   */\n  onPointerDownOutside?: DismissableLayerProps['onPointerDownOutside'];\n\n  position?: 'item-aligned' | 'popper';\n}\n\nconst Slot = createSlot('SelectContent.RemoveScroll');\n\nconst SelectContentImpl = React.forwardRef<SelectContentImplElement, SelectContentImplProps>(\n  (props: ScopedProps<SelectContentImplProps>, forwardedRef) => {\n    const {\n      __scopeSelect,\n      position = 'item-aligned',\n      onCloseAutoFocus,\n      onEscapeKeyDown,\n      onPointerDownOutside,\n      //\n      // PopperContent props\n      side,\n      sideOffset,\n      align,\n      alignOffset,\n      arrowPadding,\n      collisionBoundary,\n      collisionPadding,\n      sticky,\n      hideWhenDetached,\n      avoidCollisions,\n      //\n      ...contentProps\n    } = props;\n    const context = useSelectContext(CONTENT_NAME, __scopeSelect);\n    const [content, setContent] = React.useState<SelectContentImplElement | null>(null);\n    const [viewport, setViewport] = React.useState<SelectViewportElement | null>(null);\n    const composedRefs = useComposedRefs(forwardedRef, (node) => setContent(node));\n    const [selectedItem, setSelectedItem] = React.useState<SelectItemElement | null>(null);\n    const [selectedItemText, setSelectedItemText] = React.useState<SelectItemTextElement | null>(\n      null\n    );\n    const getItems = useCollection(__scopeSelect);\n    const [isPositioned, setIsPositioned] = React.useState(false);\n    const firstValidItemFoundRef = React.useRef(false);\n\n    // aria-hide everything except the content (better supported equivalent to setting aria-modal)\n    React.useEffect(() => {\n      if (content) return hideOthers(content);\n    }, [content]);\n\n    // Make sure the whole tree has focus guards as our `Select` may be\n    // the last element in the DOM (because of the `Portal`)\n    useFocusGuards();\n\n    const focusFirst = React.useCallback(\n      (candidates: Array<HTMLElement | null>) => {\n        const [firstItem, ...restItems] = getItems().map((item) => item.ref.current);\n        const [lastItem] = restItems.slice(-1);\n\n        const PREVIOUSLY_FOCUSED_ELEMENT = document.activeElement;\n        for (const candidate of candidates) {\n          // if focus is already where we want to go, we don't want to keep going through the candidates\n          if (candidate === PREVIOUSLY_FOCUSED_ELEMENT) return;\n          candidate?.scrollIntoView({ block: 'nearest' });\n          // viewport might have padding so scroll to its edges when focusing first/last items.\n          if (candidate === firstItem && viewport) viewport.scrollTop = 0;\n          if (candidate === lastItem && viewport) viewport.scrollTop = viewport.scrollHeight;\n          candidate?.focus();\n          if (document.activeElement !== PREVIOUSLY_FOCUSED_ELEMENT) return;\n        }\n      },\n      [getItems, viewport]\n    );\n\n    const focusSelectedItem = React.useCallback(\n      () => focusFirst([selectedItem, content]),\n      [focusFirst, selectedItem, content]\n    );\n\n    // Since this is not dependent on layout, we want to ensure this runs at the same time as\n    // other effects across components. Hence why we don't call `focusSelectedItem` inside `position`.\n    React.useEffect(() => {\n      if (isPositioned) {\n        focusSelectedItem();\n      }\n    }, [isPositioned, focusSelectedItem]);\n\n    // prevent selecting items on `pointerup` in some cases after opening from `pointerdown`\n    // and close on `pointerup` outside.\n    const { onOpenChange, triggerPointerDownPosRef } = context;\n    React.useEffect(() => {\n      if (content) {\n        let pointerMoveDelta = { x: 0, y: 0 };\n\n        const handlePointerMove = (event: PointerEvent) => {\n          pointerMoveDelta = {\n            x: Math.abs(Math.round(event.pageX) - (triggerPointerDownPosRef.current?.x ?? 0)),\n            y: Math.abs(Math.round(event.pageY) - (triggerPointerDownPosRef.current?.y ?? 0)),\n          };\n        };\n        const handlePointerUp = (event: PointerEvent) => {\n          // If the pointer hasn't moved by a certain threshold then we prevent selecting item on `pointerup`.\n          if (pointerMoveDelta.x <= 10 && pointerMoveDelta.y <= 10) {\n            event.preventDefault();\n          } else {\n            // otherwise, if the event was outside the content, close.\n            if (!content.contains(event.target as HTMLElement)) {\n              onOpenChange(false);\n            }\n          }\n          document.removeEventListener('pointermove', handlePointerMove);\n          triggerPointerDownPosRef.current = null;\n        };\n\n        if (triggerPointerDownPosRef.current !== null) {\n          document.addEventListener('pointermove', handlePointerMove);\n          document.addEventListener('pointerup', handlePointerUp, { capture: true, once: true });\n        }\n\n        return () => {\n          document.removeEventListener('pointermove', handlePointerMove);\n          document.removeEventListener('pointerup', handlePointerUp, { capture: true });\n        };\n      }\n    }, [content, onOpenChange, triggerPointerDownPosRef]);\n\n    React.useEffect(() => {\n      const close = () => onOpenChange(false);\n      window.addEventListener('blur', close);\n      window.addEventListener('resize', close);\n      return () => {\n        window.removeEventListener('blur', close);\n        window.removeEventListener('resize', close);\n      };\n    }, [onOpenChange]);\n\n    const [searchRef, handleTypeaheadSearch] = useTypeaheadSearch((search) => {\n      const enabledItems = getItems().filter((item) => !item.disabled);\n      const currentItem = enabledItems.find((item) => item.ref.current === document.activeElement);\n      const nextItem = findNextItem(enabledItems, search, currentItem);\n      if (nextItem) {\n        /**\n         * Imperative focus during keydown is risky so we prevent React's batching updates\n         * to avoid potential bugs. See: https://github.com/facebook/react/issues/20332\n         */\n        setTimeout(() => (nextItem.ref.current as HTMLElement).focus());\n      }\n    });\n\n    const itemRefCallback = React.useCallback(\n      (node: SelectItemElement | null, value: string, disabled: boolean) => {\n        const isFirstValidItem = !firstValidItemFoundRef.current && !disabled;\n        const isSelectedItem = context.value !== undefined && context.value === value;\n        if (isSelectedItem || isFirstValidItem) {\n          setSelectedItem(node);\n          if (isFirstValidItem) firstValidItemFoundRef.current = true;\n        }\n      },\n      [context.value]\n    );\n    const handleItemLeave = React.useCallback(() => content?.focus(), [content]);\n    const itemTextRefCallback = React.useCallback(\n      (node: SelectItemTextElement | null, value: string, disabled: boolean) => {\n        const isFirstValidItem = !firstValidItemFoundRef.current && !disabled;\n        const isSelectedItem = context.value !== undefined && context.value === value;\n        if (isSelectedItem || isFirstValidItem) {\n          setSelectedItemText(node);\n        }\n      },\n      [context.value]\n    );\n\n    const SelectPosition = position === 'popper' ? SelectPopperPosition : SelectItemAlignedPosition;\n\n    // Silently ignore props that are not supported by `SelectItemAlignedPosition`\n    const popperContentProps =\n      SelectPosition === SelectPopperPosition\n        ? {\n            side,\n            sideOffset,\n            align,\n            alignOffset,\n            arrowPadding,\n            collisionBoundary,\n            collisionPadding,\n            sticky,\n            hideWhenDetached,\n            avoidCollisions,\n          }\n        : {};\n\n    return (\n      <SelectContentProvider\n        scope={__scopeSelect}\n        content={content}\n        viewport={viewport}\n        onViewportChange={setViewport}\n        itemRefCallback={itemRefCallback}\n        selectedItem={selectedItem}\n        onItemLeave={handleItemLeave}\n        itemTextRefCallback={itemTextRefCallback}\n        focusSelectedItem={focusSelectedItem}\n        selectedItemText={selectedItemText}\n        position={position}\n        isPositioned={isPositioned}\n        searchRef={searchRef}\n      >\n        <RemoveScroll as={Slot} allowPinchZoom>\n          <FocusScope\n            asChild\n            // we make sure we're not trapping once it's been closed\n            // (closed !== unmounted when animating out)\n            trapped={context.open}\n            onMountAutoFocus={(event) => {\n              // we prevent open autofocus because we manually focus the selected item\n              event.preventDefault();\n            }}\n            onUnmountAutoFocus={composeEventHandlers(onCloseAutoFocus, (event) => {\n              context.trigger?.focus({ preventScroll: true });\n              event.preventDefault();\n            })}\n          >\n            <DismissableLayer\n              asChild\n              disableOutsidePointerEvents\n              onEscapeKeyDown={onEscapeKeyDown}\n              onPointerDownOutside={onPointerDownOutside}\n              // When focus is trapped, a focusout event may still happen.\n              // We make sure we don't trigger our `onDismiss` in such case.\n              onFocusOutside={(event) => event.preventDefault()}\n              onDismiss={() => context.onOpenChange(false)}\n            >\n              <SelectPosition\n                role=\"listbox\"\n                id={context.contentId}\n                data-state={context.open ? 'open' : 'closed'}\n                dir={context.dir}\n                onContextMenu={(event) => event.preventDefault()}\n                {...contentProps}\n                {...popperContentProps}\n                onPlaced={() => setIsPositioned(true)}\n                ref={composedRefs}\n                style={{\n                  // flex layout so we can place the scroll buttons properly\n                  display: 'flex',\n                  flexDirection: 'column',\n                  // reset the outline by default as the content MAY get focused\n                  outline: 'none',\n                  ...contentProps.style,\n                }}\n                onKeyDown={composeEventHandlers(contentProps.onKeyDown, (event) => {\n                  const isModifierKey = event.ctrlKey || event.altKey || event.metaKey;\n\n                  // select should not be navigated using tab key so we prevent it\n                  if (event.key === 'Tab') event.preventDefault();\n\n                  if (!isModifierKey && event.key.length === 1) handleTypeaheadSearch(event.key);\n\n                  if (['ArrowUp', 'ArrowDown', 'Home', 'End'].includes(event.key)) {\n                    const items = getItems().filter((item) => !item.disabled);\n                    let candidateNodes = items.map((item) => item.ref.current!);\n\n                    if (['ArrowUp', 'End'].includes(event.key)) {\n                      candidateNodes = candidateNodes.slice().reverse();\n                    }\n                    if (['ArrowUp', 'ArrowDown'].includes(event.key)) {\n                      const currentElement = event.target as SelectItemElement;\n                      const currentIndex = candidateNodes.indexOf(currentElement);\n                      candidateNodes = candidateNodes.slice(currentIndex + 1);\n                    }\n\n                    /**\n                     * Imperative focus during keydown is risky so we prevent React's batching updates\n                     * to avoid potential bugs. See: https://github.com/facebook/react/issues/20332\n                     */\n                    setTimeout(() => focusFirst(candidateNodes));\n\n                    event.preventDefault();\n                  }\n                })}\n              />\n            </DismissableLayer>\n          </FocusScope>\n        </RemoveScroll>\n      </SelectContentProvider>\n    );\n  }\n);\n\nSelectContentImpl.displayName = CONTENT_IMPL_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectItemAlignedPosition\n * -----------------------------------------------------------------------------------------------*/\n\nconst ITEM_ALIGNED_POSITION_NAME = 'SelectItemAlignedPosition';\n\ntype SelectItemAlignedPositionElement = React.ComponentRef<typeof Primitive.div>;\ninterface SelectItemAlignedPositionProps extends PrimitiveDivProps, SelectPopperPrivateProps {}\n\nconst SelectItemAlignedPosition = React.forwardRef<\n  SelectItemAlignedPositionElement,\n  SelectItemAlignedPositionProps\n>((props: ScopedProps<SelectItemAlignedPositionProps>, forwardedRef) => {\n  const { __scopeSelect, onPlaced, ...popperProps } = props;\n  const context = useSelectContext(CONTENT_NAME, __scopeSelect);\n  const contentContext = useSelectContentContext(CONTENT_NAME, __scopeSelect);\n  const [contentWrapper, setContentWrapper] = React.useState<HTMLDivElement | null>(null);\n  const [content, setContent] = React.useState<SelectItemAlignedPositionElement | null>(null);\n  const composedRefs = useComposedRefs(forwardedRef, (node) => setContent(node));\n  const getItems = useCollection(__scopeSelect);\n  const shouldExpandOnScrollRef = React.useRef(false);\n  const shouldRepositionRef = React.useRef(true);\n\n  const { viewport, selectedItem, selectedItemText, focusSelectedItem } = contentContext;\n  const position = React.useCallback(() => {\n    if (\n      context.trigger &&\n      context.valueNode &&\n      contentWrapper &&\n      content &&\n      viewport &&\n      selectedItem &&\n      selectedItemText\n    ) {\n      const triggerRect = context.trigger.getBoundingClientRect();\n\n      // -----------------------------------------------------------------------------------------\n      //  Horizontal positioning\n      // -----------------------------------------------------------------------------------------\n      const contentRect = content.getBoundingClientRect();\n      const valueNodeRect = context.valueNode.getBoundingClientRect();\n      const itemTextRect = selectedItemText.getBoundingClientRect();\n\n      if (context.dir !== 'rtl') {\n        const itemTextOffset = itemTextRect.left - contentRect.left;\n        const left = valueNodeRect.left - itemTextOffset;\n        const leftDelta = triggerRect.left - left;\n        const minContentWidth = triggerRect.width + leftDelta;\n        const contentWidth = Math.max(minContentWidth, contentRect.width);\n        const rightEdge = window.innerWidth - CONTENT_MARGIN;\n        const clampedLeft = clamp(left, [\n          CONTENT_MARGIN,\n          // Prevents the content from going off the starting edge of the\n          // viewport. It may still go off the ending edge, but this can be\n          // controlled by the user since they may want to manage overflow in a\n          // specific way.\n          // https://github.com/radix-ui/primitives/issues/2049\n          Math.max(CONTENT_MARGIN, rightEdge - contentWidth),\n        ]);\n\n        contentWrapper.style.minWidth = minContentWidth + 'px';\n        contentWrapper.style.left = clampedLeft + 'px';\n      } else {\n        const itemTextOffset = contentRect.right - itemTextRect.right;\n        const right = window.innerWidth - valueNodeRect.right - itemTextOffset;\n        const rightDelta = window.innerWidth - triggerRect.right - right;\n        const minContentWidth = triggerRect.width + rightDelta;\n        const contentWidth = Math.max(minContentWidth, contentRect.width);\n        const leftEdge = window.innerWidth - CONTENT_MARGIN;\n        const clampedRight = clamp(right, [\n          CONTENT_MARGIN,\n          Math.max(CONTENT_MARGIN, leftEdge - contentWidth),\n        ]);\n\n        contentWrapper.style.minWidth = minContentWidth + 'px';\n        contentWrapper.style.right = clampedRight + 'px';\n      }\n\n      // -----------------------------------------------------------------------------------------\n      // Vertical positioning\n      // -----------------------------------------------------------------------------------------\n      const items = getItems();\n      const availableHeight = window.innerHeight - CONTENT_MARGIN * 2;\n      const itemsHeight = viewport.scrollHeight;\n\n      const contentStyles = window.getComputedStyle(content);\n      const contentBorderTopWidth = parseInt(contentStyles.borderTopWidth, 10);\n      const contentPaddingTop = parseInt(contentStyles.paddingTop, 10);\n      const contentBorderBottomWidth = parseInt(contentStyles.borderBottomWidth, 10);\n      const contentPaddingBottom = parseInt(contentStyles.paddingBottom, 10);\n      const fullContentHeight = contentBorderTopWidth + contentPaddingTop + itemsHeight + contentPaddingBottom + contentBorderBottomWidth; // prettier-ignore\n      const minContentHeight = Math.min(selectedItem.offsetHeight * 5, fullContentHeight);\n\n      const viewportStyles = window.getComputedStyle(viewport);\n      const viewportPaddingTop = parseInt(viewportStyles.paddingTop, 10);\n      const viewportPaddingBottom = parseInt(viewportStyles.paddingBottom, 10);\n\n      const topEdgeToTriggerMiddle = triggerRect.top + triggerRect.height / 2 - CONTENT_MARGIN;\n      const triggerMiddleToBottomEdge = availableHeight - topEdgeToTriggerMiddle;\n\n      const selectedItemHalfHeight = selectedItem.offsetHeight / 2;\n      const itemOffsetMiddle = selectedItem.offsetTop + selectedItemHalfHeight;\n      const contentTopToItemMiddle = contentBorderTopWidth + contentPaddingTop + itemOffsetMiddle;\n      const itemMiddleToContentBottom = fullContentHeight - contentTopToItemMiddle;\n\n      const willAlignWithoutTopOverflow = contentTopToItemMiddle <= topEdgeToTriggerMiddle;\n\n      if (willAlignWithoutTopOverflow) {\n        const isLastItem =\n          items.length > 0 && selectedItem === items[items.length - 1]!.ref.current;\n        contentWrapper.style.bottom = 0 + 'px';\n        const viewportOffsetBottom =\n          content.clientHeight - viewport.offsetTop - viewport.offsetHeight;\n        const clampedTriggerMiddleToBottomEdge = Math.max(\n          triggerMiddleToBottomEdge,\n          selectedItemHalfHeight +\n            // viewport might have padding bottom, include it to avoid a scrollable viewport\n            (isLastItem ? viewportPaddingBottom : 0) +\n            viewportOffsetBottom +\n            contentBorderBottomWidth\n        );\n        const height = contentTopToItemMiddle + clampedTriggerMiddleToBottomEdge;\n        contentWrapper.style.height = height + 'px';\n      } else {\n        const isFirstItem = items.length > 0 && selectedItem === items[0]!.ref.current;\n        contentWrapper.style.top = 0 + 'px';\n        const clampedTopEdgeToTriggerMiddle = Math.max(\n          topEdgeToTriggerMiddle,\n          contentBorderTopWidth +\n            viewport.offsetTop +\n            // viewport might have padding top, include it to avoid a scrollable viewport\n            (isFirstItem ? viewportPaddingTop : 0) +\n            selectedItemHalfHeight\n        );\n        const height = clampedTopEdgeToTriggerMiddle + itemMiddleToContentBottom;\n        contentWrapper.style.height = height + 'px';\n        viewport.scrollTop = contentTopToItemMiddle - topEdgeToTriggerMiddle + viewport.offsetTop;\n      }\n\n      contentWrapper.style.margin = `${CONTENT_MARGIN}px 0`;\n      contentWrapper.style.minHeight = minContentHeight + 'px';\n      contentWrapper.style.maxHeight = availableHeight + 'px';\n      // -----------------------------------------------------------------------------------------\n\n      onPlaced?.();\n\n      // we don't want the initial scroll position adjustment to trigger \"expand on scroll\"\n      // so we explicitly turn it on only after they've registered.\n      requestAnimationFrame(() => (shouldExpandOnScrollRef.current = true));\n    }\n  }, [\n    getItems,\n    context.trigger,\n    context.valueNode,\n    contentWrapper,\n    content,\n    viewport,\n    selectedItem,\n    selectedItemText,\n    context.dir,\n    onPlaced,\n  ]);\n\n  useLayoutEffect(() => position(), [position]);\n\n  // copy z-index from content to wrapper\n  const [contentZIndex, setContentZIndex] = React.useState<string>();\n  useLayoutEffect(() => {\n    if (content) setContentZIndex(window.getComputedStyle(content).zIndex);\n  }, [content]);\n\n  // When the viewport becomes scrollable at the top, the scroll up button will mount.\n  // Because it is part of the normal flow, it will push down the viewport, thus throwing our\n  // trigger => selectedItem alignment off by the amount the viewport was pushed down.\n  // We wait for this to happen and then re-run the positining logic one more time to account for it.\n  const handleScrollButtonChange = React.useCallback(\n    (node: SelectScrollButtonImplElement | null) => {\n      if (node && shouldRepositionRef.current === true) {\n        position();\n        focusSelectedItem?.();\n        shouldRepositionRef.current = false;\n      }\n    },\n    [position, focusSelectedItem]\n  );\n\n  return (\n    <SelectViewportProvider\n      scope={__scopeSelect}\n      contentWrapper={contentWrapper}\n      shouldExpandOnScrollRef={shouldExpandOnScrollRef}\n      onScrollButtonChange={handleScrollButtonChange}\n    >\n      <div\n        ref={setContentWrapper}\n        style={{\n          display: 'flex',\n          flexDirection: 'column',\n          position: 'fixed',\n          zIndex: contentZIndex,\n        }}\n      >\n        <Primitive.div\n          {...popperProps}\n          ref={composedRefs}\n          style={{\n            // When we get the height of the content, it includes borders. If we were to set\n            // the height without having `boxSizing: 'border-box'` it would be too big.\n            boxSizing: 'border-box',\n            // We need to ensure the content doesn't get taller than the wrapper\n            maxHeight: '100%',\n            ...popperProps.style,\n          }}\n        />\n      </div>\n    </SelectViewportProvider>\n  );\n});\n\nSelectItemAlignedPosition.displayName = ITEM_ALIGNED_POSITION_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectPopperPosition\n * -----------------------------------------------------------------------------------------------*/\n\nconst POPPER_POSITION_NAME = 'SelectPopperPosition';\n\ntype SelectPopperPositionElement = React.ComponentRef<typeof PopperPrimitive.Content>;\ntype PopperContentProps = React.ComponentPropsWithoutRef<typeof PopperPrimitive.Content>;\ninterface SelectPopperPositionProps extends PopperContentProps, SelectPopperPrivateProps {}\n\nconst SelectPopperPosition = React.forwardRef<\n  SelectPopperPositionElement,\n  SelectPopperPositionProps\n>((props: ScopedProps<SelectPopperPositionProps>, forwardedRef) => {\n  const {\n    __scopeSelect,\n    align = 'start',\n    collisionPadding = CONTENT_MARGIN,\n    ...popperProps\n  } = props;\n  const popperScope = usePopperScope(__scopeSelect);\n\n  return (\n    <PopperPrimitive.Content\n      {...popperScope}\n      {...popperProps}\n      ref={forwardedRef}\n      align={align}\n      collisionPadding={collisionPadding}\n      style={{\n        // Ensure border-box for floating-ui calculations\n        boxSizing: 'border-box',\n        ...popperProps.style,\n        // re-namespace exposed content custom properties\n        ...{\n          '--radix-select-content-transform-origin': 'var(--radix-popper-transform-origin)',\n          '--radix-select-content-available-width': 'var(--radix-popper-available-width)',\n          '--radix-select-content-available-height': 'var(--radix-popper-available-height)',\n          '--radix-select-trigger-width': 'var(--radix-popper-anchor-width)',\n          '--radix-select-trigger-height': 'var(--radix-popper-anchor-height)',\n        },\n      }}\n    />\n  );\n});\n\nSelectPopperPosition.displayName = POPPER_POSITION_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectViewport\n * -----------------------------------------------------------------------------------------------*/\n\ntype SelectViewportContextValue = {\n  contentWrapper?: HTMLDivElement | null;\n  shouldExpandOnScrollRef?: React.RefObject<boolean>;\n  onScrollButtonChange?: (node: SelectScrollButtonImplElement | null) => void;\n};\n\nconst [SelectViewportProvider, useSelectViewportContext] =\n  createSelectContext<SelectViewportContextValue>(CONTENT_NAME, {});\n\nconst VIEWPORT_NAME = 'SelectViewport';\n\ntype SelectViewportElement = React.ComponentRef<typeof Primitive.div>;\ntype PrimitiveDivProps = React.ComponentPropsWithoutRef<typeof Primitive.div>;\ninterface SelectViewportProps extends PrimitiveDivProps {\n  nonce?: string;\n}\n\nconst SelectViewport = React.forwardRef<SelectViewportElement, SelectViewportProps>(\n  (props: ScopedProps<SelectViewportProps>, forwardedRef) => {\n    const { __scopeSelect, nonce, ...viewportProps } = props;\n    const contentContext = useSelectContentContext(VIEWPORT_NAME, __scopeSelect);\n    const viewportContext = useSelectViewportContext(VIEWPORT_NAME, __scopeSelect);\n    const composedRefs = useComposedRefs(forwardedRef, contentContext.onViewportChange);\n    const prevScrollTopRef = React.useRef(0);\n    return (\n      <>\n        {/* Hide scrollbars cross-browser and enable momentum scroll for touch devices */}\n        <style\n          dangerouslySetInnerHTML={{\n            __html: `[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}`,\n          }}\n          nonce={nonce}\n        />\n        <Collection.Slot scope={__scopeSelect}>\n          <Primitive.div\n            data-radix-select-viewport=\"\"\n            role=\"presentation\"\n            {...viewportProps}\n            ref={composedRefs}\n            style={{\n              // we use position: 'relative' here on the `viewport` so that when we call\n              // `selectedItem.offsetTop` in calculations, the offset is relative to the viewport\n              // (independent of the scrollUpButton).\n              position: 'relative',\n              flex: 1,\n              // Viewport should only be scrollable in the vertical direction.\n              // This won't work in vertical writing modes, so we'll need to\n              // revisit this if/when that is supported\n              // https://developer.chrome.com/blog/vertical-form-controls\n              overflow: 'hidden auto',\n              ...viewportProps.style,\n            }}\n            onScroll={composeEventHandlers(viewportProps.onScroll, (event) => {\n              const viewport = event.currentTarget;\n              const { contentWrapper, shouldExpandOnScrollRef } = viewportContext;\n              if (shouldExpandOnScrollRef?.current && contentWrapper) {\n                const scrolledBy = Math.abs(prevScrollTopRef.current - viewport.scrollTop);\n                if (scrolledBy > 0) {\n                  const availableHeight = window.innerHeight - CONTENT_MARGIN * 2;\n                  const cssMinHeight = parseFloat(contentWrapper.style.minHeight);\n                  const cssHeight = parseFloat(contentWrapper.style.height);\n                  const prevHeight = Math.max(cssMinHeight, cssHeight);\n\n                  if (prevHeight < availableHeight) {\n                    const nextHeight = prevHeight + scrolledBy;\n                    const clampedNextHeight = Math.min(availableHeight, nextHeight);\n                    const heightDiff = nextHeight - clampedNextHeight;\n\n                    contentWrapper.style.height = clampedNextHeight + 'px';\n                    if (contentWrapper.style.bottom === '0px') {\n                      viewport.scrollTop = heightDiff > 0 ? heightDiff : 0;\n                      // ensure the content stays pinned to the bottom\n                      contentWrapper.style.justifyContent = 'flex-end';\n                    }\n                  }\n                }\n              }\n              prevScrollTopRef.current = viewport.scrollTop;\n            })}\n          />\n        </Collection.Slot>\n      </>\n    );\n  }\n);\n\nSelectViewport.displayName = VIEWPORT_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectGroup\n * -----------------------------------------------------------------------------------------------*/\n\nconst GROUP_NAME = 'SelectGroup';\n\ntype SelectGroupContextValue = { id: string };\n\nconst [SelectGroupContextProvider, useSelectGroupContext] =\n  createSelectContext<SelectGroupContextValue>(GROUP_NAME);\n\ntype SelectGroupElement = React.ComponentRef<typeof Primitive.div>;\ninterface SelectGroupProps extends PrimitiveDivProps {}\n\nconst SelectGroup = React.forwardRef<SelectGroupElement, SelectGroupProps>(\n  (props: ScopedProps<SelectGroupProps>, forwardedRef) => {\n    const { __scopeSelect, ...groupProps } = props;\n    const groupId = useId();\n    return (\n      <SelectGroupContextProvider scope={__scopeSelect} id={groupId}>\n        <Primitive.div role=\"group\" aria-labelledby={groupId} {...groupProps} ref={forwardedRef} />\n      </SelectGroupContextProvider>\n    );\n  }\n);\n\nSelectGroup.displayName = GROUP_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectLabel\n * -----------------------------------------------------------------------------------------------*/\n\nconst LABEL_NAME = 'SelectLabel';\n\ntype SelectLabelElement = React.ComponentRef<typeof Primitive.div>;\ninterface SelectLabelProps extends PrimitiveDivProps {}\n\nconst SelectLabel = React.forwardRef<SelectLabelElement, SelectLabelProps>(\n  (props: ScopedProps<SelectLabelProps>, forwardedRef) => {\n    const { __scopeSelect, ...labelProps } = props;\n    const groupContext = useSelectGroupContext(LABEL_NAME, __scopeSelect);\n    return <Primitive.div id={groupContext.id} {...labelProps} ref={forwardedRef} />;\n  }\n);\n\nSelectLabel.displayName = LABEL_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectItem\n * -----------------------------------------------------------------------------------------------*/\n\nconst ITEM_NAME = 'SelectItem';\n\ntype SelectItemContextValue = {\n  value: string;\n  disabled: boolean;\n  textId: string;\n  isSelected: boolean;\n  onItemTextChange(node: SelectItemTextElement | null): void;\n};\n\nconst [SelectItemContextProvider, useSelectItemContext] =\n  createSelectContext<SelectItemContextValue>(ITEM_NAME);\n\ntype SelectItemElement = React.ComponentRef<typeof Primitive.div>;\ninterface SelectItemProps extends PrimitiveDivProps {\n  value: string;\n  disabled?: boolean;\n  textValue?: string;\n}\n\nconst SelectItem = React.forwardRef<SelectItemElement, SelectItemProps>(\n  (props: ScopedProps<SelectItemProps>, forwardedRef) => {\n    const {\n      __scopeSelect,\n      value,\n      disabled = false,\n      textValue: textValueProp,\n      ...itemProps\n    } = props;\n    const context = useSelectContext(ITEM_NAME, __scopeSelect);\n    const contentContext = useSelectContentContext(ITEM_NAME, __scopeSelect);\n    const isSelected = context.value === value;\n    const [textValue, setTextValue] = React.useState(textValueProp ?? '');\n    const [isFocused, setIsFocused] = React.useState(false);\n    const composedRefs = useComposedRefs(forwardedRef, (node) =>\n      contentContext.itemRefCallback?.(node, value, disabled)\n    );\n    const textId = useId();\n    const pointerTypeRef = React.useRef<React.PointerEvent['pointerType']>('touch');\n\n    const handleSelect = () => {\n      if (!disabled) {\n        context.onValueChange(value);\n        context.onOpenChange(false);\n      }\n    };\n\n    if (value === '') {\n      throw new Error(\n        'A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.'\n      );\n    }\n\n    return (\n      <SelectItemContextProvider\n        scope={__scopeSelect}\n        value={value}\n        disabled={disabled}\n        textId={textId}\n        isSelected={isSelected}\n        onItemTextChange={React.useCallback((node) => {\n          setTextValue((prevTextValue) => prevTextValue || (node?.textContent ?? '').trim());\n        }, [])}\n      >\n        <Collection.ItemSlot\n          scope={__scopeSelect}\n          value={value}\n          disabled={disabled}\n          textValue={textValue}\n        >\n          <Primitive.div\n            role=\"option\"\n            aria-labelledby={textId}\n            data-highlighted={isFocused ? '' : undefined}\n            // `isFocused` caveat fixes stuttering in VoiceOver\n            aria-selected={isSelected && isFocused}\n            data-state={isSelected ? 'checked' : 'unchecked'}\n            aria-disabled={disabled || undefined}\n            data-disabled={disabled ? '' : undefined}\n            tabIndex={disabled ? undefined : -1}\n            {...itemProps}\n            ref={composedRefs}\n            onFocus={composeEventHandlers(itemProps.onFocus, () => setIsFocused(true))}\n            onBlur={composeEventHandlers(itemProps.onBlur, () => setIsFocused(false))}\n            onClick={composeEventHandlers(itemProps.onClick, () => {\n              // Open on click when using a touch or pen device\n              if (pointerTypeRef.current !== 'mouse') handleSelect();\n            })}\n            onPointerUp={composeEventHandlers(itemProps.onPointerUp, () => {\n              // Using a mouse you should be able to do pointer down, move through\n              // the list, and release the pointer over the item to select it.\n              if (pointerTypeRef.current === 'mouse') handleSelect();\n            })}\n            onPointerDown={composeEventHandlers(itemProps.onPointerDown, (event) => {\n              pointerTypeRef.current = event.pointerType;\n            })}\n            onPointerMove={composeEventHandlers(itemProps.onPointerMove, (event) => {\n              // Remember pointer type when sliding over to this item from another one\n              pointerTypeRef.current = event.pointerType;\n              if (disabled) {\n                contentContext.onItemLeave?.();\n              } else if (pointerTypeRef.current === 'mouse') {\n                // even though safari doesn't support this option, it's acceptable\n                // as it only means it might scroll a few pixels when using the pointer.\n                event.currentTarget.focus({ preventScroll: true });\n              }\n            })}\n            onPointerLeave={composeEventHandlers(itemProps.onPointerLeave, (event) => {\n              if (event.currentTarget === document.activeElement) {\n                contentContext.onItemLeave?.();\n              }\n            })}\n            onKeyDown={composeEventHandlers(itemProps.onKeyDown, (event) => {\n              const isTypingAhead = contentContext.searchRef?.current !== '';\n              if (isTypingAhead && event.key === ' ') return;\n              if (SELECTION_KEYS.includes(event.key)) handleSelect();\n              // prevent page scroll if using the space key to select an item\n              if (event.key === ' ') event.preventDefault();\n            })}\n          />\n        </Collection.ItemSlot>\n      </SelectItemContextProvider>\n    );\n  }\n);\n\nSelectItem.displayName = ITEM_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectItemText\n * -----------------------------------------------------------------------------------------------*/\n\nconst ITEM_TEXT_NAME = 'SelectItemText';\n\ntype SelectItemTextElement = React.ComponentRef<typeof Primitive.span>;\ninterface SelectItemTextProps extends PrimitiveSpanProps {}\n\nconst SelectItemText = React.forwardRef<SelectItemTextElement, SelectItemTextProps>(\n  (props: ScopedProps<SelectItemTextProps>, forwardedRef) => {\n    // We ignore `className` and `style` as this part shouldn't be styled.\n    const { __scopeSelect, className, style, ...itemTextProps } = props;\n    const context = useSelectContext(ITEM_TEXT_NAME, __scopeSelect);\n    const contentContext = useSelectContentContext(ITEM_TEXT_NAME, __scopeSelect);\n    const itemContext = useSelectItemContext(ITEM_TEXT_NAME, __scopeSelect);\n    const nativeOptionsContext = useSelectNativeOptionsContext(ITEM_TEXT_NAME, __scopeSelect);\n    const [itemTextNode, setItemTextNode] = React.useState<SelectItemTextElement | null>(null);\n    const composedRefs = useComposedRefs(\n      forwardedRef,\n      (node) => setItemTextNode(node),\n      itemContext.onItemTextChange,\n      (node) => contentContext.itemTextRefCallback?.(node, itemContext.value, itemContext.disabled)\n    );\n\n    const textContent = itemTextNode?.textContent;\n    const nativeOption = React.useMemo(\n      () => (\n        <option key={itemContext.value} value={itemContext.value} disabled={itemContext.disabled}>\n          {textContent}\n        </option>\n      ),\n      [itemContext.disabled, itemContext.value, textContent]\n    );\n\n    const { onNativeOptionAdd, onNativeOptionRemove } = nativeOptionsContext;\n    useLayoutEffect(() => {\n      onNativeOptionAdd(nativeOption);\n      return () => onNativeOptionRemove(nativeOption);\n    }, [onNativeOptionAdd, onNativeOptionRemove, nativeOption]);\n\n    return (\n      <>\n        <Primitive.span id={itemContext.textId} {...itemTextProps} ref={composedRefs} />\n\n        {/* Portal the select item text into the trigger value node */}\n        {itemContext.isSelected && context.valueNode && !context.valueNodeHasChildren\n          ? ReactDOM.createPortal(itemTextProps.children, context.valueNode)\n          : null}\n      </>\n    );\n  }\n);\n\nSelectItemText.displayName = ITEM_TEXT_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectItemIndicator\n * -----------------------------------------------------------------------------------------------*/\n\nconst ITEM_INDICATOR_NAME = 'SelectItemIndicator';\n\ntype SelectItemIndicatorElement = React.ComponentRef<typeof Primitive.span>;\ninterface SelectItemIndicatorProps extends PrimitiveSpanProps {}\n\nconst SelectItemIndicator = React.forwardRef<SelectItemIndicatorElement, SelectItemIndicatorProps>(\n  (props: ScopedProps<SelectItemIndicatorProps>, forwardedRef) => {\n    const { __scopeSelect, ...itemIndicatorProps } = props;\n    const itemContext = useSelectItemContext(ITEM_INDICATOR_NAME, __scopeSelect);\n    return itemContext.isSelected ? (\n      <Primitive.span aria-hidden {...itemIndicatorProps} ref={forwardedRef} />\n    ) : null;\n  }\n);\n\nSelectItemIndicator.displayName = ITEM_INDICATOR_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectScrollUpButton\n * -----------------------------------------------------------------------------------------------*/\n\nconst SCROLL_UP_BUTTON_NAME = 'SelectScrollUpButton';\n\ntype SelectScrollUpButtonElement = SelectScrollButtonImplElement;\ninterface SelectScrollUpButtonProps extends Omit<SelectScrollButtonImplProps, 'onAutoScroll'> {}\n\nconst SelectScrollUpButton = React.forwardRef<\n  SelectScrollUpButtonElement,\n  SelectScrollUpButtonProps\n>((props: ScopedProps<SelectScrollUpButtonProps>, forwardedRef) => {\n  const contentContext = useSelectContentContext(SCROLL_UP_BUTTON_NAME, props.__scopeSelect);\n  const viewportContext = useSelectViewportContext(SCROLL_UP_BUTTON_NAME, props.__scopeSelect);\n  const [canScrollUp, setCanScrollUp] = React.useState(false);\n  const composedRefs = useComposedRefs(forwardedRef, viewportContext.onScrollButtonChange);\n\n  useLayoutEffect(() => {\n    if (contentContext.viewport && contentContext.isPositioned) {\n      const viewport = contentContext.viewport;\n      function handleScroll() {\n        const canScrollUp = viewport.scrollTop > 0;\n        setCanScrollUp(canScrollUp);\n      }\n      handleScroll();\n      viewport.addEventListener('scroll', handleScroll);\n      return () => viewport.removeEventListener('scroll', handleScroll);\n    }\n  }, [contentContext.viewport, contentContext.isPositioned]);\n\n  return canScrollUp ? (\n    <SelectScrollButtonImpl\n      {...props}\n      ref={composedRefs}\n      onAutoScroll={() => {\n        const { viewport, selectedItem } = contentContext;\n        if (viewport && selectedItem) {\n          viewport.scrollTop = viewport.scrollTop - selectedItem.offsetHeight;\n        }\n      }}\n    />\n  ) : null;\n});\n\nSelectScrollUpButton.displayName = SCROLL_UP_BUTTON_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectScrollDownButton\n * -----------------------------------------------------------------------------------------------*/\n\nconst SCROLL_DOWN_BUTTON_NAME = 'SelectScrollDownButton';\n\ntype SelectScrollDownButtonElement = SelectScrollButtonImplElement;\ninterface SelectScrollDownButtonProps extends Omit<SelectScrollButtonImplProps, 'onAutoScroll'> {}\n\nconst SelectScrollDownButton = React.forwardRef<\n  SelectScrollDownButtonElement,\n  SelectScrollDownButtonProps\n>((props: ScopedProps<SelectScrollDownButtonProps>, forwardedRef) => {\n  const contentContext = useSelectContentContext(SCROLL_DOWN_BUTTON_NAME, props.__scopeSelect);\n  const viewportContext = useSelectViewportContext(SCROLL_DOWN_BUTTON_NAME, props.__scopeSelect);\n  const [canScrollDown, setCanScrollDown] = React.useState(false);\n  const composedRefs = useComposedRefs(forwardedRef, viewportContext.onScrollButtonChange);\n\n  useLayoutEffect(() => {\n    if (contentContext.viewport && contentContext.isPositioned) {\n      const viewport = contentContext.viewport;\n      function handleScroll() {\n        const maxScroll = viewport.scrollHeight - viewport.clientHeight;\n        // we use Math.ceil here because if the UI is zoomed-in\n        // `scrollTop` is not always reported as an integer\n        const canScrollDown = Math.ceil(viewport.scrollTop) < maxScroll;\n        setCanScrollDown(canScrollDown);\n      }\n      handleScroll();\n      viewport.addEventListener('scroll', handleScroll);\n      return () => viewport.removeEventListener('scroll', handleScroll);\n    }\n  }, [contentContext.viewport, contentContext.isPositioned]);\n\n  return canScrollDown ? (\n    <SelectScrollButtonImpl\n      {...props}\n      ref={composedRefs}\n      onAutoScroll={() => {\n        const { viewport, selectedItem } = contentContext;\n        if (viewport && selectedItem) {\n          viewport.scrollTop = viewport.scrollTop + selectedItem.offsetHeight;\n        }\n      }}\n    />\n  ) : null;\n});\n\nSelectScrollDownButton.displayName = SCROLL_DOWN_BUTTON_NAME;\n\ntype SelectScrollButtonImplElement = React.ComponentRef<typeof Primitive.div>;\ninterface SelectScrollButtonImplProps extends PrimitiveDivProps {\n  onAutoScroll(): void;\n}\n\nconst SelectScrollButtonImpl = React.forwardRef<\n  SelectScrollButtonImplElement,\n  SelectScrollButtonImplProps\n>((props: ScopedProps<SelectScrollButtonImplProps>, forwardedRef) => {\n  const { __scopeSelect, onAutoScroll, ...scrollIndicatorProps } = props;\n  const contentContext = useSelectContentContext('SelectScrollButton', __scopeSelect);\n  const autoScrollTimerRef = React.useRef<number | null>(null);\n  const getItems = useCollection(__scopeSelect);\n\n  const clearAutoScrollTimer = React.useCallback(() => {\n    if (autoScrollTimerRef.current !== null) {\n      window.clearInterval(autoScrollTimerRef.current);\n      autoScrollTimerRef.current = null;\n    }\n  }, []);\n\n  React.useEffect(() => {\n    return () => clearAutoScrollTimer();\n  }, [clearAutoScrollTimer]);\n\n  // When the viewport becomes scrollable on either side, the relevant scroll button will mount.\n  // Because it is part of the normal flow, it will push down (top button) or shrink (bottom button)\n  // the viewport, potentially causing the active item to now be partially out of view.\n  // We re-run the `scrollIntoView` logic to make sure it stays within the viewport.\n  useLayoutEffect(() => {\n    const activeItem = getItems().find((item) => item.ref.current === document.activeElement);\n    activeItem?.ref.current?.scrollIntoView({ block: 'nearest' });\n  }, [getItems]);\n\n  return (\n    <Primitive.div\n      aria-hidden\n      {...scrollIndicatorProps}\n      ref={forwardedRef}\n      style={{ flexShrink: 0, ...scrollIndicatorProps.style }}\n      onPointerDown={composeEventHandlers(scrollIndicatorProps.onPointerDown, () => {\n        if (autoScrollTimerRef.current === null) {\n          autoScrollTimerRef.current = window.setInterval(onAutoScroll, 50);\n        }\n      })}\n      onPointerMove={composeEventHandlers(scrollIndicatorProps.onPointerMove, () => {\n        contentContext.onItemLeave?.();\n        if (autoScrollTimerRef.current === null) {\n          autoScrollTimerRef.current = window.setInterval(onAutoScroll, 50);\n        }\n      })}\n      onPointerLeave={composeEventHandlers(scrollIndicatorProps.onPointerLeave, () => {\n        clearAutoScrollTimer();\n      })}\n    />\n  );\n});\n\n/* -------------------------------------------------------------------------------------------------\n * SelectSeparator\n * -----------------------------------------------------------------------------------------------*/\n\nconst SEPARATOR_NAME = 'SelectSeparator';\n\ntype SelectSeparatorElement = React.ComponentRef<typeof Primitive.div>;\ninterface SelectSeparatorProps extends PrimitiveDivProps {}\n\nconst SelectSeparator = React.forwardRef<SelectSeparatorElement, SelectSeparatorProps>(\n  (props: ScopedProps<SelectSeparatorProps>, forwardedRef) => {\n    const { __scopeSelect, ...separatorProps } = props;\n    return <Primitive.div aria-hidden {...separatorProps} ref={forwardedRef} />;\n  }\n);\n\nSelectSeparator.displayName = SEPARATOR_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectArrow\n * -----------------------------------------------------------------------------------------------*/\n\nconst ARROW_NAME = 'SelectArrow';\n\ntype SelectArrowElement = React.ComponentRef<typeof PopperPrimitive.Arrow>;\ntype PopperArrowProps = React.ComponentPropsWithoutRef<typeof PopperPrimitive.Arrow>;\ninterface SelectArrowProps extends PopperArrowProps {}\n\nconst SelectArrow = React.forwardRef<SelectArrowElement, SelectArrowProps>(\n  (props: ScopedProps<SelectArrowProps>, forwardedRef) => {\n    const { __scopeSelect, ...arrowProps } = props;\n    const popperScope = usePopperScope(__scopeSelect);\n    const context = useSelectContext(ARROW_NAME, __scopeSelect);\n    const contentContext = useSelectContentContext(ARROW_NAME, __scopeSelect);\n    return context.open && contentContext.position === 'popper' ? (\n      <PopperPrimitive.Arrow {...popperScope} {...arrowProps} ref={forwardedRef} />\n    ) : null;\n  }\n);\n\nSelectArrow.displayName = ARROW_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectBubbleInput\n * -----------------------------------------------------------------------------------------------*/\n\nconst BUBBLE_INPUT_NAME = 'SelectBubbleInput';\n\ntype InputProps = React.ComponentPropsWithoutRef<typeof Primitive.select>;\ninterface SwitchBubbleInputProps extends InputProps {}\n\nconst SelectBubbleInput = React.forwardRef<HTMLSelectElement, SwitchBubbleInputProps>(\n  ({ __scopeSelect, value, ...props }: ScopedProps<SwitchBubbleInputProps>, forwardedRef) => {\n    const ref = React.useRef<HTMLSelectElement>(null);\n    const composedRefs = useComposedRefs(forwardedRef, ref);\n    const prevValue = usePrevious(value);\n\n    // Bubble value change to parents (e.g form change event)\n    React.useEffect(() => {\n      const select = ref.current;\n      if (!select) return;\n\n      const selectProto = window.HTMLSelectElement.prototype;\n      const descriptor = Object.getOwnPropertyDescriptor(\n        selectProto,\n        'value'\n      ) as PropertyDescriptor;\n      const setValue = descriptor.set;\n      if (prevValue !== value && setValue) {\n        const event = new Event('change', { bubbles: true });\n        setValue.call(select, value);\n        select.dispatchEvent(event);\n      }\n    }, [prevValue, value]);\n\n    /**\n     * We purposefully use a `select` here to support form autofill as much as\n     * possible.\n     *\n     * We purposefully do not add the `value` attribute here to allow the value\n     * to be set programmatically and bubble to any parent form `onChange`\n     * event. Adding the `value` will cause React to consider the programmatic\n     * dispatch a duplicate and it will get swallowed.\n     *\n     * We use visually hidden styles rather than `display: \"none\"` because\n     * Safari autofill won't work otherwise.\n     */\n    return (\n      <Primitive.select\n        {...props}\n        style={{ ...VISUALLY_HIDDEN_STYLES, ...props.style }}\n        ref={composedRefs}\n        defaultValue={value}\n      />\n    );\n  }\n);\n\nSelectBubbleInput.displayName = BUBBLE_INPUT_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\nfunction shouldShowPlaceholder(value?: string) {\n  return value === '' || value === undefined;\n}\n\nfunction useTypeaheadSearch(onSearchChange: (search: string) => void) {\n  const handleSearchChange = useCallbackRef(onSearchChange);\n  const searchRef = React.useRef('');\n  const timerRef = React.useRef(0);\n\n  const handleTypeaheadSearch = React.useCallback(\n    (key: string) => {\n      const search = searchRef.current + key;\n      handleSearchChange(search);\n\n      (function updateSearch(value: string) {\n        searchRef.current = value;\n        window.clearTimeout(timerRef.current);\n        // Reset `searchRef` 1 second after it was last updated\n        if (value !== '') timerRef.current = window.setTimeout(() => updateSearch(''), 1000);\n      })(search);\n    },\n    [handleSearchChange]\n  );\n\n  const resetTypeahead = React.useCallback(() => {\n    searchRef.current = '';\n    window.clearTimeout(timerRef.current);\n  }, []);\n\n  React.useEffect(() => {\n    return () => window.clearTimeout(timerRef.current);\n  }, []);\n\n  return [searchRef, handleTypeaheadSearch, resetTypeahead] as const;\n}\n\n/**\n * This is the \"meat\" of the typeahead matching logic. It takes in a list of items,\n * the search and the current item, and returns the next item (or `undefined`).\n *\n * We normalize the search because if a user has repeatedly pressed a character,\n * we want the exact same behavior as if we only had that one character\n * (ie. cycle through items starting with that character)\n *\n * We also reorder the items by wrapping the array around the current item.\n * This is so we always look forward from the current item, and picking the first\n * item will always be the correct one.\n *\n * Finally, if the normalized search is exactly one character, we exclude the\n * current item from the values because otherwise it would be the first to match always\n * and focus would never move. This is as opposed to the regular case, where we\n * don't want focus to move if the current item still matches.\n */\nfunction findNextItem<T extends { textValue: string }>(\n  items: T[],\n  search: string,\n  currentItem?: T\n) {\n  const isRepeated = search.length > 1 && Array.from(search).every((char) => char === search[0]);\n  const normalizedSearch = isRepeated ? search[0]! : search;\n  const currentItemIndex = currentItem ? items.indexOf(currentItem) : -1;\n  let wrappedItems = wrapArray(items, Math.max(currentItemIndex, 0));\n  const excludeCurrentItem = normalizedSearch.length === 1;\n  if (excludeCurrentItem) wrappedItems = wrappedItems.filter((v) => v !== currentItem);\n  const nextItem = wrappedItems.find((item) =>\n    item.textValue.toLowerCase().startsWith(normalizedSearch.toLowerCase())\n  );\n  return nextItem !== currentItem ? nextItem : undefined;\n}\n\n/**\n * Wraps an array around itself at a given start index\n * Example: `wrapArray(['a', 'b', 'c', 'd'], 2) === ['c', 'd', 'a', 'b']`\n */\nfunction wrapArray<T>(array: T[], startIndex: number) {\n  return array.map<T>((_, index) => array[(startIndex + index) % array.length]!);\n}\n\nconst Root = Select;\nconst Trigger = SelectTrigger;\nconst Value = SelectValue;\nconst Icon = SelectIcon;\nconst Portal = SelectPortal;\nconst Content = SelectContent;\nconst Viewport = SelectViewport;\nconst Group = SelectGroup;\nconst Label = SelectLabel;\nconst Item = SelectItem;\nconst ItemText = SelectItemText;\nconst ItemIndicator = SelectItemIndicator;\nconst ScrollUpButton = SelectScrollUpButton;\nconst ScrollDownButton = SelectScrollDownButton;\nconst Separator = SelectSeparator;\nconst Arrow = SelectArrow;\n\nexport {\n  createSelectScope,\n  //\n  Select,\n  SelectTrigger,\n  SelectValue,\n  SelectIcon,\n  SelectPortal,\n  SelectContent,\n  SelectViewport,\n  SelectGroup,\n  SelectLabel,\n  SelectItem,\n  SelectItemText,\n  SelectItemIndicator,\n  SelectScrollUpButton,\n  SelectScrollDownButton,\n  SelectSeparator,\n  SelectArrow,\n  //\n  Root,\n  Trigger,\n  Value,\n  Icon,\n  Portal,\n  Content,\n  Viewport,\n  Group,\n  Label,\n  Item,\n  ItemText,\n  ItemIndicator,\n  ScrollUpButton,\n  ScrollDownButton,\n  Separator,\n  Arrow,\n};\nexport type {\n  SelectProps,\n  SelectTriggerProps,\n  SelectValueProps,\n  SelectIconProps,\n  SelectPortalProps,\n  SelectContentProps,\n  SelectViewportProps,\n  SelectGroupProps,\n  SelectLabelProps,\n  SelectItemProps,\n  SelectItemTextProps,\n  SelectItemIndicatorProps,\n  SelectScrollUpButtonProps,\n  SelectScrollDownButtonProps,\n  SelectSeparatorProps,\n  SelectArrowProps,\n};\n", "// packages/react/use-previous/src/usePrevious.tsx\nimport * as React from \"react\";\nfunction usePrevious(value) {\n  const ref = React.useRef({ value, previous: value });\n  return React.useMemo(() => {\n    if (ref.current.value !== value) {\n      ref.current.previous = ref.current.value;\n      ref.current.value = value;\n    }\n    return ref.current.previous;\n  }, [value]);\n}\nexport {\n  usePrevious\n};\n//# sourceMappingURL=index.mjs.map\n", "const createStoreImpl = (createState) => {\n  let state;\n  const listeners = /* @__PURE__ */ new Set();\n  const setState = (partial, replace) => {\n    const nextState = typeof partial === \"function\" ? partial(state) : partial;\n    if (!Object.is(nextState, state)) {\n      const previousState = state;\n      state = (replace != null ? replace : typeof nextState !== \"object\" || nextState === null) ? nextState : Object.assign({}, state, nextState);\n      listeners.forEach((listener) => listener(state, previousState));\n    }\n  };\n  const getState = () => state;\n  const getInitialState = () => initialState;\n  const subscribe = (listener) => {\n    listeners.add(listener);\n    return () => listeners.delete(listener);\n  };\n  const api = { setState, getState, getInitialState, subscribe };\n  const initialState = state = createState(setState, getState, api);\n  return api;\n};\nconst createStore = (createState) => createState ? createStoreImpl(createState) : createStoreImpl;\n\nexport { createStore };\n", "import React from 'react';\nimport { createStore } from 'zustand/vanilla';\n\nconst identity = (arg) => arg;\nfunction useStore(api, selector = identity) {\n  const slice = React.useSyncExternalStore(\n    api.subscribe,\n    () => selector(api.getState()),\n    () => selector(api.getInitialState())\n  );\n  React.useDebugValue(slice);\n  return slice;\n}\nconst createImpl = (createState) => {\n  const api = createStore(createState);\n  const useBoundStore = (selector) => useStore(api, selector);\n  Object.assign(useBoundStore, api);\n  return useBoundStore;\n};\nconst create = (createState) => createState ? createImpl(createState) : createImpl;\n\nexport { create, useStore };\n"], "names": ["node", "handleAndDispatchPointerDownOutsideEvent", "handleFocusIn", "handleFocusOut", "handleMutations", "container", "PopperArrow", "handleScroll", "canScrollUp", "canScrollDown", "Root", "Content"], "sourceRoot": "", "ignoreList": [0, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 47, 48, 49, 50, 53, 54, 55, 56, 57, 61, 62, 63, 64, 65, 66, 67, 68, 70, 71, 72, 74, 75, 76]}