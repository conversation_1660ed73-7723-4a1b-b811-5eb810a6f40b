(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[254],{8320:(e,s,t)=>{Promise.resolve().then(t.bind(t,3515)),Promise.resolve().then(t.bind(t,8869)),Promise.resolve().then(t.bind(t,1548)),Promise.resolve().then(t.bind(t,1665)),Promise.resolve().then(t.bind(t,5368))},8869:(e,s,t)=>{"use strict";t.d(s,{ThreeColumnLayout:()=>K});var a=t(5155),l=t(2115),r=t(4822),i=t(9393),n=t(9367),c=t(705),d=t(3473),o=t(1773),m=t(3920),x=t(7762),u=t(490),h=t(5760),p=t(3565),j=t(2558);let f=l.forwardRef((e,s)=>{let{className:t,...l}=e;return(0,a.jsx)(h.bL,{className:(0,j.cn)("grid gap-2",t),...l,ref:s})});f.displayName=h.bL.displayName;let v=l.forwardRef((e,s)=>{let{className:t,...l}=e;return(0,a.jsx)(h.q7,{ref:s,className:(0,j.cn)("aspect-square h-4 w-4 rounded-full border border-primary text-primary ring-offset-background focus:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",t),...l,children:(0,a.jsx)(h.C1,{className:"flex items-center justify-center",children:(0,a.jsx)(p.A,{className:"h-2.5 w-2.5 fill-current text-current"})})})});v.displayName=h.q7.displayName;var g=t(477),N=t(8941);let y=[{label:"1小时",value:36e5},{label:"24小时",value:864e5},{label:"3天",value:2592e5},{label:"永久",value:0}];var b=t(5023),w=t(3244);function k(e){var s,t,r;let{onEmailCreated:h}=e,{config:p}=(0,w.U)(),[j,k]=(0,l.useState)(!1),[C,S]=(0,l.useState)(!1),[A,E]=(0,l.useState)(""),[I,P]=(0,l.useState)(""),[T,D]=(0,l.useState)(y[1].value.toString()),{toast:L}=(0,x.dj)(),{copyToClipboard:z}=(0,b.T)(),O=async()=>{if(!A.trim()){L({title:"错误",description:"请输入邮箱名",variant:"destructive"});return}S(!0);try{let e=await fetch("/api/emails/generate",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({name:A,domain:I,expiryTime:parseInt(T)})});if(!e.ok){let s=await e.json();L({title:"错误",description:s.error,variant:"destructive"});return}L({title:"成功",description:"已创建新的临时邮箱"}),h(),k(!1),E("")}catch(e){L({title:"错误",description:"创建邮箱失败",variant:"destructive"})}finally{S(!1)}};return(0,l.useEffect)(()=>{var e,s,t;(null!==(s=null==p?void 0:null===(e=p.emailDomainsArray)||void 0===e?void 0:e.length)&&void 0!==s?s:0)>0&&P(null!==(t=null==p?void 0:p.emailDomainsArray[0])&&void 0!==t?t:"")},[p]),(0,a.jsxs)(c.lG,{open:j,onOpenChange:k,children:[(0,a.jsx)(c.zM,{asChild:!0,children:(0,a.jsxs)(i.$,{className:"gap-2",children:[(0,a.jsx)(d.A,{className:"w-4 h-4"}),"创建新邮箱"]})}),(0,a.jsxs)(c.Cf,{children:[(0,a.jsx)(c.c7,{children:(0,a.jsx)(c.L3,{children:"创建新的临时邮箱"})}),(0,a.jsxs)("div",{className:"space-y-4 py-4",children:[(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsx)(n.p,{value:A,onChange:e=>E(e.target.value),placeholder:"输入邮箱名",className:"flex-1"}),(null!==(r=null==p?void 0:null===(s=p.emailDomainsArray)||void 0===s?void 0:s.length)&&void 0!==r?r:0)>1&&(0,a.jsxs)(N.l6,{value:I,onValueChange:P,children:[(0,a.jsx)(N.bq,{className:"w-[180px]",children:(0,a.jsx)(N.yv,{})}),(0,a.jsx)(N.gC,{children:null==p?void 0:null===(t=p.emailDomainsArray)||void 0===t?void 0:t.map(e=>(0,a.jsxs)(N.eb,{value:e,children:["@",e]},e))})]}),(0,a.jsx)(i.$,{variant:"outline",size:"icon",onClick:()=>E((0,u.Ak)(8)),type:"button",children:(0,a.jsx)(o.A,{className:"w-4 h-4"})})]}),(0,a.jsxs)("div",{className:"flex items-center gap-4",children:[(0,a.jsx)(g.J,{className:"shrink-0 text-muted-foreground",children:"过期时间"}),(0,a.jsx)(f,{value:T,onValueChange:D,className:"flex gap-6",children:y.map(e=>(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(v,{value:e.value.toString(),id:e.value.toString()}),(0,a.jsx)(g.J,{htmlFor:e.value.toString(),className:"cursor-pointer text-sm",children:e.label})]},e.value))})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2 text-sm text-muted-foreground",children:[(0,a.jsx)("span",{className:"shrink-0",children:"完整邮箱地址将为:"}),A?(0,a.jsxs)("div",{className:"flex items-center gap-2 min-w-0",children:[(0,a.jsx)("span",{className:"truncate",children:"".concat(A,"@").concat(I)}),(0,a.jsx)("div",{className:"shrink-0 cursor-pointer hover:text-primary transition-colors",onClick:()=>{z("".concat(A,"@").concat(I))},children:(0,a.jsx)(m.A,{className:"size-4"})})]}):(0,a.jsx)("span",{className:"text-gray-400",children:"..."})]})]}),(0,a.jsxs)("div",{className:"flex justify-end gap-2",children:[(0,a.jsx)(i.$,{variant:"outline",onClick:()=>k(!1),disabled:C,children:"取消"}),(0,a.jsx)(i.$,{onClick:O,disabled:C,children:C?"创建中...":"创建"})]})]})]})}var C=t(4113),S=t(4505);function A(e){let{onEmailBound:s}=e,[t,r]=(0,l.useState)(!1),[d,o]=(0,l.useState)(!1),[m,u]=(0,l.useState)({jwt:""}),{toast:h}=(0,x.dj)(),p=async e=>{if(e.preventDefault(),!m.jwt.trim()){h({title:"请输入邮箱凭证",description:"邮箱凭证不能为空",variant:"destructive"});return}o(!0);try{let e=await fetch("/api/email-credentials",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(m)});if(!e.ok){let s=await e.json();throw Error(s.error||"绑定邮箱失败")}h({title:"绑定成功",description:"邮箱绑定成功"}),u({jwt:""}),r(!1),s()}catch(e){h({title:"绑定失败",description:e instanceof Error?e.message:"请稍后重试",variant:"destructive"})}finally{o(!1)}};return(0,a.jsxs)(c.lG,{open:t,onOpenChange:r,children:[(0,a.jsx)(c.zM,{asChild:!0,children:(0,a.jsxs)(i.$,{variant:"outline",size:"sm",className:"gap-1",children:[(0,a.jsx)(C.A,{className:"h-3 w-3"}),"绑定邮箱"]})}),(0,a.jsxs)(c.Cf,{className:"max-w-md",children:[(0,a.jsxs)(c.c7,{children:[(0,a.jsx)(c.L3,{children:"绑定邮箱"}),(0,a.jsx)(c.rr,{children:"输入邮箱凭证来绑定邮箱到您的账户"})]}),(0,a.jsxs)("form",{onSubmit:p,className:"space-y-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(g.J,{htmlFor:"jwt",children:"邮箱凭证 (JWT)"}),(0,a.jsx)(n.p,{id:"jwt",value:m.jwt,onChange:e=>u(s=>({...s,jwt:e.target.value})),placeholder:"请输入邮箱凭证...",required:!0})]}),(0,a.jsxs)("div",{className:"rounded-lg bg-blue-50 p-3 text-sm text-blue-800",children:[(0,a.jsx)("p",{className:"font-medium mb-1",children:"提示："}),(0,a.jsxs)("ul",{className:"space-y-1 text-xs",children:[(0,a.jsx)("li",{children:"• 邮箱凭证由管理员提供"}),(0,a.jsx)("li",{children:"• 每个邮箱对应唯一的凭证"}),(0,a.jsx)("li",{children:"• 绑定后可以接收该邮箱的邮件"}),(0,a.jsx)("li",{children:"• 支持同时绑定多个邮箱"})]})]}),(0,a.jsxs)(c.Es,{children:[(0,a.jsx)(i.$,{type:"button",variant:"outline",onClick:()=>{r(!1),u({jwt:""})},disabled:d,children:"取消"}),(0,a.jsx)(i.$,{type:"submit",disabled:d,children:d?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(S.A,{className:"w-4 h-4 animate-spin mr-2"}),"绑定中..."]}):"绑定邮箱"})]})]})]})]})}var E=t(6744),I=t(8867);function P(e){let{emailAddress:s,emailType:t,credentialId:r}=e,[d,o]=(0,l.useState)(!1),[u,h]=(0,l.useState)(!1),[p,j]=(0,l.useState)(""),[f,v]=(0,l.useState)(!1),{toast:N}=(0,x.dj)(),y=async()=>{h(!0);try{let e="";if("bound"===t&&r){let s=await fetch("/api/email-credentials/".concat(r));if(!s.ok){let e=await s.json();throw Error(e.error||"获取凭证失败")}e=(await s.json()).jwt}else{let t=await fetch("/api/email-credentials/create",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({emailAddress:s})});if(!t.ok){let e=await t.json();throw Error(e.error||"创建凭证失败")}e=(await t.json()).jwt}j(e),N({title:"成功",description:"邮箱凭证提取成功"})}catch(e){console.error("Failed to extract credential:",e),N({title:"错误",description:e instanceof Error?e.message:"提取凭证失败",variant:"destructive"})}finally{h(!1)}},b=async()=>{try{await navigator.clipboard.writeText(p),v(!0),N({title:"已复制",description:"邮箱凭证已复制到剪贴板"}),setTimeout(()=>v(!1),2e3)}catch(e){N({title:"复制失败",description:"请手动复制凭证",variant:"destructive"})}},w=()=>{o(!1),j(""),v(!1)};return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(i.$,{variant:"ghost",size:"icon",className:"h-8 w-8",onClick:e=>{e.stopPropagation(),console.log("Extract credential button clicked for:",s),console.log("Setting open to true, current open state:",d),o(!0)},title:"提取邮箱凭证",children:(0,a.jsx)(E.A,{className:"h-4 w-4 text-blue-600"})}),(0,a.jsx)(c.lG,{open:d,onOpenChange:e=>{console.log("Dialog onOpenChange called with:",e),e||w()},children:(0,a.jsxs)(c.Cf,{className:"sm:max-w-md",children:[(0,a.jsxs)(c.c7,{children:[(0,a.jsx)(c.L3,{children:"提取邮箱凭证"}),(0,a.jsxs)(c.rr,{children:["为邮箱 ",(0,a.jsx)("span",{className:"font-mono text-sm bg-gray-100 px-1 rounded",children:s})," 提取API访问凭证"]})]}),(0,a.jsx)("div",{className:"space-y-4",children:p?(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(g.J,{htmlFor:"credential",children:"邮箱凭证 (JWT Token)"}),(0,a.jsxs)("div",{className:"flex gap-2 mt-1",children:[(0,a.jsx)(n.p,{id:"credential",value:p,readOnly:!0,className:"font-mono text-xs"}),(0,a.jsx)(i.$,{variant:"outline",size:"icon",onClick:b,className:"flex-shrink-0",children:f?(0,a.jsx)(I.A,{className:"h-4 w-4 text-green-600"}):(0,a.jsx)(m.A,{className:"h-4 w-4"})})]})]}),(0,a.jsxs)("div",{className:"text-xs text-gray-500 space-y-1",children:[(0,a.jsx)("p",{children:(0,a.jsx)("strong",{children:"使用方法："})}),(0,a.jsx)("p",{children:"在API请求头中添加："}),(0,a.jsxs)("code",{className:"block bg-gray-100 p-2 rounded text-xs",children:["X-Email-Credential: ",p.substring(0,20),"..."]})]})]}):(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(i.$,{onClick:y,disabled:u,className:"w-full",children:u?"提取中...":"提取凭证"}),(0,a.jsx)("p",{className:"text-xs text-gray-500 mt-2",children:"此凭证可用于API访问该邮箱的邮件"})]})})]})})]})}var T=t(6462),D=t(5686);function L(e,s){let t=(0,l.useRef)(Date.now());return(0,l.useCallback)(function(){for(var a=arguments.length,l=Array(a),r=0;r<a;r++)l[r]=arguments[r];let i=Date.now();i-t.current>=s&&(e(...l),t.current=i)},[e,s])}var z=t(8200),O=t(571);let R=O.bL;O.l9;let F=O.ZL,$=l.forwardRef((e,s)=>{let{className:t,...l}=e;return(0,a.jsx)(O.hJ,{className:(0,j.cn)("fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",t),...l,ref:s})});$.displayName=O.hJ.displayName;let _=l.forwardRef((e,s)=>{let{className:t,...l}=e;return(0,a.jsxs)(F,{children:[(0,a.jsx)($,{}),(0,a.jsx)(O.UC,{ref:s,className:(0,j.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",t),...l})]})});_.displayName=O.UC.displayName;let J=e=>{let{className:s,...t}=e;return(0,a.jsx)("div",{className:(0,j.cn)("flex flex-col space-y-2 text-center sm:text-left",s),...t})};J.displayName="AlertDialogHeader";let M=e=>{let{className:s,...t}=e;return(0,a.jsx)("div",{className:(0,j.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",s),...t})};M.displayName="AlertDialogFooter";let V=l.forwardRef((e,s)=>{let{className:t,...l}=e;return(0,a.jsx)(O.hE,{ref:s,className:(0,j.cn)("text-lg font-semibold",t),...l})});V.displayName=O.hE.displayName;let q=l.forwardRef((e,s)=>{let{className:t,...l}=e;return(0,a.jsx)(O.VY,{ref:s,className:(0,j.cn)("text-sm text-muted-foreground",t),...l})});q.displayName=O.VY.displayName;let U=l.forwardRef((e,s)=>{let{className:t,...l}=e;return(0,a.jsx)(O.rc,{ref:s,className:(0,j.cn)((0,i.r)(),t),...l})});U.displayName=O.rc.displayName;let Y=l.forwardRef((e,s)=>{let{className:t,...l}=e;return(0,a.jsx)(O.ZD,{ref:s,className:(0,j.cn)((0,i.r)({variant:"outline"}),"mt-2 sm:mt-0",t),...l})});Y.displayName=O.ZD.displayName;var G=t(2619);function H(e){let{onEmailSelect:s,selectedEmailId:t}=e,{data:n}=(0,r.wV)(),{config:c}=(0,w.U)(),{role:d}=function(){let{data:e}=(0,r.wV)(),[s,t]=(0,l.useState)(null);return(0,l.useEffect)(()=>{var s,a,l;(null==e?void 0:null===(l=e.user)||void 0===l?void 0:null===(a=l.roles)||void 0===a?void 0:null===(s=a[0])||void 0===s?void 0:s.name)&&t(e.user.roles[0].name)},[e]),{role:s,loading:!e}}(),[m,u]=(0,l.useState)([]),[h,p]=(0,l.useState)(!0),[f,v]=(0,l.useState)(!1),[g,N]=(0,l.useState)(null),[y,b]=(0,l.useState)(!1),[C,S]=(0,l.useState)(0),[E,I]=(0,l.useState)(null),{toast:O}=(0,x.dj)(),F=async e=>{try{let s=new URL("/api/emails",window.location.origin);e&&s.searchParams.set("cursor",e);let t=await fetch(s),a=await t.json();if("error"in a){console.error("API error:",a.error),O({title:"错误",description:a.error||"获取邮箱列表失败",variant:"destructive"});return}if(!a.emails||!Array.isArray(a.emails)){console.error("Invalid API response:",a),O({title:"错误",description:"服务器响应格式错误",variant:"destructive"});return}if(!e){let e=a.emails,s=e.findIndex(e=>m.some(s=>s.id===e.id));if(-1===s){u(e),N(a.nextCursor),S(a.total);return}let t=e.slice(0,s);u([...t,...m]),S(a.total);return}u(e=>[...e,...a.emails]),N(a.nextCursor),S(a.total)}catch(e){console.error("Failed to fetch emails:",e),O({title:"错误",description:"网络请求失败，请检查网络连接",variant:"destructive"})}finally{p(!1),v(!1),b(!1)}},$=async()=>{v(!0),await F()},H=L(e=>{if(y)return;let{scrollHeight:s,scrollTop:t,clientHeight:a}=e.currentTarget;s-t<=1.5*a&&g&&(b(!0),F(g))},200);(0,l.useEffect)(()=>{n&&F()},[n]);let W=async e=>{try{let a=await fetch("/api/emails/".concat(e.id),{method:"DELETE"});if(!a.ok){let e=await a.json();O({title:"错误",description:e.error,variant:"destructive"});return}u(s=>s.filter(s=>s.id!==e.id)),S(e=>e-1),O({title:"成功",description:"邮箱已删除"}),t===e.id&&s(null)}catch(e){O({title:"错误",description:"删除邮箱失败",variant:"destructive"})}finally{I(null)}};return n?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("div",{className:"flex flex-col h-full",children:[(0,a.jsxs)("div",{className:"p-2 flex justify-between items-center border-b border-primary/20",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(i.$,{variant:"ghost",size:"icon",onClick:$,disabled:f,className:(0,j.cn)("h-8 w-8",f&&"animate-spin"),children:(0,a.jsx)(o.A,{className:"h-4 w-4"})}),(0,a.jsx)("span",{className:"text-xs text-gray-500",children:d===G.gg.EMPEROR?"".concat(C,"/∞ 个邮箱"):"".concat(C,"/").concat((null==c?void 0:c.maxEmails)||z.q.MAX_ACTIVE_EMAILS," 个邮箱")})]}),(0,a.jsxs)("div",{className:"flex gap-1",children:[(0,a.jsx)(k,{onEmailCreated:$}),(0,a.jsx)(A,{onEmailBound:$})]})]}),(0,a.jsx)("div",{className:"flex-1 overflow-auto p-2",onScroll:H,children:h?(0,a.jsx)("div",{className:"text-center text-sm text-gray-500",children:"加载中..."}):m.length>0?(0,a.jsxs)("div",{className:"space-y-1",children:[m.map(e=>(0,a.jsxs)("div",{className:(0,j.cn)("flex items-center gap-2 p-2 rounded cursor-pointer text-sm group","hover:bg-primary/5",t===e.id&&"bg-primary/10"),onClick:()=>s(e),children:[(0,a.jsx)(T.A,{className:(0,j.cn)("h-4 w-4","bound"===e.type?"text-green-600":"text-primary/60")}),(0,a.jsxs)("div",{className:"truncate flex-1",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("span",{className:"font-medium truncate",children:e.address}),"bound"===e.type&&(0,a.jsx)("span",{className:"text-xs bg-green-100 text-green-800 px-1.5 py-0.5 rounded-full",children:"绑定"})]}),(0,a.jsx)("div",{className:"text-xs text-gray-500",children:"bound"===e.type?"绑定邮箱 - 自动同步邮件":9999===new Date(e.expiresAt).getFullYear()?"永久有效":"过期时间: ".concat(new Date(e.expiresAt).toLocaleString())})]}),(0,a.jsxs)("div",{className:"flex gap-1",children:[(0,a.jsx)(P,{emailAddress:e.address,emailType:e.type,credentialId:"bound"===e.type?e.credentialId:void 0}),(0,a.jsx)(i.$,{variant:"ghost",size:"icon",className:"opacity-0 group-hover:opacity-100 h-8 w-8",onClick:s=>{s.stopPropagation(),I(e)},children:(0,a.jsx)(D.A,{className:"h-4 w-4 text-destructive"})})]})]},e.id)),y&&(0,a.jsx)("div",{className:"text-center text-sm text-gray-500 py-2",children:"加载更多..."})]}):(0,a.jsx)("div",{className:"text-center text-sm text-gray-500",children:"还没有邮箱，创建一个吧！"})})]}),(0,a.jsx)(R,{open:!!E,onOpenChange:()=>I(null),children:(0,a.jsxs)(_,{children:[(0,a.jsxs)(J,{children:[(0,a.jsx)(V,{children:"确认删除"}),(0,a.jsxs)(q,{children:["确定要删除邮箱 ",null==E?void 0:E.address," 吗？此操作将同时删除该邮箱中的所有邮件，且不可恢复。"]})]}),(0,a.jsxs)(M,{children:[(0,a.jsx)(Y,{children:"取消"}),(0,a.jsx)(U,{className:"bg-destructive hover:bg-destructive/90",onClick:()=>E&&W(E),children:"删除"})]})]})})]}):null}var W=t(2423);function Z(e){let{email:s,onMessageSelect:t,selectedMessageId:r}=e,[n,c]=(0,l.useState)([]),[d,m]=(0,l.useState)(!0),[u,h]=(0,l.useState)(!1),[p,f]=(0,l.useState)(null),[v,g]=(0,l.useState)(!1),N=(0,l.useRef)(),y=(0,l.useRef)([]),[b,w]=(0,l.useState)(0),[k,C]=(0,l.useState)(null),{toast:S}=(0,x.dj)();(0,l.useEffect)(()=>{y.current=n},[n]);let A=async e=>{try{let t=new URL("/api/emails/".concat(s.id),window.location.origin);e&&t.searchParams.set("cursor",e);let a=await fetch(t),l=await a.json();if("error"in l){console.error("API error:",l.error),S({title:"错误",description:l.error||"获取邮件列表失败",variant:"destructive"});return}if(!l.messages||!Array.isArray(l.messages)){console.error("Invalid API response:",l),S({title:"错误",description:"服务器响应格式错误",variant:"destructive"});return}if(!e){let e=l.messages,s=y.current,t=e.findIndex(e=>s.some(s=>s.id===e.id));if(-1===t){c(e),f(l.nextCursor),w(l.total);return}let a=e.slice(0,t);c([...a,...s]),w(l.total);return}c(e=>[...e,...l.messages]),f(l.nextCursor),w(l.total)}catch(e){console.error("Failed to fetch messages:",e),S({title:"错误",description:"网络请求失败，请检查网络连接",variant:"destructive"})}finally{m(!1),h(!1),g(!1)}},E=()=>{I(),N.current=setInterval(()=>{u||v||A()},z.q.POLL_INTERVAL)},I=()=>{N.current&&(clearInterval(N.current),N.current=void 0)},P=async()=>{h(!0),await A()},O=L(e=>{if(v)return;let{scrollHeight:s,scrollTop:t,clientHeight:a}=e.currentTarget;s-t<=1.5*a&&p&&(g(!0),A(p))},200),F=async e=>{try{let a=await fetch("/api/emails/".concat(s.id,"/").concat(e.id),{method:"DELETE"});if(!a.ok){let e=await a.json();S({title:"错误",description:e.error,variant:"destructive"});return}c(s=>s.filter(s=>s.id!==e.id)),w(e=>e-1),S({title:"成功",description:"邮件已删除"}),r===e.id&&t(null)}catch(e){S({title:"错误",description:"删除邮件失败",variant:"destructive"})}finally{C(null)}};return(0,l.useEffect)(()=>{if(s.id)return m(!0),f(null),A(),E(),()=>{I()}},[s.id]),(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("div",{className:"h-full flex flex-col",children:[(0,a.jsxs)("div",{className:"p-2 flex justify-between items-center border-b border-primary/20",children:[(0,a.jsx)(i.$,{variant:"ghost",size:"icon",onClick:P,disabled:u,className:(0,j.cn)("h-8 w-8",u&&"animate-spin"),children:(0,a.jsx)(o.A,{className:"h-4 w-4"})}),(0,a.jsx)("span",{className:"text-xs text-gray-500",children:b>0?"".concat(b," 封邮件"):"暂无邮件"})]}),(0,a.jsx)("div",{className:"flex-1 overflow-auto",onScroll:O,children:d?(0,a.jsx)("div",{className:"p-4 text-center text-sm text-gray-500",children:"加载中..."}):n.length>0?(0,a.jsxs)("div",{className:"divide-y divide-primary/10",children:[n.map(e=>(0,a.jsx)("div",{onClick:()=>t(e.id),className:(0,j.cn)("p-3 hover:bg-primary/5 cursor-pointer group",r===e.id&&"bg-primary/10"),children:(0,a.jsxs)("div",{className:"flex items-start gap-3",children:[(0,a.jsx)(T.A,{className:"w-4 h-4 text-primary/60 mt-1"}),(0,a.jsxs)("div",{className:"min-w-0 flex-1",children:[(0,a.jsx)("p",{className:"font-medium text-sm truncate",children:e.subject}),(0,a.jsxs)("div",{className:"mt-1 flex items-center gap-2 text-xs text-gray-500",children:[(0,a.jsx)("span",{className:"truncate",children:e.from_address}),(0,a.jsxs)("span",{className:"flex items-center gap-1",children:[(0,a.jsx)(W.A,{className:"w-3 h-3"}),new Date(e.received_at).toLocaleString()]})]})]}),(0,a.jsx)(i.$,{variant:"ghost",size:"icon",className:"opacity-0 group-hover:opacity-100 h-8 w-8",onClick:s=>{s.stopPropagation(),C(e)},children:(0,a.jsx)(D.A,{className:"h-4 w-4 text-destructive"})})]})},e.id)),v&&(0,a.jsx)("div",{className:"text-center text-sm text-gray-500 py-2",children:"加载更多..."})]}):(0,a.jsx)("div",{className:"p-4 text-center text-sm text-gray-500",children:"暂无邮件"})})]}),(0,a.jsx)(R,{open:!!k,onOpenChange:()=>C(null),children:(0,a.jsxs)(_,{children:[(0,a.jsxs)(J,{children:[(0,a.jsx)(V,{children:"确认删除"}),(0,a.jsxs)(q,{children:["确定要删除邮件 ",null==k?void 0:k.subject," 吗？"]})]}),(0,a.jsxs)(M,{children:[(0,a.jsx)(Y,{children:"取消"}),(0,a.jsx)(U,{className:"bg-destructive hover:bg-destructive/90",onClick:()=>k&&F(k),children:"删除"})]})]})})]})}var X=t(8872);function B(e){let{emailId:s,messageId:t}=e,[r,i]=(0,l.useState)(null),[n,c]=(0,l.useState)(!0),[d,o]=(0,l.useState)("html"),m=(0,l.useRef)(null),{theme:x}=(0,X.D)();(0,l.useEffect)(()=>{(async()=>{try{let e=await fetch("/api/emails/".concat(s,"/").concat(t)),a=await e.json();i(a.message),a.message.html||o("text")}catch(e){console.error("Failed to fetch message:",e)}finally{c(!1)}})()},[s,t]);let u=()=>{if("html"===d&&(null==r?void 0:r.html)&&m.current){var e;let s=m.current,t=s.contentDocument||(null===(e=s.contentWindow)||void 0===e?void 0:e.document);if(t){t.open(),t.write('\n          <!DOCTYPE html>\n          <html>\n            <head>\n              <base target="_blank">\n              <style>\n                html, body {\n                  margin: 0;\n                  padding: 0;\n                  min-height: 100%;\n                  font-family: system-ui, -apple-system, sans-serif;\n                  color: '.concat("dark"===x?"#fff":"#000",";\n                  background: ").concat("dark"===x?"#1a1a1a":"#fff",";\n                }\n                body {\n                  padding: 20px;\n                }\n                img {\n                  max-width: 100%;\n                  height: auto;\n                }\n                a {\n                  color: #2563eb;\n                }\n                /* 滚动条样式 */\n                ::-webkit-scrollbar {\n                  width: 6px;\n                  height: 6px;\n                }\n                ::-webkit-scrollbar-track {\n                  background: transparent;\n                }\n                ::-webkit-scrollbar-thumb {\n                  background: ").concat("dark"===x?"rgba(130, 109, 217, 0.3)":"rgba(130, 109, 217, 0.2)",";\n                  border-radius: 9999px;\n                  transition: background-color 0.2s;\n                }\n                ::-webkit-scrollbar-thumb:hover {\n                  background: ").concat("dark"===x?"rgba(130, 109, 217, 0.5)":"rgba(130, 109, 217, 0.4)",";\n                }\n                /* Firefox 滚动条 */\n                * {\n                  scrollbar-width: thin;\n                  scrollbar-color: ").concat("dark"===x?"rgba(130, 109, 217, 0.3) transparent":"rgba(130, 109, 217, 0.2) transparent",";\n                }\n              </style>\n            </head>\n            <body>").concat(r.html,"</body>\n          </html>\n        ")),t.close();let e=()=>{let e=s.parentElement;e&&(s.style.height="".concat(e.clientHeight,"px"))};e(),window.addEventListener("resize",e);let a=new ResizeObserver(e);return a.observe(t.body),t.querySelectorAll("img").forEach(s=>{s.onload=e}),()=>{window.removeEventListener("resize",e),a.disconnect()}}}};return((0,l.useEffect)(()=>{u()},[null==r?void 0:r.html,d,x]),n)?(0,a.jsx)("div",{className:"flex items-center justify-center h-32",children:(0,a.jsx)(S.A,{className:"w-5 h-5 animate-spin text-primary/60"})}):r?(0,a.jsxs)("div",{className:"h-full flex flex-col",children:[(0,a.jsxs)("div",{className:"p-4 space-y-3 border-b border-primary/20",children:[(0,a.jsx)("h3",{className:"text-base font-bold",children:r.subject}),(0,a.jsxs)("div",{className:"text-xs text-gray-500 space-y-1",children:[(0,a.jsxs)("p",{children:["发件人：",r.from_address]}),(0,a.jsxs)("p",{children:["时间：",new Date(r.received_at).toLocaleString()]})]})]}),r.html&&(0,a.jsx)("div",{className:"border-b border-primary/20 p-2",children:(0,a.jsxs)(f,{value:d,onValueChange:e=>o(e),className:"flex items-center gap-4",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(v,{value:"html",id:"html"}),(0,a.jsx)(g.J,{htmlFor:"html",className:"text-xs cursor-pointer",children:"HTML 格式"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(v,{value:"text",id:"text"}),(0,a.jsx)(g.J,{htmlFor:"text",className:"text-xs cursor-pointer",children:"纯文本格式"})]})]})}),(0,a.jsx)("div",{className:"flex-1 overflow-auto relative",children:"html"===d&&r.html?(0,a.jsx)("iframe",{ref:m,className:"absolute inset-0 w-full h-full border-0 bg-transparent",sandbox:"allow-same-origin allow-popups"}):(0,a.jsx)("div",{className:"p-4 text-sm whitespace-pre-wrap",children:r.content})})]}):null}function K(){let[e,s]=(0,l.useState)(null),[t,r]=(0,l.useState)(null),{copyToClipboard:i}=(0,b.T)(),n="border-2 border-primary/20 bg-background rounded-lg overflow-hidden flex flex-col",c="p-2 border-b-2 border-primary/20 flex items-center justify-between shrink-0",d="text-sm font-bold px-2 w-full overflow-hidden",o=t?"message":e?"emails":"list",x=()=>{i((null==e?void 0:e.address)||"")};return(0,a.jsxs)("div",{className:"pb-5 pt-20 h-full flex flex-col",children:[(0,a.jsxs)("div",{className:"hidden lg:grid grid-cols-12 gap-4 h-full min-h-0",children:[(0,a.jsxs)("div",{className:(0,j.cn)("col-span-3",n),children:[(0,a.jsx)("div",{className:c,children:(0,a.jsx)("h2",{className:d,children:"我的邮箱"})}),(0,a.jsx)("div",{className:"flex-1 overflow-auto",children:(0,a.jsx)(H,{onEmailSelect:e=>{s(e),r(null)},selectedEmailId:null==e?void 0:e.id})})]}),(0,a.jsxs)("div",{className:(0,j.cn)("col-span-4",n),children:[(0,a.jsx)("div",{className:c,children:(0,a.jsx)("h2",{className:d,children:e?(0,a.jsxs)("div",{className:"w-full flex items-center gap-2",children:[(0,a.jsx)("span",{className:"truncate min-w-0",children:e.address}),(0,a.jsx)("div",{className:"shrink-0 cursor-pointer text-primary",onClick:x,children:(0,a.jsx)(m.A,{className:"size-4"})})]}):"选择邮箱查看消息"})}),e&&(0,a.jsx)("div",{className:"flex-1 overflow-auto",children:(0,a.jsx)(Z,{email:e,onMessageSelect:r,selectedMessageId:t})})]}),(0,a.jsxs)("div",{className:(0,j.cn)("col-span-5",n),children:[(0,a.jsx)("div",{className:c,children:(0,a.jsx)("h2",{className:d,children:t?"邮件内容":"选择邮件查看详情"})}),e&&t&&(0,a.jsx)("div",{className:"flex-1 overflow-auto",children:(0,a.jsx)(B,{emailId:e.id,messageId:t,onClose:()=>r(null)})})]})]}),(0,a.jsx)("div",{className:"lg:hidden h-full min-h-0",children:(0,a.jsxs)("div",{className:(0,j.cn)("h-full",n),children:["list"===o&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:c,children:(0,a.jsx)("h2",{className:d,children:"我的邮箱"})}),(0,a.jsx)("div",{className:"flex-1 overflow-auto",children:(0,a.jsx)(H,{onEmailSelect:e=>{s(e)},selectedEmailId:null==e?void 0:e.id})})]}),"emails"===o&&e&&(0,a.jsxs)("div",{className:"h-full flex flex-col",children:[(0,a.jsxs)("div",{className:(0,j.cn)(c,"gap-2"),children:[(0,a.jsx)("button",{onClick:()=>{s(null)},className:"text-sm text-primary shrink-0",children:"← 返回邮箱列表"}),(0,a.jsxs)("div",{className:"flex-1 flex items-center gap-2 min-w-0",children:[(0,a.jsx)("span",{className:"truncate min-w-0 flex-1 text-right",children:e.address}),(0,a.jsx)("div",{className:"shrink-0 cursor-pointer text-primary",onClick:x,children:(0,a.jsx)(m.A,{className:"size-4"})})]})]}),(0,a.jsx)("div",{className:"flex-1 overflow-auto",children:(0,a.jsx)(Z,{email:e,onMessageSelect:r,selectedMessageId:t})})]}),"message"===o&&e&&t&&(0,a.jsxs)("div",{className:"h-full flex flex-col",children:[(0,a.jsxs)("div",{className:c,children:[(0,a.jsx)("button",{onClick:()=>r(null),className:"text-sm text-primary",children:"← 返回消息列表"}),(0,a.jsx)("span",{className:"text-sm font-medium",children:"邮件内容"})]}),(0,a.jsx)("div",{className:"flex-1 overflow-auto",children:(0,a.jsx)(B,{emailId:e.id,messageId:t,onClose:()=>r(null)})})]})]})})]})}},1548:(e,s,t)=>{"use strict";t.d(s,{NoPermissionDialog:()=>n});var a=t(5155),l=t(9393),r=t(6046),i=t(3244);function n(){let e=(0,r.useRouter)(),{config:s}=(0,i.U)();return(0,a.jsx)("div",{className:"fixed inset-0 bg-background/50 backdrop-blur-sm z-50",children:(0,a.jsx)("div",{className:"fixed left-[50%] top-[50%] translate-x-[-50%] translate-y-[-50%] w-[90%] max-w-md",children:(0,a.jsx)("div",{className:"bg-background border-2 border-primary/20 rounded-lg p-6 md:p-12 shadow-lg",children:(0,a.jsxs)("div",{className:"text-center space-y-4",children:[(0,a.jsx)("h1",{className:"text-xl md:text-2xl font-bold",children:"权限不足"}),(0,a.jsx)("p",{className:"text-sm md:text-base text-muted-foreground",children:"你没有权限访问此页面，请联系网站管理员"}),(null==s?void 0:s.adminContact)&&(0,a.jsxs)("p",{className:"text-sm md:text-base text-muted-foreground",children:["管理员联系方式：",s.adminContact]}),(0,a.jsx)(l.$,{onClick:()=>e.push("/"),className:"mt-4 w-full md:w-auto",children:"返回首页"})]})})})})}}},e=>{var s=s=>e(e.s=s);e.O(0,[630,246,568,10,899,86,441,517,358],()=>s(8320)),_N_E=e.O()}]);