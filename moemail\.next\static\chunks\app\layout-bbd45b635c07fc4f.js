(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[177],{1845:(e,t,r)=>{Promise.resolve().then(r.bind(r,134)),Promise.resolve().then(r.bind(r,4229)),Promise.resolve().then(r.bind(r,6910)),Promise.resolve().then(r.bind(r,1241)),Promise.resolve().then(r.t.bind(r,6854,23)),Promise.resolve().then(r.t.bind(r,9324,23))},134:(e,t,r)=>{"use strict";r.d(t,{FloatMenu:()=>i});var n=r(5155),o=r(1626),s=r(9393),a=r(2354);function i(){return(0,n.jsx)("div",{className:"fixed bottom-6 right-6",children:(0,n.jsx)(a.Bc,{children:(0,n.jsxs)(a.m_,{children:[(0,n.jsx)(a.k$,{asChild:!0,children:(0,n.jsxs)(s.$,{variant:"outline",size:"icon",className:"bg-white dark:bg-background rounded-full shadow-lg group relative border-primary/20",onClick:()=>window.open("https://github.com/beilunyang/moemail","_blank"),children:[(0,n.jsx)(o.A,{className:"w-4 h-4 transition-all duration-300 text-primary group-hover:scale-110"}),(0,n.jsx)("span",{className:"sr-only",children:"获取网站源代码"})]})}),(0,n.jsx)(a.ZI,{children:(0,n.jsx)("div",{className:"text-sm",children:(0,n.jsx)("p",{children:"获取网站源代码"})})})]})})})}},4229:(e,t,r)=>{"use strict";r.d(t,{ThemeProvider:()=>s});var n=r(5155),o=r(8872);function s(e){let{children:t,...r}=e;return(0,n.jsx)(o.N,{...r,children:t})}},9393:(e,t,r)=>{"use strict";r.d(t,{$:()=>d,r:()=>l});var n=r(5155),o=r(2115),s=r(2317),a=r(1027),i=r(2558);let l=(0,a.F)("inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground shadow hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90",outline:"border border-input bg-transparent shadow-sm hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2",sm:"h-8 rounded-md px-3 text-xs",lg:"h-10 rounded-md px-8",icon:"h-9 w-9"}},defaultVariants:{variant:"default",size:"default"}}),d=o.forwardRef((e,t)=>{let{className:r,variant:o,size:a,asChild:d=!1,...u}=e,c=d?s.DX:"button";return(0,n.jsx)(c,{className:(0,i.cn)(l({variant:o,size:a,className:r})),ref:t,...u})});d.displayName="Button"},6910:(e,t,r)=>{"use strict";r.d(t,{Toaster:()=>es});var n=r(5155),o=r(2115),s=r(7650),a=r(3610),i=r(8068),l=r(9741),d=r(8166),u=r(9674),c=r(7323),f=r(7028),p=r(3360),m=r(1524),v=r(1488),x=r(6611),w=r(3543),h="ToastProvider",[y,g,b]=(0,l.N)("Toast"),[E,T]=(0,d.A)("Toast",[b]),[N,j]=E(h),R=e=>{let{__scopeToast:t,label:r="Notification",duration:s=5e3,swipeDirection:a="right",swipeThreshold:i=50,children:l}=e,[d,u]=o.useState(null),[c,f]=o.useState(0),p=o.useRef(!1),m=o.useRef(!1);return r.trim()||console.error("Invalid prop `label` supplied to `".concat(h,"`. Expected non-empty `string`.")),(0,n.jsx)(y.Provider,{scope:t,children:(0,n.jsx)(N,{scope:t,label:r,duration:s,swipeDirection:a,swipeThreshold:i,toastCount:c,viewport:d,onViewportChange:u,onToastAdd:o.useCallback(()=>f(e=>e+1),[]),onToastRemove:o.useCallback(()=>f(e=>e-1),[]),isFocusedToastEscapeKeyDownRef:p,isClosePausedRef:m,children:l})})};R.displayName=h;var C="ToastViewport",P=["F8"],S="toast.viewportPause",A="toast.viewportResume",D=o.forwardRef((e,t)=>{let{__scopeToast:r,hotkey:s=P,label:a="Notifications ({hotkey})",...l}=e,d=j(C,r),c=g(r),f=o.useRef(null),m=o.useRef(null),v=o.useRef(null),x=o.useRef(null),w=(0,i.s)(t,x,d.onViewportChange),h=s.join("+").replace(/Key/g,"").replace(/Digit/g,""),b=d.toastCount>0;o.useEffect(()=>{let e=e=>{var t;0!==s.length&&s.every(t=>e[t]||e.code===t)&&(null===(t=x.current)||void 0===t||t.focus())};return document.addEventListener("keydown",e),()=>document.removeEventListener("keydown",e)},[s]),o.useEffect(()=>{let e=f.current,t=x.current;if(b&&e&&t){let r=()=>{if(!d.isClosePausedRef.current){let e=new CustomEvent(S);t.dispatchEvent(e),d.isClosePausedRef.current=!0}},n=()=>{if(d.isClosePausedRef.current){let e=new CustomEvent(A);t.dispatchEvent(e),d.isClosePausedRef.current=!1}},o=t=>{e.contains(t.relatedTarget)||n()},s=()=>{e.contains(document.activeElement)||n()};return e.addEventListener("focusin",r),e.addEventListener("focusout",o),e.addEventListener("pointermove",r),e.addEventListener("pointerleave",s),window.addEventListener("blur",r),window.addEventListener("focus",n),()=>{e.removeEventListener("focusin",r),e.removeEventListener("focusout",o),e.removeEventListener("pointermove",r),e.removeEventListener("pointerleave",s),window.removeEventListener("blur",r),window.removeEventListener("focus",n)}}},[b,d.isClosePausedRef]);let E=o.useCallback(e=>{let{tabbingDirection:t}=e,r=c().map(e=>{let r=e.ref.current,n=[r,...function(e){let t=[],r=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;r.nextNode();)t.push(r.currentNode);return t}(r)];return"forwards"===t?n:n.reverse()});return("forwards"===t?r.reverse():r).flat()},[c]);return o.useEffect(()=>{let e=x.current;if(e){let t=t=>{let r=t.altKey||t.ctrlKey||t.metaKey;if("Tab"===t.key&&!r){var n,o,s;let r=document.activeElement,a=t.shiftKey;if(t.target===e&&a){null===(n=m.current)||void 0===n||n.focus();return}let i=E({tabbingDirection:a?"backwards":"forwards"}),l=i.findIndex(e=>e===r);Z(i.slice(l+1))?t.preventDefault():a?null===(o=m.current)||void 0===o||o.focus():null===(s=v.current)||void 0===s||s.focus()}};return e.addEventListener("keydown",t),()=>e.removeEventListener("keydown",t)}},[c,E]),(0,n.jsxs)(u.lg,{ref:f,role:"region","aria-label":a.replace("{hotkey}",h),tabIndex:-1,style:{pointerEvents:b?void 0:"none"},children:[b&&(0,n.jsx)(I,{ref:m,onFocusFromOutsideViewport:()=>{Z(E({tabbingDirection:"forwards"}))}}),(0,n.jsx)(y.Slot,{scope:r,children:(0,n.jsx)(p.sG.ol,{tabIndex:-1,...l,ref:w})}),b&&(0,n.jsx)(I,{ref:v,onFocusFromOutsideViewport:()=>{Z(E({tabbingDirection:"backwards"}))}})]})});D.displayName=C;var k="ToastFocusProxy",I=o.forwardRef((e,t)=>{let{__scopeToast:r,onFocusFromOutsideViewport:o,...s}=e,a=j(k,r);return(0,n.jsx)(w.s,{"aria-hidden":!0,tabIndex:0,...s,ref:t,style:{position:"fixed"},onFocus:e=>{var t;let r=e.relatedTarget;(null===(t=a.viewport)||void 0===t?void 0:t.contains(r))||o()}})});I.displayName=k;var _="Toast",L=o.forwardRef((e,t)=>{let{forceMount:r,open:o,defaultOpen:s,onOpenChange:i,...l}=e,[d=!0,u]=(0,v.i)({prop:o,defaultProp:s,onChange:i});return(0,n.jsx)(f.C,{present:r||d,children:(0,n.jsx)(O,{open:d,...l,ref:t,onClose:()=>u(!1),onPause:(0,m.c)(e.onPause),onResume:(0,m.c)(e.onResume),onSwipeStart:(0,a.m)(e.onSwipeStart,e=>{e.currentTarget.setAttribute("data-swipe","start")}),onSwipeMove:(0,a.m)(e.onSwipeMove,e=>{let{x:t,y:r}=e.detail.delta;e.currentTarget.setAttribute("data-swipe","move"),e.currentTarget.style.setProperty("--radix-toast-swipe-move-x","".concat(t,"px")),e.currentTarget.style.setProperty("--radix-toast-swipe-move-y","".concat(r,"px"))}),onSwipeCancel:(0,a.m)(e.onSwipeCancel,e=>{e.currentTarget.setAttribute("data-swipe","cancel"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),e.currentTarget.style.removeProperty("--radix-toast-swipe-end-x"),e.currentTarget.style.removeProperty("--radix-toast-swipe-end-y")}),onSwipeEnd:(0,a.m)(e.onSwipeEnd,e=>{let{x:t,y:r}=e.detail.delta;e.currentTarget.setAttribute("data-swipe","end"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),e.currentTarget.style.setProperty("--radix-toast-swipe-end-x","".concat(t,"px")),e.currentTarget.style.setProperty("--radix-toast-swipe-end-y","".concat(r,"px")),u(!1)})})})});L.displayName=_;var[M,F]=E(_,{onClose(){}}),O=o.forwardRef((e,t)=>{let{__scopeToast:r,type:l="foreground",duration:d,open:c,onClose:f,onEscapeKeyDown:v,onPause:x,onResume:w,onSwipeStart:h,onSwipeMove:g,onSwipeCancel:b,onSwipeEnd:E,...T}=e,N=j(_,r),[R,C]=o.useState(null),P=(0,i.s)(t,e=>C(e)),D=o.useRef(null),k=o.useRef(null),I=d||N.duration,L=o.useRef(0),F=o.useRef(I),O=o.useRef(0),{onToastAdd:V,onToastRemove:z}=N,U=(0,m.c)(()=>{var e;(null==R?void 0:R.contains(document.activeElement))&&(null===(e=N.viewport)||void 0===e||e.focus()),f()}),X=o.useCallback(e=>{e&&e!==1/0&&(window.clearTimeout(O.current),L.current=new Date().getTime(),O.current=window.setTimeout(U,e))},[U]);o.useEffect(()=>{let e=N.viewport;if(e){let t=()=>{X(F.current),null==w||w()},r=()=>{let e=new Date().getTime()-L.current;F.current=F.current-e,window.clearTimeout(O.current),null==x||x()};return e.addEventListener(S,r),e.addEventListener(A,t),()=>{e.removeEventListener(S,r),e.removeEventListener(A,t)}}},[N.viewport,I,x,w,X]),o.useEffect(()=>{c&&!N.isClosePausedRef.current&&X(I)},[c,I,N.isClosePausedRef,X]),o.useEffect(()=>(V(),()=>z()),[V,z]);let G=o.useMemo(()=>R?function e(t){let r=[];return Array.from(t.childNodes).forEach(t=>{if(t.nodeType===t.TEXT_NODE&&t.textContent&&r.push(t.textContent),t.nodeType===t.ELEMENT_NODE){let n=t.ariaHidden||t.hidden||"none"===t.style.display,o=""===t.dataset.radixToastAnnounceExclude;if(!n){if(o){let e=t.dataset.radixToastAnnounceAlt;e&&r.push(e)}else r.push(...e(t))}}}),r}(R):null,[R]);return N.viewport?(0,n.jsxs)(n.Fragment,{children:[G&&(0,n.jsx)(K,{__scopeToast:r,role:"status","aria-live":"foreground"===l?"assertive":"polite","aria-atomic":!0,children:G}),(0,n.jsx)(M,{scope:r,onClose:U,children:s.createPortal((0,n.jsx)(y.ItemSlot,{scope:r,children:(0,n.jsx)(u.bL,{asChild:!0,onEscapeKeyDown:(0,a.m)(v,()=>{N.isFocusedToastEscapeKeyDownRef.current||U(),N.isFocusedToastEscapeKeyDownRef.current=!1}),children:(0,n.jsx)(p.sG.li,{role:"status","aria-live":"off","aria-atomic":!0,tabIndex:0,"data-state":c?"open":"closed","data-swipe-direction":N.swipeDirection,...T,ref:P,style:{userSelect:"none",touchAction:"none",...e.style},onKeyDown:(0,a.m)(e.onKeyDown,e=>{"Escape"!==e.key||(null==v||v(e.nativeEvent),e.nativeEvent.defaultPrevented||(N.isFocusedToastEscapeKeyDownRef.current=!0,U()))}),onPointerDown:(0,a.m)(e.onPointerDown,e=>{0===e.button&&(D.current={x:e.clientX,y:e.clientY})}),onPointerMove:(0,a.m)(e.onPointerMove,e=>{if(!D.current)return;let t=e.clientX-D.current.x,r=e.clientY-D.current.y,n=!!k.current,o=["left","right"].includes(N.swipeDirection),s=["left","up"].includes(N.swipeDirection)?Math.min:Math.max,a=o?s(0,t):0,i=o?0:s(0,r),l="touch"===e.pointerType?10:2,d={x:a,y:i},u={originalEvent:e,delta:d};n?(k.current=d,q("toast.swipeMove",g,u,{discrete:!1})):B(d,N.swipeDirection,l)?(k.current=d,q("toast.swipeStart",h,u,{discrete:!1}),e.target.setPointerCapture(e.pointerId)):(Math.abs(t)>l||Math.abs(r)>l)&&(D.current=null)}),onPointerUp:(0,a.m)(e.onPointerUp,e=>{let t=k.current,r=e.target;if(r.hasPointerCapture(e.pointerId)&&r.releasePointerCapture(e.pointerId),k.current=null,D.current=null,t){let r=e.currentTarget,n={originalEvent:e,delta:t};B(t,N.swipeDirection,N.swipeThreshold)?q("toast.swipeEnd",E,n,{discrete:!0}):q("toast.swipeCancel",b,n,{discrete:!0}),r.addEventListener("click",e=>e.preventDefault(),{once:!0})}})})})}),N.viewport)})]}):null}),K=e=>{let{__scopeToast:t,children:r,...s}=e,a=j(_,t),[i,l]=o.useState(!1),[d,u]=o.useState(!1);return function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:()=>{},t=(0,m.c)(e);(0,x.N)(()=>{let e=0,r=0;return e=window.requestAnimationFrame(()=>r=window.requestAnimationFrame(t)),()=>{window.cancelAnimationFrame(e),window.cancelAnimationFrame(r)}},[t])}(()=>l(!0)),o.useEffect(()=>{let e=window.setTimeout(()=>u(!0),1e3);return()=>window.clearTimeout(e)},[]),d?null:(0,n.jsx)(c.Z,{asChild:!0,children:(0,n.jsx)(w.s,{...s,children:i&&(0,n.jsxs)(n.Fragment,{children:[a.label," ",r]})})})},V=o.forwardRef((e,t)=>{let{__scopeToast:r,...o}=e;return(0,n.jsx)(p.sG.div,{...o,ref:t})});V.displayName="ToastTitle";var z=o.forwardRef((e,t)=>{let{__scopeToast:r,...o}=e;return(0,n.jsx)(p.sG.div,{...o,ref:t})});z.displayName="ToastDescription";var U="ToastAction";o.forwardRef((e,t)=>{let{altText:r,...o}=e;return r.trim()?(0,n.jsx)($,{altText:r,asChild:!0,children:(0,n.jsx)(G,{...o,ref:t})}):(console.error("Invalid prop `altText` supplied to `".concat(U,"`. Expected non-empty `string`.")),null)}).displayName=U;var X="ToastClose",G=o.forwardRef((e,t)=>{let{__scopeToast:r,...o}=e,s=F(X,r);return(0,n.jsx)($,{asChild:!0,children:(0,n.jsx)(p.sG.button,{type:"button",...o,ref:t,onClick:(0,a.m)(e.onClick,s.onClose)})})});G.displayName=X;var $=o.forwardRef((e,t)=>{let{__scopeToast:r,altText:o,...s}=e;return(0,n.jsx)(p.sG.div,{"data-radix-toast-announce-exclude":"","data-radix-toast-announce-alt":o||void 0,...s,ref:t})});function q(e,t,r,n){let{discrete:o}=n,s=r.originalEvent.currentTarget,a=new CustomEvent(e,{bubbles:!0,cancelable:!0,detail:r});t&&s.addEventListener(e,t,{once:!0}),o?(0,p.hO)(s,a):s.dispatchEvent(a)}var B=function(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,n=Math.abs(e.x),o=Math.abs(e.y),s=n>o;return"left"===t||"right"===t?s&&n>r:!s&&o>r};function Z(e){let t=document.activeElement;return e.some(e=>e===t||(e.focus(),document.activeElement!==t))}var H=r(1027),W=r(767),Y=r(2558);let Q=o.forwardRef((e,t)=>{let{className:r,...o}=e;return(0,n.jsx)(D,{ref:t,className:(0,Y.cn)("fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]",r),...o})});Q.displayName=D.displayName;let J=(0,H.F)("group pointer-events-auto relative flex w-full items-center justify-between space-x-2 overflow-hidden rounded-md border p-4 pr-6 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full",{variants:{variant:{default:"border bg-background text-foreground",destructive:"destructive group border-destructive bg-destructive text-destructive-foreground"}},defaultVariants:{variant:"default"}}),ee=o.forwardRef((e,t)=>{let{className:r,variant:o,...s}=e;return(0,n.jsx)(L,{ref:t,className:(0,Y.cn)(J({variant:o}),r),...s})});ee.displayName=L.displayName;let et=o.forwardRef((e,t)=>{let{className:r,...o}=e;return(0,n.jsx)(G,{ref:t,className:(0,Y.cn)("absolute right-1 top-1 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-1 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600",r),"toast-close":"",...o,children:(0,n.jsx)(W.A,{className:"h-4 w-4"})})});et.displayName=G.displayName;let er=o.forwardRef((e,t)=>{let{className:r,...o}=e;return(0,n.jsx)(V,{ref:t,className:(0,Y.cn)("text-sm font-semibold [&+div]:text-xs",r),...o})});er.displayName=V.displayName;let en=o.forwardRef((e,t)=>{let{className:r,...o}=e;return(0,n.jsx)(z,{ref:t,className:(0,Y.cn)("text-sm opacity-90",r),...o})});en.displayName=z.displayName;var eo=r(7762);function es(){let{toasts:e}=(0,eo.dj)();return(0,n.jsxs)(R,{children:[e.map(function(e){let{id:t,title:r,description:o,action:s,...a}=e;return(0,n.jsxs)(ee,{...a,children:[(0,n.jsxs)("div",{className:"grid gap-1",children:[r&&(0,n.jsx)(er,{children:r}),o&&(0,n.jsx)(en,{children:o})]}),s,(0,n.jsx)(et,{})]},t)}),(0,n.jsx)(Q,{})]})}},2354:(e,t,r)=>{"use strict";r.d(t,{Bc:()=>i,ZI:()=>u,k$:()=>d,m_:()=>l});var n=r(5155),o=r(2115),s=r(2614),a=r(2558);let i=s.Kq,l=s.bL,d=s.l9,u=o.forwardRef((e,t)=>{let{className:r,sideOffset:o=4,...i}=e;return(0,n.jsx)(s.UC,{ref:t,sideOffset:o,className:(0,a.cn)("z-50 overflow-hidden rounded-md bg-primary px-3 py-1.5 text-xs text-primary-foreground animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",r),...i})});u.displayName=s.UC.displayName},7762:(e,t,r)=>{"use strict";r.d(t,{dj:()=>f});var n=r(2115);let o=0,s=new Map,a=e=>{if(s.has(e))return;let t=setTimeout(()=>{s.delete(e),u({type:"REMOVE_TOAST",toastId:e})},1e6);s.set(e,t)},i=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,1)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case"DISMISS_TOAST":{let{toastId:r}=t;return r?a(r):e.toasts.forEach(e=>{a(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===r||void 0===r?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===t.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)}}},l=[],d={toasts:[]};function u(e){d=i(d,e),l.forEach(e=>{e(d)})}function c(e){let{...t}=e,r=(o=(o+1)%Number.MAX_VALUE).toString(),n=()=>u({type:"DISMISS_TOAST",toastId:r});return u({type:"ADD_TOAST",toast:{...t,id:r,open:!0,onOpenChange:e=>{e||n()}}}),{id:r,dismiss:n,update:e=>u({type:"UPDATE_TOAST",toast:{...e,id:r}})}}function f(){let[e,t]=n.useState(d);return n.useEffect(()=>(l.push(t),()=>{let e=l.indexOf(t);e>-1&&l.splice(e,1)}),[e]),{...e,toast:c,dismiss:e=>u({type:"DISMISS_TOAST",toastId:e})}}},2558:(e,t,r)=>{"use strict";r.d(t,{cn:()=>s});var n=r(3463),o=r(9795);function s(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,o.QP)((0,n.$)(t))}r(2818)},1241:(e,t,r)=>{"use strict";r.d(t,{Providers:()=>s});var n=r(5155),o=r(4822);function s(e){let{children:t}=e;return(0,n.jsx)(o.CP,{children:t})}},9324:()=>{},6854:e=>{e.exports={style:{fontFamily:"'zpix', 'zpix Fallback'"},className:"__className_101f89",variable:"__variable_101f89"}},9741:(e,t,r)=>{"use strict";r.d(t,{N:()=>l});var n=r(2115),o=r(8166),s=r(8068),a=r(2317),i=r(5155);function l(e){let t=e+"CollectionProvider",[r,l]=(0,o.A)(t),[d,u]=r(t,{collectionRef:{current:null},itemMap:new Map}),c=e=>{let{scope:t,children:r}=e,o=n.useRef(null),s=n.useRef(new Map).current;return(0,i.jsx)(d,{scope:t,itemMap:s,collectionRef:o,children:r})};c.displayName=t;let f=e+"CollectionSlot",p=n.forwardRef((e,t)=>{let{scope:r,children:n}=e,o=u(f,r),l=(0,s.s)(t,o.collectionRef);return(0,i.jsx)(a.DX,{ref:l,children:n})});p.displayName=f;let m=e+"CollectionItemSlot",v="data-radix-collection-item",x=n.forwardRef((e,t)=>{let{scope:r,children:o,...l}=e,d=n.useRef(null),c=(0,s.s)(t,d),f=u(m,r);return n.useEffect(()=>(f.itemMap.set(d,{ref:d,...l}),()=>void f.itemMap.delete(d))),(0,i.jsx)(a.DX,{[v]:"",ref:c,children:o})});return x.displayName=m,[{Provider:c,Slot:p,ItemSlot:x},function(t){let r=u(e+"CollectionConsumer",t);return n.useCallback(()=>{let e=r.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll("[".concat(v,"]")));return Array.from(r.itemMap.values()).sort((e,r)=>t.indexOf(e.ref.current)-t.indexOf(r.ref.current))},[r.collectionRef,r.itemMap])},l]}}},e=>{var t=t=>e(e.s=t);e.O(0,[982,630,246,275,441,517,358],()=>t(1845)),_N_E=e.O()}]);