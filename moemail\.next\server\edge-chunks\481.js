(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[481],{47811:(e,t,o)=>{Promise.resolve().then(o.bind(o,85684)),Promise.resolve().then(o.bind(o,70819)),Promise.resolve().then(o.bind(o,28329)),Promise.resolve().then(o.bind(o,13935))},13467:(e,t,o)=>{Promise.resolve().then(o.bind(o,63404)),Promise.resolve().then(o.bind(o,52483)),Promise.resolve().then(o.bind(o,59375)),Promise.resolve().then(o.bind(o,97711))},1205:(e,t,o)=>{Promise.resolve().then(o.bind(o,25275)),Promise.resolve().then(o.bind(o,16435)),Promise.resolve().then(o.bind(o,45791)),Promise.resolve().then(o.bind(o,86867)),Promise.resolve().then(o.bind(o,9234)),Promise.resolve().then(o.bind(o,10614)),Promise.resolve().then(o.bind(o,28066)),Promise.resolve().then(o.bind(o,11751)),Promise.resolve().then(o.bind(o,5321)),Promise.resolve().then(o.bind(o,80798)),Promise.resolve().then(o.bind(o,54267)),Promise.resolve().then(o.bind(o,54505))},43061:(e,t,o)=>{Promise.resolve().then(o.bind(o,46726)),Promise.resolve().then(o.bind(o,91267)),Promise.resolve().then(o.bind(o,50623)),Promise.resolve().then(o.bind(o,9795)),Promise.resolve().then(o.bind(o,54930)),Promise.resolve().then(o.bind(o,55560)),Promise.resolve().then(o.bind(o,71970)),Promise.resolve().then(o.bind(o,79143)),Promise.resolve().then(o.bind(o,34737)),Promise.resolve().then(o.bind(o,734)),Promise.resolve().then(o.bind(o,23723)),Promise.resolve().then(o.bind(o,2769))},63404:(e,t,o)=>{"use strict";o.d(t,{FloatMenu:()=>n});var r=o(37785),s=o(91378),i=o(277),a=o(48156);function n(){return(0,r.jsx)("div",{className:"fixed bottom-6 right-6",children:(0,r.jsx)(a.Bc,{children:(0,r.jsxs)(a.m_,{children:[(0,r.jsx)(a.k$,{asChild:!0,children:(0,r.jsxs)(i.$,{variant:"outline",size:"icon",className:"bg-white dark:bg-background rounded-full shadow-lg group relative border-primary/20",onClick:()=>window.open("https://github.com/beilunyang/moemail","_blank"),children:[(0,r.jsx)(s.A,{className:"w-4 h-4 transition-all duration-300 text-primary group-hover:scale-110"}),(0,r.jsx)("span",{className:"sr-only",children:"获取网站源代码"})]})}),(0,r.jsx)(a.ZI,{children:(0,r.jsx)("div",{className:"text-sm",children:(0,r.jsx)("p",{children:"获取网站源代码"})})})]})})})}},52483:(e,t,o)=>{"use strict";o.d(t,{ThemeProvider:()=>i});var r=o(37785),s=o(38182);function i({children:e,...t}){return(0,r.jsx)(s.N,{...t,children:e})}},277:(e,t,o)=>{"use strict";o.d(t,{$:()=>l,r:()=>d});var r=o(37785),s=o(57845),i=o(13197),a=o(39447),n=o(39086);let d=(0,a.F)("inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground shadow hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90",outline:"border border-input bg-transparent shadow-sm hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2",sm:"h-8 rounded-md px-3 text-xs",lg:"h-10 rounded-md px-8",icon:"h-9 w-9"}},defaultVariants:{variant:"default",size:"default"}}),l=s.forwardRef(({className:e,variant:t,size:o,asChild:s=!1,...a},l)=>{let m=s?i.DX:"button";return(0,r.jsx)(m,{className:(0,n.cn)(d({variant:t,size:o,className:e})),ref:l,...a})});l.displayName="Button"},59375:(e,t,o)=>{"use strict";o.d(t,{Toaster:()=>b});var r=o(37785),s=o(57845),i=o(46156),a=o(39447),n=o(16325),d=o(39086);let l=i.Kq,m=s.forwardRef(({className:e,...t},o)=>(0,r.jsx)(i.LM,{ref:o,className:(0,d.cn)("fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]",e),...t}));m.displayName=i.LM.displayName;let c=(0,a.F)("group pointer-events-auto relative flex w-full items-center justify-between space-x-2 overflow-hidden rounded-md border p-4 pr-6 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full",{variants:{variant:{default:"border bg-background text-foreground",destructive:"destructive group border-destructive bg-destructive text-destructive-foreground"}},defaultVariants:{variant:"default"}}),u=s.forwardRef(({className:e,variant:t,...o},s)=>(0,r.jsx)(i.bL,{ref:s,className:(0,d.cn)(c({variant:t}),e),...o}));u.displayName=i.bL.displayName;let p=s.forwardRef(({className:e,...t},o)=>(0,r.jsx)(i.bm,{ref:o,className:(0,d.cn)("absolute right-1 top-1 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-1 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600",e),"toast-close":"",...t,children:(0,r.jsx)(n.A,{className:"h-4 w-4"})}));p.displayName=i.bm.displayName;let h=s.forwardRef(({className:e,...t},o)=>(0,r.jsx)(i.hE,{ref:o,className:(0,d.cn)("text-sm font-semibold [&+div]:text-xs",e),...t}));h.displayName=i.hE.displayName;let f=s.forwardRef(({className:e,...t},o)=>(0,r.jsx)(i.VY,{ref:o,className:(0,d.cn)("text-sm opacity-90",e),...t}));f.displayName=i.VY.displayName;var v=o(37062);function b(){let{toasts:e}=(0,v.dj)();return(0,r.jsxs)(l,{children:[e.map(function({id:e,title:t,description:o,action:s,...i}){return(0,r.jsxs)(u,{...i,children:[(0,r.jsxs)("div",{className:"grid gap-1",children:[t&&(0,r.jsx)(h,{children:t}),o&&(0,r.jsx)(f,{children:o})]}),s,(0,r.jsx)(p,{})]},e)}),(0,r.jsx)(m,{})]})}},48156:(e,t,o)=>{"use strict";o.d(t,{Bc:()=>n,ZI:()=>m,k$:()=>l,m_:()=>d});var r=o(37785),s=o(57845),i=o(99367),a=o(39086);let n=i.Kq,d=i.bL,l=i.l9,m=s.forwardRef(({className:e,sideOffset:t=4,...o},s)=>(0,r.jsx)(i.UC,{ref:s,sideOffset:t,className:(0,a.cn)("z-50 overflow-hidden rounded-md bg-primary px-3 py-1.5 text-xs text-primary-foreground animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...o}));m.displayName=i.UC.displayName},37062:(e,t,o)=>{"use strict";o.d(t,{dj:()=>u});var r=o(57845);let s=0,i=new Map,a=e=>{if(i.has(e))return;let t=setTimeout(()=>{i.delete(e),m({type:"REMOVE_TOAST",toastId:e})},1e6);i.set(e,t)},n=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,1)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case"DISMISS_TOAST":{let{toastId:o}=t;return o?a(o):e.toasts.forEach(e=>{a(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===o||void 0===o?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===t.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)}}},d=[],l={toasts:[]};function m(e){l=n(l,e),d.forEach(e=>{e(l)})}function c({...e}){let t=(s=(s+1)%Number.MAX_VALUE).toString(),o=()=>m({type:"DISMISS_TOAST",toastId:t});return m({type:"ADD_TOAST",toast:{...e,id:t,open:!0,onOpenChange:e=>{e||o()}}}),{id:t,dismiss:o,update:e=>m({type:"UPDATE_TOAST",toast:{...e,id:t}})}}function u(){let[e,t]=r.useState(l);return r.useEffect(()=>(d.push(t),()=>{let e=d.indexOf(t);e>-1&&d.splice(e,1)}),[e]),{...e,toast:c,dismiss:e=>m({type:"DISMISS_TOAST",toastId:e})}}},39086:(e,t,o)=>{"use strict";o.d(t,{cn:()=>i});var r=o(35253),s=o(59129);function i(...e){return(0,s.QP)((0,r.$)(e))}},97711:(e,t,o)=>{"use strict";o.d(t,{Providers:()=>i});var r=o(37785),s=o(31648);function i({children:e}){return(0,r.jsx)(s.CP,{children:e})}},85684:(e,t,o)=>{"use strict";o.d(t,{FloatMenu:()=>r});let r=(0,o(45710).YR)(function(){throw Error("Attempted to call FloatMenu() from the server but FloatMenu is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"F:\\CODE\\Project\\tempmail\\moemail\\app\\components\\float-menu.tsx","FloatMenu")},70819:(e,t,o)=>{"use strict";o.d(t,{ThemeProvider:()=>r});let r=(0,o(45710).YR)(function(){throw Error("Attempted to call ThemeProvider() from the server but ThemeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"F:\\CODE\\Project\\tempmail\\moemail\\app\\components\\theme\\theme-provider.tsx","ThemeProvider")},28329:(e,t,o)=>{"use strict";o.d(t,{Toaster:()=>r});let r=(0,o(45710).YR)(function(){throw Error("Attempted to call Toaster() from the server but Toaster is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"F:\\CODE\\Project\\tempmail\\moemail\\app\\components\\ui\\toaster.tsx","Toaster")},27711:(e,t,o)=>{"use strict";o.r(t),o.d(t,{default:()=>p,metadata:()=>c,viewport:()=>u});var r=o(4302),s=o(70819),i=o(28329),a=o(29678),n=o(99252),d=o.n(n);o(82704);var l=o(13935),m=o(85684);let c={title:"MoeMail - 萌萌哒临时邮箱服务",description:"安全、快速、一次性的临时邮箱地址，保护您的隐私，远离垃圾邮件。支持即时收件，到期自动失效。",keywords:"临时邮箱, 一次性邮箱, 匿名邮箱, 隐私保护, 垃圾邮件过滤, 即时收件, 自动过期, 安全邮箱, 注册验证, 临时账号, 萌系邮箱, 电子邮件, 隐私安全, 邮件服务, MoeMail",authors:[{name:"SoftMoe Studio"}],creator:"SoftMoe Studio",publisher:"SoftMoe Studio",robots:{index:!0,follow:!0,googleBot:{index:!0,follow:!0}},openGraph:{type:"website",locale:"zh_CN",url:"https://moemail.app",title:"MoeMail - 萌萌哒临时邮箱服务",description:"安全、快速、一次性的临时邮箱地址，保护您的隐私，远离垃圾邮件。支持即时收件，到期自动失效。",siteName:"MoeMail"},twitter:{card:"summary_large_image",title:"MoeMail - 萌萌哒临时邮箱服务",description:"安全、快速、一次性的临时邮箱地址，保护您的隐私，远离垃圾邮件。支持即时收件，到期自动失效。"},manifest:"/manifest.json",icons:[{rel:"apple-touch-icon",url:"/icons/icon-192x192.png"}]},u={themeColor:"#826DD9",width:"device-width",initialScale:1,maximumScale:1,userScalable:!1};function p({children:e}){return(0,r.jsxs)("html",{lang:"zh",suppressHydrationWarning:!0,children:[(0,r.jsxs)("head",{children:[(0,r.jsx)("meta",{name:"application-name",content:"MoeMail"}),(0,r.jsx)("meta",{name:"apple-mobile-web-app-capable",content:"yes"}),(0,r.jsx)("meta",{name:"apple-mobile-web-app-status-bar-style",content:"default"}),(0,r.jsx)("meta",{name:"apple-mobile-web-app-title",content:"MoeMail"}),(0,r.jsx)("meta",{name:"format-detection",content:"telephone=no"}),(0,r.jsx)("meta",{name:"mobile-web-app-capable",content:"yes"})]}),(0,r.jsx)("body",{className:(0,a.cn)(d().variable,"font-zpix min-h-screen antialiased","bg-background text-foreground","transition-colors duration-300"),children:(0,r.jsxs)(s.ThemeProvider,{attribute:"class",defaultTheme:"system",enableSystem:!0,disableTransitionOnChange:!1,storageKey:"temp-mail-theme",children:[(0,r.jsx)(l.Providers,{children:e}),(0,r.jsx)(i.Toaster,{}),(0,r.jsx)(m.FloatMenu,{})]})})]})}},13935:(e,t,o)=>{"use strict";o.d(t,{Providers:()=>r});let r=(0,o(45710).YR)(function(){throw Error("Attempted to call Providers() from the server but Providers is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"F:\\CODE\\Project\\tempmail\\moemail\\app\\providers.tsx","Providers")},46055:(e,t,o)=>{"use strict";o.r(t),o.d(t,{default:()=>s});var r=o(78937);let s=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.Ol)(".",await e.params,"favicon.ico")+""}]},82704:()=>{}}]);
//# sourceMappingURL=481.js.map