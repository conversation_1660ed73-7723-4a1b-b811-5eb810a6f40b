(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[773],{65521:e=>{"use strict";e.exports=require("node:async_hooks")},25356:e=>{"use strict";e.exports=require("node:buffer")},64919:(e,t,i)=>{"use strict";i.r(t),i.d(t,{ComponentMod:()=>M,default:()=>T});var r,a={};i.r(a),i.d(a,{DELETE:()=>y,GET:()=>S,runtime:()=>w});var s={};i.r(s),i.d(s,{patchFetch:()=>C,routeModule:()=>b,serverHooks:()=>q,workAsyncStorage:()=>E,workUnitAsyncStorage:()=>R});var o=i(26312),n=i(35471),c=i(22680),l=i(65954),m=i(14139),u=i(43113),d=i(25601),f=i(85885),p=i(43144),g=i(13091),h=i(80260),v=i(1189),x=i(5120);let w="edge";async function y(e,{params:t}){let i=await (0,x.F6)();if(!i)return d.Rp.json({error:"未授权"},{status:401});try{let e=(0,f.d)(),{id:r}=await t;if(!await e.query.emails.findFirst({where:(0,g.Uo)((0,g.eq)(p.emails.id,r),(0,g.eq)(p.emails.userId,i))}))return d.Rp.json({error:"邮箱不存在或无权限删除"},{status:403});return await e.delete(p.messages).where((0,g.eq)(p.messages.emailId,r)),await e.delete(p.emails).where((0,g.eq)(p.emails.id,r)),d.Rp.json({success:!0})}catch(e){return console.error("Failed to delete email:",e),d.Rp.json({error:"删除邮箱失败"},{status:500})}}async function S(e,{params:t}){let{searchParams:i}=new URL(e.url),r=i.get("cursor");try{let e=(0,f.d)(),{id:i}=await t,a=await (0,x.F6)();if(!a)return d.Rp.json({error:"未授权"},{status:401});let s=await e.query.emails.findFirst({where:(0,g.eq)(p.emails.id,i)});if(!s)return d.Rp.json({error:"邮箱不存在"},{status:404});let o=s.userId===a,n=!1;if(o||(n=!!await e.query.userEmailBindings.findFirst({where:(0,g.Uo)((0,g.eq)(p.userEmailBindings.userId,a),(0,g.eq)(p.userEmailBindings.emailId,i))})),!o&&!n)return d.Rp.json({error:"无权限查看"},{status:403});let c=(0,g.eq)(p.messages.emailId,i),l=await e.select({count:(0,h.ll)`count(*)`}).from(p.messages).where(c),m=Number(l[0].count),u=[c];if(r){let{timestamp:e,id:t}=(0,v.L)(r);u.push((0,g.or)((0,g.lt)(p.messages.receivedAt,new Date(e)),(0,g.Uo)((0,g.eq)(p.messages.receivedAt,new Date(e)),(0,g.lt)(p.messages.id,t))))}let w=await e.query.messages.findMany({where:(0,g.Uo)(...u),orderBy:(e,{desc:t})=>[t(e.receivedAt),t(e.id)],limit:21}),y=w.length>20,S=y?(0,v.v)(w[19].receivedAt.getTime(),w[19].id):null,b=y?w.slice(0,20):w;return d.Rp.json({messages:b.map(e=>({id:e.id,from_address:e.fromAddress,subject:e.subject,received_at:e.receivedAt.getTime()})),nextCursor:S,total:m})}catch(e){return console.error("Failed to fetch messages:",e),d.Rp.json({error:"Failed to fetch messages"},{status:500})}}let b=new l.AppRouteRouteModule({definition:{kind:m.A.APP_ROUTE,page:"/api/emails/[id]/route",pathname:"/api/emails/[id]",filename:"route",bundlePath:"app/api/emails/[id]/route"},resolvedPagePath:"F:\\CODE\\Project\\tempmail\\moemail\\app\\api\\emails\\[id]\\route.ts",nextConfigOutput:"",userland:a}),{workAsyncStorage:E,workUnitAsyncStorage:R,serverHooks:q}=b;function C(){return(0,u.V5)({workAsyncStorage:E,workUnitAsyncStorage:R})}let P=null==(r=self.__RSC_MANIFEST)?void 0:r["/api/emails/[id]/route"],F=(e=>e?JSON.parse(e):void 0)(self.__RSC_SERVER_MANIFEST);P&&F&&(0,n.fQ)({page:"/api/emails/[id]/route",clientReferenceManifest:P,serverActionsManifest:F,serverModuleMap:(0,o.e)({serverActionsManifest:F})});let M=s,T=c.s.wrap(b,{nextConfig:{env:{},eslint:{ignoreDuringBuilds:!1},typescript:{ignoreBuildErrors:!1,tsconfigPath:"tsconfig.json"},distDir:".next",cleanDistDir:!0,assetPrefix:"",cacheMaxMemorySize:0x3200000,configOrigin:"next.config.ts",useFileSystemPublicRoutes:!0,generateEtags:!0,pageExtensions:["tsx","ts","jsx","js"],poweredByHeader:!0,compress:!0,images:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",loaderFile:"",domains:[],disableStaticImages:!1,minimumCacheTTL:60,formats:["image/webp"],dangerouslyAllowSVG:!1,contentSecurityPolicy:"script-src 'none'; frame-src 'none'; sandbox;",contentDispositionType:"attachment",remotePatterns:[{protocol:"https",hostname:"avatars.githubusercontent.com"}],unoptimized:!1},devIndicators:{appIsrStatus:!0,buildActivity:!0,buildActivityPosition:"bottom-right"},onDemandEntries:{maxInactiveAge:6e4,pagesBufferLength:5},amp:{canonicalBase:""},basePath:"",sassOptions:{},trailingSlash:!1,i18n:null,productionBrowserSourceMaps:!1,excludeDefaultMomentLocales:!0,serverRuntimeConfig:{},publicRuntimeConfig:{},reactProductionProfiling:!1,reactStrictMode:null,reactMaxHeadersLength:6e3,httpAgentOptions:{keepAlive:!0},logging:{},expireTime:31536e3,staticPageGenerationTimeout:60,modularizeImports:{"@mui/icons-material":{transform:"@mui/icons-material/{{member}}"},lodash:{transform:"lodash/{{member}}"}},outputFileTracingRoot:"F:\\CODE\\Project\\tempmail\\moemail",experimental:{cacheLife:{default:{stale:300,revalidate:900,expire:0xfffffffe},seconds:{stale:0,revalidate:1,expire:60},minutes:{stale:300,revalidate:60,expire:3600},hours:{stale:300,revalidate:3600,expire:86400},days:{stale:300,revalidate:86400,expire:604800},weeks:{stale:300,revalidate:604800,expire:2592e3},max:{stale:300,revalidate:2592e3,expire:0xfffffffe}},cacheHandlers:{},cssChunking:!0,multiZoneDraftMode:!1,appNavFailHandling:!1,prerenderEarlyExit:!0,serverMinification:!0,serverSourceMaps:!1,linkNoTouchStart:!1,caseSensitiveRoutes:!1,clientSegmentCache:!1,preloadEntriesOnStart:!0,clientRouterFilter:!0,clientRouterFilterRedirects:!1,fetchCacheKeyPrefix:"",middlewarePrefetch:"flexible",optimisticClientCache:!0,manualClientBasePath:!1,cpus:11,memoryBasedWorkersCount:!1,imgOptConcurrency:null,imgOptTimeoutInSeconds:7,imgOptMaxInputPixels:0xfff8001,imgOptSequentialRead:null,isrFlushToDisk:!0,workerThreads:!1,optimizeCss:!1,nextScriptWorkers:!1,scrollRestoration:!1,externalDir:!1,disableOptimizedLoading:!1,gzipSize:!0,craCompat:!1,esmExternals:!0,fullySpecified:!1,swcTraceProfiling:!1,forceSwcTransforms:!1,largePageDataBytes:128e3,turbo:{root:"F:\\CODE\\Project\\tempmail\\moemail"},typedRoutes:!1,typedEnv:!1,parallelServerCompiles:!1,parallelServerBuildTraces:!1,ppr:!1,authInterrupts:!1,reactOwnerStack:!1,webpackMemoryOptimizations:!1,optimizeServerReact:!0,useEarlyImport:!1,staleTimes:{dynamic:0,static:300},serverComponentsHmrCache:!0,staticGenerationMaxConcurrency:8,staticGenerationMinPagesPerWorker:25,dynamicIO:!1,inlineCss:!1,optimizePackageImports:["lucide-react","date-fns","lodash-es","ramda","antd","react-bootstrap","ahooks","@ant-design/icons","@headlessui/react","@headlessui-float/react","@heroicons/react/20/solid","@heroicons/react/24/solid","@heroicons/react/24/outline","@visx/visx","@tremor/react","rxjs","@mui/material","@mui/icons-material","recharts","react-use","effect","@effect/schema","@effect/platform","@effect/platform-node","@effect/platform-browser","@effect/platform-bun","@effect/sql","@effect/sql-mssql","@effect/sql-mysql2","@effect/sql-pg","@effect/sql-squlite-node","@effect/sql-squlite-bun","@effect/sql-squlite-wasm","@effect/sql-squlite-react-native","@effect/rpc","@effect/rpc-http","@effect/typeclass","@effect/experimental","@effect/opentelemetry","@material-ui/core","@material-ui/icons","@tabler/icons-react","mui-core","react-icons/ai","react-icons/bi","react-icons/bs","react-icons/cg","react-icons/ci","react-icons/di","react-icons/fa","react-icons/fa6","react-icons/fc","react-icons/fi","react-icons/gi","react-icons/go","react-icons/gr","react-icons/hi","react-icons/hi2","react-icons/im","react-icons/io","react-icons/io5","react-icons/lia","react-icons/lib","react-icons/lu","react-icons/md","react-icons/pi","react-icons/ri","react-icons/rx","react-icons/si","react-icons/sl","react-icons/tb","react-icons/tfi","react-icons/ti","react-icons/vsc","react-icons/wi"]},bundlePagesRouterDependencies:!1,configFile:"F:\\CODE\\Project\\tempmail\\moemail\\next.config.ts",configFileName:"next.config.ts"}})},96487:()=>{},78335:()=>{},1189:(e,t,i)=>{"use strict";i.d(t,{L:()=>s,v:()=>a});var r=i(25356).Buffer;function a(e,t){return r.from(JSON.stringify({timestamp:e,id:t})).toString("base64")}function s(e){return JSON.parse(r.from(e,"base64").toString())}}},e=>{var t=t=>e(e.s=t);e.O(0,[730,752,899,498,220,156],()=>t(64919));var i=e.O();(_ENTRIES="undefined"==typeof _ENTRIES?{}:_ENTRIES)["middleware_app/api/emails/[id]/route"]=i}]);
//# sourceMappingURL=route.js.map