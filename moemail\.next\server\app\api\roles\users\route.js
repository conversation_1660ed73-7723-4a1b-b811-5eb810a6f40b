(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[272],{65521:e=>{"use strict";e.exports=require("node:async_hooks")},25356:e=>{"use strict";e.exports=require("node:buffer")},26341:(e,t,a)=>{"use strict";a.r(t),a.d(t,{ComponentMod:()=>Q,default:()=>b});var i,r={};a.r(r),a.d(r,{POST:()=>q,runtime:()=>_});var s={};a.r(s),a.d(s,{patchFetch:()=>I,routeModule:()=>g,serverHooks:()=>h,workAsyncStorage:()=>y,workUnitAsyncStorage:()=>x});var n=a(26312),o=a(35471),l=a(22680),d=a(65954),c=a(14139),u=a(43113),m=a(85885),p=a(43144),f=a(13091);let _="edge";async function q(e){try{let{searchText:t}=await e.json();if(!t)return Response.json({error:"请提供用户名或邮箱地址"},{status:400});let a=(0,m.d)(),i=await a.query.users.findFirst({where:t.includes("@")?(0,f.eq)(p.users.email,t):(0,f.eq)(p.users.username,t),with:{userRoles:{with:{role:!0}}}});if(!i)return Response.json({error:"未找到用户"},{status:404});return Response.json({user:{id:i.id,name:i.name,username:i.username,email:i.email,role:i.userRoles[0]?.role.name}})}catch(e){return console.error("Failed to find user:",e),Response.json({error:"查询用户失败"},{status:500})}}let g=new d.AppRouteRouteModule({definition:{kind:c.A.APP_ROUTE,page:"/api/roles/users/route",pathname:"/api/roles/users",filename:"route",bundlePath:"app/api/roles/users/route"},resolvedPagePath:"F:\\CODE\\Project\\tempmail\\moemail\\app\\api\\roles\\users\\route.ts",nextConfigOutput:"",userland:r}),{workAsyncStorage:y,workUnitAsyncStorage:x,serverHooks:h}=g;function I(){return(0,u.V5)({workAsyncStorage:y,workUnitAsyncStorage:x})}let D=null==(i=self.__RSC_MANIFEST)?void 0:i["/api/roles/users/route"],v=(e=>e?JSON.parse(e):void 0)(self.__RSC_SERVER_MANIFEST);D&&v&&(0,o.fQ)({page:"/api/roles/users/route",clientReferenceManifest:D,serverActionsManifest:v,serverModuleMap:(0,n.e)({serverActionsManifest:v})});let Q=s,b=l.s.wrap(g,{nextConfig:{env:{},eslint:{ignoreDuringBuilds:!1},typescript:{ignoreBuildErrors:!1,tsconfigPath:"tsconfig.json"},distDir:".next",cleanDistDir:!0,assetPrefix:"",cacheMaxMemorySize:0x3200000,configOrigin:"next.config.ts",useFileSystemPublicRoutes:!0,generateEtags:!0,pageExtensions:["tsx","ts","jsx","js"],poweredByHeader:!0,compress:!0,images:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",loaderFile:"",domains:[],disableStaticImages:!1,minimumCacheTTL:60,formats:["image/webp"],dangerouslyAllowSVG:!1,contentSecurityPolicy:"script-src 'none'; frame-src 'none'; sandbox;",contentDispositionType:"attachment",remotePatterns:[{protocol:"https",hostname:"avatars.githubusercontent.com"}],unoptimized:!1},devIndicators:{appIsrStatus:!0,buildActivity:!0,buildActivityPosition:"bottom-right"},onDemandEntries:{maxInactiveAge:6e4,pagesBufferLength:5},amp:{canonicalBase:""},basePath:"",sassOptions:{},trailingSlash:!1,i18n:null,productionBrowserSourceMaps:!1,excludeDefaultMomentLocales:!0,serverRuntimeConfig:{},publicRuntimeConfig:{},reactProductionProfiling:!1,reactStrictMode:null,reactMaxHeadersLength:6e3,httpAgentOptions:{keepAlive:!0},logging:{},expireTime:31536e3,staticPageGenerationTimeout:60,modularizeImports:{"@mui/icons-material":{transform:"@mui/icons-material/{{member}}"},lodash:{transform:"lodash/{{member}}"}},outputFileTracingRoot:"F:\\CODE\\Project\\tempmail\\moemail",experimental:{cacheLife:{default:{stale:300,revalidate:900,expire:0xfffffffe},seconds:{stale:0,revalidate:1,expire:60},minutes:{stale:300,revalidate:60,expire:3600},hours:{stale:300,revalidate:3600,expire:86400},days:{stale:300,revalidate:86400,expire:604800},weeks:{stale:300,revalidate:604800,expire:2592e3},max:{stale:300,revalidate:2592e3,expire:0xfffffffe}},cacheHandlers:{},cssChunking:!0,multiZoneDraftMode:!1,appNavFailHandling:!1,prerenderEarlyExit:!0,serverMinification:!0,serverSourceMaps:!1,linkNoTouchStart:!1,caseSensitiveRoutes:!1,clientSegmentCache:!1,preloadEntriesOnStart:!0,clientRouterFilter:!0,clientRouterFilterRedirects:!1,fetchCacheKeyPrefix:"",middlewarePrefetch:"flexible",optimisticClientCache:!0,manualClientBasePath:!1,cpus:11,memoryBasedWorkersCount:!1,imgOptConcurrency:null,imgOptTimeoutInSeconds:7,imgOptMaxInputPixels:0xfff8001,imgOptSequentialRead:null,isrFlushToDisk:!0,workerThreads:!1,optimizeCss:!1,nextScriptWorkers:!1,scrollRestoration:!1,externalDir:!1,disableOptimizedLoading:!1,gzipSize:!0,craCompat:!1,esmExternals:!0,fullySpecified:!1,swcTraceProfiling:!1,forceSwcTransforms:!1,largePageDataBytes:128e3,turbo:{root:"F:\\CODE\\Project\\tempmail\\moemail"},typedRoutes:!1,typedEnv:!1,parallelServerCompiles:!1,parallelServerBuildTraces:!1,ppr:!1,authInterrupts:!1,reactOwnerStack:!1,webpackMemoryOptimizations:!1,optimizeServerReact:!0,useEarlyImport:!1,staleTimes:{dynamic:0,static:300},serverComponentsHmrCache:!0,staticGenerationMaxConcurrency:8,staticGenerationMinPagesPerWorker:25,dynamicIO:!1,inlineCss:!1,optimizePackageImports:["lucide-react","date-fns","lodash-es","ramda","antd","react-bootstrap","ahooks","@ant-design/icons","@headlessui/react","@headlessui-float/react","@heroicons/react/20/solid","@heroicons/react/24/solid","@heroicons/react/24/outline","@visx/visx","@tremor/react","rxjs","@mui/material","@mui/icons-material","recharts","react-use","effect","@effect/schema","@effect/platform","@effect/platform-node","@effect/platform-browser","@effect/platform-bun","@effect/sql","@effect/sql-mssql","@effect/sql-mysql2","@effect/sql-pg","@effect/sql-squlite-node","@effect/sql-squlite-bun","@effect/sql-squlite-wasm","@effect/sql-squlite-react-native","@effect/rpc","@effect/rpc-http","@effect/typeclass","@effect/experimental","@effect/opentelemetry","@material-ui/core","@material-ui/icons","@tabler/icons-react","mui-core","react-icons/ai","react-icons/bi","react-icons/bs","react-icons/cg","react-icons/ci","react-icons/di","react-icons/fa","react-icons/fa6","react-icons/fc","react-icons/fi","react-icons/gi","react-icons/go","react-icons/gr","react-icons/hi","react-icons/hi2","react-icons/im","react-icons/io","react-icons/io5","react-icons/lia","react-icons/lib","react-icons/lu","react-icons/md","react-icons/pi","react-icons/ri","react-icons/rx","react-icons/si","react-icons/sl","react-icons/tb","react-icons/tfi","react-icons/ti","react-icons/vsc","react-icons/wi"]},bundlePagesRouterDependencies:!1,configFile:"F:\\CODE\\Project\\tempmail\\moemail\\next.config.ts",configFileName:"next.config.ts"}})},96487:()=>{},78335:()=>{},85885:(e,t,a)=>{"use strict";a.d(t,{d:()=>n});var i=a(69179),r=a(39235),s=a(43144);let n=()=>(0,r.f)((0,i.getRequestContext)().env.DB,{schema:s})},43144:(e,t,a)=>{"use strict";a.r(t),a.d(t,{accounts:()=>c,apiKeys:()=>q,apiKeysRelations:()=>g,emailCredentials:()=>y,emailCredentialsRelations:()=>x,emailVerificationCodes:()=>D,emails:()=>u,messages:()=>m,roles:()=>f,rolesRelations:()=>b,userEmailBindings:()=>h,userEmailBindingsRelations:()=>I,userRoles:()=>_,userRolesRelations:()=>v,users:()=>d,usersRelations:()=>Q,webhooks:()=>p});var i=a(82792),r=a(50382),s=a(71647),n=a(89376),o=a(57689),l=a(88795);let d=(0,i.D)("user",{id:(0,r.Qq)("id").primaryKey().$defaultFn(()=>crypto.randomUUID()),name:(0,r.Qq)("name"),email:(0,r.Qq)("email").unique(),emailVerified:(0,s.nd)("emailVerified",{mode:"timestamp_ms"}),image:(0,r.Qq)("image"),username:(0,r.Qq)("username").unique(),password:(0,r.Qq)("password")}),c=(0,i.D)("account",{userId:(0,r.Qq)("userId").notNull().references(()=>d.id,{onDelete:"cascade"}),type:(0,r.Qq)("type").$type().notNull(),provider:(0,r.Qq)("provider").notNull(),providerAccountId:(0,r.Qq)("providerAccountId").notNull(),refresh_token:(0,r.Qq)("refresh_token"),access_token:(0,r.Qq)("access_token"),expires_at:(0,s.nd)("expires_at"),token_type:(0,r.Qq)("token_type"),scope:(0,r.Qq)("scope"),id_token:(0,r.Qq)("id_token"),session_state:(0,r.Qq)("session_state")},e=>({compoundKey:(0,n.ie)({columns:[e.provider,e.providerAccountId]})})),u=(0,i.D)("email",{id:(0,r.Qq)("id").primaryKey().$defaultFn(()=>crypto.randomUUID()),address:(0,r.Qq)("address").notNull().unique(),userId:(0,r.Qq)("userId").references(()=>d.id,{onDelete:"cascade"}),createdAt:(0,s.nd)("created_at",{mode:"timestamp_ms"}).notNull().$defaultFn(()=>new Date),expiresAt:(0,s.nd)("expires_at",{mode:"timestamp_ms"}).notNull()},e=>({expiresAtIdx:(0,o.Pe)("email_expires_at_idx").on(e.expiresAt)})),m=(0,i.D)("message",{id:(0,r.Qq)("id").primaryKey().$defaultFn(()=>crypto.randomUUID()),emailId:(0,r.Qq)("emailId").notNull().references(()=>u.id,{onDelete:"cascade"}),fromAddress:(0,r.Qq)("from_address").notNull(),subject:(0,r.Qq)("subject").notNull(),content:(0,r.Qq)("content").notNull(),html:(0,r.Qq)("html"),receivedAt:(0,s.nd)("received_at",{mode:"timestamp_ms"}).notNull().$defaultFn(()=>new Date)},e=>({emailIdIdx:(0,o.Pe)("message_email_id_idx").on(e.emailId)})),p=(0,i.D)("webhook",{id:(0,r.Qq)("id").primaryKey().$defaultFn(()=>crypto.randomUUID()),userId:(0,r.Qq)("user_id").notNull().references(()=>d.id,{onDelete:"cascade"}),url:(0,r.Qq)("url").notNull(),enabled:(0,s.nd)("enabled",{mode:"boolean"}).notNull().default(!0),createdAt:(0,s.nd)("created_at",{mode:"timestamp_ms"}).notNull().$defaultFn(()=>new Date),updatedAt:(0,s.nd)("updated_at",{mode:"timestamp_ms"}).notNull().$defaultFn(()=>new Date)}),f=(0,i.D)("role",{id:(0,r.Qq)("id").primaryKey().$defaultFn(()=>crypto.randomUUID()),name:(0,r.Qq)("name").notNull(),description:(0,r.Qq)("description"),createdAt:(0,s.nd)("created_at",{mode:"timestamp"}).$defaultFn(()=>new Date),updatedAt:(0,s.nd)("updated_at",{mode:"timestamp"}).$defaultFn(()=>new Date)}),_=(0,i.D)("user_role",{userId:(0,r.Qq)("user_id").notNull().references(()=>d.id,{onDelete:"cascade"}),roleId:(0,r.Qq)("role_id").notNull().references(()=>f.id,{onDelete:"cascade"}),createdAt:(0,s.nd)("created_at",{mode:"timestamp"}).$defaultFn(()=>new Date)},e=>({pk:(0,n.ie)({columns:[e.userId,e.roleId]})})),q=(0,i.D)("api_keys",{id:(0,r.Qq)("id").primaryKey().$defaultFn(()=>crypto.randomUUID()),userId:(0,r.Qq)("user_id").notNull().references(()=>d.id),name:(0,r.Qq)("name").notNull(),key:(0,r.Qq)("key").notNull().unique(),createdAt:(0,s.nd)("created_at",{mode:"timestamp"}).$defaultFn(()=>new Date),expiresAt:(0,s.nd)("expires_at",{mode:"timestamp"}),enabled:(0,s.nd)("enabled",{mode:"boolean"}).notNull().default(!0)},e=>({nameUserIdUnique:(0,o.GL)("name_user_id_unique").on(e.name,e.userId)})),g=(0,l.K1)(q,({one:e})=>({user:e(d,{fields:[q.userId],references:[d.id]})})),y=(0,i.D)("email_credentials",{id:(0,r.Qq)("id").primaryKey().$defaultFn(()=>crypto.randomUUID()),userId:(0,r.Qq)("user_id").notNull().references(()=>d.id,{onDelete:"cascade"}),emailAddress:(0,r.Qq)("email_address").notNull(),name:(0,r.Qq)("name").notNull(),jwt:(0,r.Qq)("jwt").notNull(),enabled:(0,s.nd)("enabled",{mode:"boolean"}).notNull().default(!0),createdAt:(0,s.nd)("created_at",{mode:"timestamp"}).$defaultFn(()=>new Date),expiresAt:(0,s.nd)("expires_at",{mode:"timestamp"}),lastUsedAt:(0,s.nd)("last_used_at",{mode:"timestamp"})},e=>({emailUnique:(0,o.GL)("email_address_unique").on(e.emailAddress),nameUserUnique:(0,o.GL)("email_credential_name_user_id_unique").on(e.name,e.userId)})),x=(0,l.K1)(y,({many:e})=>({bindings:e(h)})),h=(0,i.D)("user_email_bindings",{id:(0,r.Qq)("id").primaryKey().$defaultFn(()=>crypto.randomUUID()),userId:(0,r.Qq)("user_id").notNull().references(()=>d.id,{onDelete:"cascade"}),emailId:(0,r.Qq)("email_id").notNull().references(()=>u.id,{onDelete:"cascade"}),credentialId:(0,r.Qq)("credential_id").references(()=>y.id,{onDelete:"set null"}),createdAt:(0,s.nd)("created_at",{mode:"timestamp"}).$defaultFn(()=>new Date)},e=>({userEmailUnique:(0,o.GL)("user_email_binding_unique").on(e.userId,e.emailId)})),I=(0,l.K1)(h,({one:e})=>({user:e(d,{fields:[h.userId],references:[d.id]}),email:e(u,{fields:[h.emailId],references:[u.id]}),credential:e(y,{fields:[h.credentialId],references:[y.id]})})),D=(0,i.D)("email_verification_codes",{id:(0,r.Qq)("id").primaryKey().$defaultFn(()=>crypto.randomUUID()),email:(0,r.Qq)("email").notNull(),code:(0,r.Qq)("code").notNull(),createdAt:(0,s.nd)("created_at",{mode:"timestamp"}).$defaultFn(()=>new Date),expiresAt:(0,s.nd)("expires_at",{mode:"timestamp"}).notNull(),used:(0,s.nd)("used",{mode:"boolean"}).notNull().default(!1)},e=>({emailIdx:(0,o.Pe)("email_verification_email_idx").on(e.email),expiresAtIdx:(0,o.Pe)("email_verification_expires_at_idx").on(e.expiresAt)})),v=(0,l.K1)(_,({one:e})=>({user:e(d,{fields:[_.userId],references:[d.id]}),role:e(f,{fields:[_.roleId],references:[f.id]})})),Q=(0,l.K1)(d,({many:e})=>({userRoles:e(_),apiKeys:e(q)})),b=(0,l.K1)(f,({many:e})=>({userRoles:e(_)}))}},e=>{var t=t=>e(e.s=t);e.O(0,[730,752,220],()=>t(26341));var a=e.O();(_ENTRIES="undefined"==typeof _ENTRIES?{}:_ENTRIES)["middleware_app/api/roles/users/route"]=a}]);
//# sourceMappingURL=route.js.map