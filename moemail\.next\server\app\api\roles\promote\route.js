(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[340],{65521:e=>{"use strict";e.exports=require("node:async_hooks")},25356:e=>{"use strict";e.exports=require("node:buffer")},29898:(e,t,r)=>{"use strict";r.r(t),r.d(t,{ComponentMod:()=>w,default:()=>I});var i,a={};r.r(a),r.d(a,{POST:()=>v,runtime:()=>x});var s={};r.r(s),r.d(s,{patchFetch:()=>E,routeModule:()=>S,serverHooks:()=>C,workAsyncStorage:()=>y,workUnitAsyncStorage:()=>R});var o=r(26312),n=r(35471),c=r(22680),l=r(65954),u=r(14139),p=r(43113),f=r(85885),m=r(43144),d=r(13091),g=r(17451),h=r(83553);let x="edge";async function v(e){try{let{userId:t,roleName:r}=await e.json();if(!t||!r)return Response.json({error:"缺少必要参数"},{status:400});if(![g.gg.DUKE,g.gg.KNIGHT,g.gg.CIVILIAN].includes(r))return Response.json({error:"角色不合法"},{status:400});let i=(0,f.d)(),a=await i.query.userRoles.findFirst({where:(0,d.eq)(m.userRoles.userId,t),with:{role:!0}});if(a?.role.name===g.gg.EMPEROR)return Response.json({error:"不能降级皇帝"},{status:400});let s=await i.query.roles.findFirst({where:(0,d.eq)(m.roles.name,r)});if(!s){let e={[g.gg.DUKE]:"超级用户",[g.gg.KNIGHT]:"高级用户",[g.gg.CIVILIAN]:"普通用户"}[r],[t]=await i.insert(m.roles).values({name:r,description:e}).returning();s=t}return await (0,h.iz)(i,t,s.id),Response.json({success:!0})}catch(e){return console.error("Failed to change user role:",e),Response.json({error:"操作失败"},{status:500})}}let S=new l.AppRouteRouteModule({definition:{kind:u.A.APP_ROUTE,page:"/api/roles/promote/route",pathname:"/api/roles/promote",filename:"route",bundlePath:"app/api/roles/promote/route"},resolvedPagePath:"F:\\CODE\\Project\\tempmail\\moemail\\app\\api\\roles\\promote\\route.ts",nextConfigOutput:"",userland:a}),{workAsyncStorage:y,workUnitAsyncStorage:R,serverHooks:C}=S;function E(){return(0,p.V5)({workAsyncStorage:y,workUnitAsyncStorage:R})}let P=null==(i=self.__RSC_MANIFEST)?void 0:i["/api/roles/promote/route"],b=(e=>e?JSON.parse(e):void 0)(self.__RSC_SERVER_MANIFEST);P&&b&&(0,n.fQ)({page:"/api/roles/promote/route",clientReferenceManifest:P,serverActionsManifest:b,serverModuleMap:(0,o.e)({serverActionsManifest:b})});let w=s,I=c.s.wrap(S,{nextConfig:{env:{},eslint:{ignoreDuringBuilds:!1},typescript:{ignoreBuildErrors:!1,tsconfigPath:"tsconfig.json"},distDir:".next",cleanDistDir:!0,assetPrefix:"",cacheMaxMemorySize:0x3200000,configOrigin:"next.config.ts",useFileSystemPublicRoutes:!0,generateEtags:!0,pageExtensions:["tsx","ts","jsx","js"],poweredByHeader:!0,compress:!0,images:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",loaderFile:"",domains:[],disableStaticImages:!1,minimumCacheTTL:60,formats:["image/webp"],dangerouslyAllowSVG:!1,contentSecurityPolicy:"script-src 'none'; frame-src 'none'; sandbox;",contentDispositionType:"attachment",remotePatterns:[{protocol:"https",hostname:"avatars.githubusercontent.com"}],unoptimized:!1},devIndicators:{appIsrStatus:!0,buildActivity:!0,buildActivityPosition:"bottom-right"},onDemandEntries:{maxInactiveAge:6e4,pagesBufferLength:5},amp:{canonicalBase:""},basePath:"",sassOptions:{},trailingSlash:!1,i18n:null,productionBrowserSourceMaps:!1,excludeDefaultMomentLocales:!0,serverRuntimeConfig:{},publicRuntimeConfig:{},reactProductionProfiling:!1,reactStrictMode:null,reactMaxHeadersLength:6e3,httpAgentOptions:{keepAlive:!0},logging:{},expireTime:31536e3,staticPageGenerationTimeout:60,modularizeImports:{"@mui/icons-material":{transform:"@mui/icons-material/{{member}}"},lodash:{transform:"lodash/{{member}}"}},outputFileTracingRoot:"F:\\CODE\\Project\\tempmail\\moemail",experimental:{cacheLife:{default:{stale:300,revalidate:900,expire:0xfffffffe},seconds:{stale:0,revalidate:1,expire:60},minutes:{stale:300,revalidate:60,expire:3600},hours:{stale:300,revalidate:3600,expire:86400},days:{stale:300,revalidate:86400,expire:604800},weeks:{stale:300,revalidate:604800,expire:2592e3},max:{stale:300,revalidate:2592e3,expire:0xfffffffe}},cacheHandlers:{},cssChunking:!0,multiZoneDraftMode:!1,appNavFailHandling:!1,prerenderEarlyExit:!0,serverMinification:!0,serverSourceMaps:!1,linkNoTouchStart:!1,caseSensitiveRoutes:!1,clientSegmentCache:!1,preloadEntriesOnStart:!0,clientRouterFilter:!0,clientRouterFilterRedirects:!1,fetchCacheKeyPrefix:"",middlewarePrefetch:"flexible",optimisticClientCache:!0,manualClientBasePath:!1,cpus:11,memoryBasedWorkersCount:!1,imgOptConcurrency:null,imgOptTimeoutInSeconds:7,imgOptMaxInputPixels:0xfff8001,imgOptSequentialRead:null,isrFlushToDisk:!0,workerThreads:!1,optimizeCss:!1,nextScriptWorkers:!1,scrollRestoration:!1,externalDir:!1,disableOptimizedLoading:!1,gzipSize:!0,craCompat:!1,esmExternals:!0,fullySpecified:!1,swcTraceProfiling:!1,forceSwcTransforms:!1,largePageDataBytes:128e3,turbo:{root:"F:\\CODE\\Project\\tempmail\\moemail"},typedRoutes:!1,typedEnv:!1,parallelServerCompiles:!1,parallelServerBuildTraces:!1,ppr:!1,authInterrupts:!1,reactOwnerStack:!1,webpackMemoryOptimizations:!1,optimizeServerReact:!0,useEarlyImport:!1,staleTimes:{dynamic:0,static:300},serverComponentsHmrCache:!0,staticGenerationMaxConcurrency:8,staticGenerationMinPagesPerWorker:25,dynamicIO:!1,inlineCss:!1,optimizePackageImports:["lucide-react","date-fns","lodash-es","ramda","antd","react-bootstrap","ahooks","@ant-design/icons","@headlessui/react","@headlessui-float/react","@heroicons/react/20/solid","@heroicons/react/24/solid","@heroicons/react/24/outline","@visx/visx","@tremor/react","rxjs","@mui/material","@mui/icons-material","recharts","react-use","effect","@effect/schema","@effect/platform","@effect/platform-node","@effect/platform-browser","@effect/platform-bun","@effect/sql","@effect/sql-mssql","@effect/sql-mysql2","@effect/sql-pg","@effect/sql-squlite-node","@effect/sql-squlite-bun","@effect/sql-squlite-wasm","@effect/sql-squlite-react-native","@effect/rpc","@effect/rpc-http","@effect/typeclass","@effect/experimental","@effect/opentelemetry","@material-ui/core","@material-ui/icons","@tabler/icons-react","mui-core","react-icons/ai","react-icons/bi","react-icons/bs","react-icons/cg","react-icons/ci","react-icons/di","react-icons/fa","react-icons/fa6","react-icons/fc","react-icons/fi","react-icons/gi","react-icons/go","react-icons/gr","react-icons/hi","react-icons/hi2","react-icons/im","react-icons/io","react-icons/io5","react-icons/lia","react-icons/lib","react-icons/lu","react-icons/md","react-icons/pi","react-icons/ri","react-icons/rx","react-icons/si","react-icons/sl","react-icons/tb","react-icons/tfi","react-icons/ti","react-icons/vsc","react-icons/wi"]},bundlePagesRouterDependencies:!1,configFile:"F:\\CODE\\Project\\tempmail\\moemail\\next.config.ts",configFileName:"next.config.ts"}})},96487:()=>{},78335:()=>{}},e=>{var t=t=>e(e.s=t);e.O(0,[730,752,899,498,220,156],()=>t(29898));var r=e.O();(_ENTRIES="undefined"==typeof _ENTRIES?{}:_ENTRIES)["middleware_app/api/roles/promote/route"]=r}]);
//# sourceMappingURL=route.js.map