"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Link, Loader2 } from "lucide-react"
import { useToast } from "@/components/ui/use-toast"
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogFooter,
  DialogDescription,
} from "@/components/ui/dialog"

interface BindEmailDialogProps {
  onEmailBound: () => void
}

export function BindEmailDialog({ onEmailBound }: BindEmailDialogProps) {
  const [open, setOpen] = useState(false)
  const [loading, setLoading] = useState(false)
  const [formData, setFormData] = useState({
    jwt: ""
  })
  const { toast } = useToast()

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!formData.jwt.trim()) {
      toast({
        title: "请输入邮箱凭证",
        description: "邮箱凭证不能为空",
        variant: "destructive"
      })
      return
    }

    setLoading(true)
    try {
      const response = await fetch("/api/email-credentials", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(formData),
      })

      if (!response.ok) {
        const data = await response.json()
        throw new Error(data.error || "绑定邮箱失败")
      }

      toast({
        title: "绑定成功",
        description: "邮箱绑定成功"
      })

      setFormData({ jwt: "" })
      setOpen(false)
      onEmailBound()
    } catch (error) {
      toast({
        title: "绑定失败",
        description: error instanceof Error ? error.message : "请稍后重试",
        variant: "destructive"
      })
    } finally {
      setLoading(false)
    }
  }

  const handleClose = () => {
    setOpen(false)
    setFormData({ jwt: "" })
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" size="sm" className="gap-1">
          <Link className="h-3 w-3" />
          绑定邮箱
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle>绑定邮箱</DialogTitle>
          <DialogDescription>
            输入邮箱凭证来绑定邮箱到您的账户
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="jwt">邮箱凭证 (JWT)</Label>
            <Input
              id="jwt"
              value={formData.jwt}
              onChange={(e) => setFormData(prev => ({ ...prev, jwt: e.target.value }))}
              placeholder="请输入邮箱凭证..."
              required
            />
          </div>

          <div className="rounded-lg bg-blue-50 p-3 text-sm text-blue-800">
            <p className="font-medium mb-1">提示：</p>
            <ul className="space-y-1 text-xs">
              <li>• 邮箱凭证由管理员提供</li>
              <li>• 每个邮箱对应唯一的凭证</li>
              <li>• 绑定后可以接收该邮箱的邮件</li>
              <li>• 支持同时绑定多个邮箱</li>
            </ul>
          </div>

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={handleClose}
              disabled={loading}
            >
              取消
            </Button>
            <Button type="submit" disabled={loading}>
              {loading ? (
                <>
                  <Loader2 className="w-4 h-4 animate-spin mr-2" />
                  绑定中...
                </>
              ) : (
                "绑定邮箱"
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}
