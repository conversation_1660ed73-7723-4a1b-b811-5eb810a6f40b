import { auth } from "@/lib/auth"
import { NextResponse } from "next/server"
import { EmailCredentialManager } from "@/lib/emailCredentials"
import { z } from "zod"

export const runtime = "edge"

const createCredentialSchema = z.object({
  emailAddress: z.string().email("无效的邮箱地址"),
})

export async function POST(request: Request) {
  const session = await auth()
  if (!session?.user?.id) {
    return NextResponse.json({ error: "未授权" }, { status: 401 })
  }

  try {
    const body = await request.json()
    const validatedData = createCredentialSchema.parse(body)

    // 验证邮箱是否属于当前用户
    const credential = await EmailCredentialManager.createCredentialForEmail(
      validatedData.emailAddress,
      session.user.id
    )

    return NextResponse.json({
      success: true,
      jwt: credential.jwt,
      credentialId: credential.id,
      message: "邮箱凭证创建成功"
    })
  } catch (error) {
    console.error("Failed to create email credential:", error)

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: error.errors[0].message },
        { status: 400 }
      )
    }

    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: "创建邮箱凭证失败" },
      { status: 500 }
    )
  }
}
