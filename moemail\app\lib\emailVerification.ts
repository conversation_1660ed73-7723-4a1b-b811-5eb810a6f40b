import { createDb } from './db'
import { emailVerificationCodes } from './schema'
import { eq, and, gt } from 'drizzle-orm'
import { JWTUtils } from './jwt'

export interface SendVerificationCodeParams {
  email: string
  expirationMinutes?: number
}

export interface VerifyCodeParams {
  email: string
  code: string
}

export class EmailVerificationManager {
  // 发送验证码
  static async sendVerificationCode(params: SendVerificationCodeParams): Promise<{
    success: boolean
    expirationTtl: number
    message?: string
  }> {
    const { email, expirationMinutes = 5 } = params
    const db = createDb()

    // 检查是否已有未过期的验证码
    const existingCode = await db.query.emailVerificationCodes.findFirst({
      where: and(
        eq(emailVerificationCodes.email, email),
        gt(emailVerificationCodes.expiresAt, new Date()),
        eq(emailVerificationCodes.used, false)
      )
    })

    if (existingCode) {
      const remainingTime = Math.ceil((existingCode.expiresAt.getTime() - Date.now()) / 1000)
      return {
        success: false,
        expirationTtl: remainingTime,
        message: '验证码已发送，请稍后再试'
      }
    }

    // 生成6位数验证码
    const code = JWTUtils.generateVerificationCode()
    const expiresAt = new Date(Date.now() + expirationMinutes * 60 * 1000)

    // 保存验证码到数据库
    await db.insert(emailVerificationCodes).values({
      email,
      code,
      expiresAt
    })

    // TODO: 这里应该集成实际的邮件发送服务
    // 目前只是模拟发送，实际项目中需要集成如 Resend、SendGrid 等服务
    console.log(`发送验证码到 ${email}: ${code}`)

    return {
      success: true,
      expirationTtl: expirationMinutes * 60
    }
  }

  // 验证验证码
  static async verifyCode(params: VerifyCodeParams): Promise<{
    success: boolean
    message?: string
  }> {
    const { email, code } = params
    const db = createDb()

    // 查找有效的验证码
    const verificationRecord = await db.query.emailVerificationCodes.findFirst({
      where: and(
        eq(emailVerificationCodes.email, email),
        eq(emailVerificationCodes.code, code),
        gt(emailVerificationCodes.expiresAt, new Date()),
        eq(emailVerificationCodes.used, false)
      )
    })

    if (!verificationRecord) {
      return {
        success: false,
        message: '验证码无效或已过期'
      }
    }

    // 标记验证码为已使用
    await db.update(emailVerificationCodes)
      .set({ used: true })
      .where(eq(emailVerificationCodes.id, verificationRecord.id))

    return {
      success: true
    }
  }

  // 清理过期的验证码
  static async cleanupExpiredCodes(): Promise<number> {
    const db = createDb()
    
    const result = await db.delete(emailVerificationCodes)
      .where(gt(new Date(), emailVerificationCodes.expiresAt))

    return result.changes
  }

  // 检查邮箱是否有待验证的验证码
  static async hasPendingVerification(email: string): Promise<boolean> {
    const db = createDb()

    const pendingCode = await db.query.emailVerificationCodes.findFirst({
      where: and(
        eq(emailVerificationCodes.email, email),
        gt(emailVerificationCodes.expiresAt, new Date()),
        eq(emailVerificationCodes.used, false)
      )
    })

    return !!pendingCode
  }

  // 获取验证码剩余时间（秒）
  static async getRemainingTime(email: string): Promise<number> {
    const db = createDb()

    const verificationRecord = await db.query.emailVerificationCodes.findFirst({
      where: and(
        eq(emailVerificationCodes.email, email),
        gt(emailVerificationCodes.expiresAt, new Date()),
        eq(emailVerificationCodes.used, false)
      )
    })

    if (!verificationRecord) {
      return 0
    }

    return Math.max(0, Math.ceil((verificationRecord.expiresAt.getTime() - Date.now()) / 1000))
  }
}

// 邮件发送接口（需要根据实际邮件服务提供商实现）
export interface EmailSender {
  sendVerificationCode(to: string, code: string): Promise<void>
}

// 示例邮件发送器实现（使用 console.log 模拟）
export class ConsoleEmailSender implements EmailSender {
  async sendVerificationCode(to: string, code: string): Promise<void> {
    console.log(`[邮件发送] 收件人: ${to}, 验证码: ${code}`)
    console.log(`邮件内容: 您的验证码是 ${code}，5分钟内有效。`)
  }
}

// 如果使用 Resend 服务的示例实现
export class ResendEmailSender implements EmailSender {
  private apiKey: string

  constructor(apiKey: string) {
    this.apiKey = apiKey
  }

  async sendVerificationCode(to: string, code: string): Promise<void> {
    // 这里是使用 Resend API 发送邮件的示例代码
    // 实际使用时需要安装 resend 包并正确配置
    /*
    const resend = new Resend(this.apiKey)
    
    await resend.emails.send({
      from: '<EMAIL>',
      to,
      subject: '邮箱验证码',
      html: `
        <h2>邮箱验证</h2>
        <p>您的验证码是：<strong>${code}</strong></p>
        <p>验证码5分钟内有效，请及时使用。</p>
      `
    })
    */
    
    // 临时使用 console.log 代替
    console.log(`[Resend] 发送验证码到 ${to}: ${code}`)
  }
}
