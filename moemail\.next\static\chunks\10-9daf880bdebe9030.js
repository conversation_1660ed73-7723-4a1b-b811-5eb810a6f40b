"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[10],{5587:(e,t,n)=>{n.d(t,{Eq:()=>s});var r=new WeakMap,o=new WeakMap,l={},i=0,a=function(e){return e&&(e.host||a(e.parentNode))},u=function(e,t,n,u){var s=(Array.isArray(e)?e:[e]).map(function(e){if(t.contains(e))return e;var n=a(e);return n&&t.contains(n)?n:(console.error("aria-hidden",e,"in not contained inside",t,". Doing nothing"),null)}).filter(function(e){return!!e});l[n]||(l[n]=new WeakMap);var c=l[n],d=[],f=new Set,p=new Set(s),v=function(e){!e||f.has(e)||(f.add(e),v(e.parentNode))};s.forEach(v);var m=function(e){!e||p.has(e)||Array.prototype.forEach.call(e.children,function(e){if(f.has(e))m(e);else try{var t=e.getAttribute(u),l=null!==t&&"false"!==t,i=(r.get(e)||0)+1,a=(c.get(e)||0)+1;r.set(e,i),c.set(e,a),d.push(e),1===i&&l&&o.set(e,!0),1===a&&e.setAttribute(n,"true"),l||e.setAttribute(u,"true")}catch(t){console.error("aria-hidden: cannot operate on ",e,t)}})};return m(t),f.clear(),i++,function(){d.forEach(function(e){var t=r.get(e)-1,l=c.get(e)-1;r.set(e,t),c.set(e,l),t||(o.has(e)||e.removeAttribute(u),o.delete(e)),l||e.removeAttribute(n)}),--i||(r=new WeakMap,r=new WeakMap,o=new WeakMap,l={})}},s=function(e,t,n){void 0===n&&(n="data-aria-hidden");var r,o=Array.from(Array.isArray(e)?e:[e]),l=t||(r=e,"undefined"==typeof document?null:(Array.isArray(r)?r[0]:r).ownerDocument.body);return l?(o.push.apply(o,Array.from(l.querySelectorAll("[aria-live]"))),u(o,l,n,"aria-hidden")):function(){return null}}},8867:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(7401).A)("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},1719:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(7401).A)("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},3920:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(7401).A)("Copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]])},6744:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(7401).A)("Key",[["path",{d:"m15.5 7.5 2.3 2.3a1 1 0 0 0 1.4 0l2.1-2.1a1 1 0 0 0 0-1.4L19 4",key:"g0fldk"}],["path",{d:"m21 2-9.6 9.6",key:"1j0ho8"}],["circle",{cx:"7.5",cy:"15.5",r:"5.5",key:"yqb3hr"}]])},4505:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(7401).A)("LoaderCircle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},3473:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(7401).A)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},1773:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(7401).A)("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},5686:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(7401).A)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},4065:(e,t,n)=>{n.d(t,{A:()=>K});var r,o,l=function(){return(l=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)};function i(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)0>t.indexOf(r[o])&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n}Object.create,Object.create;var a=("function"==typeof SuppressedError&&SuppressedError,n(2115)),u="right-scroll-bar-position",s="width-before-scroll-bar";function c(e,t){return"function"==typeof e?e(t):e&&(e.current=t),e}var d="undefined"!=typeof window?a.useLayoutEffect:a.useEffect,f=new WeakMap;function p(e){return e}var v=function(e){void 0===e&&(e={});var t,n,r,o=(void 0===t&&(t=p),n=[],r=!1,{read:function(){if(r)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return n.length?n[n.length-1]:null},useMedium:function(e){var o=t(e,r);return n.push(o),function(){n=n.filter(function(e){return e!==o})}},assignSyncMedium:function(e){for(r=!0;n.length;){var t=n;n=[],t.forEach(e)}n={push:function(t){return e(t)},filter:function(){return n}}},assignMedium:function(e){r=!0;var t=[];if(n.length){var o=n;n=[],o.forEach(e),t=n}var l=function(){var n=t;t=[],n.forEach(e)},i=function(){return Promise.resolve().then(l)};i(),n={push:function(e){t.push(e),i()},filter:function(e){return t=t.filter(e),n}}}});return o.options=l({async:!0,ssr:!1},e),o}(),m=function(){},h=a.forwardRef(function(e,t){var n,r,o,u,s=a.useRef(null),p=a.useState({onScrollCapture:m,onWheelCapture:m,onTouchMoveCapture:m}),h=p[0],y=p[1],g=e.forwardProps,w=e.children,b=e.className,x=e.removeScrollBar,E=e.enabled,C=e.shards,S=e.sideCar,R=e.noRelative,N=e.noIsolation,j=e.inert,P=e.allowPinchZoom,D=e.as,A=e.gapMode,k=i(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),T=(n=[s,t],r=function(e){return n.forEach(function(t){return c(t,e)})},(o=(0,a.useState)(function(){return{value:null,callback:r,facade:{get current(){return o.value},set current(value){var e=o.value;e!==value&&(o.value=value,o.callback(value,e))}}}})[0]).callback=r,u=o.facade,d(function(){var e=f.get(u);if(e){var t=new Set(e),r=new Set(n),o=u.current;t.forEach(function(e){r.has(e)||c(e,null)}),r.forEach(function(e){t.has(e)||c(e,o)})}f.set(u,n)},[n]),u),O=l(l({},k),h);return a.createElement(a.Fragment,null,E&&a.createElement(S,{sideCar:v,removeScrollBar:x,shards:C,noRelative:R,noIsolation:N,inert:j,setCallbacks:y,allowPinchZoom:!!P,lockRef:s,gapMode:A}),g?a.cloneElement(a.Children.only(w),l(l({},O),{ref:T})):a.createElement(void 0===D?"div":D,l({},O,{className:b,ref:T}),w))});h.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},h.classNames={fullWidth:s,zeroRight:u};var y=function(e){var t=e.sideCar,n=i(e,["sideCar"]);if(!t)throw Error("Sidecar: please provide `sideCar` property to import the right car");var r=t.read();if(!r)throw Error("Sidecar medium not found");return a.createElement(r,l({},n))};y.isSideCarExport=!0;var g=function(){var e=0,t=null;return{add:function(r){if(0==e&&(t=function(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=o||n.nc;return t&&e.setAttribute("nonce",t),e}())){var l,i;(l=t).styleSheet?l.styleSheet.cssText=r:l.appendChild(document.createTextNode(r)),i=t,(document.head||document.getElementsByTagName("head")[0]).appendChild(i)}e++},remove:function(){--e||!t||(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},w=function(){var e=g();return function(t,n){a.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},b=function(){var e=w();return function(t){return e(t.styles,t.dynamic),null}},x={left:0,top:0,right:0,gap:0},E=function(e){return parseInt(e||"",10)||0},C=function(e){var t=window.getComputedStyle(document.body),n=t["padding"===e?"paddingLeft":"marginLeft"],r=t["padding"===e?"paddingTop":"marginTop"],o=t["padding"===e?"paddingRight":"marginRight"];return[E(n),E(r),E(o)]},S=function(e){if(void 0===e&&(e="margin"),"undefined"==typeof window)return x;var t=C(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}},R=b(),N="data-scroll-locked",j=function(e,t,n,r){var o=e.left,l=e.top,i=e.right,a=e.gap;return void 0===n&&(n="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(r,";\n   padding-right: ").concat(a,"px ").concat(r,";\n  }\n  body[").concat(N,"] {\n    overflow: hidden ").concat(r,";\n    overscroll-behavior: contain;\n    ").concat([t&&"position: relative ".concat(r,";"),"margin"===n&&"\n    padding-left: ".concat(o,"px;\n    padding-top: ").concat(l,"px;\n    padding-right: ").concat(i,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(a,"px ").concat(r,";\n    "),"padding"===n&&"padding-right: ".concat(a,"px ").concat(r,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(u," {\n    right: ").concat(a,"px ").concat(r,";\n  }\n  \n  .").concat(s," {\n    margin-right: ").concat(a,"px ").concat(r,";\n  }\n  \n  .").concat(u," .").concat(u," {\n    right: 0 ").concat(r,";\n  }\n  \n  .").concat(s," .").concat(s," {\n    margin-right: 0 ").concat(r,";\n  }\n  \n  body[").concat(N,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(a,"px;\n  }\n")},P=function(){var e=parseInt(document.body.getAttribute(N)||"0",10);return isFinite(e)?e:0},D=function(){a.useEffect(function(){return document.body.setAttribute(N,(P()+1).toString()),function(){var e=P()-1;e<=0?document.body.removeAttribute(N):document.body.setAttribute(N,e.toString())}},[])},A=function(e){var t=e.noRelative,n=e.noImportant,r=e.gapMode,o=void 0===r?"margin":r;D();var l=a.useMemo(function(){return S(o)},[o]);return a.createElement(R,{styles:j(l,!t,o,n?"":"!important")})},k=!1;if("undefined"!=typeof window)try{var T=Object.defineProperty({},"passive",{get:function(){return k=!0,!0}});window.addEventListener("test",T,T),window.removeEventListener("test",T,T)}catch(e){k=!1}var O=!!k&&{passive:!1},M=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return"hidden"!==n[t]&&!(n.overflowY===n.overflowX&&"TEXTAREA"!==e.tagName&&"visible"===n[t])},L=function(e,t){var n=t.ownerDocument,r=t;do{if("undefined"!=typeof ShadowRoot&&r instanceof ShadowRoot&&(r=r.host),I(e,r)){var o=_(e,r);if(o[1]>o[2])return!0}r=r.parentNode}while(r&&r!==n.body);return!1},I=function(e,t){return"v"===e?M(t,"overflowY"):M(t,"overflowX")},_=function(e,t){return"v"===e?[t.scrollTop,t.scrollHeight,t.clientHeight]:[t.scrollLeft,t.scrollWidth,t.clientWidth]},W=function(e,t,n,r,o){var l,i=(l=window.getComputedStyle(t).direction,"h"===e&&"rtl"===l?-1:1),a=i*r,u=n.target,s=t.contains(u),c=!1,d=a>0,f=0,p=0;do{if(!u)break;var v=_(e,u),m=v[0],h=v[1]-v[2]-i*m;(m||h)&&I(e,u)&&(f+=h,p+=m);var y=u.parentNode;u=y&&y.nodeType===Node.DOCUMENT_FRAGMENT_NODE?y.host:y}while(!s&&u!==document.body||s&&(t.contains(u)||t===u));return d&&(o&&1>Math.abs(f)||!o&&a>f)?c=!0:!d&&(o&&1>Math.abs(p)||!o&&-a>p)&&(c=!0),c},F=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},B=function(e){return[e.deltaX,e.deltaY]},H=function(e){return e&&"current"in e?e.current:e},V=0,$=[];let U=(r=function(e){var t=a.useRef([]),n=a.useRef([0,0]),r=a.useRef(),o=a.useState(V++)[0],l=a.useState(b)[0],i=a.useRef(e);a.useEffect(function(){i.current=e},[e]),a.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var t=(function(e,t,n){if(n||2==arguments.length)for(var r,o=0,l=t.length;o<l;o++)!r&&o in t||(r||(r=Array.prototype.slice.call(t,0,o)),r[o]=t[o]);return e.concat(r||Array.prototype.slice.call(t))})([e.lockRef.current],(e.shards||[]).map(H),!0).filter(Boolean);return t.forEach(function(e){return e.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),t.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var u=a.useCallback(function(e,t){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!i.current.allowPinchZoom;var o,l=F(e),a=n.current,u="deltaX"in e?e.deltaX:a[0]-l[0],s="deltaY"in e?e.deltaY:a[1]-l[1],c=e.target,d=Math.abs(u)>Math.abs(s)?"h":"v";if("touches"in e&&"h"===d&&"range"===c.type)return!1;var f=L(d,c);if(!f)return!0;if(f?o=d:(o="v"===d?"h":"v",f=L(d,c)),!f)return!1;if(!r.current&&"changedTouches"in e&&(u||s)&&(r.current=o),!o)return!0;var p=r.current||o;return W(p,t,e,"h"===p?u:s,!0)},[]),s=a.useCallback(function(e){if($.length&&$[$.length-1]===l){var n="deltaY"in e?B(e):F(e),r=t.current.filter(function(t){var r;return t.name===e.type&&(t.target===e.target||e.target===t.shadowParent)&&(r=t.delta)[0]===n[0]&&r[1]===n[1]})[0];if(r&&r.should){e.cancelable&&e.preventDefault();return}if(!r){var o=(i.current.shards||[]).map(H).filter(Boolean).filter(function(t){return t.contains(e.target)});(o.length>0?u(e,o[0]):!i.current.noIsolation)&&e.cancelable&&e.preventDefault()}}},[]),c=a.useCallback(function(e,n,r,o){var l={name:e,delta:n,target:r,should:o,shadowParent:function(e){for(var t=null;null!==e;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}(r)};t.current.push(l),setTimeout(function(){t.current=t.current.filter(function(e){return e!==l})},1)},[]),d=a.useCallback(function(e){n.current=F(e),r.current=void 0},[]),f=a.useCallback(function(t){c(t.type,B(t),t.target,u(t,e.lockRef.current))},[]),p=a.useCallback(function(t){c(t.type,F(t),t.target,u(t,e.lockRef.current))},[]);a.useEffect(function(){return $.push(l),e.setCallbacks({onScrollCapture:f,onWheelCapture:f,onTouchMoveCapture:p}),document.addEventListener("wheel",s,O),document.addEventListener("touchmove",s,O),document.addEventListener("touchstart",d,O),function(){$=$.filter(function(e){return e!==l}),document.removeEventListener("wheel",s,O),document.removeEventListener("touchmove",s,O),document.removeEventListener("touchstart",d,O)}},[]);var v=e.removeScrollBar,m=e.inert;return a.createElement(a.Fragment,null,m?a.createElement(l,{styles:"\n  .block-interactivity-".concat(o," {pointer-events: none;}\n  .allow-interactivity-").concat(o," {pointer-events: all;}\n")}):null,v?a.createElement(A,{noRelative:e.noRelative,gapMode:e.gapMode}):null)},v.useMedium(r),y);var z=a.forwardRef(function(e,t){return a.createElement(h,l({},e,{ref:t,sideCar:U}))});z.classNames=h.classNames;let K=z},3322:(e,t,n)=>{n.d(t,{bm:()=>eb,UC:()=>ey,VY:()=>ew,hJ:()=>eh,ZL:()=>em,bL:()=>ep,hE:()=>eg,l9:()=>ev,G$:()=>es,Hs:()=>I});var r,o=n(2115),l=n.t(o,2);function i(e,t,{checkForDefaultPrevented:n=!0}={}){return function(r){if(e?.(r),!1===n||!r.defaultPrevented)return t?.(r)}}function a(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function u(...e){return t=>{let n=!1,r=e.map(e=>{let r=a(e,t);return n||"function"!=typeof r||(n=!0),r});if(n)return()=>{for(let t=0;t<r.length;t++){let n=r[t];"function"==typeof n?n():a(e[t],null)}}}}function s(...e){return o.useCallback(u(...e),e)}var c=n(5155),d=globalThis?.document?o.useLayoutEffect:()=>{},f=l[" useId ".trim().toString()]||(()=>void 0),p=0;function v(e){let[t,n]=o.useState(f());return d(()=>{e||n(e=>e??String(p++))},[e]),e||(t?`radix-${t}`:"")}var m=l[" useInsertionEffect ".trim().toString()]||d,h=(Symbol("RADIX:SYNC_STATE"),n(7650));function y(e){let t=function(e){let t=o.forwardRef((e,t)=>{let{children:n,...r}=e;if(o.isValidElement(n)){let e,l;let i=(e=Object.getOwnPropertyDescriptor(n.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?n.ref:(e=Object.getOwnPropertyDescriptor(n,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?n.props.ref:n.props.ref||n.ref,a=function(e,t){let n={...t};for(let r in t){let o=e[r],l=t[r];/^on[A-Z]/.test(r)?o&&l?n[r]=(...e)=>{let t=l(...e);return o(...e),t}:o&&(n[r]=o):"style"===r?n[r]={...o,...l}:"className"===r&&(n[r]=[o,l].filter(Boolean).join(" "))}return{...e,...n}}(r,n.props);return n.type!==o.Fragment&&(a.ref=t?u(t,i):i),o.cloneElement(n,a)}return o.Children.count(n)>1?o.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),n=o.forwardRef((e,n)=>{let{children:r,...l}=e,i=o.Children.toArray(r),a=i.find(w);if(a){let e=a.props.children,r=i.map(t=>t!==a?t:o.Children.count(e)>1?o.Children.only(null):o.isValidElement(e)?e.props.children:null);return(0,c.jsx)(t,{...l,ref:n,children:o.isValidElement(e)?o.cloneElement(e,void 0,r):null})}return(0,c.jsx)(t,{...l,ref:n,children:r})});return n.displayName=`${e}.Slot`,n}var g=Symbol("radix.slottable");function w(e){return o.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===g}var b=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let n=y(`Primitive.${t}`),r=o.forwardRef((e,r)=>{let{asChild:o,...l}=e,i=o?n:t;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,c.jsx)(i,{...l,ref:r})});return r.displayName=`Primitive.${t}`,{...e,[t]:r}},{});function x(e){let t=o.useRef(e);return o.useEffect(()=>{t.current=e}),o.useMemo(()=>(...e)=>t.current?.(...e),[])}var E="dismissableLayer.update",C=o.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),S=o.forwardRef((e,t)=>{var n,l;let{disableOutsidePointerEvents:a=!1,onEscapeKeyDown:u,onPointerDownOutside:d,onFocusOutside:f,onInteractOutside:p,onDismiss:v,...m}=e,h=o.useContext(C),[y,g]=o.useState(null),w=null!==(l=null==y?void 0:y.ownerDocument)&&void 0!==l?l:null===(n=globalThis)||void 0===n?void 0:n.document,[,S]=o.useState({}),j=s(t,e=>g(e)),P=Array.from(h.layers),[D]=[...h.layersWithOutsidePointerEventsDisabled].slice(-1),A=P.indexOf(D),k=y?P.indexOf(y):-1,T=h.layersWithOutsidePointerEventsDisabled.size>0,O=k>=A,M=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null===(t=globalThis)||void 0===t?void 0:t.document,r=x(e),l=o.useRef(!1),i=o.useRef(()=>{});return o.useEffect(()=>{let e=e=>{if(e.target&&!l.current){let t=function(){N("dismissableLayer.pointerDownOutside",r,o,{discrete:!0})},o={originalEvent:e};"touch"===e.pointerType?(n.removeEventListener("click",i.current),i.current=t,n.addEventListener("click",i.current,{once:!0})):t()}else n.removeEventListener("click",i.current);l.current=!1},t=window.setTimeout(()=>{n.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(t),n.removeEventListener("pointerdown",e),n.removeEventListener("click",i.current)}},[n,r]),{onPointerDownCapture:()=>l.current=!0}}(e=>{let t=e.target,n=[...h.branches].some(e=>e.contains(t));!O||n||(null==d||d(e),null==p||p(e),e.defaultPrevented||null==v||v())},w),L=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null===(t=globalThis)||void 0===t?void 0:t.document,r=x(e),l=o.useRef(!1);return o.useEffect(()=>{let e=e=>{e.target&&!l.current&&N("dismissableLayer.focusOutside",r,{originalEvent:e},{discrete:!1})};return n.addEventListener("focusin",e),()=>n.removeEventListener("focusin",e)},[n,r]),{onFocusCapture:()=>l.current=!0,onBlurCapture:()=>l.current=!1}}(e=>{let t=e.target;[...h.branches].some(e=>e.contains(t))||(null==f||f(e),null==p||p(e),e.defaultPrevented||null==v||v())},w);return!function(e,t=globalThis?.document){let n=x(e);o.useEffect(()=>{let e=e=>{"Escape"===e.key&&n(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[n,t])}(e=>{k!==h.layers.size-1||(null==u||u(e),!e.defaultPrevented&&v&&(e.preventDefault(),v()))},w),o.useEffect(()=>{if(y)return a&&(0===h.layersWithOutsidePointerEventsDisabled.size&&(r=w.body.style.pointerEvents,w.body.style.pointerEvents="none"),h.layersWithOutsidePointerEventsDisabled.add(y)),h.layers.add(y),R(),()=>{a&&1===h.layersWithOutsidePointerEventsDisabled.size&&(w.body.style.pointerEvents=r)}},[y,w,a,h]),o.useEffect(()=>()=>{y&&(h.layers.delete(y),h.layersWithOutsidePointerEventsDisabled.delete(y),R())},[y,h]),o.useEffect(()=>{let e=()=>S({});return document.addEventListener(E,e),()=>document.removeEventListener(E,e)},[]),(0,c.jsx)(b.div,{...m,ref:j,style:{pointerEvents:T?O?"auto":"none":void 0,...e.style},onFocusCapture:i(e.onFocusCapture,L.onFocusCapture),onBlurCapture:i(e.onBlurCapture,L.onBlurCapture),onPointerDownCapture:i(e.onPointerDownCapture,M.onPointerDownCapture)})});function R(){let e=new CustomEvent(E);document.dispatchEvent(e)}function N(e,t,n,r){let{discrete:o}=r,l=n.originalEvent.target,i=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});(t&&l.addEventListener(e,t,{once:!0}),o)?l&&h.flushSync(()=>l.dispatchEvent(i)):l.dispatchEvent(i)}S.displayName="DismissableLayer",o.forwardRef((e,t)=>{let n=o.useContext(C),r=o.useRef(null),l=s(t,r);return o.useEffect(()=>{let e=r.current;if(e)return n.branches.add(e),()=>{n.branches.delete(e)}},[n.branches]),(0,c.jsx)(b.div,{...e,ref:l})}).displayName="DismissableLayerBranch";var j=n(9780),P=o.forwardRef((e,t)=>{var n,r;let{container:l,...i}=e,[a,u]=o.useState(!1);d(()=>u(!0),[]);let s=l||a&&(null===(r=globalThis)||void 0===r?void 0:null===(n=r.document)||void 0===n?void 0:n.body);return s?h.createPortal((0,c.jsx)(b.div,{...i,ref:t}),s):null});P.displayName="Portal";var D=e=>{let{present:t,children:n}=e,r=function(e){var t,n;let[r,l]=o.useState(),i=o.useRef(null),a=o.useRef(e),u=o.useRef("none"),[s,c]=(t=e?"mounted":"unmounted",n={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},o.useReducer((e,t)=>{let r=n[e][t];return null!=r?r:e},t));return o.useEffect(()=>{let e=A(i.current);u.current="mounted"===s?e:"none"},[s]),d(()=>{let t=i.current,n=a.current;if(n!==e){let r=u.current,o=A(t);e?c("MOUNT"):"none"===o||(null==t?void 0:t.display)==="none"?c("UNMOUNT"):n&&r!==o?c("ANIMATION_OUT"):c("UNMOUNT"),a.current=e}},[e,c]),d(()=>{if(r){var e;let t;let n=null!==(e=r.ownerDocument.defaultView)&&void 0!==e?e:window,o=e=>{let o=A(i.current).includes(e.animationName);if(e.target===r&&o&&(c("ANIMATION_END"),!a.current)){let e=r.style.animationFillMode;r.style.animationFillMode="forwards",t=n.setTimeout(()=>{"forwards"===r.style.animationFillMode&&(r.style.animationFillMode=e)})}},l=e=>{e.target===r&&(u.current=A(i.current))};return r.addEventListener("animationstart",l),r.addEventListener("animationcancel",o),r.addEventListener("animationend",o),()=>{n.clearTimeout(t),r.removeEventListener("animationstart",l),r.removeEventListener("animationcancel",o),r.removeEventListener("animationend",o)}}c("ANIMATION_END")},[r,c]),{isPresent:["mounted","unmountSuspended"].includes(s),ref:o.useCallback(e=>{i.current=e?getComputedStyle(e):null,l(e)},[])}}(t),l="function"==typeof n?n({present:r.isPresent}):o.Children.only(n),i=s(r.ref,function(e){var t,n;let r=null===(t=Object.getOwnPropertyDescriptor(e.props,"ref"))||void 0===t?void 0:t.get,o=r&&"isReactWarning"in r&&r.isReactWarning;return o?e.ref:(o=(r=null===(n=Object.getOwnPropertyDescriptor(e,"ref"))||void 0===n?void 0:n.get)&&"isReactWarning"in r&&r.isReactWarning)?e.props.ref:e.props.ref||e.ref}(l));return"function"==typeof n||r.isPresent?o.cloneElement(l,{ref:i}):null};function A(e){return(null==e?void 0:e.animationName)||"none"}D.displayName="Presence";var k=n(2292),T=n(4065),O=n(5587),M="Dialog",[L,I]=function(e,t=[]){let n=[],r=()=>{let t=n.map(e=>o.createContext(e));return function(n){let r=n?.[e]||t;return o.useMemo(()=>({[`__scope${e}`]:{...n,[e]:r}}),[n,r])}};return r.scopeName=e,[function(t,r){let l=o.createContext(r),i=n.length;n=[...n,r];let a=t=>{let{scope:n,children:r,...a}=t,u=n?.[e]?.[i]||l,s=o.useMemo(()=>a,Object.values(a));return(0,c.jsx)(u.Provider,{value:s,children:r})};return a.displayName=t+"Provider",[a,function(n,a){let u=a?.[e]?.[i]||l,s=o.useContext(u);if(s)return s;if(void 0!==r)return r;throw Error(`\`${n}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let n=()=>{let n=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let r=n.reduce((t,{useScope:n,scopeName:r})=>{let o=n(e)[`__scope${r}`];return{...t,...o}},{});return o.useMemo(()=>({[`__scope${t.scopeName}`]:r}),[r])}};return n.scopeName=t.scopeName,n}(r,...t)]}(M),[_,W]=L(M),F=e=>{let{__scopeDialog:t,children:n,open:r,defaultOpen:l,onOpenChange:i,modal:a=!0}=e,u=o.useRef(null),s=o.useRef(null),[d,f]=function({prop:e,defaultProp:t,onChange:n=()=>{},caller:r}){let[l,i,a]=function({defaultProp:e,onChange:t}){let[n,r]=o.useState(e),l=o.useRef(n),i=o.useRef(t);return m(()=>{i.current=t},[t]),o.useEffect(()=>{l.current!==n&&(i.current?.(n),l.current=n)},[n,l]),[n,r,i]}({defaultProp:t,onChange:n}),u=void 0!==e,s=u?e:l;{let t=o.useRef(void 0!==e);o.useEffect(()=>{let e=t.current;if(e!==u){let t=u?"controlled":"uncontrolled";console.warn(`${r} is changing from ${e?"controlled":"uncontrolled"} to ${t}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}t.current=u},[u,r])}return[s,o.useCallback(t=>{if(u){let n="function"==typeof t?t(e):t;n!==e&&a.current?.(n)}else i(t)},[u,e,i,a])]}({prop:r,defaultProp:null!=l&&l,onChange:i,caller:M});return(0,c.jsx)(_,{scope:t,triggerRef:u,contentRef:s,contentId:v(),titleId:v(),descriptionId:v(),open:d,onOpenChange:f,onOpenToggle:o.useCallback(()=>f(e=>!e),[f]),modal:a,children:n})};F.displayName=M;var B="DialogTrigger",H=o.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=W(B,n),l=s(t,o.triggerRef);return(0,c.jsx)(b.button,{type:"button","aria-haspopup":"dialog","aria-expanded":o.open,"aria-controls":o.contentId,"data-state":ea(o.open),...r,ref:l,onClick:i(e.onClick,o.onOpenToggle)})});H.displayName=B;var V="DialogPortal",[$,U]=L(V,{forceMount:void 0}),z=e=>{let{__scopeDialog:t,forceMount:n,children:r,container:l}=e,i=W(V,t);return(0,c.jsx)($,{scope:t,forceMount:n,children:o.Children.map(r,e=>(0,c.jsx)(D,{present:n||i.open,children:(0,c.jsx)(P,{asChild:!0,container:l,children:e})}))})};z.displayName=V;var K="DialogOverlay",Y=o.forwardRef((e,t)=>{let n=U(K,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,l=W(K,e.__scopeDialog);return l.modal?(0,c.jsx)(D,{present:r||l.open,children:(0,c.jsx)(X,{...o,ref:t})}):null});Y.displayName=K;var q=y("DialogOverlay.RemoveScroll"),X=o.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=W(K,n);return(0,c.jsx)(T.A,{as:q,allowPinchZoom:!0,shards:[o.contentRef],children:(0,c.jsx)(b.div,{"data-state":ea(o.open),...r,ref:t,style:{pointerEvents:"auto",...r.style}})})}),Z="DialogContent",G=o.forwardRef((e,t)=>{let n=U(Z,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,l=W(Z,e.__scopeDialog);return(0,c.jsx)(D,{present:r||l.open,children:l.modal?(0,c.jsx)(J,{...o,ref:t}):(0,c.jsx)(Q,{...o,ref:t})})});G.displayName=Z;var J=o.forwardRef((e,t)=>{let n=W(Z,e.__scopeDialog),r=o.useRef(null),l=s(t,n.contentRef,r);return o.useEffect(()=>{let e=r.current;if(e)return(0,O.Eq)(e)},[]),(0,c.jsx)(ee,{...e,ref:l,trapFocus:n.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:i(e.onCloseAutoFocus,e=>{var t;e.preventDefault(),null===(t=n.triggerRef.current)||void 0===t||t.focus()}),onPointerDownOutside:i(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,n=0===t.button&&!0===t.ctrlKey;(2===t.button||n)&&e.preventDefault()}),onFocusOutside:i(e.onFocusOutside,e=>e.preventDefault())})}),Q=o.forwardRef((e,t)=>{let n=W(Z,e.__scopeDialog),r=o.useRef(!1),l=o.useRef(!1);return(0,c.jsx)(ee,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{var o,i;null===(o=e.onCloseAutoFocus)||void 0===o||o.call(e,t),t.defaultPrevented||(r.current||null===(i=n.triggerRef.current)||void 0===i||i.focus(),t.preventDefault()),r.current=!1,l.current=!1},onInteractOutside:t=>{var o,i;null===(o=e.onInteractOutside)||void 0===o||o.call(e,t),t.defaultPrevented||(r.current=!0,"pointerdown"!==t.detail.originalEvent.type||(l.current=!0));let a=t.target;(null===(i=n.triggerRef.current)||void 0===i?void 0:i.contains(a))&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&l.current&&t.preventDefault()}})}),ee=o.forwardRef((e,t)=>{let{__scopeDialog:n,trapFocus:r,onOpenAutoFocus:l,onCloseAutoFocus:i,...a}=e,u=W(Z,n),d=o.useRef(null),f=s(t,d);return(0,k.Oh)(),(0,c.jsxs)(c.Fragment,{children:[(0,c.jsx)(j.n,{asChild:!0,loop:!0,trapped:r,onMountAutoFocus:l,onUnmountAutoFocus:i,children:(0,c.jsx)(S,{role:"dialog",id:u.contentId,"aria-describedby":u.descriptionId,"aria-labelledby":u.titleId,"data-state":ea(u.open),...a,ref:f,onDismiss:()=>u.onOpenChange(!1)})}),(0,c.jsxs)(c.Fragment,{children:[(0,c.jsx)(ed,{titleId:u.titleId}),(0,c.jsx)(ef,{contentRef:d,descriptionId:u.descriptionId})]})]})}),et="DialogTitle",en=o.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=W(et,n);return(0,c.jsx)(b.h2,{id:o.titleId,...r,ref:t})});en.displayName=et;var er="DialogDescription",eo=o.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=W(er,n);return(0,c.jsx)(b.p,{id:o.descriptionId,...r,ref:t})});eo.displayName=er;var el="DialogClose",ei=o.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=W(el,n);return(0,c.jsx)(b.button,{type:"button",...r,ref:t,onClick:i(e.onClick,()=>o.onOpenChange(!1))})});function ea(e){return e?"open":"closed"}ei.displayName=el;var eu="DialogTitleWarning",[es,ec]=function(e,t){let n=o.createContext(t),r=e=>{let{children:t,...r}=e,l=o.useMemo(()=>r,Object.values(r));return(0,c.jsx)(n.Provider,{value:l,children:t})};return r.displayName=e+"Provider",[r,function(r){let l=o.useContext(n);if(l)return l;if(void 0!==t)return t;throw Error(`\`${r}\` must be used within \`${e}\``)}]}(eu,{contentName:Z,titleName:et,docsSlug:"dialog"}),ed=e=>{let{titleId:t}=e,n=ec(eu),r="`".concat(n.contentName,"` requires a `").concat(n.titleName,"` for the component to be accessible for screen reader users.\n\nIf you want to hide the `").concat(n.titleName,"`, you can wrap it with our VisuallyHidden component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/").concat(n.docsSlug);return o.useEffect(()=>{t&&!document.getElementById(t)&&console.error(r)},[r,t]),null},ef=e=>{let{contentRef:t,descriptionId:n}=e,r=ec("DialogDescriptionWarning"),l="Warning: Missing `Description` or `aria-describedby={undefined}` for {".concat(r.contentName,"}.");return o.useEffect(()=>{var e;let r=null===(e=t.current)||void 0===e?void 0:e.getAttribute("aria-describedby");n&&r&&!document.getElementById(n)&&console.warn(l)},[l,t,n]),null},ep=F,ev=H,em=z,eh=Y,ey=G,eg=en,ew=eo,eb=ei},2292:(e,t,n)=>{n.d(t,{Oh:()=>l});var r=n(2115),o=0;function l(){r.useEffect(()=>{var e,t;let n=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",null!==(e=n[0])&&void 0!==e?e:i()),document.body.insertAdjacentElement("beforeend",null!==(t=n[1])&&void 0!==t?t:i()),o++,()=>{1===o&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),o--}},[])}function i(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}},9780:(e,t,n)=>{n.d(t,{n:()=>v});var r=n(2115);function o(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function l(...e){return t=>{let n=!1,r=e.map(e=>{let r=o(e,t);return n||"function"!=typeof r||(n=!0),r});if(n)return()=>{for(let t=0;t<r.length;t++){let n=r[t];"function"==typeof n?n():o(e[t],null)}}}}n(7650);var i=n(5155),a=Symbol("radix.slottable");function u(e){return r.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===a}var s=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let n=function(e){let t=function(e){let t=r.forwardRef((e,t)=>{let{children:n,...o}=e;if(r.isValidElement(n)){let e,i;let a=(e=Object.getOwnPropertyDescriptor(n.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?n.ref:(e=Object.getOwnPropertyDescriptor(n,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?n.props.ref:n.props.ref||n.ref,u=function(e,t){let n={...t};for(let r in t){let o=e[r],l=t[r];/^on[A-Z]/.test(r)?o&&l?n[r]=(...e)=>{let t=l(...e);return o(...e),t}:o&&(n[r]=o):"style"===r?n[r]={...o,...l}:"className"===r&&(n[r]=[o,l].filter(Boolean).join(" "))}return{...e,...n}}(o,n.props);return n.type!==r.Fragment&&(u.ref=t?l(t,a):a),r.cloneElement(n,u)}return r.Children.count(n)>1?r.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),n=r.forwardRef((e,n)=>{let{children:o,...l}=e,a=r.Children.toArray(o),s=a.find(u);if(s){let e=s.props.children,o=a.map(t=>t!==s?t:r.Children.count(e)>1?r.Children.only(null):r.isValidElement(e)?e.props.children:null);return(0,i.jsx)(t,{...l,ref:n,children:r.isValidElement(e)?r.cloneElement(e,void 0,o):null})}return(0,i.jsx)(t,{...l,ref:n,children:o})});return n.displayName=`${e}.Slot`,n}(`Primitive.${t}`),o=r.forwardRef((e,r)=>{let{asChild:o,...l}=e,a=o?n:t;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,i.jsx)(a,{...l,ref:r})});return o.displayName=`Primitive.${t}`,{...e,[t]:o}},{});function c(e){let t=r.useRef(e);return r.useEffect(()=>{t.current=e}),r.useMemo(()=>(...e)=>t.current?.(...e),[])}var d="focusScope.autoFocusOnMount",f="focusScope.autoFocusOnUnmount",p={bubbles:!1,cancelable:!0},v=r.forwardRef((e,t)=>{let{loop:n=!1,trapped:o=!1,onMountAutoFocus:a,onUnmountAutoFocus:u,...v}=e,[w,b]=r.useState(null),x=c(a),E=c(u),C=r.useRef(null),S=function(...e){return r.useCallback(l(...e),e)}(t,e=>b(e)),R=r.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;r.useEffect(()=>{if(o){let e=function(e){if(R.paused||!w)return;let t=e.target;w.contains(t)?C.current=t:y(C.current,{select:!0})},t=function(e){if(R.paused||!w)return;let t=e.relatedTarget;null===t||w.contains(t)||y(C.current,{select:!0})};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let n=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&y(w)});return w&&n.observe(w,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),n.disconnect()}}},[o,w,R.paused]),r.useEffect(()=>{if(w){g.add(R);let e=document.activeElement;if(!w.contains(e)){let t=new CustomEvent(d,p);w.addEventListener(d,x),w.dispatchEvent(t),t.defaultPrevented||(function(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=document.activeElement;for(let r of e)if(y(r,{select:t}),document.activeElement!==n)return}(m(w).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&y(w))}return()=>{w.removeEventListener(d,x),setTimeout(()=>{let t=new CustomEvent(f,p);w.addEventListener(f,E),w.dispatchEvent(t),t.defaultPrevented||y(null!=e?e:document.body,{select:!0}),w.removeEventListener(f,E),g.remove(R)},0)}}},[w,x,E,R]);let N=r.useCallback(e=>{if(!n&&!o||R.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,r=document.activeElement;if(t&&r){let t=e.currentTarget,[o,l]=function(e){let t=m(e);return[h(t,e),h(t.reverse(),e)]}(t);o&&l?e.shiftKey||r!==l?e.shiftKey&&r===o&&(e.preventDefault(),n&&y(l,{select:!0})):(e.preventDefault(),n&&y(o,{select:!0})):r===t&&e.preventDefault()}},[n,o,R.paused]);return(0,i.jsx)(s.div,{tabIndex:-1,...v,ref:S,onKeyDown:N})});function m(e){let t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function h(e,t){for(let n of e)if(!function(e,t){let{upTo:n}=t;if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===n||e!==n);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(n,{upTo:t}))return n}function y(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(e&&e.focus){var n;let r=document.activeElement;e.focus({preventScroll:!0}),e!==r&&(n=e)instanceof HTMLInputElement&&"select"in n&&t&&e.select()}}v.displayName="FocusScope";var g=function(){let e=[];return{add(t){let n=e[0];t!==n&&(null==n||n.pause()),(e=w(e,t)).unshift(t)},remove(t){var n;null===(n=(e=w(e,t))[0])||void 0===n||n.resume()}}}();function w(e,t){let n=[...e],r=n.indexOf(t);return -1!==r&&n.splice(r,1),n}},6195:(e,t,n)=>{n.d(t,{b:()=>a});var r=n(2115),o=n(3360),l=n(5155),i=r.forwardRef((e,t)=>(0,l.jsx)(o.sG.label,{...e,ref:t,onMouseDown:t=>{var n;t.target.closest("button, input, select, textarea")||(null===(n=e.onMouseDown)||void 0===n||n.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));i.displayName="Label";var a=i},3123:(e,t,n)=>{n.d(t,{UC:()=>e3,In:()=>e4,q7:()=>te,VF:()=>tn,p4:()=>tt,ZL:()=>e6,bL:()=>e5,l9:()=>e7,WT:()=>e9,LM:()=>e8});var r,o,l=n(2115),i=n.t(l,2),a=n(7650);function u(e,[t,n]){return Math.min(n,Math.max(t,e))}function s(e,t,{checkForDefaultPrevented:n=!0}={}){return function(r){if(e?.(r),!1===n||!r.defaultPrevented)return t?.(r)}}n(5359),n(587),n(5465);var c=n(5155);function d(e,t=[]){let n=[],r=()=>{let t=n.map(e=>l.createContext(e));return function(n){let r=n?.[e]||t;return l.useMemo(()=>({[`__scope${e}`]:{...n,[e]:r}}),[n,r])}};return r.scopeName=e,[function(t,r){let o=l.createContext(r),i=n.length;n=[...n,r];let a=t=>{let{scope:n,children:r,...a}=t,u=n?.[e]?.[i]||o,s=l.useMemo(()=>a,Object.values(a));return(0,c.jsx)(u.Provider,{value:s,children:r})};return a.displayName=t+"Provider",[a,function(n,a){let u=a?.[e]?.[i]||o,s=l.useContext(u);if(s)return s;if(void 0!==r)return r;throw Error(`\`${n}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let n=()=>{let n=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let r=n.reduce((t,{useScope:n,scopeName:r})=>{let o=n(e)[`__scope${r}`];return{...t,...o}},{});return l.useMemo(()=>({[`__scope${t.scopeName}`]:r}),[r])}};return n.scopeName=t.scopeName,n}(r,...t)]}function f(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function p(...e){return t=>{let n=!1,r=e.map(e=>{let r=f(e,t);return n||"function"!=typeof r||(n=!0),r});if(n)return()=>{for(let t=0;t<r.length;t++){let n=r[t];"function"==typeof n?n():f(e[t],null)}}}}function v(...e){return l.useCallback(p(...e),e)}function m(e){let t=function(e){let t=l.forwardRef((e,t)=>{let{children:n,...r}=e;if(l.isValidElement(n)){let e,o;let i=(e=Object.getOwnPropertyDescriptor(n.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?n.ref:(e=Object.getOwnPropertyDescriptor(n,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?n.props.ref:n.props.ref||n.ref,a=function(e,t){let n={...t};for(let r in t){let o=e[r],l=t[r];/^on[A-Z]/.test(r)?o&&l?n[r]=(...e)=>{let t=l(...e);return o(...e),t}:o&&(n[r]=o):"style"===r?n[r]={...o,...l}:"className"===r&&(n[r]=[o,l].filter(Boolean).join(" "))}return{...e,...n}}(r,n.props);return n.type!==l.Fragment&&(a.ref=t?p(t,i):i),l.cloneElement(n,a)}return l.Children.count(n)>1?l.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),n=l.forwardRef((e,n)=>{let{children:r,...o}=e,i=l.Children.toArray(r),a=i.find(y);if(a){let e=a.props.children,r=i.map(t=>t!==a?t:l.Children.count(e)>1?l.Children.only(null):l.isValidElement(e)?e.props.children:null);return(0,c.jsx)(t,{...o,ref:n,children:l.isValidElement(e)?l.cloneElement(e,void 0,r):null})}return(0,c.jsx)(t,{...o,ref:n,children:r})});return n.displayName=`${e}.Slot`,n}var h=Symbol("radix.slottable");function y(e){return l.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===h}var g=l.createContext(void 0),w=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let n=m(`Primitive.${t}`),r=l.forwardRef((e,r)=>{let{asChild:o,...l}=e,i=o?n:t;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,c.jsx)(i,{...l,ref:r})});return r.displayName=`Primitive.${t}`,{...e,[t]:r}},{});function b(e){let t=l.useRef(e);return l.useEffect(()=>{t.current=e}),l.useMemo(()=>(...e)=>t.current?.(...e),[])}var x="dismissableLayer.update",E=l.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),C=l.forwardRef((e,t)=>{var n,r;let{disableOutsidePointerEvents:i=!1,onEscapeKeyDown:a,onPointerDownOutside:u,onFocusOutside:d,onInteractOutside:f,onDismiss:p,...m}=e,h=l.useContext(E),[y,g]=l.useState(null),C=null!==(r=null==y?void 0:y.ownerDocument)&&void 0!==r?r:null===(n=globalThis)||void 0===n?void 0:n.document,[,N]=l.useState({}),j=v(t,e=>g(e)),P=Array.from(h.layers),[D]=[...h.layersWithOutsidePointerEventsDisabled].slice(-1),A=P.indexOf(D),k=y?P.indexOf(y):-1,T=h.layersWithOutsidePointerEventsDisabled.size>0,O=k>=A,M=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null===(t=globalThis)||void 0===t?void 0:t.document,r=b(e),o=l.useRef(!1),i=l.useRef(()=>{});return l.useEffect(()=>{let e=e=>{if(e.target&&!o.current){let t=function(){R("dismissableLayer.pointerDownOutside",r,o,{discrete:!0})},o={originalEvent:e};"touch"===e.pointerType?(n.removeEventListener("click",i.current),i.current=t,n.addEventListener("click",i.current,{once:!0})):t()}else n.removeEventListener("click",i.current);o.current=!1},t=window.setTimeout(()=>{n.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(t),n.removeEventListener("pointerdown",e),n.removeEventListener("click",i.current)}},[n,r]),{onPointerDownCapture:()=>o.current=!0}}(e=>{let t=e.target,n=[...h.branches].some(e=>e.contains(t));!O||n||(null==u||u(e),null==f||f(e),e.defaultPrevented||null==p||p())},C),L=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null===(t=globalThis)||void 0===t?void 0:t.document,r=b(e),o=l.useRef(!1);return l.useEffect(()=>{let e=e=>{e.target&&!o.current&&R("dismissableLayer.focusOutside",r,{originalEvent:e},{discrete:!1})};return n.addEventListener("focusin",e),()=>n.removeEventListener("focusin",e)},[n,r]),{onFocusCapture:()=>o.current=!0,onBlurCapture:()=>o.current=!1}}(e=>{let t=e.target;[...h.branches].some(e=>e.contains(t))||(null==d||d(e),null==f||f(e),e.defaultPrevented||null==p||p())},C);return!function(e,t=globalThis?.document){let n=b(e);l.useEffect(()=>{let e=e=>{"Escape"===e.key&&n(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[n,t])}(e=>{k!==h.layers.size-1||(null==a||a(e),!e.defaultPrevented&&p&&(e.preventDefault(),p()))},C),l.useEffect(()=>{if(y)return i&&(0===h.layersWithOutsidePointerEventsDisabled.size&&(o=C.body.style.pointerEvents,C.body.style.pointerEvents="none"),h.layersWithOutsidePointerEventsDisabled.add(y)),h.layers.add(y),S(),()=>{i&&1===h.layersWithOutsidePointerEventsDisabled.size&&(C.body.style.pointerEvents=o)}},[y,C,i,h]),l.useEffect(()=>()=>{y&&(h.layers.delete(y),h.layersWithOutsidePointerEventsDisabled.delete(y),S())},[y,h]),l.useEffect(()=>{let e=()=>N({});return document.addEventListener(x,e),()=>document.removeEventListener(x,e)},[]),(0,c.jsx)(w.div,{...m,ref:j,style:{pointerEvents:T?O?"auto":"none":void 0,...e.style},onFocusCapture:s(e.onFocusCapture,L.onFocusCapture),onBlurCapture:s(e.onBlurCapture,L.onBlurCapture),onPointerDownCapture:s(e.onPointerDownCapture,M.onPointerDownCapture)})});function S(){let e=new CustomEvent(x);document.dispatchEvent(e)}function R(e,t,n,r){let{discrete:o}=r,l=n.originalEvent.target,i=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});(t&&l.addEventListener(e,t,{once:!0}),o)?l&&a.flushSync(()=>l.dispatchEvent(i)):l.dispatchEvent(i)}C.displayName="DismissableLayer",l.forwardRef((e,t)=>{let n=l.useContext(E),r=l.useRef(null),o=v(t,r);return l.useEffect(()=>{let e=r.current;if(e)return n.branches.add(e),()=>{n.branches.delete(e)}},[n.branches]),(0,c.jsx)(w.div,{...e,ref:o})}).displayName="DismissableLayerBranch";var N=n(2292),j=n(9780),P=globalThis?.document?l.useLayoutEffect:()=>{},D=i[" useId ".trim().toString()]||(()=>void 0),A=0;function k(e){let[t,n]=l.useState(D());return P(()=>{e||n(e=>e??String(A++))},[e]),e||(t?`radix-${t}`:"")}var T=n(6932),O=n(7205),M=l.forwardRef((e,t)=>{let{children:n,width:r=10,height:o=5,...l}=e;return(0,c.jsx)(w.svg,{...l,ref:t,width:r,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:(0,c.jsx)("polygon",{points:"0,0 30,0 15,10"})})});M.displayName="Arrow";var L="Popper",[I,_]=d(L),[W,F]=I(L),B=e=>{let{__scopePopper:t,children:n}=e,[r,o]=l.useState(null);return(0,c.jsx)(W,{scope:t,anchor:r,onAnchorChange:o,children:n})};B.displayName=L;var H="PopperAnchor",V=l.forwardRef((e,t)=>{let{__scopePopper:n,virtualRef:r,...o}=e,i=F(H,n),a=l.useRef(null),u=v(t,a);return l.useEffect(()=>{i.onAnchorChange((null==r?void 0:r.current)||a.current)}),r?null:(0,c.jsx)(w.div,{...o,ref:u})});V.displayName=H;var $="PopperContent",[U,z]=I($),K=l.forwardRef((e,t)=>{var n,r,o,i,a,u,s,d;let{__scopePopper:f,side:p="bottom",sideOffset:m=0,align:h="center",alignOffset:y=0,arrowPadding:g=0,avoidCollisions:x=!0,collisionBoundary:E=[],collisionPadding:C=0,sticky:S="partial",hideWhenDetached:R=!1,updatePositionStrategy:N="optimized",onPlaced:j,...D}=e,A=F($,f),[k,M]=l.useState(null),L=v(t,e=>M(e)),[I,_]=l.useState(null),W=function(e){let[t,n]=l.useState(void 0);return P(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let r,o;if(!Array.isArray(t)||!t.length)return;let l=t[0];if("borderBoxSize"in l){let e=l.borderBoxSize,t=Array.isArray(e)?e[0]:e;r=t.inlineSize,o=t.blockSize}else r=e.offsetWidth,o=e.offsetHeight;n({width:r,height:o})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}n(void 0)},[e]),t}(I),B=null!==(s=null==W?void 0:W.width)&&void 0!==s?s:0,H=null!==(d=null==W?void 0:W.height)&&void 0!==d?d:0,V="number"==typeof C?C:{top:0,right:0,bottom:0,left:0,...C},z=Array.isArray(E)?E:[E],K=z.length>0,Y={padding:V,boundary:z.filter(Z),altBoundary:K},{refs:q,floatingStyles:X,placement:Q,isPositioned:ee,middlewareData:et}=(0,T.we)({strategy:"fixed",placement:p+("center"!==h?"-"+h:""),whileElementsMounted:function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return(0,O.ll)(...t,{animationFrame:"always"===N})},elements:{reference:A.anchor},middleware:[(0,T.cY)({mainAxis:m+H,alignmentAxis:y}),x&&(0,T.BN)({mainAxis:!0,crossAxis:!1,limiter:"partial"===S?(0,T.ER)():void 0,...Y}),x&&(0,T.UU)({...Y}),(0,T.Ej)({...Y,apply:e=>{let{elements:t,rects:n,availableWidth:r,availableHeight:o}=e,{width:l,height:i}=n.reference,a=t.floating.style;a.setProperty("--radix-popper-available-width","".concat(r,"px")),a.setProperty("--radix-popper-available-height","".concat(o,"px")),a.setProperty("--radix-popper-anchor-width","".concat(l,"px")),a.setProperty("--radix-popper-anchor-height","".concat(i,"px"))}}),I&&(0,T.UE)({element:I,padding:g}),G({arrowWidth:B,arrowHeight:H}),R&&(0,T.jD)({strategy:"referenceHidden",...Y})]}),[en,er]=J(Q),eo=b(j);P(()=>{ee&&(null==eo||eo())},[ee,eo]);let el=null===(n=et.arrow)||void 0===n?void 0:n.x,ei=null===(r=et.arrow)||void 0===r?void 0:r.y,ea=(null===(o=et.arrow)||void 0===o?void 0:o.centerOffset)!==0,[eu,es]=l.useState();return P(()=>{k&&es(window.getComputedStyle(k).zIndex)},[k]),(0,c.jsx)("div",{ref:q.setFloating,"data-radix-popper-content-wrapper":"",style:{...X,transform:ee?X.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:eu,"--radix-popper-transform-origin":[null===(i=et.transformOrigin)||void 0===i?void 0:i.x,null===(a=et.transformOrigin)||void 0===a?void 0:a.y].join(" "),...(null===(u=et.hide)||void 0===u?void 0:u.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,c.jsx)(U,{scope:f,placedSide:en,onArrowChange:_,arrowX:el,arrowY:ei,shouldHideArrow:ea,children:(0,c.jsx)(w.div,{"data-side":en,"data-align":er,...D,ref:L,style:{...D.style,animation:ee?void 0:"none"}})})})});K.displayName=$;var Y="PopperArrow",q={top:"bottom",right:"left",bottom:"top",left:"right"},X=l.forwardRef(function(e,t){let{__scopePopper:n,...r}=e,o=z(Y,n),l=q[o.placedSide];return(0,c.jsx)("span",{ref:o.onArrowChange,style:{position:"absolute",left:o.arrowX,top:o.arrowY,[l]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[o.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[o.placedSide],visibility:o.shouldHideArrow?"hidden":void 0},children:(0,c.jsx)(M,{...r,ref:t,style:{...r.style,display:"block"}})})});function Z(e){return null!==e}X.displayName=Y;var G=e=>({name:"transformOrigin",options:e,fn(t){var n,r,o,l,i;let{placement:a,rects:u,middlewareData:s}=t,c=(null===(n=s.arrow)||void 0===n?void 0:n.centerOffset)!==0,d=c?0:e.arrowWidth,f=c?0:e.arrowHeight,[p,v]=J(a),m={start:"0%",center:"50%",end:"100%"}[v],h=(null!==(l=null===(r=s.arrow)||void 0===r?void 0:r.x)&&void 0!==l?l:0)+d/2,y=(null!==(i=null===(o=s.arrow)||void 0===o?void 0:o.y)&&void 0!==i?i:0)+f/2,g="",w="";return"bottom"===p?(g=c?m:"".concat(h,"px"),w="".concat(-f,"px")):"top"===p?(g=c?m:"".concat(h,"px"),w="".concat(u.floating.height+f,"px")):"right"===p?(g="".concat(-f,"px"),w=c?m:"".concat(y,"px")):"left"===p&&(g="".concat(u.floating.width+f,"px"),w=c?m:"".concat(y,"px")),{data:{x:g,y:w}}}});function J(e){let[t,n="center"]=e.split("-");return[t,n]}var Q=l.forwardRef((e,t)=>{var n,r;let{container:o,...i}=e,[u,s]=l.useState(!1);P(()=>s(!0),[]);let d=o||u&&(null===(r=globalThis)||void 0===r?void 0:null===(n=r.document)||void 0===n?void 0:n.body);return d?a.createPortal((0,c.jsx)(w.div,{...i,ref:t}),d):null});Q.displayName="Portal";var ee=i[" useInsertionEffect ".trim().toString()]||P;function et({prop:e,defaultProp:t,onChange:n=()=>{},caller:r}){let[o,i,a]=function({defaultProp:e,onChange:t}){let[n,r]=l.useState(e),o=l.useRef(n),i=l.useRef(t);return ee(()=>{i.current=t},[t]),l.useEffect(()=>{o.current!==n&&(i.current?.(n),o.current=n)},[n,o]),[n,r,i]}({defaultProp:t,onChange:n}),u=void 0!==e,s=u?e:o;{let t=l.useRef(void 0!==e);l.useEffect(()=>{let e=t.current;if(e!==u){let t=u?"controlled":"uncontrolled";console.warn(`${r} is changing from ${e?"controlled":"uncontrolled"} to ${t}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}t.current=u},[u,r])}return[s,l.useCallback(t=>{if(u){let n="function"==typeof t?t(e):t;n!==e&&a.current?.(n)}else i(t)},[u,e,i,a])]}Symbol("RADIX:SYNC_STATE");var en=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"});l.forwardRef((e,t)=>(0,c.jsx)(w.span,{...e,ref:t,style:{...en,...e.style}})).displayName="VisuallyHidden";var er=n(5587),eo=n(4065),el=[" ","Enter","ArrowUp","ArrowDown"],ei=[" ","Enter"],ea="Select",[eu,es,ec]=function(e){let t=e+"CollectionProvider",[n,r]=d(t),[o,i]=n(t,{collectionRef:{current:null},itemMap:new Map}),a=e=>{let{scope:t,children:n}=e,r=l.useRef(null),i=l.useRef(new Map).current;return(0,c.jsx)(o,{scope:t,itemMap:i,collectionRef:r,children:n})};a.displayName=t;let u=e+"CollectionSlot",s=m(u),f=l.forwardRef((e,t)=>{let{scope:n,children:r}=e,o=v(t,i(u,n).collectionRef);return(0,c.jsx)(s,{ref:o,children:r})});f.displayName=u;let p=e+"CollectionItemSlot",h="data-radix-collection-item",y=m(p),g=l.forwardRef((e,t)=>{let{scope:n,children:r,...o}=e,a=l.useRef(null),u=v(t,a),s=i(p,n);return l.useEffect(()=>(s.itemMap.set(a,{ref:a,...o}),()=>void s.itemMap.delete(a))),(0,c.jsx)(y,{[h]:"",ref:u,children:r})});return g.displayName=p,[{Provider:a,Slot:f,ItemSlot:g},function(t){let n=i(e+"CollectionConsumer",t);return l.useCallback(()=>{let e=n.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll("[".concat(h,"]")));return Array.from(n.itemMap.values()).sort((e,n)=>t.indexOf(e.ref.current)-t.indexOf(n.ref.current))},[n.collectionRef,n.itemMap])},r]}(ea),[ed,ef]=d(ea,[ec,_]),ep=_(),[ev,em]=ed(ea),[eh,ey]=ed(ea),eg=e=>{let{__scopeSelect:t,children:n,open:r,defaultOpen:o,onOpenChange:i,value:a,defaultValue:u,onValueChange:s,dir:d,name:f,autoComplete:p,disabled:v,required:m,form:h}=e,y=ep(t),[w,b]=l.useState(null),[x,E]=l.useState(null),[C,S]=l.useState(!1),R=function(e){let t=l.useContext(g);return e||t||"ltr"}(d),[N,j]=et({prop:r,defaultProp:null!=o&&o,onChange:i,caller:ea}),[P,D]=et({prop:a,defaultProp:u,onChange:s,caller:ea}),A=l.useRef(null),T=!w||h||!!w.closest("form"),[O,M]=l.useState(new Set),L=Array.from(O).map(e=>e.props.value).join(";");return(0,c.jsx)(B,{...y,children:(0,c.jsxs)(ev,{required:m,scope:t,trigger:w,onTriggerChange:b,valueNode:x,onValueNodeChange:E,valueNodeHasChildren:C,onValueNodeHasChildrenChange:S,contentId:k(),value:P,onValueChange:D,open:N,onOpenChange:j,dir:R,triggerPointerDownPosRef:A,disabled:v,children:[(0,c.jsx)(eu.Provider,{scope:t,children:(0,c.jsx)(eh,{scope:e.__scopeSelect,onNativeOptionAdd:l.useCallback(e=>{M(t=>new Set(t).add(e))},[]),onNativeOptionRemove:l.useCallback(e=>{M(t=>{let n=new Set(t);return n.delete(e),n})},[]),children:n})}),T?(0,c.jsxs)(eQ,{"aria-hidden":!0,required:m,tabIndex:-1,name:f,autoComplete:p,value:P,onChange:e=>D(e.target.value),disabled:v,form:h,children:[void 0===P?(0,c.jsx)("option",{value:""}):null,Array.from(O)]},L):null]})})};eg.displayName=ea;var ew="SelectTrigger",eb=l.forwardRef((e,t)=>{let{__scopeSelect:n,disabled:r=!1,...o}=e,i=ep(n),a=em(ew,n),u=a.disabled||r,d=v(t,a.onTriggerChange),f=es(n),p=l.useRef("touch"),[m,h,y]=e1(e=>{let t=f().filter(e=>!e.disabled),n=t.find(e=>e.value===a.value),r=e2(t,e,n);void 0!==r&&a.onValueChange(r.value)}),g=e=>{u||(a.onOpenChange(!0),y()),e&&(a.triggerPointerDownPosRef.current={x:Math.round(e.pageX),y:Math.round(e.pageY)})};return(0,c.jsx)(V,{asChild:!0,...i,children:(0,c.jsx)(w.button,{type:"button",role:"combobox","aria-controls":a.contentId,"aria-expanded":a.open,"aria-required":a.required,"aria-autocomplete":"none",dir:a.dir,"data-state":a.open?"open":"closed",disabled:u,"data-disabled":u?"":void 0,"data-placeholder":e0(a.value)?"":void 0,...o,ref:d,onClick:s(o.onClick,e=>{e.currentTarget.focus(),"mouse"!==p.current&&g(e)}),onPointerDown:s(o.onPointerDown,e=>{p.current=e.pointerType;let t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),0===e.button&&!1===e.ctrlKey&&"mouse"===e.pointerType&&(g(e),e.preventDefault())}),onKeyDown:s(o.onKeyDown,e=>{let t=""!==m.current;e.ctrlKey||e.altKey||e.metaKey||1!==e.key.length||h(e.key),(!t||" "!==e.key)&&el.includes(e.key)&&(g(),e.preventDefault())})})})});eb.displayName=ew;var ex="SelectValue",eE=l.forwardRef((e,t)=>{let{__scopeSelect:n,className:r,style:o,children:l,placeholder:i="",...a}=e,u=em(ex,n),{onValueNodeHasChildrenChange:s}=u,d=void 0!==l,f=v(t,u.onValueNodeChange);return P(()=>{s(d)},[s,d]),(0,c.jsx)(w.span,{...a,ref:f,style:{pointerEvents:"none"},children:e0(u.value)?(0,c.jsx)(c.Fragment,{children:i}):l})});eE.displayName=ex;var eC=l.forwardRef((e,t)=>{let{__scopeSelect:n,children:r,...o}=e;return(0,c.jsx)(w.span,{"aria-hidden":!0,...o,ref:t,children:r||"▼"})});eC.displayName="SelectIcon";var eS=e=>(0,c.jsx)(Q,{asChild:!0,...e});eS.displayName="SelectPortal";var eR="SelectContent",eN=l.forwardRef((e,t)=>{let n=em(eR,e.__scopeSelect),[r,o]=l.useState();return(P(()=>{o(new DocumentFragment)},[]),n.open)?(0,c.jsx)(eA,{...e,ref:t}):r?a.createPortal((0,c.jsx)(ej,{scope:e.__scopeSelect,children:(0,c.jsx)(eu.Slot,{scope:e.__scopeSelect,children:(0,c.jsx)("div",{children:e.children})})}),r):null});eN.displayName=eR;var[ej,eP]=ed(eR),eD=m("SelectContent.RemoveScroll"),eA=l.forwardRef((e,t)=>{let{__scopeSelect:n,position:r="item-aligned",onCloseAutoFocus:o,onEscapeKeyDown:i,onPointerDownOutside:a,side:u,sideOffset:d,align:f,alignOffset:p,arrowPadding:m,collisionBoundary:h,collisionPadding:y,sticky:g,hideWhenDetached:w,avoidCollisions:b,...x}=e,E=em(eR,n),[S,R]=l.useState(null),[P,D]=l.useState(null),A=v(t,e=>R(e)),[k,T]=l.useState(null),[O,M]=l.useState(null),L=es(n),[I,_]=l.useState(!1),W=l.useRef(!1);l.useEffect(()=>{if(S)return(0,er.Eq)(S)},[S]),(0,N.Oh)();let F=l.useCallback(e=>{let[t,...n]=L().map(e=>e.ref.current),[r]=n.slice(-1),o=document.activeElement;for(let n of e)if(n===o||(null==n||n.scrollIntoView({block:"nearest"}),n===t&&P&&(P.scrollTop=0),n===r&&P&&(P.scrollTop=P.scrollHeight),null==n||n.focus(),document.activeElement!==o))return},[L,P]),B=l.useCallback(()=>F([k,S]),[F,k,S]);l.useEffect(()=>{I&&B()},[I,B]);let{onOpenChange:H,triggerPointerDownPosRef:V}=E;l.useEffect(()=>{if(S){let e={x:0,y:0},t=t=>{var n,r,o,l;e={x:Math.abs(Math.round(t.pageX)-(null!==(o=null===(n=V.current)||void 0===n?void 0:n.x)&&void 0!==o?o:0)),y:Math.abs(Math.round(t.pageY)-(null!==(l=null===(r=V.current)||void 0===r?void 0:r.y)&&void 0!==l?l:0))}},n=n=>{e.x<=10&&e.y<=10?n.preventDefault():S.contains(n.target)||H(!1),document.removeEventListener("pointermove",t),V.current=null};return null!==V.current&&(document.addEventListener("pointermove",t),document.addEventListener("pointerup",n,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",t),document.removeEventListener("pointerup",n,{capture:!0})}}},[S,H,V]),l.useEffect(()=>{let e=()=>H(!1);return window.addEventListener("blur",e),window.addEventListener("resize",e),()=>{window.removeEventListener("blur",e),window.removeEventListener("resize",e)}},[H]);let[$,U]=e1(e=>{let t=L().filter(e=>!e.disabled),n=t.find(e=>e.ref.current===document.activeElement),r=e2(t,e,n);r&&setTimeout(()=>r.ref.current.focus())}),z=l.useCallback((e,t,n)=>{let r=!W.current&&!n;(void 0!==E.value&&E.value===t||r)&&(T(e),r&&(W.current=!0))},[E.value]),K=l.useCallback(()=>null==S?void 0:S.focus(),[S]),Y=l.useCallback((e,t,n)=>{let r=!W.current&&!n;(void 0!==E.value&&E.value===t||r)&&M(e)},[E.value]),q="popper"===r?eT:ek,X=q===eT?{side:u,sideOffset:d,align:f,alignOffset:p,arrowPadding:m,collisionBoundary:h,collisionPadding:y,sticky:g,hideWhenDetached:w,avoidCollisions:b}:{};return(0,c.jsx)(ej,{scope:n,content:S,viewport:P,onViewportChange:D,itemRefCallback:z,selectedItem:k,onItemLeave:K,itemTextRefCallback:Y,focusSelectedItem:B,selectedItemText:O,position:r,isPositioned:I,searchRef:$,children:(0,c.jsx)(eo.A,{as:eD,allowPinchZoom:!0,children:(0,c.jsx)(j.n,{asChild:!0,trapped:E.open,onMountAutoFocus:e=>{e.preventDefault()},onUnmountAutoFocus:s(o,e=>{var t;null===(t=E.trigger)||void 0===t||t.focus({preventScroll:!0}),e.preventDefault()}),children:(0,c.jsx)(C,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:i,onPointerDownOutside:a,onFocusOutside:e=>e.preventDefault(),onDismiss:()=>E.onOpenChange(!1),children:(0,c.jsx)(q,{role:"listbox",id:E.contentId,"data-state":E.open?"open":"closed",dir:E.dir,onContextMenu:e=>e.preventDefault(),...x,...X,onPlaced:()=>_(!0),ref:A,style:{display:"flex",flexDirection:"column",outline:"none",...x.style},onKeyDown:s(x.onKeyDown,e=>{let t=e.ctrlKey||e.altKey||e.metaKey;if("Tab"===e.key&&e.preventDefault(),t||1!==e.key.length||U(e.key),["ArrowUp","ArrowDown","Home","End"].includes(e.key)){let t=L().filter(e=>!e.disabled).map(e=>e.ref.current);if(["ArrowUp","End"].includes(e.key)&&(t=t.slice().reverse()),["ArrowUp","ArrowDown"].includes(e.key)){let n=e.target,r=t.indexOf(n);t=t.slice(r+1)}setTimeout(()=>F(t)),e.preventDefault()}})})})})})})});eA.displayName="SelectContentImpl";var ek=l.forwardRef((e,t)=>{let{__scopeSelect:n,onPlaced:r,...o}=e,i=em(eR,n),a=eP(eR,n),[s,d]=l.useState(null),[f,p]=l.useState(null),m=v(t,e=>p(e)),h=es(n),y=l.useRef(!1),g=l.useRef(!0),{viewport:b,selectedItem:x,selectedItemText:E,focusSelectedItem:C}=a,S=l.useCallback(()=>{if(i.trigger&&i.valueNode&&s&&f&&b&&x&&E){let e=i.trigger.getBoundingClientRect(),t=f.getBoundingClientRect(),n=i.valueNode.getBoundingClientRect(),o=E.getBoundingClientRect();if("rtl"!==i.dir){let r=o.left-t.left,l=n.left-r,i=e.left-l,a=e.width+i,c=Math.max(a,t.width),d=u(l,[10,Math.max(10,window.innerWidth-10-c)]);s.style.minWidth=a+"px",s.style.left=d+"px"}else{let r=t.right-o.right,l=window.innerWidth-n.right-r,i=window.innerWidth-e.right-l,a=e.width+i,c=Math.max(a,t.width),d=u(l,[10,Math.max(10,window.innerWidth-10-c)]);s.style.minWidth=a+"px",s.style.right=d+"px"}let l=h(),a=window.innerHeight-20,c=b.scrollHeight,d=window.getComputedStyle(f),p=parseInt(d.borderTopWidth,10),v=parseInt(d.paddingTop,10),m=parseInt(d.borderBottomWidth,10),g=p+v+c+parseInt(d.paddingBottom,10)+m,w=Math.min(5*x.offsetHeight,g),C=window.getComputedStyle(b),S=parseInt(C.paddingTop,10),R=parseInt(C.paddingBottom,10),N=e.top+e.height/2-10,j=x.offsetHeight/2,P=p+v+(x.offsetTop+j);if(P<=N){let e=l.length>0&&x===l[l.length-1].ref.current;s.style.bottom="0px";let t=Math.max(a-N,j+(e?R:0)+(f.clientHeight-b.offsetTop-b.offsetHeight)+m);s.style.height=P+t+"px"}else{let e=l.length>0&&x===l[0].ref.current;s.style.top="0px";let t=Math.max(N,p+b.offsetTop+(e?S:0)+j);s.style.height=t+(g-P)+"px",b.scrollTop=P-N+b.offsetTop}s.style.margin="".concat(10,"px 0"),s.style.minHeight=w+"px",s.style.maxHeight=a+"px",null==r||r(),requestAnimationFrame(()=>y.current=!0)}},[h,i.trigger,i.valueNode,s,f,b,x,E,i.dir,r]);P(()=>S(),[S]);let[R,N]=l.useState();P(()=>{f&&N(window.getComputedStyle(f).zIndex)},[f]);let j=l.useCallback(e=>{e&&!0===g.current&&(S(),null==C||C(),g.current=!1)},[S,C]);return(0,c.jsx)(eO,{scope:n,contentWrapper:s,shouldExpandOnScrollRef:y,onScrollButtonChange:j,children:(0,c.jsx)("div",{ref:d,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:R},children:(0,c.jsx)(w.div,{...o,ref:m,style:{boxSizing:"border-box",maxHeight:"100%",...o.style}})})})});ek.displayName="SelectItemAlignedPosition";var eT=l.forwardRef((e,t)=>{let{__scopeSelect:n,align:r="start",collisionPadding:o=10,...l}=e,i=ep(n);return(0,c.jsx)(K,{...i,...l,ref:t,align:r,collisionPadding:o,style:{boxSizing:"border-box",...l.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});eT.displayName="SelectPopperPosition";var[eO,eM]=ed(eR,{}),eL="SelectViewport",eI=l.forwardRef((e,t)=>{let{__scopeSelect:n,nonce:r,...o}=e,i=eP(eL,n),a=eM(eL,n),u=v(t,i.onViewportChange),d=l.useRef(0);return(0,c.jsxs)(c.Fragment,{children:[(0,c.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:r}),(0,c.jsx)(eu.Slot,{scope:n,children:(0,c.jsx)(w.div,{"data-radix-select-viewport":"",role:"presentation",...o,ref:u,style:{position:"relative",flex:1,overflow:"hidden auto",...o.style},onScroll:s(o.onScroll,e=>{let t=e.currentTarget,{contentWrapper:n,shouldExpandOnScrollRef:r}=a;if((null==r?void 0:r.current)&&n){let e=Math.abs(d.current-t.scrollTop);if(e>0){let r=window.innerHeight-20,o=Math.max(parseFloat(n.style.minHeight),parseFloat(n.style.height));if(o<r){let l=o+e,i=Math.min(r,l),a=l-i;n.style.height=i+"px","0px"===n.style.bottom&&(t.scrollTop=a>0?a:0,n.style.justifyContent="flex-end")}}}d.current=t.scrollTop})})})]})});eI.displayName=eL;var e_="SelectGroup",[eW,eF]=ed(e_);l.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e,o=k();return(0,c.jsx)(eW,{scope:n,id:o,children:(0,c.jsx)(w.div,{role:"group","aria-labelledby":o,...r,ref:t})})}).displayName=e_;var eB="SelectLabel";l.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e,o=eF(eB,n);return(0,c.jsx)(w.div,{id:o.id,...r,ref:t})}).displayName=eB;var eH="SelectItem",[eV,e$]=ed(eH),eU=l.forwardRef((e,t)=>{let{__scopeSelect:n,value:r,disabled:o=!1,textValue:i,...a}=e,u=em(eH,n),d=eP(eH,n),f=u.value===r,[p,m]=l.useState(null!=i?i:""),[h,y]=l.useState(!1),g=v(t,e=>{var t;return null===(t=d.itemRefCallback)||void 0===t?void 0:t.call(d,e,r,o)}),b=k(),x=l.useRef("touch"),E=()=>{o||(u.onValueChange(r),u.onOpenChange(!1))};if(""===r)throw Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return(0,c.jsx)(eV,{scope:n,value:r,disabled:o,textId:b,isSelected:f,onItemTextChange:l.useCallback(e=>{m(t=>{var n;return t||(null!==(n=null==e?void 0:e.textContent)&&void 0!==n?n:"").trim()})},[]),children:(0,c.jsx)(eu.ItemSlot,{scope:n,value:r,disabled:o,textValue:p,children:(0,c.jsx)(w.div,{role:"option","aria-labelledby":b,"data-highlighted":h?"":void 0,"aria-selected":f&&h,"data-state":f?"checked":"unchecked","aria-disabled":o||void 0,"data-disabled":o?"":void 0,tabIndex:o?void 0:-1,...a,ref:g,onFocus:s(a.onFocus,()=>y(!0)),onBlur:s(a.onBlur,()=>y(!1)),onClick:s(a.onClick,()=>{"mouse"!==x.current&&E()}),onPointerUp:s(a.onPointerUp,()=>{"mouse"===x.current&&E()}),onPointerDown:s(a.onPointerDown,e=>{x.current=e.pointerType}),onPointerMove:s(a.onPointerMove,e=>{if(x.current=e.pointerType,o){var t;null===(t=d.onItemLeave)||void 0===t||t.call(d)}else"mouse"===x.current&&e.currentTarget.focus({preventScroll:!0})}),onPointerLeave:s(a.onPointerLeave,e=>{if(e.currentTarget===document.activeElement){var t;null===(t=d.onItemLeave)||void 0===t||t.call(d)}}),onKeyDown:s(a.onKeyDown,e=>{var t;(null===(t=d.searchRef)||void 0===t?void 0:t.current)!==""&&" "===e.key||(ei.includes(e.key)&&E()," "===e.key&&e.preventDefault())})})})})});eU.displayName=eH;var ez="SelectItemText",eK=l.forwardRef((e,t)=>{let{__scopeSelect:n,className:r,style:o,...i}=e,u=em(ez,n),s=eP(ez,n),d=e$(ez,n),f=ey(ez,n),[p,m]=l.useState(null),h=v(t,e=>m(e),d.onItemTextChange,e=>{var t;return null===(t=s.itemTextRefCallback)||void 0===t?void 0:t.call(s,e,d.value,d.disabled)}),y=null==p?void 0:p.textContent,g=l.useMemo(()=>(0,c.jsx)("option",{value:d.value,disabled:d.disabled,children:y},d.value),[d.disabled,d.value,y]),{onNativeOptionAdd:b,onNativeOptionRemove:x}=f;return P(()=>(b(g),()=>x(g)),[b,x,g]),(0,c.jsxs)(c.Fragment,{children:[(0,c.jsx)(w.span,{id:d.textId,...i,ref:h}),d.isSelected&&u.valueNode&&!u.valueNodeHasChildren?a.createPortal(i.children,u.valueNode):null]})});eK.displayName=ez;var eY="SelectItemIndicator",eq=l.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e;return e$(eY,n).isSelected?(0,c.jsx)(w.span,{"aria-hidden":!0,...r,ref:t}):null});eq.displayName=eY;var eX="SelectScrollUpButton";l.forwardRef((e,t)=>{let n=eP(eX,e.__scopeSelect),r=eM(eX,e.__scopeSelect),[o,i]=l.useState(!1),a=v(t,r.onScrollButtonChange);return P(()=>{if(n.viewport&&n.isPositioned){let e=function(){i(t.scrollTop>0)},t=n.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[n.viewport,n.isPositioned]),o?(0,c.jsx)(eG,{...e,ref:a,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=n;e&&t&&(e.scrollTop=e.scrollTop-t.offsetHeight)}}):null}).displayName=eX;var eZ="SelectScrollDownButton";l.forwardRef((e,t)=>{let n=eP(eZ,e.__scopeSelect),r=eM(eZ,e.__scopeSelect),[o,i]=l.useState(!1),a=v(t,r.onScrollButtonChange);return P(()=>{if(n.viewport&&n.isPositioned){let e=function(){let e=t.scrollHeight-t.clientHeight;i(Math.ceil(t.scrollTop)<e)},t=n.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[n.viewport,n.isPositioned]),o?(0,c.jsx)(eG,{...e,ref:a,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=n;e&&t&&(e.scrollTop=e.scrollTop+t.offsetHeight)}}):null}).displayName=eZ;var eG=l.forwardRef((e,t)=>{let{__scopeSelect:n,onAutoScroll:r,...o}=e,i=eP("SelectScrollButton",n),a=l.useRef(null),u=es(n),d=l.useCallback(()=>{null!==a.current&&(window.clearInterval(a.current),a.current=null)},[]);return l.useEffect(()=>()=>d(),[d]),P(()=>{var e;let t=u().find(e=>e.ref.current===document.activeElement);null==t||null===(e=t.ref.current)||void 0===e||e.scrollIntoView({block:"nearest"})},[u]),(0,c.jsx)(w.div,{"aria-hidden":!0,...o,ref:t,style:{flexShrink:0,...o.style},onPointerDown:s(o.onPointerDown,()=>{null===a.current&&(a.current=window.setInterval(r,50))}),onPointerMove:s(o.onPointerMove,()=>{var e;null===(e=i.onItemLeave)||void 0===e||e.call(i),null===a.current&&(a.current=window.setInterval(r,50))}),onPointerLeave:s(o.onPointerLeave,()=>{d()})})});l.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e;return(0,c.jsx)(w.div,{"aria-hidden":!0,...r,ref:t})}).displayName="SelectSeparator";var eJ="SelectArrow";l.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e,o=ep(n),l=em(eJ,n),i=eP(eJ,n);return l.open&&"popper"===i.position?(0,c.jsx)(X,{...o,...r,ref:t}):null}).displayName=eJ;var eQ=l.forwardRef((e,t)=>{let{__scopeSelect:n,value:r,...o}=e,i=l.useRef(null),a=v(t,i),u=function(e){let t=l.useRef({value:e,previous:e});return l.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}(r);return l.useEffect(()=>{let e=i.current;if(!e)return;let t=Object.getOwnPropertyDescriptor(window.HTMLSelectElement.prototype,"value").set;if(u!==r&&t){let n=new Event("change",{bubbles:!0});t.call(e,r),e.dispatchEvent(n)}},[u,r]),(0,c.jsx)(w.select,{...o,style:{...en,...o.style},ref:a,defaultValue:r})});function e0(e){return""===e||void 0===e}function e1(e){let t=b(e),n=l.useRef(""),r=l.useRef(0),o=l.useCallback(e=>{let o=n.current+e;t(o),function e(t){n.current=t,window.clearTimeout(r.current),""!==t&&(r.current=window.setTimeout(()=>e(""),1e3))}(o)},[t]),i=l.useCallback(()=>{n.current="",window.clearTimeout(r.current)},[]);return l.useEffect(()=>()=>window.clearTimeout(r.current),[]),[n,o,i]}function e2(e,t,n){var r;let o=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,l=(r=Math.max(n?e.indexOf(n):-1,0),e.map((t,n)=>e[(r+n)%e.length]));1===o.length&&(l=l.filter(e=>e!==n));let i=l.find(e=>e.textValue.toLowerCase().startsWith(o.toLowerCase()));return i!==n?i:void 0}eQ.displayName="SelectBubbleInput";var e5=eg,e7=eb,e9=eE,e4=eC,e6=eS,e3=eN,e8=eI,te=eU,tt=eK,tn=eq},858:(e,t,n)=>{n.d(t,{Z:()=>o});var r=n(2115);function o(e){let t=r.useRef({value:e,previous:e});return r.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}},8244:(e,t,n)=>{n.d(t,{_:()=>r});function r(e,t,n){if(!t.has(e))throw TypeError("attempted to "+n+" private field on non-instance");return t.get(e)}},5359:(e,t,n)=>{n.d(t,{_:()=>o});var r=n(8244);function o(e,t){var n=(0,r._)(e,t,"get");return n.get?n.get.call(e):n.value}},587:(e,t,n)=>{n.d(t,{_:()=>r});function r(e,t,n){!function(e,t){if(t.has(e))throw TypeError("Cannot initialize the same private elements twice on an object")}(e,t),t.set(e,n)}},5465:(e,t,n)=>{n.d(t,{_:()=>o});var r=n(8244);function o(e,t,n){var o=(0,r._)(e,t,"set");return!function(e,t,n){if(t.set)t.set.call(e,n);else{if(!t.writable)throw TypeError("attempted to set read only private field");t.value=n}}(e,o,n),n}},9827:(e,t,n)=>{n.d(t,{v:()=>u});var r=n(2115);let o=e=>{let t;let n=new Set,r=(e,r)=>{let o="function"==typeof e?e(t):e;if(!Object.is(o,t)){let e=t;t=(null!=r?r:"object"!=typeof o||null===o)?o:Object.assign({},t,o),n.forEach(n=>n(t,e))}},o=()=>t,l={setState:r,getState:o,getInitialState:()=>i,subscribe:e=>(n.add(e),()=>n.delete(e))},i=t=e(r,o,l);return l},l=e=>e?o(e):o,i=e=>e,a=e=>{let t=l(e),n=e=>(function(e,t=i){let n=r.useSyncExternalStore(e.subscribe,()=>t(e.getState()),()=>t(e.getInitialState()));return r.useDebugValue(n),n})(t,e);return Object.assign(n,t),n},u=e=>e?a(e):a}}]);