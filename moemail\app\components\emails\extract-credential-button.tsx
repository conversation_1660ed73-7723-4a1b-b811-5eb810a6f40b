"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Key, Copy, Check } from "lucide-react"
import { useToast } from "@/components/ui/use-toast"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"


interface ExtractCredentialButtonProps {
  emailAddress: string
  emailType: 'temporary' | 'bound'
  credentialId?: string
}

export function ExtractCredentialButton({
  emailAddress,
  emailType,
  credentialId
}: ExtractCredentialButtonProps) {
  const [open, setOpen] = useState(false)
  const [loading, setLoading] = useState(false)
  const [credential, setCredential] = useState<string>("")
  const [copied, setCopied] = useState(false)
  const { toast } = useToast()

  const handleExtractCredential = async () => {
    setLoading(true)
    try {
      let jwt = ""

      if (emailType === 'bound' && credentialId) {
        // 对于绑定邮箱，通过 credentialId 获取凭证
        const response = await fetch(`/api/email-credentials/${credentialId}`)
        if (!response.ok) {
          const errorData = await response.json()
          throw new Error(errorData.error || '获取凭证失败')
        }
        const data = await response.json()
        jwt = data.jwt
      } else {
        // 对于临时邮箱，创建新的凭证
        const response = await fetch('/api/email-credentials/create', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            emailAddress: emailAddress
          })
        })

        if (!response.ok) {
          const errorData = await response.json()
          throw new Error(errorData.error || '创建凭证失败')
        }

        const data = await response.json()
        jwt = data.jwt
      }

      setCredential(jwt)
      toast({
        title: "成功",
        description: "邮箱凭证提取成功",
      })
    } catch (error) {
      console.error('Failed to extract credential:', error)
      toast({
        title: "错误",
        description: error instanceof Error ? error.message : "提取凭证失败",
        variant: "destructive"
      })
    } finally {
      setLoading(false)
    }
  }

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(credential)
      setCopied(true)
      toast({
        title: "已复制",
        description: "邮箱凭证已复制到剪贴板",
      })
      setTimeout(() => setCopied(false), 2000)
    } catch (error) {
      toast({
        title: "复制失败",
        description: "请手动复制凭证",
        variant: "destructive"
      })
    }
  }

  const handleClose = () => {
    setOpen(false)
    setCredential("")
    setCopied(false)
  }

  return (
    <>
      <Button
        variant="ghost"
        size="icon"
        className="h-8 w-8"
        onClick={(e) => {
          e.stopPropagation()
          console.log('Extract credential button clicked for:', emailAddress)
          console.log('Setting open to true, current open state:', open)
          setOpen(true)
        }}
        title="提取邮箱凭证"
      >
        <Key className="h-4 w-4 text-blue-600" />
      </Button>

      <Dialog open={open} onOpenChange={(newOpen) => {
        console.log('Dialog onOpenChange called with:', newOpen)
        if (!newOpen) {
          handleClose()
        }
      }}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>提取邮箱凭证</DialogTitle>
          <DialogDescription>
            为邮箱 <span className="font-mono text-sm bg-gray-100 px-1 rounded">{emailAddress}</span> 提取API访问凭证
          </DialogDescription>
        </DialogHeader>
        <div className="space-y-4">
          {!credential ? (
            <div className="text-center">
              <Button 
                onClick={handleExtractCredential}
                disabled={loading}
                className="w-full"
              >
                {loading ? "提取中..." : "提取凭证"}
              </Button>
              <p className="text-xs text-gray-500 mt-2">
                此凭证可用于API访问该邮箱的邮件
              </p>
            </div>
          ) : (
            <div className="space-y-3">
              <div>
                <Label htmlFor="credential">邮箱凭证 (JWT Token)</Label>
                <div className="flex gap-2 mt-1">
                  <Input
                    id="credential"
                    value={credential}
                    readOnly
                    className="font-mono text-xs"
                  />
                  <Button
                    variant="outline"
                    size="icon"
                    onClick={handleCopy}
                    className="flex-shrink-0"
                  >
                    {copied ? (
                      <Check className="h-4 w-4 text-green-600" />
                    ) : (
                      <Copy className="h-4 w-4" />
                    )}
                  </Button>
                </div>
              </div>
              <div className="text-xs text-gray-500 space-y-1">
                <p><strong>使用方法：</strong></p>
                <p>在API请求头中添加：</p>
                <code className="block bg-gray-100 p-2 rounded text-xs">
                  X-Email-Credential: {credential.substring(0, 20)}...
                </code>
              </div>
            </div>
          )}
        </div>
      </DialogContent>
      </Dialog>
    </>
  )
}
