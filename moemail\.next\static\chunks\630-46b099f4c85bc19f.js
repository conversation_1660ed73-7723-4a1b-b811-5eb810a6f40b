"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[630],{7401:(e,t,r)=>{r.d(t,{A:()=>i});var o=r(2115);let n=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),s=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return t.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim()};var l={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let a=(0,o.forwardRef)((e,t)=>{let{color:r="currentColor",size:n=24,strokeWidth:a=2,absoluteStrokeWidth:i,className:c="",children:d,iconNode:u,...p}=e;return(0,o.createElement)("svg",{ref:t,...l,width:n,height:n,stroke:r,strokeWidth:i?24*Number(a)/Number(n):a,className:s("lucide",c),...p},[...u.map(e=>{let[t,r]=e;return(0,o.createElement)(t,r)}),...Array.isArray(d)?d:[d]])}),i=(e,t)=>{let r=(0,o.forwardRef)((r,l)=>{let{className:i,...c}=r;return(0,o.createElement)(a,{ref:l,iconNode:t,className:s("lucide-".concat(n(e)),i),...c})});return r.displayName="".concat(e),r}},8068:(e,t,r)=>{r.d(t,{s:()=>l,t:()=>s});var o=r(2115);function n(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function s(...e){return t=>{let r=!1,o=e.map(e=>{let o=n(e,t);return r||"function"!=typeof o||(r=!0),o});if(r)return()=>{for(let t=0;t<o.length;t++){let r=o[t];"function"==typeof r?r():n(e[t],null)}}}}function l(...e){return o.useCallback(s(...e),e)}},2317:(e,t,r)=>{r.d(t,{DX:()=>l,xV:()=>i});var o=r(2115),n=r(8068),s=r(5155),l=o.forwardRef((e,t)=>{let{children:r,...n}=e,l=o.Children.toArray(r),i=l.find(c);if(i){let e=i.props.children,r=l.map(t=>t!==i?t:o.Children.count(e)>1?o.Children.only(null):o.isValidElement(e)?e.props.children:null);return(0,s.jsx)(a,{...n,ref:t,children:o.isValidElement(e)?o.cloneElement(e,void 0,r):null})}return(0,s.jsx)(a,{...n,ref:t,children:r})});l.displayName="Slot";var a=o.forwardRef((e,t)=>{let{children:r,...s}=e;if(o.isValidElement(r)){let e=function(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,r=t&&"isReactWarning"in t&&t.isReactWarning;return r?e.ref:(r=(t=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning)?e.props.ref:e.props.ref||e.ref}(r);return o.cloneElement(r,{...function(e,t){let r={...t};for(let o in t){let n=e[o],s=t[o];/^on[A-Z]/.test(o)?n&&s?r[o]=(...e)=>{s(...e),n(...e)}:n&&(r[o]=n):"style"===o?r[o]={...n,...s}:"className"===o&&(r[o]=[n,s].filter(Boolean).join(" "))}return{...e,...r}}(s,r.props),ref:t?(0,n.t)(t,e):e})}return o.Children.count(r)>1?o.Children.only(null):null});a.displayName="SlotClone";var i=({children:e})=>(0,s.jsx)(s.Fragment,{children:e});function c(e){return o.isValidElement(e)&&e.type===i}},1027:(e,t,r)=>{r.d(t,{F:()=>l});var o=r(3463);let n=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,s=o.$,l=(e,t)=>r=>{var o;if((null==t?void 0:t.variants)==null)return s(e,null==r?void 0:r.class,null==r?void 0:r.className);let{variants:l,defaultVariants:a}=t,i=Object.keys(l).map(e=>{let t=null==r?void 0:r[e],o=null==a?void 0:a[e];if(null===t)return null;let s=n(t)||n(o);return l[e][s]}),c=r&&Object.entries(r).reduce((e,t)=>{let[r,o]=t;return void 0===o||(e[r]=o),e},{});return s(e,i,null==t?void 0:null===(o=t.compoundVariants)||void 0===o?void 0:o.reduce((e,t)=>{let{class:r,className:o,...n}=t;return Object.entries(n).every(e=>{let[t,r]=e;return Array.isArray(r)?r.includes({...a,...c}[t]):({...a,...c})[t]===r})?[...e,r,o]:e},[]),null==r?void 0:r.class,null==r?void 0:r.className)}},3463:(e,t,r)=>{r.d(t,{$:()=>o});function o(){for(var e,t,r=0,o="",n=arguments.length;r<n;r++)(e=arguments[r])&&(t=function e(t){var r,o,n="";if("string"==typeof t||"number"==typeof t)n+=t;else if("object"==typeof t){if(Array.isArray(t)){var s=t.length;for(r=0;r<s;r++)t[r]&&(o=e(t[r]))&&(n&&(n+=" "),n+=o)}else for(o in t)t[o]&&(n&&(n+=" "),n+=o)}return n}(e))&&(o&&(o+=" "),o+=t);return o}},4822:(e,t,r)=>{r.d(t,{CP:()=>ed,Jv:()=>ei,CI:()=>ec,wV:()=>en});var o,n,s,l,a,i=r(5155),c=r(2115),d=r.t(c,2);class u extends Error{constructor(e,t){e instanceof Error?super(void 0,{cause:{err:e,...e.cause,...t}}):"string"==typeof e?(t instanceof Error&&(t={err:t,...t.cause}),super(e,t)):super(void 0,e),this.name=this.constructor.name,this.type=this.constructor.type??"AuthError",this.kind=this.constructor.kind??"error",Error.captureStackTrace?.(this,this.constructor);let r=`https://errors.authjs.dev#${this.type.toLowerCase()}`;this.message+=`${this.message?". ":""}Read more at ${r}`}}class p extends u{}p.kind="signIn";class f extends u{}f.type="AdapterError";class b extends u{}b.type="AccessDenied";class g extends u{}g.type="CallbackRouteError";class h extends u{}h.type="ErrorPageLoop";class m extends u{}m.type="EventError";class v extends u{}v.type="InvalidCallbackUrl";class y extends p{constructor(){super(...arguments),this.code="credentials"}}y.type="CredentialsSignin";class w extends u{}w.type="InvalidEndpoints";class x extends u{}x.type="InvalidCheck";class k extends u{}k.type="JWTSessionError";class E extends u{}E.type="MissingAdapter";class S extends u{}S.type="MissingAdapterMethods";class C extends u{}C.type="MissingAuthorize";class z extends u{}z.type="MissingSecret";class R extends p{}R.type="OAuthAccountNotLinked";class _ extends p{}_.type="OAuthCallbackError";class L extends u{}L.type="OAuthProfileParseError";class j extends u{}j.type="SessionTokenError";class A extends p{}A.type="OAuthSignInError";class U extends p{}U.type="EmailSignInError";class N extends u{}N.type="SignOutError";class P extends u{}P.type="UnknownAction";class T extends u{}T.type="UnsupportedStrategy";class O extends u{}O.type="InvalidProvider";class M extends u{}M.type="UntrustedHost";class I extends u{}I.type="Verification";class $ extends p{}$.type="MissingCSRF";class W extends u{}W.type="DuplicateConditionalUI";class G extends u{}G.type="MissingWebAuthnAutocomplete";class V extends u{}V.type="WebAuthnVerificationError";class X extends p{}X.type="AccountNotLinked";class H extends u{}H.type="ExperimentalFeatureNotEnabled";class D extends u{}class F extends u{}async function B(e,t,r){let o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},n="".concat(q(t),"/").concat(e);try{var s;let e={headers:{"Content-Type":"application/json",...(null==o?void 0:null===(s=o.headers)||void 0===s?void 0:s.cookie)?{cookie:o.headers.cookie}:{}}};(null==o?void 0:o.body)&&(e.body=JSON.stringify(o.body),e.method="POST");let t=await fetch(n,e),r=await t.json();if(!t.ok)throw r;return r}catch(e){return r.error(new D(e.message,e)),null}}function q(e){return"undefined"==typeof window?"".concat(e.baseUrlServer).concat(e.basePathServer):e.basePath}function J(){return Math.floor(Date.now()/1e3)}function Z(e){let t=new URL("http://localhost:3000/api/auth");e&&!e.startsWith("http")&&(e="https://".concat(e));let r=new URL(e||t),o=("/"===r.pathname?t.pathname:r.pathname).replace(/\/$/,""),n="".concat(r.origin).concat(o);return{origin:r.origin,host:r.host,path:o,base:n,toString:()=>n}}var Q=r(2818);let K={baseUrl:Z(null!==(n=Q.env.NEXTAUTH_URL)&&void 0!==n?n:Q.env.VERCEL_URL).origin,basePath:Z(Q.env.NEXTAUTH_URL).path,baseUrlServer:Z(null!==(l=null!==(s=Q.env.NEXTAUTH_URL_INTERNAL)&&void 0!==s?s:Q.env.NEXTAUTH_URL)&&void 0!==l?l:Q.env.VERCEL_URL).origin,basePathServer:Z(null!==(a=Q.env.NEXTAUTH_URL_INTERNAL)&&void 0!==a?a:Q.env.NEXTAUTH_URL).path,_lastSync:0,_session:void 0,_getSession:()=>{}},Y=null;function ee(){return new BroadcastChannel("next-auth")}function et(){return"undefined"==typeof BroadcastChannel?{postMessage:()=>{},addEventListener:()=>{},removeEventListener:()=>{}}:(null===Y&&(Y=ee()),Y)}let er={debug:console.debug,error:console.error,warn:console.warn},eo=null===(o=c.createContext)||void 0===o?void 0:o.call(d,void 0);function en(e){if(!eo)throw Error("React Context is unavailable in Server Components");let t=c.useContext(eo),{required:r,onUnauthenticated:o}=null!=e?e:{},n=r&&"unauthenticated"===t.status;return(c.useEffect(()=>{if(n){let e="".concat(K.basePath,"/signin?").concat(new URLSearchParams({error:"SessionRequired",callbackUrl:window.location.href}));o?o():window.location.href=e}},[n,o]),n)?{data:t.data,update:t.update,status:"loading"}:t}async function es(e){var t;let r=await B("session",K,er,e);return(null===(t=null==e?void 0:e.broadcast)||void 0===t||t)&&ee().postMessage({event:"session",data:{trigger:"getSession"}}),r}async function el(){var e;let t=await B("csrf",K,er);return null!==(e=null==t?void 0:t.csrfToken)&&void 0!==e?e:""}async function ea(){return B("providers",K,er)}async function ei(e,t,r){var o,n,s;let{redirect:l=!0}=null!=t?t:{},a=null!==(n=null!==(o=null==t?void 0:t.redirectTo)&&void 0!==o?o:null==t?void 0:t.callbackUrl)&&void 0!==n?n:window.location.href,i=q(K),c=await ea();if(!c){window.location.href="".concat(i,"/error");return}if(!e||!(e in c)){window.location.href="".concat(i,"/signin?").concat(new URLSearchParams({callbackUrl:a}));return}let d="credentials"===c[e].type,u="email"===c[e].type,p="".concat(i,"/").concat(d?"callback":"signin","/").concat(e),f=await el(),b=await fetch("".concat(p,"?").concat(new URLSearchParams(r)),{method:"post",headers:{"Content-Type":"application/x-www-form-urlencoded","X-Auth-Return-Redirect":"1"},body:new URLSearchParams({...t,csrfToken:f,callbackUrl:a})}),g=await b.json();if(l||!(d||u)){let e=null!==(s=g.url)&&void 0!==s?s:a;window.location.href=e,e.includes("#")&&window.location.reload();return}let h=new URL(g.url).searchParams.get("error"),m=new URL(g.url).searchParams.get("code");return b.ok&&await K._getSession({event:"storage"}),{error:h,code:m,status:b.status,ok:b.ok,url:h?null:g.url}}async function ec(e){var t,r,o,n;let s=null!==(r=null!==(t=null==e?void 0:e.redirectTo)&&void 0!==t?t:null==e?void 0:e.callbackUrl)&&void 0!==r?r:window.location.href,l=q(K),a=await el(),i=await fetch("".concat(l,"/signout"),{method:"post",headers:{"Content-Type":"application/x-www-form-urlencoded","X-Auth-Return-Redirect":"1"},body:new URLSearchParams({csrfToken:a,callbackUrl:s})}),c=await i.json();if(et().postMessage({event:"session",data:{trigger:"signout"}}),null===(o=null==e?void 0:e.redirect)||void 0===o||o){let e=null!==(n=c.url)&&void 0!==n?n:s;window.location.href=e,e.includes("#")&&window.location.reload();return}return await K._getSession({event:"storage"}),c}function ed(e){if(!eo)throw Error("React Context is unavailable in Server Components");let{children:t,basePath:r,refetchInterval:o,refetchWhenOffline:n}=e;r&&(K.basePath=r);let s=void 0!==e.session;K._lastSync=s?J():0;let[l,a]=c.useState(()=>(s&&(K._session=e.session),e.session)),[d,u]=c.useState(!s);c.useEffect(()=>(K._getSession=async function(){let{event:e}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};try{let t="storage"===e;if(t||void 0===K._session){K._lastSync=J(),K._session=await es({broadcast:!t}),a(K._session);return}if(!e||null===K._session||J()<K._lastSync)return;K._lastSync=J(),K._session=await es(),a(K._session)}catch(e){er.error(new F(e.message,e))}finally{u(!1)}},K._getSession(),()=>{K._lastSync=0,K._session=void 0,K._getSession=()=>{}}),[]),c.useEffect(()=>{let e=()=>K._getSession({event:"storage"});return et().addEventListener("message",e),()=>et().removeEventListener("message",e)},[]),c.useEffect(()=>{let{refetchOnWindowFocus:t=!0}=e,r=()=>{t&&"visible"===document.visibilityState&&K._getSession({event:"visibilitychange"})};return document.addEventListener("visibilitychange",r,!1),()=>document.removeEventListener("visibilitychange",r,!1)},[e.refetchOnWindowFocus]);let p=function(){let[e,t]=c.useState("undefined"!=typeof navigator&&navigator.onLine),r=()=>t(!0),o=()=>t(!1);return c.useEffect(()=>(window.addEventListener("online",r),window.addEventListener("offline",o),()=>{window.removeEventListener("online",r),window.removeEventListener("offline",o)}),[]),e}(),f=!1!==n||p;c.useEffect(()=>{if(o&&f){let e=setInterval(()=>{K._session&&K._getSession({event:"poll"})},1e3*o);return()=>clearInterval(e)}},[o,f]);let b=c.useMemo(()=>({data:l,status:d?"loading":l?"authenticated":"unauthenticated",async update(e){if(d)return;u(!0);let t=await B("session",K,er,void 0===e?void 0:{body:{csrfToken:await el(),data:e}});return u(!1),t&&(a(t),et().postMessage({event:"session",data:{trigger:"getSession"}})),t}}),[l,d]);return(0,i.jsx)(eo.Provider,{value:b,children:t})}},9795:(e,t,r)=>{r.d(t,{QP:()=>J});let o=e=>{let t=a(e),{conflictingClassGroups:r,conflictingClassGroupModifiers:o}=e;return{getClassGroupId:e=>{let r=e.split("-");return""===r[0]&&1!==r.length&&r.shift(),n(r,t)||l(e)},getConflictingClassGroupIds:(e,t)=>{let n=r[e]||[];return t&&o[e]?[...n,...o[e]]:n}}},n=(e,t)=>{if(0===e.length)return t.classGroupId;let r=e[0],o=t.nextPart.get(r),s=o?n(e.slice(1),o):void 0;if(s)return s;if(0===t.validators.length)return;let l=e.join("-");return t.validators.find(({validator:e})=>e(l))?.classGroupId},s=/^\[(.+)\]$/,l=e=>{if(s.test(e)){let t=s.exec(e)[1],r=t?.substring(0,t.indexOf(":"));if(r)return"arbitrary.."+r}},a=e=>{let{theme:t,prefix:r}=e,o={nextPart:new Map,validators:[]};return u(Object.entries(e.classGroups),r).forEach(([e,r])=>{i(r,o,e,t)}),o},i=(e,t,r,o)=>{e.forEach(e=>{if("string"==typeof e){(""===e?t:c(t,e)).classGroupId=r;return}if("function"==typeof e){if(d(e)){i(e(o),t,r,o);return}t.validators.push({validator:e,classGroupId:r});return}Object.entries(e).forEach(([e,n])=>{i(n,c(t,e),r,o)})})},c=(e,t)=>{let r=e;return t.split("-").forEach(e=>{r.nextPart.has(e)||r.nextPart.set(e,{nextPart:new Map,validators:[]}),r=r.nextPart.get(e)}),r},d=e=>e.isThemeGetter,u=(e,t)=>t?e.map(([e,r])=>[e,r.map(e=>"string"==typeof e?t+e:"object"==typeof e?Object.fromEntries(Object.entries(e).map(([e,r])=>[t+e,r])):e)]):e,p=e=>{if(e<1)return{get:()=>void 0,set:()=>{}};let t=0,r=new Map,o=new Map,n=(n,s)=>{r.set(n,s),++t>e&&(t=0,o=r,r=new Map)};return{get(e){let t=r.get(e);return void 0!==t?t:void 0!==(t=o.get(e))?(n(e,t),t):void 0},set(e,t){r.has(e)?r.set(e,t):n(e,t)}}},f=e=>{let{separator:t,experimentalParseClassName:r}=e,o=1===t.length,n=t[0],s=t.length,l=e=>{let r;let l=[],a=0,i=0;for(let c=0;c<e.length;c++){let d=e[c];if(0===a){if(d===n&&(o||e.slice(c,c+s)===t)){l.push(e.slice(i,c)),i=c+s;continue}if("/"===d){r=c;continue}}"["===d?a++:"]"===d&&a--}let c=0===l.length?e:e.substring(i),d=c.startsWith("!"),u=d?c.substring(1):c;return{modifiers:l,hasImportantModifier:d,baseClassName:u,maybePostfixModifierPosition:r&&r>i?r-i:void 0}};return r?e=>r({className:e,parseClassName:l}):l},b=e=>{if(e.length<=1)return e;let t=[],r=[];return e.forEach(e=>{"["===e[0]?(t.push(...r.sort(),e),r=[]):r.push(e)}),t.push(...r.sort()),t},g=e=>({cache:p(e.cacheSize),parseClassName:f(e),...o(e)}),h=/\s+/,m=(e,t)=>{let{parseClassName:r,getClassGroupId:o,getConflictingClassGroupIds:n}=t,s=[],l=e.trim().split(h),a="";for(let e=l.length-1;e>=0;e-=1){let t=l[e],{modifiers:i,hasImportantModifier:c,baseClassName:d,maybePostfixModifierPosition:u}=r(t),p=!!u,f=o(p?d.substring(0,u):d);if(!f){if(!p||!(f=o(d))){a=t+(a.length>0?" "+a:a);continue}p=!1}let g=b(i).join(":"),h=c?g+"!":g,m=h+f;if(s.includes(m))continue;s.push(m);let v=n(f,p);for(let e=0;e<v.length;++e){let t=v[e];s.push(h+t)}a=t+(a.length>0?" "+a:a)}return a};function v(){let e,t,r=0,o="";for(;r<arguments.length;)(e=arguments[r++])&&(t=y(e))&&(o&&(o+=" "),o+=t);return o}let y=e=>{let t;if("string"==typeof e)return e;let r="";for(let o=0;o<e.length;o++)e[o]&&(t=y(e[o]))&&(r&&(r+=" "),r+=t);return r},w=e=>{let t=t=>t[e]||[];return t.isThemeGetter=!0,t},x=/^\[(?:([a-z-]+):)?(.+)\]$/i,k=/^\d+\/\d+$/,E=new Set(["px","full","screen"]),S=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,C=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,z=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,R=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,_=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,L=e=>A(e)||E.has(e)||k.test(e),j=e=>H(e,"length",D),A=e=>!!e&&!Number.isNaN(Number(e)),U=e=>H(e,"number",A),N=e=>!!e&&Number.isInteger(Number(e)),P=e=>e.endsWith("%")&&A(e.slice(0,-1)),T=e=>x.test(e),O=e=>S.test(e),M=new Set(["length","size","percentage"]),I=e=>H(e,M,F),$=e=>H(e,"position",F),W=new Set(["image","url"]),G=e=>H(e,W,q),V=e=>H(e,"",B),X=()=>!0,H=(e,t,r)=>{let o=x.exec(e);return!!o&&(o[1]?"string"==typeof t?o[1]===t:t.has(o[1]):r(o[2]))},D=e=>C.test(e)&&!z.test(e),F=()=>!1,B=e=>R.test(e),q=e=>_.test(e);Symbol.toStringTag;let J=function(e,...t){let r,o,n;let s=function(a){return o=(r=g(t.reduce((e,t)=>t(e),e()))).cache.get,n=r.cache.set,s=l,l(a)};function l(e){let t=o(e);if(t)return t;let s=m(e,r);return n(e,s),s}return function(){return s(v.apply(null,arguments))}}(()=>{let e=w("colors"),t=w("spacing"),r=w("blur"),o=w("brightness"),n=w("borderColor"),s=w("borderRadius"),l=w("borderSpacing"),a=w("borderWidth"),i=w("contrast"),c=w("grayscale"),d=w("hueRotate"),u=w("invert"),p=w("gap"),f=w("gradientColorStops"),b=w("gradientColorStopPositions"),g=w("inset"),h=w("margin"),m=w("opacity"),v=w("padding"),y=w("saturate"),x=w("scale"),k=w("sepia"),E=w("skew"),S=w("space"),C=w("translate"),z=()=>["auto","contain","none"],R=()=>["auto","hidden","clip","visible","scroll"],_=()=>["auto",T,t],M=()=>[T,t],W=()=>["",L,j],H=()=>["auto",A,T],D=()=>["bottom","center","left","left-bottom","left-top","right","right-bottom","right-top","top"],F=()=>["solid","dashed","dotted","double","none"],B=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],q=()=>["start","end","center","between","around","evenly","stretch"],J=()=>["","0",T],Z=()=>["auto","avoid","all","avoid-page","page","left","right","column"],Q=()=>[A,T];return{cacheSize:500,separator:":",theme:{colors:[X],spacing:[L,j],blur:["none","",O,T],brightness:Q(),borderColor:[e],borderRadius:["none","","full",O,T],borderSpacing:M(),borderWidth:W(),contrast:Q(),grayscale:J(),hueRotate:Q(),invert:J(),gap:M(),gradientColorStops:[e],gradientColorStopPositions:[P,j],inset:_(),margin:_(),opacity:Q(),padding:M(),saturate:Q(),scale:Q(),sepia:J(),skew:Q(),space:M(),translate:M()},classGroups:{aspect:[{aspect:["auto","square","video",T]}],container:["container"],columns:[{columns:[O]}],"break-after":[{"break-after":Z()}],"break-before":[{"break-before":Z()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:[...D(),T]}],overflow:[{overflow:R()}],"overflow-x":[{"overflow-x":R()}],"overflow-y":[{"overflow-y":R()}],overscroll:[{overscroll:z()}],"overscroll-x":[{"overscroll-x":z()}],"overscroll-y":[{"overscroll-y":z()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:[g]}],"inset-x":[{"inset-x":[g]}],"inset-y":[{"inset-y":[g]}],start:[{start:[g]}],end:[{end:[g]}],top:[{top:[g]}],right:[{right:[g]}],bottom:[{bottom:[g]}],left:[{left:[g]}],visibility:["visible","invisible","collapse"],z:[{z:["auto",N,T]}],basis:[{basis:_()}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["wrap","wrap-reverse","nowrap"]}],flex:[{flex:["1","auto","initial","none",T]}],grow:[{grow:J()}],shrink:[{shrink:J()}],order:[{order:["first","last","none",N,T]}],"grid-cols":[{"grid-cols":[X]}],"col-start-end":[{col:["auto",{span:["full",N,T]},T]}],"col-start":[{"col-start":H()}],"col-end":[{"col-end":H()}],"grid-rows":[{"grid-rows":[X]}],"row-start-end":[{row:["auto",{span:[N,T]},T]}],"row-start":[{"row-start":H()}],"row-end":[{"row-end":H()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":["auto","min","max","fr",T]}],"auto-rows":[{"auto-rows":["auto","min","max","fr",T]}],gap:[{gap:[p]}],"gap-x":[{"gap-x":[p]}],"gap-y":[{"gap-y":[p]}],"justify-content":[{justify:["normal",...q()]}],"justify-items":[{"justify-items":["start","end","center","stretch"]}],"justify-self":[{"justify-self":["auto","start","end","center","stretch"]}],"align-content":[{content:["normal",...q(),"baseline"]}],"align-items":[{items:["start","end","center","baseline","stretch"]}],"align-self":[{self:["auto","start","end","center","stretch","baseline"]}],"place-content":[{"place-content":[...q(),"baseline"]}],"place-items":[{"place-items":["start","end","center","baseline","stretch"]}],"place-self":[{"place-self":["auto","start","end","center","stretch"]}],p:[{p:[v]}],px:[{px:[v]}],py:[{py:[v]}],ps:[{ps:[v]}],pe:[{pe:[v]}],pt:[{pt:[v]}],pr:[{pr:[v]}],pb:[{pb:[v]}],pl:[{pl:[v]}],m:[{m:[h]}],mx:[{mx:[h]}],my:[{my:[h]}],ms:[{ms:[h]}],me:[{me:[h]}],mt:[{mt:[h]}],mr:[{mr:[h]}],mb:[{mb:[h]}],ml:[{ml:[h]}],"space-x":[{"space-x":[S]}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":[S]}],"space-y-reverse":["space-y-reverse"],w:[{w:["auto","min","max","fit","svw","lvw","dvw",T,t]}],"min-w":[{"min-w":[T,t,"min","max","fit"]}],"max-w":[{"max-w":[T,t,"none","full","min","max","fit","prose",{screen:[O]},O]}],h:[{h:[T,t,"auto","min","max","fit","svh","lvh","dvh"]}],"min-h":[{"min-h":[T,t,"min","max","fit","svh","lvh","dvh"]}],"max-h":[{"max-h":[T,t,"min","max","fit","svh","lvh","dvh"]}],size:[{size:[T,t,"auto","min","max","fit"]}],"font-size":[{text:["base",O,j]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:["thin","extralight","light","normal","medium","semibold","bold","extrabold","black",U]}],"font-family":[{font:[X]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:["tighter","tight","normal","wide","wider","widest",T]}],"line-clamp":[{"line-clamp":["none",A,U]}],leading:[{leading:["none","tight","snug","normal","relaxed","loose",L,T]}],"list-image":[{"list-image":["none",T]}],"list-style-type":[{list:["none","disc","decimal",T]}],"list-style-position":[{list:["inside","outside"]}],"placeholder-color":[{placeholder:[e]}],"placeholder-opacity":[{"placeholder-opacity":[m]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"text-color":[{text:[e]}],"text-opacity":[{"text-opacity":[m]}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...F(),"wavy"]}],"text-decoration-thickness":[{decoration:["auto","from-font",L,j]}],"underline-offset":[{"underline-offset":["auto",L,T]}],"text-decoration-color":[{decoration:[e]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:M()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",T]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",T]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-opacity":[{"bg-opacity":[m]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:[...D(),$]}],"bg-repeat":[{bg:["no-repeat",{repeat:["","x","y","round","space"]}]}],"bg-size":[{bg:["auto","cover","contain",I]}],"bg-image":[{bg:["none",{"gradient-to":["t","tr","r","br","b","bl","l","tl"]},G]}],"bg-color":[{bg:[e]}],"gradient-from-pos":[{from:[b]}],"gradient-via-pos":[{via:[b]}],"gradient-to-pos":[{to:[b]}],"gradient-from":[{from:[f]}],"gradient-via":[{via:[f]}],"gradient-to":[{to:[f]}],rounded:[{rounded:[s]}],"rounded-s":[{"rounded-s":[s]}],"rounded-e":[{"rounded-e":[s]}],"rounded-t":[{"rounded-t":[s]}],"rounded-r":[{"rounded-r":[s]}],"rounded-b":[{"rounded-b":[s]}],"rounded-l":[{"rounded-l":[s]}],"rounded-ss":[{"rounded-ss":[s]}],"rounded-se":[{"rounded-se":[s]}],"rounded-ee":[{"rounded-ee":[s]}],"rounded-es":[{"rounded-es":[s]}],"rounded-tl":[{"rounded-tl":[s]}],"rounded-tr":[{"rounded-tr":[s]}],"rounded-br":[{"rounded-br":[s]}],"rounded-bl":[{"rounded-bl":[s]}],"border-w":[{border:[a]}],"border-w-x":[{"border-x":[a]}],"border-w-y":[{"border-y":[a]}],"border-w-s":[{"border-s":[a]}],"border-w-e":[{"border-e":[a]}],"border-w-t":[{"border-t":[a]}],"border-w-r":[{"border-r":[a]}],"border-w-b":[{"border-b":[a]}],"border-w-l":[{"border-l":[a]}],"border-opacity":[{"border-opacity":[m]}],"border-style":[{border:[...F(),"hidden"]}],"divide-x":[{"divide-x":[a]}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":[a]}],"divide-y-reverse":["divide-y-reverse"],"divide-opacity":[{"divide-opacity":[m]}],"divide-style":[{divide:F()}],"border-color":[{border:[n]}],"border-color-x":[{"border-x":[n]}],"border-color-y":[{"border-y":[n]}],"border-color-s":[{"border-s":[n]}],"border-color-e":[{"border-e":[n]}],"border-color-t":[{"border-t":[n]}],"border-color-r":[{"border-r":[n]}],"border-color-b":[{"border-b":[n]}],"border-color-l":[{"border-l":[n]}],"divide-color":[{divide:[n]}],"outline-style":[{outline:["",...F()]}],"outline-offset":[{"outline-offset":[L,T]}],"outline-w":[{outline:[L,j]}],"outline-color":[{outline:[e]}],"ring-w":[{ring:W()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:[e]}],"ring-opacity":[{"ring-opacity":[m]}],"ring-offset-w":[{"ring-offset":[L,j]}],"ring-offset-color":[{"ring-offset":[e]}],shadow:[{shadow:["","inner","none",O,V]}],"shadow-color":[{shadow:[X]}],opacity:[{opacity:[m]}],"mix-blend":[{"mix-blend":[...B(),"plus-lighter","plus-darker"]}],"bg-blend":[{"bg-blend":B()}],filter:[{filter:["","none"]}],blur:[{blur:[r]}],brightness:[{brightness:[o]}],contrast:[{contrast:[i]}],"drop-shadow":[{"drop-shadow":["","none",O,T]}],grayscale:[{grayscale:[c]}],"hue-rotate":[{"hue-rotate":[d]}],invert:[{invert:[u]}],saturate:[{saturate:[y]}],sepia:[{sepia:[k]}],"backdrop-filter":[{"backdrop-filter":["","none"]}],"backdrop-blur":[{"backdrop-blur":[r]}],"backdrop-brightness":[{"backdrop-brightness":[o]}],"backdrop-contrast":[{"backdrop-contrast":[i]}],"backdrop-grayscale":[{"backdrop-grayscale":[c]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[d]}],"backdrop-invert":[{"backdrop-invert":[u]}],"backdrop-opacity":[{"backdrop-opacity":[m]}],"backdrop-saturate":[{"backdrop-saturate":[y]}],"backdrop-sepia":[{"backdrop-sepia":[k]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":[l]}],"border-spacing-x":[{"border-spacing-x":[l]}],"border-spacing-y":[{"border-spacing-y":[l]}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["none","all","","colors","opacity","shadow","transform",T]}],duration:[{duration:Q()}],ease:[{ease:["linear","in","out","in-out",T]}],delay:[{delay:Q()}],animate:[{animate:["none","spin","ping","pulse","bounce",T]}],transform:[{transform:["","gpu","none"]}],scale:[{scale:[x]}],"scale-x":[{"scale-x":[x]}],"scale-y":[{"scale-y":[x]}],rotate:[{rotate:[N,T]}],"translate-x":[{"translate-x":[C]}],"translate-y":[{"translate-y":[C]}],"skew-x":[{"skew-x":[E]}],"skew-y":[{"skew-y":[E]}],"transform-origin":[{origin:["center","top","top-right","right","bottom-right","bottom","bottom-left","left","top-left",T]}],accent:[{accent:["auto",e]}],appearance:[{appearance:["none","auto"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",T]}],"caret-color":[{caret:[e]}],"pointer-events":[{"pointer-events":["none","auto"]}],resize:[{resize:["none","y","x",""]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":M()}],"scroll-mx":[{"scroll-mx":M()}],"scroll-my":[{"scroll-my":M()}],"scroll-ms":[{"scroll-ms":M()}],"scroll-me":[{"scroll-me":M()}],"scroll-mt":[{"scroll-mt":M()}],"scroll-mr":[{"scroll-mr":M()}],"scroll-mb":[{"scroll-mb":M()}],"scroll-ml":[{"scroll-ml":M()}],"scroll-p":[{"scroll-p":M()}],"scroll-px":[{"scroll-px":M()}],"scroll-py":[{"scroll-py":M()}],"scroll-ps":[{"scroll-ps":M()}],"scroll-pe":[{"scroll-pe":M()}],"scroll-pt":[{"scroll-pt":M()}],"scroll-pr":[{"scroll-pr":M()}],"scroll-pb":[{"scroll-pb":M()}],"scroll-pl":[{"scroll-pl":M()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",T]}],fill:[{fill:[e,"none"]}],"stroke-w":[{stroke:[L,j,U]}],stroke:[{stroke:[e,"none"]}],sr:["sr-only","not-sr-only"],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]}}})}}]);