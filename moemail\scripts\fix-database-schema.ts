/**
 * 修复数据库表结构脚本
 * 
 * 此脚本用于修复邮箱凭证系统的数据库表结构问题
 */

import { createDb } from '../app/lib/db'

async function fixDatabaseSchema() {
  console.log('开始修复数据库表结构...')
  
  const db = createDb()
  
  try {
    // 1. 删除旧的 email_credentials 表
    console.log('删除旧的 email_credentials 表...')
    await db.run(`DROP TABLE IF EXISTS email_credentials`)
    
    // 2. 创建新的 email_credentials 表
    console.log('创建新的 email_credentials 表...')
    await db.run(`
      CREATE TABLE email_credentials (
        id TEXT PRIMARY KEY,
        email_address TEXT NOT NULL,
        jwt TEXT NOT NULL,
        enabled INTEGER NOT NULL DEFAULT 1,
        created_at INTEGER NOT NULL,
        last_used_at INTEGER,
        UNIQUE(email_address)
      )
    `)
    
    // 3. 删除旧的 user_email_bindings 表
    console.log('删除旧的 user_email_bindings 表...')
    await db.run(`DROP TABLE IF EXISTS user_email_bindings`)
    
    // 4. 创建新的 user_email_bindings 表
    console.log('创建新的 user_email_bindings 表...')
    await db.run(`
      CREATE TABLE user_email_bindings (
        id TEXT PRIMARY KEY,
        user_id TEXT NOT NULL,
        email_address TEXT NOT NULL,
        credential_id TEXT NOT NULL,
        created_at INTEGER NOT NULL,
        UNIQUE(user_id, email_address),
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        FOREIGN KEY (credential_id) REFERENCES email_credentials(id) ON DELETE CASCADE
      )
    `)
    
    console.log('数据库表结构修复完成！')
    
  } catch (error) {
    console.error('修复数据库表结构时发生错误:', error)
    throw error
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  fixDatabaseSchema()
    .then(() => {
      console.log('数据库表结构修复成功')
      process.exit(0)
    })
    .catch((error) => {
      console.error('数据库表结构修复失败:', error)
      process.exit(1)
    })
}

export { fixDatabaseSchema }
