"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[514],{12012:(e,t,r)=>{r.d(t,{U9:()=>i});var a=r(80260);function i(e){return(0,a.ll)`count(${e||a.ll.raw("*")})`.mapWith(Number)}},53719:(e,t,r)=>{r.d(t,{P:()=>w});var a=r(51551),i=r(34008),o=r(63167),s=r(88226);let n=async(e,t,r)=>{let a=await (0,s.A)(e,t,"sign");return(0,o.A)(e,a),new Uint8Array(await crypto.subtle.sign((0,i.A)(e,a.algorithm),a,r))};var c=r(87121),d=r(31886),l=r(61526),p=r(42691),y=r(20491),h=r(23088);class u{#e;#t;#r;constructor(e){if(!(e instanceof Uint8Array))throw TypeError("payload must be an instance of Uint8Array");this.#e=e}setProtectedHeader(e){if(this.#t)throw TypeError("setProtectedHeader can only be called once");return this.#t=e,this}setUnprotectedHeader(e){if(this.#r)throw TypeError("setUnprotectedHeader can only be called once");return this.#r=e,this}async sign(e,t){let r;if(!this.#t&&!this.#r)throw new d.Ye("either setProtectedHeader or setUnprotectedHeader must be called before #sign()");if(!(0,c.A)(this.#t,this.#r))throw new d.Ye("JWS Protected and JWS Unprotected Header Parameter names must be disjoint");let i={...this.#t,...this.#r},o=(0,y.A)(d.Ye,new Map([["b64",!0]]),t?.crit,this.#t,i),s=!0;if(o.has("b64")&&"boolean"!=typeof(s=this.#t.b64))throw new d.Ye('The "b64" (base64url-encode payload) Header Parameter must be a boolean');let{alg:u}=i;if("string"!=typeof u||!u)throw new d.Ye('JWS "alg" (Algorithm) Header Parameter missing or invalid');(0,p.A)(u,e,"sign");let f=this.#e;s&&(f=l.Rd.encode((0,a.l)(f))),r=this.#t?l.Rd.encode((0,a.l)(JSON.stringify(this.#t))):l.Rd.encode("");let m=(0,l.xW)(r,l.Rd.encode("."),f),w=await (0,h.A)(e,u),S=await n(u,w,m),b={signature:(0,a.l)(S),payload:""};return s&&(b.payload=l.D0.decode(f)),this.#r&&(b.header=this.#r),this.#t&&(b.protected=l.D0.decode(r)),b}}class f{#a;constructor(e){this.#a=new u(e)}setProtectedHeader(e){return this.#a.setProtectedHeader(e),this}async sign(e,t){let r=await this.#a.sign(e,t);if(void 0===r.payload)throw TypeError("use the flattened module for creating JWS with b64: false");return`${r.protected}.${r.payload}.${r.signature}`}}var m=r(58388);class w{#t;#i;constructor(e={}){this.#i=new m.c(e)}setIssuer(e){return this.#i.iss=e,this}setSubject(e){return this.#i.sub=e,this}setAudience(e){return this.#i.aud=e,this}setJti(e){return this.#i.jti=e,this}setNotBefore(e){return this.#i.nbf=e,this}setExpirationTime(e){return this.#i.exp=e,this}setIssuedAt(e){return this.#i.iat=e,this}setProtectedHeader(e){return this.#t=e,this}async sign(e,t){let r=new f(this.#i.data());if(r.setProtectedHeader(this.#t),Array.isArray(this.#t?.crit)&&this.#t.crit.includes("b64")&&!1===this.#t.b64)throw new d.Dp("JWTs MUST NOT use unencoded payload");return r.sign(e,t)}}},91585:(e,t,r)=>{r.d(t,{V:()=>b});var a=r(51551),i=r(34008),o=r(63167),s=r(88226);let n=async(e,t,r,a)=>{let n=await (0,s.A)(e,t,"verify");(0,o.A)(e,n);let c=(0,i.A)(e,n.algorithm);try{return await crypto.subtle.verify(c,n,r,a)}catch{return!1}};var c=r(31886),d=r(61526),l=r(87121),p=r(78882),y=r(42691),h=r(20491);let u=(e,t)=>{if(void 0!==t&&(!Array.isArray(t)||t.some(e=>"string"!=typeof e)))throw TypeError(`"${e}" option must be an array of strings`);if(t)return new Set(t)};var f=r(23088);async function m(e,t,r){let i,o;if(!(0,p.A)(e))throw new c.Ye("Flattened JWS must be an object");if(void 0===e.protected&&void 0===e.header)throw new c.Ye('Flattened JWS must have either of the "protected" or "header" members');if(void 0!==e.protected&&"string"!=typeof e.protected)throw new c.Ye("JWS Protected Header incorrect type");if(void 0===e.payload)throw new c.Ye("JWS Payload missing");if("string"!=typeof e.signature)throw new c.Ye("JWS Signature missing or incorrect type");if(void 0!==e.header&&!(0,p.A)(e.header))throw new c.Ye("JWS Unprotected Header incorrect type");let s={};if(e.protected)try{let t=(0,a.D)(e.protected);s=JSON.parse(d.D0.decode(t))}catch{throw new c.Ye("JWS Protected Header is invalid")}if(!(0,l.A)(s,e.header))throw new c.Ye("JWS Protected and JWS Unprotected Header Parameter names must be disjoint");let m={...s,...e.header},w=(0,h.A)(c.Ye,new Map([["b64",!0]]),r?.crit,s,m),S=!0;if(w.has("b64")&&"boolean"!=typeof(S=s.b64))throw new c.Ye('The "b64" (base64url-encode payload) Header Parameter must be a boolean');let{alg:b}=m;if("string"!=typeof b||!b)throw new c.Ye('JWS "alg" (Algorithm) Header Parameter missing or invalid');let g=r&&u("algorithms",r.algorithms);if(g&&!g.has(b))throw new c.Rb('"alg" (Algorithm) Header Parameter value not allowed');if(S){if("string"!=typeof e.payload)throw new c.Ye("JWS Payload must be a string")}else if("string"!=typeof e.payload&&!(e.payload instanceof Uint8Array))throw new c.Ye("JWS Payload must be a string or an Uint8Array instance");let A=!1;"function"==typeof t&&(t=await t(s,e),A=!0),(0,y.A)(b,t,"verify");let E=(0,d.xW)(d.Rd.encode(e.protected??""),d.Rd.encode("."),"string"==typeof e.payload?d.Rd.encode(e.payload):e.payload);try{i=(0,a.D)(e.signature)}catch{throw new c.Ye("Failed to base64url decode the signature")}let v=await (0,f.A)(t,b);if(!await n(b,v,i,E))throw new c.h2;if(S)try{o=(0,a.D)(e.payload)}catch{throw new c.Ye("Failed to base64url decode the payload")}else o="string"==typeof e.payload?d.Rd.encode(e.payload):e.payload;let T={payload:o};return(void 0!==e.protected&&(T.protectedHeader=s),void 0!==e.header&&(T.unprotectedHeader=e.header),A)?{...T,key:v}:T}async function w(e,t,r){if(e instanceof Uint8Array&&(e=d.D0.decode(e)),"string"!=typeof e)throw new c.Ye("Compact JWS must be a string or Uint8Array");let{0:a,1:i,2:o,length:s}=e.split(".");if(3!==s)throw new c.Ye("Invalid Compact JWS");let n=await m({payload:i,protected:a,signature:o},t,r),l={payload:n.payload,protectedHeader:n.protectedHeader};return"function"==typeof t?{...l,key:n.key}:l}var S=r(58388);async function b(e,t,r){let a=await w(e,t,r);if(a.protectedHeader.crit?.includes("b64")&&!1===a.protectedHeader.b64)throw new c.Dp("JWTs MUST NOT use unencoded payload");let i={payload:(0,S.k)(a.protectedHeader,a.payload,r),protectedHeader:a.protectedHeader};return"function"==typeof t?{...i,key:a.key}:i}},61526:(e,t,r)=>{r.d(t,{D0:()=>i,Rd:()=>a,xW:()=>o});let a=new TextEncoder,i=new TextDecoder;function o(...e){let t=new Uint8Array(e.reduce((e,{length:t})=>e+t,0)),r=0;for(let a of e)t.set(a,r),r+=a.length;return t}},63167:(e,t,r)=>{r.d(t,{A:()=>a});let a=(e,t)=>{if(e.startsWith("RS")||e.startsWith("PS")){let{modulusLength:r}=t.algorithm;if("number"!=typeof r||r<2048)throw TypeError(`${e} requires key modulusLength to be 2048 bits or larger`)}}},42691:(e,t,r)=>{r.d(t,{A:()=>l});var a=r(94724),i=r(14290),o=r(6417);let s=e=>e?.[Symbol.toStringTag],n=(e,t,r)=>{if(void 0!==t.use){let e;switch(r){case"sign":case"verify":e="sig";break;case"encrypt":case"decrypt":e="enc"}if(t.use!==e)throw TypeError(`Invalid key for this operation, its "use" must be "${e}" when present`)}if(void 0!==t.alg&&t.alg!==e)throw TypeError(`Invalid key for this operation, its "alg" must be "${e}" when present`);if(Array.isArray(t.key_ops)){let a;switch(!0){case"sign"===r||"verify"===r:case"dir"===e:case e.includes("CBC-HS"):a=r;break;case e.startsWith("PBES2"):a="deriveBits";break;case/^A\d{3}(?:GCM)?(?:KW)?$/.test(e):a=!e.includes("GCM")&&e.endsWith("KW")?"encrypt"===r?"wrapKey":"unwrapKey":r;break;case"encrypt"===r&&e.startsWith("RSA"):a="wrapKey";break;case"decrypt"===r:a=e.startsWith("RSA")?"unwrapKey":"deriveBits"}if(a&&t.key_ops?.includes?.(a)===!1)throw TypeError(`Invalid key for this operation, its "key_ops" must include "${a}" when present`)}return!0},c=(e,t,r)=>{if(!(t instanceof Uint8Array)){if(o.ll(t)){if(o.t9(t)&&n(e,t,r))return;throw TypeError('JSON Web Key for symmetric algorithms must have JWK "kty" (Key Type) equal to "oct" and the JWK "k" (Key Value) present')}if(!(0,i.Ay)(t))throw TypeError((0,a.t)(e,t,"CryptoKey","KeyObject","JSON Web Key","Uint8Array"));if("secret"!==t.type)throw TypeError(`${s(t)} instances for symmetric algorithms must be of type "secret"`)}},d=(e,t,r)=>{if(o.ll(t))switch(r){case"decrypt":case"sign":if(o.W2(t)&&n(e,t,r))return;throw TypeError("JSON Web Key for this operation be a private JWK");case"encrypt":case"verify":if(o.M3(t)&&n(e,t,r))return;throw TypeError("JSON Web Key for this operation be a public JWK")}if(!(0,i.Ay)(t))throw TypeError((0,a.t)(e,t,"CryptoKey","KeyObject","JSON Web Key"));if("secret"===t.type)throw TypeError(`${s(t)} instances for asymmetric algorithms must not be of type "secret"`);if("public"===t.type)switch(r){case"sign":throw TypeError(`${s(t)} instances for asymmetric algorithm signing must be of type "private"`);case"decrypt":throw TypeError(`${s(t)} instances for asymmetric algorithm decryption must be of type "private"`)}if("private"===t.type)switch(r){case"verify":throw TypeError(`${s(t)} instances for asymmetric algorithm verifying must be of type "public"`);case"encrypt":throw TypeError(`${s(t)} instances for asymmetric algorithm encryption must be of type "public"`)}},l=(e,t,r)=>{e.startsWith("HS")||"dir"===e||e.startsWith("PBES2")||/^A(?:128|192|256)(?:GCM)?(?:KW)?$/.test(e)||/^A(?:128|192|256)CBC-HS(?:256|384|512)$/.test(e)?c(e,t,r):d(e,t,r)}},88226:(e,t,r)=>{function a(e,t="algorithm.name"){return TypeError(`CryptoKey does not support this operation, its ${t} must be ${e}`)}function i(e,t){return e.name===t}function o(e){return parseInt(e.name.slice(4),10)}r.d(t,{A:()=>n});var s=r(94724);let n=async(e,t,r)=>{if(t instanceof Uint8Array){if(!e.startsWith("HS"))throw TypeError((0,s.A)(t,"CryptoKey","KeyObject","JSON Web Key"));return crypto.subtle.importKey("raw",t,{hash:`SHA-${e.slice(-3)}`,name:"HMAC"},!1,[r])}return!function(e,t,r){switch(t){case"HS256":case"HS384":case"HS512":{if(!i(e.algorithm,"HMAC"))throw a("HMAC");let r=parseInt(t.slice(2),10);if(o(e.algorithm.hash)!==r)throw a(`SHA-${r}`,"algorithm.hash");break}case"RS256":case"RS384":case"RS512":{if(!i(e.algorithm,"RSASSA-PKCS1-v1_5"))throw a("RSASSA-PKCS1-v1_5");let r=parseInt(t.slice(2),10);if(o(e.algorithm.hash)!==r)throw a(`SHA-${r}`,"algorithm.hash");break}case"PS256":case"PS384":case"PS512":{if(!i(e.algorithm,"RSA-PSS"))throw a("RSA-PSS");let r=parseInt(t.slice(2),10);if(o(e.algorithm.hash)!==r)throw a(`SHA-${r}`,"algorithm.hash");break}case"Ed25519":case"EdDSA":if(!i(e.algorithm,"Ed25519"))throw a("Ed25519");break;case"ES256":case"ES384":case"ES512":{if(!i(e.algorithm,"ECDSA"))throw a("ECDSA");let r=function(e){switch(e){case"ES256":return"P-256";case"ES384":return"P-384";case"ES512":return"P-521";default:throw Error("unreachable")}}(t);if(e.algorithm.namedCurve!==r)throw a(r,"algorithm.namedCurve");break}default:throw TypeError("CryptoKey does not support this operation")}(function(e,t){if(t&&!e.usages.includes(t))throw TypeError(`CryptoKey does not support this operation, its usages must include ${t}.`)})(e,r)}(t,e,r),t}},94724:(e,t,r)=>{function a(e,t,...r){if((r=r.filter(Boolean)).length>2){let t=r.pop();e+=`one of type ${r.join(", ")}, or ${t}.`}else 2===r.length?e+=`one of type ${r[0]} or ${r[1]}.`:e+=`of type ${r[0]}.`;return null==t?e+=` Received ${t}`:"function"==typeof t&&t.name?e+=` Received function ${t.name}`:"object"==typeof t&&null!=t&&t.constructor?.name&&(e+=` Received an instance of ${t.constructor.name}`),e}r.d(t,{A:()=>i,t:()=>o});let i=(e,...t)=>a("Key must be ",e,...t);function o(e,t,...r){return a(`Key for the ${e} algorithm must be `,t,...r)}},87121:(e,t,r)=>{r.d(t,{A:()=>a});let a=(...e)=>{let t;let r=e.filter(Boolean);if(0===r.length||1===r.length)return!0;for(let e of r){let r=Object.keys(e);if(!t||0===t.size){t=new Set(r);continue}for(let e of r){if(t.has(e))return!1;t.add(e)}}return!0}},6417:(e,t,r)=>{r.d(t,{M3:()=>s,W2:()=>o,ll:()=>i,t9:()=>n});var a=r(78882);function i(e){return(0,a.A)(e)&&"string"==typeof e.kty}function o(e){return"oct"!==e.kty&&"string"==typeof e.d}function s(e){return"oct"!==e.kty&&void 0===e.d}function n(e){return"oct"===e.kty&&"string"==typeof e.k}},14290:(e,t,r)=>{function a(e){return e?.[Symbol.toStringTag]==="CryptoKey"}function i(e){return e?.[Symbol.toStringTag]==="KeyObject"}r.d(t,{Ay:()=>o,D2:()=>i,R7:()=>a});let o=e=>a(e)||i(e)},78882:(e,t,r)=>{r.d(t,{A:()=>a});let a=e=>{if(!function(e){return"object"==typeof e&&null!==e}(e)||"[object Object]"!==Object.prototype.toString.call(e))return!1;if(null===Object.getPrototypeOf(e))return!0;let t=e;for(;null!==Object.getPrototypeOf(t);)t=Object.getPrototypeOf(t);return Object.getPrototypeOf(e)===t}},58388:(e,t,r)=>{r.d(t,{c:()=>h,k:()=>y});var a=r(31886),i=r(61526);let o=e=>Math.floor(e.getTime()/1e3),s=/^(\+|\-)? ?(\d+|\d+\.\d+) ?(seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)(?: (ago|from now))?$/i,n=e=>{let t;let r=s.exec(e);if(!r||r[4]&&r[1])throw TypeError("Invalid time period format");let a=parseFloat(r[2]);switch(r[3].toLowerCase()){case"sec":case"secs":case"second":case"seconds":case"s":t=Math.round(a);break;case"minute":case"minutes":case"min":case"mins":case"m":t=Math.round(60*a);break;case"hour":case"hours":case"hr":case"hrs":case"h":t=Math.round(3600*a);break;case"day":case"days":case"d":t=Math.round(86400*a);break;case"week":case"weeks":case"w":t=Math.round(604800*a);break;default:t=Math.round(0x1e187e0*a)}return"-"===r[1]||"ago"===r[4]?-t:t};var c=r(78882);function d(e,t){if(!Number.isFinite(t))throw TypeError(`Invalid ${e} input`);return t}let l=e=>e.includes("/")?e.toLowerCase():`application/${e.toLowerCase()}`,p=(e,t)=>"string"==typeof e?t.includes(e):!!Array.isArray(e)&&t.some(Set.prototype.has.bind(new Set(e)));function y(e,t,r={}){let s,d;try{s=JSON.parse(i.D0.decode(t))}catch{}if(!(0,c.A)(s))throw new a.Dp("JWT Claims Set must be a top-level JSON object");let{typ:h}=r;if(h&&("string"!=typeof e.typ||l(e.typ)!==l(h)))throw new a.ie('unexpected "typ" JWT header value',s,"typ","check_failed");let{requiredClaims:u=[],issuer:f,subject:m,audience:w,maxTokenAge:S}=r,b=[...u];for(let e of(void 0!==S&&b.push("iat"),void 0!==w&&b.push("aud"),void 0!==m&&b.push("sub"),void 0!==f&&b.push("iss"),new Set(b.reverse())))if(!(e in s))throw new a.ie(`missing required "${e}" claim`,s,e,"missing");if(f&&!(Array.isArray(f)?f:[f]).includes(s.iss))throw new a.ie('unexpected "iss" claim value',s,"iss","check_failed");if(m&&s.sub!==m)throw new a.ie('unexpected "sub" claim value',s,"sub","check_failed");if(w&&!p(s.aud,"string"==typeof w?[w]:w))throw new a.ie('unexpected "aud" claim value',s,"aud","check_failed");switch(typeof r.clockTolerance){case"string":d=n(r.clockTolerance);break;case"number":d=r.clockTolerance;break;case"undefined":d=0;break;default:throw TypeError("Invalid clockTolerance option type")}let{currentDate:g}=r,A=o(g||new Date);if((void 0!==s.iat||S)&&"number"!=typeof s.iat)throw new a.ie('"iat" claim must be a number',s,"iat","invalid");if(void 0!==s.nbf){if("number"!=typeof s.nbf)throw new a.ie('"nbf" claim must be a number',s,"nbf","invalid");if(s.nbf>A+d)throw new a.ie('"nbf" claim timestamp check failed',s,"nbf","check_failed")}if(void 0!==s.exp){if("number"!=typeof s.exp)throw new a.ie('"exp" claim must be a number',s,"exp","invalid");if(s.exp<=A-d)throw new a.n('"exp" claim timestamp check failed',s,"exp","check_failed")}if(S){let e=A-s.iat;if(e-d>("number"==typeof S?S:n(S)))throw new a.n('"iat" claim timestamp check failed (too far in the past)',s,"iat","check_failed");if(e<0-d)throw new a.ie('"iat" claim timestamp check failed (it should be in the past)',s,"iat","check_failed")}return s}class h{#e;constructor(e){if(!(0,c.A)(e))throw TypeError("JWT Claims Set MUST be an object");this.#e=structuredClone(e)}data(){return i.Rd.encode(JSON.stringify(this.#e))}get iss(){return this.#e.iss}set iss(e){this.#e.iss=e}get sub(){return this.#e.sub}set sub(e){this.#e.sub=e}get aud(){return this.#e.aud}set aud(e){this.#e.aud=e}set jti(e){this.#e.jti=e}set nbf(e){"number"==typeof e?this.#e.nbf=d("setNotBefore",e):e instanceof Date?this.#e.nbf=d("setNotBefore",o(e)):this.#e.nbf=o(new Date)+n(e)}set exp(e){"number"==typeof e?this.#e.exp=d("setExpirationTime",e):e instanceof Date?this.#e.exp=d("setExpirationTime",o(e)):this.#e.exp=o(new Date)+n(e)}set iat(e){void 0===e?this.#e.iat=o(new Date):e instanceof Date?this.#e.iat=d("setIssuedAt",o(e)):"string"==typeof e?this.#e.iat=d("setIssuedAt",o(new Date)+n(e)):this.#e.iat=d("setIssuedAt",e)}}},23088:(e,t,r)=>{let a;r.d(t,{A:()=>p});var i=r(6417),o=r(51551),s=r(31886);let n=async e=>{if(!e.alg)throw TypeError('"alg" argument is required when "jwk.alg" is not present');let{algorithm:t,keyUsages:r}=function(e){let t,r;switch(e.kty){case"RSA":switch(e.alg){case"PS256":case"PS384":case"PS512":t={name:"RSA-PSS",hash:`SHA-${e.alg.slice(-3)}`},r=e.d?["sign"]:["verify"];break;case"RS256":case"RS384":case"RS512":t={name:"RSASSA-PKCS1-v1_5",hash:`SHA-${e.alg.slice(-3)}`},r=e.d?["sign"]:["verify"];break;case"RSA-OAEP":case"RSA-OAEP-256":case"RSA-OAEP-384":case"RSA-OAEP-512":t={name:"RSA-OAEP",hash:`SHA-${parseInt(e.alg.slice(-3),10)||1}`},r=e.d?["decrypt","unwrapKey"]:["encrypt","wrapKey"];break;default:throw new s.T0('Invalid or unsupported JWK "alg" (Algorithm) Parameter value')}break;case"EC":switch(e.alg){case"ES256":t={name:"ECDSA",namedCurve:"P-256"},r=e.d?["sign"]:["verify"];break;case"ES384":t={name:"ECDSA",namedCurve:"P-384"},r=e.d?["sign"]:["verify"];break;case"ES512":t={name:"ECDSA",namedCurve:"P-521"},r=e.d?["sign"]:["verify"];break;case"ECDH-ES":case"ECDH-ES+A128KW":case"ECDH-ES+A192KW":case"ECDH-ES+A256KW":t={name:"ECDH",namedCurve:e.crv},r=e.d?["deriveBits"]:[];break;default:throw new s.T0('Invalid or unsupported JWK "alg" (Algorithm) Parameter value')}break;case"OKP":switch(e.alg){case"Ed25519":case"EdDSA":t={name:"Ed25519"},r=e.d?["sign"]:["verify"];break;case"ECDH-ES":case"ECDH-ES+A128KW":case"ECDH-ES+A192KW":case"ECDH-ES+A256KW":t={name:e.crv},r=e.d?["deriveBits"]:[];break;default:throw new s.T0('Invalid or unsupported JWK "alg" (Algorithm) Parameter value')}break;default:throw new s.T0('Invalid or unsupported JWK "kty" (Key Type) Parameter value')}return{algorithm:t,keyUsages:r}}(e),a={...e};return delete a.alg,delete a.use,crypto.subtle.importKey("jwk",a,t,e.ext??!e.d,e.key_ops??r)};var c=r(14290);let d=async(e,t,r,i=!1)=>{let o=(a||=new WeakMap).get(e);if(o?.[r])return o[r];let s=await n({...t,alg:r});return i&&Object.freeze(e),o?o[r]=s:a.set(e,{[r]:s}),s},l=(e,t)=>{let r;let i=(a||=new WeakMap).get(e);if(i?.[t])return i[t];let o="public"===e.type,s=!!o;if("x25519"===e.asymmetricKeyType){switch(t){case"ECDH-ES":case"ECDH-ES+A128KW":case"ECDH-ES+A192KW":case"ECDH-ES+A256KW":break;default:throw TypeError("given KeyObject instance cannot be used for this algorithm")}r=e.toCryptoKey(e.asymmetricKeyType,s,o?[]:["deriveBits"])}if("ed25519"===e.asymmetricKeyType){if("EdDSA"!==t&&"Ed25519"!==t)throw TypeError("given KeyObject instance cannot be used for this algorithm");r=e.toCryptoKey(e.asymmetricKeyType,s,[o?"verify":"sign"])}if("rsa"===e.asymmetricKeyType){let a;switch(t){case"RSA-OAEP":a="SHA-1";break;case"RS256":case"PS256":case"RSA-OAEP-256":a="SHA-256";break;case"RS384":case"PS384":case"RSA-OAEP-384":a="SHA-384";break;case"RS512":case"PS512":case"RSA-OAEP-512":a="SHA-512";break;default:throw TypeError("given KeyObject instance cannot be used for this algorithm")}if(t.startsWith("RSA-OAEP"))return e.toCryptoKey({name:"RSA-OAEP",hash:a},s,o?["encrypt"]:["decrypt"]);r=e.toCryptoKey({name:t.startsWith("PS")?"RSA-PSS":"RSASSA-PKCS1-v1_5",hash:a},s,[o?"verify":"sign"])}if("ec"===e.asymmetricKeyType){let a=new Map([["prime256v1","P-256"],["secp384r1","P-384"],["secp521r1","P-521"]]).get(e.asymmetricKeyDetails?.namedCurve);if(!a)throw TypeError("given KeyObject instance cannot be used for this algorithm");"ES256"===t&&"P-256"===a&&(r=e.toCryptoKey({name:"ECDSA",namedCurve:a},s,[o?"verify":"sign"])),"ES384"===t&&"P-384"===a&&(r=e.toCryptoKey({name:"ECDSA",namedCurve:a},s,[o?"verify":"sign"])),"ES512"===t&&"P-521"===a&&(r=e.toCryptoKey({name:"ECDSA",namedCurve:a},s,[o?"verify":"sign"])),t.startsWith("ECDH-ES")&&(r=e.toCryptoKey({name:"ECDH",namedCurve:a},s,o?[]:["deriveBits"]))}if(!r)throw TypeError("given KeyObject instance cannot be used for this algorithm");return i?i[t]=r:a.set(e,{[t]:r}),r},p=async(e,t)=>{if(e instanceof Uint8Array||(0,c.R7)(e))return e;if((0,c.D2)(e)){if("secret"===e.type)return e.export();if("toCryptoKey"in e&&"function"==typeof e.toCryptoKey)try{return l(e,t)}catch(e){if(e instanceof TypeError)throw e}let r=e.export({format:"jwk"});return d(e,r,t)}if((0,i.ll)(e))return e.k?(0,o.D)(e.k):d(e,e,t,!0);throw Error("unreachable")}},34008:(e,t,r)=>{r.d(t,{A:()=>i});var a=r(31886);let i=(e,t)=>{let r=`SHA-${e.slice(-3)}`;switch(e){case"HS256":case"HS384":case"HS512":return{hash:r,name:"HMAC"};case"PS256":case"PS384":case"PS512":return{hash:r,name:"RSA-PSS",saltLength:parseInt(e.slice(-3),10)>>3};case"RS256":case"RS384":case"RS512":return{hash:r,name:"RSASSA-PKCS1-v1_5"};case"ES256":case"ES384":case"ES512":return{hash:r,name:"ECDSA",namedCurve:t.namedCurve};case"Ed25519":case"EdDSA":return{name:"Ed25519"};default:throw new a.T0(`alg ${e} is not supported either by JOSE or your javascript runtime`)}}},20491:(e,t,r)=>{r.d(t,{A:()=>i});var a=r(31886);let i=(e,t,r,i,o)=>{let s;if(void 0!==o.crit&&i?.crit===void 0)throw new e('"crit" (Critical) Header Parameter MUST be integrity protected');if(!i||void 0===i.crit)return new Set;if(!Array.isArray(i.crit)||0===i.crit.length||i.crit.some(e=>"string"!=typeof e||0===e.length))throw new e('"crit" (Critical) Header Parameter MUST be an array of non-empty strings when present');for(let n of(s=void 0!==r?new Map([...Object.entries(r),...t.entries()]):t,i.crit)){if(!s.has(n))throw new a.T0(`Extension Header Parameter "${n}" is not recognized`);if(void 0===o[n])throw new e(`Extension Header Parameter "${n}" is missing`);if(s.get(n)&&void 0===i[n])throw new e(`Extension Header Parameter "${n}" MUST be integrity protected`)}return new Set(i.crit)}},51551:(e,t,r)=>{r.d(t,{D:()=>i,l:()=>o});var a=r(61526);function i(e){if(Uint8Array.fromBase64)return Uint8Array.fromBase64("string"==typeof e?e:a.D0.decode(e),{alphabet:"base64url"});let t=e;t instanceof Uint8Array&&(t=a.D0.decode(t)),t=t.replace(/-/g,"+").replace(/_/g,"/").replace(/\s/g,"");try{return function(e){if(Uint8Array.fromBase64)return Uint8Array.fromBase64(e);let t=atob(e),r=new Uint8Array(t.length);for(let e=0;e<t.length;e++)r[e]=t.charCodeAt(e);return r}(t)}catch{throw TypeError("The input to be decoded is not correctly encoded.")}}function o(e){let t=e;return("string"==typeof t&&(t=a.Rd.encode(t)),Uint8Array.prototype.toBase64)?t.toBase64({alphabet:"base64url",omitPadding:!0}):(function(e){if(Uint8Array.prototype.toBase64)return e.toBase64();let t=[];for(let r=0;r<e.length;r+=32768)t.push(String.fromCharCode.apply(null,e.subarray(r,r+32768)));return btoa(t.join(""))})(t).replace(/=/g,"").replace(/\+/g,"-").replace(/\//g,"_")}},31886:(e,t,r)=>{r.d(t,{Dp:()=>d,Rb:()=>s,T0:()=>n,Ye:()=>c,h2:()=>p,ie:()=>i,n:()=>o});class a extends Error{static code="ERR_JOSE_GENERIC";code="ERR_JOSE_GENERIC";constructor(e,t){super(e,t),this.name=this.constructor.name,Error.captureStackTrace?.(this,this.constructor)}}class i extends a{static code="ERR_JWT_CLAIM_VALIDATION_FAILED";code="ERR_JWT_CLAIM_VALIDATION_FAILED";claim;reason;payload;constructor(e,t,r="unspecified",a="unspecified"){super(e,{cause:{claim:r,reason:a,payload:t}}),this.claim=r,this.reason=a,this.payload=t}}class o extends a{static code="ERR_JWT_EXPIRED";code="ERR_JWT_EXPIRED";claim;reason;payload;constructor(e,t,r="unspecified",a="unspecified"){super(e,{cause:{claim:r,reason:a,payload:t}}),this.claim=r,this.reason=a,this.payload=t}}class s extends a{static code="ERR_JOSE_ALG_NOT_ALLOWED";code="ERR_JOSE_ALG_NOT_ALLOWED"}class n extends a{static code="ERR_JOSE_NOT_SUPPORTED";code="ERR_JOSE_NOT_SUPPORTED"}class c extends a{static code="ERR_JWS_INVALID";code="ERR_JWS_INVALID"}class d extends a{static code="ERR_JWT_INVALID";code="ERR_JWT_INVALID"}class l extends a{[Symbol.asyncIterator];static code="ERR_JWKS_MULTIPLE_MATCHING_KEYS";code="ERR_JWKS_MULTIPLE_MATCHING_KEYS";constructor(e="multiple matching keys found in the JSON Web Key Set",t){super(e,t)}}class p extends a{static code="ERR_JWS_SIGNATURE_VERIFICATION_FAILED";code="ERR_JWS_SIGNATURE_VERIFICATION_FAILED";constructor(e="signature verification failed",t){super(e,t)}}}}]);
//# sourceMappingURL=514.js.map