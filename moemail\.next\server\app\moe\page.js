(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[254],{65521:e=>{"use strict";e.exports=require("node:async_hooks")},25356:e=>{"use strict";e.exports=require("node:buffer")},7745:(e,t,r)=>{"use strict";r.r(t),r.d(t,{ComponentMod:()=>P,default:()=>E});var s,a={};r.r(a),r.d(a,{ClientPageRoot:()=>u.Fy,ClientSegmentRoot:()=>u.pl,GlobalError:()=>m.default,HTTPAccessFallbackBoundary:()=>u.nQ,LayoutRouter:()=>u.C3,MetadataBoundary:()=>u.qB,OutletBoundary:()=>u.Cr,Postpone:()=>u.fK,RenderFromTemplateContext:()=>u.IY,ViewportBoundary:()=>u.PX,__next_app__:()=>f,actionAsyncStorage:()=>u.sc,collectSegmentData:()=>u.Uy,createMetadataComponents:()=>u.IB,createPrerenderParamsForClientSegment:()=>u.lu,createPrerenderSearchParamsForClientPage:()=>u.jO,createServerParamsForMetadata:()=>u.Kx,createServerParamsForServerSegment:()=>u.LV,createServerSearchParamsForMetadata:()=>u.mh,createServerSearchParamsForServerPage:()=>u.Vv,createTemporaryReferenceSet:()=>u.XI,decodeAction:()=>u.Jk,decodeFormState:()=>u.Am,decodeReply:()=>u.X$,pages:()=>x,patchFetch:()=>u.V5,preconnect:()=>u.kZ,preloadFont:()=>u.PY,preloadStyle:()=>u.vI,prerender:()=>u.CR,renderToReadableStream:()=>u.WK,routeModule:()=>h,serverHooks:()=>u.ge,taintObjectReference:()=>u.N2,tree:()=>p,workAsyncStorage:()=>u.J_,workUnitAsyncStorage:()=>u.FP}),r(79556);var i=r(19022),n=r(58661),l=r(20809),o=r(11939),c=r(13348),d=r(14139),m=r(86867),u=r(71102);let p=["",{children:["moe",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,90628)),"F:\\CODE\\Project\\tempmail\\moemail\\app\\moe\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,27711)),"F:\\CODE\\Project\\tempmail\\moemail\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,64913)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.bind(r,2412)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.bind(r,77757)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],x=["F:\\CODE\\Project\\tempmail\\moemail\\app\\moe\\page.tsx"],f={require:r,loadChunk:()=>Promise.resolve()},h=new c.AppPageRouteModule({definition:{kind:d.A.APP_PAGE,page:"/moe/page",pathname:"/moe",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:p}});var v=r(18058),g=r(72495),j=r(75376);globalThis.__nextCacheHandlers||(globalThis.__nextCacheHandlers={});let y=e=>e?JSON.parse(e):void 0,N=self.__BUILD_MANIFEST,b=y(self.__REACT_LOADABLE_MANIFEST),w=null==(s=self.__RSC_MANIFEST)?void 0:s["/moe/page"],C=y(self.__RSC_SERVER_MANIFEST),S=y(self.__NEXT_FONT_MANIFEST),A=y(self.__INTERCEPTION_ROUTE_REWRITE_MANIFEST)??[];w&&C&&(0,g.fQ)({page:"/moe/page",clientReferenceManifest:w,serverActionsManifest:C,serverModuleMap:(0,j.e)({serverActionsManifest:C})});let k=(0,n.R)({pagesType:v.g.APP,dev:!1,page:"/moe/page",appMod:null,pageMod:a,errorMod:null,error500Mod:null,Document:null,buildManifest:N,renderToHTML:o.W,reactLoadableManifest:b,clientReferenceManifest:w,serverActionsManifest:C,serverActions:void 0,subresourceIntegrityManifest:void 0,config:{env:{},eslint:{ignoreDuringBuilds:!1},typescript:{ignoreBuildErrors:!1,tsconfigPath:"tsconfig.json"},distDir:".next",cleanDistDir:!0,assetPrefix:"",cacheMaxMemorySize:0x3200000,configOrigin:"next.config.ts",useFileSystemPublicRoutes:!0,generateEtags:!0,pageExtensions:["tsx","ts","jsx","js"],poweredByHeader:!0,compress:!0,images:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",loaderFile:"",domains:[],disableStaticImages:!1,minimumCacheTTL:60,formats:["image/webp"],dangerouslyAllowSVG:!1,contentSecurityPolicy:"script-src 'none'; frame-src 'none'; sandbox;",contentDispositionType:"attachment",remotePatterns:[{protocol:"https",hostname:"avatars.githubusercontent.com"}],unoptimized:!1},devIndicators:{appIsrStatus:!0,buildActivity:!0,buildActivityPosition:"bottom-right"},onDemandEntries:{maxInactiveAge:6e4,pagesBufferLength:5},amp:{canonicalBase:""},basePath:"",sassOptions:{},trailingSlash:!1,i18n:null,productionBrowserSourceMaps:!1,excludeDefaultMomentLocales:!0,serverRuntimeConfig:{},publicRuntimeConfig:{},reactProductionProfiling:!1,reactStrictMode:null,reactMaxHeadersLength:6e3,httpAgentOptions:{keepAlive:!0},logging:{},expireTime:31536e3,staticPageGenerationTimeout:60,modularizeImports:{"@mui/icons-material":{transform:"@mui/icons-material/{{member}}"},lodash:{transform:"lodash/{{member}}"}},outputFileTracingRoot:"F:\\CODE\\Project\\tempmail\\moemail",experimental:{cacheLife:{default:{stale:300,revalidate:900,expire:0xfffffffe},seconds:{stale:0,revalidate:1,expire:60},minutes:{stale:300,revalidate:60,expire:3600},hours:{stale:300,revalidate:3600,expire:86400},days:{stale:300,revalidate:86400,expire:604800},weeks:{stale:300,revalidate:604800,expire:2592e3},max:{stale:300,revalidate:2592e3,expire:0xfffffffe}},cacheHandlers:{},cssChunking:!0,multiZoneDraftMode:!1,appNavFailHandling:!1,prerenderEarlyExit:!0,serverMinification:!0,serverSourceMaps:!1,linkNoTouchStart:!1,caseSensitiveRoutes:!1,clientSegmentCache:!1,preloadEntriesOnStart:!0,clientRouterFilter:!0,clientRouterFilterRedirects:!1,fetchCacheKeyPrefix:"",middlewarePrefetch:"flexible",optimisticClientCache:!0,manualClientBasePath:!1,cpus:11,memoryBasedWorkersCount:!1,imgOptConcurrency:null,imgOptTimeoutInSeconds:7,imgOptMaxInputPixels:0xfff8001,imgOptSequentialRead:null,isrFlushToDisk:!0,workerThreads:!1,optimizeCss:!1,nextScriptWorkers:!1,scrollRestoration:!1,externalDir:!1,disableOptimizedLoading:!1,gzipSize:!0,craCompat:!1,esmExternals:!0,fullySpecified:!1,swcTraceProfiling:!1,forceSwcTransforms:!1,largePageDataBytes:128e3,turbo:{root:"F:\\CODE\\Project\\tempmail\\moemail"},typedRoutes:!1,typedEnv:!1,parallelServerCompiles:!1,parallelServerBuildTraces:!1,ppr:!1,authInterrupts:!1,reactOwnerStack:!1,webpackMemoryOptimizations:!1,optimizeServerReact:!0,useEarlyImport:!1,staleTimes:{dynamic:0,static:300},serverComponentsHmrCache:!0,staticGenerationMaxConcurrency:8,staticGenerationMinPagesPerWorker:25,dynamicIO:!1,inlineCss:!1,optimizePackageImports:["lucide-react","date-fns","lodash-es","ramda","antd","react-bootstrap","ahooks","@ant-design/icons","@headlessui/react","@headlessui-float/react","@heroicons/react/20/solid","@heroicons/react/24/solid","@heroicons/react/24/outline","@visx/visx","@tremor/react","rxjs","@mui/material","@mui/icons-material","recharts","react-use","effect","@effect/schema","@effect/platform","@effect/platform-node","@effect/platform-browser","@effect/platform-bun","@effect/sql","@effect/sql-mssql","@effect/sql-mysql2","@effect/sql-pg","@effect/sql-squlite-node","@effect/sql-squlite-bun","@effect/sql-squlite-wasm","@effect/sql-squlite-react-native","@effect/rpc","@effect/rpc-http","@effect/typeclass","@effect/experimental","@effect/opentelemetry","@material-ui/core","@material-ui/icons","@tabler/icons-react","mui-core","react-icons/ai","react-icons/bi","react-icons/bs","react-icons/cg","react-icons/ci","react-icons/di","react-icons/fa","react-icons/fa6","react-icons/fc","react-icons/fi","react-icons/gi","react-icons/go","react-icons/gr","react-icons/hi","react-icons/hi2","react-icons/im","react-icons/io","react-icons/io5","react-icons/lia","react-icons/lib","react-icons/lu","react-icons/md","react-icons/pi","react-icons/ri","react-icons/rx","react-icons/si","react-icons/sl","react-icons/tb","react-icons/tfi","react-icons/ti","react-icons/vsc","react-icons/wi"]},bundlePagesRouterDependencies:!1,configFile:"F:\\CODE\\Project\\tempmail\\moemail\\next.config.ts",configFileName:"next.config.ts"},buildId:process.env.__NEXT_BUILD_ID,nextFontManifest:S,incrementalCacheHandler:null,interceptionRouteRewrites:A}),P=a;function E(e){return(0,i.O)({...e,IncrementalCache:l.N,handler:k})}},57656:(e,t,r)=>{Promise.resolve().then(r.bind(r,44245)),Promise.resolve().then(r.bind(r,37470)),Promise.resolve().then(r.bind(r,1422)),Promise.resolve().then(r.bind(r,34980)),Promise.resolve().then(r.bind(r,26336))},67384:(e,t,r)=>{Promise.resolve().then(r.bind(r,37357)),Promise.resolve().then(r.bind(r,81312)),Promise.resolve().then(r.bind(r,90494)),Promise.resolve().then(r.bind(r,56588)),Promise.resolve().then(r.bind(r,52744))},81312:(e,t,r)=>{"use strict";r.d(t,{ThreeColumnLayout:()=>tn});var s=r(37785),a=r(57845),i=r(31648),n=r(277),l=r(70937),o=r(12181),c=r(11965),d=r(18866),m=r(37800),u=r(37062);let p=(e=21)=>{let t="",r=crypto.getRandomValues(new Uint8Array(e|=0));for(;e--;)t+="useandom-26T198340PX75pxJACKVERYMINDBUSHWOLF_GQZbfghjklqvwyzrict"[63&r[e]];return t};var x=r(19816),f=r(53852),h=r(87496),v=r(39706),g=r(42605),j=r(65244),y=r(98272),N=r(20812),b=a.createContext(void 0);function w(e){let t=a.useContext(b);return e||t||"ltr"}var C="rovingFocusGroup.onEntryFocus",S={bubbles:!1,cancelable:!0},A="RovingFocusGroup",[k,P,E]=(0,g.N)(A),[R,I]=(0,h.A)(A,[E]),[T,D]=R(A),F=a.forwardRef((e,t)=>(0,s.jsx)(k.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,s.jsx)(k.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,s.jsx)(_,{...e,ref:t})})}));F.displayName=A;var _=a.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:r,orientation:i,loop:n=!1,dir:l,currentTabStopId:o,defaultCurrentTabStopId:c,onCurrentTabStopIdChange:d,onEntryFocus:m,preventScrollOnEntryFocus:u=!1,...p}=e,h=a.useRef(null),g=(0,f.s)(t,h),j=w(l),[b=null,A]=(0,N.i)({prop:o,defaultProp:c,onChange:d}),[k,E]=a.useState(!1),R=(0,y.c)(m),I=P(r),D=a.useRef(!1),[F,_]=a.useState(0);return a.useEffect(()=>{let e=h.current;if(e)return e.addEventListener(C,R),()=>e.removeEventListener(C,R)},[R]),(0,s.jsx)(T,{scope:r,orientation:i,dir:j,loop:n,currentTabStopId:b,onItemFocus:a.useCallback(e=>A(e),[A]),onItemShiftTab:a.useCallback(()=>E(!0),[]),onFocusableItemAdd:a.useCallback(()=>_(e=>e+1),[]),onFocusableItemRemove:a.useCallback(()=>_(e=>e-1),[]),children:(0,s.jsx)(v.sG.div,{tabIndex:k||0===F?-1:0,"data-orientation":i,...p,ref:g,style:{outline:"none",...e.style},onMouseDown:(0,x.m)(e.onMouseDown,()=>{D.current=!0}),onFocus:(0,x.m)(e.onFocus,e=>{let t=!D.current;if(e.target===e.currentTarget&&t&&!k){let t=new CustomEvent(C,S);if(e.currentTarget.dispatchEvent(t),!t.defaultPrevented){let e=I().filter(e=>e.focusable);$([e.find(e=>e.active),e.find(e=>e.id===b),...e].filter(Boolean).map(e=>e.ref.current),u)}}D.current=!1}),onBlur:(0,x.m)(e.onBlur,()=>E(!1))})})}),M="RovingFocusGroupItem",O=a.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:r,focusable:i=!0,active:n=!1,tabStopId:l,...o}=e,c=(0,j.B)(),d=l||c,m=D(M,r),u=m.currentTabStopId===d,p=P(r),{onFocusableItemAdd:f,onFocusableItemRemove:h}=m;return a.useEffect(()=>{if(i)return f(),()=>h()},[i,f,h]),(0,s.jsx)(k.ItemSlot,{scope:r,id:d,focusable:i,active:n,children:(0,s.jsx)(v.sG.span,{tabIndex:u?0:-1,"data-orientation":m.orientation,...o,ref:t,onMouseDown:(0,x.m)(e.onMouseDown,e=>{i?m.onItemFocus(d):e.preventDefault()}),onFocus:(0,x.m)(e.onFocus,()=>m.onItemFocus(d)),onKeyDown:(0,x.m)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey){m.onItemShiftTab();return}if(e.target!==e.currentTarget)return;let t=function(e,t,r){var s;let a=(s=e.key,"rtl"!==r?s:"ArrowLeft"===s?"ArrowRight":"ArrowRight"===s?"ArrowLeft":s);if(!("vertical"===t&&["ArrowLeft","ArrowRight"].includes(a))&&!("horizontal"===t&&["ArrowUp","ArrowDown"].includes(a)))return L[a]}(e,m.orientation,m.dir);if(void 0!==t){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let r=p().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===t)r.reverse();else if("prev"===t||"next"===t){"prev"===t&&r.reverse();let s=r.indexOf(e.currentTarget);r=m.loop?function(e,t){return e.map((r,s)=>e[(t+s)%e.length])}(r,s+1):r.slice(s+1)}setTimeout(()=>$(r))}})})})});O.displayName=M;var L={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function $(e,t=!1){let r=document.activeElement;for(let s of e)if(s===r||(s.focus({preventScroll:t}),document.activeElement!==r))return}var z=r(67870),q=r(60834),G=r(34192),B="Radio",[H,V]=(0,h.A)(B),[J,U]=H(B),K=a.forwardRef((e,t)=>{let{__scopeRadio:r,name:i,checked:n=!1,required:l,disabled:o,value:c="on",onCheck:d,form:m,...u}=e,[p,h]=a.useState(null),g=(0,f.s)(t,e=>h(e)),j=a.useRef(!1),y=!p||m||!!p.closest("form");return(0,s.jsxs)(J,{scope:r,checked:n,disabled:o,children:[(0,s.jsx)(v.sG.button,{type:"button",role:"radio","aria-checked":n,"data-state":Z(n),"data-disabled":o?"":void 0,disabled:o,value:c,...u,ref:g,onClick:(0,x.m)(e.onClick,e=>{n||d?.(),y&&(j.current=e.isPropagationStopped(),j.current||e.stopPropagation())})}),y&&(0,s.jsx)(W,{control:p,bubbles:!j.current,name:i,value:c,checked:n,required:l,disabled:o,form:m,style:{transform:"translateX(-100%)"}})]})});K.displayName=B;var X="RadioIndicator",Y=a.forwardRef((e,t)=>{let{__scopeRadio:r,forceMount:a,...i}=e,n=U(X,r);return(0,s.jsx)(G.C,{present:a||n.checked,children:(0,s.jsx)(v.sG.span,{"data-state":Z(n.checked),"data-disabled":n.disabled?"":void 0,...i,ref:t})})});Y.displayName=X;var W=e=>{let{control:t,checked:r,bubbles:i=!0,...n}=e,l=a.useRef(null),o=(0,q.Z)(r),c=(0,z.X)(t);return a.useEffect(()=>{let e=l.current,t=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(o!==r&&t){let s=new Event("click",{bubbles:i});t.call(e,r),e.dispatchEvent(s)}},[o,r,i]),(0,s.jsx)("input",{type:"radio","aria-hidden":!0,defaultChecked:r,...n,tabIndex:-1,ref:l,style:{...e.style,...c,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})};function Z(e){return e?"checked":"unchecked"}var Q=["ArrowUp","ArrowDown","ArrowLeft","ArrowRight"],ee="RadioGroup",[et,er]=(0,h.A)(ee,[I,V]),es=I(),ea=V(),[ei,en]=et(ee),el=a.forwardRef((e,t)=>{let{__scopeRadioGroup:r,name:a,defaultValue:i,value:n,required:l=!1,disabled:o=!1,orientation:c,dir:d,loop:m=!0,onValueChange:u,...p}=e,x=es(r),f=w(d),[h,g]=(0,N.i)({prop:n,defaultProp:i,onChange:u});return(0,s.jsx)(ei,{scope:r,name:a,required:l,disabled:o,value:h,onValueChange:g,children:(0,s.jsx)(F,{asChild:!0,...x,orientation:c,dir:f,loop:m,children:(0,s.jsx)(v.sG.div,{role:"radiogroup","aria-required":l,"aria-orientation":c,"data-disabled":o?"":void 0,dir:f,...p,ref:t})})})});el.displayName=ee;var eo="RadioGroupItem",ec=a.forwardRef((e,t)=>{let{__scopeRadioGroup:r,disabled:i,...n}=e,l=en(eo,r),o=l.disabled||i,c=es(r),d=ea(r),m=a.useRef(null),u=(0,f.s)(t,m),p=l.value===n.value,h=a.useRef(!1);return a.useEffect(()=>{let e=e=>{Q.includes(e.key)&&(h.current=!0)},t=()=>h.current=!1;return document.addEventListener("keydown",e),document.addEventListener("keyup",t),()=>{document.removeEventListener("keydown",e),document.removeEventListener("keyup",t)}},[]),(0,s.jsx)(O,{asChild:!0,...c,focusable:!o,active:p,children:(0,s.jsx)(K,{disabled:o,required:l.required,checked:p,...d,...n,name:l.name,ref:u,onCheck:()=>l.onValueChange(n.value),onKeyDown:(0,x.m)(e=>{"Enter"===e.key&&e.preventDefault()}),onFocus:(0,x.m)(n.onFocus,()=>{h.current&&m.current?.click()})})})});ec.displayName=eo;var ed=a.forwardRef((e,t)=>{let{__scopeRadioGroup:r,...a}=e,i=ea(r);return(0,s.jsx)(Y,{...i,...a,ref:t})});ed.displayName="RadioGroupIndicator";var em=r(93823);let eu=(0,em.A)("Circle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]]);var ep=r(39086);let ex=a.forwardRef(({className:e,...t},r)=>(0,s.jsx)(el,{className:(0,ep.cn)("grid gap-2",e),...t,ref:r}));ex.displayName=el.displayName;let ef=a.forwardRef(({className:e,...t},r)=>(0,s.jsx)(ec,{ref:r,className:(0,ep.cn)("aspect-square h-4 w-4 rounded-full border border-primary text-primary ring-offset-background focus:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),...t,children:(0,s.jsx)(ed,{className:"flex items-center justify-center",children:(0,s.jsx)(eu,{className:"h-2.5 w-2.5 fill-current text-current"})})}));ef.displayName=ec.displayName;var eh=r(89515),ev=r(67213);let eg=[{label:"1小时",value:36e5},{label:"24小时",value:864e5},{label:"3天",value:2592e5},{label:"永久",value:0}];var ej=r(6917),ey=r(32290);function eN({onEmailCreated:e}){let{config:t}=(0,ey.U)(),[r,i]=(0,a.useState)(!1),[x,f]=(0,a.useState)(!1),[h,v]=(0,a.useState)(""),[g,j]=(0,a.useState)(""),[y,N]=(0,a.useState)(eg[1].value.toString()),{toast:b}=(0,u.dj)(),{copyToClipboard:w}=(0,ej.T)(),C=async()=>{if(!h.trim()){b({title:"错误",description:"请输入邮箱名",variant:"destructive"});return}f(!0);try{let t=await fetch("/api/emails/generate",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({name:h,domain:g,expiryTime:parseInt(y)})});if(!t.ok){let e=await t.json();b({title:"错误",description:e.error,variant:"destructive"});return}b({title:"成功",description:"已创建新的临时邮箱"}),e(),i(!1),v("")}catch{b({title:"错误",description:"创建邮箱失败",variant:"destructive"})}finally{f(!1)}};return(0,s.jsxs)(o.lG,{open:r,onOpenChange:i,children:[(0,s.jsx)(o.zM,{asChild:!0,children:(0,s.jsxs)(n.$,{className:"gap-2",children:[(0,s.jsx)(c.A,{className:"w-4 h-4"}),"创建新邮箱"]})}),(0,s.jsxs)(o.Cf,{children:[(0,s.jsx)(o.c7,{children:(0,s.jsx)(o.L3,{children:"创建新的临时邮箱"})}),(0,s.jsxs)("div",{className:"space-y-4 py-4",children:[(0,s.jsxs)("div",{className:"flex gap-2",children:[(0,s.jsx)(l.p,{value:h,onChange:e=>v(e.target.value),placeholder:"输入邮箱名",className:"flex-1"}),(t?.emailDomainsArray?.length??0)>1&&(0,s.jsxs)(ev.l6,{value:g,onValueChange:j,children:[(0,s.jsx)(ev.bq,{className:"w-[180px]",children:(0,s.jsx)(ev.yv,{})}),(0,s.jsx)(ev.gC,{children:t?.emailDomainsArray?.map(e=>s.jsxs(ev.eb,{value:e,children:["@",e]},e))})]}),(0,s.jsx)(n.$,{variant:"outline",size:"icon",onClick:()=>v(p(8)),type:"button",children:(0,s.jsx)(d.A,{className:"w-4 h-4"})})]}),(0,s.jsxs)("div",{className:"flex items-center gap-4",children:[(0,s.jsx)(eh.J,{className:"shrink-0 text-muted-foreground",children:"过期时间"}),(0,s.jsx)(ex,{value:y,onValueChange:N,className:"flex gap-6",children:eg.map(e=>(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(ef,{value:e.value.toString(),id:e.value.toString()}),(0,s.jsx)(eh.J,{htmlFor:e.value.toString(),className:"cursor-pointer text-sm",children:e.label})]},e.value))})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2 text-sm text-muted-foreground",children:[(0,s.jsx)("span",{className:"shrink-0",children:"完整邮箱地址将为:"}),h?(0,s.jsxs)("div",{className:"flex items-center gap-2 min-w-0",children:[(0,s.jsx)("span",{className:"truncate",children:`${h}@${g}`}),(0,s.jsx)("div",{className:"shrink-0 cursor-pointer hover:text-primary transition-colors",onClick:()=>{w(`${h}@${g}`)},children:(0,s.jsx)(m.A,{className:"size-4"})})]}):(0,s.jsx)("span",{className:"text-gray-400",children:"..."})]})]}),(0,s.jsxs)("div",{className:"flex justify-end gap-2",children:[(0,s.jsx)(n.$,{variant:"outline",onClick:()=>i(!1),disabled:x,children:"取消"}),(0,s.jsx)(n.$,{onClick:C,disabled:x,children:x?"创建中...":"创建"})]})]})]})}let eb=(0,em.A)("Link",[["path",{d:"M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71",key:"1cjeqo"}],["path",{d:"M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71",key:"19qd67"}]]);var ew=r(88563);function eC({onEmailBound:e}){let[t,r]=(0,a.useState)(!1),[i,c]=(0,a.useState)(!1),[d,m]=(0,a.useState)({jwt:""}),{toast:p}=(0,u.dj)(),x=async t=>{if(t.preventDefault(),!d.jwt.trim()){p({title:"请输入邮箱凭证",description:"邮箱凭证不能为空",variant:"destructive"});return}c(!0);try{let t=await fetch("/api/email-credentials",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(d)});if(!t.ok){let e=await t.json();throw Error(e.error||"绑定邮箱失败")}p({title:"绑定成功",description:"邮箱绑定成功"}),m({jwt:""}),r(!1),e()}catch(e){p({title:"绑定失败",description:e instanceof Error?e.message:"请稍后重试",variant:"destructive"})}finally{c(!1)}};return(0,s.jsxs)(o.lG,{open:t,onOpenChange:r,children:[(0,s.jsx)(o.zM,{asChild:!0,children:(0,s.jsxs)(n.$,{variant:"outline",size:"sm",className:"gap-1",children:[(0,s.jsx)(eb,{className:"h-3 w-3"}),"绑定邮箱"]})}),(0,s.jsxs)(o.Cf,{className:"max-w-md",children:[(0,s.jsxs)(o.c7,{children:[(0,s.jsx)(o.L3,{children:"绑定邮箱"}),(0,s.jsx)(o.rr,{children:"输入邮箱凭证来绑定邮箱到您的账户"})]}),(0,s.jsxs)("form",{onSubmit:x,className:"space-y-4",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(eh.J,{htmlFor:"jwt",children:"邮箱凭证 (JWT)"}),(0,s.jsx)(l.p,{id:"jwt",value:d.jwt,onChange:e=>m(t=>({...t,jwt:e.target.value})),placeholder:"请输入邮箱凭证...",required:!0})]}),(0,s.jsxs)("div",{className:"rounded-lg bg-blue-50 p-3 text-sm text-blue-800",children:[(0,s.jsx)("p",{className:"font-medium mb-1",children:"提示："}),(0,s.jsxs)("ul",{className:"space-y-1 text-xs",children:[(0,s.jsx)("li",{children:"• 邮箱凭证由管理员提供"}),(0,s.jsx)("li",{children:"• 每个邮箱对应唯一的凭证"}),(0,s.jsx)("li",{children:"• 绑定后可以接收该邮箱的邮件"}),(0,s.jsx)("li",{children:"• 支持同时绑定多个邮箱"})]})]}),(0,s.jsxs)(o.Es,{children:[(0,s.jsx)(n.$,{type:"button",variant:"outline",onClick:()=>{r(!1),m({jwt:""})},disabled:i,children:"取消"}),(0,s.jsx)(n.$,{type:"submit",disabled:i,children:i?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(ew.A,{className:"w-4 h-4 animate-spin mr-2"}),"绑定中..."]}):"绑定邮箱"})]})]})]})]})}var eS=r(84690),eA=r(44505);function ek({emailAddress:e,emailType:t,credentialId:r}){let[i,c]=(0,a.useState)(!1),[d,p]=(0,a.useState)(!1),[x,f]=(0,a.useState)(""),[h,v]=(0,a.useState)(!1),{toast:g}=(0,u.dj)(),j=async()=>{p(!0);try{let s="";if("bound"===t&&r){let e=await fetch(`/api/email-credentials/${r}`);if(!e.ok){let t=await e.json();throw Error(t.error||"获取凭证失败")}s=(await e.json()).jwt}else{let t=await fetch("/api/email-credentials/create",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({emailAddress:e})});if(!t.ok){let e=await t.json();throw Error(e.error||"创建凭证失败")}s=(await t.json()).jwt}f(s),g({title:"成功",description:"邮箱凭证提取成功"})}catch(e){console.error("Failed to extract credential:",e),g({title:"错误",description:e instanceof Error?e.message:"提取凭证失败",variant:"destructive"})}finally{p(!1)}},y=async()=>{try{await navigator.clipboard.writeText(x),v(!0),g({title:"已复制",description:"邮箱凭证已复制到剪贴板"}),setTimeout(()=>v(!1),2e3)}catch(e){g({title:"复制失败",description:"请手动复制凭证",variant:"destructive"})}},N=()=>{c(!1),f(""),v(!1)};return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(n.$,{variant:"ghost",size:"icon",className:"h-8 w-8",onClick:t=>{t.stopPropagation(),console.log("Extract credential button clicked for:",e),console.log("Setting open to true, current open state:",i),c(!0)},title:"提取邮箱凭证",children:(0,s.jsx)(eS.A,{className:"h-4 w-4 text-blue-600"})}),(0,s.jsx)(o.lG,{open:i,onOpenChange:e=>{console.log("Dialog onOpenChange called with:",e),e||N()},children:(0,s.jsxs)(o.Cf,{className:"sm:max-w-md",children:[(0,s.jsxs)(o.c7,{children:[(0,s.jsx)(o.L3,{children:"提取邮箱凭证"}),(0,s.jsxs)(o.rr,{children:["为邮箱 ",(0,s.jsx)("span",{className:"font-mono text-sm bg-gray-100 px-1 rounded",children:e})," 提取API访问凭证"]})]}),(0,s.jsx)("div",{className:"space-y-4",children:x?(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)(eh.J,{htmlFor:"credential",children:"邮箱凭证 (JWT Token)"}),(0,s.jsxs)("div",{className:"flex gap-2 mt-1",children:[(0,s.jsx)(l.p,{id:"credential",value:x,readOnly:!0,className:"font-mono text-xs"}),(0,s.jsx)(n.$,{variant:"outline",size:"icon",onClick:y,className:"flex-shrink-0",children:h?(0,s.jsx)(eA.A,{className:"h-4 w-4 text-green-600"}):(0,s.jsx)(m.A,{className:"h-4 w-4"})})]})]}),(0,s.jsxs)("div",{className:"text-xs text-gray-500 space-y-1",children:[(0,s.jsx)("p",{children:(0,s.jsx)("strong",{children:"使用方法："})}),(0,s.jsx)("p",{children:"在API请求头中添加："}),(0,s.jsxs)("code",{className:"block bg-gray-100 p-2 rounded text-xs",children:["X-Email-Credential: ",x.substring(0,20),"..."]})]})]}):(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)(n.$,{onClick:j,disabled:d,className:"w-full",children:d?"提取中...":"提取凭证"}),(0,s.jsx)("p",{className:"text-xs text-gray-500 mt-2",children:"此凭证可用于API访问该邮箱的邮件"})]})})]})})]})}var eP=r(43178),eE=r(75456);function eR(e,t){let r=(0,a.useRef)(Date.now());return(0,a.useCallback)((...s)=>{let a=Date.now();a-r.current>=t&&(e(...s),r.current=a)},[e,t])}var eI=r(32061);function eT(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function eD(...e){return a.useCallback(function(...e){return t=>{let r=!1,s=e.map(e=>{let s=eT(e,t);return r||"function"!=typeof s||(r=!0),s});if(r)return()=>{for(let t=0;t<s.length;t++){let r=s[t];"function"==typeof r?r():eT(e[t],null)}}}}(...e),e)}var eF=r(93185),e_=Symbol("radix.slottable"),eM="AlertDialog",[eO,eL]=function(e,t=[]){let r=[],i=()=>{let t=r.map(e=>a.createContext(e));return function(r){let s=r?.[e]||t;return a.useMemo(()=>({[`__scope${e}`]:{...r,[e]:s}}),[r,s])}};return i.scopeName=e,[function(t,i){let n=a.createContext(i),l=r.length;r=[...r,i];let o=t=>{let{scope:r,children:i,...o}=t,c=r?.[e]?.[l]||n,d=a.useMemo(()=>o,Object.values(o));return(0,s.jsx)(c.Provider,{value:d,children:i})};return o.displayName=t+"Provider",[o,function(r,s){let o=s?.[e]?.[l]||n,c=a.useContext(o);if(c)return c;if(void 0!==i)return i;throw Error(`\`${r}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let r=()=>{let r=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let s=r.reduce((t,{useScope:r,scopeName:s})=>{let a=r(e)[`__scope${s}`];return{...t,...a}},{});return a.useMemo(()=>({[`__scope${t.scopeName}`]:s}),[s])}};return r.scopeName=t.scopeName,r}(i,...t)]}(eM,[eF.Hs]),e$=(0,eF.Hs)(),ez=e=>{let{__scopeAlertDialog:t,...r}=e,a=e$(t);return(0,s.jsx)(eF.bL,{...a,...r,modal:!0})};ez.displayName=eM,a.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...a}=e,i=e$(r);return(0,s.jsx)(eF.l9,{...i,...a,ref:t})}).displayName="AlertDialogTrigger";var eq=e=>{let{__scopeAlertDialog:t,...r}=e,a=e$(t);return(0,s.jsx)(eF.ZL,{...a,...r})};eq.displayName="AlertDialogPortal";var eG=a.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...a}=e,i=e$(r);return(0,s.jsx)(eF.hJ,{...i,...a,ref:t})});eG.displayName="AlertDialogOverlay";var eB="AlertDialogContent",[eH,eV]=eO(eB),eJ=function(e){let t=({children:e})=>(0,s.jsx)(s.Fragment,{children:e});return t.displayName=`${e}.Slottable`,t.__radixId=e_,t}("AlertDialogContent"),eU=a.forwardRef((e,t)=>{let{__scopeAlertDialog:r,children:i,...n}=e,l=e$(r),o=a.useRef(null),c=eD(t,o),d=a.useRef(null);return(0,s.jsx)(eF.G$,{contentName:eB,titleName:eK,docsSlug:"alert-dialog",children:(0,s.jsx)(eH,{scope:r,cancelRef:d,children:(0,s.jsxs)(eF.UC,{role:"alertdialog",...l,...n,ref:c,onOpenAutoFocus:function(e,t,{checkForDefaultPrevented:r=!0}={}){return function(s){if(e?.(s),!1===r||!s.defaultPrevented)return t?.(s)}}(n.onOpenAutoFocus,e=>{e.preventDefault(),d.current?.focus({preventScroll:!0})}),onPointerDownOutside:e=>e.preventDefault(),onInteractOutside:e=>e.preventDefault(),children:[(0,s.jsx)(eJ,{children:i}),(0,s.jsx)(e1,{contentRef:o})]})})})});eU.displayName=eB;var eK="AlertDialogTitle",eX=a.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...a}=e,i=e$(r);return(0,s.jsx)(eF.hE,{...i,...a,ref:t})});eX.displayName=eK;var eY="AlertDialogDescription",eW=a.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...a}=e,i=e$(r);return(0,s.jsx)(eF.VY,{...i,...a,ref:t})});eW.displayName=eY;var eZ=a.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...a}=e,i=e$(r);return(0,s.jsx)(eF.bm,{...i,...a,ref:t})});eZ.displayName="AlertDialogAction";var eQ="AlertDialogCancel",e0=a.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...a}=e,{cancelRef:i}=eV(eQ,r),n=e$(r),l=eD(t,i);return(0,s.jsx)(eF.bm,{...n,...a,ref:l})});e0.displayName=eQ;var e1=({contentRef:e})=>{let t=`\`${eB}\` requires a description for the component to be accessible for screen reader users.

You can add a description to the \`${eB}\` by passing a \`${eY}\` component as a child, which also benefits sighted users by adding visible context to the dialog.

Alternatively, you can use your own component as a description by assigning it an \`id\` and passing the same value to the \`aria-describedby\` prop in \`${eB}\`. If the description is confusing or duplicative for sighted users, you can use the \`@radix-ui/react-visually-hidden\` primitive as a wrapper around your description component.

For more information, see https://radix-ui.com/primitives/docs/components/alert-dialog`;return a.useEffect(()=>{document.getElementById(e.current?.getAttribute("aria-describedby"))||console.warn(t)},[t,e]),null};let e2=a.forwardRef(({className:e,...t},r)=>(0,s.jsx)(eG,{className:(0,ep.cn)("fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",e),...t,ref:r}));e2.displayName=eG.displayName;let e4=a.forwardRef(({className:e,...t},r)=>(0,s.jsxs)(eq,{children:[(0,s.jsx)(e2,{}),(0,s.jsx)(eU,{ref:r,className:(0,ep.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",e),...t})]}));e4.displayName=eU.displayName;let e5=({className:e,...t})=>(0,s.jsx)("div",{className:(0,ep.cn)("flex flex-col space-y-2 text-center sm:text-left",e),...t});e5.displayName="AlertDialogHeader";let e8=({className:e,...t})=>(0,s.jsx)("div",{className:(0,ep.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",e),...t});e8.displayName="AlertDialogFooter";let e3=a.forwardRef(({className:e,...t},r)=>(0,s.jsx)(eX,{ref:r,className:(0,ep.cn)("text-lg font-semibold",e),...t}));e3.displayName=eX.displayName;let e6=a.forwardRef(({className:e,...t},r)=>(0,s.jsx)(eW,{ref:r,className:(0,ep.cn)("text-sm text-muted-foreground",e),...t}));e6.displayName=eW.displayName;let e7=a.forwardRef(({className:e,...t},r)=>(0,s.jsx)(eZ,{ref:r,className:(0,ep.cn)((0,n.r)(),e),...t}));e7.displayName=eZ.displayName;let e9=a.forwardRef(({className:e,...t},r)=>(0,s.jsx)(e0,{ref:r,className:(0,ep.cn)((0,n.r)({variant:"outline"}),"mt-2 sm:mt-0",e),...t}));e9.displayName=e0.displayName;var te=r(12779);function tt({onEmailSelect:e,selectedEmailId:t}){let{data:r}=(0,i.wV)(),{config:l}=(0,ey.U)(),{role:o}=function(){let{data:e}=(0,i.wV)(),[t,r]=(0,a.useState)(null);return{role:t,loading:!e}}(),[c,m]=(0,a.useState)([]),[p,x]=(0,a.useState)(!0),[f,h]=(0,a.useState)(!1),[v,g]=(0,a.useState)(null),[j,y]=(0,a.useState)(!1),[N,b]=(0,a.useState)(0),[w,C]=(0,a.useState)(null),{toast:S}=(0,u.dj)(),A=async e=>{try{let t=new URL("/api/emails",window.location.origin);e&&t.searchParams.set("cursor",e);let r=await fetch(t),s=await r.json();if("error"in s){console.error("API error:",s.error),S({title:"错误",description:s.error||"获取邮箱列表失败",variant:"destructive"});return}if(!s.emails||!Array.isArray(s.emails)){console.error("Invalid API response:",s),S({title:"错误",description:"服务器响应格式错误",variant:"destructive"});return}if(!e){let e=s.emails,t=e.findIndex(e=>c.some(t=>t.id===e.id));if(-1===t){m(e),g(s.nextCursor),b(s.total);return}let r=e.slice(0,t);m([...r,...c]),b(s.total);return}m(e=>[...e,...s.emails]),g(s.nextCursor),b(s.total)}catch(e){console.error("Failed to fetch emails:",e),S({title:"错误",description:"网络请求失败，请检查网络连接",variant:"destructive"})}finally{x(!1),h(!1),y(!1)}},k=async()=>{h(!0),await A()},P=eR(e=>{if(j)return;let{scrollHeight:t,scrollTop:r,clientHeight:s}=e.currentTarget;t-r<=1.5*s&&v&&(y(!0),A(v))},200),E=async r=>{try{let s=await fetch(`/api/emails/${r.id}`,{method:"DELETE"});if(!s.ok){let e=await s.json();S({title:"错误",description:e.error,variant:"destructive"});return}m(e=>e.filter(e=>e.id!==r.id)),b(e=>e-1),S({title:"成功",description:"邮箱已删除"}),t===r.id&&e(null)}catch{S({title:"错误",description:"删除邮箱失败",variant:"destructive"})}finally{C(null)}};return r?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("div",{className:"flex flex-col h-full",children:[(0,s.jsxs)("div",{className:"p-2 flex justify-between items-center border-b border-primary/20",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(n.$,{variant:"ghost",size:"icon",onClick:k,disabled:f,className:(0,ep.cn)("h-8 w-8",f&&"animate-spin"),children:(0,s.jsx)(d.A,{className:"h-4 w-4"})}),(0,s.jsx)("span",{className:"text-xs text-gray-500",children:o===te.gg.EMPEROR?`${N}/∞ 个邮箱`:`${N}/${l?.maxEmails||eI.q.MAX_ACTIVE_EMAILS} 个邮箱`})]}),(0,s.jsxs)("div",{className:"flex gap-1",children:[(0,s.jsx)(eN,{onEmailCreated:k}),(0,s.jsx)(eC,{onEmailBound:k})]})]}),(0,s.jsx)("div",{className:"flex-1 overflow-auto p-2",onScroll:P,children:p?(0,s.jsx)("div",{className:"text-center text-sm text-gray-500",children:"加载中..."}):c.length>0?(0,s.jsxs)("div",{className:"space-y-1",children:[c.map(r=>(0,s.jsxs)("div",{className:(0,ep.cn)("flex items-center gap-2 p-2 rounded cursor-pointer text-sm group","hover:bg-primary/5",t===r.id&&"bg-primary/10"),onClick:()=>e(r),children:[(0,s.jsx)(eP.A,{className:(0,ep.cn)("h-4 w-4","bound"===r.type?"text-green-600":"text-primary/60")}),(0,s.jsxs)("div",{className:"truncate flex-1",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("span",{className:"font-medium truncate",children:r.address}),"bound"===r.type&&(0,s.jsx)("span",{className:"text-xs bg-green-100 text-green-800 px-1.5 py-0.5 rounded-full",children:"绑定"})]}),(0,s.jsx)("div",{className:"text-xs text-gray-500",children:"bound"===r.type?"绑定邮箱 - 自动同步邮件":9999===new Date(r.expiresAt).getFullYear()?"永久有效":`过期时间: ${new Date(r.expiresAt).toLocaleString()}`})]}),(0,s.jsxs)("div",{className:"flex gap-1",children:[(0,s.jsx)(ek,{emailAddress:r.address,emailType:r.type,credentialId:"bound"===r.type?r.credentialId:void 0}),(0,s.jsx)(n.$,{variant:"ghost",size:"icon",className:"opacity-0 group-hover:opacity-100 h-8 w-8",onClick:e=>{e.stopPropagation(),C(r)},children:(0,s.jsx)(eE.A,{className:"h-4 w-4 text-destructive"})})]})]},r.id)),j&&(0,s.jsx)("div",{className:"text-center text-sm text-gray-500 py-2",children:"加载更多..."})]}):(0,s.jsx)("div",{className:"text-center text-sm text-gray-500",children:"还没有邮箱，创建一个吧！"})})]}),(0,s.jsx)(ez,{open:!!w,onOpenChange:()=>C(null),children:(0,s.jsxs)(e4,{children:[(0,s.jsxs)(e5,{children:[(0,s.jsx)(e3,{children:"确认删除"}),(0,s.jsxs)(e6,{children:["确定要删除邮箱 ",w?.address," 吗？此操作将同时删除该邮箱中的所有邮件，且不可恢复。"]})]}),(0,s.jsxs)(e8,{children:[(0,s.jsx)(e9,{children:"取消"}),(0,s.jsx)(e7,{className:"bg-destructive hover:bg-destructive/90",onClick:()=>w&&E(w),children:"删除"})]})]})})]}):null}let tr=(0,em.A)("Calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]]);function ts({email:e,onMessageSelect:t,selectedMessageId:r}){let[i,l]=(0,a.useState)([]),[o,c]=(0,a.useState)(!0),[m,p]=(0,a.useState)(!1),[x,f]=(0,a.useState)(null),[h,v]=(0,a.useState)(!1),g=((0,a.useRef)(),(0,a.useRef)([])),[j,y]=(0,a.useState)(0),[N,b]=(0,a.useState)(null),{toast:w}=(0,u.dj)(),C=async t=>{try{let r=new URL(`/api/emails/${e.id}`,window.location.origin);t&&r.searchParams.set("cursor",t);let s=await fetch(r),a=await s.json();if("error"in a){console.error("API error:",a.error),w({title:"错误",description:a.error||"获取邮件列表失败",variant:"destructive"});return}if(!a.messages||!Array.isArray(a.messages)){console.error("Invalid API response:",a),w({title:"错误",description:"服务器响应格式错误",variant:"destructive"});return}if(!t){let e=a.messages,t=g.current,r=e.findIndex(e=>t.some(t=>t.id===e.id));if(-1===r){l(e),f(a.nextCursor),y(a.total);return}let s=e.slice(0,r);l([...s,...t]),y(a.total);return}l(e=>[...e,...a.messages]),f(a.nextCursor),y(a.total)}catch(e){console.error("Failed to fetch messages:",e),w({title:"错误",description:"网络请求失败，请检查网络连接",variant:"destructive"})}finally{c(!1),p(!1),v(!1)}},S=async()=>{p(!0),await C()},A=eR(e=>{if(h)return;let{scrollHeight:t,scrollTop:r,clientHeight:s}=e.currentTarget;t-r<=1.5*s&&x&&(v(!0),C(x))},200),k=async s=>{try{let a=await fetch(`/api/emails/${e.id}/${s.id}`,{method:"DELETE"});if(!a.ok){let e=await a.json();w({title:"错误",description:e.error,variant:"destructive"});return}l(e=>e.filter(e=>e.id!==s.id)),y(e=>e-1),w({title:"成功",description:"邮件已删除"}),r===s.id&&t(null)}catch{w({title:"错误",description:"删除邮件失败",variant:"destructive"})}finally{b(null)}};return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("div",{className:"h-full flex flex-col",children:[(0,s.jsxs)("div",{className:"p-2 flex justify-between items-center border-b border-primary/20",children:[(0,s.jsx)(n.$,{variant:"ghost",size:"icon",onClick:S,disabled:m,className:(0,ep.cn)("h-8 w-8",m&&"animate-spin"),children:(0,s.jsx)(d.A,{className:"h-4 w-4"})}),(0,s.jsx)("span",{className:"text-xs text-gray-500",children:j>0?`${j} 封邮件`:"暂无邮件"})]}),(0,s.jsx)("div",{className:"flex-1 overflow-auto",onScroll:A,children:o?(0,s.jsx)("div",{className:"p-4 text-center text-sm text-gray-500",children:"加载中..."}):i.length>0?(0,s.jsxs)("div",{className:"divide-y divide-primary/10",children:[i.map(e=>(0,s.jsx)("div",{onClick:()=>t(e.id),className:(0,ep.cn)("p-3 hover:bg-primary/5 cursor-pointer group",r===e.id&&"bg-primary/10"),children:(0,s.jsxs)("div",{className:"flex items-start gap-3",children:[(0,s.jsx)(eP.A,{className:"w-4 h-4 text-primary/60 mt-1"}),(0,s.jsxs)("div",{className:"min-w-0 flex-1",children:[(0,s.jsx)("p",{className:"font-medium text-sm truncate",children:e.subject}),(0,s.jsxs)("div",{className:"mt-1 flex items-center gap-2 text-xs text-gray-500",children:[(0,s.jsx)("span",{className:"truncate",children:e.from_address}),(0,s.jsxs)("span",{className:"flex items-center gap-1",children:[(0,s.jsx)(tr,{className:"w-3 h-3"}),new Date(e.received_at).toLocaleString()]})]})]}),(0,s.jsx)(n.$,{variant:"ghost",size:"icon",className:"opacity-0 group-hover:opacity-100 h-8 w-8",onClick:t=>{t.stopPropagation(),b(e)},children:(0,s.jsx)(eE.A,{className:"h-4 w-4 text-destructive"})})]})},e.id)),h&&(0,s.jsx)("div",{className:"text-center text-sm text-gray-500 py-2",children:"加载更多..."})]}):(0,s.jsx)("div",{className:"p-4 text-center text-sm text-gray-500",children:"暂无邮件"})})]}),(0,s.jsx)(ez,{open:!!N,onOpenChange:()=>b(null),children:(0,s.jsxs)(e4,{children:[(0,s.jsxs)(e5,{children:[(0,s.jsx)(e3,{children:"确认删除"}),(0,s.jsxs)(e6,{children:["确定要删除邮件 ",N?.subject," 吗？"]})]}),(0,s.jsxs)(e8,{children:[(0,s.jsx)(e9,{children:"取消"}),(0,s.jsx)(e7,{className:"bg-destructive hover:bg-destructive/90",onClick:()=>N&&k(N),children:"删除"})]})]})})]})}var ta=r(38182);function ti({emailId:e,messageId:t}){let[r,i]=(0,a.useState)(null),[n,l]=(0,a.useState)(!0),[o,c]=(0,a.useState)("html"),d=(0,a.useRef)(null),{theme:m}=(0,ta.D)();return n?(0,s.jsx)("div",{className:"flex items-center justify-center h-32",children:(0,s.jsx)(ew.A,{className:"w-5 h-5 animate-spin text-primary/60"})}):r?(0,s.jsxs)("div",{className:"h-full flex flex-col",children:[(0,s.jsxs)("div",{className:"p-4 space-y-3 border-b border-primary/20",children:[(0,s.jsx)("h3",{className:"text-base font-bold",children:r.subject}),(0,s.jsxs)("div",{className:"text-xs text-gray-500 space-y-1",children:[(0,s.jsxs)("p",{children:["发件人：",r.from_address]}),(0,s.jsxs)("p",{children:["时间：",new Date(r.received_at).toLocaleString()]})]})]}),r.html&&(0,s.jsx)("div",{className:"border-b border-primary/20 p-2",children:(0,s.jsxs)(ex,{value:o,onValueChange:e=>c(e),className:"flex items-center gap-4",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(ef,{value:"html",id:"html"}),(0,s.jsx)(eh.J,{htmlFor:"html",className:"text-xs cursor-pointer",children:"HTML 格式"})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(ef,{value:"text",id:"text"}),(0,s.jsx)(eh.J,{htmlFor:"text",className:"text-xs cursor-pointer",children:"纯文本格式"})]})]})}),(0,s.jsx)("div",{className:"flex-1 overflow-auto relative",children:"html"===o&&r.html?(0,s.jsx)("iframe",{ref:d,className:"absolute inset-0 w-full h-full border-0 bg-transparent",sandbox:"allow-same-origin allow-popups"}):(0,s.jsx)("div",{className:"p-4 text-sm whitespace-pre-wrap",children:r.content})})]}):null}function tn(){let[e,t]=(0,a.useState)(null),[r,i]=(0,a.useState)(null),{copyToClipboard:n}=(0,ej.T)(),l="border-2 border-primary/20 bg-background rounded-lg overflow-hidden flex flex-col",o="p-2 border-b-2 border-primary/20 flex items-center justify-between shrink-0",c="text-sm font-bold px-2 w-full overflow-hidden",d=r?"message":e?"emails":"list",u=()=>{n(e?.address||"")};return(0,s.jsxs)("div",{className:"pb-5 pt-20 h-full flex flex-col",children:[(0,s.jsxs)("div",{className:"hidden lg:grid grid-cols-12 gap-4 h-full min-h-0",children:[(0,s.jsxs)("div",{className:(0,ep.cn)("col-span-3",l),children:[(0,s.jsx)("div",{className:o,children:(0,s.jsx)("h2",{className:c,children:"我的邮箱"})}),(0,s.jsx)("div",{className:"flex-1 overflow-auto",children:(0,s.jsx)(tt,{onEmailSelect:e=>{t(e),i(null)},selectedEmailId:e?.id})})]}),(0,s.jsxs)("div",{className:(0,ep.cn)("col-span-4",l),children:[(0,s.jsx)("div",{className:o,children:(0,s.jsx)("h2",{className:c,children:e?(0,s.jsxs)("div",{className:"w-full flex items-center gap-2",children:[(0,s.jsx)("span",{className:"truncate min-w-0",children:e.address}),(0,s.jsx)("div",{className:"shrink-0 cursor-pointer text-primary",onClick:u,children:(0,s.jsx)(m.A,{className:"size-4"})})]}):"选择邮箱查看消息"})}),e&&(0,s.jsx)("div",{className:"flex-1 overflow-auto",children:(0,s.jsx)(ts,{email:e,onMessageSelect:i,selectedMessageId:r})})]}),(0,s.jsxs)("div",{className:(0,ep.cn)("col-span-5",l),children:[(0,s.jsx)("div",{className:o,children:(0,s.jsx)("h2",{className:c,children:r?"邮件内容":"选择邮件查看详情"})}),e&&r&&(0,s.jsx)("div",{className:"flex-1 overflow-auto",children:(0,s.jsx)(ti,{emailId:e.id,messageId:r,onClose:()=>i(null)})})]})]}),(0,s.jsx)("div",{className:"lg:hidden h-full min-h-0",children:(0,s.jsxs)("div",{className:(0,ep.cn)("h-full",l),children:["list"===d&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:o,children:(0,s.jsx)("h2",{className:c,children:"我的邮箱"})}),(0,s.jsx)("div",{className:"flex-1 overflow-auto",children:(0,s.jsx)(tt,{onEmailSelect:e=>{t(e)},selectedEmailId:e?.id})})]}),"emails"===d&&e&&(0,s.jsxs)("div",{className:"h-full flex flex-col",children:[(0,s.jsxs)("div",{className:(0,ep.cn)(o,"gap-2"),children:[(0,s.jsx)("button",{onClick:()=>{t(null)},className:"text-sm text-primary shrink-0",children:"← 返回邮箱列表"}),(0,s.jsxs)("div",{className:"flex-1 flex items-center gap-2 min-w-0",children:[(0,s.jsx)("span",{className:"truncate min-w-0 flex-1 text-right",children:e.address}),(0,s.jsx)("div",{className:"shrink-0 cursor-pointer text-primary",onClick:u,children:(0,s.jsx)(m.A,{className:"size-4"})})]})]}),(0,s.jsx)("div",{className:"flex-1 overflow-auto",children:(0,s.jsx)(ts,{email:e,onMessageSelect:i,selectedMessageId:r})})]}),"message"===d&&e&&r&&(0,s.jsxs)("div",{className:"h-full flex flex-col",children:[(0,s.jsxs)("div",{className:o,children:[(0,s.jsx)("button",{onClick:()=>i(null),className:"text-sm text-primary",children:"← 返回消息列表"}),(0,s.jsx)("span",{className:"text-sm font-medium",children:"邮件内容"})]}),(0,s.jsx)("div",{className:"flex-1 overflow-auto",children:(0,s.jsx)(ti,{emailId:e.id,messageId:r,onClose:()=>i(null)})})]})]})})]})}},90494:(e,t,r)=>{"use strict";r.d(t,{NoPermissionDialog:()=>l});var s=r(37785),a=r(277),i=r(90842),n=r(32290);function l(){let e=(0,i.rd)(),{config:t}=(0,n.U)();return(0,s.jsx)("div",{className:"fixed inset-0 bg-background/50 backdrop-blur-sm z-50",children:(0,s.jsx)("div",{className:"fixed left-[50%] top-[50%] translate-x-[-50%] translate-y-[-50%] w-[90%] max-w-md",children:(0,s.jsx)("div",{className:"bg-background border-2 border-primary/20 rounded-lg p-6 md:p-12 shadow-lg",children:(0,s.jsxs)("div",{className:"text-center space-y-4",children:[(0,s.jsx)("h1",{className:"text-xl md:text-2xl font-bold",children:"权限不足"}),(0,s.jsx)("p",{className:"text-sm md:text-base text-muted-foreground",children:"你没有权限访问此页面，请联系网站管理员"}),t?.adminContact&&(0,s.jsxs)("p",{className:"text-sm md:text-base text-muted-foreground",children:["管理员联系方式：",t.adminContact]}),(0,s.jsx)(a.$,{onClick:()=>e.push("/"),className:"mt-4 w-full md:w-auto",children:"返回首页"})]})})})})}},37470:(e,t,r)=>{"use strict";r.d(t,{ThreeColumnLayout:()=>s});let s=(0,r(45710).YR)(function(){throw Error("Attempted to call ThreeColumnLayout() from the server but ThreeColumnLayout is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"F:\\CODE\\Project\\tempmail\\moemail\\app\\components\\emails\\three-column-layout.tsx","ThreeColumnLayout")},1422:(e,t,r)=>{"use strict";r.d(t,{NoPermissionDialog:()=>s});let s=(0,r(45710).YR)(function(){throw Error("Attempted to call NoPermissionDialog() from the server but NoPermissionDialog is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"F:\\CODE\\Project\\tempmail\\moemail\\app\\components\\no-permission-dialog.tsx","NoPermissionDialog")},90628:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>m,runtime:()=>d});var s=r(4302),a=r(96942),i=r(37470),n=r(1422),l=r(83553),o=r(51756),c=r(17451);let d="edge";async function m(){let e=await (0,l.j2)();e?.user||(0,o.V2)("/");let t=await (0,l.Yj)(c.Jj.MANAGE_EMAIL);return(0,s.jsx)("div",{className:"bg-gradient-to-b from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 h-screen",children:(0,s.jsxs)("div",{className:"container mx-auto h-full px-4 lg:px-8 max-w-[1600px]",children:[(0,s.jsx)(a.Y,{}),(0,s.jsxs)("main",{className:"h-full",children:[(0,s.jsx)(i.ThreeColumnLayout,{}),!t&&(0,s.jsx)(n.NoPermissionDialog,{})]})]})})}}},e=>{var t=t=>e(e.s=t);e.O(0,[730,752,899,498,943,86,672,156,481,889],()=>t(7745));var r=e.O();(_ENTRIES="undefined"==typeof _ENTRIES?{}:_ENTRIES)["middleware_app/moe/page"]=r}]);
//# sourceMappingURL=page.js.map