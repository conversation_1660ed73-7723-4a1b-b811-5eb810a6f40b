import { auth } from "@/lib/auth"
import { NextResponse } from "next/server"
import { EmailCredentialManager } from "@/lib/emailCredentials"

export const runtime = "edge"

export async function GET(
  request: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  const session = await auth()
  if (!session?.user?.id) {
    return NextResponse.json({ error: "未授权" }, { status: 401 })
  }

  try {
    const { id } = await params
    const credential = await EmailCredentialManager.getCredentialById(id)

    if (!credential) {
      return NextResponse.json(
        { error: "凭证不存在" },
        { status: 404 }
      )
    }

    return NextResponse.json({
      id: credential.id,
      emailAddress: credential.emailAddress,
      jwt: credential.jwt,
      enabled: credential.enabled,
      createdAt: credential.createdAt.toISOString(),
      lastUsedAt: credential.lastUsedAt?.toISOString() || null
    })
  } catch (error) {
    console.error("Failed to get email credential:", error)
    return NextResponse.json(
      { error: "获取邮箱凭证失败" },
      { status: 500 }
    )
  }
}
