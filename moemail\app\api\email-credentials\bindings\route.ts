import { auth } from "@/lib/auth"
import { NextResponse } from "next/server"
import { EmailCredentialManager } from "@/lib/emailCredentials"
import { z } from "zod"

export const runtime = "edge"

// 解绑邮箱的请求体验证
const unbindEmailSchema = z.object({
  emailAddress: z.string().email("无效的邮箱地址"),
})

// 获取用户绑定的邮箱列表
export async function GET() {
  const session = await auth()
  if (!session?.user?.id) {
    return NextResponse.json({ error: "未授权" }, { status: 401 })
  }

  try {
    const bindings = await EmailCredentialManager.getUserBindings(session.user.id)

    return NextResponse.json({
      bindings: bindings.map(binding => ({
        id: binding.id,
        emailAddress: binding.emailAddress,
        createdAt: binding.createdAt.toISOString(),
        enabled: binding.enabled
      }))
    })
  } catch (error) {
    console.error("Failed to fetch user email bindings:", error)
    return NextResponse.json(
      { error: "获取邮箱绑定失败" },
      { status: 500 }
    )
  }
}

// 解绑邮箱
export async function DELETE(request: Request) {
  const session = await auth()
  if (!session?.user?.id) {
    return NextResponse.json({ error: "未授权" }, { status: 401 })
  }

  try {
    const body = await request.json()
    const validatedData = unbindEmailSchema.parse(body)

    const success = await EmailCredentialManager.unbindEmail(
      session.user.id,
      validatedData.emailAddress
    )

    if (!success) {
      return NextResponse.json(
        { error: "邮箱绑定不存在" },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      message: "邮箱解绑成功"
    })
  } catch (error) {
    console.error("Failed to unbind email:", error)

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: error.errors[0].message },
        { status: 400 }
      )
    }

    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: "解绑邮箱失败" },
      { status: 500 }
    )
  }
}
