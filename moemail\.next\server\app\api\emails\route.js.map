{"version": 3, "file": "app/api/emails/route.js", "mappings": "sFAAA,8DCAA,oHGAA,qTFOO,IAAMA,EAAU,OAAM,eAIPC,EAAIC,CAAgB,EACxC,IAAMC,EAAS,MAAMC,CAAAA,EAAAA,EAAAA,EAAAA,CAASA,GAE9B,GAAI,CAACD,EACH,MADW,CACJE,EAAAA,EAAYA,CAACC,IAAI,CACtB,CAAEC,MAAO,KAAM,EACf,CAAEC,OAAQ,GAAI,GAIlB,GAAM,cAAEC,CAAY,CAAE,CAAG,IAAIC,IAAIR,EAAQS,GAAG,EACtCC,EAASH,EAAaI,GAAG,CAAC,UAE1BC,EAAKC,CAAAA,EAAAA,EAAAA,CAAAA,CAAQA,GAEnB,GAAI,CAEF,IAAMC,EAAsBC,CAAAA,EAAAA,EAAAA,EAAAA,CAAGA,CAC7BC,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACC,EAAAA,MAAMA,CAAChB,MAAM,CAAEA,GAClBiB,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACD,EAAAA,MAAMA,CAACE,SAAS,CAAE,IAAIC,OAGrBC,EAAmB,MAAMT,EAAGU,KAAK,CAACL,MAAM,CAACM,QAAQ,CAAC,CACtDC,MAAOV,EACPW,QAAS,CAACR,EAAQ,MAAES,CAAI,CAAE,GAAK,CAC7BA,EAAKT,EAAOU,SAAS,EACrBD,EAAKT,EAAOW,EAAE,EACf,GAIGC,EAAoB,MAAMjB,EAAGU,KAAK,CAACQ,iBAAiB,CAACP,QAAQ,CAAC,CAClEC,MAAOR,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACc,EAAAA,iBAAiBA,CAAC7B,MAAM,CAAEA,GACpCwB,QAAS,CAACM,EAAU,MAAEL,CAAI,CAAE,GAAK,CAC/BA,EAAKK,EAASJ,SAAS,EACxB,CACDK,KAAM,CACJC,MAAO,EACT,CACF,EAFgB,CAKVC,EAAab,EAAiBc,GAAG,CAACF,EALb,CAKuB,EAChDL,GADgD,EACtCA,EAAE,CACZQ,QAASH,EAAMG,OAAO,CACtBT,UAAWM,EAAMN,SAAS,CAACU,OAAO,GAClClB,UAAWc,EAAMd,SAAS,CAACkB,OAAO,GAClCC,KAAM,YACR,GAEMC,EAAcV,EAAkBM,GAAG,CAACK,GAAY,EACpDZ,GAAIY,EAAQP,KAAK,EAAEL,IAAMY,EAAQZ,EAAE,CACnCQ,QAASI,EAAQP,KAAK,EAAEG,SAAW,GACnCT,UAAWa,EAAQb,SAAS,CAACU,OAAO,GACpClB,UAAW,cACXmB,KAAM,QACNG,aAAcD,EAAQC,YAAY,CACpC,GAGMC,EAAY,IAAIR,KAAeK,EAAY,CAACI,IAAI,CAAC,CAACC,EAAGC,IAAMA,EAAElB,SAAS,CAAGiB,EAAEjB,SAAS,EAGtFmB,EAAYJ,EAChB,GAAIhC,EAAQ,CACV,GAAM,WAAEqC,CAAS,IAAEnB,CAAE,CAAE,CAAGoB,CAAAA,EAAAA,EAAAA,CAAAA,CAAYA,CAACtC,GACjCuC,EAAcP,EAAUQ,SAAS,CAACjB,GACtCA,EAAMN,SAAS,CAAGoB,GAAcd,EAAMN,SAAS,GAAKoB,GAAad,EAAML,EAAE,CAAGA,GAE1EqB,EAAc,CAAC,GAAG,CACpBH,EAAYJ,EAAUS,KAAK,CAACF,EAAAA,CAEhC,CAEA,IAAMG,EAAUN,EAAUO,MAAM,GAAGC,CAC7BC,EAAaH,EACfI,CAAAA,EAAAA,EAAAA,CAAAA,CAAYA,CACVV,CAAS,CAACQ,GAAc,CAAC3B,QAAH,CAAY,CAClCmB,CAAS,CAACQ,GAAc,CAAC1B,EAAE,EAE7B,IAFwB,CAGtB6B,EAAiBL,EAAUN,EAAUK,KAAK,CAAC,EAnFnC,CAmFsCG,GAAaR,EAEjE,OAAO3C,EAAAA,EAAYA,CAACC,IAAI,CAAC,CACvBa,OAAQwC,EACRF,aACAG,MAAOhB,EAAUW,MAAM,EAE3B,CAAE,MAAOhD,EAAO,CAEd,OADAsD,QAAQtD,KAAK,CAAC,+BAAgCA,GACvCF,EAAAA,EAAYA,CAACC,IAAI,CACtB,CAAEC,MAAO,wBAAyB,EAClC,CAAEC,OAAQ,GAAI,EAElB,CACF,CCnGA,UAAwB,qBAAmB,EAC3C,YACA,KAAc,GAAS,WACvB,yBACA,uBACA,iBACA,iCACA,CAAK,CACL,oFACA,iBAVA,GAWA,QAAY,EACZ,CAAC,EAID,kBAAQ,wCAAsD,EAC9D,aACA,MAAW,QAAW,EACtB,mBACA,sBACA,CAAK,CACL,CCpBA,kEACA,EAFA,4BAEA,4BACA,MACI,QAA8B,EAClC,yBACA,0BACA,wBACA,gBAAyB,OAAqB,EAC9C,uBACA,CAAS,CACT,CAAK,EAEE,MAAqB,EAC5B,EAAe,GAAsB,MAAM,EAAkB,CAC7D,QAD6D,GAf7D,CAAoB,MAAQ,OAcM,CAdN,CAAW,sBAA2B,aAAe,kDAAyD,uOAAsQ,2RAAuT,kBAAkB,QAAQ,uDAA2D,0DAA8D,iBAAsB,gBAAkB,sEAAgF,kBAAoB,uCAA6C,MAAQ,iBAAmB,2BAA+B,gHAAkI,uBAAyB,8FAA0G,aAAiB,WAAa,sEAA6E,uBAAuB,iCAAmC,QAAQ,EAAE,SAAW,oBAAsB,QAAQ,GAAG,4EAAgF,WAAa,SAAW,2CAAiD,UAAY,+BAAqC,UAAY,oCAA0C,QAAU,uCAA6C,OAAS,yCAA+C,QAAU,0CAAiD,MAAQ,+CAAsD,iBAAmB,ywBAA+5B,4CAA8C,kNAAqQ,qBAAyB,s+CAA0/C,oIAiB7wJ,CAAC,CAAC,EAAC,qGClBI,SAASkD,EAAaT,CAAiB,CAAEnB,CAAU,EAExD,OAAOgC,EAAOC,IAADD,CAAME,KAAKC,SAAS,CADR,WAAEhB,KAAWnB,CAAG,IACAoC,QAAQ,CAAC,SACpD,CAEO,SAAShB,EAAatC,CAAc,EAEzC,OADaoD,KAAKG,KAAK,CAACL,EAAOC,IAADD,CAAMlD,EAAQ,UAAUsD,QAAQ,GAEhE", "sources": ["webpack://_N_E/external commonjs \"node:async_hooks\"", "webpack://_N_E/external commonjs \"node:buffer\"", "webpack://_N_E/./app/api/emails/route.ts", "webpack://_N_E/./app/api/emails/route.ts?a4e9", "webpack://_N_E/?4272", "webpack://_N_E/./app/lib/cursor.ts"], "sourcesContent": ["module.exports = require(\"node:async_hooks\");", "module.exports = require(\"node:buffer\");", "import { createDb } from \"@/lib/db\"\r\nimport { and, eq, gt, lt, or, sql } from \"drizzle-orm\"\r\nimport { NextResponse } from \"next/server\"\r\nimport { emails, userEmailBindings } from \"@/lib/schema\"\r\nimport { encodeCursor, decodeCursor } from \"@/lib/cursor\"\r\nimport { getUserId } from \"@/lib/apiKey\"\r\n\r\nexport const runtime = \"edge\"\r\n\r\nconst PAGE_SIZE = 20\r\n\r\nexport async function GET(request: Request) {\r\n  const userId = await getUserId()\r\n\r\n  if (!userId) {\r\n    return NextResponse.json(\r\n      { error: \"未授权\" },\r\n      { status: 401 }\r\n    )\r\n  }\r\n\r\n  const { searchParams } = new URL(request.url)\r\n  const cursor = searchParams.get('cursor')\r\n\r\n  const db = createDb()\r\n\r\n  try {\r\n    // 获取临时邮箱\r\n    const tempEmailConditions = and(\r\n      eq(emails.userId, userId),\r\n      gt(emails.expiresAt, new Date())\r\n    )\r\n\r\n    const tempEmailResults = await db.query.emails.findMany({\r\n      where: tempEmailConditions,\r\n      orderBy: (emails, { desc }) => [\r\n        desc(emails.createdAt),\r\n        desc(emails.id)\r\n      ]\r\n    })\r\n\r\n    // 获取绑定邮箱\r\n    const boundEmailResults = await db.query.userEmailBindings.findMany({\r\n      where: eq(userEmailBindings.userId, userId),\r\n      orderBy: (bindings, { desc }) => [\r\n        desc(bindings.createdAt)\r\n      ],\r\n      with: {\r\n        email: true // 关联查询邮箱信息\r\n      }\r\n    })\r\n\r\n    // 合并结果，转换为统一格式\r\n    const tempEmails = tempEmailResults.map(email => ({\r\n      id: email.id,\r\n      address: email.address,\r\n      createdAt: email.createdAt.getTime(),\r\n      expiresAt: email.expiresAt.getTime(),\r\n      type: 'temporary' as const\r\n    }))\r\n\r\n    const boundEmails = boundEmailResults.map(binding => ({\r\n      id: binding.email?.id || binding.id,\r\n      address: binding.email?.address || '',\r\n      createdAt: binding.createdAt.getTime(),\r\n      expiresAt: 9999999999999, // 绑定邮箱永不过期\r\n      type: 'bound' as const,\r\n      credentialId: binding.credentialId\r\n    }))\r\n\r\n    // 合并并按创建时间排序\r\n    const allEmails = [...tempEmails, ...boundEmails].sort((a, b) => b.createdAt - a.createdAt)\r\n\r\n    // 应用分页\r\n    let emailList = allEmails\r\n    if (cursor) {\r\n      const { timestamp, id } = decodeCursor(cursor)\r\n      const cursorIndex = allEmails.findIndex(email =>\r\n        email.createdAt < timestamp || (email.createdAt === timestamp && email.id < id)\r\n      )\r\n      if (cursorIndex > -1) {\r\n        emailList = allEmails.slice(cursorIndex)\r\n      }\r\n    }\r\n\r\n    const hasMore = emailList.length > PAGE_SIZE\r\n    const nextCursor = hasMore\r\n      ? encodeCursor(\r\n          emailList[PAGE_SIZE - 1].createdAt,\r\n          emailList[PAGE_SIZE - 1].id\r\n        )\r\n      : null\r\n    const finalEmailList = hasMore ? emailList.slice(0, PAGE_SIZE) : emailList\r\n\r\n    return NextResponse.json({\r\n      emails: finalEmailList,\r\n      nextCursor,\r\n      total: allEmails.length\r\n    })\r\n  } catch (error) {\r\n    console.error('Failed to fetch user emails:', error)\r\n    return NextResponse.json(\r\n      { error: \"Failed to fetch emails\" },\r\n      { status: 500 }\r\n    )\r\n  }\r\n} ", "import { AppRouteRouteModule } from \"next/dist/server/route-modules/app-route/module.compiled\";\nimport { RouteKind } from \"next/dist/server/route-kind\";\nimport { patchFetch as _patchFetch } from \"next/dist/server/lib/patch-fetch\";\nimport * as userland from \"F:\\\\CODE\\\\Project\\\\tempmail\\\\moemail\\\\app\\\\api\\\\emails\\\\route.ts\";\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new AppRouteRouteModule({\n    definition: {\n        kind: RouteKind.APP_ROUTE,\n        page: \"/api/emails/route\",\n        pathname: \"/api/emails\",\n        filename: \"route\",\n        bundlePath: \"app/api/emails/route\"\n    },\n    resolvedPagePath: \"F:\\\\CODE\\\\Project\\\\tempmail\\\\moemail\\\\app\\\\api\\\\emails\\\\route.ts\",\n    nextConfigOutput,\n    userland\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return _patchFetch({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\nexport { routeModule, workAsyncStorage, workUnitAsyncStorage, serverHooks, patchFetch,  };\n\n//# sourceMappingURL=app-route.js.map", "var _self___RSC_MANIFEST;\nimport { createServerModuleMap } from \"next/dist/server/app-render/action-utils\";\nimport { setReferenceManifestsSingleton } from \"next/dist/server/app-render/encryption-utils\";\nimport { EdgeRouteModuleWrapper } from \"next/dist/server/web/edge-route-module-wrapper\";\n// Import the userland code.\nimport * as module from \"next-app-loader?name=app%2Fapi%2Femails%2Froute&page=%2Fapi%2Femails%2Froute&pagePath=private-next-app-dir%2Fapi%2Femails%2Froute.ts&appDir=F%3A%5CCODE%5CProject%5Ctempmail%5Cmoemail%5Capp&appPaths=%2Fapi%2Femails%2Froute&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!private-next-app-dir/api/emails/route.ts?__next_edge_ssr_entry__\";\nconst nextConfig = {\"env\":{},\"eslint\":{\"ignoreDuringBuilds\":false},\"typescript\":{\"ignoreBuildErrors\":false,\"tsconfigPath\":\"tsconfig.json\"},\"distDir\":\".next\",\"cleanDistDir\":true,\"assetPrefix\":\"\",\"cacheMaxMemorySize\":52428800,\"configOrigin\":\"next.config.ts\",\"useFileSystemPublicRoutes\":true,\"generateEtags\":true,\"pageExtensions\":[\"tsx\",\"ts\",\"jsx\",\"js\"],\"poweredByHeader\":true,\"compress\":true,\"images\":{\"deviceSizes\":[640,750,828,1080,1200,1920,2048,3840],\"imageSizes\":[16,32,48,64,96,128,256,384],\"path\":\"/_next/image\",\"loader\":\"default\",\"loaderFile\":\"\",\"domains\":[],\"disableStaticImages\":false,\"minimumCacheTTL\":60,\"formats\":[\"image/webp\"],\"dangerouslyAllowSVG\":false,\"contentSecurityPolicy\":\"script-src 'none'; frame-src 'none'; sandbox;\",\"contentDispositionType\":\"attachment\",\"remotePatterns\":[{\"protocol\":\"https\",\"hostname\":\"avatars.githubusercontent.com\"}],\"unoptimized\":false},\"devIndicators\":{\"appIsrStatus\":true,\"buildActivity\":true,\"buildActivityPosition\":\"bottom-right\"},\"onDemandEntries\":{\"maxInactiveAge\":60000,\"pagesBufferLength\":5},\"amp\":{\"canonicalBase\":\"\"},\"basePath\":\"\",\"sassOptions\":{},\"trailingSlash\":false,\"i18n\":null,\"productionBrowserSourceMaps\":false,\"excludeDefaultMomentLocales\":true,\"serverRuntimeConfig\":{},\"publicRuntimeConfig\":{},\"reactProductionProfiling\":false,\"reactStrictMode\":null,\"reactMaxHeadersLength\":6000,\"httpAgentOptions\":{\"keepAlive\":true},\"logging\":{},\"expireTime\":31536000,\"staticPageGenerationTimeout\":60,\"modularizeImports\":{\"@mui/icons-material\":{\"transform\":\"@mui/icons-material/{{member}}\"},\"lodash\":{\"transform\":\"lodash/{{member}}\"}},\"outputFileTracingRoot\":\"F:\\\\CODE\\\\Project\\\\tempmail\\\\moemail\",\"experimental\":{\"cacheLife\":{\"default\":{\"stale\":300,\"revalidate\":900,\"expire\":4294967294},\"seconds\":{\"stale\":0,\"revalidate\":1,\"expire\":60},\"minutes\":{\"stale\":300,\"revalidate\":60,\"expire\":3600},\"hours\":{\"stale\":300,\"revalidate\":3600,\"expire\":86400},\"days\":{\"stale\":300,\"revalidate\":86400,\"expire\":604800},\"weeks\":{\"stale\":300,\"revalidate\":604800,\"expire\":2592000},\"max\":{\"stale\":300,\"revalidate\":2592000,\"expire\":4294967294}},\"cacheHandlers\":{},\"cssChunking\":true,\"multiZoneDraftMode\":false,\"appNavFailHandling\":false,\"prerenderEarlyExit\":true,\"serverMinification\":true,\"serverSourceMaps\":false,\"linkNoTouchStart\":false,\"caseSensitiveRoutes\":false,\"clientSegmentCache\":false,\"preloadEntriesOnStart\":true,\"clientRouterFilter\":true,\"clientRouterFilterRedirects\":false,\"fetchCacheKeyPrefix\":\"\",\"middlewarePrefetch\":\"flexible\",\"optimisticClientCache\":true,\"manualClientBasePath\":false,\"cpus\":11,\"memoryBasedWorkersCount\":false,\"imgOptConcurrency\":null,\"imgOptTimeoutInSeconds\":7,\"imgOptMaxInputPixels\":268402689,\"imgOptSequentialRead\":null,\"isrFlushToDisk\":true,\"workerThreads\":false,\"optimizeCss\":false,\"nextScriptWorkers\":false,\"scrollRestoration\":false,\"externalDir\":false,\"disableOptimizedLoading\":false,\"gzipSize\":true,\"craCompat\":false,\"esmExternals\":true,\"fullySpecified\":false,\"swcTraceProfiling\":false,\"forceSwcTransforms\":false,\"largePageDataBytes\":128000,\"turbo\":{\"root\":\"F:\\\\CODE\\\\Project\\\\tempmail\\\\moemail\"},\"typedRoutes\":false,\"typedEnv\":false,\"parallelServerCompiles\":false,\"parallelServerBuildTraces\":false,\"ppr\":false,\"authInterrupts\":false,\"reactOwnerStack\":false,\"webpackMemoryOptimizations\":false,\"optimizeServerReact\":true,\"useEarlyImport\":false,\"staleTimes\":{\"dynamic\":0,\"static\":300},\"serverComponentsHmrCache\":true,\"staticGenerationMaxConcurrency\":8,\"staticGenerationMinPagesPerWorker\":25,\"dynamicIO\":false,\"inlineCss\":false,\"optimizePackageImports\":[\"lucide-react\",\"date-fns\",\"lodash-es\",\"ramda\",\"antd\",\"react-bootstrap\",\"ahooks\",\"@ant-design/icons\",\"@headlessui/react\",\"@headlessui-float/react\",\"@heroicons/react/20/solid\",\"@heroicons/react/24/solid\",\"@heroicons/react/24/outline\",\"@visx/visx\",\"@tremor/react\",\"rxjs\",\"@mui/material\",\"@mui/icons-material\",\"recharts\",\"react-use\",\"effect\",\"@effect/schema\",\"@effect/platform\",\"@effect/platform-node\",\"@effect/platform-browser\",\"@effect/platform-bun\",\"@effect/sql\",\"@effect/sql-mssql\",\"@effect/sql-mysql2\",\"@effect/sql-pg\",\"@effect/sql-squlite-node\",\"@effect/sql-squlite-bun\",\"@effect/sql-squlite-wasm\",\"@effect/sql-squlite-react-native\",\"@effect/rpc\",\"@effect/rpc-http\",\"@effect/typeclass\",\"@effect/experimental\",\"@effect/opentelemetry\",\"@material-ui/core\",\"@material-ui/icons\",\"@tabler/icons-react\",\"mui-core\",\"react-icons/ai\",\"react-icons/bi\",\"react-icons/bs\",\"react-icons/cg\",\"react-icons/ci\",\"react-icons/di\",\"react-icons/fa\",\"react-icons/fa6\",\"react-icons/fc\",\"react-icons/fi\",\"react-icons/gi\",\"react-icons/go\",\"react-icons/gr\",\"react-icons/hi\",\"react-icons/hi2\",\"react-icons/im\",\"react-icons/io\",\"react-icons/io5\",\"react-icons/lia\",\"react-icons/lib\",\"react-icons/lu\",\"react-icons/md\",\"react-icons/pi\",\"react-icons/ri\",\"react-icons/rx\",\"react-icons/si\",\"react-icons/sl\",\"react-icons/tb\",\"react-icons/tfi\",\"react-icons/ti\",\"react-icons/vsc\",\"react-icons/wi\"]},\"bundlePagesRouterDependencies\":false,\"configFile\":\"F:\\\\CODE\\\\Project\\\\tempmail\\\\moemail\\\\next.config.ts\",\"configFileName\":\"next.config.ts\"}\nconst maybeJSONParse = (str)=>str ? JSON.parse(str) : undefined;\nconst rscManifest = (_self___RSC_MANIFEST = self.__RSC_MANIFEST) == null ? void 0 : _self___RSC_MANIFEST[\"/api/emails/route\"];\nconst rscServerManifest = maybeJSONParse(self.__RSC_SERVER_MANIFEST);\nif (rscManifest && rscServerManifest) {\n    setReferenceManifestsSingleton({\n        page: \"/api/emails/route\",\n        clientReferenceManifest: rscManifest,\n        serverActionsManifest: rscServerManifest,\n        serverModuleMap: createServerModuleMap({\n            serverActionsManifest: rscServerManifest\n        })\n    });\n}\nexport const ComponentMod = module;\nexport default EdgeRouteModuleWrapper.wrap(module.routeModule, {\n    nextConfig\n});\n\n//# sourceMappingURL=edge-app-route.js.map", "interface CursorData {\r\n  timestamp: number\r\n  id: string\r\n}\r\n\r\nexport function encodeCursor(timestamp: number, id: string): string {\r\n  const data: CursorData = { timestamp, id }\r\n  return Buffer.from(JSON.stringify(data)).toString('base64')\r\n}\r\n\r\nexport function decodeCursor(cursor: string): CursorData {\r\n  const data = JSON.parse(Buffer.from(cursor, 'base64').toString())\r\n  return data as CursorData\r\n} "], "names": ["runtime", "GET", "request", "userId", "getUserId", "NextResponse", "json", "error", "status", "searchParams", "URL", "url", "cursor", "get", "db", "createDb", "tempEmailConditions", "and", "eq", "emails", "gt", "expiresAt", "Date", "tempEmailResults", "query", "find<PERSON>any", "where", "orderBy", "desc", "createdAt", "id", "boundEmailResults", "userEmailBindings", "bindings", "with", "email", "tempEmails", "map", "address", "getTime", "type", "boundEmails", "binding", "credentialId", "allEmails", "sort", "a", "b", "emailList", "timestamp", "decodeCursor", "cursorIndex", "findIndex", "slice", "hasMore", "length", "PAGE_SIZE", "nextCursor", "encodeCursor", "finalEmailList", "total", "console", "<PERSON><PERSON><PERSON>", "from", "JSON", "stringify", "toString", "parse"], "sourceRoot": "", "ignoreList": []}