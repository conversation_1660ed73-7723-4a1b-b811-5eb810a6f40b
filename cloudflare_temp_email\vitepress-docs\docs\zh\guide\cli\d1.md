# 初始化/更新 D1 数据库

第一次执行登录 wrangler 命令时，会提示登录, 按提示操作即可

## 初始化数据库

```bash
cd worker
cp wrangler.toml.template wrangler.toml
# 创建 D1 并执行 schema.sql
wrangler d1 create dev
wrangler d1 execute dev --file=../db/schema.sql --remote
```

创建完成后，我们在 cloudflare 的控制台可以看到 D1 数据库

![D1](/readme_assets/d1.png)

## 更新数据库 schema

`schema` 更新，请确认你之前部署的版本,
查看 [更新日志](https://github.com/dreamhunter2333/cloudflare_temp_email/blob/main/CHANGELOG.md)

找到需要执行的 `patch` 文件, 执行, 例如:

```bash
cd worker
wrangler d1 execute dev --file=../db/2024-01-13-patch.sql --remote
wrangler d1 execute dev --file=../db/2024-04-03-patch.sql --remote
```
