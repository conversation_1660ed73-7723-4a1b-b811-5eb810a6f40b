"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[689],{1626:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(7401).A)("Github",[["path",{d:"M15 22v-4a4.8 4.8 0 0 0-1-3.5c3 0 6-2 6-5.5.08-1.25-.27-2.48-1-3.5.28-1.15.28-2.35 0-3.5 0 0-1 0-3 1.5-2.64-.5-5.36-.5-8 0C6 2 5 2 5 2c-.3 1.15-.3 2.35 0 3.5A5.403 5.403 0 0 0 4 9c0 3.5 3 5.5 6 5.5-.39.49-.68 1.05-.85 1.65-.17.6-.22 1.23-.15 1.85v4",key:"tonef"}],["path",{d:"M9 18c-4.51 2-5-2-7-2",key:"9comsn"}]])},6693:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(7401).A)("KeyRound",[["path",{d:"M2.586 17.414A2 2 0 0 0 2 18.828V21a1 1 0 0 0 1 1h3a1 1 0 0 0 1-1v-1a1 1 0 0 1 1-1h1a1 1 0 0 0 1-1v-1a1 1 0 0 1 1-1h.172a2 2 0 0 0 1.414-.586l.814-.814a6.5 6.5 0 1 0-4-4z",key:"1s6t7t"}],["circle",{cx:"16.5",cy:"7.5",r:".5",fill:"currentColor",key:"w0ekpg"}]])},4505:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(7401).A)("LoaderCircle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},2699:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(7401).A)("UserRound",[["circle",{cx:"12",cy:"8",r:"5",key:"1hypcn"}],["path",{d:"M20 21a8 8 0 0 0-16 0",key:"rfgkzh"}]])},1420:(e,t,n)=>{n.d(t,{UC:()=>ei,B8:()=>eo,bL:()=>er,l9:()=>el});var r,o=n(2115),l=n.t(o,2);function i(e,t,{checkForDefaultPrevented:n=!0}={}){return function(r){if(e?.(r),!1===n||!r.defaultPrevented)return t?.(r)}}var a=n(5155);function u(e,t=[]){let n=[],r=()=>{let t=n.map(e=>o.createContext(e));return function(n){let r=n?.[e]||t;return o.useMemo(()=>({[`__scope${e}`]:{...n,[e]:r}}),[n,r])}};return r.scopeName=e,[function(t,r){let l=o.createContext(r),i=n.length;n=[...n,r];let u=t=>{let{scope:n,children:r,...u}=t,c=n?.[e]?.[i]||l,s=o.useMemo(()=>u,Object.values(u));return(0,a.jsx)(c.Provider,{value:s,children:r})};return u.displayName=t+"Provider",[u,function(n,a){let u=a?.[e]?.[i]||l,c=o.useContext(u);if(c)return c;if(void 0!==r)return r;throw Error(`\`${n}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let n=()=>{let n=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let r=n.reduce((t,{useScope:n,scopeName:r})=>{let o=n(e)[`__scope${r}`];return{...t,...o}},{});return o.useMemo(()=>({[`__scope${t.scopeName}`]:r}),[r])}};return n.scopeName=t.scopeName,n}(r,...t)]}function c(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function s(...e){return t=>{let n=!1,r=e.map(e=>{let r=c(e,t);return n||"function"!=typeof r||(n=!0),r});if(n)return()=>{for(let t=0;t<r.length;t++){let n=r[t];"function"==typeof n?n():c(e[t],null)}}}}function f(...e){return o.useCallback(s(...e),e)}function d(e){let t=function(e){let t=o.forwardRef((e,t)=>{let{children:n,...r}=e;if(o.isValidElement(n)){let e,l;let i=(e=Object.getOwnPropertyDescriptor(n.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?n.ref:(e=Object.getOwnPropertyDescriptor(n,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?n.props.ref:n.props.ref||n.ref,a=function(e,t){let n={...t};for(let r in t){let o=e[r],l=t[r];/^on[A-Z]/.test(r)?o&&l?n[r]=(...e)=>{let t=l(...e);return o(...e),t}:o&&(n[r]=o):"style"===r?n[r]={...o,...l}:"className"===r&&(n[r]=[o,l].filter(Boolean).join(" "))}return{...e,...n}}(r,n.props);return n.type!==o.Fragment&&(a.ref=t?s(t,i):i),o.cloneElement(n,a)}return o.Children.count(n)>1?o.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),n=o.forwardRef((e,n)=>{let{children:r,...l}=e,i=o.Children.toArray(r),u=i.find(m);if(u){let e=u.props.children,r=i.map(t=>t!==u?t:o.Children.count(e)>1?o.Children.only(null):o.isValidElement(e)?e.props.children:null);return(0,a.jsx)(t,{...l,ref:n,children:o.isValidElement(e)?o.cloneElement(e,void 0,r):null})}return(0,a.jsx)(t,{...l,ref:n,children:r})});return n.displayName=`${e}.Slot`,n}n(5359),n(587),n(5465);var p=Symbol("radix.slottable");function m(e){return o.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===p}var v=globalThis?.document?o.useLayoutEffect:()=>{},y=l[" useId ".trim().toString()]||(()=>void 0),h=0;function g(e){let[t,n]=o.useState(y());return v(()=>{e||n(e=>e??String(h++))},[e]),e||(t?`radix-${t}`:"")}n(7650);var w=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let n=d(`Primitive.${t}`),r=o.forwardRef((e,r)=>{let{asChild:o,...l}=e,i=o?n:t;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,a.jsx)(i,{...l,ref:r})});return r.displayName=`Primitive.${t}`,{...e,[t]:r}},{}),b=l[" useInsertionEffect ".trim().toString()]||v;function R({prop:e,defaultProp:t,onChange:n=()=>{},caller:r}){let[l,i,a]=function({defaultProp:e,onChange:t}){let[n,r]=o.useState(e),l=o.useRef(n),i=o.useRef(t);return b(()=>{i.current=t},[t]),o.useEffect(()=>{l.current!==n&&(i.current?.(n),l.current=n)},[n,l]),[n,r,i]}({defaultProp:t,onChange:n}),u=void 0!==e,c=u?e:l;{let t=o.useRef(void 0!==e);o.useEffect(()=>{let e=t.current;if(e!==u){let t=u?"controlled":"uncontrolled";console.warn(`${r} is changing from ${e?"controlled":"uncontrolled"} to ${t}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}t.current=u},[u,r])}return[c,o.useCallback(t=>{if(u){let n="function"==typeof t?t(e):t;n!==e&&a.current?.(n)}else i(t)},[u,e,i,a])]}Symbol("RADIX:SYNC_STATE");var N=o.createContext(void 0);function C(e){let t=o.useContext(N);return e||t||"ltr"}var x="rovingFocusGroup.onEntryFocus",A={bubbles:!1,cancelable:!0},E="RovingFocusGroup",[M,T,I]=function(e){let t=e+"CollectionProvider",[n,r]=u(t),[l,i]=n(t,{collectionRef:{current:null},itemMap:new Map}),c=e=>{let{scope:t,children:n}=e,r=o.useRef(null),i=o.useRef(new Map).current;return(0,a.jsx)(l,{scope:t,itemMap:i,collectionRef:r,children:n})};c.displayName=t;let s=e+"CollectionSlot",p=d(s),m=o.forwardRef((e,t)=>{let{scope:n,children:r}=e,o=f(t,i(s,n).collectionRef);return(0,a.jsx)(p,{ref:o,children:r})});m.displayName=s;let v=e+"CollectionItemSlot",y="data-radix-collection-item",h=d(v),g=o.forwardRef((e,t)=>{let{scope:n,children:r,...l}=e,u=o.useRef(null),c=f(t,u),s=i(v,n);return o.useEffect(()=>(s.itemMap.set(u,{ref:u,...l}),()=>void s.itemMap.delete(u))),(0,a.jsx)(h,{[y]:"",ref:c,children:r})});return g.displayName=v,[{Provider:c,Slot:m,ItemSlot:g},function(t){let n=i(e+"CollectionConsumer",t);return o.useCallback(()=>{let e=n.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll("[".concat(y,"]")));return Array.from(n.itemMap.values()).sort((e,n)=>t.indexOf(e.ref.current)-t.indexOf(n.ref.current))},[n.collectionRef,n.itemMap])},r]}(E),[S,_]=u(E,[I]),[j,D]=S(E),O=o.forwardRef((e,t)=>(0,a.jsx)(M.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,a.jsx)(M.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,a.jsx)(k,{...e,ref:t})})}));O.displayName=E;var k=o.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:n,orientation:r,loop:l=!1,dir:u,currentTabStopId:c,defaultCurrentTabStopId:s,onCurrentTabStopIdChange:d,onEntryFocus:p,preventScrollOnEntryFocus:m=!1,...v}=e,y=o.useRef(null),h=f(t,y),g=C(u),[b,N]=R({prop:c,defaultProp:null!=s?s:null,onChange:d,caller:E}),[M,I]=o.useState(!1),S=function(e){let t=o.useRef(e);return o.useEffect(()=>{t.current=e}),o.useMemo(()=>(...e)=>t.current?.(...e),[])}(p),_=T(n),D=o.useRef(!1),[O,k]=o.useState(0);return o.useEffect(()=>{let e=y.current;if(e)return e.addEventListener(x,S),()=>e.removeEventListener(x,S)},[S]),(0,a.jsx)(j,{scope:n,orientation:r,dir:g,loop:l,currentTabStopId:b,onItemFocus:o.useCallback(e=>N(e),[N]),onItemShiftTab:o.useCallback(()=>I(!0),[]),onFocusableItemAdd:o.useCallback(()=>k(e=>e+1),[]),onFocusableItemRemove:o.useCallback(()=>k(e=>e-1),[]),children:(0,a.jsx)(w.div,{tabIndex:M||0===O?-1:0,"data-orientation":r,...v,ref:h,style:{outline:"none",...e.style},onMouseDown:i(e.onMouseDown,()=>{D.current=!0}),onFocus:i(e.onFocus,e=>{let t=!D.current;if(e.target===e.currentTarget&&t&&!M){let t=new CustomEvent(x,A);if(e.currentTarget.dispatchEvent(t),!t.defaultPrevented){let e=_().filter(e=>e.focusable);U([e.find(e=>e.active),e.find(e=>e.id===b),...e].filter(Boolean).map(e=>e.ref.current),m)}}D.current=!1}),onBlur:i(e.onBlur,()=>I(!1))})})}),F="RovingFocusGroupItem",P=o.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:n,focusable:r=!0,active:l=!1,tabStopId:u,children:c,...s}=e,f=g(),d=u||f,p=D(F,n),m=p.currentTabStopId===d,v=T(n),{onFocusableItemAdd:y,onFocusableItemRemove:h,currentTabStopId:b}=p;return o.useEffect(()=>{if(r)return y(),()=>h()},[r,y,h]),(0,a.jsx)(M.ItemSlot,{scope:n,id:d,focusable:r,active:l,children:(0,a.jsx)(w.span,{tabIndex:m?0:-1,"data-orientation":p.orientation,...s,ref:t,onMouseDown:i(e.onMouseDown,e=>{r?p.onItemFocus(d):e.preventDefault()}),onFocus:i(e.onFocus,()=>p.onItemFocus(d)),onKeyDown:i(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey){p.onItemShiftTab();return}if(e.target!==e.currentTarget)return;let t=function(e,t,n){var r;let o=(r=e.key,"rtl"!==n?r:"ArrowLeft"===r?"ArrowRight":"ArrowRight"===r?"ArrowLeft":r);if(!("vertical"===t&&["ArrowLeft","ArrowRight"].includes(o))&&!("horizontal"===t&&["ArrowUp","ArrowDown"].includes(o)))return L[o]}(e,p.orientation,p.dir);if(void 0!==t){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let n=v().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===t)n.reverse();else if("prev"===t||"next"===t){"prev"===t&&n.reverse();let r=n.indexOf(e.currentTarget);n=p.loop?function(e,t){return e.map((n,r)=>e[(t+r)%e.length])}(n,r+1):n.slice(r+1)}setTimeout(()=>U(n))}}),children:"function"==typeof c?c({isCurrentTabStop:m,hasTabStop:null!=b}):c})})});P.displayName=F;var L={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function U(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=document.activeElement;for(let r of e)if(r===n||(r.focus({preventScroll:t}),document.activeElement!==n))return}var $=e=>{let{present:t,children:n}=e,r=function(e){var t,n;let[r,l]=o.useState(),i=o.useRef(null),a=o.useRef(e),u=o.useRef("none"),[c,s]=(t=e?"mounted":"unmounted",n={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},o.useReducer((e,t)=>{let r=n[e][t];return null!=r?r:e},t));return o.useEffect(()=>{let e=K(i.current);u.current="mounted"===c?e:"none"},[c]),v(()=>{let t=i.current,n=a.current;if(n!==e){let r=u.current,o=K(t);e?s("MOUNT"):"none"===o||(null==t?void 0:t.display)==="none"?s("UNMOUNT"):n&&r!==o?s("ANIMATION_OUT"):s("UNMOUNT"),a.current=e}},[e,s]),v(()=>{if(r){var e;let t;let n=null!==(e=r.ownerDocument.defaultView)&&void 0!==e?e:window,o=e=>{let o=K(i.current).includes(e.animationName);if(e.target===r&&o&&(s("ANIMATION_END"),!a.current)){let e=r.style.animationFillMode;r.style.animationFillMode="forwards",t=n.setTimeout(()=>{"forwards"===r.style.animationFillMode&&(r.style.animationFillMode=e)})}},l=e=>{e.target===r&&(u.current=K(i.current))};return r.addEventListener("animationstart",l),r.addEventListener("animationcancel",o),r.addEventListener("animationend",o),()=>{n.clearTimeout(t),r.removeEventListener("animationstart",l),r.removeEventListener("animationcancel",o),r.removeEventListener("animationend",o)}}s("ANIMATION_END")},[r,s]),{isPresent:["mounted","unmountSuspended"].includes(c),ref:o.useCallback(e=>{i.current=e?getComputedStyle(e):null,l(e)},[])}}(t),l="function"==typeof n?n({present:r.isPresent}):o.Children.only(n),i=f(r.ref,function(e){var t,n;let r=null===(t=Object.getOwnPropertyDescriptor(e.props,"ref"))||void 0===t?void 0:t.get,o=r&&"isReactWarning"in r&&r.isReactWarning;return o?e.ref:(o=(r=null===(n=Object.getOwnPropertyDescriptor(e,"ref"))||void 0===n?void 0:n.get)&&"isReactWarning"in r&&r.isReactWarning)?e.props.ref:e.props.ref||e.ref}(l));return"function"==typeof n||r.isPresent?o.cloneElement(l,{ref:i}):null};function K(e){return(null==e?void 0:e.animationName)||"none"}$.displayName="Presence";var V="Tabs",[W,z]=u(V,[_]),G=_(),[B,q]=W(V),H=o.forwardRef((e,t)=>{let{__scopeTabs:n,value:r,onValueChange:o,defaultValue:l,orientation:i="horizontal",dir:u,activationMode:c="automatic",...s}=e,f=C(u),[d,p]=R({prop:r,onChange:o,defaultProp:null!=l?l:"",caller:V});return(0,a.jsx)(B,{scope:n,baseId:g(),value:d,onValueChange:p,orientation:i,dir:f,activationMode:c,children:(0,a.jsx)(w.div,{dir:f,"data-orientation":i,...s,ref:t})})});H.displayName=V;var X="TabsList",Y=o.forwardRef((e,t)=>{let{__scopeTabs:n,loop:r=!0,...o}=e,l=q(X,n),i=G(n);return(0,a.jsx)(O,{asChild:!0,...i,orientation:l.orientation,dir:l.dir,loop:r,children:(0,a.jsx)(w.div,{role:"tablist","aria-orientation":l.orientation,...o,ref:t})})});Y.displayName=X;var Z="TabsTrigger",J=o.forwardRef((e,t)=>{let{__scopeTabs:n,value:r,disabled:o=!1,...l}=e,u=q(Z,n),c=G(n),s=et(u.baseId,r),f=en(u.baseId,r),d=r===u.value;return(0,a.jsx)(P,{asChild:!0,...c,focusable:!o,active:d,children:(0,a.jsx)(w.button,{type:"button",role:"tab","aria-selected":d,"aria-controls":f,"data-state":d?"active":"inactive","data-disabled":o?"":void 0,disabled:o,id:s,...l,ref:t,onMouseDown:i(e.onMouseDown,e=>{o||0!==e.button||!1!==e.ctrlKey?e.preventDefault():u.onValueChange(r)}),onKeyDown:i(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&u.onValueChange(r)}),onFocus:i(e.onFocus,()=>{let e="manual"!==u.activationMode;d||o||!e||u.onValueChange(r)})})})});J.displayName=Z;var Q="TabsContent",ee=o.forwardRef((e,t)=>{let{__scopeTabs:n,value:r,forceMount:l,children:i,...u}=e,c=q(Q,n),s=et(c.baseId,r),f=en(c.baseId,r),d=r===c.value,p=o.useRef(d);return o.useEffect(()=>{let e=requestAnimationFrame(()=>p.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,a.jsx)($,{present:l||d,children:n=>{let{present:r}=n;return(0,a.jsx)(w.div,{"data-state":d?"active":"inactive","data-orientation":c.orientation,role:"tabpanel","aria-labelledby":s,hidden:!r,id:f,tabIndex:0,...u,ref:t,style:{...e.style,animationDuration:p.current?"0s":void 0},children:r&&i})}})});function et(e,t){return"".concat(e,"-trigger-").concat(t)}function en(e,t){return"".concat(e,"-content-").concat(t)}ee.displayName=Q;var er=H,eo=Y,el=J,ei=ee},8244:(e,t,n)=>{n.d(t,{_:()=>r});function r(e,t,n){if(!t.has(e))throw TypeError("attempted to "+n+" private field on non-instance");return t.get(e)}},5359:(e,t,n)=>{n.d(t,{_:()=>o});var r=n(8244);function o(e,t){var n=(0,r._)(e,t,"get");return n.get?n.get.call(e):n.value}},587:(e,t,n)=>{n.d(t,{_:()=>r});function r(e,t,n){!function(e,t){if(t.has(e))throw TypeError("Cannot initialize the same private elements twice on an object")}(e,t),t.set(e,n)}},5465:(e,t,n)=>{n.d(t,{_:()=>o});var r=n(8244);function o(e,t,n){var o=(0,r._)(e,t,"set");return!function(e,t,n){if(t.set)t.set.call(e,n);else{if(!t.writable)throw TypeError("attempted to set read only private field");t.value=n}}(e,o,n),n}}}]);