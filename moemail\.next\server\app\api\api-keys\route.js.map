{"version": 3, "file": "app/api/api-keys/route.js", "mappings": "sFAAA,6DCAA,qHGAA,uVFSO,IAAMA,EAAU,OAEhB,eAAeC,IAEpB,GAAI,CADkB,MAAMC,CAAAA,EAAAA,EAAAA,EAAAA,CAAeA,CAACC,EAAAA,EAAWA,CAACC,cAAc,EAEpE,OAAOC,EAAAA,EAAYA,CAACC,IAAI,CAAC,CAAEC,MAAO,MAAO,EAAG,CAAEC,OAAQ,GAAI,GAG5D,IAAMC,EAAU,MAAMC,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,GAC1B,GAAI,CACF,IAAMC,EAAKC,CAAAA,EAAAA,EAAAA,CAAAA,CAAQA,GACbC,EAAO,MAAMF,EAAGG,KAAK,CAACC,OAAO,CAACC,QAAQ,CAAC,CAC3CC,MAAOC,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACH,EAAAA,OAAOA,CAACI,MAAM,CAAEV,EAASW,IAAI,CAACC,EAAE,EAC1CC,QAASC,CAAAA,EAAAA,EAAAA,CAAAA,CAAIA,CAACR,EAAAA,OAAOA,CAACS,SAAS,CACjC,GAEA,OAAOnB,EAAAA,EAAYA,CAACC,IAAI,CAAC,CACvBS,QAASF,EAAKY,GAAG,CAACC,GAAQ,EACxB,CADwB,EACrBA,CAAG,CACNA,IAAKC,MACP,GACF,EACF,CAAE,MAAOpB,EAAO,CAEd,OADAqB,QAAQrB,KAAK,CAAC,4BAA6BA,GACpCF,EAAAA,EAAYA,CAACC,IAAI,CACtB,CAAEC,MAAO,gBAAiB,EAC1B,CAAEC,OAAQ,GAAI,EAElB,CACF,CAEO,eAAeqB,EAAKC,CAAgB,EAEzC,GAAI,CADkB,MAAM5B,CAAAA,EAAAA,EAAAA,EAAAA,CAAeA,CACvB,EADwBC,EAAWA,CAACC,cAAc,EAEpE,OAAOC,EAAAA,EAAYA,CAACC,IAAI,CAAC,CAAEC,MAAO,MAAO,EAAG,CAAEC,OAAQ,GAAI,GAG5D,IAAMC,EAAU,MAAMC,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,GAC1B,GAAI,CACF,GAAM,MAAEqB,CAAI,CAAE,CAAG,MAAMD,EAAQxB,IAAI,GACnC,GAAI,CAACyB,GAAMC,OACT,CADiB,MACV3B,EAAAA,EAAYA,CAACC,IAAI,CACtB,CAAEC,MAAO,QAAS,EAClB,CAAEC,OAAQ,GAAI,GAIlB,IAAMkB,EAAM,CAAC,GAAG,EAAEO,CAAAA,EAAAA,EAAAA,EAAAA,CAAMA,CAAC,KAAK,CACxBtB,EAAKC,CAAAA,EAAAA,EAAAA,CAAAA,CAAQA,GASnB,OAPA,MAAMD,EAAGuB,MAAM,CAACnB,EAAAA,OAAOA,EAAEoB,MAAM,CAAC,MAC9BJ,MACAL,EACAP,OAAQV,EAASW,IAAI,CAACC,EAAE,CACxBe,UAAW,IAAIC,KAAKA,KAAKC,GAAG,GAAK,MAAM,EACzC,GAEOjC,EAAAA,EAAYA,CAACC,IAAI,CAAC,KAAEoB,CAAI,EACjC,CAAE,MAAOnB,EAAO,CAEd,OADAqB,QAAQrB,KAAK,CAAC,4BAA6BA,GACpCF,EAAAA,EAAYA,CAACC,IAAI,CACtB,CAAEC,MAAO,eAAgB,EACzB,CAAEC,OAAQ,GAAI,EAElB,CACF,CCnEA,UAAwB,qBAAmB,EAC3C,YACA,KAAc,GAAS,WACvB,2BACA,yBACA,iBACA,mCACA,CAAK,CACL,sFACA,iBAVA,GAWA,QAAY,EACZ,CAAC,EAID,kBAAQ,wCAAsD,EAC9D,aACA,MAAW,QAAW,EACtB,mBACA,sBACA,CAAK,CACL,CCpBA,oEACA,EAFA,4BAEA,4BACA,MACI,QAA8B,EAClC,2BACA,0BACA,wBACA,gBAAyB,OAAqB,EAC9C,uBACA,CAAS,CACT,CAAK,EAEE,MAAqB,EAC5B,EAAe,GAAsB,MAAM,EAAkB,CAC7D,QAD6D,GAf7D,CAAoB,MAAQ,OAcM,CAdN,CAAW,sBAA2B,aAAe,kDAAyD,uOAAsQ,2RAAuT,kBAAkB,QAAQ,uDAA2D,0DAA8D,iBAAsB,gBAAkB,sEAAgF,kBAAoB,uCAA6C,MAAQ,iBAAmB,2BAA+B,gHAAkI,uBAAyB,8FAA0G,aAAiB,WAAa,sEAA6E,uBAAuB,iCAAmC,QAAQ,EAAE,SAAW,oBAAsB,QAAQ,GAAG,4EAAgF,WAAa,SAAW,2CAAiD,UAAY,+BAAqC,UAAY,oCAA0C,QAAU,uCAA6C,OAAS,yCAA+C,QAAU,0CAAiD,MAAQ,+CAAsD,iBAAmB,ywBAA+5B,4CAA8C,kNAAqQ,qBAAyB,s+CAA0/C,oIAiB7wJ,CAAC,CAAC,EAAC,wEEHI,eACP,SACA,+CACA,UACA,GDvBA,kECuB2B,UAE3B,QACA", "sources": ["webpack://_N_E/external commonjs \"node:async_hooks\"", "webpack://_N_E/external commonjs \"node:buffer\"", "webpack://_N_E/./app/api/api-keys/route.ts", "webpack://_N_E/./app/api/api-keys/route.ts?9d07", "webpack://_N_E/?9b99", "webpack://_N_E/./node_modules/nanoid/url-alphabet/index.js", "webpack://_N_E/./node_modules/nanoid/index.browser.js"], "sourcesContent": ["module.exports = require(\"node:async_hooks\");", "module.exports = require(\"node:buffer\");", "import { auth } from \"@/lib/auth\"\r\nimport { createDb } from \"@/lib/db\"\r\nimport { api<PERSON>eys } from \"@/lib/schema\"\r\nimport { nanoid } from \"nanoid\"\r\nimport { NextResponse } from \"next/server\"\r\nimport { checkPermission } from \"@/lib/auth\"\r\nimport { PERMISSIONS } from \"@/lib/permissions\"\r\nimport { desc, eq } from \"drizzle-orm\"\r\n\r\nexport const runtime = \"edge\"\r\n\r\nexport async function GET() {\r\n  const hasPermission = await checkPermission(PERMISSIONS.MANAGE_API_KEY)\r\n  if (!hasPermission) {\r\n    return NextResponse.json({ error: \"权限不足\" }, { status: 403 })\r\n  }\r\n\r\n  const session = await auth()\r\n  try {\r\n    const db = createDb()\r\n    const keys = await db.query.apiKeys.findMany({\r\n      where: eq(apiKeys.userId, session!.user.id!),\r\n      orderBy: desc(apiKeys.createdAt),\r\n    })\r\n\r\n    return NextResponse.json({\r\n      apiKeys: keys.map(key => ({\r\n        ...key,\r\n        key: undefined\r\n      }))\r\n    })\r\n  } catch (error) {\r\n    console.error(\"Failed to fetch API keys:\", error)\r\n    return NextResponse.json(\r\n      { error: \"获取 API Keys 失败\" },\r\n      { status: 500 }\r\n    )\r\n  }\r\n}\r\n\r\nexport async function POST(request: Request) {\r\n  const hasPermission = await checkPermission(PERMISSIONS.MANAGE_API_KEY)\r\n  if (!hasPermission) {\r\n    return NextResponse.json({ error: \"权限不足\" }, { status: 403 })\r\n  }\r\n\r\n  const session = await auth()\r\n  try {\r\n    const { name } = await request.json() as { name: string }\r\n    if (!name?.trim()) {\r\n      return NextResponse.json(\r\n        { error: \"名称不能为空\" },\r\n        { status: 400 }\r\n      )\r\n    }\r\n\r\n    const key = `mk_${nanoid(32)}`\r\n    const db = createDb()\r\n    \r\n    await db.insert(apiKeys).values({\r\n      name,\r\n      key,\r\n      userId: session!.user.id!,\r\n      expiresAt: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000), // 1 year\r\n    })\r\n\r\n    return NextResponse.json({ key })\r\n  } catch (error) {\r\n    console.error(\"Failed to create API key:\", error)\r\n    return NextResponse.json(\r\n      { error: \"创建 API Key 失败\" },\r\n      { status: 500 }\r\n    )\r\n  }\r\n} ", "import { AppRouteRouteModule } from \"next/dist/server/route-modules/app-route/module.compiled\";\nimport { RouteKind } from \"next/dist/server/route-kind\";\nimport { patchFetch as _patchFetch } from \"next/dist/server/lib/patch-fetch\";\nimport * as userland from \"F:\\\\CODE\\\\Project\\\\tempmail\\\\moemail\\\\app\\\\api\\\\api-keys\\\\route.ts\";\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new AppRouteRouteModule({\n    definition: {\n        kind: RouteKind.APP_ROUTE,\n        page: \"/api/api-keys/route\",\n        pathname: \"/api/api-keys\",\n        filename: \"route\",\n        bundlePath: \"app/api/api-keys/route\"\n    },\n    resolvedPagePath: \"F:\\\\CODE\\\\Project\\\\tempmail\\\\moemail\\\\app\\\\api\\\\api-keys\\\\route.ts\",\n    nextConfigOutput,\n    userland\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return _patchFetch({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\nexport { routeModule, workAsyncStorage, workUnitAsyncStorage, serverHooks, patchFetch,  };\n\n//# sourceMappingURL=app-route.js.map", "var _self___RSC_MANIFEST;\nimport { createServerModuleMap } from \"next/dist/server/app-render/action-utils\";\nimport { setReferenceManifestsSingleton } from \"next/dist/server/app-render/encryption-utils\";\nimport { EdgeRouteModuleWrapper } from \"next/dist/server/web/edge-route-module-wrapper\";\n// Import the userland code.\nimport * as module from \"next-app-loader?name=app%2Fapi%2Fapi-keys%2Froute&page=%2Fapi%2Fapi-keys%2Froute&pagePath=private-next-app-dir%2Fapi%2Fapi-keys%2Froute.ts&appDir=F%3A%5CCODE%5CProject%5Ctempmail%5Cmoemail%5Capp&appPaths=%2Fapi%2Fapi-keys%2Froute&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!private-next-app-dir/api/api-keys/route.ts?__next_edge_ssr_entry__\";\nconst nextConfig = {\"env\":{},\"eslint\":{\"ignoreDuringBuilds\":false},\"typescript\":{\"ignoreBuildErrors\":false,\"tsconfigPath\":\"tsconfig.json\"},\"distDir\":\".next\",\"cleanDistDir\":true,\"assetPrefix\":\"\",\"cacheMaxMemorySize\":52428800,\"configOrigin\":\"next.config.ts\",\"useFileSystemPublicRoutes\":true,\"generateEtags\":true,\"pageExtensions\":[\"tsx\",\"ts\",\"jsx\",\"js\"],\"poweredByHeader\":true,\"compress\":true,\"images\":{\"deviceSizes\":[640,750,828,1080,1200,1920,2048,3840],\"imageSizes\":[16,32,48,64,96,128,256,384],\"path\":\"/_next/image\",\"loader\":\"default\",\"loaderFile\":\"\",\"domains\":[],\"disableStaticImages\":false,\"minimumCacheTTL\":60,\"formats\":[\"image/webp\"],\"dangerouslyAllowSVG\":false,\"contentSecurityPolicy\":\"script-src 'none'; frame-src 'none'; sandbox;\",\"contentDispositionType\":\"attachment\",\"remotePatterns\":[{\"protocol\":\"https\",\"hostname\":\"avatars.githubusercontent.com\"}],\"unoptimized\":false},\"devIndicators\":{\"appIsrStatus\":true,\"buildActivity\":true,\"buildActivityPosition\":\"bottom-right\"},\"onDemandEntries\":{\"maxInactiveAge\":60000,\"pagesBufferLength\":5},\"amp\":{\"canonicalBase\":\"\"},\"basePath\":\"\",\"sassOptions\":{},\"trailingSlash\":false,\"i18n\":null,\"productionBrowserSourceMaps\":false,\"excludeDefaultMomentLocales\":true,\"serverRuntimeConfig\":{},\"publicRuntimeConfig\":{},\"reactProductionProfiling\":false,\"reactStrictMode\":null,\"reactMaxHeadersLength\":6000,\"httpAgentOptions\":{\"keepAlive\":true},\"logging\":{},\"expireTime\":31536000,\"staticPageGenerationTimeout\":60,\"modularizeImports\":{\"@mui/icons-material\":{\"transform\":\"@mui/icons-material/{{member}}\"},\"lodash\":{\"transform\":\"lodash/{{member}}\"}},\"outputFileTracingRoot\":\"F:\\\\CODE\\\\Project\\\\tempmail\\\\moemail\",\"experimental\":{\"cacheLife\":{\"default\":{\"stale\":300,\"revalidate\":900,\"expire\":4294967294},\"seconds\":{\"stale\":0,\"revalidate\":1,\"expire\":60},\"minutes\":{\"stale\":300,\"revalidate\":60,\"expire\":3600},\"hours\":{\"stale\":300,\"revalidate\":3600,\"expire\":86400},\"days\":{\"stale\":300,\"revalidate\":86400,\"expire\":604800},\"weeks\":{\"stale\":300,\"revalidate\":604800,\"expire\":2592000},\"max\":{\"stale\":300,\"revalidate\":2592000,\"expire\":4294967294}},\"cacheHandlers\":{},\"cssChunking\":true,\"multiZoneDraftMode\":false,\"appNavFailHandling\":false,\"prerenderEarlyExit\":true,\"serverMinification\":true,\"serverSourceMaps\":false,\"linkNoTouchStart\":false,\"caseSensitiveRoutes\":false,\"clientSegmentCache\":false,\"preloadEntriesOnStart\":true,\"clientRouterFilter\":true,\"clientRouterFilterRedirects\":false,\"fetchCacheKeyPrefix\":\"\",\"middlewarePrefetch\":\"flexible\",\"optimisticClientCache\":true,\"manualClientBasePath\":false,\"cpus\":11,\"memoryBasedWorkersCount\":false,\"imgOptConcurrency\":null,\"imgOptTimeoutInSeconds\":7,\"imgOptMaxInputPixels\":268402689,\"imgOptSequentialRead\":null,\"isrFlushToDisk\":true,\"workerThreads\":false,\"optimizeCss\":false,\"nextScriptWorkers\":false,\"scrollRestoration\":false,\"externalDir\":false,\"disableOptimizedLoading\":false,\"gzipSize\":true,\"craCompat\":false,\"esmExternals\":true,\"fullySpecified\":false,\"swcTraceProfiling\":false,\"forceSwcTransforms\":false,\"largePageDataBytes\":128000,\"turbo\":{\"root\":\"F:\\\\CODE\\\\Project\\\\tempmail\\\\moemail\"},\"typedRoutes\":false,\"typedEnv\":false,\"parallelServerCompiles\":false,\"parallelServerBuildTraces\":false,\"ppr\":false,\"authInterrupts\":false,\"reactOwnerStack\":false,\"webpackMemoryOptimizations\":false,\"optimizeServerReact\":true,\"useEarlyImport\":false,\"staleTimes\":{\"dynamic\":0,\"static\":300},\"serverComponentsHmrCache\":true,\"staticGenerationMaxConcurrency\":8,\"staticGenerationMinPagesPerWorker\":25,\"dynamicIO\":false,\"inlineCss\":false,\"optimizePackageImports\":[\"lucide-react\",\"date-fns\",\"lodash-es\",\"ramda\",\"antd\",\"react-bootstrap\",\"ahooks\",\"@ant-design/icons\",\"@headlessui/react\",\"@headlessui-float/react\",\"@heroicons/react/20/solid\",\"@heroicons/react/24/solid\",\"@heroicons/react/24/outline\",\"@visx/visx\",\"@tremor/react\",\"rxjs\",\"@mui/material\",\"@mui/icons-material\",\"recharts\",\"react-use\",\"effect\",\"@effect/schema\",\"@effect/platform\",\"@effect/platform-node\",\"@effect/platform-browser\",\"@effect/platform-bun\",\"@effect/sql\",\"@effect/sql-mssql\",\"@effect/sql-mysql2\",\"@effect/sql-pg\",\"@effect/sql-squlite-node\",\"@effect/sql-squlite-bun\",\"@effect/sql-squlite-wasm\",\"@effect/sql-squlite-react-native\",\"@effect/rpc\",\"@effect/rpc-http\",\"@effect/typeclass\",\"@effect/experimental\",\"@effect/opentelemetry\",\"@material-ui/core\",\"@material-ui/icons\",\"@tabler/icons-react\",\"mui-core\",\"react-icons/ai\",\"react-icons/bi\",\"react-icons/bs\",\"react-icons/cg\",\"react-icons/ci\",\"react-icons/di\",\"react-icons/fa\",\"react-icons/fa6\",\"react-icons/fc\",\"react-icons/fi\",\"react-icons/gi\",\"react-icons/go\",\"react-icons/gr\",\"react-icons/hi\",\"react-icons/hi2\",\"react-icons/im\",\"react-icons/io\",\"react-icons/io5\",\"react-icons/lia\",\"react-icons/lib\",\"react-icons/lu\",\"react-icons/md\",\"react-icons/pi\",\"react-icons/ri\",\"react-icons/rx\",\"react-icons/si\",\"react-icons/sl\",\"react-icons/tb\",\"react-icons/tfi\",\"react-icons/ti\",\"react-icons/vsc\",\"react-icons/wi\"]},\"bundlePagesRouterDependencies\":false,\"configFile\":\"F:\\\\CODE\\\\Project\\\\tempmail\\\\moemail\\\\next.config.ts\",\"configFileName\":\"next.config.ts\"}\nconst maybeJSONParse = (str)=>str ? JSON.parse(str) : undefined;\nconst rscManifest = (_self___RSC_MANIFEST = self.__RSC_MANIFEST) == null ? void 0 : _self___RSC_MANIFEST[\"/api/api-keys/route\"];\nconst rscServerManifest = maybeJSONParse(self.__RSC_SERVER_MANIFEST);\nif (rscManifest && rscServerManifest) {\n    setReferenceManifestsSingleton({\n        page: \"/api/api-keys/route\",\n        clientReferenceManifest: rscManifest,\n        serverActionsManifest: rscServerManifest,\n        serverModuleMap: createServerModuleMap({\n            serverActionsManifest: rscServerManifest\n        })\n    });\n}\nexport const ComponentMod = module;\nexport default EdgeRouteModuleWrapper.wrap(module.routeModule, {\n    nextConfig\n});\n\n//# sourceMappingURL=edge-app-route.js.map", "export const urlAlphabet =\n  'useandom-26T198340PX75pxJACKVERYMINDBUSHWOLF_GQZbfghjklqvwyzrict'\n", "import { urlAlphabet as scopedUrlAlphabet } from './url-alphabet/index.js'\nexport { urlAlphabet } from './url-alphabet/index.js'\nexport let random = bytes => crypto.getRandomValues(new Uint8Array(bytes))\nexport let customRandom = (alphabet, defaultSize, getRandom) => {\n  let mask = (2 << Math.log2(alphabet.length - 1)) - 1\n  let step = -~((1.6 * mask * defaultSize) / alphabet.length)\n  return (size = defaultSize) => {\n    let id = ''\n    while (true) {\n      let bytes = getRandom(step)\n      let j = step | 0\n      while (j--) {\n        id += alphabet[bytes[j] & mask] || ''\n        if (id.length >= size) return id\n      }\n    }\n  }\n}\nexport let customAlphabet = (alphabet, size = 21) =>\n  customRandom(alphabet, size | 0, random)\nexport let nanoid = (size = 21) => {\n  let id = ''\n  let bytes = crypto.getRandomValues(new Uint8Array((size |= 0)))\n  while (size--) {\n    id += scopedUrlAlphabet[bytes[size] & 63]\n  }\n  return id\n}\n"], "names": ["runtime", "GET", "checkPermission", "PERMISSIONS", "MANAGE_API_KEY", "NextResponse", "json", "error", "status", "session", "auth", "db", "createDb", "keys", "query", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "find<PERSON>any", "where", "eq", "userId", "user", "id", "orderBy", "desc", "createdAt", "map", "key", "undefined", "console", "POST", "request", "name", "trim", "nanoid", "insert", "values", "expiresAt", "Date", "now"], "sourceRoot": "", "ignoreList": [5, 6]}