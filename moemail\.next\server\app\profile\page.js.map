{"version": 3, "file": "app/profile/page.js", "mappings": "qFAAA,+DCAA,oHEAA,ooCDWA,MACA,CACA,GACA,CACA,UACA,UACA,CACA,uBAAiC,EACjC,MAfA,IAAoB,uCAAiG,CAerH,gEAES,EACF,CACP,CAEA,UACA,sBAAoC,wCAAkP,aACtR,SACA,aACA,WACA,eACA,CACA,EACA,CACO,CACP,CACA,QApCA,IAAsB,uCAA0F,CAoChH,yDACA,aApCA,IAAsB,uCAAgF,CAoCtG,+CACA,WApCA,IAAsB,sCAAgF,CAoCtG,+CACA,cApCA,IAAsB,uCAAmF,CAoCzG,kDACA,UACA,sBAAoC,wCAAkP,aACtR,SACA,aACA,WACA,eACA,CACA,EACA,CAEA,mEAKO,GACP,QAH6B,EAI7B,UAHA,OADgD,KAChD,SAIA,EAGO,MAAwB,oBAAkB,EACjD,YACA,KAAc,GAAS,UACvB,qBACA,oBAEA,cACA,YACA,YACK,CACL,UACA,YACA,CACA,CAAC,sCC5DD,iCAEA,gCALA,CAEA,CAGA,EAWA,gCACA,wBACA,oCACA,0DACA,gCAEA,+BACA,oDACA,MACI,QAA8B,EAClC,qBACA,0BACA,wBACA,gBAAyB,OAAqB,EAC9C,uBACA,CAAS,CACT,CAAK,EAEL,IAAM,EAAS,OAAS,EACxB,UAAe,GAAU,KACzB,IAvBA,GAwBA,qBACA,OA9BA,KA+BA,OAAW,GACX,SA/BA,KAgCA,YA/BA,KAgCA,SAnCA,KAoCA,gBACA,YAAgB,KAChB,wBACA,0BACA,wBACA,cAlCA,OAmCA,6BA5BA,OA6BA,OAnCA,CAAoB,MAAQ,SAAW,sBAA2B,aAAe,kDAAyD,uOAAsQ,2RAAuT,kBAAkB,QAAQ,uDAA2D,0DAA8D,iBAAsB,gBAAkB,sEAAgF,kBAAoB,uCAA6C,MAAQ,iBAAmB,2BAA+B,gHAAkI,uBAAyB,8FAA0G,aAAiB,WAAa,sEAA6E,uBAAuB,iCAAmC,QAAQ,EAAE,SAAW,oBAAsB,QAAQ,GAAG,4EAAgF,WAAa,SAAW,2CAAiD,UAAY,+BAAqC,UAAY,oCAA0C,QAAU,uCAA6C,OAAS,yCAA+C,QAAU,0CAAiD,MAAQ,+CAAsD,iBAAmB,ywBAA+5B,4CAA8C,kNAAqQ,qBAAyB,s+CAA0/C,qIAoC7wJ,oCACA,mBACA,wBAtDA,KAuDA,2BACA,CAAC,EACM,EAAqB,EACb,cACf,MAAW,OAAO,EAClB,KACA,IAJmC,YAIX,KACxB,QAAiB,CACjB,CAAK,CACL,mBC1EA,uCAAiJ,CAEjJ,sCAAsJ,CAEtJ,uCAAoJ,CAEpJ,uCAAkI,kBCNlI,uCAAiJ,CAEjJ,uCAAsJ,CAEtJ,uCAAoJ,CAEpJ,uCAAkI,oHCO5H,MAAQ,OAAgB,CAAC,OAAS,EACtC,CACE,OACA,CACE,CAAG,4NACH,GAAK,SACP,EACF,CACA,CAAC,MAAQ,EAAE,EAAG,CAAY,cAAK,SAAU,EAC1C,ECTK,EAAM,OAAgB,CAAC,KAAO,EAClC,CAAC,MAAQ,EAAE,EAAG,CAA0B,4BAAK,SAAU,EACvD,CAAC,MAAQ,EAAE,EAAG,CAA2B,6BAAK,SAAU,EACxD,CAAC,MAAQ,EAAE,EAAG,CAAW,aAAK,SAAU,EACzC,ECJK,EAAQ,OAAgB,CAAC,OAAS,EACtC,CAAC,UAAY,EAAE,OAAQ,CAAmC,qCAAK,SAAU,EACzE,CAAC,OAAQ,CAAE,GAAI,CAAM,OAAI,CAAM,OAAI,IAAM,IAAI,IAAM,KAAK,SAAU,EAClE,CAAC,OAAQ,CAAE,GAAI,CAAM,OAAI,CAAM,OAAI,IAAM,IAAI,IAAM,KAAK,SAAU,EAClE,CAAC,OAAQ,CAAE,GAAI,CAAM,OAAI,CAAM,OAAI,IAAM,IAAI,IAAM,KAAK,SAAU,EACnE,4BCLK,MAAW,OAAgB,CAAC,UAAY,EAC5C,CACE,OACA,CACE,CAAG,yjBACH,GAAK,SACP,EACF,CACA,CAAC,QAAU,EAAE,EAAI,MAAM,CAAI,OAAM,CAAG,KAAK,GAAK,UAAU,EACzD,0ICPK,EAAc,SAGd,CAAC,EAAqB,EAAiB,CAAI,OAAkB,CAAC,GAG9D,CAAC,EAHsC,EAGN,CAAI,EAHoC,GAczE,EAAe,MAXyE,MAWzE,CACnB,CAAC,EAAiC,KAChC,GAAM,eACJ,EACA,OACA,QAAS,iBACT,WACA,WACA,QACA,EAAQ,qBACR,OACA,EACA,GAAG,EACL,CAAI,EACE,CAAC,EAAQ,EAAS,CAAU,MAAV,IAAU,CAAmC,IAAI,EACnE,EAAe,OAAe,CAAC,EAAc,GAAU,EAAU,IAAI,CAAC,CAC7B,UAAO,GAEhD,EAFqD,CAErC,GAAS,GAAQ,CAAC,CAAC,EAAO,QAAQ,MAAM,EACxD,CAAC,CAD2D,EACjD,EAAO,EAAU,CAAI,OAAoB,CAAC,CACzD,KAAM,EACN,YAAa,EACb,SAAU,CACZ,CAAC,EAED,MACE,WAAC,GAAe,MAAO,UAAe,EAAkB,WACtD,oBAAC,IAAS,CAAC,OAAV,CACC,KAAK,SACL,KAAK,SACL,eAAc,EACd,gBAAe,EACf,aAAY,EAAS,GACrB,IAD4B,YACb,EAAW,GAAK,gBAC/B,QACA,EACC,GAAG,EACJ,IAAK,EACL,QAAS,OAAoB,CAAC,EAAM,QAAS,IAC3C,EAAW,GAAiB,CAAC,GACzB,IACF,EAAiC,EAFK,KAEL,CAAU,CAD1B,CACgC,qBAAqB,EAIjE,EAAiC,QAAS,GAAM,gBAAgB,EAEzE,CAAC,IAEF,GACC,UAAC,GACC,CADF,OACW,EACT,QAAS,CAAC,EAAiC,aAC3C,QACA,UACA,WACA,WACA,OACA,EAIA,MAAO,CAAE,UAAW,mBAAoB,IAC1C,CAEJ,CAEJ,GAGF,EAAO,YAAc,EAMrB,IAAM,EAAa,cAMb,EAAoB,aACxB,CAAC,EAAsC,KACrC,GAAM,eAAE,EAAe,GAAG,EAAW,CAAI,EACnC,EAAU,EAAiB,EAAY,GAC7C,MACE,IAFwD,CAExD,KAAC,IAAS,CAAC,KAAV,CACC,aAAY,EAAS,EAAQ,OAAO,EACpC,gBAAe,EAAQ,SAAW,GAAK,OACtC,GAAG,EACJ,IAAK,GAGX,GAGF,EAAY,YAAc,EAW1B,IAAM,EAAc,IAClB,GAAM,SAAE,UAAS,EAAS,WAAU,EAAM,GAAG,EAAW,CAAI,EACtD,EAAY,IADsC,IACtC,CAAyB,IAAI,EACzC,EAAc,OAAW,CAAC,GAC1B,EAAc,EADmB,CACnB,IAAO,CAAC,GAe5B,IAfmC,GAG7B,YAAU,KACd,IAAM,EAAQ,EAAI,QAGZ,EAAa,OADO,yBADP,OAAO,iBAAiB,UACoB,SAAS,EAC1C,IAC9B,GAAI,IAAgB,GAAW,EAAY,CACzC,IAAM,EAAQ,IAAI,MAAM,QAAS,SAAE,CAAQ,CAAC,EAC5C,EAAW,KAAK,EAAO,GACvB,EAAM,EADwB,WACxB,CAAc,EACtB,CACF,EAF6B,CAEzB,EAAa,EAAS,EAAQ,EAGhC,UAAC,SACC,KAAK,WACL,eAAW,EACX,eAAgB,EACf,GAAG,EACJ,SAAU,OACV,EACA,MAAO,CACL,GAAG,EAAM,MACT,GAAG,EACH,SAAU,WACV,cAAe,OACf,QAAS,EACT,OAAQ,CACV,GAGN,EAEA,SAAS,EAAS,GAAkB,OAC3B,EAAU,UAAY,WAC/B,gBC/KA,IAAMA,EAASC,EAAAA,SAAHD,CAAmB,CAG7B,CAAC,WAAEE,CAAS,CAAE,GAAGC,EAAO,CAAEC,IAC1B,UAACC,EAAqB,CACpBH,CADoB,SACTI,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CACX,8XACAJ,GAED,GAAGC,CAAK,CACTC,IAAKA,WAEL,UDsKU,ECtKa,CACrBF,EADqB,QACVI,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CACX,kLAKRN,GAAOO,UAADP,CAAY,CAAGK,EAAsBE,EAAD,SAAY,2BCZhD,MAAO,OAAgB,CAAC,MAAQ,EACpC,CACE,OACA,CACE,CAAG,mIACH,GAAK,SACP,EACF,CACA,CAAC,MAAQ,EAAE,EAAG,CAA8B,gCAAK,SAAU,EAC5D,ECTK,EAAY,QAAiB,WAAa,EAAC,CAAC,OAAQ,CAAE,EAAG,gBAAkB,KAAK,CAAS,QAAC,CAAC,CAAC,4BCI3F,SAASC,IACd,GAAM,CAACC,EAASC,EAAW,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GACjC,CAACC,EAAKC,EAAO,CAAGF,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,IACzB,CAACG,EAASC,EAAW,CAAGJ,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,IACjC,CAACK,EAASC,EAAW,CAAGN,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GACjC,CAACO,EAAUC,EAAY,CAAGR,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GACnC,CAACS,EAAgBC,EAAkB,CAAGV,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GAC/C,OAAEW,CAAK,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAQA,GAa1B,GAAIH,EACF,MACE,QAFgB,CAEhB,EAACI,MAAAA,CAAItB,UAAU,wBACb,UAACsB,MAAAA,CAAItB,UAAU,yFACb,UAACuB,EAAAA,CAAOA,CAAAA,CAACvB,UAAU,wCAErB,UAACsB,MAAAA,UACC,UAACE,IAAAA,CAAExB,UAAU,yCAAgC,gBAMrD,IAAMyB,EAAe,MAAOC,IAE1B,GADAA,CACI,CADFC,cAAc,GACXjB,GAELG,EAFU,CAEC,GACX,GAAI,CAOF,GAAI,CAACe,CANO,MAAMC,MAAM,eAAgB,CACtCC,OAAQ,OACRC,QAAS,CAAE,eAAgB,kBAAmB,EAC9CC,KAAMC,KAAKC,SAAS,CAAC,KAAExB,UAAKH,CAAQ,EACtC,IAES4B,EAAE,CAAE,MAAM,MAAU,kBAE7Bf,EAAM,CACJgB,MAAO,OACPC,YAAa,eACf,EACF,CAAE,MAAOC,EAAQ,CACflB,EAAM,CACJgB,MAAO,OACPC,YAAa,QACbE,QAAS,aACX,EACF,QAAU,CACR1B,GAAW,EACb,EACF,EAEM2B,EAAa,UACjB,GAAK9B,CAAD,EAEJK,EAFU,CAEC,GACX,GAAI,CAOF,GAAI,CAACa,CANO,MAAMC,MAAM,oBAAqB,CAC3CC,OAAQ,OACRC,QAAS,CAAE,eAAgB,kBAAmB,EAC9CC,KAAMC,KAAKC,SAAS,CAAC,CAAExB,KAAI,EAC7B,IAESyB,EAAE,CAAE,MAAM,MAAU,QAE7Bf,EAAM,CACJgB,MAAO,OACPC,YAAa,6BACf,EACF,CAAE,MAAOC,EAAQ,CACflB,EAAM,CACJgB,MAAO,OACPC,YAAa,mBACbE,QAAS,aACX,EACF,QAAU,CACRxB,GAAW,EACb,EACF,EAEA,MACE,WAAC0B,OAAAA,CAAKC,SAAUjB,EAAczB,UAAU,sBACtC,WAACsB,MAAAA,CAAItB,UAAU,8CACb,WAACsB,MAAAA,CAAItB,UAAU,wBACb,UAAC2C,EAAAA,CAAKA,CAAAA,UAAC,eACP,UAACrB,MAAAA,CAAItB,UAAU,yCAAgC,wBAIjD,UAACF,EAAMA,CACL8C,QAASrC,EADJT,gBAEYU,OAIpBD,GACC,WAACe,MAAAA,CAAItB,UAAU,sBACb,WAACsB,MAAAA,CAAItB,UAAU,sBACb,UAAC2C,EAAAA,CAAKA,CAAAA,CAACE,QAAQ,uBAAc,gBAC7B,WAACvB,MAAAA,CAAItB,UAAU,uBACb,UAAC8C,EAAAA,CAAKA,CAAAA,CACJC,GAAG,cACHC,YAAY,8BACZC,MAAOvC,EACPwC,SAAU,GAAOvC,EAAOe,EAAEyB,MAAM,CAACF,KAAK,EACtCG,KAAK,MACLC,QAAQ,MAEV,UAACC,EAAAA,CAAMA,CAAAA,CAACF,KAAK,SAASG,SAAU3C,EAASZ,UAAU,yBAChDY,EACC,UAACW,EAAAA,CAAOA,CAAAA,CAACvB,UAAU,yBAEnB,OAGJ,UAACwD,EAAAA,EAAeA,CAAAA,UACd,WAACC,EAAAA,EAAOA,CAAAA,WACN,UAACC,EAAAA,EAAcA,CAAAA,CAACC,OAAO,aACrB,UAACL,EAAAA,CAAMA,CAAAA,CACLF,KAAK,SACLb,QAAQ,UACRqB,QAASpB,EACTe,SAAUzC,GAAW,CAACJ,WAErBI,EACC,UAACS,EAAAA,CAAOA,CAAAA,CAACvB,UAAU,yBAEnB,UAAC6D,EAAIA,CAAC7D,CAAD6D,SAAW,gBAItB,UAACC,EAAAA,EAAcA,CAAAA,UACb,UAACtC,IAAAA,UAAE,+BAKX,UAACA,IAAAA,CAAExB,UAAU,yCAAgC,uCAK/C,WAACsB,MAAAA,CAAItB,UAAU,qBXjGF,CWkGX,WAAC+D,SAAAA,CACCX,KAAK,SACLpD,UAAU,gGACV4D,QAAS,IAAM3C,EAAY,CAACD,aAE3BA,EAAW,UAACgD,EAASA,CAAChE,MAADgE,IAAW,YAAe,UAACC,EAAAA,CAAWA,CAAAA,CAACjE,UAAU,YAAa,cAIrFgB,GACC,WAACM,MAAAA,CAAItB,UAAU,sDACb,UAACwB,IAAAA,UAAE,0CACH,WAAC0C,MAAAA,CAAIlE,UAAU,8CAAoC,iCAClB,KAAK,kCAItC,UAACwB,IAAAA,UAAE,WACH,UAAC0C,MAAAA,CAAIlE,UAAU,2DACZ,CAAC;;;;;;;;2BAQO,EAAEmE,OAAOC,QAAQ,CAACC,IAAI,CAAC;CACjD,CAAC,cASF,2BC/LA,IAAMC,EAAY,CAChB,CAACC,EAAAA,EAAKA,CAACC,IAAI,CAAC,CAAEC,EACd,CADiBA,EAChBF,EAAKA,CAACG,MAAM,CAAC,CAAEC,EAChB,CAACJ,EAAAA,EAAKA,CAACK,QAAQ,CAAC,CAAEC,EAAAA,CAAKA,EAGnBC,EAAY,CAChB,CAACP,EAAAA,EAAKA,CAACC,IAAI,CAAC,CAAE,KACd,CAACD,EAAAA,EAAKA,CAACG,MAAM,CAAC,CAAE,KAChB,CAACH,EAAAA,EAAKA,CAACK,QAAQ,CAAC,CAAE,IACpB,EAIO,SAASG,IACd,GAAM,CAACC,EAAYC,EAAc,CAAGxE,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,IACvC,CAACG,EAASC,EAAW,CAAGJ,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GACjC,CAACyE,EAAYC,EAAc,CAAG1E,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAqB8D,EAAAA,EAAKA,CAACG,MAAM,EACvE,CAAEtD,OAAK,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAQA,GAEpB+D,EAAe,UACnB,GAAKJ,CAAD,EAEJnE,GAAW,GACX,GAHiB,CAIf,IAAMe,EAAM,MAAMC,MAAM,mBAAoB,CAC1CC,OAAQ,OACRC,QAAS,CAAE,eAAgB,kBAAmB,EAC9CC,KAAMC,KAAKC,SAAS,CAAC,YAAE8C,CAAW,EACpC,GACMK,EAAO,MAAMzD,EAAI0D,IAAI,GAW3B,GAAI,CAAC1D,EAAIO,EAAE,CAAE,MAAM,MAAUkD,EAAKE,KAAK,EAAI,QAE3C,GAAI,CAACF,EAAKG,IAAI,CAAE,CACdpE,EAAM,CACJgB,MAAO,QACPC,YAAa,kBACbE,QAAS,aACX,GACA,MACF,CAEA,GAAI8C,EAAKG,IAAI,CAACC,IAAI,GAAKP,EAAY,CACjC9D,EAAM,CACJgB,MAAO,CAAC,IAAI,EAAE0C,CAAS,CAACI,EAAW,EAAE,CACrC7C,YAAa,QACf,GACA,MACF,CAEA,IAAMqD,EAAa,MAAM7D,MAAM,qBAAsB,CACnDC,OAAQ,OACRC,QAAS,CAAE,eAAgB,kBAAmB,EAC9CC,KAAMC,KAAKC,SAAS,CAAC,CACnByD,OAAQN,EAAKG,IAAI,CAACzC,EAAE,CACpB6C,SAAUV,CACZ,EACF,GAEA,GAAI,CAACQ,EAAWvD,EAAE,CAAE,CAClB,IAAMoD,EAAQ,MAAMG,EAAWJ,IAAI,EACnC,OAAM,MAAUC,EAAMA,KAAK,EAAI,OACjC,CAEAnE,EAAM,CACJgB,MAAO,OACPC,YAAa,CAAC,KAAK,EAAEgD,EAAKG,IAAI,CAACK,QAAQ,EAAIR,EAAKG,IAAI,CAACM,KAAK,CAAC,GAAG,EAAEhB,CAAS,CAACI,EAAW,EAAE,GAEzFD,EAAc,GAChB,CAAE,MAAOM,EAAO,CACdnE,EAAM,CACJgB,MAAO,OACPC,YAAakD,aAAiBQ,MAAQR,EAAMS,OAAO,CAAG,QACtDzD,QAAS,aACX,EACF,QAAU,CACR1B,GAAW,EACb,EACF,EAEMoF,EAAO3B,CAAS,CAACY,EAAW,CAElC,MACE,WAAC5D,MAAAA,CAAItB,UAAU,oEACb,WAACsB,MAAAA,CAAItB,UAAU,yCACb,UAACiG,EAAAA,CAAKjG,UAAU,yBAChB,UAACkG,KAAAA,CAAGlG,UAAU,iCAAwB,YAGxC,WAACsB,MAAAA,CAAItB,UAAU,sBACb,WAACsB,MAAAA,CAAItB,UAAU,uBACb,UAACsB,MAAAA,CAAItB,UAAU,kBACb,UAAC8C,EAAAA,CAAKA,CAAAA,CACJG,MAAO+B,EACP9B,SAAU,GAAO+B,EAAcvD,EAAEyB,MAAM,CAACF,KAAK,EAC7CD,YAAY,eAGhB,WAACmD,EAAAA,EAAMA,CAAAA,CAAClD,MAAOiC,EAAYkB,cAAe,GAAWjB,EAAclC,aACjE,UAACoD,EAAAA,EAAaA,CAAAA,CAACrG,UAAU,gBACvB,UAACsG,EAAAA,EAAWA,CAAAA,CAAAA,KAEd,WAACC,EAAAA,EAAaA,CAAAA,WACZ,UAACC,EAAAA,EAAUA,CAAAA,CAACvD,MAAOsB,EAAAA,EAAKA,CAACC,IAAI,UAC3B,WAAClD,MAAAA,CAAItB,UAAU,oCACb,UAACyE,EAAGA,CAACzE,UAAU,YAAY,UAI/B,UAACwG,EAAAA,EAAUA,CAAAA,CAACvD,MAAOsB,EAAAA,EAAKA,CAACG,MAAM,UAC7B,WAACpD,MAAAA,CAAItB,UAAU,oCACb,UAAC2E,EAAKA,CAAC3E,EAAD2E,QAAW,YAAY,UAIjC,UAAC6B,EAAAA,EAAUA,CAAAA,CAACvD,MAAOsB,EAAAA,EAAKA,CAACK,QAAQ,UAC/B,WAACtD,MAAAA,CAAItB,UAAU,oCACb,UAAC6E,EAAAA,CAAKA,CAAAA,CAAC7E,UAAU,YAAY,mBAQvC,UAACsD,EAAAA,CAAMA,CAAAA,CACLM,QAASwB,EACT7B,SAAU3C,GAAW,CAACoE,EAAWyB,IAAI,GACrCzG,UAAU,kBAETY,EACC,UAACW,EAAAA,CAAOA,CAAAA,CAACvB,UAAU,yBAEnB,CAAC,EAAE,EAAE8E,CAAS,CAACI,EAAW,EAAE,QAMxC,CChKO,SAASwB,IACd,GAAM,CAAErB,KAAMsB,CAAO,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAUA,GAC9BC,EAAQF,GAASnB,MAAMqB,MAY7B,MAAO,CACLC,gBAXsB,GACtB,EAAKD,EAAD,CACGE,CAAAA,EAAAA,CADK,CACLA,EAAAA,CAAaA,CAACF,EAAMG,GAAG,CAACC,GAAKA,EAAEC,IAAI,EAAaC,GAUvDC,QAPc,GACd,EAAKP,EAAD,CACGA,EAAMQ,EADD,EACK,CAACJ,GAAKA,CADJ,CACMC,IAAI,GAAKzB,GAMlCoB,OACF,CACF,gBCPO,SAASS,IACd,GAAM,CAACC,EAAaC,EAAe,CAAG/G,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAS,IACjD,CAACgH,EAAcC,EAAgB,CAAGjH,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAS,IACnD,CAACkH,EAAcC,EAAgB,CAAGnH,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAS,IACnD,CAACoH,EAAWC,EAAa,CAAGrH,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAASsH,EAAAA,CAAYA,CAACC,iBAAiB,CAACC,QAAQ,IACpF,CAACrH,EAASC,EAAW,CAAGJ,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GACjC,OAAEW,CAAK,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAQA,GAuBpB6G,EAAa,UACjBrH,EAAW,IACX,GAAI,CAYF,GAAI,CAXQ,OAAMgB,MAAM,cAAe,CACrCC,OAAQ,OACRC,QAAS,CAAE,eAAgB,kBAAmB,EAC9CC,KAAMC,KAAKC,SAAS,CAAC,aACnBqF,eACAE,eACAE,EACAE,UAAWA,GAAaE,EAAAA,CAAYA,CAACC,iBAAiB,CAACC,QAAQ,EACjE,EACF,IAES9F,EAAE,CAAE,MAAM,MAAU,QAE7Bf,EAAM,CACJgB,MAAO,OACPC,YAAa,SACf,EACF,CAAE,MAAOkD,EAAO,CACdnE,EAAM,CACJgB,MAAO,OACPC,YAAakD,aAAiBQ,MAAQR,EAAMS,OAAO,CAAG,QACtDzD,QAAS,aACX,EACF,QAAU,CACR1B,GAAW,EACb,CACF,EAEA,MACE,WAACS,MAAAA,CAAItB,UAAU,oEACb,WAACsB,MAAAA,CAAItB,UAAU,yCACb,UAACmI,EAAQA,CAACnI,KAADmI,KAAW,yBACpB,UAACjC,KAAAA,CAAGlG,UAAU,iCAAwB,YAGxC,WAACsB,MAAAA,CAAItB,UAAU,sBACb,WAACsB,MAAAA,CAAItB,UAAU,oCACb,UAACoI,OAAAA,CAAKpI,UAAU,mBAAU,aAC1B,WAACmG,EAAAA,EAAMA,CAAAA,CAAClD,MAAOsE,EAAanB,cAAeoB,YACzC,UAACnB,EAAAA,EAAaA,CAAAA,CAACrG,UAAU,gBACvB,UAACsG,EAAAA,EAAWA,CAAAA,CAAAA,KAEd,WAACC,EAAAA,EAAaA,CAAAA,WACZ,UAACC,EAAAA,EAAUA,CAAAA,CAACvD,MAAOsB,EAAAA,EAAKA,CAACC,IAAI,UAAE,OAC/B,UAACgC,EAAAA,EAAUA,CAAAA,CAACvD,MAAOsB,EAAAA,EAAKA,CAACG,MAAM,UAAE,OACjC,UAAC8B,EAAAA,EAAUA,CAAAA,CAACvD,MAAOsB,EAAAA,EAAKA,CAACK,QAAQ,UAAE,gBAKzC,WAACtD,MAAAA,CAAItB,UAAU,oCACb,UAACoI,OAAAA,CAAKpI,UAAU,mBAAU,UAC1B,UAACsB,MAAAA,CAAItB,UAAU,kBACb,UAAC8C,EAAAA,CAAKA,CAAAA,CACJG,MAAOwE,EACPvE,SAAU,GAAOwE,EAAgBhG,EAAEyB,MAAM,CAACF,KAAK,EAC/CD,YAAY,+CAKlB,WAAC1B,MAAAA,CAAItB,UAAU,oCACb,UAACoI,OAAAA,CAAKpI,UAAU,mBAAU,aAC1B,UAACsB,MAAAA,CAAItB,UAAU,kBACb,UAAC8C,EAAAA,CAAKA,CAAAA,CACJG,MAAO0E,EACPzE,SAAU,GAAO0E,EAAgBlG,EAAEyB,MAAM,CAACF,KAAK,EAC/CD,YAAY,oBAKlB,WAAC1B,MAAAA,CAAItB,UAAU,oCACb,UAACoI,OAAAA,CAAKpI,UAAU,mBAAU,YAC1B,UAACsB,MAAAA,CAAItB,UAAU,kBACb,UAAC8C,EAAAA,CAAKA,CAAAA,CACJM,KAAK,SACLiF,IAAI,IACJC,IAAI,MACJrF,MAAO4E,EACP3E,SAAU,GAAO4E,EAAapG,EAAEyB,MAAM,CAACF,KAAK,EAC5CD,YAAa,CAAC,IAAI,EAAE+E,EAAAA,CAAYA,CAACC,iBAAiB,EAAE,QAK1D,UAAC1E,EAAAA,CAAMA,CAAAA,CACLM,QAASsE,EACT3E,SAAU3C,EACVZ,UAAU,kBACX,YAMT,uFChHO,SAASuI,KACd,GAAM,CAACC,EAASC,EAAW,CAAGhI,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAW,EAAE,EAC7C,CAACG,EAASC,EAAW,CAAGJ,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GACjC,CAACiI,EAAkBC,EAAoB,CAAGlI,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GACnD,CAACmI,EAAYC,EAAc,CAAGpI,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,IACvC,CAACqI,EAAQC,EAAU,CAAGtI,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAgB,MAC9C,CAAEW,OAAK,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAQA,GACpB,iBAAE2H,CAAe,CAAE,CAAGC,CAAAA,EAAAA,GAAAA,CAAAA,CAAOA,GAC7B,CAACC,EAAcC,EAAgB,CAAG1I,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GAC3C,CAAC2I,EAAWC,EAAa,CAAG5I,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GACrC,iBAAEqG,CAAe,CAAE,CAAGJ,IACtB4C,EAAkBxC,EAAgByC,EAAAA,EAAWA,CAACC,IADP9C,UACqB,EAE5D+C,EAAe,UACnB,GAAI,CACF,IAAM7H,EAAM,MAAMC,MAAM,iBACxB,GAAI,CAACD,EAAIO,EAAE,CAAE,MAAM,MAAU,kBAC7B,IAAMkD,EAAO,MAAMzD,EAAI0D,IAAI,GAC3BmD,EAAWpD,EAAKmD,OAAO,CACzB,CAAE,MAAOjD,EAAO,CACdmE,QAAQnE,KAAK,CAACA,GACdnE,EAAM,CACJgB,MAAO,OACPC,YAAa,mBACbE,QAAS,aACX,EACF,QAAU,CACR8G,GAAa,EACf,CACF,EAQM,QAAEM,CAAM,CAAE,CAAGC,CAAAA,EAAAA,GAAAA,CAAAA,CAASA,GAEtBC,EAAe,UACnB,GAAKjB,CAAD,CAAYnC,IAAI,IAAI,GAEb,GACX,GAAI,CACF,IAAM7E,EAAM,MAAMC,MAAM,gBAAiB,CACvCC,OAAQ,OACRC,QAAS,CAAE,eAAgB,kBAAmB,EAC9CC,KAAMC,KAAKC,SAAS,CAAC,CAAEgF,KAAM0B,CAAW,EAC1C,GAEA,GAAI,CAAChH,EAAIO,EAAE,CAAE,MAAM,MAAU,iBAE7B,IAAMkD,EAAO,MAAMzD,EAAI0D,IAAI,GAC3ByD,EAAU1D,EAAKyE,GAAG,EAClBL,GACF,CAAE,MAAOlE,EAAO,CACdnE,EAAM,CACJgB,MAAO,OACPC,YAAakD,aAAiBQ,MAAQR,EAAMS,OAAO,CAAG,QACtDzD,QAAS,aACX,GACAoG,GAAoB,EACtB,QAAU,CACR9H,GAAW,EACb,EACF,EAQMkJ,EAAe,MAAOhH,EAAYxC,KACtC,GAAI,CAOF,GAAI,CAACqB,CANO,MAAMC,MAAM,CAAC,cAAc,EAAEkB,EAAAA,CAAI,CAAE,CAC7CjB,OAAQ,QACRC,QAAS,CAAE,eAAgB,kBAAmB,EAC9CC,KAAMC,KAAKC,SAAS,CAAC,SAAE3B,CAAQ,EACjC,IAES4B,EAAE,CAAE,MAAM,MAAU,QAE7BsG,EAAWuB,GACTA,EAAKhD,GAAG,CAAC8C,GACPA,EAAI/G,EAAE,GAAKA,EAAK,CAAE,GAAG+G,CAAG,CAAEvJ,SAAQ,EAAIuJ,GAG5C,CAAE,MAAOvE,EAAO,CACdmE,QAAQnE,KAAK,CAACA,GACdnE,EAAM,CACJgB,MAAO,OACPC,YAAa,kBACbE,QAAS,aACX,EACF,CACF,EAEM0H,EAAe,MAAOlH,IAC1B,GAAI,CAKF,GAAI,CAACnB,CAJO,MAAMC,MAAM,CAAC,cAAc,EAAEkB,EAAAA,CAAI,CAAE,CAC7CjB,OAAQ,QACV,IAESK,EAAE,CAAE,MAAM,MAAU,QAE7BsG,EAAWuB,GAAQA,EAAKE,MAAM,CAACJ,GAAOA,EAAI/G,EAAE,GAAKA,IACjD3B,EAAM,CACJgB,MAAO,OACPC,YAAa,aACf,EACF,CAAE,MAAOkD,EAAO,CACdmE,QAAQnE,KAAK,CAACA,GACdnE,EAAM,CACJgB,MAAO,OACPC,YAAa,gBACbE,QAAS,aACX,EACF,CACF,EAEA,MACE,WAACjB,MAAAA,CAAItB,UAAU,8EACb,WAACsB,MAAAA,CAAItB,UAAU,mDACb,WAACsB,MAAAA,CAAItB,UAAU,oCACb,UAACmK,EAAAA,CAAGA,CAAAA,CAACnK,UAAU,yBACf,UAACkG,KAAAA,CAAGlG,UAAU,iCAAwB,gBAGtCsJ,GACE,WAACc,GAAAA,EAAMA,CAAAA,CAACC,KAAM3B,EAAkB4B,aAAc3B,YAC5C,UAAC4B,GAAAA,EAAaA,CAAAA,CAAC5G,OAAO,aACpB,WAACL,EAAAA,CAAMA,CAAAA,CAACtD,UAAU,QAAQ4D,QAAS,IAAM+E,GAAoB,aAC3D,UAAC6B,GAAAA,CAAIA,CAAAA,CAACxK,UAAU,YAAY,kBAIhC,WAACyK,GAAAA,EAAaA,CAAAA,WACZ,WAACC,GAAAA,EAAYA,CAAAA,WACX,UAACC,GAAAA,EAAWA,CAAAA,UACT7B,EAAS,eAAiB,iBAE5BA,GACC,UAAC8B,GAAAA,EAAiBA,CAAAA,CAAC5K,UAAU,4BAAmB,6BAMnD,EAYC,UAACsB,MAAAA,CAAItB,UAAU,0BACb,WAACsB,MAAAA,CAAItB,UAAU,sBACb,UAAC2C,EAAAA,CAAKA,CAAAA,UAAC,YACP,WAACrB,MAAAA,CAAItB,UAAU,uBACb,UAAC8C,EAAAA,CAAKA,CAAAA,CACJG,MAAO6F,EACP+B,QAAQ,IACR7K,UAAU,sBAEZ,UAACsD,EAAAA,CAAMA,CAAAA,CACLf,QAAQ,UACRuI,KAAK,OACLlH,QAAS,IAAMoF,EAAgBF,YAE/B,UAACiC,GAAAA,CAAIA,CAAAA,CAAC/K,UAAU,sBAzBxB,UAACsB,MAAAA,CAAItB,UAAU,0BACb,WAACsB,MAAAA,CAAItB,UAAU,sBACb,UAAC2C,EAAAA,CAAKA,CAAAA,UAAC,OACP,UAACG,EAAAA,CAAKA,CAAAA,CACJG,MAAO2F,EACP1F,SAAU,GAAO2F,EAAcnH,EAAEyB,MAAM,CAACF,KAAK,EAC7CD,YAAY,0BA0BpB,WAACgI,GAAAA,EAAYA,CAAAA,WACX,UAACC,GAAAA,EAAWA,CAAAA,CAACtH,OAAO,aAClB,UAACL,EAAAA,CAAMA,CAAAA,CACLf,QAAQ,UACRqB,QAvHM,CAuHGsH,IAtH3BvC,GAAoB,GACpBE,EAAc,IACdE,EAAU,KACZ,EAoHoBxF,SAAU3C,WAETkI,EAAS,KAAO,SAGpB,CAACA,GACA,UAACxF,EAAAA,CAAMA,CAAAA,CACLM,QAASiG,EACTtG,SAAU3C,GAAW,CAACgI,EAAWnC,IAAI,YAEpC7F,EACC,UAACW,EAAAA,CAAOA,CAAAA,CAACvB,UAAU,yBAEnB,mBAYhB,EAWE,UAACsB,MAAAA,CAAItB,UAAU,qBACZoJ,EACC,WAAC9H,MAAAA,CAAItB,UAAU,uCACb,UAACsB,MAAAA,CAAItB,UAAU,yFACb,UAACuB,EAAAA,CAAOA,CAAAA,CAACvB,UAAU,wCAErB,UAACsB,MAAAA,UACC,UAACE,IAAAA,CAAExB,UAAU,yCAAgC,gBAG5B,IAAnBwI,EAAQ2C,MAAM,CAChB,WAAC7J,MAAAA,CAAItB,UAAU,uCACb,UAACsB,MAAAA,CAAItB,UAAU,yFACb,UAACmK,EAAAA,CAAGA,CAAAA,CAACnK,UAAU,2BAEjB,WAACsB,MAAAA,WACC,UAAC8J,KAAAA,CAAGpL,UAAU,+BAAsB,gBACpC,UAACwB,IAAAA,CAAExB,UAAU,8CAAqC,+CAMtD,iCACGwI,EAAQxB,GAAG,CAAE8C,GACZ,WAACxI,MAAAA,CAECtB,UAAU,4EAEV,WAACsB,MAAAA,CAAItB,UAAU,sBACb,UAACsB,MAAAA,CAAItB,UAAU,uBAAe8J,EAAI5C,IAAI,GACtC,WAAC5F,MAAAA,CAAItB,UAAU,0CAAgC,OACxC,IAAIqL,KAAKvB,EAAIwB,SAAS,EAAEC,cAAc,SAG/C,WAACjK,MAAAA,CAAItB,UAAU,oCACb,UAACF,EAAMA,CACL8C,QAASkH,EADJhK,OACe,CACpB0L,gBAAiB,GAAazB,EAAaD,EAAI/G,EAAE,CAAEH,KAErD,UAACU,EAAAA,CAAMA,CAAAA,CACLf,QAAQ,QACRuI,KAAK,OACLlH,QAAS,IAAMqG,EAAaH,EAAI/G,EAAE,WAElC,UAAC0I,GAAAA,CAAMA,CAAAA,CAACzL,UAAU,mBAnBjB8J,EAAI/G,EAAE,GAyBf,WAACzB,MAAAA,CAAItB,UAAU,2BACb,WAAC+D,SAAAA,CACCX,KAAK,SACLpD,UAAU,gGACV4D,QAAS,IAAMuF,EAAgB,CAACD,aAE/BA,EAAe,UAAClF,EAASA,CAAChE,MAADgE,IAAW,YAAe,UAACC,EAAAA,CAAWA,CAAAA,CAACjE,UAAU,YAAa,YAIzFkJ,GACC,WAAC5H,MAAAA,CAAItB,UAAU,oDACb,WAACsB,MAAAA,CAAItB,UAAU,sBACb,WAACsB,MAAAA,CAAItB,UAAU,8CACb,UAACsB,MAAAA,CAAItB,UAAU,+BAAsB,WACrC,UAACsD,EAAAA,CAAMA,CAAAA,CACLf,QAAQ,QACRuI,KAAK,OACLlH,QAAS,IAAMoF,EACb,CAAC,aAAa,EAAE7E,OAAOC,QAAQ,CAACsH,QAAQ,CAAC,EAAE,EAAEvH,OAAOC,QAAQ,CAACC,IAAI,CAAC;;;;;;;IAO5F,CAAC,WAGuB,UAAC0G,GAAAA,CAAIA,CAAAA,CAAC/K,UAAU,iBAGpB,UAACkE,MAAAA,CAAIlE,UAAU,8DACZ,CAAC,aAAa,EAAEmE,OAAOC,QAAQ,CAACsH,QAAQ,CAAC,EAAE,EAAEvH,OAAOC,QAAQ,CAACC,IAAI,CAAC;;;;;;;IAOzF,CAAC,MAIiB,WAAC/C,MAAAA,CAAItB,UAAU,sBACb,WAACsB,MAAAA,CAAItB,UAAU,8CACb,UAACsB,MAAAA,CAAItB,UAAU,+BAAsB,WACrC,UAACsD,EAAAA,CAAMA,CAAAA,CACLf,QAAQ,QACRuI,KAAK,OACLlH,QAAS,IAAMoF,EACb,CAAC,KAAK,EAAE7E,OAAOC,QAAQ,CAACsH,QAAQ,CAAC,EAAE,EAAEvH,OAAOC,QAAQ,CAACC,IAAI,CAAC;8BAC1D,CAAC,WAGH,UAAC0G,GAAAA,CAAIA,CAAAA,CAAC/K,UAAU,iBAGpB,UAACkE,MAAAA,CAAIlE,UAAU,8DACZ,CAAC,KAAK,EAAEmE,OAAOC,QAAQ,CAACsH,QAAQ,CAAC,EAAE,EAAEvH,OAAOC,QAAQ,CAACC,IAAI,CAAC;8BACvD,CAAC,MAIT,WAAC/C,MAAAA,CAAItB,UAAU,sBACb,WAACsB,MAAAA,CAAItB,UAAU,8CACb,UAACsB,MAAAA,CAAItB,UAAU,+BAAsB,WACrC,UAACsD,EAAAA,CAAMA,CAAAA,CACLf,QAAQ,QACRuI,KAAK,OACLlH,QAAS,IAAMoF,EACb,CAAC,KAAK,EAAE7E,OAAOC,QAAQ,CAACsH,QAAQ,CAAC,EAAE,EAAEvH,OAAOC,QAAQ,CAACC,IAAI,CAAC;8BAC1D,CAAC,WAGH,UAAC0G,GAAAA,CAAIA,CAAAA,CAAC/K,UAAU,iBAGpB,UAACkE,MAAAA,CAAIlE,UAAU,8DACZ,CAAC,KAAK,EAAEmE,OAAOC,QAAQ,CAACsH,QAAQ,CAAC,EAAE,EAAEvH,OAAOC,QAAQ,CAACC,IAAI,CAAC;8BACvD,CAAC,MAIT,WAAC/C,MAAAA,CAAItB,UAAU,sBACb,WAACsB,MAAAA,CAAItB,UAAU,8CACb,UAACsB,MAAAA,CAAItB,UAAU,+BAAsB,WACrC,UAACsD,EAAAA,CAAMA,CAAAA,CACLf,QAAQ,QACRuI,KAAK,OACLlH,QAAS,IAAMoF,EACb,CAAC,KAAK,EAAE7E,OAAOC,QAAQ,CAACsH,QAAQ,CAAC,EAAE,EAAEvH,OAAOC,QAAQ,CAACC,IAAI,CAAC;8BAC1D,CAAC,WAGH,UAAC0G,GAAAA,CAAIA,CAAAA,CAAC/K,UAAU,iBAGpB,UAACkE,MAAAA,CAAIlE,UAAU,8DACZ,CAAC,KAAK,EAAEmE,OAAOC,QAAQ,CAACsH,QAAQ,CAAC,EAAE,EAAEvH,OAAOC,QAAQ,CAACC,IAAI,CAAC;8BACvD,CAAC,MAIT,WAAC/C,MAAAA,CAAItB,UAAU,+CACb,UAACwB,IAAAA,UAAE,QACH,WAACmK,KAAAA,CAAG3L,UAAU,iDACZ,UAAC4L,KAAAA,UAAG,oCACJ,UAACA,KAAAA,UAAG,sBACJ,UAACA,KAAAA,UAAG,wBACJ,UAACA,KAAAA,UAAG,6EACJ,UAACA,KAAAA,UAAG,kDACJ,UAACA,KAAAA,UAAG,wCACJ,UAACA,KAAAA,UAAG,6CA9KpB,WAACtK,MAAAA,CAAItB,UAAU,mDACb,UAACwB,IAAAA,UAAE,0BACH,UAACA,IAAAA,CAAExB,UAAU,gBAAO,mBAElB2J,GAAQhC,cACN,WAACnG,IAAAA,CAAExB,UAAU,iBAAO,WAAS2J,EAAOhC,YAAY,SAsLhE,iBCpaM,OAAM,OAAgB,CAAC,KAAO,EAClC,CACE,OACA,CACE,CAAG,yGACH,GAAK,SACP,EACF,CACA,CAAC,QAAU,EAAE,EAAI,MAAM,CAAI,OAAM,CAAG,KAAK,GAAK,UAAU,EACzD,ECTK,GAAS,OAAgB,CAAC,QAAU,EACxC,CACE,OACA,CACE,CAAG,kGACH,GAAK,SACP,EACF,CACA,CAAC,MAAQ,EAAE,EAAG,CAAwC,0CAAK,SAAU,EACrE,CACE,OACA,CACE,CAAG,gGACH,GAAK,SACP,EACF,CACA,CAAC,MAAQ,EAAE,EAAG,CAAc,gBAAK,SAAU,EAC5C,ECjBK,GAAQ,OAAgB,CAAC,OAAS,EACtC,CAAC,MAAQ,EAAE,EAAG,CAA6C,+CAAK,SAAU,EAC1E,CAAC,QAAU,EAAE,EAAI,KAAK,CAAI,MAAK,CAAG,KAAK,GAAK,SAAS,EACrD,CAAC,MAAQ,EAAE,EAAG,CAA8B,gCAAK,SAAU,EAC3D,CAAC,MAAQ,EAAE,EAAG,CAA6B,+BAAK,SAAU,EAC3D,ECyBM,SAASkE,KACd,GAAM,CAACC,EAAaC,EAAe,CAAGtL,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAoB,EAAE,EAC9D,CAACuL,EAAcC,EAAgB,CAAGxL,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAqB,EAAE,EACjE,CAACG,EAASC,EAAW,CAAGJ,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GACjC,CAACyL,EAAgBC,EAAkB,CAAG1L,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GAC/C,CAAC2L,EAAmBC,EAAqB,CAAG5L,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GACrD,CAAC6L,EAAYC,EAAc,CAAG9L,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,CAC3C+L,IAAK,EACP,GACM,CAACC,EAAcC,EAAgB,CAAGjM,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,IAC3C,CAACkM,EAAqBC,EAAuB,CAAGnM,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAyB,MACjF,CAACoM,EAAsBC,EAAwB,CAAGrM,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAoB,EAAE,EAChF,CAACsM,EAAgBC,EAAkB,CAAGvM,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAW,EAAE,EAC3D,CAACwM,EAAaC,EAAe,CAAGzM,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAqB,SAC7D,CAAC0M,EAASC,EAAW,CAAG3M,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GACjC,CAACyI,EAAcC,EAAgB,CAAG1I,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GAC3C,CAAC2I,EAAWC,EAAa,CAAG5I,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GACrC,CAAC4M,EAAYC,EAAc,CAAG7M,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GACvC,OAAEW,CAAK,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAQA,GACpB,iBAAEyF,CAAe,CAAE,CAAGJ,IACtB6G,EAA4BzG,EAAgByC,EAAAA,EAAWA,CAACiE,IADjB9G,UAC+B,EAEtE+G,EAAmB,UACvB,GAAKF,CAAD,CACJ,GAAI,CACF,IAAM3L,EAAM,MAAMC,MAAM,GAFM,uBAG9B,GAAI,CAACD,EAAIO,EAAE,CAAE,MAAM,MAAU,YAC7B,IAAMkD,EAAO,MAAMzD,EAAI0D,IAAI,GAC3ByG,EAAe1G,EAAKyG,WAAW,CACjC,CAAE,MAAOvG,EAAO,CACdmE,QAAQnE,KAAK,CAACA,GACdnE,EAAM,CACJgB,MAAO,OACPC,YAAa,aACbE,QAAS,aACX,EACF,CACF,EAEMmL,EAAoB,UACxB,GAAI,CACF,IAAM9L,EAAM,MAAMC,MAAM,mCACxB,GAAI,CAACD,EAAIO,EAAE,CAAE,MAAM,MAAU,YAC7B,IAAMkD,EAAO,MAAMzD,EAAI0D,IAAI,GAC3B2G,EAAgB5G,EAAKsI,QAAQ,CAC/B,CAAE,MAAOpI,EAAO,CACdmE,QAAQnE,KAAK,CAACA,GACdnE,EAAM,CACJgB,MAAO,OACPC,YAAa,aACbE,QAAS,aACX,EACF,CACF,EAEMqL,EAAY,UAChBvE,GAAa,GACb,MAAMwE,QAAQC,GAAG,CAAC,CAChBP,EAA4BE,IAAqBI,QAAQE,OAAO,GAChEL,IACD,EACDrE,EAAa,IACbiE,GAAc,EAChB,EAMM,QAAE3D,CAAM,CAAE,CAAGC,CAAAA,EAAAA,GAAAA,CAAAA,CAASA,GAEtBoE,EAAgB,UACpBV,GAAc,GACd,MAAMM,GACR,EAEMK,EAAY,UAChB,GAAI,CAAC3B,EAAWE,GAAG,CAAC/F,IAAI,GAAI,CAC1BrF,EAAM,CACJgB,MAAO,UACPC,YAAa,WACbE,QAAS,aACX,GACA,MACF,CAEA1B,GAAW,GACX,GAAI,CACF,IAAMe,EAAM,MAAMC,MAAM,yBAA0B,CAChDC,OAAQ,OACRC,QAAS,CAAE,eAAgB,kBAAmB,EAC9CC,KAAMC,KAAKC,SAAS,CAACoK,EACvB,GAEA,GAAI,CAAC1K,EAAIO,EAAE,CAAE,CACX,IAAMoD,EAAQ,MAAM3D,EAAI0D,IAAI,EAC5B,OAAUS,MAAMR,EAAMA,KAAK,EAAI,SACjC,CAEAnE,EAAM,CACJgB,MAAO,OACPC,YAAa,QACf,GAEAqL,IACAQ,GACF,CAAE,MAAO3I,EAAO,CACdnE,EAAM,CACJgB,MAAO,OACPC,YAAakD,aAAiBQ,MAAQR,EAAMS,OAAO,CAAG,QACtDzD,QAAS,aACX,EACF,QAAU,CACR1B,GAAW,EACb,CACF,EAEMsN,EAAoB,UACxB,GAAoB,SAAS,CAAzBlB,GACF,GAAI,CAACR,EAAahG,IAAI,GAAI,CACxBrF,EAAM,CACJgB,MAAO,UACPC,YAAa,WACbE,QAAS,aACX,GACA,OACF,MAEA,GAAIwK,MAAe5B,MAAM,CAAQ,CAC/B/J,EAAM,CACJgB,MAAO,QACPC,YAAa,WACbE,QAAS,aACX,GACA,MACF,CAGF1B,GAAW,GACX,GAAI,CACF,IAAMuN,EAAWb,EAA4B,iCAAmC,sCAC1Ec,EAA8B,UAAhBpB,EAChB,CAAEqB,aAAc7B,CAAa,EAC7B,CAAE8B,eAAgBxB,CAAe,EAE/BnL,EAAM,MAAMC,MAAMuM,EAAU,CAChCtM,OAAQ,OACRC,QAAS,CAAE,eAAgB,kBAAmB,EAC9CC,KAAMC,KAAKC,SAAS,CAACmM,EACvB,GAEA,GAAI,CAACzM,EAAIO,EAAE,CAAE,CACX,IAAMoD,EAAQ,MAAM3D,EAAI0D,IAAI,EAC5B,OAAM,MAAUC,EAAMA,KAAK,EAAI,WACjC,CAEA,IAAMF,EAAO,MAAMzD,EAAI0D,IAAI,GAEP,SAAS,CAAzB2H,GACFL,EAAuBvH,EAAKmJ,UAAU,EACtC1B,EAAwB,EAAE,IAE1BA,EAAwBzH,EAAKyG,WAAW,EACxCc,EAAuB,OAGzBxL,EAAM,CACJgB,MAAO,OACPC,YAAa,CAAC,IAAI,EAAkB,UAAhB4K,EAA0B,EAAI5H,EAAKyG,WAAW,CAACX,MAAM,CAAC,MAAM,CAAC,EAErF,CAAE,MAAO5F,EAAO,CACdnE,EAAM,CACJgB,MAAO,OACPC,YAAakD,aAAiBQ,MAAQR,EAAMS,OAAO,CAAG,QACtDzD,QAAS,aACX,EACF,QAAU,CACR1B,GAAW,EACb,CACF,EAEMqN,EAAwB,KAC5B/B,GAAkB,GAClBI,EAAc,CAAEC,IAAK,EAAG,EAC1B,EAYMiC,EAAc,MAAOH,IACzB,GAAI,CAOF,GAAI,CANQ,OAAMzM,MAAM,kCAAmC,CACzDC,OAAQ,SACRC,QAAS,CAAE,eAAgB,kBAAmB,EAC9CC,KAAMC,KAAKC,SAAS,CAAC,cAAEoM,CAAa,EACtC,IAESnM,EAAE,CAAE,MAAM,MAAU,QAE7B8J,EAAgB0B,GAAYA,EAASzD,MAAM,CAACwE,GAAWA,EAAQJ,YAAY,GAAKA,IAChFlN,EAAM,CACJgB,MAAO,OACPC,YAAa,OACf,EACF,CAAE,MAAOkD,EAAO,CACdmE,QAAQnE,KAAK,CAACA,GACdnE,EAAM,CACJgB,MAAO,OACPC,YAAa,SACbE,QAAS,aACX,EACF,CACF,EAEMyG,GAAkB,MAAO2F,IAC7B,GAAI,CACF,MAAMC,UAAUC,SAAS,CAACC,SAAS,CAACH,GACpCvN,EAAM,CACJgB,MAAO,OACPC,YAAa,SACf,EACF,CAAE,MAAOkD,EAAO,CACdnE,EAAM,CACJgB,MAAO,OACPC,YAAa,WACbE,QAAS,aACX,EACF,CACF,EAEA,MACE,WAACjB,MAAAA,CAAItB,UAAU,8EACb,WAACsB,MAAAA,CAAItB,UAAU,mDACb,WAACsB,MAAAA,CAAItB,UAAU,oCACb,UAAC+O,EAAAA,CAAIA,CAAAA,CAAC/O,UAAU,yBAChB,UAACkG,KAAAA,CAAGlG,UAAU,iCAAwB,cAExC,WAACsB,MAAAA,CAAItB,UAAU,oCACb,UAACsD,EAAAA,CAAMA,CAAAA,CACLf,QAAQ,QACRuI,KAAK,OACLlH,QAASoK,EACTzK,SAAU8J,EACVrN,UAAWqN,EAAa,eAAiB,YAEzC,UAAC2B,GAAAA,CAASA,CAAAA,CAAChP,UAAU,cAIvB,WAACoK,GAAAA,EAAMA,CAAAA,CAACC,KAAM6B,EAAgB5B,aAAc6B,YAC1C,UAAC5B,GAAAA,EAAaA,CAAAA,CAAC5G,OAAO,aACpB,WAACL,EAAAA,CAAMA,CAAAA,CAACtD,UAAU,kBAChB,UAACwK,GAAAA,CAAIA,CAAAA,CAACxK,UAAU,YAAY,YAIhC,WAACyK,GAAAA,EAAaA,CAAAA,CAACzK,UAAU,qBACvB,WAAC0K,GAAAA,EAAYA,CAAAA,WACX,UAACC,GAAAA,EAAWA,CAAAA,UAAC,SACb,UAACC,GAAAA,EAAiBA,CAAAA,UAAC,wBAKrB,UAACtJ,MAAAA,CAAItB,UAAU,0BACb,WAACsB,MAAAA,CAAItB,UAAU,sBACb,UAAC2C,EAAAA,CAAKA,CAAAA,UAAC,eACP,UAACG,EAAAA,CAAKA,CAAAA,CACJG,MAAOqJ,EAAWE,GAAG,CACrBtJ,SAAU,GAAOqJ,EAAc0C,GAAS,EAAE,EAAF,CAAKA,CAAI,CAAEzC,IAAK9K,EAAEyB,MAAM,CAACF,KAAK,CAAC,GACvED,YAAY,oBAKlB,WAACgI,GAAAA,EAAYA,CAAAA,WACX,UAACC,GAAAA,EAAWA,CAAAA,CAACtH,OAAO,aAClB,UAACL,EAAAA,CAAMA,CAAAA,CAACf,QAAQ,UAAUqB,QAASsK,EAAuB3K,SAAU3C,WAAS,SAI/E,UAAC0C,EAAAA,CAAMA,CAAAA,CAACM,QAASqK,EAAW1K,SAAU3C,WACnCA,EAAU,UAACW,EAAAA,CAAOA,CAAAA,CAACvB,UAAU,yBAA4B,gBAOjEuN,GACC,WAACnD,GAAAA,EAAMA,CAAAA,CAACC,KAAM+B,EAAmB9B,aAAc+B,YAC7C,UAAC9B,GAAAA,EAAaA,CAAAA,CAAC5G,OAAO,aACpB,WAACL,EAAAA,CAAMA,CAAAA,CAACf,QAAQ,UAAUvC,UAAU,kBAClC,UAACkP,GAAGA,CAAClP,UAAU,YAAY,YAI/B,WAACyK,GAAAA,EAAaA,CAAAA,CAACzK,UAAU,qBACvB,WAAC0K,GAAAA,EAAYA,CAAAA,WACX,UAACC,GAAAA,EAAWA,CAAAA,UAAC,WACb,UAACC,GAAAA,EAAiBA,CAAAA,UAAC,wBAKrB,WAACtJ,MAAAA,CAAItB,UAAU,2BACb,WAACsB,MAAAA,CAAItB,UAAU,sBACb,UAAC2C,EAAAA,CAAKA,CAAAA,UAAC,SACP,UAACG,EAAAA,CAAKA,CAAAA,CACJM,KAAK,QACLH,MAAOwJ,EACPvJ,SAAU,GAAOwJ,EAAgBhL,EAAEyB,MAAM,CAACF,KAAK,EAC/CD,YAAY,0BAIf2J,GACC,WAACrL,MAAAA,CAAItB,UAAU,sBACb,UAAC2C,EAAAA,CAAKA,CAAAA,UAAC,UACP,WAACrB,MAAAA,CAAItB,UAAU,qBACb,UAAC8C,EAAAA,CAAKA,CAAAA,CACJM,KAAM+J,EAAU,OAAS,WACzBlK,MAAO0J,EAAoBH,GAAG,CAC9B3B,QAAQ,IACR7K,UAAU,UAEZ,WAACsB,MAAAA,CAAItB,UAAU,kEACb,UAACsD,EAAAA,CAAMA,CAAAA,CACLF,KAAK,SACLb,QAAQ,QACRuI,KAAK,OACLlH,QAAS,IAAMwJ,EAAW,CAACD,YAE1BA,EAAU,UAACgC,GAAMA,CAACnP,EAADmP,QAAW,YAAe,UAACD,GAAGA,CAAClP,UAAU,cAE7D,UAACsD,EAAAA,CAAMA,CAAAA,CACLF,KAAK,SACLb,QAAQ,QACRuI,KAAK,OACLlH,QAAS,IAAMoF,GAAgB2D,EAAoBH,GAAG,WAEtD,UAACzB,GAAAA,CAAIA,CAAAA,CAAC/K,UAAU,oBAItB,WAACsB,MAAAA,CAAItB,UAAU,0CAAgC,UACrC2M,EAAoByC,YAAY,UAMhD,WAACpE,GAAAA,EAAYA,CAAAA,WACX,UAACC,GAAAA,EAAWA,CAAAA,CAACtH,OAAO,aAClB,UAACL,EAAAA,CAAMA,CAAAA,CAACf,QAAQ,UAAUqB,QA/KX,CA+KoByL,IA9KnDhD,GAAqB,GACrBK,EAAgB,IAChBE,EAAuB,MACvBE,EAAwB,EAAE,EAC1BE,EAAkB,EAAE,EACpBE,EAAe,SACfE,GAAW,EACb,WAuK+E,SAI/D,UAAC9J,EAAAA,CAAMA,CAAAA,CAACM,QAASuK,EAAmB5K,SAAU3C,WAC3CA,EAAU,UAACW,EAAAA,CAAOA,CAAAA,CAACvB,UAAU,yBAA4B,sBAUvEoJ,EACC,WAAC9H,MAAAA,CAAItB,UAAU,uCACb,UAACsB,MAAAA,CAAItB,UAAU,yFACb,UAACuB,EAAAA,CAAOA,CAAAA,CAACvB,UAAU,wCAErB,UAACsB,MAAAA,UACC,UAACE,IAAAA,CAAExB,UAAU,yCAAgC,gBAIjD,WAACsB,MAAAA,CAAItB,UAAU,sBAEb,WAACsB,MAAAA,CAAItB,UAAU,sBACb,UAACoL,KAAAA,CAAGpL,UAAU,+BAAsB,WACX,IAAxBgM,EAAab,MAAM,CAClB,WAAC7J,MAAAA,CAAItB,UAAU,yDACb,UAACsB,MAAAA,CAAItB,UAAU,yFACb,UAAC+O,EAAAA,CAAIA,CAAAA,CAAC/O,UAAU,2BAElB,WAACsB,MAAAA,WACC,UAACE,IAAAA,CAAExB,UAAU,yCAAgC,eAG7C,UAACwB,IAAAA,CAAExB,UAAU,8CAAqC,kCAMtD,UAACsB,MAAAA,CAAItB,UAAU,qBACZgM,EAAahF,GAAG,CAAC,GAChB,WAAC1F,MAAAA,CAECtB,UAAU,4EAEV,WAACsB,MAAAA,CAAItB,UAAU,sBACb,UAACsB,MAAAA,CAAItB,UAAU,uBAAe0O,EAAQJ,YAAY,GAClD,WAAChN,MAAAA,CAAItB,UAAU,0CAAgC,OACxC,IAAIqL,KAAKqD,EAAQpD,SAAS,EAAEC,cAAc,SAGnD,UAACjI,EAAAA,CAAMA,CAAAA,CACLf,QAAQ,QACRuI,KAAK,KACLlH,QAAS,IAAM6K,EAAYC,EAAQJ,YAAY,WAE/C,UAAC7C,GAAAA,CAAMA,CAAAA,CAACzL,UAAU,gBAdf0O,EAAQ3L,EAAE,QAuBxBwK,GACC,WAACjM,MAAAA,CAAItB,UAAU,sBACb,UAACoL,KAAAA,CAAGpL,UAAU,+BAAsB,iBACZ,IAAvB8L,EAAYX,MAAM,CACjB,WAAC7J,MAAAA,CAAItB,UAAU,yDACb,UAACsB,MAAAA,CAAItB,UAAU,yFACb,UAAC+O,EAAAA,CAAIA,CAAAA,CAAC/O,UAAU,2BAElB,WAACsB,MAAAA,WACC,UAACE,IAAAA,CAAExB,UAAU,yCAAgC,eAG7C,UAACwB,IAAAA,CAAExB,UAAU,8CAAqC,0BAMtD,UAACsB,MAAAA,CAAItB,UAAU,qBACZ8L,EAAY9E,GAAG,CAAC,GACf,WAAC1F,MAAAA,CAECtB,UAAU,4EAEV,WAACsB,MAAAA,CAAItB,UAAU,sBACb,UAACsB,MAAAA,CAAItB,UAAU,uBAAewO,EAAWF,YAAY,GACrD,WAAChN,MAAAA,CAAItB,UAAU,kEACb,WAACoI,OAAAA,WAAK,OAAK,IAAIiD,KAAKmD,EAAWlD,SAAS,EAAEC,cAAc,MACvDiD,EAAWc,UAAU,EACpB,WAAClH,OAAAA,WAAK,SAAO,IAAIiD,KAAKmD,EAAWc,UAAU,EAAE/D,cAAc,MAE7D,WAACjK,MAAAA,CAAItB,UAAU,oCACb,UAACuP,GAAKA,CAACvP,CAADuP,SAAW,YACjB,WAACnH,OAAAA,WAAMoG,EAAWY,YAAY,CAAC,qBAIrC,UAAC9N,MAAAA,CAAItB,UAAU,mCACb,UAACoI,OAAAA,CAAKpI,UAAW,CAAC,0BAA0B,EAAEwO,EAAWjO,OAAO,CAAG,8BAAgC,6BAA6B,UAC7HiO,EAAWjO,OAAO,CAAG,KAAO,WAlB5BiO,EAAWzL,EAAE,QA6B9B,WAACzB,MAAAA,CAAItB,UAAU,sBACb,WAAC+D,SAAAA,CACCX,KAAK,SACLpD,UAAU,gGACV4D,QAAS,IAAMuF,EAAgB,CAACD,aAE/BA,EAAe,UAAClF,EAASA,CAAChE,MAADgE,IAAW,YAAe,UAACC,EAAAA,CAAWA,CAAAA,CAACjE,UAAU,YAAa,YAIzFkJ,GACC,WAAC5H,MAAAA,CAAItB,UAAU,oDACb,WAACsB,MAAAA,CAAItB,UAAU,sBACb,UAACwP,KAAAA,CAAGxP,UAAU,uBAAc,aAC5B,WAACsB,MAAAA,CAAItB,UAAU,oDACb,WAACwB,IAAAA,WAAE,MAAG,UAACiO,SAAAA,UAAO,SAAa,2BAC3B,WAACjO,IAAAA,WAAE,MAAG,UAACiO,SAAAA,UAAO,SAAa,2BAC3B,WAACjO,IAAAA,WAAE,MAAG,UAACiO,SAAAA,UAAO,SAAa,0BAC3B,WAACjO,IAAAA,WAAE,MAAG,UAACiO,SAAAA,UAAO,SAAa,mCAI/B,WAACnO,MAAAA,CAAItB,UAAU,sBACb,UAACwP,KAAAA,CAAGxP,UAAU,uBAAc,SAC5B,WAACsB,MAAAA,CAAItB,UAAU,oDACb,WAACwB,IAAAA,WAAE,UAACiO,SAAAA,UAAO,QAAY,sCACvB,WAACjO,IAAAA,WAAE,UAACiO,SAAAA,UAAO,SAAa,wCAI5B,WAACnO,MAAAA,CAAItB,UAAU,sBACb,UAACwP,KAAAA,CAAGxP,UAAU,uBAAc,SAC5B,WAACsB,MAAAA,CAAItB,UAAU,oDACb,UAACwB,IAAAA,UAAE,0BACH,UAACA,IAAAA,UAAE,mBACH,UAACA,IAAAA,UAAE,kBACH,UAACA,IAAAA,UAAE,qCAUvB,CCtiBA,IAAMkO,GAAc,CAClBC,QAAS,CAAEzI,KAAM,KAAM0I,KAAMC,CAAM,EACnCC,EADkCD,GAC5B,CAAE3I,KAAM,KAAM0I,KAAMnL,CAAI,EAADA,OACrB,CAAEyC,KAAM,KAAM0I,KAAMjL,CAAM,EAClCoL,EADiCpL,OACvB,CAAEuC,KAAM,KAAM0I,KAAM/K,EAAAA,CAAKA,CACrC,EAEO,SAASmL,GAAY,MAAExK,CAAI,CAAoB,EACpD,IAAMyK,EAASC,CAAAA,EAAAA,EAAAA,EAAAA,CAASA,GAClB,CAAEpJ,iBAAe,CAAE,CAAGJ,IACtByJ,EAAmBrJ,EAAgByC,EAAAA,EAAWA,CAACiE,IADR9G,UACsB,EAC7D0J,EAAatJ,EAAgByC,EAAAA,EAAWA,CAAC8G,YAAY,EACrDC,EAAkBxJ,EAAgByC,EAAAA,EAAWA,CAACgH,aAAa,EAC3DhD,EAA4BzG,EAAgByC,EAAAA,EAAWA,CAACiH,wBAAwB,EAEtF,MACE,WAAClP,MAAAA,CAAItB,UAAU,wCACb,UAACsB,MAAAA,CAAItB,UAAU,mEACb,WAACsB,MAAAA,CAAItB,UAAU,oCACb,UAACsB,MAAAA,CAAItB,UAAU,oBACZwF,EAAKiL,KAAK,EACT,UAACC,EAAAA,CAAKA,CAAAA,CACJC,IAAKnL,EAAKiL,KAAK,CACfG,IAAKpL,EAAK0B,IAAI,EAAI,OAClB2J,MAAO,GACPC,OAAQ,GACR9Q,UAAU,0CAIhB,WAACsB,MAAAA,CAAItB,UAAU,2BACb,WAACsB,MAAAA,CAAItB,UAAU,oCACb,UAACkG,KAAAA,CAAGlG,UAAU,sCAA8BwF,EAAK0B,IAAI,GAEnD1B,EAAKM,KAAK,EAER,EADA,CACA,QAACxE,KADa,CACbA,CAAItB,UAAU,8GACb,UAAC+Q,EAAAA,CAAMA,CAAAA,CAAC/Q,UAAU,YAAY,YAMtC,UAACwB,IAAAA,CAAExB,UAAU,uDAETwF,EAAKM,KAAK,CAAGN,EAAKM,KAAK,CAAG,CAAC,KAAK,EAAEN,EAAKK,QAAQ,EAAE,GAGpDL,EAAKqB,KAAK,EACT,UAACvF,MAAAA,CAAItB,UAAU,2BACZwF,EAAKqB,KAAK,CAACG,GAAG,CAAC,CAAC,MAAEE,CAAI,CAAE,IACvB,IAAM8J,EAAatB,EAAW,CAACxI,EAAiC,CAC1DjB,EAAO+K,EAAWpB,IAAI,CAC5B,MACE,WAACtO,MAAAA,CAECtB,UAAU,iFACVoC,MAAO4O,EAAW9J,IAAI,WAEtB,UAACjB,EAAAA,CAAKjG,UAAU,YACfgR,EAAW9J,IAAI,GALXA,EAQX,aAOTiJ,GACC,WAAC7O,MAAAA,CAAItB,UAAU,oEACb,WAACsB,MAAAA,CAAItB,UAAU,yCACb,UAACmI,EAAQA,CAACnI,KAADmI,KAAW,yBACpB,UAACjC,KAAAA,CAAGlG,UAAU,iCAAwB,kBAExC,UAACM,EAAaA,CAAAA,MAIjBgQ,GAAmB,UAAChJ,EAAWA,CAAAA,GAC/B8I,GAAc,EADiB9I,CACjB,OAACvC,EAAYA,CAAAA,GAC3BoL,GAAoB,UAAC5H,GAAWA,CAAAA,GAChCgF,GAA6B,CADGhF,EACH,OAACsD,GAAqBA,CAAAA,GAEpD,WAACvK,GAFmDuK,GAEnDvK,CAAItB,UAAU,iDACb,WAACsD,EAAAA,CAAMA,CAAAA,CACLM,QAAS,IAAMqM,EAAOgB,IAAI,CAAC,QAC3BjR,UAAU,yBAEV,UAAC+O,EAAAA,CAAIA,CAAAA,CAAC/O,UAAU,YAAY,UAG9B,UAACsD,EAAAA,CAAMA,CAAAA,CACLf,QAAQ,UACRqB,QAAS,IAAMsN,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,CAAEC,YAAa,GAAI,GAC1CnR,UAAU,kBACX,cAMT,iDC9GM,MAAY,cAAgB,CAAC,WAAa,EAC9C,CAAC,QAAU,EAAE,EAAI,MAAM,CAAI,MAAK,CAAG,KAAK,GAAK,UAAU,EACvD,CAAC,MAAQ,EAAE,EAAG,CAAyB,2BAAK,SAAU,EACvD,kjBCXM,IAAMoR,EAAU,OAAM,eAECC,IAC5B,IAAM1K,EAAU,MAAM2K,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,GAM1B,OAJK3K,GAASnB,MAAM,CAClB+L,EAAAA,EAAAA,EAAAA,CAAQA,CAAC,KAIT,UAACjQ,MAAAA,CAAItB,UAAU,sGACb,WAACsB,MAAAA,CAAItB,UAAU,0DACb,UAACwR,EAAAA,CAAMA,CAAAA,CAAAA,GACP,UAACC,OAAAA,CAAKzR,UAAU,sBACd,UAACgQ,EAAAA,WAAWA,CAAAA,CAACxK,KAAMmB,EAAQnB,IAAI,SAKzC", "sources": ["webpack://_N_E/external commonjs \"node:async_hooks\"", "webpack://_N_E/external commonjs \"node:buffer\"", "webpack://_N_E/./app/profile/page.tsx?d7b1", "webpack://_N_E/|ssr?9302", "webpack://_N_E/?f049", "webpack://_N_E/?4df2", "webpack://_N_E/../../../src/icons/crown.ts", "webpack://_N_E/../../../src/icons/gem.ts", "webpack://_N_E/../../../src/icons/sword.ts", "webpack://_N_E/../../../src/icons/settings.ts", "webpack://_N_E/../src/Switch.tsx", "webpack://_N_E/./app/components/ui/switch.tsx", "webpack://_N_E/../../../src/icons/send.ts", "webpack://_N_E/../../../src/icons/chevron-up.ts", "webpack://_N_E/./app/components/profile/webhook-config.tsx", "webpack://_N_E/./app/components/profile/promote-panel.tsx", "webpack://_N_E/./app/hooks/use-role-permission.ts", "webpack://_N_E/./app/components/profile/config-panel.tsx", "webpack://_N_E/./app/components/profile/api-key-panel.tsx", "webpack://_N_E/../../../src/icons/eye.ts", "webpack://_N_E/../../../src/icons/eye-off.ts", "webpack://_N_E/../../../src/icons/users.ts", "webpack://_N_E/./app/components/profile/email-credentials-panel.tsx", "webpack://_N_E/./app/components/profile/profile-card.tsx", "webpack://_N_E/../../../src/icons/user-round.ts", "webpack://_N_E/./app/profile/page.tsx"], "sourcesContent": ["module.exports = require(\"node:async_hooks\");", "module.exports = require(\"node:buffer\");", "const module0 = () => import(/* webpackMode: \"eager\" */ \"F:\\\\CODE\\\\Project\\\\tempmail\\\\moemail\\\\app\\\\layout.tsx\");\nconst module1 = () => import(/* webpackMode: \"eager\" */ \"next/dist/client/components/not-found-error\");\nconst module2 = () => import(/* webpackMode: \"eager\" */ \"next/dist/client/components/forbidden-error\");\nconst module3 = () => import(/* webpackMode: \"eager\" */ \"next/dist/client/components/unauthorized-error\");\nconst page4 = () => import(/* webpackMode: \"eager\" */ \"F:\\\\CODE\\\\Project\\\\tempmail\\\\moemail\\\\app\\\\profile\\\\page.tsx\");\nimport { AppPageRouteModule } from \"next/dist/server/route-modules/app-page/module.compiled\" with {\n    'turbopack-transition': 'next-ssr'\n};\nimport { RouteKind } from \"next/dist/server/route-kind\";\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'profile',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"F:\\\\CODE\\\\Project\\\\tempmail\\\\moemail\\\\app\\\\profile\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        metadata: {\n    icon: [(async (props) => (await import(/* webpackMode: \"eager\" */ \"next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!F:\\\\CODE\\\\Project\\\\tempmail\\\\moemail\\\\app\\\\favicon.ico?__next_metadata__\")).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [module0, \"F:\\\\CODE\\\\Project\\\\tempmail\\\\moemail\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await import(/* webpackMode: \"eager\" */ \"next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!F:\\\\CODE\\\\Project\\\\tempmail\\\\moemail\\\\app\\\\favicon.ico?__next_metadata__\")).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"F:\\\\CODE\\\\Project\\\\tempmail\\\\moemail\\\\app\\\\profile\\\\page.tsx\"];\nexport { tree, pages };\nexport { default as GlobalError } from \"next/dist/client/components/error-boundary\";\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nexport const __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\nexport * from \"next/dist/server/app-render/entry-base\";\n// Create and export the route module that will be consumed.\nexport const routeModule = new AppPageRouteModule({\n    definition: {\n        kind: RouteKind.APP_PAGE,\n        page: \"/profile/page\",\n        pathname: \"/profile\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map", "var _self___RSC_MANIFEST;\nimport \"next/dist/server/web/globals\";\nimport { adapter } from \"next/dist/server/web/adapter\";\nimport { getRender } from \"next/dist/build/webpack/loaders/next-edge-ssr-loader/render\";\nimport { IncrementalCache } from \"next/dist/server/lib/incremental-cache\";\nimport { renderToHTMLOrFlight as renderToHTML } from \"next/dist/server/app-render/app-render\";\nimport * as pageMod from \"next-app-loader?name=app%2Fprofile%2Fpage&page=%2Fprofile%2Fpage&pagePath=private-next-app-dir%2Fprofile%2Fpage.tsx&appDir=F%3A%5CCODE%5CProject%5Ctempmail%5Cmoemail%5Capp&appPaths=%2Fprofile%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!private-next-app-dir/profile/page.tsx?__next_edge_ssr_entry__\";\nimport { PAGE_TYPES } from \"next/dist/lib/page-types\";\nimport { setReferenceManifestsSingleton } from \"next/dist/server/app-render/encryption-utils\";\nimport { createServerModuleMap } from \"next/dist/server/app-render/action-utils\";\nconst incrementalCacheHandler = null\nconst cacheHandlers = {\n\n};\nif (!globalThis.__nextCacheHandlers) {\n    ;\n    globalThis.__nextCacheHandlers = cacheHandlers;\n}\nconst Document = null;\nconst appMod = null;\nconst errorMod = null;\nconst error500Mod = null;\nconst sriEnabled = false\nconst isServerComponent = true\nconst dev = false\nconst serverActions = undefined\nconst nextConfig = {\"env\":{},\"eslint\":{\"ignoreDuringBuilds\":false},\"typescript\":{\"ignoreBuildErrors\":false,\"tsconfigPath\":\"tsconfig.json\"},\"distDir\":\".next\",\"cleanDistDir\":true,\"assetPrefix\":\"\",\"cacheMaxMemorySize\":52428800,\"configOrigin\":\"next.config.ts\",\"useFileSystemPublicRoutes\":true,\"generateEtags\":true,\"pageExtensions\":[\"tsx\",\"ts\",\"jsx\",\"js\"],\"poweredByHeader\":true,\"compress\":true,\"images\":{\"deviceSizes\":[640,750,828,1080,1200,1920,2048,3840],\"imageSizes\":[16,32,48,64,96,128,256,384],\"path\":\"/_next/image\",\"loader\":\"default\",\"loaderFile\":\"\",\"domains\":[],\"disableStaticImages\":false,\"minimumCacheTTL\":60,\"formats\":[\"image/webp\"],\"dangerouslyAllowSVG\":false,\"contentSecurityPolicy\":\"script-src 'none'; frame-src 'none'; sandbox;\",\"contentDispositionType\":\"attachment\",\"remotePatterns\":[{\"protocol\":\"https\",\"hostname\":\"avatars.githubusercontent.com\"}],\"unoptimized\":false},\"devIndicators\":{\"appIsrStatus\":true,\"buildActivity\":true,\"buildActivityPosition\":\"bottom-right\"},\"onDemandEntries\":{\"maxInactiveAge\":60000,\"pagesBufferLength\":5},\"amp\":{\"canonicalBase\":\"\"},\"basePath\":\"\",\"sassOptions\":{},\"trailingSlash\":false,\"i18n\":null,\"productionBrowserSourceMaps\":false,\"excludeDefaultMomentLocales\":true,\"serverRuntimeConfig\":{},\"publicRuntimeConfig\":{},\"reactProductionProfiling\":false,\"reactStrictMode\":null,\"reactMaxHeadersLength\":6000,\"httpAgentOptions\":{\"keepAlive\":true},\"logging\":{},\"expireTime\":31536000,\"staticPageGenerationTimeout\":60,\"modularizeImports\":{\"@mui/icons-material\":{\"transform\":\"@mui/icons-material/{{member}}\"},\"lodash\":{\"transform\":\"lodash/{{member}}\"}},\"outputFileTracingRoot\":\"F:\\\\CODE\\\\Project\\\\tempmail\\\\moemail\",\"experimental\":{\"cacheLife\":{\"default\":{\"stale\":300,\"revalidate\":900,\"expire\":4294967294},\"seconds\":{\"stale\":0,\"revalidate\":1,\"expire\":60},\"minutes\":{\"stale\":300,\"revalidate\":60,\"expire\":3600},\"hours\":{\"stale\":300,\"revalidate\":3600,\"expire\":86400},\"days\":{\"stale\":300,\"revalidate\":86400,\"expire\":604800},\"weeks\":{\"stale\":300,\"revalidate\":604800,\"expire\":2592000},\"max\":{\"stale\":300,\"revalidate\":2592000,\"expire\":4294967294}},\"cacheHandlers\":{},\"cssChunking\":true,\"multiZoneDraftMode\":false,\"appNavFailHandling\":false,\"prerenderEarlyExit\":true,\"serverMinification\":true,\"serverSourceMaps\":false,\"linkNoTouchStart\":false,\"caseSensitiveRoutes\":false,\"clientSegmentCache\":false,\"preloadEntriesOnStart\":true,\"clientRouterFilter\":true,\"clientRouterFilterRedirects\":false,\"fetchCacheKeyPrefix\":\"\",\"middlewarePrefetch\":\"flexible\",\"optimisticClientCache\":true,\"manualClientBasePath\":false,\"cpus\":11,\"memoryBasedWorkersCount\":false,\"imgOptConcurrency\":null,\"imgOptTimeoutInSeconds\":7,\"imgOptMaxInputPixels\":268402689,\"imgOptSequentialRead\":null,\"isrFlushToDisk\":true,\"workerThreads\":false,\"optimizeCss\":false,\"nextScriptWorkers\":false,\"scrollRestoration\":false,\"externalDir\":false,\"disableOptimizedLoading\":false,\"gzipSize\":true,\"craCompat\":false,\"esmExternals\":true,\"fullySpecified\":false,\"swcTraceProfiling\":false,\"forceSwcTransforms\":false,\"largePageDataBytes\":128000,\"turbo\":{\"root\":\"F:\\\\CODE\\\\Project\\\\tempmail\\\\moemail\"},\"typedRoutes\":false,\"typedEnv\":false,\"parallelServerCompiles\":false,\"parallelServerBuildTraces\":false,\"ppr\":false,\"authInterrupts\":false,\"reactOwnerStack\":false,\"webpackMemoryOptimizations\":false,\"optimizeServerReact\":true,\"useEarlyImport\":false,\"staleTimes\":{\"dynamic\":0,\"static\":300},\"serverComponentsHmrCache\":true,\"staticGenerationMaxConcurrency\":8,\"staticGenerationMinPagesPerWorker\":25,\"dynamicIO\":false,\"inlineCss\":false,\"optimizePackageImports\":[\"lucide-react\",\"date-fns\",\"lodash-es\",\"ramda\",\"antd\",\"react-bootstrap\",\"ahooks\",\"@ant-design/icons\",\"@headlessui/react\",\"@headlessui-float/react\",\"@heroicons/react/20/solid\",\"@heroicons/react/24/solid\",\"@heroicons/react/24/outline\",\"@visx/visx\",\"@tremor/react\",\"rxjs\",\"@mui/material\",\"@mui/icons-material\",\"recharts\",\"react-use\",\"effect\",\"@effect/schema\",\"@effect/platform\",\"@effect/platform-node\",\"@effect/platform-browser\",\"@effect/platform-bun\",\"@effect/sql\",\"@effect/sql-mssql\",\"@effect/sql-mysql2\",\"@effect/sql-pg\",\"@effect/sql-squlite-node\",\"@effect/sql-squlite-bun\",\"@effect/sql-squlite-wasm\",\"@effect/sql-squlite-react-native\",\"@effect/rpc\",\"@effect/rpc-http\",\"@effect/typeclass\",\"@effect/experimental\",\"@effect/opentelemetry\",\"@material-ui/core\",\"@material-ui/icons\",\"@tabler/icons-react\",\"mui-core\",\"react-icons/ai\",\"react-icons/bi\",\"react-icons/bs\",\"react-icons/cg\",\"react-icons/ci\",\"react-icons/di\",\"react-icons/fa\",\"react-icons/fa6\",\"react-icons/fc\",\"react-icons/fi\",\"react-icons/gi\",\"react-icons/go\",\"react-icons/gr\",\"react-icons/hi\",\"react-icons/hi2\",\"react-icons/im\",\"react-icons/io\",\"react-icons/io5\",\"react-icons/lia\",\"react-icons/lib\",\"react-icons/lu\",\"react-icons/md\",\"react-icons/pi\",\"react-icons/ri\",\"react-icons/rx\",\"react-icons/si\",\"react-icons/sl\",\"react-icons/tb\",\"react-icons/tfi\",\"react-icons/ti\",\"react-icons/vsc\",\"react-icons/wi\"]},\"bundlePagesRouterDependencies\":false,\"configFile\":\"F:\\\\CODE\\\\Project\\\\tempmail\\\\moemail\\\\next.config.ts\",\"configFileName\":\"next.config.ts\"}\nconst maybeJSONParse = (str)=>str ? JSON.parse(str) : undefined;\nconst buildManifest = self.__BUILD_MANIFEST;\nconst reactLoadableManifest = maybeJSONParse(self.__REACT_LOADABLE_MANIFEST);\nconst rscManifest = (_self___RSC_MANIFEST = self.__RSC_MANIFEST) == null ? void 0 : _self___RSC_MANIFEST[\"/profile/page\"];\nconst rscServerManifest = maybeJSONParse(self.__RSC_SERVER_MANIFEST);\nconst subresourceIntegrityManifest = sriEnabled ? maybeJSONParse(self.__SUBRESOURCE_INTEGRITY_MANIFEST) : undefined;\nconst nextFontManifest = maybeJSONParse(self.__NEXT_FONT_MANIFEST);\nconst interceptionRouteRewrites = maybeJSONParse(self.__INTERCEPTION_ROUTE_REWRITE_MANIFEST) ?? [];\nif (rscManifest && rscServerManifest) {\n    setReferenceManifestsSingleton({\n        page: \"/profile/page\",\n        clientReferenceManifest: rscManifest,\n        serverActionsManifest: rscServerManifest,\n        serverModuleMap: createServerModuleMap({\n            serverActionsManifest: rscServerManifest\n        })\n    });\n}\nconst render = getRender({\n    pagesType: PAGE_TYPES.APP,\n    dev,\n    page: \"/profile/page\",\n    appMod,\n    pageMod,\n    errorMod,\n    error500Mod,\n    Document,\n    buildManifest,\n    renderToHTML,\n    reactLoadableManifest,\n    clientReferenceManifest: isServerComponent ? rscManifest : null,\n    serverActionsManifest: isServerComponent ? rscServerManifest : null,\n    serverActions: isServerComponent ? serverActions : undefined,\n    subresourceIntegrityManifest,\n    config: nextConfig,\n    buildId: process.env.__NEXT_BUILD_ID,\n    nextFontManifest,\n    incrementalCacheHandler,\n    interceptionRouteRewrites\n});\nexport const ComponentMod = pageMod;\nexport default function nHandler(opts) {\n    return adapter({\n        ...opts,\n        IncrementalCache,\n        handler: render\n    });\n}\n\n//# sourceMappingURL=edge-ssr-app.js.map", "import(/* webpackMode: \"eager\", webpackExports: [\"SignButton\"] */ \"F:\\\\CODE\\\\Project\\\\tempmail\\\\moemail\\\\app\\\\components\\\\auth\\\\sign-button.tsx\");\n;\nimport(/* webpackMode: \"eager\", webpackExports: [\"ProfileCard\"] */ \"F:\\\\CODE\\\\Project\\\\tempmail\\\\moemail\\\\app\\\\components\\\\profile\\\\profile-card.tsx\");\n;\nimport(/* webpackMode: \"eager\", webpackExports: [\"ThemeToggle\"] */ \"F:\\\\CODE\\\\Project\\\\tempmail\\\\moemail\\\\app\\\\components\\\\theme\\\\theme-toggle.tsx\");\n;\nimport(/* webpackMode: \"eager\", webpackExports: [\"Logo\"] */ \"F:\\\\CODE\\\\Project\\\\tempmail\\\\moemail\\\\app\\\\components\\\\ui\\\\logo.tsx\");\n", "import(/* webpackMode: \"eager\", webpackExports: [\"SignButton\"] */ \"F:\\\\CODE\\\\Project\\\\tempmail\\\\moemail\\\\app\\\\components\\\\auth\\\\sign-button.tsx\");\n;\nimport(/* webpackMode: \"eager\", webpackExports: [\"ProfileCard\"] */ \"F:\\\\CODE\\\\Project\\\\tempmail\\\\moemail\\\\app\\\\components\\\\profile\\\\profile-card.tsx\");\n;\nimport(/* webpackMode: \"eager\", webpackExports: [\"ThemeToggle\"] */ \"F:\\\\CODE\\\\Project\\\\tempmail\\\\moemail\\\\app\\\\components\\\\theme\\\\theme-toggle.tsx\");\n;\nimport(/* webpackMode: \"eager\", webpackExports: [\"Logo\"] */ \"F:\\\\CODE\\\\Project\\\\tempmail\\\\moemail\\\\app\\\\components\\\\ui\\\\logo.tsx\");\n", "import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Crown\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTEuNTYyIDMuMjY2YS41LjUgMCAwIDEgLjg3NiAwTDE1LjM5IDguODdhMSAxIDAgMCAwIDEuNTE2LjI5NEwyMS4xODMgNS41YS41LjUgMCAwIDEgLjc5OC41MTlsLTIuODM0IDEwLjI0NmExIDEgMCAwIDEtLjk1Ni43MzRINS44MWExIDEgMCAwIDEtLjk1Ny0uNzM0TDIuMDIgNi4wMmEuNS41IDAgMCAxIC43OTgtLjUxOWw0LjI3NiAzLjY2NGExIDEgMCAwIDAgMS41MTYtLjI5NHoiIC8+CiAgPHBhdGggZD0iTTUgMjFoMTQiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/crown\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Crown = createLucideIcon('Crown', [\n  [\n    'path',\n    {\n      d: 'M11.562 3.266a.5.5 0 0 1 .876 0L15.39 8.87a1 1 0 0 0 1.516.294L21.183 5.5a.5.5 0 0 1 .798.519l-2.834 10.246a1 1 0 0 1-.956.734H5.81a1 1 0 0 1-.957-.734L2.02 6.02a.5.5 0 0 1 .798-.519l4.276 3.664a1 1 0 0 0 1.516-.294z',\n      key: '1vdc57',\n    },\n  ],\n  ['path', { d: 'M5 21h14', key: '11awu3' }],\n]);\n\nexport default Crown;\n", "import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Gem\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNNiAzaDEybDQgNi0xMCAxM0wyIDlaIiAvPgogIDxwYXRoIGQ9Ik0xMSAzIDggOWw0IDEzIDQtMTMtMy02IiAvPgogIDxwYXRoIGQ9Ik0yIDloMjAiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/gem\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Gem = createLucideIcon('Gem', [\n  ['path', { d: 'M6 3h12l4 6-10 13L2 9Z', key: '1pcd5k' }],\n  ['path', { d: 'M11 3 8 9l4 13 4-13-3-6', key: '1fcu3u' }],\n  ['path', { d: 'M2 9h20', key: '16fsjt' }],\n]);\n\nexport default Gem;\n", "import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Sword\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cG9seWxpbmUgcG9pbnRzPSIxNC41IDE3LjUgMyA2IDMgMyA2IDMgMTcuNSAxNC41IiAvPgogIDxsaW5lIHgxPSIxMyIgeDI9IjE5IiB5MT0iMTkiIHkyPSIxMyIgLz4KICA8bGluZSB4MT0iMTYiIHgyPSIyMCIgeTE9IjE2IiB5Mj0iMjAiIC8+CiAgPGxpbmUgeDE9IjE5IiB4Mj0iMjEiIHkxPSIyMSIgeTI9IjE5IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/sword\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Sword = createLucideIcon('Sword', [\n  ['polyline', { points: '14.5 17.5 3 6 3 3 6 3 17.5 14.5', key: '1hfsw2' }],\n  ['line', { x1: '13', x2: '19', y1: '19', y2: '13', key: '1vrmhu' }],\n  ['line', { x1: '16', x2: '20', y1: '16', y2: '20', key: '1bron3' }],\n  ['line', { x1: '19', x2: '21', y1: '21', y2: '19', key: '13pww6' }],\n]);\n\nexport default Sword;\n", "import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Settings\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTIuMjIgMmgtLjQ0YTIgMiAwIDAgMC0yIDJ2LjE4YTIgMiAwIDAgMS0xIDEuNzNsLS40My4yNWEyIDIgMCAwIDEtMiAwbC0uMTUtLjA4YTIgMiAwIDAgMC0yLjczLjczbC0uMjIuMzhhMiAyIDAgMCAwIC43MyAyLjczbC4xNS4xYTIgMiAwIDAgMSAxIDEuNzJ2LjUxYTIgMiAwIDAgMS0xIDEuNzRsLS4xNS4wOWEyIDIgMCAwIDAtLjczIDIuNzNsLjIyLjM4YTIgMiAwIDAgMCAyLjczLjczbC4xNS0uMDhhMiAyIDAgMCAxIDIgMGwuNDMuMjVhMiAyIDAgMCAxIDEgMS43M1YyMGEyIDIgMCAwIDAgMiAyaC40NGEyIDIgMCAwIDAgMi0ydi0uMThhMiAyIDAgMCAxIDEtMS43M2wuNDMtLjI1YTIgMiAwIDAgMSAyIDBsLjE1LjA4YTIgMiAwIDAgMCAyLjczLS43M2wuMjItLjM5YTIgMiAwIDAgMC0uNzMtMi43M2wtLjE1LS4wOGEyIDIgMCAwIDEtMS0xLjc0di0uNWEyIDIgMCAwIDEgMS0xLjc0bC4xNS0uMDlhMiAyIDAgMCAwIC43My0yLjczbC0uMjItLjM4YTIgMiAwIDAgMC0yLjczLS43M2wtLjE1LjA4YTIgMiAwIDAgMS0yIDBsLS40My0uMjVhMiAyIDAgMCAxLTEtMS43M1Y0YTIgMiAwIDAgMC0yLTJ6IiAvPgogIDxjaXJjbGUgY3g9IjEyIiBjeT0iMTIiIHI9IjMiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/settings\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Settings = createLucideIcon('Settings', [\n  [\n    'path',\n    {\n      d: 'M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z',\n      key: '1qme2f',\n    },\n  ],\n  ['circle', { cx: '12', cy: '12', r: '3', key: '1v7zrd' }],\n]);\n\nexport default Settings;\n", "import * as React from 'react';\nimport { composeEventHandlers } from '@radix-ui/primitive';\nimport { useComposedRefs } from '@radix-ui/react-compose-refs';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { useControllableState } from '@radix-ui/react-use-controllable-state';\nimport { usePrevious } from '@radix-ui/react-use-previous';\nimport { useSize } from '@radix-ui/react-use-size';\nimport { Primitive } from '@radix-ui/react-primitive';\n\nimport type { Scope } from '@radix-ui/react-context';\n\n/* -------------------------------------------------------------------------------------------------\n * Switch\n * -----------------------------------------------------------------------------------------------*/\n\nconst SWITCH_NAME = 'Switch';\n\ntype ScopedProps<P> = P & { __scopeSwitch?: Scope };\nconst [createSwitchContext, createSwitchScope] = createContextScope(SWITCH_NAME);\n\ntype SwitchContextValue = { checked: boolean; disabled?: boolean };\nconst [SwitchProvider, useSwitchContext] = createSwitchContext<SwitchContextValue>(SWITCH_NAME);\n\ntype SwitchElement = React.ElementRef<typeof Primitive.button>;\ntype PrimitiveButtonProps = React.ComponentPropsWithoutRef<typeof Primitive.button>;\ninterface SwitchProps extends PrimitiveButtonProps {\n  checked?: boolean;\n  defaultChecked?: boolean;\n  required?: boolean;\n  onCheckedChange?(checked: boolean): void;\n}\n\nconst Switch = React.forwardRef<SwitchElement, SwitchProps>(\n  (props: ScopedProps<SwitchProps>, forwardedRef) => {\n    const {\n      __scopeSwitch,\n      name,\n      checked: checkedProp,\n      defaultChecked,\n      required,\n      disabled,\n      value = 'on',\n      onCheckedChange,\n      form,\n      ...switchProps\n    } = props;\n    const [button, setButton] = React.useState<HTMLButtonElement | null>(null);\n    const composedRefs = useComposedRefs(forwardedRef, (node) => setButton(node));\n    const hasConsumerStoppedPropagationRef = React.useRef(false);\n    // We set this to true by default so that events bubble to forms without JS (SSR)\n    const isFormControl = button ? form || !!button.closest('form') : true;\n    const [checked = false, setChecked] = useControllableState({\n      prop: checkedProp,\n      defaultProp: defaultChecked,\n      onChange: onCheckedChange,\n    });\n\n    return (\n      <SwitchProvider scope={__scopeSwitch} checked={checked} disabled={disabled}>\n        <Primitive.button\n          type=\"button\"\n          role=\"switch\"\n          aria-checked={checked}\n          aria-required={required}\n          data-state={getState(checked)}\n          data-disabled={disabled ? '' : undefined}\n          disabled={disabled}\n          value={value}\n          {...switchProps}\n          ref={composedRefs}\n          onClick={composeEventHandlers(props.onClick, (event) => {\n            setChecked((prevChecked) => !prevChecked);\n            if (isFormControl) {\n              hasConsumerStoppedPropagationRef.current = event.isPropagationStopped();\n              // if switch is in a form, stop propagation from the button so that we only propagate\n              // one click event (from the input). We propagate changes from an input so that native\n              // form validation works and form events reflect switch updates.\n              if (!hasConsumerStoppedPropagationRef.current) event.stopPropagation();\n            }\n          })}\n        />\n        {isFormControl && (\n          <BubbleInput\n            control={button}\n            bubbles={!hasConsumerStoppedPropagationRef.current}\n            name={name}\n            value={value}\n            checked={checked}\n            required={required}\n            disabled={disabled}\n            form={form}\n            // We transform because the input is absolutely positioned but we have\n            // rendered it **after** the button. This pulls it back to sit on top\n            // of the button.\n            style={{ transform: 'translateX(-100%)' }}\n          />\n        )}\n      </SwitchProvider>\n    );\n  }\n);\n\nSwitch.displayName = SWITCH_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SwitchThumb\n * -----------------------------------------------------------------------------------------------*/\n\nconst THUMB_NAME = 'SwitchThumb';\n\ntype SwitchThumbElement = React.ElementRef<typeof Primitive.span>;\ntype PrimitiveSpanProps = React.ComponentPropsWithoutRef<typeof Primitive.span>;\ninterface SwitchThumbProps extends PrimitiveSpanProps {}\n\nconst SwitchThumb = React.forwardRef<SwitchThumbElement, SwitchThumbProps>(\n  (props: ScopedProps<SwitchThumbProps>, forwardedRef) => {\n    const { __scopeSwitch, ...thumbProps } = props;\n    const context = useSwitchContext(THUMB_NAME, __scopeSwitch);\n    return (\n      <Primitive.span\n        data-state={getState(context.checked)}\n        data-disabled={context.disabled ? '' : undefined}\n        {...thumbProps}\n        ref={forwardedRef}\n      />\n    );\n  }\n);\n\nSwitchThumb.displayName = THUMB_NAME;\n\n/* ---------------------------------------------------------------------------------------------- */\n\ntype InputProps = React.ComponentPropsWithoutRef<'input'>;\ninterface BubbleInputProps extends Omit<InputProps, 'checked'> {\n  checked: boolean;\n  control: HTMLElement | null;\n  bubbles: boolean;\n}\n\nconst BubbleInput = (props: BubbleInputProps) => {\n  const { control, checked, bubbles = true, ...inputProps } = props;\n  const ref = React.useRef<HTMLInputElement>(null);\n  const prevChecked = usePrevious(checked);\n  const controlSize = useSize(control);\n\n  // Bubble checked change to parents (e.g form change event)\n  React.useEffect(() => {\n    const input = ref.current!;\n    const inputProto = window.HTMLInputElement.prototype;\n    const descriptor = Object.getOwnPropertyDescriptor(inputProto, 'checked') as PropertyDescriptor;\n    const setChecked = descriptor.set;\n    if (prevChecked !== checked && setChecked) {\n      const event = new Event('click', { bubbles });\n      setChecked.call(input, checked);\n      input.dispatchEvent(event);\n    }\n  }, [prevChecked, checked, bubbles]);\n\n  return (\n    <input\n      type=\"checkbox\"\n      aria-hidden\n      defaultChecked={checked}\n      {...inputProps}\n      tabIndex={-1}\n      ref={ref}\n      style={{\n        ...props.style,\n        ...controlSize,\n        position: 'absolute',\n        pointerEvents: 'none',\n        opacity: 0,\n        margin: 0,\n      }}\n    />\n  );\n};\n\nfunction getState(checked: boolean) {\n  return checked ? 'checked' : 'unchecked';\n}\n\nconst Root = Switch;\nconst Thumb = SwitchThumb;\n\nexport {\n  createSwitchScope,\n  //\n  Switch,\n  SwitchThumb,\n  //\n  Root,\n  Thumb,\n};\nexport type { SwitchProps, SwitchThumbProps };\n", "\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as SwitchPrimitives from \"@radix-ui/react-switch\"\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Switch = React.forwardRef<\r\n  React.ElementRef<typeof SwitchPrimitives.Root>,\r\n  React.ComponentPropsWithoutRef<typeof SwitchPrimitives.Root>\r\n>(({ className, ...props }, ref) => (\r\n  <SwitchPrimitives.Root\r\n    className={cn(\r\n      \"peer inline-flex h-5 w-9 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent shadow-sm transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input\",\r\n      className\r\n    )}\r\n    {...props}\r\n    ref={ref}\r\n  >\r\n    <SwitchPrimitives.Thumb\r\n      className={cn(\r\n        \"pointer-events-none block h-4 w-4 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-4 data-[state=unchecked]:translate-x-0\"\r\n      )}\r\n    />\r\n  </SwitchPrimitives.Root>\r\n))\r\nSwitch.displayName = SwitchPrimitives.Root.displayName\r\n\r\nexport { Switch } ", "import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Send\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTQuNTM2IDIxLjY4NmEuNS41IDAgMCAwIC45MzctLjAyNGw2LjUtMTlhLjQ5Ni40OTYgMCAwIDAtLjYzNS0uNjM1bC0xOSA2LjVhLjUuNSAwIDAgMC0uMDI0LjkzN2w3LjkzIDMuMThhMiAyIDAgMCAxIDEuMTEyIDEuMTF6IiAvPgogIDxwYXRoIGQ9Im0yMS44NTQgMi4xNDctMTAuOTQgMTAuOTM5IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/send\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Send = createLucideIcon('Send', [\n  [\n    'path',\n    {\n      d: 'M14.536 21.686a.5.5 0 0 0 .937-.024l6.5-19a.496.496 0 0 0-.635-.635l-19 6.5a.5.5 0 0 0-.024.937l7.93 3.18a2 2 0 0 1 1.112 1.11z',\n      key: '1ffxy3',\n    },\n  ],\n  ['path', { d: 'm21.854 2.147-10.94 10.939', key: '12cjpa' }],\n]);\n\nexport default Send;\n", "import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name ChevronUp\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtMTggMTUtNi02LTYgNiIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/chevron-up\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ChevronUp = createLucideIcon('ChevronUp', [['path', { d: 'm18 15-6-6-6 6', key: '153udz' }]]);\n\nexport default ChevronUp;\n", "/* eslint-disable @typescript-eslint/no-unused-vars */\r\n\"use client\"\r\n\r\nimport { useState, useEffect } from \"react\"\r\nimport { But<PERSON> } from \"@/components/ui/button\"\r\nimport { Input } from \"@/components/ui/input\"\r\nimport { Label } from \"@/components/ui/label\"\r\nimport { Switch } from \"@/components/ui/switch\"\r\nimport { useToast } from \"@/components/ui/use-toast\"\r\nimport { Loader2, Send, ChevronDown, ChevronUp } from \"lucide-react\"\r\nimport {\r\n  <PERSON><PERSON><PERSON>,\r\n  TooltipContent,\r\n  TooltipProvider,\r\n  TooltipTrigger,\r\n} from \"@/components/ui/tooltip\"\r\n\r\nexport function WebhookConfig() {\r\n  const [enabled, setEnabled] = useState(false)\r\n  const [url, setUrl] = useState(\"\")\r\n  const [loading, setLoading] = useState(false)\r\n  const [testing, setTesting] = useState(false)\r\n  const [showDocs, setShowDocs] = useState(false)\r\n  const [initialLoading, setInitialLoading] = useState(true)\r\n  const { toast } = useToast()\r\n\r\n  useEffect(() => {\r\n    fetch(\"/api/webhook\")\r\n      .then(res => res.json() as Promise<{ enabled: boolean; url: string }>)\r\n      .then(data => {\r\n        setEnabled(data.enabled)\r\n        setUrl(data.url)\r\n      })\r\n      .catch(console.error)\r\n      .finally(() => setInitialLoading(false))\r\n  }, [])\r\n\r\n  if (initialLoading) {\r\n    return (\r\n      <div className=\"text-center\">\r\n        <div className=\"w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center mx-auto\">\r\n          <Loader2 className=\"w-6 h-6 text-primary animate-spin\" />\r\n        </div>\r\n        <div>\r\n          <p className=\"text-sm text-muted-foreground\">加载中...</p>\r\n        </div>\r\n      </div>\r\n    )\r\n  }\r\n\r\n  const handleSubmit = async (e: React.FormEvent) => {\r\n    e.preventDefault()\r\n    if (!url) return\r\n\r\n    setLoading(true)\r\n    try {\r\n      const res = await fetch(\"/api/webhook\", {\r\n        method: \"POST\",\r\n        headers: { \"Content-Type\": \"application/json\" },\r\n        body: JSON.stringify({ url, enabled })\r\n      })\r\n\r\n      if (!res.ok) throw new Error(\"Failed to save\")\r\n\r\n      toast({\r\n        title: \"保存成功\",\r\n        description: \"Webhook 配置已更新\"\r\n      })\r\n    } catch (_error) {\r\n      toast({\r\n        title: \"保存失败\",\r\n        description: \"请稍后重试\",\r\n        variant: \"destructive\"\r\n      })\r\n    } finally {\r\n      setLoading(false)\r\n    }\r\n  }\r\n\r\n  const handleTest = async () => {\r\n    if (!url) return\r\n\r\n    setTesting(true)\r\n    try {\r\n      const res = await fetch(\"/api/webhook/test\", {\r\n        method: \"POST\",\r\n        headers: { \"Content-Type\": \"application/json\" },\r\n        body: JSON.stringify({ url })\r\n      })\r\n\r\n      if (!res.ok) throw new Error(\"测试失败\")\r\n\r\n      toast({\r\n        title: \"测试成功\",\r\n        description: \"Webhook 调用成功,请检查目标服务器是否收到请求\"\r\n      })\r\n    } catch (_error) {\r\n      toast({\r\n        title: \"测试失败\",\r\n        description: \"请检查 URL 是否正确且可访问\",\r\n        variant: \"destructive\"\r\n      })\r\n    } finally {\r\n      setTesting(false)\r\n    }\r\n  }\r\n\r\n  return (\r\n    <form onSubmit={handleSubmit} className=\"space-y-4\">\r\n      <div className=\"flex items-center justify-between\">\r\n        <div className=\"space-y-0.5\">\r\n          <Label>启用 Webhook</Label>\r\n          <div className=\"text-sm text-muted-foreground\">\r\n            当收到新邮件时通知指定的 URL\r\n          </div>\r\n        </div>\r\n        <Switch\r\n          checked={enabled}\r\n          onCheckedChange={setEnabled}\r\n        />\r\n      </div>\r\n\r\n      {enabled && (\r\n        <div className=\"space-y-4\">\r\n          <div className=\"space-y-2\">\r\n            <Label htmlFor=\"webhook-url\">Webhook URL</Label>\r\n            <div className=\"flex gap-2\">\r\n              <Input\r\n                id=\"webhook-url\"\r\n                placeholder=\"https://example.com/webhook\"\r\n                value={url}\r\n                onChange={(e) => setUrl(e.target.value)}\r\n                type=\"url\"\r\n                required\r\n              />\r\n              <Button type=\"submit\" disabled={loading} className=\"flex-shrink-0\">\r\n                {loading ? (\r\n                  <Loader2 className=\"w-4 h-4 animate-spin\" />\r\n                ) : (\r\n                  \"保存\"\r\n                )}\r\n              </Button>\r\n              <TooltipProvider>\r\n                <Tooltip>\r\n                  <TooltipTrigger asChild>\r\n                    <Button\r\n                      type=\"button\"\r\n                      variant=\"outline\"\r\n                      onClick={handleTest}\r\n                      disabled={testing || !url}\r\n                    >\r\n                      {testing ? (\r\n                        <Loader2 className=\"w-4 h-4 animate-spin\" />\r\n                      ) : (\r\n                        <Send className=\"w-4 h-4\" />\r\n                      )}\r\n                    </Button>\r\n                  </TooltipTrigger>\r\n                  <TooltipContent>\r\n                    <p>发送测试消息到此 Webhook</p>\r\n                  </TooltipContent>\r\n                </Tooltip>\r\n              </TooltipProvider>\r\n            </div>\r\n            <p className=\"text-xs text-muted-foreground\">\r\n              我们会向此 URL 发送 POST 请求,包含新邮件的相关信息\r\n            </p>\r\n          </div>\r\n\r\n          <div className=\"space-y-2\">\r\n            <button\r\n              type=\"button\"\r\n              className=\"flex items-center gap-1 text-sm text-muted-foreground hover:text-foreground transition-colors\"\r\n              onClick={() => setShowDocs(!showDocs)}\r\n            >\r\n              {showDocs ? <ChevronUp className=\"w-4 h-4\" /> : <ChevronDown className=\"w-4 h-4\" />}\r\n              查看数据格式说明\r\n            </button>\r\n\r\n            {showDocs && (\r\n              <div className=\"rounded-md bg-muted p-4 text-sm space-y-3\">\r\n                <p>当收到新邮件时，我们会向配置的 URL 发送 POST 请求，请求头包含:</p>\r\n                <pre className=\"bg-background p-2 rounded text-xs\">\r\n                  Content-Type: application/json{'\\n'}\r\n                  X-Webhook-Event: new_message\r\n                </pre>\r\n\r\n                <p>请求体示例:</p>\r\n                <pre className=\"bg-background p-2 rounded text-xs overflow-auto\">\r\n                  {`{\r\n  \"emailId\": \"email-uuid\",\r\n  \"messageId\": \"message-uuid\",\r\n  \"fromAddress\": \"<EMAIL>\",\r\n  \"subject\": \"邮件主题\",\r\n  \"content\": \"邮件文本内容\",\r\n  \"html\": \"邮件HTML内容\",\r\n  \"receivedAt\": \"2024-01-01T12:00:00.000Z\",\r\n  \"toAddress\": \"your-email@${window.location.host}\"\r\n}`}\r\n                </pre>\r\n              </div>\r\n            )}\r\n          </div>\r\n        </div>\r\n      )}\r\n    </form>\r\n  )\r\n} ", "\"use client\"\r\n\r\nimport { <PERSON><PERSON> } from \"@/components/ui/button\"\r\nimport { <PERSON><PERSON>, Sword, User2, Loader2 } from \"lucide-react\"\r\nimport { Input } from \"@/components/ui/input\"\r\nimport { useState } from \"react\"\r\nimport { useToast } from \"@/components/ui/use-toast\"\r\nimport { <PERSON><PERSON><PERSON>, Role } from \"@/lib/permissions\"\r\nimport {\r\n  Select,\r\n  SelectContent,\r\n  SelectItem,\r\n  SelectTrigger,\r\n  SelectValue,\r\n} from \"@/components/ui/select\"\r\n\r\nconst roleIcons = {\r\n  [ROLES.DUKE]: Gem,\r\n  [ROLES.KNIGHT]: Sword,\r\n  [ROLES.CIVILIAN]: User2,\r\n} as const\r\n\r\nconst roleNames = {\r\n  [ROLES.DUKE]: \"公爵\",\r\n  [ROLES.KNIGHT]: \"骑士\",\r\n  [ROLES.CIVILIAN]: \"平民\",\r\n} as const\r\n\r\ntype RoleWithoutEmperor = Exclude<Role, typeof ROLES.EMPEROR>\r\n\r\nexport function PromotePanel() {\r\n  const [searchText, setSearchText] = useState(\"\")\r\n  const [loading, setLoading] = useState(false)\r\n  const [targetRole, setTargetRole] = useState<RoleWithoutEmperor>(ROLES.KNIGHT)\r\n  const { toast } = useToast()\r\n\r\n  const handleAction = async () => {\r\n    if (!searchText) return\r\n\r\n    setLoading(true)\r\n    try {\r\n      const res = await fetch(\"/api/roles/users\", {\r\n        method: \"POST\",\r\n        headers: { \"Content-Type\": \"application/json\" },\r\n        body: JSON.stringify({ searchText })\r\n      })\r\n      const data = await res.json() as {\r\n        user?: {\r\n          id: string\r\n          name?: string\r\n          username?: string\r\n          email: string\r\n          role?: string\r\n        }\r\n        error?: string\r\n      }\r\n\r\n      if (!res.ok) throw new Error(data.error || \"未知错误\")\r\n\r\n      if (!data.user) {\r\n        toast({\r\n          title: \"未找到用户\",\r\n          description: \"请确认用户名或邮箱地址是否正确\",\r\n          variant: \"destructive\"\r\n        })\r\n        return\r\n      }\r\n\r\n      if (data.user.role === targetRole) {\r\n        toast({\r\n          title: `用户已是${roleNames[targetRole]}`,\r\n          description: \"无需重复设置\",\r\n        })\r\n        return\r\n      }\r\n\r\n      const promoteRes = await fetch(\"/api/roles/promote\", {\r\n        method: \"POST\",\r\n        headers: { \"Content-Type\": \"application/json\" },\r\n        body: JSON.stringify({\r\n          userId: data.user.id,\r\n          roleName: targetRole\r\n        })\r\n      })\r\n\r\n      if (!promoteRes.ok) {\r\n        const error = await promoteRes.json() as { error: string }\r\n        throw new Error(error.error || \"设置失败\")\r\n      }\r\n\r\n      toast({\r\n        title: \"设置成功\",\r\n        description: `已将用户 ${data.user.username || data.user.email} 设为${roleNames[targetRole]}`,\r\n      })\r\n      setSearchText(\"\")\r\n    } catch (error) {\r\n      toast({\r\n        title: \"设置失败\",\r\n        description: error instanceof Error ? error.message : \"请稍后重试\",\r\n        variant: \"destructive\"\r\n      })\r\n    } finally {\r\n      setLoading(false)\r\n    }\r\n  }\r\n\r\n  const Icon = roleIcons[targetRole]\r\n\r\n  return (\r\n    <div className=\"bg-background rounded-lg border-2 border-primary/20 p-6\">\r\n      <div className=\"flex items-center gap-2 mb-6\">\r\n        <Icon className=\"w-5 h-5 text-primary\" />\r\n        <h2 className=\"text-lg font-semibold\">角色管理</h2>\r\n      </div>\r\n\r\n      <div className=\"space-y-4\">\r\n        <div className=\"flex gap-4\">\r\n          <div className=\"flex-1\">\r\n            <Input\r\n              value={searchText}\r\n              onChange={(e) => setSearchText(e.target.value)}\r\n              placeholder=\"输入用户名或邮箱\"\r\n            />\r\n          </div>\r\n          <Select value={targetRole} onValueChange={(value) => setTargetRole(value as RoleWithoutEmperor)}>\r\n            <SelectTrigger className=\"w-32\">\r\n              <SelectValue />\r\n            </SelectTrigger>\r\n            <SelectContent>\r\n              <SelectItem value={ROLES.DUKE}>\r\n                <div className=\"flex items-center gap-2\">\r\n                  <Gem className=\"w-4 h-4\" />\r\n                  公爵\r\n                </div>\r\n              </SelectItem>\r\n              <SelectItem value={ROLES.KNIGHT}>\r\n                <div className=\"flex items-center gap-2\">\r\n                  <Sword className=\"w-4 h-4\" />\r\n                  骑士\r\n                </div>\r\n              </SelectItem>\r\n              <SelectItem value={ROLES.CIVILIAN}>\r\n                <div className=\"flex items-center gap-2\">\r\n                  <User2 className=\"w-4 h-4\" />\r\n                  平民\r\n                </div>\r\n              </SelectItem>\r\n            </SelectContent>\r\n          </Select>\r\n        </div>\r\n\r\n        <Button\r\n          onClick={handleAction}\r\n          disabled={loading || !searchText.trim()}\r\n          className=\"w-full\"\r\n        >\r\n          {loading ? (\r\n            <Loader2 className=\"w-4 h-4 animate-spin\" />\r\n          ) : (\r\n            `设为${roleNames[targetRole]}`\r\n          )}\r\n        </Button>\r\n      </div>\r\n    </div>\r\n  )\r\n} ", "\"use client\"\r\n\r\nimport { useSession } from \"next-auth/react\"\r\nimport { Permission, Role, hasPermission } from \"@/lib/permissions\"\r\n\r\nexport function useRolePermission() {\r\n  const { data: session } = useSession()\r\n  const roles = session?.user?.roles\r\n\r\n  const checkPermission = (permission: Permission) => {\r\n    if (!roles) return false\r\n    return hasPermission(roles.map(r => r.name) as Role[], permission)\r\n  }\r\n\r\n  const hasRole = (role: Role) => {\r\n    if (!roles) return false\r\n    return roles.some(r => r.name === role)\r\n  }\r\n\r\n  return {\r\n    checkPermission,\r\n    hasRole,\r\n    roles,\r\n  }\r\n}", "\"use client\"\r\n\r\nimport { <PERSON><PERSON> } from \"@/components/ui/button\"\r\nimport { Settings } from \"lucide-react\"\r\nimport { useToast } from \"@/components/ui/use-toast\"\r\nimport { useState, useEffect } from \"react\"\r\nimport { Role, ROLES } from \"@/lib/permissions\"\r\nimport { Input } from \"@/components/ui/input\"\r\nimport {\r\n  Select,\r\n  SelectContent,\r\n  SelectItem,\r\n  SelectTrigger,\r\n  SelectValue,\r\n} from \"@/components/ui/select\"\r\nimport { EMAIL_CONFIG } from \"@/config\"\r\n\r\nexport function ConfigPanel() {\r\n  const [defaultRole, setDefaultRole] = useState<string>(\"\")\r\n  const [emailDomains, setEmailDomains] = useState<string>(\"\")\r\n  const [adminContact, setAdminContact] = useState<string>(\"\")\r\n  const [maxEmails, setMaxEmails] = useState<string>(EMAIL_CONFIG.MAX_ACTIVE_EMAILS.toString())\r\n  const [loading, setLoading] = useState(false)\r\n  const { toast } = useToast()\r\n\r\n\r\n  useEffect(() => {\r\n    fetchConfig()\r\n  }, [])\r\n\r\n  const fetchConfig = async () => {\r\n    const res = await fetch(\"/api/config\")\r\n    if (res.ok) {\r\n      const data = await res.json() as { \r\n        defaultRole: Exclude<Role, typeof ROLES.EMPEROR>,\r\n        emailDomains: string,\r\n        adminContact: string,\r\n        maxEmails: string\r\n      }\r\n      setDefaultRole(data.defaultRole)\r\n      setEmailDomains(data.emailDomains)\r\n      setAdminContact(data.adminContact)\r\n      setMaxEmails(data.maxEmails || EMAIL_CONFIG.MAX_ACTIVE_EMAILS.toString())\r\n    }\r\n  }\r\n\r\n  const handleSave = async () => {\r\n    setLoading(true)\r\n    try {\r\n      const res = await fetch(\"/api/config\", {\r\n        method: \"POST\",\r\n        headers: { \"Content-Type\": \"application/json\" },\r\n        body: JSON.stringify({ \r\n          defaultRole, \r\n          emailDomains,\r\n          adminContact,\r\n          maxEmails: maxEmails || EMAIL_CONFIG.MAX_ACTIVE_EMAILS.toString()\r\n        }),\r\n      })\r\n\r\n      if (!res.ok) throw new Error(\"保存失败\")\r\n\r\n      toast({\r\n        title: \"保存成功\",\r\n        description: \"网站设置已更新\",\r\n      })\r\n    } catch (error) {\r\n      toast({\r\n        title: \"保存失败\",\r\n        description: error instanceof Error ? error.message : \"请稍后重试\",\r\n        variant: \"destructive\",\r\n      })\r\n    } finally {\r\n      setLoading(false)\r\n    }\r\n  }\r\n\r\n  return (\r\n    <div className=\"bg-background rounded-lg border-2 border-primary/20 p-6\">\r\n      <div className=\"flex items-center gap-2 mb-6\">\r\n        <Settings className=\"w-5 h-5 text-primary\" />\r\n        <h2 className=\"text-lg font-semibold\">网站设置</h2>\r\n      </div>\r\n\r\n      <div className=\"space-y-4\">\r\n        <div className=\"flex items-center gap-4\">\r\n          <span className=\"text-sm\">新用户默认角色:</span>\r\n          <Select value={defaultRole} onValueChange={setDefaultRole}>\r\n            <SelectTrigger className=\"w-32\">\r\n              <SelectValue />\r\n            </SelectTrigger>\r\n            <SelectContent>\r\n              <SelectItem value={ROLES.DUKE}>公爵</SelectItem>\r\n              <SelectItem value={ROLES.KNIGHT}>骑士</SelectItem>\r\n              <SelectItem value={ROLES.CIVILIAN}>平民</SelectItem>\r\n            </SelectContent>\r\n          </Select>\r\n        </div>\r\n\r\n        <div className=\"flex items-center gap-4\">\r\n          <span className=\"text-sm\">邮箱域名:</span>\r\n          <div className=\"flex-1\">\r\n            <Input \r\n              value={emailDomains}\r\n              onChange={(e) => setEmailDomains(e.target.value)}\r\n              placeholder=\"多个域名用逗号分隔，如: moemail.app,bitibiti.com\"\r\n            />\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"flex items-center gap-4\">\r\n          <span className=\"text-sm\">管理员联系方式:</span>\r\n          <div className=\"flex-1\">\r\n            <Input \r\n              value={adminContact}\r\n              onChange={(e) => setAdminContact(e.target.value)}\r\n              placeholder=\"如: 微信号、邮箱等\"\r\n            />\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"flex items-center gap-4\">\r\n          <span className=\"text-sm\">最大邮箱数量:</span>\r\n          <div className=\"flex-1\">\r\n            <Input \r\n              type=\"number\"\r\n              min=\"1\"\r\n              max=\"100\"\r\n              value={maxEmails}\r\n              onChange={(e) => setMaxEmails(e.target.value)}\r\n              placeholder={`默认为 ${EMAIL_CONFIG.MAX_ACTIVE_EMAILS}`}\r\n            />\r\n          </div>\r\n        </div>\r\n\r\n        <Button \r\n          onClick={handleSave}\r\n          disabled={loading}\r\n          className=\"w-full\"\r\n        >\r\n          保存\r\n        </Button>\r\n      </div>\r\n    </div>\r\n  )\r\n} ", "\"use client\"\r\n\r\nimport { useState, useEffect } from \"react\"\r\nimport { <PERSON><PERSON> } from \"@/components/ui/button\"\r\nimport { Input } from \"@/components/ui/input\"\r\nimport { Key, Plus, Loader2, <PERSON><PERSON>, Trash2, <PERSON><PERSON>ronDown, ChevronUp } from \"lucide-react\"\r\nimport { useToast } from \"@/components/ui/use-toast\"\r\nimport {\r\n  Dialog,\r\n  DialogContent,\r\n  DialogHeader,\r\n  DialogTitle,\r\n  DialogTrigger,\r\n  DialogFooter,\r\n  DialogDescription,\r\n  DialogClose,\r\n} from \"@/components/ui/dialog\"\r\nimport { Switch } from \"@/components/ui/switch\"\r\nimport { Label } from \"@/components/ui/label\"\r\nimport { useCopy } from \"@/hooks/use-copy\"\r\nimport { useRolePermission } from \"@/hooks/use-role-permission\"\r\nimport { PERMISSIONS } from \"@/lib/permissions\"\r\nimport { useConfig } from \"@/hooks/use-config\"\r\n\r\ntype ApiKey = {\r\n  id: string\r\n  name: string\r\n  key: string\r\n  createdAt: string\r\n  expiresAt: string | null\r\n  enabled: boolean\r\n}\r\n\r\nexport function ApiKeyPanel() {\r\n  const [apiKeys, setApiKeys] = useState<ApiKey[]>([])\r\n  const [loading, setLoading] = useState(false)\r\n  const [createDialogOpen, setCreateDialogOpen] = useState(false)\r\n  const [newKeyName, setNewKeyName] = useState(\"\")\r\n  const [newKey, setNewKey] = useState<string | null>(null)\r\n  const { toast } = useToast()\r\n  const { copyToClipboard } = useCopy()\r\n  const [showExamples, setShowExamples] = useState(false)\r\n  const [isLoading, setIsLoading] = useState(true)\r\n  const { checkPermission } = useRolePermission()\r\n  const canManageApiKey = checkPermission(PERMISSIONS.MANAGE_API_KEY)\r\n\r\n  const fetchApiKeys = async () => {\r\n    try {\r\n      const res = await fetch(\"/api/api-keys\")\r\n      if (!res.ok) throw new Error(\"获取 API Keys 失败\")\r\n      const data = await res.json() as { apiKeys: ApiKey[] }\r\n      setApiKeys(data.apiKeys)\r\n    } catch (error) {\r\n      console.error(error)\r\n      toast({\r\n        title: \"获取失败\",\r\n        description: \"获取 API Keys 列表失败\",\r\n        variant: \"destructive\"\r\n      })\r\n    } finally {\r\n      setIsLoading(false)\r\n    }\r\n  }\r\n\r\n  useEffect(() => {\r\n    if (canManageApiKey) {\r\n      fetchApiKeys()\r\n    }\r\n  }, [canManageApiKey])\r\n\r\n  const { config } = useConfig()\r\n\r\n  const createApiKey = async () => {\r\n    if (!newKeyName.trim()) return\r\n\r\n    setLoading(true)\r\n    try {\r\n      const res = await fetch(\"/api/api-keys\", {\r\n        method: \"POST\",\r\n        headers: { \"Content-Type\": \"application/json\" },\r\n        body: JSON.stringify({ name: newKeyName })\r\n      })\r\n\r\n      if (!res.ok) throw new Error(\"创建 API Key 失败\")\r\n\r\n      const data = await res.json() as { key: string }\r\n      setNewKey(data.key)\r\n      fetchApiKeys()\r\n    } catch (error) {\r\n      toast({\r\n        title: \"创建失败\",\r\n        description: error instanceof Error ? error.message : \"请稍后重试\",\r\n        variant: \"destructive\"\r\n      })\r\n      setCreateDialogOpen(false)\r\n    } finally {\r\n      setLoading(false)\r\n    }\r\n  }\r\n\r\n  const handleDialogClose = () => {\r\n    setCreateDialogOpen(false)\r\n    setNewKeyName(\"\")\r\n    setNewKey(null)\r\n  }\r\n\r\n  const toggleApiKey = async (id: string, enabled: boolean) => {\r\n    try {\r\n      const res = await fetch(`/api/api-keys/${id}`, {\r\n        method: \"PATCH\",\r\n        headers: { \"Content-Type\": \"application/json\" },\r\n        body: JSON.stringify({ enabled })\r\n      })\r\n\r\n      if (!res.ok) throw new Error(\"更新失败\")\r\n\r\n      setApiKeys(keys =>\r\n        keys.map(key =>\r\n          key.id === id ? { ...key, enabled } : key\r\n        )\r\n      )\r\n    } catch (error) {\r\n      console.error(error)\r\n      toast({\r\n        title: \"更新失败\",\r\n        description: \"更新 API Key 状态失败\",\r\n        variant: \"destructive\"\r\n      })\r\n    }\r\n  }\r\n\r\n  const deleteApiKey = async (id: string) => {\r\n    try {\r\n      const res = await fetch(`/api/api-keys/${id}`, {\r\n        method: \"DELETE\"\r\n      })\r\n\r\n      if (!res.ok) throw new Error(\"删除失败\")\r\n\r\n      setApiKeys(keys => keys.filter(key => key.id !== id))\r\n      toast({\r\n        title: \"删除成功\",\r\n        description: \"API Key 已删除\"\r\n      })\r\n    } catch (error) {\r\n      console.error(error)\r\n      toast({\r\n        title: \"删除失败\",\r\n        description: \"删除 API Key 失败\",\r\n        variant: \"destructive\"\r\n      })\r\n    }\r\n  }\r\n\r\n  return (\r\n    <div className=\"bg-background rounded-lg border-2 border-primary/20 p-6 space-y-6\">\r\n      <div className=\"flex items-center justify-between mb-6\">\r\n        <div className=\"flex items-center gap-2\">\r\n          <Key className=\"w-5 h-5 text-primary\" />\r\n          <h2 className=\"text-lg font-semibold\">API Keys</h2>\r\n        </div>\r\n        {\r\n          canManageApiKey && (\r\n            <Dialog open={createDialogOpen} onOpenChange={setCreateDialogOpen}>\r\n              <DialogTrigger asChild>\r\n                <Button className=\"gap-2\" onClick={() => setCreateDialogOpen(true)}>\r\n                  <Plus className=\"w-4 h-4\" />\r\n                  创建 API Key\r\n                </Button>\r\n              </DialogTrigger>\r\n              <DialogContent>\r\n                <DialogHeader>\r\n                  <DialogTitle>\r\n                    {newKey ? \"API Key 创建成功\" : \"创建新的 API Key\"}\r\n                  </DialogTitle>\r\n                  {newKey && (\r\n                    <DialogDescription className=\"text-destructive\">\r\n                      请立即保存此密钥，它只会显示一次且无法恢复\r\n                    </DialogDescription>\r\n                  )}\r\n                </DialogHeader>\r\n\r\n                {!newKey ? (\r\n                  <div className=\"space-y-4 py-4\">\r\n                    <div className=\"space-y-2\">\r\n                      <Label>名称</Label>\r\n                      <Input\r\n                        value={newKeyName}\r\n                        onChange={(e) => setNewKeyName(e.target.value)}\r\n                        placeholder=\"为你的 API Key 起个名字\"\r\n                      />\r\n                    </div>\r\n                  </div>\r\n                ) : (\r\n                  <div className=\"space-y-4 py-4\">\r\n                    <div className=\"space-y-2\">\r\n                      <Label>API Key</Label>\r\n                      <div className=\"flex gap-2\">\r\n                        <Input\r\n                          value={newKey}\r\n                          readOnly\r\n                          className=\"font-mono text-sm\"\r\n                        />\r\n                        <Button\r\n                          variant=\"outline\"\r\n                          size=\"icon\"\r\n                          onClick={() => copyToClipboard(newKey)}\r\n                        >\r\n                          <Copy className=\"w-4 h-4\" />\r\n                        </Button>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                )}\r\n\r\n                <DialogFooter>\r\n                  <DialogClose asChild>\r\n                    <Button\r\n                      variant=\"outline\"\r\n                      onClick={handleDialogClose}\r\n                      disabled={loading}\r\n                    >\r\n                      {newKey ? \"完成\" : \"取消\"}\r\n                    </Button>\r\n                  </DialogClose>\r\n                  {!newKey && (\r\n                    <Button\r\n                      onClick={createApiKey}\r\n                      disabled={loading || !newKeyName.trim()}\r\n                    >\r\n                      {loading ? (\r\n                        <Loader2 className=\"w-4 h-4 animate-spin\" />\r\n                      ) : (\r\n                        \"创建\"\r\n                      )}\r\n                    </Button>\r\n                  )}\r\n                </DialogFooter>\r\n              </DialogContent>\r\n            </Dialog>\r\n          )\r\n        }\r\n      </div>\r\n\r\n      {\r\n        !canManageApiKey ? (\r\n          <div className=\"text-center text-muted-foreground py-8\">\r\n            <p>需要公爵或更高权限才能管理 API Key</p>\r\n            <p className=\"mt-2\">请联系网站管理员升级您的角色</p>\r\n            {\r\n              config?.adminContact && (\r\n                <p className=\"mt-2\">管理员联系方式：{config.adminContact}</p>\r\n              )\r\n            }\r\n          </div>\r\n        ) : (\r\n          <div className=\"space-y-4\">\r\n            {isLoading ? (\r\n              <div className=\"text-center py-8 space-y-3\">\r\n                <div className=\"w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center mx-auto\">\r\n                  <Loader2 className=\"w-6 h-6 text-primary animate-spin\" />\r\n                </div>\r\n                <div>\r\n                  <p className=\"text-sm text-muted-foreground\">加载中...</p>\r\n                </div>\r\n              </div>\r\n            ) : apiKeys.length === 0 ? (\r\n              <div className=\"text-center py-8 space-y-3\">\r\n                <div className=\"w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center mx-auto\">\r\n                  <Key className=\"w-6 h-6 text-primary\" />\r\n                </div>\r\n                <div>\r\n                  <h3 className=\"text-lg font-medium\">没有 API Keys</h3>\r\n                  <p className=\"text-sm text-muted-foreground mt-1\">\r\n                    点击上方的创建 &quot;API Key&quot; 按钮来创建你的第一个 API Key\r\n                  </p>\r\n                </div>\r\n              </div>\r\n            ) : (\r\n              <>\r\n                {apiKeys.map((key) => (\r\n                  <div\r\n                    key={key.id}\r\n                    className=\"flex items-center justify-between p-4 rounded-lg border bg-card\"\r\n                  >\r\n                    <div className=\"space-y-1\">\r\n                      <div className=\"font-medium\">{key.name}</div>\r\n                      <div className=\"text-sm text-muted-foreground\">\r\n                        创建于 {new Date(key.createdAt).toLocaleString()}\r\n                      </div>\r\n                    </div>\r\n                    <div className=\"flex items-center gap-2\">\r\n                      <Switch\r\n                        checked={key.enabled}\r\n                        onCheckedChange={(checked) => toggleApiKey(key.id, checked)}\r\n                      />\r\n                      <Button\r\n                        variant=\"ghost\"\r\n                        size=\"icon\"\r\n                        onClick={() => deleteApiKey(key.id)}\r\n                      >\r\n                        <Trash2 className=\"w-4 h-4\" />\r\n                      </Button>\r\n                    </div>\r\n                  </div>\r\n                ))}\r\n\r\n                <div className=\"mt-8 space-y-4\">\r\n                  <button\r\n                    type=\"button\"\r\n                    className=\"flex items-center gap-1 text-sm text-muted-foreground hover:text-foreground transition-colors\"\r\n                    onClick={() => setShowExamples(!showExamples)}\r\n                  >\r\n                    {showExamples ? <ChevronUp className=\"w-4 h-4\" /> : <ChevronDown className=\"w-4 h-4\" />}\r\n                    查看使用文档\r\n                  </button>\r\n\r\n                  {showExamples && (\r\n                    <div className=\"rounded-lg border bg-card p-4 space-y-4\">\r\n                      <div className=\"space-y-2\">\r\n                        <div className=\"flex items-center justify-between\">\r\n                          <div className=\"text-sm font-medium\">生成临时邮箱</div>\r\n                          <Button\r\n                            variant=\"ghost\"\r\n                            size=\"icon\"\r\n                            onClick={() => copyToClipboard(\r\n                              `curl -X POST ${window.location.protocol}//${window.location.host}/api/emails/generate \\\\\r\n  -H \"X-API-Key: YOUR_API_KEY\" \\\\\r\n  -H \"Content-Type: application/json\" \\\\\r\n  -d '{\r\n    \"name\": \"test\",\r\n    \"expiryTime\": 3600000,\r\n    \"domain\": \"moemail.app\"\r\n  }'`\r\n                            )}\r\n                          >\r\n                            <Copy className=\"w-4 h-4\" />\r\n                          </Button>\r\n                        </div>\r\n                        <pre className=\"text-xs bg-muted/50 rounded-lg p-4 overflow-x-auto\">\r\n                          {`curl -X POST ${window.location.protocol}//${window.location.host}/api/emails/generate \\\\\r\n  -H \"X-API-Key: YOUR_API_KEY\" \\\\\r\n  -H \"Content-Type: application/json\" \\\\\r\n  -d '{\r\n    \"name\": \"test\",\r\n    \"expiryTime\": 3600000,\r\n    \"domain\": \"moemail.app\"\r\n  }'`}\r\n                        </pre>\r\n                      </div>\r\n\r\n                      <div className=\"space-y-2\">\r\n                        <div className=\"flex items-center justify-between\">\r\n                          <div className=\"text-sm font-medium\">获取邮箱列表</div>\r\n                          <Button\r\n                            variant=\"ghost\"\r\n                            size=\"icon\"\r\n                            onClick={() => copyToClipboard(\r\n                              `curl ${window.location.protocol}//${window.location.host}/api/emails?cursor=CURSOR \\\\\r\n  -H \"X-API-Key: YOUR_API_KEY\"`\r\n                            )}\r\n                          >\r\n                            <Copy className=\"w-4 h-4\" />\r\n                          </Button>\r\n                        </div>\r\n                        <pre className=\"text-xs bg-muted/50 rounded-lg p-4 overflow-x-auto\">\r\n                          {`curl ${window.location.protocol}//${window.location.host}/api/emails?cursor=CURSOR \\\\\r\n  -H \"X-API-Key: YOUR_API_KEY\"`}\r\n                        </pre>\r\n                      </div>\r\n\r\n                      <div className=\"space-y-2\">\r\n                        <div className=\"flex items-center justify-between\">\r\n                          <div className=\"text-sm font-medium\">获取邮件列表</div>\r\n                          <Button\r\n                            variant=\"ghost\"\r\n                            size=\"icon\"\r\n                            onClick={() => copyToClipboard(\r\n                              `curl ${window.location.protocol}//${window.location.host}/api/emails/{emailId}?cursor=CURSOR \\\\\r\n  -H \"X-API-Key: YOUR_API_KEY\"`\r\n                            )}\r\n                          >\r\n                            <Copy className=\"w-4 h-4\" />\r\n                          </Button>\r\n                        </div>\r\n                        <pre className=\"text-xs bg-muted/50 rounded-lg p-4 overflow-x-auto\">\r\n                          {`curl ${window.location.protocol}//${window.location.host}/api/emails/{emailId}?cursor=CURSOR \\\\\r\n  -H \"X-API-Key: YOUR_API_KEY\"`}\r\n                        </pre>\r\n                      </div>\r\n\r\n                      <div className=\"space-y-2\">\r\n                        <div className=\"flex items-center justify-between\">\r\n                          <div className=\"text-sm font-medium\">获取单封邮件</div>\r\n                          <Button\r\n                            variant=\"ghost\"\r\n                            size=\"icon\"\r\n                            onClick={() => copyToClipboard(\r\n                              `curl ${window.location.protocol}//${window.location.host}/api/emails/{emailId}/{messageId} \\\\\r\n  -H \"X-API-Key: YOUR_API_KEY\"`\r\n                            )}\r\n                          >\r\n                            <Copy className=\"w-4 h-4\" />\r\n                          </Button>\r\n                        </div>\r\n                        <pre className=\"text-xs bg-muted/50 rounded-lg p-4 overflow-x-auto\">\r\n                          {`curl ${window.location.protocol}//${window.location.host}/api/emails/{emailId}/{messageId} \\\\\r\n  -H \"X-API-Key: YOUR_API_KEY\"`}\r\n                        </pre>\r\n                      </div>\r\n\r\n                      <div className=\"text-xs text-muted-foreground mt-4\">\r\n                        <p>注意：</p>\r\n                        <ul className=\"list-disc list-inside space-y-1 mt-2\">\r\n                          <li>请将 YOUR_API_KEY 替换为你的实际 API Key</li>\r\n                          <li>emailId 是邮箱的唯一标识符</li>\r\n                          <li>messageId 是邮件的唯一标识符</li>\r\n                          <li>expiryTime 是邮箱的有效期（毫秒），可选值：3600000（1小时）、86400000（1天）、604800000（7天）、0（永久）</li>\r\n                          <li>domain 是邮箱域名，可通过 /api/emails/domains 获取可用域名列表</li>\r\n                          <li>cursor 用于分页，从上一次请求的响应中获取 nextCursor</li>\r\n                          <li>所有请求都需要包含 X-API-Key 请求头</li>\r\n                        </ul>\r\n                      </div>\r\n                    </div>\r\n                  )}\r\n                </div>\r\n              </>\r\n            )}\r\n          </div>\r\n        )\r\n      }\r\n    </div>\r\n  )\r\n} ", "import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Eye\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMi4wNjIgMTIuMzQ4YTEgMSAwIDAgMSAwLS42OTYgMTAuNzUgMTAuNzUgMCAwIDEgMTkuODc2IDAgMSAxIDAgMCAxIDAgLjY5NiAxMC43NSAxMC43NSAwIDAgMS0xOS44NzYgMCIgLz4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSIzIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/eye\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Eye = createLucideIcon('Eye', [\n  [\n    'path',\n    {\n      d: 'M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0',\n      key: '1nclc0',\n    },\n  ],\n  ['circle', { cx: '12', cy: '12', r: '3', key: '1v7zrd' }],\n]);\n\nexport default Eye;\n", "import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name EyeOff\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTAuNzMzIDUuMDc2YTEwLjc0NCAxMC43NDQgMCAwIDEgMTEuMjA1IDYuNTc1IDEgMSAwIDAgMSAwIC42OTYgMTAuNzQ3IDEwLjc0NyAwIDAgMS0xLjQ0NCAyLjQ5IiAvPgogIDxwYXRoIGQ9Ik0xNC4wODQgMTQuMTU4YTMgMyAwIDAgMS00LjI0Mi00LjI0MiIgLz4KICA8cGF0aCBkPSJNMTcuNDc5IDE3LjQ5OWExMC43NSAxMC43NSAwIDAgMS0xNS40MTctNS4xNTEgMSAxIDAgMCAxIDAtLjY5NiAxMC43NSAxMC43NSAwIDAgMSA0LjQ0Ni01LjE0MyIgLz4KICA8cGF0aCBkPSJtMiAyIDIwIDIwIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/eye-off\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst EyeOff = createLucideIcon('EyeOff', [\n  [\n    'path',\n    {\n      d: 'M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49',\n      key: 'ct8e1f',\n    },\n  ],\n  ['path', { d: 'M14.084 14.158a3 3 0 0 1-4.242-4.242', key: '151rxh' }],\n  [\n    'path',\n    {\n      d: 'M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143',\n      key: '13bj9a',\n    },\n  ],\n  ['path', { d: 'm2 2 20 20', key: '1ooewy' }],\n]);\n\nexport default EyeOff;\n", "import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Users\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTYgMjF2LTJhNCA0IDAgMCAwLTQtNEg2YTQgNCAwIDAgMC00IDR2MiIgLz4KICA8Y2lyY2xlIGN4PSI5IiBjeT0iNyIgcj0iNCIgLz4KICA8cGF0aCBkPSJNMjIgMjF2LTJhNCA0IDAgMCAwLTMtMy44NyIgLz4KICA8cGF0aCBkPSJNMTYgMy4xM2E0IDQgMCAwIDEgMCA3Ljc1IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/users\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Users = createLucideIcon('Users', [\n  ['path', { d: 'M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2', key: '1yyitq' }],\n  ['circle', { cx: '9', cy: '7', r: '4', key: 'nufk8' }],\n  ['path', { d: 'M22 21v-2a4 4 0 0 0-3-3.87', key: 'kshegd' }],\n  ['path', { d: 'M16 3.13a4 4 0 0 1 0 7.75', key: '1da9ce' }],\n]);\n\nexport default Users;\n", "\"use client\"\n\nimport { useState, useEffect } from \"react\"\nimport { <PERSON><PERSON> } from \"@/components/ui/button\"\nimport { Input } from \"@/components/ui/input\"\nimport { Mail, Plus, Loader2, Trash2, Eye, EyeOff, ChevronDown, ChevronUp, Users, <PERSON>fresh<PERSON><PERSON>, <PERSON><PERSON>, <PERSON> } from \"lucide-react\"\nimport { useToast } from \"@/components/ui/use-toast\"\nimport {\n  <PERSON><PERSON>,\n  DialogContent,\n  DialogHeader,\n  DialogTitle,\n  DialogTrigger,\n  DialogFooter,\n  DialogDescription,\n  DialogClose,\n} from \"@/components/ui/dialog\"\nimport { Switch } from \"@/components/ui/switch\"\nimport { Label } from \"@/components/ui/label\"\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from \"@/components/ui/select\"\nimport { useRolePermission } from \"@/hooks/use-role-permission\"\nimport { PERMISSIONS } from \"@/lib/permissions\"\nimport { useConfig } from \"@/hooks/use-config\"\nimport { Checkbox } from \"@/components/ui/checkbox\"\nimport { RadioGroup, RadioGroupItem } from \"@/components/ui/radio-group\"\n\ntype EmailCredential = {\n  id: string\n  emailAddress: string\n  jwt: string\n  enabled: boolean\n  createdAt: string\n  lastUsedAt: string | null\n  bindingCount: number\n}\n\ntype UserEmailBinding = {\n  id: string\n  emailAddress: string\n  createdAt: string\n  enabled: boolean\n}\n\nexport function EmailCredentialsPanel() {\n  const [credentials, setCredentials] = useState<EmailCredential[]>([])\n  const [userBindings, setUserBindings] = useState<UserEmailBinding[]>([])\n  const [loading, setLoading] = useState(false)\n  const [bindDialogOpen, setBindDialogOpen] = useState(false)\n  const [extractDialogOpen, setExtractDialogOpen] = useState(false)\n  const [newBinding, setNewBinding] = useState({\n    jwt: \"\"\n  })\n  const [extractEmail, setExtractEmail] = useState(\"\")\n  const [extractedCredential, setExtractedCredential] = useState<EmailCredential | null>(null)\n  const [extractedCredentials, setExtractedCredentials] = useState<EmailCredential[]>([])\n  const [selectedEmails, setSelectedEmails] = useState<string[]>([])\n  const [extractMode, setExtractMode] = useState<'input' | 'select'>('input')\n  const [showJWT, setShowJWT] = useState(false)\n  const [showExamples, setShowExamples] = useState(false)\n  const [isLoading, setIsLoading] = useState(true)\n  const [refreshing, setRefreshing] = useState(false)\n  const { toast } = useToast()\n  const { checkPermission } = useRolePermission()\n  const canManageEmailCredentials = checkPermission(PERMISSIONS.MANAGE_WEBHOOK)\n\n  const fetchCredentials = async () => {\n    if (!canManageEmailCredentials) return\n    try {\n      const res = await fetch(\"/api/email-credentials\")\n      if (!res.ok) throw new Error(\"获取邮箱凭证失败\")\n      const data = await res.json() as { credentials: EmailCredential[] }\n      setCredentials(data.credentials)\n    } catch (error) {\n      console.error(error)\n      toast({\n        title: \"获取失败\",\n        description: \"获取邮箱凭证列表失败\",\n        variant: \"destructive\"\n      })\n    }\n  }\n\n  const fetchUserBindings = async () => {\n    try {\n      const res = await fetch(\"/api/email-credentials/bindings\")\n      if (!res.ok) throw new Error(\"获取邮箱绑定失败\")\n      const data = await res.json() as { bindings: UserEmailBinding[] }\n      setUserBindings(data.bindings)\n    } catch (error) {\n      console.error(error)\n      toast({\n        title: \"获取失败\",\n        description: \"获取邮箱绑定列表失败\",\n        variant: \"destructive\"\n      })\n    }\n  }\n\n  const fetchData = async () => {\n    setIsLoading(true)\n    await Promise.all([\n      canManageEmailCredentials ? fetchCredentials() : Promise.resolve(),\n      fetchUserBindings()\n    ])\n    setIsLoading(false)\n    setRefreshing(false)\n  }\n\n  useEffect(() => {\n    fetchData()\n  }, [canManageEmailCredentials])\n\n  const { config } = useConfig()\n\n  const handleRefresh = async () => {\n    setRefreshing(true)\n    await fetchData()\n  }\n\n  const bindEmail = async () => {\n    if (!newBinding.jwt.trim()) {\n      toast({\n        title: \"请输入邮箱凭证\",\n        description: \"邮箱凭证不能为空\",\n        variant: \"destructive\"\n      })\n      return\n    }\n\n    setLoading(true)\n    try {\n      const res = await fetch(\"/api/email-credentials\", {\n        method: \"POST\",\n        headers: { \"Content-Type\": \"application/json\" },\n        body: JSON.stringify(newBinding)\n      })\n\n      if (!res.ok) {\n        const error = await res.json()\n        throw new Error(error.error || \"绑定邮箱失败\")\n      }\n\n      toast({\n        title: \"绑定成功\",\n        description: \"邮箱绑定成功\"\n      })\n\n      fetchUserBindings()\n      handleBindDialogClose()\n    } catch (error) {\n      toast({\n        title: \"绑定失败\",\n        description: error instanceof Error ? error.message : \"请稍后重试\",\n        variant: \"destructive\"\n      })\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const extractCredential = async () => {\n    if (extractMode === 'input') {\n      if (!extractEmail.trim()) {\n        toast({\n          title: \"请输入邮箱地址\",\n          description: \"邮箱地址不能为空\",\n          variant: \"destructive\"\n        })\n        return\n      }\n    } else {\n      if (selectedEmails.length === 0) {\n        toast({\n          title: \"请选择邮箱\",\n          description: \"至少选择一个邮箱\",\n          variant: \"destructive\"\n        })\n        return\n      }\n    }\n\n    setLoading(true)\n    try {\n      const endpoint = canManageEmailCredentials ? \"/api/email-credentials/extract\" : \"/api/email-credentials/extract-user\"\n      const requestBody = extractMode === 'input'\n        ? { emailAddress: extractEmail }\n        : { emailAddresses: selectedEmails }\n\n      const res = await fetch(endpoint, {\n        method: \"POST\",\n        headers: { \"Content-Type\": \"application/json\" },\n        body: JSON.stringify(requestBody)\n      })\n\n      if (!res.ok) {\n        const error = await res.json()\n        throw new Error(error.error || \"提取邮箱凭证失败\")\n      }\n\n      const data = await res.json()\n\n      if (extractMode === 'input') {\n        setExtractedCredential(data.credential)\n        setExtractedCredentials([])\n      } else {\n        setExtractedCredentials(data.credentials)\n        setExtractedCredential(null)\n      }\n\n      toast({\n        title: \"提取成功\",\n        description: `已提取 ${extractMode === 'input' ? 1 : data.credentials.length} 个邮箱凭证`\n      })\n    } catch (error) {\n      toast({\n        title: \"提取失败\",\n        description: error instanceof Error ? error.message : \"请稍后重试\",\n        variant: \"destructive\"\n      })\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const handleBindDialogClose = () => {\n    setBindDialogOpen(false)\n    setNewBinding({ jwt: \"\" })\n  }\n\n  const handleExtractDialogClose = () => {\n    setExtractDialogOpen(false)\n    setExtractEmail(\"\")\n    setExtractedCredential(null)\n    setExtractedCredentials([])\n    setSelectedEmails([])\n    setExtractMode('input')\n    setShowJWT(false)\n  }\n\n  const unbindEmail = async (emailAddress: string) => {\n    try {\n      const res = await fetch(\"/api/email-credentials/bindings\", {\n        method: \"DELETE\",\n        headers: { \"Content-Type\": \"application/json\" },\n        body: JSON.stringify({ emailAddress })\n      })\n\n      if (!res.ok) throw new Error(\"解绑失败\")\n\n      setUserBindings(bindings => bindings.filter(binding => binding.emailAddress !== emailAddress))\n      toast({\n        title: \"解绑成功\",\n        description: \"邮箱已解绑\"\n      })\n    } catch (error) {\n      console.error(error)\n      toast({\n        title: \"解绑失败\",\n        description: \"解绑邮箱失败\",\n        variant: \"destructive\"\n      })\n    }\n  }\n\n  const copyToClipboard = async (text: string) => {\n    try {\n      await navigator.clipboard.writeText(text)\n      toast({\n        title: \"复制成功\",\n        description: \"已复制到剪贴板\"\n      })\n    } catch (error) {\n      toast({\n        title: \"复制失败\",\n        description: \"无法复制到剪贴板\",\n        variant: \"destructive\"\n      })\n    }\n  }\n\n  return (\n    <div className=\"bg-background rounded-lg border-2 border-primary/20 p-6 space-y-6\">\n      <div className=\"flex items-center justify-between mb-6\">\n        <div className=\"flex items-center gap-2\">\n          <Mail className=\"w-5 h-5 text-primary\" />\n          <h2 className=\"text-lg font-semibold\">邮箱凭证系统</h2>\n        </div>\n        <div className=\"flex items-center gap-2\">\n          <Button\n            variant=\"ghost\"\n            size=\"icon\"\n            onClick={handleRefresh}\n            disabled={refreshing}\n            className={refreshing ? \"animate-spin\" : \"\"}\n          >\n            <RefreshCw className=\"w-4 h-4\" />\n          </Button>\n\n          {/* 用户绑定邮箱按钮 */}\n          <Dialog open={bindDialogOpen} onOpenChange={setBindDialogOpen}>\n            <DialogTrigger asChild>\n              <Button className=\"gap-2\">\n                <Plus className=\"w-4 h-4\" />\n                绑定邮箱\n              </Button>\n            </DialogTrigger>\n            <DialogContent className=\"max-w-md\">\n              <DialogHeader>\n                <DialogTitle>绑定邮箱</DialogTitle>\n                <DialogDescription>\n                  输入邮箱凭证来绑定邮箱到您的账户\n                </DialogDescription>\n              </DialogHeader>\n\n              <div className=\"space-y-4 py-4\">\n                <div className=\"space-y-2\">\n                  <Label>邮箱凭证 (JWT)</Label>\n                  <Input\n                    value={newBinding.jwt}\n                    onChange={(e) => setNewBinding(prev => ({ ...prev, jwt: e.target.value }))}\n                    placeholder=\"请输入邮箱凭证...\"\n                  />\n                </div>\n              </div>\n\n              <DialogFooter>\n                <DialogClose asChild>\n                  <Button variant=\"outline\" onClick={handleBindDialogClose} disabled={loading}>\n                    取消\n                  </Button>\n                </DialogClose>\n                <Button onClick={bindEmail} disabled={loading}>\n                  {loading ? <Loader2 className=\"w-4 h-4 animate-spin\" /> : \"绑定\"}\n                </Button>\n              </DialogFooter>\n            </DialogContent>\n          </Dialog>\n\n          {/* 管理员提取凭证按钮 */}\n          {canManageEmailCredentials && (\n            <Dialog open={extractDialogOpen} onOpenChange={setExtractDialogOpen}>\n              <DialogTrigger asChild>\n                <Button variant=\"outline\" className=\"gap-2\">\n                  <Eye className=\"w-4 h-4\" />\n                  提取凭证\n                </Button>\n              </DialogTrigger>\n              <DialogContent className=\"max-w-md\">\n                <DialogHeader>\n                  <DialogTitle>提取邮箱凭证</DialogTitle>\n                  <DialogDescription>\n                    输入邮箱地址来提取对应的邮箱凭证\n                  </DialogDescription>\n                </DialogHeader>\n\n                <div className=\"space-y-4 py-4\">\n                  <div className=\"space-y-2\">\n                    <Label>邮箱地址</Label>\n                    <Input\n                      type=\"email\"\n                      value={extractEmail}\n                      onChange={(e) => setExtractEmail(e.target.value)}\n                      placeholder=\"<EMAIL>\"\n                    />\n                  </div>\n\n                  {extractedCredential && (\n                    <div className=\"space-y-2\">\n                      <Label>提取的凭证</Label>\n                      <div className=\"relative\">\n                        <Input\n                          type={showJWT ? \"text\" : \"password\"}\n                          value={extractedCredential.jwt}\n                          readOnly\n                          className=\"pr-20\"\n                        />\n                        <div className=\"absolute right-2 top-0 h-full flex items-center gap-1\">\n                          <Button\n                            type=\"button\"\n                            variant=\"ghost\"\n                            size=\"icon\"\n                            onClick={() => setShowJWT(!showJWT)}\n                          >\n                            {showJWT ? <EyeOff className=\"w-4 h-4\" /> : <Eye className=\"w-4 h-4\" />}\n                          </Button>\n                          <Button\n                            type=\"button\"\n                            variant=\"ghost\"\n                            size=\"icon\"\n                            onClick={() => copyToClipboard(extractedCredential.jwt)}\n                          >\n                            <Copy className=\"w-4 h-4\" />\n                          </Button>\n                        </div>\n                      </div>\n                      <div className=\"text-sm text-muted-foreground\">\n                        绑定用户数: {extractedCredential.bindingCount}\n                      </div>\n                    </div>\n                  )}\n                </div>\n\n                <DialogFooter>\n                  <DialogClose asChild>\n                    <Button variant=\"outline\" onClick={handleExtractDialogClose}>\n                      关闭\n                    </Button>\n                  </DialogClose>\n                  <Button onClick={extractCredential} disabled={loading}>\n                    {loading ? <Loader2 className=\"w-4 h-4 animate-spin\" /> : \"提取\"}\n                  </Button>\n                </DialogFooter>\n              </DialogContent>\n            </Dialog>\n          )}\n        </div>\n      </div>\n\n      {/* 主要内容区域 */}\n      {isLoading ? (\n        <div className=\"text-center py-8 space-y-3\">\n          <div className=\"w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center mx-auto\">\n            <Loader2 className=\"w-6 h-6 text-primary animate-spin\" />\n          </div>\n          <div>\n            <p className=\"text-sm text-muted-foreground\">加载中...</p>\n          </div>\n        </div>\n      ) : (\n        <div className=\"space-y-6\">\n          {/* 用户绑定的邮箱列表 */}\n          <div className=\"space-y-4\">\n            <h3 className=\"text-lg font-medium\">我绑定的邮箱</h3>\n            {userBindings.length === 0 ? (\n              <div className=\"text-center py-6 space-y-3 border rounded-lg\">\n                <div className=\"w-10 h-10 rounded-full bg-primary/10 flex items-center justify-center mx-auto\">\n                  <Mail className=\"w-5 h-5 text-primary\" />\n                </div>\n                <div>\n                  <p className=\"text-sm text-muted-foreground\">\n                    您还没有绑定任何邮箱\n                  </p>\n                  <p className=\"text-xs text-muted-foreground mt-1\">\n                    点击上方的 \"绑定邮箱\" 按钮来绑定第一个邮箱\n                  </p>\n                </div>\n              </div>\n            ) : (\n              <div className=\"space-y-2\">\n                {userBindings.map((binding) => (\n                  <div\n                    key={binding.id}\n                    className=\"flex items-center justify-between p-3 rounded-lg border bg-card\"\n                  >\n                    <div className=\"space-y-1\">\n                      <div className=\"font-medium\">{binding.emailAddress}</div>\n                      <div className=\"text-xs text-muted-foreground\">\n                        绑定于 {new Date(binding.createdAt).toLocaleString()}\n                      </div>\n                    </div>\n                    <Button\n                      variant=\"ghost\"\n                      size=\"sm\"\n                      onClick={() => unbindEmail(binding.emailAddress)}\n                    >\n                      <Trash2 className=\"w-4 h-4\" />\n                    </Button>\n                  </div>\n                ))}\n              </div>\n            )}\n          </div>\n\n          {/* 管理员邮箱凭证列表 */}\n          {canManageEmailCredentials && (\n            <div className=\"space-y-4\">\n              <h3 className=\"text-lg font-medium\">系统邮箱凭证 (管理员)</h3>\n              {credentials.length === 0 ? (\n                <div className=\"text-center py-6 space-y-3 border rounded-lg\">\n                  <div className=\"w-10 h-10 rounded-full bg-primary/10 flex items-center justify-center mx-auto\">\n                    <Mail className=\"w-5 h-5 text-primary\" />\n                  </div>\n                  <div>\n                    <p className=\"text-sm text-muted-foreground\">\n                      系统中还没有邮箱凭证\n                    </p>\n                    <p className=\"text-xs text-muted-foreground mt-1\">\n                      创建邮箱时会自动生成对应的凭证\n                    </p>\n                  </div>\n                </div>\n              ) : (\n                <div className=\"space-y-2\">\n                  {credentials.map((credential) => (\n                    <div\n                      key={credential.id}\n                      className=\"flex items-center justify-between p-4 rounded-lg border bg-card\"\n                    >\n                      <div className=\"space-y-1\">\n                        <div className=\"font-medium\">{credential.emailAddress}</div>\n                        <div className=\"flex items-center gap-4 text-xs text-muted-foreground\">\n                          <span>创建于 {new Date(credential.createdAt).toLocaleString()}</span>\n                          {credential.lastUsedAt && (\n                            <span>最后使用: {new Date(credential.lastUsedAt).toLocaleString()}</span>\n                          )}\n                          <div className=\"flex items-center gap-1\">\n                            <Users className=\"w-3 h-3\" />\n                            <span>{credential.bindingCount} 个用户绑定</span>\n                          </div>\n                        </div>\n                      </div>\n                      <div className=\"flex items-center gap-2\">\n                        <span className={`text-xs px-2 py-1 rounded ${credential.enabled ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}`}>\n                          {credential.enabled ? '启用' : '禁用'}\n                        </span>\n                      </div>\n                    </div>\n                  ))}\n                </div>\n              )}\n            </div>\n          )}\n\n          {/* 使用说明 */}\n          <div className=\"space-y-4\">\n            <button\n              type=\"button\"\n              className=\"flex items-center gap-1 text-sm text-muted-foreground hover:text-foreground transition-colors\"\n              onClick={() => setShowExamples(!showExamples)}\n            >\n              {showExamples ? <ChevronUp className=\"w-4 h-4\" /> : <ChevronDown className=\"w-4 h-4\" />}\n              查看使用说明\n            </button>\n\n            {showExamples && (\n              <div className=\"rounded-lg border bg-card p-4 space-y-4\">\n                <div className=\"space-y-2\">\n                  <h4 className=\"font-medium\">邮箱凭证系统说明</h4>\n                  <div className=\"text-sm text-muted-foreground space-y-2\">\n                    <p>1. <strong>邮箱创建</strong>：系统创建邮箱时会自动生成对应的JWT凭证</p>\n                    <p>2. <strong>凭证绑定</strong>：用户可以通过邮箱凭证绑定邮箱到自己的账户</p>\n                    <p>3. <strong>凭证提取</strong>：管理员可以提取任何邮箱的凭证供用户使用</p>\n                    <p>4. <strong>绑定统计</strong>：显示每个邮箱绑定了多少个用户（不包括管理员）</p>\n                  </div>\n                </div>\n\n                <div className=\"space-y-2\">\n                  <h4 className=\"font-medium\">使用流程</h4>\n                  <div className=\"text-sm text-muted-foreground space-y-1\">\n                    <p><strong>用户：</strong> 获取邮箱凭证 → 点击\"绑定邮箱\" → 输入凭证 → 完成绑定</p>\n                    <p><strong>管理员：</strong> 查看所有邮箱凭证 → 提取特定邮箱凭证 → 提供给用户</p>\n                  </div>\n                </div>\n\n                <div className=\"space-y-2\">\n                  <h4 className=\"font-medium\">安全说明</h4>\n                  <div className=\"text-sm text-muted-foreground space-y-1\">\n                    <p>• 邮箱凭证是JWT格式，包含邮箱访问权限</p>\n                    <p>• 凭证永久有效，请妥善保管</p>\n                    <p>• 系统会记录凭证使用情况</p>\n                    <p>• 管理员可以查看绑定统计信息</p>\n                  </div>\n                </div>\n              </div>\n            )}\n          </div>\n        </div>\n      )}\n    </div>\n  )\n}\n", "\"use client\"\r\n\r\nimport { User } from \"next-auth\"\r\nimport Image from \"next/image\"\r\nimport { <PERSON><PERSON> } from \"@/components/ui/button\"\r\nimport { signOut } from \"next-auth/react\"\r\nimport { Github, Mail, Settings, Crown, Sword, User2, Gem } from \"lucide-react\"\r\nimport { useRouter } from \"next/navigation\"\r\nimport { WebhookConfig } from \"./webhook-config\"\r\nimport { PromotePanel } from \"./promote-panel\"\r\nimport { useRolePermission } from \"@/hooks/use-role-permission\"\r\nimport { PERMISSIONS } from \"@/lib/permissions\"\r\nimport { ConfigPanel } from \"./config-panel\"\r\nimport { ApiKeyPanel } from \"./api-key-panel\"\r\nimport { EmailCredentialsPanel } from \"./email-credentials-panel\"\r\n\r\ninterface ProfileCardProps {\r\n  user: User\r\n}\r\n\r\nconst roleConfigs = {\r\n  emperor: { name: '皇帝', icon: Crown },\r\n  duke: { name: '公爵', icon: Gem },\r\n  knight: { name: '骑士', icon: Sword },\r\n  civilian: { name: '平民', icon: User2 },\r\n} as const\r\n\r\nexport function ProfileCard({ user }: ProfileCardProps) {\r\n  const router = useRouter()\r\n  const { checkPermission } = useRolePermission()\r\n  const canManageWebhook = checkPermission(PERMISSIONS.MANAGE_WEBHOOK)\r\n  const canPromote = checkPermission(PERMISSIONS.PROMOTE_USER)\r\n  const canManageConfig = checkPermission(PERMISSIONS.MANAGE_CONFIG)\r\n  const canManageEmailCredentials = checkPermission(PERMISSIONS.MANAGE_EMAIL_CREDENTIALS)\r\n\r\n  return (\r\n    <div className=\"max-w-2xl mx-auto space-y-6\">\r\n      <div className=\"bg-background rounded-lg border-2 border-primary/20 p-6\">\r\n        <div className=\"flex items-center gap-6\">\r\n          <div className=\"relative\">\r\n            {user.image && (\r\n              <Image\r\n                src={user.image}\r\n                alt={user.name || \"用户头像\"}\r\n                width={80}\r\n                height={80}\r\n                className=\"rounded-full ring-2 ring-primary/20\"\r\n              />\r\n            )}\r\n          </div>\r\n          <div className=\"flex-1 min-w-0\">\r\n            <div className=\"flex items-center gap-2\">\r\n              <h2 className=\"text-xl font-bold truncate\">{user.name}</h2>\r\n              {\r\n                user.email && (\r\n                  // 先简单实现，后续再完善\r\n                  <div className=\"flex items-center gap-1 text-xs text-primary bg-primary/10 px-2 py-0.5 rounded-full flex-shrink-0\">\r\n                    <Github className=\"w-3 h-3\" />\r\n                    已关联\r\n                  </div>\r\n                )\r\n              }\r\n            </div>\r\n            <p className=\"text-sm text-muted-foreground truncate mt-1\">\r\n              {\r\n                user.email ? user.email : `用户名: ${user.username}`\r\n              }\r\n            </p>\r\n            {user.roles && (\r\n              <div className=\"flex gap-2 mt-2\">\r\n                {user.roles.map(({ name }) => {\r\n                  const roleConfig = roleConfigs[name as keyof typeof roleConfigs]\r\n                  const Icon = roleConfig.icon\r\n                  return (\r\n                    <div \r\n                      key={name}\r\n                      className=\"flex items-center gap-1 text-xs bg-primary/10 text-primary px-2 py-0.5 rounded\"\r\n                      title={roleConfig.name}\r\n                    >\r\n                      <Icon className=\"w-3 h-3\" />\r\n                      {roleConfig.name}\r\n                    </div>\r\n                  )\r\n                })}\r\n              </div>\r\n            )}\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {canManageWebhook && (\r\n        <div className=\"bg-background rounded-lg border-2 border-primary/20 p-6\">\r\n          <div className=\"flex items-center gap-2 mb-6\">\r\n            <Settings className=\"w-5 h-5 text-primary\" />\r\n            <h2 className=\"text-lg font-semibold\">Webhook 配置</h2>\r\n          </div>\r\n          <WebhookConfig />\r\n        </div>\r\n      )}\r\n\r\n      {canManageConfig && <ConfigPanel />}\r\n      {canPromote && <PromotePanel />}\r\n      {canManageWebhook && <ApiKeyPanel />}\r\n      {canManageEmailCredentials && <EmailCredentialsPanel />}\r\n\r\n      <div className=\"flex flex-col sm:flex-row gap-4 px-1\">\r\n        <Button \r\n          onClick={() => router.push(\"/moe\")}\r\n          className=\"gap-2 flex-1\"\r\n        >\r\n          <Mail className=\"w-4 h-4\" />\r\n          返回邮箱\r\n        </Button>\r\n        <Button \r\n          variant=\"outline\" \r\n          onClick={() => signOut({ callbackUrl: \"/\" })}\r\n          className=\"flex-1\"\r\n        >\r\n          退出登录\r\n        </Button>\r\n      </div>\r\n    </div>\r\n  )\r\n} ", "import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name UserRound\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjgiIHI9IjUiIC8+CiAgPHBhdGggZD0iTTIwIDIxYTggOCAwIDAgMC0xNiAwIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/user-round\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst UserRound = createLucideIcon('UserRound', [\n  ['circle', { cx: '12', cy: '8', r: '5', key: '1hypcn' }],\n  ['path', { d: 'M20 21a8 8 0 0 0-16 0', key: 'rfgkzh' }],\n]);\n\nexport default UserRound;\n", "import { Header } from \"@/components/layout/header\"\r\nimport { ProfileCard } from \"@/components/profile/profile-card\"\r\nimport { auth } from \"@/lib/auth\"\r\nimport { redirect } from \"next/navigation\"\r\n\r\nexport const runtime = \"edge\"\r\n\r\nexport default async function ProfilePage() {\r\n  const session = await auth()\r\n  \r\n  if (!session?.user) {\r\n    redirect(\"/\")\r\n  }\r\n\r\n  return (\r\n    <div className=\"min-h-screen bg-gradient-to-b from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800\">\r\n      <div className=\"container mx-auto px-4 lg:px-8 max-w-[1600px]\">\r\n        <Header />\r\n        <main className=\"pt-20 pb-5\">\r\n          <ProfileCard user={session.user} />\r\n        </main>\r\n      </div>\r\n    </div>\r\n  )\r\n} "], "names": ["Switch", "React", "className", "props", "ref", "SwitchPrimitives", "cn", "displayName", "WebhookConfig", "enabled", "setEnabled", "useState", "url", "setUrl", "loading", "setLoading", "testing", "setTesting", "showDocs", "setShowDocs", "initialLoading", "setInitialLoading", "toast", "useToast", "div", "Loader2", "p", "handleSubmit", "e", "preventDefault", "res", "fetch", "method", "headers", "body", "JSON", "stringify", "ok", "title", "description", "_error", "variant", "handleTest", "form", "onSubmit", "Label", "checked", "htmlFor", "Input", "id", "placeholder", "value", "onChange", "target", "type", "required", "<PERSON><PERSON>", "disabled", "TooltipProvider", "<PERSON><PERSON><PERSON>", "TooltipTrigger", "<PERSON><PERSON><PERSON><PERSON>", "onClick", "Send", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "button", "ChevronUp", "ChevronDown", "pre", "window", "location", "host", "roleIcons", "ROLES", "DUKE", "Gem", "KNIGHT", "Sword", "CIVILIAN", "User2", "roleNames", "PromotePanel", "searchText", "setSearchText", "targetRole", "setTargetRole", "handleAction", "data", "json", "error", "user", "role", "promoteRes", "userId", "<PERSON><PERSON><PERSON>", "username", "email", "Error", "message", "Icon", "h2", "Select", "onValueChange", "SelectTrigger", "SelectValue", "SelectContent", "SelectItem", "trim", "useRolePermission", "session", "useSession", "roles", "checkPermission", "hasPermission", "map", "r", "name", "permission", "hasRole", "some", "ConfigPanel", "defaultRole", "setDefaultRole", "emailDomains", "setEmailDomains", "adminContact", "setAdminContact", "maxEmails", "setMaxEmails", "EMAIL_CONFIG", "MAX_ACTIVE_EMAILS", "toString", "handleSave", "Settings", "span", "min", "max", "ApiKeyPanel", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "set<PERSON><PERSON><PERSON><PERSON><PERSON>", "createDialogOpen", "setCreateDialogOpen", "newKeyName", "setNewKeyName", "new<PERSON>ey", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "copyToClipboard", "useCopy", "showExamples", "setShowExamples", "isLoading", "setIsLoading", "canManageApiKey", "PERMISSIONS", "MANAGE_API_KEY", "fetchApiKeys", "console", "config", "useConfig", "createApiKey", "key", "to<PERSON><PERSON><PERSON><PERSON><PERSON>", "keys", "deleteApiKey", "filter", "Key", "Dialog", "open", "onOpenChange", "DialogTrigger", "Plus", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogHeader", "DialogTitle", "DialogDescription", "readOnly", "size", "Copy", "<PERSON><PERSON><PERSON><PERSON>er", "DialogClose", "handleDialogClose", "length", "h3", "Date", "createdAt", "toLocaleString", "onCheckedChange", "Trash2", "protocol", "ul", "li", "EmailCredentialsPanel", "credentials", "setCredentials", "userBindings", "setUserBindings", "bindDialogOpen", "setBindDialogOpen", "extractDialogOpen", "setExtractDialogOpen", "newBinding", "setNewBinding", "jwt", "extractEmail", "setExtractEmail", "extractedCredential", "setExtractedCredential", "extractedCredentials", "setExtractedCredentials", "selectedEmails", "setSelectedEmails", "extractMode", "setExtractMode", "showJWT", "setShowJWT", "refreshing", "setRefreshing", "canManageEmailCredentials", "MANAGE_WEBHOOK", "fetchCredentials", "fetchUserBindings", "bindings", "fetchData", "Promise", "all", "resolve", "handleRefresh", "bindEmail", "handleBindDialogClose", "extractCredential", "endpoint", "requestBody", "emailAddress", "emailAddresses", "credential", "unbindEmail", "binding", "text", "navigator", "clipboard", "writeText", "Mail", "RefreshCw", "prev", "Eye", "Eye<PERSON>ff", "bindingCount", "handleExtractDialogClose", "lastUsedAt", "Users", "h4", "strong", "roleConfigs", "emperor", "icon", "Crown", "duke", "civilian", "ProfileCard", "router", "useRouter", "canManageWebhook", "canPromote", "PROMOTE_USER", "canManageConfig", "MANAGE_CONFIG", "MANAGE_EMAIL_CREDENTIALS", "image", "Image", "src", "alt", "width", "height", "<PERSON><PERSON><PERSON>", "roleConfig", "push", "signOut", "callbackUrl", "runtime", "ProfilePage", "auth", "redirect", "Header", "main"], "sourceRoot": "", "ignoreList": []}