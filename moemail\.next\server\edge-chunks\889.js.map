{"version": 3, "file": "edge-chunks/889.js", "mappings": "gNAcO,SAASA,EAAW,MAAEC,EAAO,SAAS,CAAmB,EAC9D,IAAMC,EAASC,CAAAA,EAAAA,EAAAA,EAAAA,CAASA,GAClB,CAAEC,KAAMC,CAAO,QAAEC,CAAM,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAUA,SACjB,IAEvBC,SAAS,CACJ,UAACC,MAAAA,CAAIC,UAAU,QAGnBL,GAASM,KAUZ,CAVkB,EAUlB,QAACF,MAAAA,CAAIC,UAAU,oCACb,WAACE,EAAAA,CAAIA,CAAAA,CACHC,KAAK,WACLH,UAAU,wEAETL,EAAQM,IAAI,CAACG,KAAK,EACjB,UAACC,EAAAA,CAAKA,CAAAA,CACJC,IAAKX,EAAQM,IAAI,CAACG,KAAK,CACvBG,IAAKZ,EAAQM,IAAI,CAACO,IAAI,EAAI,OAC1BC,MAAO,GACPC,OAAQ,GACRV,UAAU,iBAGd,UAACW,OAAAA,CAAKX,UAAU,mBAAWL,EAAQM,IAAI,CAACO,IAAI,MAE9C,UAACI,EAAAA,CAAMA,CAAAA,CAACC,QAAS,IAAMC,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,CAAEC,YAAa,GAAI,GAAIC,QAAQ,UAAUhB,UAAWiB,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,gBAA0B,OAAT1B,EAAgB,OAAS,IAAKA,KAAMA,WAAM,UAxBjJ,WAACqB,EAAAA,CAAMA,CAAAA,CAACC,QAAS,IAAMrB,EAAO0B,IAAI,CAAC,UAAWlB,UAAWiB,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,QAAkB,OAAT1B,EAAgB,OAAS,IAAKA,KAAMA,YACvG,UAAC4B,EAAAA,CAAKA,CAAAA,CAACnB,UAAoB,OAATT,EAAgB,UAAY,YAAa,UA4BnE,uGChDO,SAAS6B,IACd,GAAM,OAAEC,CAAK,UAAEC,CAAQ,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,CAAAA,CAAQA,GAEpC,MACE,WAACX,EAAAA,CAAMA,CAAAA,CACLI,QAAQ,QACRzB,KAAK,OACLsB,QAAS,IAAMS,EAAmB,UAAVD,EAAoB,OAAS,SACrDrB,UAAU,yBAEV,UAACwB,EAAAA,CAAGA,CAAAA,CAACxB,UAAU,2EACf,UAACyB,EAAAA,CAAIA,CAAAA,CAACzB,UAAU,mFAChB,UAACW,OAAAA,CAAKX,UAAU,mBAAU,WAGhC,+JCdA,IAAM0B,EAASC,EAAAA,EAAoB,CAC7BC,EAAgBD,EAAAA,EAAuB,CACvCE,EAAeF,EAAAA,EAAsB,CACrCG,EAAcH,EAAAA,EAAqB,CAEnCI,EAAgBC,EAAAA,UAAgB,CAGpC,CAAC,WAAEhC,CAAS,CAAE,GAAGiC,EAAO,CAAEC,IAC1B,UAACP,EAAAA,EAAuB,EACtBO,IAAKA,EACLlC,UAAWiB,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CACX,yJACAjB,GAED,GAAGiC,CAAK,IAGbF,EAAcI,WAAW,CAAGR,EAAAA,EAAuB,CAACQ,WAAW,CAE/D,IAAMC,EAAgBJ,EAAAA,UAAgB,CAGpC,CAAC,WAAEhC,CAAS,UAAEqC,CAAQ,CAAE,GAAGJ,EAAO,CAAEC,IACpC,WAACL,EAAAA,WACC,UAACE,EAAAA,CAAAA,GACD,WAACJ,EAAAA,EAAuB,EACtBO,IAAKA,EACLlC,UAAWiB,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CACX,8fACAjB,GAED,GAAGiC,CAAK,WAERI,EACD,WAACV,EAAAA,EAAqB,EAAC3B,UAAU,0RAC/B,UAACsC,EAAAA,CAACA,CAAAA,CAACtC,UAAU,YACb,UAACW,OAAAA,CAAKX,UAAU,mBAAU,iBAKlCoC,EAAcD,WAAW,CAAGR,EAAAA,EAAuB,CAACQ,WAAW,CAE/D,IAAMI,EAAe,CAAC,WACpBvC,CAAS,CACT,GAAGiC,EACkC,GACrC,UAAClC,MAAAA,CACCC,UAAWiB,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CACX,qDACAjB,GAED,GAAGiC,CAAK,GAGbM,EAAaJ,WAAW,CAAG,eAE3B,IAAMK,EAAe,CAAC,WACpBxC,CAAS,CACT,GAAGiC,EACkC,GACrC,UAAClC,MAAAA,CACCC,UAAWiB,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CACX,gEACAjB,GAED,GAAGiC,CAAK,EAGbO,GAAaL,WAAW,CAAG,eAE3B,IAAMM,EAAcT,EAAAA,UAAgB,CAGlC,CAAC,WAAEhC,CAAS,CAAE,GAAGiC,EAAO,CAAEC,IAC1B,UAACP,EAAAA,EAAqB,EACpBO,IAAKA,EACLlC,UAAWiB,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CACX,oDACAjB,GAED,GAAGiC,CAAK,IAGbQ,EAAYN,WAAW,CAAGR,EAAAA,EAAqB,CAACQ,WAAW,CAE3D,IAAMO,EAAoBV,EAAAA,UAAgB,CAGxC,CAAC,CAAEhC,WAAS,CAAE,GAAGiC,EAAO,CAAEC,IAC1B,UAACP,EAAAA,EAA2B,EAC1BO,IAAKA,EACLlC,UAAWiB,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,gCAAiCjB,GAC9C,GAAGiC,CAAK,IAGbS,EAAkBP,WAAW,CAAGR,EAAAA,EAA2B,CAACQ,WAAW,wEChGvE,IAAMQ,EAAQX,EAAAA,UAAgB,CAC5B,CAAC,CAAEhC,WAAS,CAAE4C,MAAI,CAAE,GAAGX,EAAO,CAAEC,IAE5B,UAACW,QAAAA,CACCD,KAAMA,EACN5C,UAAWiB,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CACX,wUACAjB,GAEFkC,IAAKA,EACJ,GAAGD,CAAK,IAKjBU,EAAMR,WAAW,CAAG,0FCjBpB,IAAMW,EAAQd,EAAAA,UAAgB,CAG5B,CAAC,CAAEhC,WAAS,CAAE,GAAGiC,EAAO,CAAEC,IAC1B,UAACa,EAAAA,CAAmB,EAClBb,IAAKA,EACLlC,UAAWiB,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CACX,6FACAjB,GAED,GAAGiC,CAAK,IAGba,EAAMX,WAAW,CAAGY,EAAAA,CAAmB,CAACZ,WAAW,gECf5C,SAASa,IACd,MACE,WAAC9C,EAAAA,CAAIA,CAAAA,CACHC,KAAK,IACLH,UAAU,wEAEV,UAACD,MAAAA,CAAIC,UAAU,4BACb,UAACD,MAAAA,CAAIC,UAAU,gEACb,WAACiD,MAAAA,CACCxC,MAAM,KACNC,OAAO,KACPwC,QAAQ,YACRC,KAAK,OACLC,MAAM,6BACNpD,UAAU,yBAGV,UAACqD,OAAAA,CACCC,EAAE,kBACFtD,UAAU,oBAIZ,UAACqD,OAAAA,CACCC,EAAE,iCACFtD,UAAU,iBAIZ,UAACqD,OAAAA,CACCC,EAAE,uEACFtD,UAAU,iBAIZ,UAACqD,OAAAA,CACCC,EAAE,iBACFtD,UAAU,0BACVmD,KAAK,SAIP,UAACE,OAAAA,CACCC,EAAE,mCACFtD,UAAU,oBAIZ,UAACqD,OAAAA,CACCC,EAAE,mCACFtD,UAAU,2BAKlB,UAACW,OAAAA,CAAKX,UAAU,8GAAqG,cAK3H,+ICzDA,IAAMuD,EAASC,EAAAA,EAAoB,CAE7BC,EAAgBzB,EAAAA,UAAgB,CAGpC,CAAC,CAAEhC,WAAS,UAAEqC,CAAQ,CAAE,GAAGJ,EAAO,CAAEC,IACpC,WAACsB,EAAAA,EAAuB,EACtBtB,IAAKA,EACLlC,UAAWiB,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CACX,2RACAjB,GAED,GAAGiC,CAAK,WAERI,EACD,UAACmB,EAAAA,EAAoB,EAACE,OAAO,aAC3B,UAACC,EAAAA,CAAWA,CAAAA,CAAC3D,UAAU,6BAI7ByD,EAActB,WAAW,CAAGqB,EAAAA,EAAuB,CAACrB,WAAW,CAE/D,IAAMyB,EAAgB5B,EAAAA,UAAgB,CAGpC,CAAC,CAAEhC,WAAS,CAAEqC,UAAQ,UAAEwB,EAAW,QAAQ,CAAE,GAAG5B,EAAO,CAAEC,IACzD,UAACsB,EAAAA,EAAsB,WACrB,UAACA,EAAAA,EAAuB,EACtBtB,IAAKA,EACLlC,UAAWiB,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CACX,6bACa,WAAb4C,GACE,kIACF7D,GAEF6D,SAAUA,EACT,GAAG5B,CAAK,UAET,UAACuB,EAAAA,EAAwB,EACvBxD,UAAWiB,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CACX,MACA4C,cACE,oGAGHxB,SAKTuB,EAAczB,WAAW,CAAGqB,EAAAA,EAAuB,CAACrB,WAAW,CAE/D,IAAM2B,EAAa9B,EAAAA,UAAgB,CAGjC,CAAC,WAAEhC,CAAS,UAAEqC,CAAQ,CAAE,GAAGJ,EAAO,CAAEC,IACpC,WAACsB,EAAAA,EAAoB,EACnBtB,IAAKA,EACLlC,UAAWiB,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CACX,4NACAjB,GAED,GAAGiC,CAAK,WAET,UAACtB,OAAAA,CAAKX,UAAU,wEACd,UAACwD,EAAAA,EAA6B,WAC5B,UAACO,EAAAA,CAAKA,CAAAA,CAAC/D,UAAU,gBAIrB,UAACwD,EAAAA,EAAwB,WAAEnB,QAG/ByB,EAAW3B,WAAW,CAAGqB,EAAAA,EAAoB,CAACrB,WAAW,CAEzD,IAAM6B,EAAcR,EAAAA,EAAqB,mCClFlC,IAAMS,EAAe,CAC1BC,kBAAmB,GACnBC,cAAe,GACjB,EAAU,gFGmBV,IAAMC,EAAiBC,CAAAA,EAAAA,EAAAA,CAAAA,CAAMA,CAAc,GAAU,EACnDC,CADmD,MAC3C,KACRxE,SAAS,EACTyE,MAAO,KACPC,MAAO,UACL,GAAI,CACFC,EAAI,CAAE3E,SAAS,EAAMyE,MAAO,IAAK,GACjC,IAAMG,EAAM,MAAMF,MAAM,eACxB,GAAI,CAACE,EAAIC,EAAE,CAAE,MAAUC,MAAM,UAC7B,IAAMlF,EAAO,MAAMgF,EAAIG,IAAI,GAC3BJ,EAAI,CACFH,OAAQ,CACNQ,YAAapF,EAAKoF,WAAW,EAAIC,EAAAA,EAAKA,CAACC,QAAQ,CAC/CC,aAAcvF,EAAKuF,YAAY,CAC/BC,kBAAmBxF,EAAKuF,YAAY,CAACE,KAAK,CAAC,KAC3CC,aAAc1F,EAAK0F,YAAY,EAAI,GACnCC,UAAWC,OAAO5F,EAAK2F,SAAS,GAAKpB,EAAAA,CAAYA,CAACC,iBAAiB,EAErEpE,SAAS,CACX,EACF,CAAE,MAAOyE,EAAO,CACdE,EAAI,CACFF,MAAOA,aAAiBK,MAAQL,EAAMgB,OAAO,CAAG,SAChDzF,SAAS,CACX,EACF,CACF,EACF,GAEO,SAAS0F,IASd,OARcpB,GAShB,6DCnDO,SAASqB,EAAQC,EAA0B,CAAC,CAAC,EAClD,GAAM,CAAEC,OAAK,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAQA,GACpB,gBACJC,EAAiB,SAAS,cAC1BC,EAAe,MAAM,CACtB,CAAGJ,EAoBJ,MAAO,CACLK,gBAnBsBC,CAAAA,EAAAA,EAAAA,WAAAA,CAAWA,CAAC,MAAOC,IACzC,GAAI,CAMF,OALA,MAAMC,UAAUC,SAAS,CAACC,SAAS,CAACH,GACpCN,EAAM,CACJU,MAAO,KACPC,YAAaT,CACf,IACO,CACT,CAAE,KAAM,CAMN,OALAF,EAAM,CACJU,MAAO,KACPC,YAAaR,EACb9E,QAAS,aACX,IACO,CACT,CACF,EAAG,CAAC6E,EAAgBC,EAAcH,EAAM,CAIxC,CACF,uDCtCO,IAAMZ,EAAQ,CACnBwB,QAAS,UACTC,KAAM,OACNC,OAAQ,SACRzB,SAAU,UACZ,EAAW,EAIgB,CACzB0B,aAAc,eACdC,eAAgB,iBAChBC,aAAc,eACdC,cAAe,gBACfC,eAAgB,iBAChBC,yBAA0B,0BAC5B,EAAW,EAIiD,CAC1D,CAAChC,EAAMwB,OAAO,CAAC,CAAES,OAAOC,MAAM,CAACC,GAC/B,CAACnC,EAAMyB,IAAI,CAAC,CAAE,CACZU,EAAYR,YAAY,CACxBQ,EAAYP,cAAc,CAC1BO,EAAYJ,cAAc,CAC1BI,EAAYH,wBAAwB,CACrC,CACD,CAAChC,EAAM0B,MAAM,CAAC,CAAE,CACdS,EAAYR,YAAY,CACxBQ,EAAYP,cAAc,CAC1BO,EAAYH,wBAAwB,CACrC,CACD,CAAChC,EAAMC,QAAQ,CAAC,CAAE,CAChBkC,EAAYR,YAAY,CACxBQ,EAAYH,wBAAwB,CACrC,EACQ,SAEKI,EAAcC,CAAiB,CAAEC,CAAsB,EACrE,OAAOD,EAAUE,IAAI,CAACC,GAAQC,CAAgB,CAACD,EAAK,EAAEE,SAASJ,GACjE,4eCrCO,SAASK,IACd,MACE,UAACC,SAAAA,CAAO3H,UAAU,2FAChB,UAACD,MAAAA,CAAIC,UAAU,yCACb,WAACD,MAAAA,CAAIC,UAAU,qDACb,UAACgD,EAAAA,IAAIA,CAAAA,CAAAA,GACL,WAACjD,MAAAA,CAAIC,UAAU,oCACb,UAACoB,EAAAA,WAAWA,CAAAA,CAAAA,GACZ,UAAC9B,EAAAA,UAAUA,CAAAA,CAAAA,YAMvB", "sources": ["webpack://_N_E/./app/components/auth/sign-button.tsx", "webpack://_N_E/./app/components/theme/theme-toggle.tsx", "webpack://_N_E/./app/components/ui/dialog.tsx", "webpack://_N_E/./app/components/ui/input.tsx", "webpack://_N_E/./app/components/ui/label.tsx", "webpack://_N_E/./app/components/ui/logo.tsx", "webpack://_N_E/./app/components/ui/select.tsx", "webpack://_N_E/./app/config/email.ts", "webpack://_N_E/./app/config/webhook.ts", "webpack://_N_E/./app/config/index.ts", "webpack://_N_E/./app/hooks/use-config.ts", "webpack://_N_E/./app/hooks/use-copy.ts", "webpack://_N_E/./app/lib/permissions.ts", "webpack://_N_E/./app/components/layout/header.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport { But<PERSON> } from \"@/components/ui/button\"\r\nimport Image from \"next/image\"\r\nimport { signOut, useSession } from \"next-auth/react\"\r\nimport { LogIn } from \"lucide-react\"\r\nimport { useRouter } from 'next/navigation'\r\nimport Link from \"next/link\"\r\nimport { cn } from \"@/lib/utils\"\r\n\r\ninterface SignButtonProps {\r\n  size?: \"default\" | \"lg\"\r\n}\r\n\r\nexport function SignButton({ size = \"default\" }: SignButtonProps) {\r\n  const router = useRouter()\r\n  const { data: session, status } = useSession()\r\n  const loading = status === \"loading\"\r\n\r\n  if (loading) {\r\n    return <div className=\"h-9\" />\r\n  }\r\n\r\n  if (!session?.user) {\r\n    return (\r\n      <Button onClick={() => router.push('/login')} className={cn(\"gap-2\", size === \"lg\" ? \"px-8\" : \"\")} size={size}>\r\n        <LogIn className={size === \"lg\" ? \"w-5 h-5\" : \"w-4 h-4\"} />\r\n        登录/注册\r\n      </Button>\r\n    )\r\n  }\r\n\r\n  return (\r\n    <div className=\"flex items-center gap-4\">\r\n      <Link \r\n        href=\"/profile\"\r\n        className=\"flex items-center gap-2 hover:opacity-80 transition-opacity\"\r\n      >\r\n        {session.user.image && (\r\n          <Image\r\n            src={session.user.image}\r\n            alt={session.user.name || \"用户头像\"}\r\n            width={24}\r\n            height={24}\r\n            className=\"rounded-full\"\r\n          />\r\n        )}\r\n        <span className=\"text-sm\">{session.user.name}</span>\r\n      </Link>\r\n      <Button onClick={() => signOut({ callbackUrl: \"/\" })} variant=\"outline\" className={cn(\"flex-shrink-0\", size === \"lg\" ? \"px-8\" : \"\")} size={size}>\r\n        登出\r\n      </Button>\r\n    </div>\r\n  )\r\n} ", "\"use client\"\r\n\r\nimport { Moon, Sun } from \"lucide-react\"\r\nimport { useTheme } from \"next-themes\"\r\nimport { But<PERSON> } from \"@/components/ui/button\"\r\n\r\nexport function ThemeToggle() {\r\n  const { theme, setTheme } = useTheme()\r\n\r\n  return (\r\n    <Button\r\n      variant=\"ghost\"\r\n      size=\"icon\"\r\n      onClick={() => setTheme(theme === \"light\" ? \"dark\" : \"light\")}\r\n      className=\"rounded-full\"\r\n    >\r\n      <Sun className=\"h-5 w-5 rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0\" />\r\n      <Moon className=\"absolute h-5 w-5 rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100\" />\r\n      <span className=\"sr-only\">切换主题</span>\r\n    </Button>\r\n  )\r\n} ", "\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\"\r\nimport { X } from \"lucide-react\"\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Dialog = DialogPrimitive.Root\r\nconst DialogTrigger = DialogPrimitive.Trigger\r\nconst DialogPortal = DialogPrimitive.Portal\r\nconst DialogClose = DialogPrimitive.Close\r\n\r\nconst DialogOverlay = React.forwardRef<\r\n  React.ElementRef<typeof DialogPrimitive.Overlay>,\r\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Overlay>\r\n>(({ className, ...props }, ref) => (\r\n  <DialogPrimitive.Overlay\r\n    ref={ref}\r\n    className={cn(\r\n      \"fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nDialogOverlay.displayName = DialogPrimitive.Overlay.displayName\r\n\r\nconst DialogContent = React.forwardRef<\r\n  React.ElementRef<typeof DialogPrimitive.Content>,\r\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Content>\r\n>(({ className, children, ...props }, ref) => (\r\n  <DialogPortal>\r\n    <DialogOverlay />\r\n    <DialogPrimitive.Content\r\n      ref={ref}\r\n      className={cn(\r\n        \"fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      {children}\r\n      <DialogPrimitive.Close className=\"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground\">\r\n        <X className=\"h-4 w-4\" />\r\n        <span className=\"sr-only\">关闭</span>\r\n      </DialogPrimitive.Close>\r\n    </DialogPrimitive.Content>\r\n  </DialogPortal>\r\n))\r\nDialogContent.displayName = DialogPrimitive.Content.displayName\r\n\r\nconst DialogHeader = ({\r\n  className,\r\n  ...props\r\n}: React.HTMLAttributes<HTMLDivElement>) => (\r\n  <div\r\n    className={cn(\r\n      \"flex flex-col space-y-1.5 text-center sm:text-left\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n)\r\nDialogHeader.displayName = \"DialogHeader\"\r\n\r\nconst DialogFooter = ({\r\n  className,\r\n  ...props\r\n}: React.HTMLAttributes<HTMLDivElement>) => (\r\n  <div\r\n    className={cn(\r\n      \"flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n)\r\nDialogFooter.displayName = \"DialogFooter\"\r\n\r\nconst DialogTitle = React.forwardRef<\r\n  React.ElementRef<typeof DialogPrimitive.Title>,\r\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Title>\r\n>(({ className, ...props }, ref) => (\r\n  <DialogPrimitive.Title\r\n    ref={ref}\r\n    className={cn(\r\n      \"text-lg font-semibold leading-none tracking-tight\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nDialogTitle.displayName = DialogPrimitive.Title.displayName\r\n\r\nconst DialogDescription = React.forwardRef<\r\n  React.ElementRef<typeof DialogPrimitive.Description>,\r\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Description>\r\n>(({ className, ...props }, ref) => (\r\n  <DialogPrimitive.Description\r\n    ref={ref}\r\n    className={cn(\"text-sm text-muted-foreground\", className)}\r\n    {...props}\r\n  />\r\n))\r\nDialogDescription.displayName = DialogPrimitive.Description.displayName\r\n\r\nexport {\r\n  Dialog,\r\n  DialogTrigger,\r\n  DialogContent,\r\n  DialogHeader,\r\n  DialogFooter,\r\n  DialogTitle,\r\n  DialogClose,\r\n  DialogDescription,\r\n} ", "import * as React from \"react\"\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nexport interface InputProps\r\n  extends React.InputHTMLAttributes<HTMLInputElement> {\r\n  className?: string\r\n}\r\n\r\nconst Input = React.forwardRef<HTMLInputElement, InputProps>(\r\n  ({ className, type, ...props }, ref) => {\r\n    return (\r\n      <input\r\n        type={type}\r\n        className={cn(\r\n          \"flex h-9 w-full rounded-md border border-input bg-transparent px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50\",\r\n          className\r\n        )}\r\n        ref={ref}\r\n        {...props}\r\n      />\r\n    )\r\n  }\r\n)\r\nInput.displayName = \"Input\"\r\n\r\nexport { Input } ", "\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Label = React.forwardRef<\r\n  React.ElementRef<typeof LabelPrimitive.Root>,\r\n  React.ComponentPropsWithoutRef<typeof LabelPrimitive.Root>\r\n>(({ className, ...props }, ref) => (\r\n  <LabelPrimitive.Root\r\n    ref={ref}\r\n    className={cn(\r\n      \"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nLabel.displayName = LabelPrimitive.Root.displayName\r\n\r\nexport { Label } ", "\"use client\"\r\n\r\nimport Link from \"next/link\"\r\n\r\nexport function Logo() {\r\n  return (\r\n    <Link \r\n      href=\"/\"\r\n      className=\"flex items-center gap-2 hover:opacity-80 transition-opacity\"\r\n    >\r\n      <div className=\"relative w-8 h-8\">\r\n        <div className=\"absolute inset-0 grid grid-cols-8 grid-rows-8 gap-px\">\r\n          <svg\r\n            width=\"32\"\r\n            height=\"32\"\r\n            viewBox=\"0 0 32 32\"\r\n            fill=\"none\"\r\n            xmlns=\"http://www.w3.org/2000/svg\"\r\n            className=\"text-primary\"\r\n          >\r\n            {/* 信封主体 */}\r\n            <path\r\n              d=\"M4 8h24v16H4V8z\"\r\n              className=\"fill-primary/20\"\r\n            />\r\n            \r\n            {/* 信封边框 */}\r\n            <path\r\n              d=\"M4 8h24v2H4V8zM4 22h24v2H4v-2z\"\r\n              className=\"fill-primary\"\r\n            />\r\n            \r\n            {/* @ 符号 */}\r\n            <path\r\n              d=\"M14 12h4v4h-4v-4zM12 14h2v4h-2v-4zM18 14h2v4h-2v-4zM14 18h4v2h-4v-2z\"\r\n              className=\"fill-primary\"\r\n            />\r\n            \r\n            {/* 折线装饰 */}\r\n            <path\r\n              d=\"M4 8l12 8 12-8\"\r\n              className=\"stroke-primary stroke-2\"\r\n              fill=\"none\"\r\n            />\r\n            \r\n            {/* 装饰点 */}\r\n            <path\r\n              d=\"M8 18h2v2H8v-2zM22 18h2v2h-2v-2z\"\r\n              className=\"fill-primary/60\"\r\n            />\r\n            \r\n            {/* 底部装饰线 */}\r\n            <path\r\n              d=\"M8 14h2v2H8v-2zM22 14h2v2h-2v-2z\"\r\n              className=\"fill-primary/40\"\r\n            />\r\n          </svg>\r\n        </div>\r\n      </div>\r\n      <span className=\"font-bold tracking-wider bg-clip-text text-transparent bg-gradient-to-r from-primary to-purple-600\">\r\n        MoeMail\r\n      </span>\r\n    </Link>\r\n  )\r\n}", "\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\r\nimport { Check, ChevronDown } from \"lucide-react\"\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Select = SelectPrimitive.Root\r\n\r\nconst SelectTrigger = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.Trigger>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Trigger>\r\n>(({ className, children, ...props }, ref) => (\r\n  <SelectPrimitive.Trigger\r\n    ref={ref}\r\n    className={cn(\r\n      \"flex h-9 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\r\n      className\r\n    )}\r\n    {...props}\r\n  >\r\n    {children}\r\n    <SelectPrimitive.Icon asChild>\r\n      <ChevronDown className=\"h-4 w-4 opacity-50\" />\r\n    </SelectPrimitive.Icon>\r\n  </SelectPrimitive.Trigger>\r\n))\r\nSelectTrigger.displayName = SelectPrimitive.Trigger.displayName\r\n\r\nconst SelectContent = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.Content>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Content>\r\n>(({ className, children, position = \"popper\", ...props }, ref) => (\r\n  <SelectPrimitive.Portal>\r\n    <SelectPrimitive.Content\r\n      ref={ref}\r\n      className={cn(\r\n        \"relative z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\r\n        position === \"popper\" &&\r\n          \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\r\n        className\r\n      )}\r\n      position={position}\r\n      {...props}\r\n    >\r\n      <SelectPrimitive.Viewport\r\n        className={cn(\r\n          \"p-1\",\r\n          position === \"popper\" &&\r\n            \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]\"\r\n        )}\r\n      >\r\n        {children}\r\n      </SelectPrimitive.Viewport>\r\n    </SelectPrimitive.Content>\r\n  </SelectPrimitive.Portal>\r\n))\r\nSelectContent.displayName = SelectPrimitive.Content.displayName\r\n\r\nconst SelectItem = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.Item>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Item>\r\n>(({ className, children, ...props }, ref) => (\r\n  <SelectPrimitive.Item\r\n    ref={ref}\r\n    className={cn(\r\n      \"relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\r\n      className\r\n    )}\r\n    {...props}\r\n  >\r\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\r\n      <SelectPrimitive.ItemIndicator>\r\n        <Check className=\"h-4 w-4\" />\r\n      </SelectPrimitive.ItemIndicator>\r\n    </span>\r\n\r\n    <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\r\n  </SelectPrimitive.Item>\r\n))\r\nSelectItem.displayName = SelectPrimitive.Item.displayName\r\n\r\nconst SelectValue = SelectPrimitive.Value\r\n\r\nexport {\r\n  Select,\r\n  SelectTrigger,\r\n  SelectContent,\r\n  SelectItem,\r\n  SelectValue,\r\n} ", "export const EMAIL_CONFIG = {\r\n  MAX_ACTIVE_EMAILS: 30, // Maximum number of active emails\r\n  POLL_INTERVAL: 10_000, // Polling interval in milliseconds\r\n} as const\r\n\r\nexport type EmailConfig = typeof EMAIL_CONFIG ", "export const WEBHOOK_CONFIG = {\r\n  MAX_RETRIES: 3, // Maximum retry count\r\n  TIMEOUT: 10_000, // Timeout time (milliseconds)\r\n  RETRY_DELAY: 1000, // Retry delay (milliseconds)\r\n  EVENTS: {\r\n    NEW_MESSAGE: 'new_message',\r\n  }\r\n} as const\r\n\r\nexport type WebhookConfig = typeof WEBHOOK_CONFIG ", "export * from './email'\r\nexport * from './webhook'", "\"use client\"\r\n\r\nimport { create } from \"zustand\"\r\nimport { Role, ROLES } from \"@/lib/permissions\"\r\nimport { EMAIL_CONFIG } from \"@/config\"\r\nimport { useEffect } from \"react\"\r\n\r\ninterface Config {\r\n  defaultRole: Exclude<Role, typeof ROLES.EMPEROR>\r\n  emailDomains: string\r\n  emailDomainsArray: string[]\r\n  adminContact: string\r\n  maxEmails: number\r\n}\r\n\r\ninterface ConfigStore {\r\n  config: Config | null\r\n  loading: boolean\r\n  error: string | null\r\n  fetch: () => Promise<void>\r\n}\r\n\r\nconst useConfigStore = create<ConfigStore>((set) => ({\r\n  config: null,\r\n  loading: false,\r\n  error: null,\r\n  fetch: async () => {\r\n    try {\r\n      set({ loading: true, error: null })\r\n      const res = await fetch(\"/api/config\")\r\n      if (!res.ok) throw new Error(\"获取配置失败\")\r\n      const data = await res.json() as Config\r\n      set({\r\n        config: {\r\n          defaultRole: data.defaultRole || ROLES.CIVILIAN,\r\n          emailDomains: data.emailDomains,\r\n          emailDomainsArray: data.emailDomains.split(','),\r\n          adminContact: data.adminContact || \"\",\r\n          maxEmails: Number(data.maxEmails) || EMAIL_CONFIG.MAX_ACTIVE_EMAILS\r\n        },\r\n        loading: false\r\n      })\r\n    } catch (error) {\r\n      set({ \r\n        error: error instanceof Error ? error.message : \"获取配置失败\",\r\n        loading: false \r\n      })\r\n    }\r\n  }\r\n}))\r\n\r\nexport function useConfig() {\r\n  const store = useConfigStore()\r\n\r\n  useEffect(() => {\r\n    if (!store.config && !store.loading) {\r\n      store.fetch()\r\n    }\r\n  }, [store.config, store.loading])\r\n\r\n  return store\r\n} ", "\"use client\"\r\n\r\nimport { useCallback } from \"react\"\r\nimport { useToast } from \"@/components/ui/use-toast\"\r\n\r\ninterface UseCopyOptions {\r\n  successMessage?: string\r\n  errorMessage?: string\r\n}\r\n\r\nexport function useCopy(options: UseCopyOptions = {}) {\r\n  const { toast } = useToast()\r\n  const {\r\n    successMessage = \"已复制到剪贴板\",\r\n    errorMessage = \"复制失败\"\r\n  } = options\r\n\r\n  const copyToClipboard = useCallback(async (text: string) => {\r\n    try {\r\n      await navigator.clipboard.writeText(text)\r\n      toast({\r\n        title: \"成功\",\r\n        description: successMessage\r\n      })\r\n      return true\r\n    } catch {\r\n      toast({\r\n        title: \"错误\",\r\n        description: errorMessage,\r\n        variant: \"destructive\"\r\n      })\r\n      return false\r\n    }\r\n  }, [successMessage, errorMessage, toast])\r\n\r\n  return {\r\n    copyToClipboard\r\n  }\r\n}", "export const ROLES = {\r\n  EMPEROR: 'emperor',\r\n  <PERSON>U<PERSON>: 'duke',\r\n  <PERSON><PERSON><PERSON><PERSON>: 'knight',\r\n  CIVILIAN: 'civilian',\r\n} as const;\r\n\r\nexport type Role = typeof ROLES[keyof typeof ROLES];\r\n\r\nexport const PERMISSIONS = {\r\n  MANAGE_EMAIL: 'manage_email',\r\n  <PERSON><PERSON><PERSON>_WEBHOOK: 'manage_webhook',\r\n  PROMOTE_USER: 'promote_user',\r\n  <PERSON><PERSON><PERSON>_CONFIG: 'manage_config',\r\n  MANAGE_API_KEY: 'manage_api_key',\r\n  MANAGE_EMAIL_CREDENTIALS: 'manage_email_credentials',\r\n} as const;\r\n\r\nexport type Permission = typeof PERMISSIONS[keyof typeof PERMISSIONS];\r\n\r\nexport const ROLE_PERMISSIONS: Record<Role, Permission[]> = {\r\n  [ROLES.EMPEROR]: Object.values(PERMISSIONS),\r\n  [ROLES.DUKE]: [\r\n    PERMISSIONS.MANAGE_EMAIL,\r\n    PERMISSIONS.MANAGE_WEBHOOK,\r\n    PERMISSIONS.MANAGE_API_KEY,\r\n    PERMISSIONS.MANAGE_EMAIL_CREDENTIALS,\r\n  ],\r\n  [ROLES.KNIGHT]: [\r\n    PERMISSIONS.MANAGE_EMAIL,\r\n    PERMISSIONS.MANAGE_WEBHOOK,\r\n    PERMISSIONS.MANAGE_EMAIL_CREDENTIALS,\r\n  ],\r\n  [ROLES.CIVILIAN]: [\r\n    PERMISSIONS.MANAGE_EMAIL,\r\n    PERMISSIONS.MANAGE_EMAIL_CREDENTIALS,\r\n  ],\r\n} as const;\r\n\r\nexport function hasPermission(userRoles: Role[], permission: Permission): boolean {\r\n  return userRoles.some(role => ROLE_PERMISSIONS[role]?.includes(permission));\r\n} ", "import { SignButton } from \"@/components/auth/sign-button\"\r\nimport { ThemeToggle } from \"@/components/theme/theme-toggle\"\r\nimport { Logo } from \"@/components/ui/logo\"\r\n\r\nexport function Header() {\r\n  return (\r\n    <header className=\"fixed top-0 left-0 right-0 z-50 h-16 bg-background/80 backdrop-blur-sm border-b\">\r\n      <div className=\"container mx-auto h-full px-4\">\r\n        <div className=\"h-full flex items-center justify-between\">\r\n          <Logo />\r\n          <div className=\"flex items-center gap-4\">\r\n            <ThemeToggle />\r\n            <SignButton />\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </header>\r\n  )\r\n} "], "names": ["SignButton", "size", "router", "useRouter", "data", "session", "status", "useSession", "loading", "div", "className", "user", "Link", "href", "image", "Image", "src", "alt", "name", "width", "height", "span", "<PERSON><PERSON>", "onClick", "signOut", "callbackUrl", "variant", "cn", "push", "LogIn", "ThemeToggle", "theme", "setTheme", "useTheme", "Sun", "Moon", "Dialog", "DialogPrimitive", "DialogTrigger", "DialogPortal", "DialogClose", "DialogOverlay", "React", "props", "ref", "displayName", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "children", "X", "DialogHeader", "<PERSON><PERSON><PERSON><PERSON>er", "DialogTitle", "DialogDescription", "Input", "type", "input", "Label", "LabelPrimitive", "Logo", "svg", "viewBox", "fill", "xmlns", "path", "d", "Select", "SelectPrimitive", "SelectTrigger", "<PERSON><PERSON><PERSON><PERSON>", "ChevronDown", "SelectContent", "position", "SelectItem", "Check", "SelectValue", "EMAIL_CONFIG", "MAX_ACTIVE_EMAILS", "POLL_INTERVAL", "useConfigStore", "create", "config", "error", "fetch", "set", "res", "ok", "Error", "json", "defaultRole", "ROLES", "CIVILIAN", "emailDomains", "emailDomainsArray", "split", "adminContact", "maxEmails", "Number", "message", "useConfig", "useCopy", "options", "toast", "useToast", "successMessage", "errorMessage", "copyToClipboard", "useCallback", "text", "navigator", "clipboard", "writeText", "title", "description", "EMPEROR", "DUKE", "KNIGHT", "MANAGE_EMAIL", "MANAGE_WEBHOOK", "PROMOTE_USER", "MANAGE_CONFIG", "MANAGE_API_KEY", "MANAGE_EMAIL_CREDENTIALS", "Object", "values", "PERMISSIONS", "hasPermission", "userRoles", "permission", "some", "role", "ROLE_PERMISSIONS", "includes", "Header", "header"], "sourceRoot": "", "ignoreList": []}