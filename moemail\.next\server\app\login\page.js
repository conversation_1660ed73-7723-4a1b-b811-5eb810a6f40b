(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[520],{65521:e=>{"use strict";e.exports=require("node:async_hooks")},25356:e=>{"use strict";e.exports=require("node:buffer")},52039:(e,t,r)=>{"use strict";r.r(t),r.d(t,{ComponentMod:()=>E,default:()=>T});var n,a={};r.r(a),r.d(a,{ClientPageRoot:()=>f.Fy,ClientSegmentRoot:()=>f.pl,GlobalError:()=>u.default,HTTPAccessFallbackBoundary:()=>f.nQ,LayoutRouter:()=>f.C3,MetadataBoundary:()=>f.qB,OutletBoundary:()=>f.Cr,Postpone:()=>f.fK,RenderFromTemplateContext:()=>f.IY,ViewportBoundary:()=>f.PX,__next_app__:()=>v,actionAsyncStorage:()=>f.sc,collectSegmentData:()=>f.Uy,createMetadataComponents:()=>f.IB,createPrerenderParamsForClientSegment:()=>f.lu,createPrerenderSearchParamsForClientPage:()=>f.jO,createServerParamsForMetadata:()=>f.Kx,createServerParamsForServerSegment:()=>f.LV,createServerSearchParamsForMetadata:()=>f.mh,createServerSearchParamsForServerPage:()=>f.Vv,createTemporaryReferenceSet:()=>f.XI,decodeAction:()=>f.Jk,decodeFormState:()=>f.Am,decodeReply:()=>f.X$,pages:()=>p,patchFetch:()=>f.V5,preconnect:()=>f.kZ,preloadFont:()=>f.PY,preloadStyle:()=>f.vI,prerender:()=>f.CR,renderToReadableStream:()=>f.WK,routeModule:()=>g,serverHooks:()=>f.ge,taintObjectReference:()=>f.N2,tree:()=>m,workAsyncStorage:()=>f.J_,workUnitAsyncStorage:()=>f.FP}),r(79556);var i=r(19022),s=r(58661),o=r(20809),l=r(11939),c=r(13348),d=r(14139),u=r(86867),f=r(71102);let m=["",{children:["login",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,9526)),"F:\\CODE\\Project\\tempmail\\moemail\\app\\login\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,27711)),"F:\\CODE\\Project\\tempmail\\moemail\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,64913)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.bind(r,2412)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.bind(r,77757)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],p=["F:\\CODE\\Project\\tempmail\\moemail\\app\\login\\page.tsx"],v={require:r,loadChunk:()=>Promise.resolve()},g=new c.AppPageRouteModule({definition:{kind:d.A.APP_PAGE,page:"/login/page",pathname:"/login",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:m}});var h=r(18058),x=r(72495),y=r(75376);globalThis.__nextCacheHandlers||(globalThis.__nextCacheHandlers={});let b=e=>e?JSON.parse(e):void 0,N=self.__BUILD_MANIFEST,w=b(self.__REACT_LOADABLE_MANIFEST),j=null==(n=self.__RSC_MANIFEST)?void 0:n["/login/page"],C=b(self.__RSC_SERVER_MANIFEST),R=b(self.__NEXT_FONT_MANIFEST),S=b(self.__INTERCEPTION_ROUTE_REWRITE_MANIFEST)??[];j&&C&&(0,x.fQ)({page:"/login/page",clientReferenceManifest:j,serverActionsManifest:C,serverModuleMap:(0,y.e)({serverActionsManifest:C})});let P=(0,s.R)({pagesType:h.g.APP,dev:!1,page:"/login/page",appMod:null,pageMod:a,errorMod:null,error500Mod:null,Document:null,buildManifest:N,renderToHTML:l.W,reactLoadableManifest:w,clientReferenceManifest:j,serverActionsManifest:C,serverActions:void 0,subresourceIntegrityManifest:void 0,config:{env:{},eslint:{ignoreDuringBuilds:!1},typescript:{ignoreBuildErrors:!1,tsconfigPath:"tsconfig.json"},distDir:".next",cleanDistDir:!0,assetPrefix:"",cacheMaxMemorySize:0x3200000,configOrigin:"next.config.ts",useFileSystemPublicRoutes:!0,generateEtags:!0,pageExtensions:["tsx","ts","jsx","js"],poweredByHeader:!0,compress:!0,images:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",loaderFile:"",domains:[],disableStaticImages:!1,minimumCacheTTL:60,formats:["image/webp"],dangerouslyAllowSVG:!1,contentSecurityPolicy:"script-src 'none'; frame-src 'none'; sandbox;",contentDispositionType:"attachment",remotePatterns:[{protocol:"https",hostname:"avatars.githubusercontent.com"}],unoptimized:!1},devIndicators:{appIsrStatus:!0,buildActivity:!0,buildActivityPosition:"bottom-right"},onDemandEntries:{maxInactiveAge:6e4,pagesBufferLength:5},amp:{canonicalBase:""},basePath:"",sassOptions:{},trailingSlash:!1,i18n:null,productionBrowserSourceMaps:!1,excludeDefaultMomentLocales:!0,serverRuntimeConfig:{},publicRuntimeConfig:{},reactProductionProfiling:!1,reactStrictMode:null,reactMaxHeadersLength:6e3,httpAgentOptions:{keepAlive:!0},logging:{},expireTime:31536e3,staticPageGenerationTimeout:60,modularizeImports:{"@mui/icons-material":{transform:"@mui/icons-material/{{member}}"},lodash:{transform:"lodash/{{member}}"}},outputFileTracingRoot:"F:\\CODE\\Project\\tempmail\\moemail",experimental:{cacheLife:{default:{stale:300,revalidate:900,expire:0xfffffffe},seconds:{stale:0,revalidate:1,expire:60},minutes:{stale:300,revalidate:60,expire:3600},hours:{stale:300,revalidate:3600,expire:86400},days:{stale:300,revalidate:86400,expire:604800},weeks:{stale:300,revalidate:604800,expire:2592e3},max:{stale:300,revalidate:2592e3,expire:0xfffffffe}},cacheHandlers:{},cssChunking:!0,multiZoneDraftMode:!1,appNavFailHandling:!1,prerenderEarlyExit:!0,serverMinification:!0,serverSourceMaps:!1,linkNoTouchStart:!1,caseSensitiveRoutes:!1,clientSegmentCache:!1,preloadEntriesOnStart:!0,clientRouterFilter:!0,clientRouterFilterRedirects:!1,fetchCacheKeyPrefix:"",middlewarePrefetch:"flexible",optimisticClientCache:!0,manualClientBasePath:!1,cpus:11,memoryBasedWorkersCount:!1,imgOptConcurrency:null,imgOptTimeoutInSeconds:7,imgOptMaxInputPixels:0xfff8001,imgOptSequentialRead:null,isrFlushToDisk:!0,workerThreads:!1,optimizeCss:!1,nextScriptWorkers:!1,scrollRestoration:!1,externalDir:!1,disableOptimizedLoading:!1,gzipSize:!0,craCompat:!1,esmExternals:!0,fullySpecified:!1,swcTraceProfiling:!1,forceSwcTransforms:!1,largePageDataBytes:128e3,turbo:{root:"F:\\CODE\\Project\\tempmail\\moemail"},typedRoutes:!1,typedEnv:!1,parallelServerCompiles:!1,parallelServerBuildTraces:!1,ppr:!1,authInterrupts:!1,reactOwnerStack:!1,webpackMemoryOptimizations:!1,optimizeServerReact:!0,useEarlyImport:!1,staleTimes:{dynamic:0,static:300},serverComponentsHmrCache:!0,staticGenerationMaxConcurrency:8,staticGenerationMinPagesPerWorker:25,dynamicIO:!1,inlineCss:!1,optimizePackageImports:["lucide-react","date-fns","lodash-es","ramda","antd","react-bootstrap","ahooks","@ant-design/icons","@headlessui/react","@headlessui-float/react","@heroicons/react/20/solid","@heroicons/react/24/solid","@heroicons/react/24/outline","@visx/visx","@tremor/react","rxjs","@mui/material","@mui/icons-material","recharts","react-use","effect","@effect/schema","@effect/platform","@effect/platform-node","@effect/platform-browser","@effect/platform-bun","@effect/sql","@effect/sql-mssql","@effect/sql-mysql2","@effect/sql-pg","@effect/sql-squlite-node","@effect/sql-squlite-bun","@effect/sql-squlite-wasm","@effect/sql-squlite-react-native","@effect/rpc","@effect/rpc-http","@effect/typeclass","@effect/experimental","@effect/opentelemetry","@material-ui/core","@material-ui/icons","@tabler/icons-react","mui-core","react-icons/ai","react-icons/bi","react-icons/bs","react-icons/cg","react-icons/ci","react-icons/di","react-icons/fa","react-icons/fa6","react-icons/fc","react-icons/fi","react-icons/gi","react-icons/go","react-icons/gr","react-icons/hi","react-icons/hi2","react-icons/im","react-icons/io","react-icons/io5","react-icons/lia","react-icons/lib","react-icons/lu","react-icons/md","react-icons/pi","react-icons/ri","react-icons/rx","react-icons/si","react-icons/sl","react-icons/tb","react-icons/tfi","react-icons/ti","react-icons/vsc","react-icons/wi"]},bundlePagesRouterDependencies:!1,configFile:"F:\\CODE\\Project\\tempmail\\moemail\\next.config.ts",configFileName:"next.config.ts"},buildId:process.env.__NEXT_BUILD_ID,nextFontManifest:R,incrementalCacheHandler:null,interceptionRouteRewrites:S}),E=a;function T(e){return(0,i.O)({...e,IncrementalCache:o.N,handler:P})}},41221:(e,t,r)=>{Promise.resolve().then(r.bind(r,3055))},54373:(e,t,r)=>{Promise.resolve().then(r.bind(r,60342))},60342:(e,t,r)=>{"use strict";r.d(t,{LoginForm:()=>ey});var n=r(37785),a=r(57845),i=r.t(a,2),s=r(31648),o=r(37062),l=r(277),c=r(70937),d=r(39086);let u=a.forwardRef(({className:e,...t},r)=>(0,n.jsx)("div",{ref:r,className:(0,d.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",e),...t}));u.displayName="Card";let f=a.forwardRef(({className:e,...t},r)=>(0,n.jsx)("div",{ref:r,className:(0,d.cn)("flex flex-col space-y-1.5 p-6",e),...t}));f.displayName="CardHeader";let m=a.forwardRef(({className:e,...t},r)=>(0,n.jsx)("h3",{ref:r,className:(0,d.cn)("text-2xl font-semibold leading-none tracking-tight",e),...t}));m.displayName="CardTitle";let p=a.forwardRef(({className:e,...t},r)=>(0,n.jsx)("p",{ref:r,className:(0,d.cn)("text-sm text-muted-foreground",e),...t}));p.displayName="CardDescription";let v=a.forwardRef(({className:e,...t},r)=>(0,n.jsx)("div",{ref:r,className:(0,d.cn)("p-6 pt-0",e),...t}));function g(e,t,{checkForDefaultPrevented:r=!0}={}){return function(n){if(e?.(n),!1===r||!n.defaultPrevented)return t?.(n)}}function h(e,t=[]){let r=[],i=()=>{let t=r.map(e=>a.createContext(e));return function(r){let n=r?.[e]||t;return a.useMemo(()=>({[`__scope${e}`]:{...r,[e]:n}}),[r,n])}};return i.scopeName=e,[function(t,i){let s=a.createContext(i),o=r.length;r=[...r,i];let l=t=>{let{scope:r,children:i,...l}=t,c=r?.[e]?.[o]||s,d=a.useMemo(()=>l,Object.values(l));return(0,n.jsx)(c.Provider,{value:d,children:i})};return l.displayName=t+"Provider",[l,function(r,n){let l=n?.[e]?.[o]||s,c=a.useContext(l);if(c)return c;if(void 0!==i)return i;throw Error(`\`${r}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let r=()=>{let r=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let n=r.reduce((t,{useScope:r,scopeName:n})=>{let a=r(e)[`__scope${n}`];return{...t,...a}},{});return a.useMemo(()=>({[`__scope${t.scopeName}`]:n}),[n])}};return r.scopeName=t.scopeName,r}(i,...t)]}function x(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function y(...e){return t=>{let r=!1,n=e.map(e=>{let n=x(e,t);return r||"function"!=typeof n||(r=!0),n});if(r)return()=>{for(let t=0;t<n.length;t++){let r=n[t];"function"==typeof r?r():x(e[t],null)}}}}function b(...e){return a.useCallback(y(...e),e)}function N(e){let t=function(e){let t=a.forwardRef((e,t)=>{let{children:r,...n}=e;if(a.isValidElement(r)){let e,i;let s=(e=Object.getOwnPropertyDescriptor(r.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?r.ref:(e=Object.getOwnPropertyDescriptor(r,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?r.props.ref:r.props.ref||r.ref,o=function(e,t){let r={...t};for(let n in t){let a=e[n],i=t[n];/^on[A-Z]/.test(n)?a&&i?r[n]=(...e)=>{let t=i(...e);return a(...e),t}:a&&(r[n]=a):"style"===n?r[n]={...a,...i}:"className"===n&&(r[n]=[a,i].filter(Boolean).join(" "))}return{...e,...r}}(n,r.props);return r.type!==a.Fragment&&(o.ref=t?y(t,s):s),a.cloneElement(r,o)}return a.Children.count(r)>1?a.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),r=a.forwardRef((e,r)=>{let{children:i,...s}=e,o=a.Children.toArray(i),l=o.find(j);if(l){let e=l.props.children,i=o.map(t=>t!==l?t:a.Children.count(e)>1?a.Children.only(null):a.isValidElement(e)?e.props.children:null);return(0,n.jsx)(t,{...s,ref:r,children:a.isValidElement(e)?a.cloneElement(e,void 0,i):null})}return(0,n.jsx)(t,{...s,ref:r,children:i})});return r.displayName=`${e}.Slot`,r}v.displayName="CardContent",a.forwardRef(({className:e,...t},r)=>(0,n.jsx)("div",{ref:r,className:(0,d.cn)("flex items-center p-6 pt-0",e),...t})).displayName="CardFooter";var w=Symbol("radix.slottable");function j(e){return a.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===w}var C=globalThis?.document?a.useLayoutEffect:()=>{},R=i[" useId ".trim().toString()]||(()=>void 0),S=0;function P(e){let[t,r]=a.useState(R());return C(()=>{e||r(e=>e??String(S++))},[e]),e||(t?`radix-${t}`:"")}r(75724);var E=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let r=N(`Primitive.${t}`),i=a.forwardRef((e,a)=>{let{asChild:i,...s}=e,o=i?r:t;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,n.jsx)(o,{...s,ref:a})});return i.displayName=`Primitive.${t}`,{...e,[t]:i}},{}),T=i[" useInsertionEffect ".trim().toString()]||C;function M({prop:e,defaultProp:t,onChange:r=()=>{},caller:n}){let[i,s,o]=function({defaultProp:e,onChange:t}){let[r,n]=a.useState(e),i=a.useRef(r),s=a.useRef(t);return T(()=>{s.current=t},[t]),a.useEffect(()=>{i.current!==r&&(s.current?.(r),i.current=r)},[r,i]),[r,n,s]}({defaultProp:t,onChange:r}),l=void 0!==e,c=l?e:i;{let t=a.useRef(void 0!==e);a.useEffect(()=>{let e=t.current;if(e!==l){let t=l?"controlled":"uncontrolled";console.warn(`${n} is changing from ${e?"controlled":"uncontrolled"} to ${t}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}t.current=l},[l,n])}return[c,a.useCallback(t=>{if(l){let r="function"==typeof t?t(e):t;r!==e&&o.current?.(r)}else s(t)},[l,e,s,o])]}Symbol("RADIX:SYNC_STATE");var A=a.createContext(void 0);function _(e){let t=a.useContext(A);return e||t||"ltr"}var I="rovingFocusGroup.onEntryFocus",F={bubbles:!1,cancelable:!0},k="RovingFocusGroup",[O,D,L]=function(e){let t=e+"CollectionProvider",[r,i]=h(t),[s,o]=r(t,{collectionRef:{current:null},itemMap:new Map}),l=e=>{let{scope:t,children:r}=e,i=a.useRef(null),o=a.useRef(new Map).current;return(0,n.jsx)(s,{scope:t,itemMap:o,collectionRef:i,children:r})};l.displayName=t;let c=e+"CollectionSlot",d=N(c),u=a.forwardRef((e,t)=>{let{scope:r,children:a}=e,i=b(t,o(c,r).collectionRef);return(0,n.jsx)(d,{ref:i,children:a})});u.displayName=c;let f=e+"CollectionItemSlot",m="data-radix-collection-item",p=N(f),v=a.forwardRef((e,t)=>{let{scope:r,children:i,...s}=e,l=a.useRef(null),c=b(t,l),d=o(f,r);return a.useEffect(()=>(d.itemMap.set(l,{ref:l,...s}),()=>void d.itemMap.delete(l))),(0,n.jsx)(p,{[m]:"",ref:c,children:i})});return v.displayName=f,[{Provider:l,Slot:u,ItemSlot:v},function(t){let r=o(e+"CollectionConsumer",t);return a.useCallback(()=>{let e=r.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll(`[${m}]`));return Array.from(r.itemMap.values()).sort((e,r)=>t.indexOf(e.ref.current)-t.indexOf(r.ref.current))},[r.collectionRef,r.itemMap])},i]}(k),[B,$]=h(k,[L]),[q,U]=B(k),z=a.forwardRef((e,t)=>(0,n.jsx)(O.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,n.jsx)(O.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,n.jsx)(V,{...e,ref:t})})}));z.displayName=k;var V=a.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:r,orientation:i,loop:s=!1,dir:o,currentTabStopId:l,defaultCurrentTabStopId:c,onCurrentTabStopIdChange:d,onEntryFocus:u,preventScrollOnEntryFocus:f=!1,...m}=e,p=a.useRef(null),v=b(t,p),h=_(o),[x,y]=M({prop:l,defaultProp:c??null,onChange:d,caller:k}),[N,w]=a.useState(!1),j=function(e){let t=a.useRef(e);return a.useEffect(()=>{t.current=e}),a.useMemo(()=>(...e)=>t.current?.(...e),[])}(u),C=D(r),R=a.useRef(!1),[S,P]=a.useState(0);return a.useEffect(()=>{let e=p.current;if(e)return e.addEventListener(I,j),()=>e.removeEventListener(I,j)},[j]),(0,n.jsx)(q,{scope:r,orientation:i,dir:h,loop:s,currentTabStopId:x,onItemFocus:a.useCallback(e=>y(e),[y]),onItemShiftTab:a.useCallback(()=>w(!0),[]),onFocusableItemAdd:a.useCallback(()=>P(e=>e+1),[]),onFocusableItemRemove:a.useCallback(()=>P(e=>e-1),[]),children:(0,n.jsx)(E.div,{tabIndex:N||0===S?-1:0,"data-orientation":i,...m,ref:v,style:{outline:"none",...e.style},onMouseDown:g(e.onMouseDown,()=>{R.current=!0}),onFocus:g(e.onFocus,e=>{let t=!R.current;if(e.target===e.currentTarget&&t&&!N){let t=new CustomEvent(I,F);if(e.currentTarget.dispatchEvent(t),!t.defaultPrevented){let e=C().filter(e=>e.focusable);W([e.find(e=>e.active),e.find(e=>e.id===x),...e].filter(Boolean).map(e=>e.ref.current),f)}}R.current=!1}),onBlur:g(e.onBlur,()=>w(!1))})})}),G="RovingFocusGroupItem",K=a.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:r,focusable:i=!0,active:s=!1,tabStopId:o,children:l,...c}=e,d=P(),u=o||d,f=U(G,r),m=f.currentTabStopId===u,p=D(r),{onFocusableItemAdd:v,onFocusableItemRemove:h,currentTabStopId:x}=f;return a.useEffect(()=>{if(i)return v(),()=>h()},[i,v,h]),(0,n.jsx)(O.ItemSlot,{scope:r,id:u,focusable:i,active:s,children:(0,n.jsx)(E.span,{tabIndex:m?0:-1,"data-orientation":f.orientation,...c,ref:t,onMouseDown:g(e.onMouseDown,e=>{i?f.onItemFocus(u):e.preventDefault()}),onFocus:g(e.onFocus,()=>f.onItemFocus(u)),onKeyDown:g(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey){f.onItemShiftTab();return}if(e.target!==e.currentTarget)return;let t=function(e,t,r){var n;let a=(n=e.key,"rtl"!==r?n:"ArrowLeft"===n?"ArrowRight":"ArrowRight"===n?"ArrowLeft":n);if(!("vertical"===t&&["ArrowLeft","ArrowRight"].includes(a))&&!("horizontal"===t&&["ArrowUp","ArrowDown"].includes(a)))return H[a]}(e,f.orientation,f.dir);if(void 0!==t){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let r=p().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===t)r.reverse();else if("prev"===t||"next"===t){"prev"===t&&r.reverse();let n=r.indexOf(e.currentTarget);r=f.loop?function(e,t){return e.map((r,n)=>e[(t+n)%e.length])}(r,n+1):r.slice(n+1)}setTimeout(()=>W(r))}}),children:"function"==typeof l?l({isCurrentTabStop:m,hasTabStop:null!=x}):l})})});K.displayName=G;var H={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function W(e,t=!1){let r=document.activeElement;for(let n of e)if(n===r||(n.focus({preventScroll:t}),document.activeElement!==r))return}var J=e=>{let{present:t,children:r}=e,n=function(e){var t,r;let[n,i]=a.useState(),s=a.useRef(null),o=a.useRef(e),l=a.useRef("none"),[c,d]=(t=e?"mounted":"unmounted",r={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},a.useReducer((e,t)=>r[e][t]??e,t));return a.useEffect(()=>{let e=X(s.current);l.current="mounted"===c?e:"none"},[c]),C(()=>{let t=s.current,r=o.current;if(r!==e){let n=l.current,a=X(t);e?d("MOUNT"):"none"===a||t?.display==="none"?d("UNMOUNT"):r&&n!==a?d("ANIMATION_OUT"):d("UNMOUNT"),o.current=e}},[e,d]),C(()=>{if(n){let e;let t=n.ownerDocument.defaultView??window,r=r=>{let a=X(s.current).includes(r.animationName);if(r.target===n&&a&&(d("ANIMATION_END"),!o.current)){let r=n.style.animationFillMode;n.style.animationFillMode="forwards",e=t.setTimeout(()=>{"forwards"===n.style.animationFillMode&&(n.style.animationFillMode=r)})}},a=e=>{e.target===n&&(l.current=X(s.current))};return n.addEventListener("animationstart",a),n.addEventListener("animationcancel",r),n.addEventListener("animationend",r),()=>{t.clearTimeout(e),n.removeEventListener("animationstart",a),n.removeEventListener("animationcancel",r),n.removeEventListener("animationend",r)}}d("ANIMATION_END")},[n,d]),{isPresent:["mounted","unmountSuspended"].includes(c),ref:a.useCallback(e=>{s.current=e?getComputedStyle(e):null,i(e)},[])}}(t),i="function"==typeof r?r({present:n.isPresent}):a.Children.only(r),s=b(n.ref,function(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,r=t&&"isReactWarning"in t&&t.isReactWarning;return r?e.ref:(r=(t=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning)?e.props.ref:e.props.ref||e.ref}(i));return"function"==typeof r||n.isPresent?a.cloneElement(i,{ref:s}):null};function X(e){return e?.animationName||"none"}J.displayName="Presence";var Y="Tabs",[Z,Q]=h(Y,[$]),ee=$(),[et,er]=Z(Y),en=a.forwardRef((e,t)=>{let{__scopeTabs:r,value:a,onValueChange:i,defaultValue:s,orientation:o="horizontal",dir:l,activationMode:c="automatic",...d}=e,u=_(l),[f,m]=M({prop:a,onChange:i,defaultProp:s??"",caller:Y});return(0,n.jsx)(et,{scope:r,baseId:P(),value:f,onValueChange:m,orientation:o,dir:u,activationMode:c,children:(0,n.jsx)(E.div,{dir:u,"data-orientation":o,...d,ref:t})})});en.displayName=Y;var ea="TabsList",ei=a.forwardRef((e,t)=>{let{__scopeTabs:r,loop:a=!0,...i}=e,s=er(ea,r),o=ee(r);return(0,n.jsx)(z,{asChild:!0,...o,orientation:s.orientation,dir:s.dir,loop:a,children:(0,n.jsx)(E.div,{role:"tablist","aria-orientation":s.orientation,...i,ref:t})})});ei.displayName=ea;var es="TabsTrigger",eo=a.forwardRef((e,t)=>{let{__scopeTabs:r,value:a,disabled:i=!1,...s}=e,o=er(es,r),l=ee(r),c=ed(o.baseId,a),d=eu(o.baseId,a),u=a===o.value;return(0,n.jsx)(K,{asChild:!0,...l,focusable:!i,active:u,children:(0,n.jsx)(E.button,{type:"button",role:"tab","aria-selected":u,"aria-controls":d,"data-state":u?"active":"inactive","data-disabled":i?"":void 0,disabled:i,id:c,...s,ref:t,onMouseDown:g(e.onMouseDown,e=>{i||0!==e.button||!1!==e.ctrlKey?e.preventDefault():o.onValueChange(a)}),onKeyDown:g(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&o.onValueChange(a)}),onFocus:g(e.onFocus,()=>{let e="manual"!==o.activationMode;u||i||!e||o.onValueChange(a)})})})});eo.displayName=es;var el="TabsContent",ec=a.forwardRef((e,t)=>{let{__scopeTabs:r,value:i,forceMount:s,children:o,...l}=e,c=er(el,r),d=ed(c.baseId,i),u=eu(c.baseId,i),f=i===c.value,m=a.useRef(f);return a.useEffect(()=>{let e=requestAnimationFrame(()=>m.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,n.jsx)(J,{present:s||f,children:({present:r})=>(0,n.jsx)(E.div,{"data-state":f?"active":"inactive","data-orientation":c.orientation,role:"tabpanel","aria-labelledby":d,hidden:!r,id:u,tabIndex:0,...l,ref:t,style:{...e.style,animationDuration:m.current?"0s":void 0},children:r&&o})})});function ed(e,t){return`${e}-trigger-${t}`}function eu(e,t){return`${e}-content-${t}`}ec.displayName=el;let ef=a.forwardRef(({className:e,...t},r)=>(0,n.jsx)(ei,{ref:r,className:(0,d.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",e),...t}));ef.displayName=ei.displayName;let em=a.forwardRef(({className:e,...t},r)=>(0,n.jsx)(eo,{ref:r,className:(0,d.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",e),...t}));em.displayName=eo.displayName;let ep=a.forwardRef(({className:e,...t},r)=>(0,n.jsx)(ec,{ref:r,className:(0,d.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",e),...t}));ep.displayName=ec.displayName;var ev=r(93295);let eg=(0,r(93823).A)("KeyRound",[["path",{d:"M2.586 17.414A2 2 0 0 0 2 18.828V21a1 1 0 0 0 1 1h3a1 1 0 0 0 1-1v-1a1 1 0 0 1 1-1h1a1 1 0 0 0 1-1v-1a1 1 0 0 1 1-1h.172a2 2 0 0 0 1.414-.586l.814-.814a6.5 6.5 0 1 0-4-4z",key:"1s6t7t"}],["circle",{cx:"16.5",cy:"7.5",r:".5",fill:"currentColor",key:"w0ekpg"}]]);var eh=r(88563),ex=r(91378);function ey(){let[e,t]=(0,a.useState)(""),[r,i]=(0,a.useState)(""),[g,h]=(0,a.useState)(""),[x,y]=(0,a.useState)(!1),[b,N]=(0,a.useState)({}),{toast:w}=(0,o.dj)(),j=()=>{let t={};return e||(t.username="请输入用户名"),r||(t.password="请输入密码"),e.includes("@")&&(t.username="用户名不能包含 @ 符号"),r&&r.length<8&&(t.password="密码长度必须大于等于8位"),N(t),0===Object.keys(t).length},C=()=>{let t={};return e||(t.username="请输入用户名"),r||(t.password="请输入密码"),e.includes("@")&&(t.username="用户名不能包含 @ 符号"),r&&r.length<8&&(t.password="密码长度必须大于等于8位"),g||(t.confirmPassword="请确认密码"),r!==g&&(t.confirmPassword="两次输入的密码不一致"),N(t),0===Object.keys(t).length},R=async()=>{if(j()){y(!0);try{let t=await (0,s.Jv)("credentials",{username:e,password:r,redirect:!1});if(t?.error){w({title:"登录失败",description:"用户名或密码错误",variant:"destructive"}),y(!1);return}window.location.href="/"}catch(e){w({title:"登录失败",description:e instanceof Error?e.message:"请稍后重试",variant:"destructive"}),y(!1)}}},S=async()=>{if(C()){y(!0);try{let t=await fetch("/api/auth/register",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({username:e,password:r})}),n=await t.json();if(!t.ok){w({title:"注册失败",description:n.error||"请稍后重试",variant:"destructive"}),y(!1);return}let a=await (0,s.Jv)("credentials",{username:e,password:r,redirect:!1});if(a?.error){w({title:"登录失败",description:"自动登录失败，请手动登录",variant:"destructive"}),y(!1);return}window.location.href="/"}catch(e){w({title:"注册失败",description:e instanceof Error?e.message:"请稍后重试",variant:"destructive"}),y(!1)}}};return(0,n.jsxs)(u,{className:"w-[95%] max-w-lg border-2 border-primary/20",children:[(0,n.jsxs)(f,{className:"space-y-2",children:[(0,n.jsx)(m,{className:"text-2xl text-center bg-gradient-to-r from-primary to-purple-600 bg-clip-text text-transparent",children:"欢迎使用 MoeMail"}),(0,n.jsx)(p,{className:"text-center",children:"萌萌哒临时邮箱服务 (。・∀・)ノ"})]}),(0,n.jsx)(v,{className:"px-6",children:(0,n.jsxs)(en,{defaultValue:"login",className:"w-full",onValueChange:()=>{t(""),i(""),h(""),N({})},children:[(0,n.jsxs)(ef,{className:"grid w-full grid-cols-2 mb-6",children:[(0,n.jsx)(em,{value:"login",children:"登录"}),(0,n.jsx)(em,{value:"register",children:"注册"})]}),(0,n.jsxs)("div",{className:"min-h-[220px]",children:[(0,n.jsxs)(ep,{value:"login",className:"space-y-4 mt-0",children:[(0,n.jsxs)("div",{className:"space-y-3",children:[(0,n.jsxs)("div",{className:"space-y-1.5",children:[(0,n.jsxs)("div",{className:"relative",children:[(0,n.jsx)("div",{className:"absolute left-2.5 top-2 text-muted-foreground",children:(0,n.jsx)(ev.A,{className:"h-5 w-5"})}),(0,n.jsx)(c.p,{className:(0,d.cn)("h-9 pl-9 pr-3",b.username&&"border-destructive focus-visible:ring-destructive"),placeholder:"用户名",value:e,onChange:e=>{t(e.target.value),N({})},disabled:x})]}),b.username&&(0,n.jsx)("p",{className:"text-xs text-destructive",children:b.username})]}),(0,n.jsxs)("div",{className:"space-y-1.5",children:[(0,n.jsxs)("div",{className:"relative",children:[(0,n.jsx)("div",{className:"absolute left-2.5 top-2 text-muted-foreground",children:(0,n.jsx)(eg,{className:"h-5 w-5"})}),(0,n.jsx)(c.p,{className:(0,d.cn)("h-9 pl-9 pr-3",b.password&&"border-destructive focus-visible:ring-destructive"),type:"password",placeholder:"密码",value:r,onChange:e=>{i(e.target.value),N({})},disabled:x})]}),b.password&&(0,n.jsx)("p",{className:"text-xs text-destructive",children:b.password})]})]}),(0,n.jsxs)("div",{className:"space-y-3 pt-1",children:[(0,n.jsxs)(l.$,{className:"w-full",onClick:R,disabled:x,children:[x&&(0,n.jsx)(eh.A,{className:"mr-2 h-4 w-4 animate-spin"}),"登录"]}),(0,n.jsxs)("div",{className:"relative",children:[(0,n.jsx)("div",{className:"absolute inset-0 flex items-center",children:(0,n.jsx)("span",{className:"w-full border-t"})}),(0,n.jsx)("div",{className:"relative flex justify-center text-xs uppercase",children:(0,n.jsx)("span",{className:"bg-background px-2 text-muted-foreground",children:"或者"})})]}),(0,n.jsxs)(l.$,{variant:"outline",className:"w-full",onClick:()=>{(0,s.Jv)("github",{callbackUrl:"/"})},children:[(0,n.jsx)(ex.A,{className:"mr-2 h-4 w-4"}),"使用 GitHub 账号登录"]})]})]}),(0,n.jsxs)(ep,{value:"register",className:"space-y-4 mt-0",children:[(0,n.jsxs)("div",{className:"space-y-3",children:[(0,n.jsxs)("div",{className:"space-y-1.5",children:[(0,n.jsxs)("div",{className:"relative",children:[(0,n.jsx)("div",{className:"absolute left-2.5 top-2 text-muted-foreground",children:(0,n.jsx)(ev.A,{className:"h-5 w-5"})}),(0,n.jsx)(c.p,{className:(0,d.cn)("h-9 pl-9 pr-3",b.username&&"border-destructive focus-visible:ring-destructive"),placeholder:"用户名",value:e,onChange:e=>{t(e.target.value),N({})},disabled:x})]}),b.username&&(0,n.jsx)("p",{className:"text-xs text-destructive",children:b.username})]}),(0,n.jsxs)("div",{className:"space-y-1.5",children:[(0,n.jsxs)("div",{className:"relative",children:[(0,n.jsx)("div",{className:"absolute left-2.5 top-2 text-muted-foreground",children:(0,n.jsx)(eg,{className:"h-5 w-5"})}),(0,n.jsx)(c.p,{className:(0,d.cn)("h-9 pl-9 pr-3",b.password&&"border-destructive focus-visible:ring-destructive"),type:"password",placeholder:"密码",value:r,onChange:e=>{i(e.target.value),N({})},disabled:x})]}),b.password&&(0,n.jsx)("p",{className:"text-xs text-destructive",children:b.password})]}),(0,n.jsxs)("div",{className:"space-y-1.5",children:[(0,n.jsxs)("div",{className:"relative",children:[(0,n.jsx)("div",{className:"absolute left-2.5 top-2 text-muted-foreground",children:(0,n.jsx)(eg,{className:"h-5 w-5"})}),(0,n.jsx)(c.p,{className:(0,d.cn)("h-9 pl-9 pr-3",b.confirmPassword&&"border-destructive focus-visible:ring-destructive"),type:"password",placeholder:"确认密码",value:g,onChange:e=>{h(e.target.value),N({})},disabled:x})]}),b.confirmPassword&&(0,n.jsx)("p",{className:"text-xs text-destructive",children:b.confirmPassword})]})]}),(0,n.jsx)("div",{className:"space-y-3 pt-1",children:(0,n.jsxs)(l.$,{className:"w-full",onClick:S,disabled:x,children:[x&&(0,n.jsx)(eh.A,{className:"mr-2 h-4 w-4 animate-spin"}),"注册"]})})]})]})]})})]})}},70937:(e,t,r)=>{"use strict";r.d(t,{p:()=>s});var n=r(37785),a=r(57845),i=r(39086);let s=a.forwardRef(({className:e,type:t,...r},a)=>(0,n.jsx)("input",{type:t,className:(0,i.cn)("flex h-9 w-full rounded-md border border-input bg-transparent px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50",e),ref:a,...r}));s.displayName="Input"},88563:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(93823).A)("LoaderCircle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},93295:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(93823).A)("UserRound",[["circle",{cx:"12",cy:"8",r:"5",key:"1hypcn"}],["path",{d:"M20 21a8 8 0 0 0-16 0",key:"rfgkzh"}]])},3055:(e,t,r)=>{"use strict";r.d(t,{LoginForm:()=>n});let n=(0,r(45710).YR)(function(){throw Error("Attempted to call LoginForm() from the server but LoginForm is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"F:\\CODE\\Project\\tempmail\\moemail\\app\\components\\auth\\login-form.tsx","LoginForm")},9526:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>l,runtime:()=>o});var n=r(4302),a=r(3055),i=r(83553),s=r(51756);let o="edge";async function l(){let e=await (0,i.j2)();return e?.user&&(0,s.V2)("/"),(0,n.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gradient-to-b from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800",children:(0,n.jsx)(a.LoginForm,{})})}}},e=>{var t=t=>e(e.s=t);e.O(0,[730,752,899,498,943,156,481],()=>t(52039));var r=e.O();(_ENTRIES="undefined"==typeof _ENTRIES?{}:_ENTRIES)["middleware_app/login/page"]=r}]);
//# sourceMappingURL=page.js.map