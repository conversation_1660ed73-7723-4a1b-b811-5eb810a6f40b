{"version": 3, "file": "edge-chunks/514.js", "mappings": "6HAGA,cACA,MAAS,QAAG,SAAS,GAAc,IAAG,UAAU,kBAChD,oFCFA,MAAe,eACf,YAA4B,OAAU,aAGtC,MAFI,OAAc,MAElB,eADA,yBAA+C,OAAe,qBAE9D,CAAC,EAAC,oECAK,SACP,IACA,GACA,cACA,GACA,8BACA,4DAEA,UACA,CACA,sBACA,WACA,8DAGA,OADA,UACA,KAEA,wBACA,WACA,gEAGA,OADA,UACA,KAEA,oBA4BA,EA3BA,sBACA,UAAsB,IAAU,oFAEhC,IAAa,OAAU,kBACvB,UAAsB,IAAU,8EAEhC,OACA,WACA,YAEA,EAA2B,OAAY,CAAC,IAAU,0CAClD,KACA,iBAEA,iBADA,gBAEA,UAA0B,IAAU,4EAGpC,QAAgB,GAAM,EACtB,0BACA,UAAsB,IAAU,8DAExB,OAAY,aACpB,cACA,GACA,GAAsB,IAAO,QAAQ,OAAI,MAIzC,EADA,QAC8B,IAAO,QAAQ,OAAI,2BAGnB,IAAO,YAErC,MAAqB,QAAM,GAAkB,IAAO,gBACpD,QAAwB,OAAY,MACpC,QAAgC,EAAI,OACpC,GACA,UAAuB,OAAI,IAC3B,UACA,EAUA,OATA,GACA,WAA0B,IAAO,YAEjC,SACA,mBAEA,SACA,aAA4B,IAAO,YAEnC,CACA,CACA,CCpFO,QACP,eACA,GACA,YAA8B,EAAa,EAC3C,CACA,QAF2C,WAE3C,GAEA,OADA,8BACA,KAEA,gBACA,8BACA,sBACA,6EAEA,SAAkB,YAAc,GAAG,UAAY,GAAG,YAAc,EAEhE,eCdO,SACP,IACA,cACA,IAA4B,EAC5B,YAAwB,GAAgB,GACxC,CACA,aAEA,OADA,cACA,KAEA,cAEA,OADA,cACA,KAEA,eAEA,OADA,cACA,IACA,CACA,UAEA,OADA,cACA,KAEA,gBAEA,OADA,cACA,KAEA,qBAEA,OADA,cACA,KAEA,eAEA,OADA,cACA,KAEA,sBAEA,OADA,UACA,KAEA,gBACA,UAAwB,EAAW,gBAEnC,GADA,8BACA,8BACA,8BACA,iBACA,UAAsB,IAAU,wCAEhC,kBACA,CACA,oFChDA,MAAe,iBACf,YAA4B,OAAY,eACpC,OAAc,MAClB,MAAsB,OAAe,gBACrC,IACA,0CACA,CACA,MACA,QACA,CACA,CAAC,EAAC,qECbF,MAAe,QACf,eACA,mDACA,oBAAgC,EAAO,uCAEvC,KAGA,iBACA,CAAC,EAAC,cCCK,4BAmEP,EAYA,EA9EA,IAAS,OAAQ,IACjB,UAAkB,IAAU,oCAE5B,2CACA,UAAkB,IAAU,0EAE5B,sDACA,UAAkB,IAAU,wCAE5B,sBACA,UAAkB,IAAU,wBAE5B,gCACA,UAAkB,IAAU,4CAE5B,uBAAqC,OAAQ,WAC7C,UAAkB,IAAU,0CAE5B,SACA,eACA,IACA,MAAoC,OAAI,cACxC,aAAoC,IAAO,WAC3C,CACA,MACA,UAAsB,IAAU,mCAChC,CAEA,IAAS,OAAU,aACnB,UAAkB,IAAU,8EAE5B,OACA,KACA,WACA,EACA,EAAuB,OAAY,CAAC,IAAU,oCAC9C,KACA,iBAEA,iBADA,UAEA,UAAsB,IAAU,4EAGhC,QAAY,GAAM,EAClB,0BACA,UAAkB,IAAU,8DAE5B,SAAkC,EAAkB,2BACpD,gBACA,UAAkB,IAAiB,yDAEnC,KACA,+BACA,UAAsB,IAAU,gCAChC,MAEA,kEACA,UAAkB,IAAU,2DAE5B,QACA,wBACA,eACA,MAEI,OAAY,eAChB,MAAiB,QAAM,CAAC,IAAO,yBAA8B,IAAO,wCAAgD,IAAO,8BAE3H,IACA,EAAoB,OAAI,aACxB,CACA,MACA,UAAkB,IAAU,4CAC5B,CACA,YAAoB,OAAY,MAEhC,IADA,MAA2B,EAAM,SAEjC,UAAkB,IAA8B,CAGhD,KACA,IACA,EAAsB,OAAI,WAC1B,CACA,MACA,UAAsB,IAAU,0CAChC,MAGA,EADA,2BACkB,IAAO,mBAGzB,UAEA,eAAqB,SAOrB,CANA,sBACA,sBAEA,mBACA,+BAEA,GACA,CAAiB,YAEjB,CACA,CChHO,wBAIP,GAHA,yBACA,GAAc,IAAO,YAErB,mBACA,UAAkB,IAAU,+CAE5B,IAAY,sBAAuD,aACnE,SACA,UAAkB,IAAU,wBAE5B,YAA2B,EAAe,SAAG,IAAH,QAAG,cAAgD,MAC7F,GAAqB,2DACrB,qBACA,CAAiB,gBAEjB,CACA,gBCjBO,wBACP,YAA2B,EAAa,OACxC,IADwC,CACxC,kEACA,UAAkB,IAAU,wCAG5B,OAAqB,QADD,OAAiB,gCAChB,yCACrB,qBACA,CAAiB,gBAEjB,CACA,uDCdO,sBACA,kBAEA,iBAEP,qBADA,oBAAwC,EAAQ,WAEhD,IACA,eACA,WACA,YAEA,QACA,oCCZA,MAAe,QACf,2CACA,kBAAgB,GAAgB,YAChC,8BACA,mBAAmC,GAAK,sDAExC,CACA,CAAC,EAAC,qECJF,iCACA,YACA,mBACA,MACA,UACA,WACA,aACA,QACA,KACA,eACA,cACA,OAEA,CACA,aACA,sEAAsF,EAAS,gBAE/F,CACA,6BACA,sEAAkF,EAAI,iBAEtF,6BACA,MACA,WACA,6BACA,cACA,0BACA,IACA,KACA,4BACA,eACA,KACA,WAAuB,EAAE,0BAEzB,EADA,qCACA,oCAGA,EAEA,KACA,wCACA,YACA,KACA,mBACA,8CAEA,CACA,oCACA,+EAA+F,EAAc,gBAE7G,CACA,QACA,EACA,YACA,+BAEA,GAAQ,IAAS,KACjB,GAAY,IAAe,cAC3B,MACA,2IACA,CACA,IAAS,QAAS,IAClB,gBAA4B,OAAe,2DAE3C,qBACA,mBAA+B,MAAU,8DAEzC,EACA,YACA,GAAQ,IAAS,IACjB,UACA,cACA,WACA,GAAoB,IAAgB,cACpC,MACA,oEACA,eACA,aACA,GAAoB,IAAe,cACnC,MACA,mEACA,CAEA,IAAS,QAAS,IAClB,gBAA4B,OAAe,8CAE3C,qBACA,mBAA+B,MAAU,mEAEzC,qBACA,UACA,WACA,mBAAuC,MAAU,sEACjD,eACA,mBAAuC,MAAU,yEAGjD,CAEA,sBACA,UACA,aACA,mBAAuC,MAAU,uEACjD,eACA,mBAAuC,MAAU,wEAGjD,CAEA,EACA,EAAe,UACf,oBACA,WACA,uBACA,6CACA,kDAEA,SAGA,QAEA,CAAC,EAAC,gBC7HF,iCACA,mEAA2E,GAAM,UAAU,EAAK,EAChG,CACA,gBACA,iBACA,CACA,cACA,mCACA,iCCNA,MAAe,eACf,4BACA,uBACA,gBAAgC,OAAe,4CAE/C,wCAAqD,YAAa,YAAc,cAAiB,QACjG,CAEA,ODgBO,gBACP,CClBqB,MDkBrB,GACA,YACA,YACA,aACA,0BACA,gBACA,8BAEA,GADA,sBACA,EACA,eAAsC,EAAS,oBAC/C,KACA,CACA,YACA,YACA,aACA,uCACA,6BACA,8BAEA,GADA,sBACA,EACA,eAAsC,EAAS,oBAC/C,KACA,CACA,YACA,YACA,aACA,6BACA,mBACA,8BAEA,GADA,sBACA,EACA,eAAsC,EAAS,oBAC/C,KACA,CACA,cACA,YACA,6BACA,mBACA,KAEA,aACA,YACA,aACA,2BACA,iBACA,MA/DA,YACA,UACA,YACA,aACA,aACA,aACA,aACA,aACA,SACA,0BACA,CACA,EAoDA,GAEA,GADA,yBACA,EACA,kCACA,KACA,CACA,QACA,4DACA,CACA,CA5DA,cACA,4BACA,sFAAkG,EAAM,IAExG,EAwDA,IACA,ECzEqB,OACrB,CACA,CAAC,EAAC,gBCXF,qBAEA,GADA,sBACA,UACA,cACA,kBAA8B,aAAiB,OAAO,EAAK,QAE3D,aACA,kBAA8B,MAAU,KAAK,KAAS,GAGtD,cAA0B,KAAS,GAanC,OAXA,QACA,gBAA4B,EAAO,EAEnC,6BACA,yBAAqC,OAAY,EAEjD,6BACA,qBACA,gCAA+C,oBAAwB,EAGvE,CACA,0BACA,MAAe,UACf,yBAEO,qBACP,wBAAkC,GAAK,2BACvC,oCC9BA,MAAe,aAKf,EAJA,wBACA,8BACA,SAGA,gBACA,qBACA,mBACA,aACA,QACA,CACA,gBACA,YACA,SAEA,QACA,CACA,CACA,QACA,CAAC,EAAC,2ECnBK,cACP,MAAW,OAAQ,2BACnB,CACO,cACP,0CAEO,cACP,kCAEO,cACP,4DCNO,cACP,4CACA,CACO,cACP,4CACA,qCACA,MAAe,GACf,6CCTA,MAAe,IACf,IAJA,YACA,kCACA,EAEA,0DACA,SAEA,mCACA,SAEA,QACA,sCACA,2BAEA,mCACA,CAAC,EAAC,mECfF,MAAe,+BAA2C,ECK1D,oIACA,EAAe,QAOf,EANA,gBACA,kBACA,8CAEA,uBAGA,OAFA,oBAGA,UACA,WACA,aACA,cACA,QACA,gBACA,KACA,cACA,cACA,UACA,WACA,QACA,mBACA,KACA,YACA,YACA,SACA,UACA,QACA,qBACA,KACA,WACA,WACA,QACA,sBACA,KACA,YACA,YACA,QACA,uBACA,KACA,SACA,yBAEA,OACA,yBACA,GAEA,CACA,CAAC,EAAC,cChDF,gBACA,uBACA,2BAAuC,GAAO,QAE9C,QACA,CACA,SACA,gBACA,gBAEA,eAA0B,gBAAoB,EAE9C,SACA,mBACA,gBAEA,kBACA,2CAIO,mBAAwE,MAC/E,EAyCA,EAxCA,IACA,aAA6B,IAAO,WACpC,CACA,MACA,CACA,IAAS,OAAQ,IACjB,UAAkB,IAAU,mDAE5B,QAAY,GAAM,EAClB,MACA,yBACA,iBACA,UAAkB,IAAwB,6DAE1C,mBAAY,kDAA8D,EAC1E,SASA,aARA,YACA,cACA,YACA,cACA,YACA,cACA,YACA,cACA,sBACA,aACA,UAAsB,IAAwB,sBAAsB,EAAM,wBAG1E,MACA,0CACA,UAAkB,IAAwB,wDAE1C,gBACA,UAAkB,IAAwB,wDAE1C,MACA,mCACA,UAAkB,IAAwB,wDAG1C,gCACA,aACA,EAAwB,EAAI,kBAC5B,KACA,cACA,mBACA,KACA,iBACA,IACA,KACA,SACA,qDACA,CACA,gBAAY,GAAc,EAC1B,EAAgB,EAAK,aACrB,+CACA,UAAkB,IAAwB,mDAE1C,mBACA,0BACA,UAAsB,IAAwB,mDAE9C,aACA,UAAsB,IAAwB,6DAE9C,CACA,mBACA,0BACA,UAAsB,IAAwB,mDAE9C,cACA,UAAsB,GAAU,6DAEhC,CACA,MACA,cAEA,OADA,sBAAoE,EAAI,IAExE,UAAsB,GAAU,oFAEhC,SACA,UAAsB,IAAwB,wFAE9C,CACA,QACA,CACO,QACP,eACA,GACA,IAAa,OAAQ,IACrB,mDAEA,2BACA,CACA,OACA,OAAe,IAAO,gCACtB,CACA,UACA,mBAEA,WACA,aACA,CACA,UACA,mBAEA,WACA,aACA,CACA,UACA,mBAEA,WACA,aACA,CACA,WACA,aACA,CACA,WACA,mBACA,gCAEA,kBACA,6BAA8D,EAAK,IAGnE,YAAgC,EAAK,UAAe,EAAI,GAGxD,WACA,mBACA,qCAEA,kBACA,kCAAmE,EAAK,IAGxE,YAAgC,EAAK,UAAe,EAAI,GAGxD,WACA,WACA,YAAgC,EAAK,UAErC,kBACA,4BAA6D,EAAK,IAElE,mBACA,4BAA6D,EAAK,UAAe,EAAI,IAGrF,8BAEA,CACA,uBEpLA,uDDgFA,MAAe,UACf,UACA,4EAEA,IAAY,yBAvFZ,YACA,MACA,EACA,cACA,UACA,cACA,YACA,YACA,YACA,GAAkC,2BAA8B,gBAAkB,GAClF,0BACA,KACA,aACA,YACA,YACA,GAAkC,qCAAwC,gBAAkB,GAC5F,0BACA,KACA,gBACA,mBACA,mBACA,mBACA,GACA,gBACA,YAAqC,gCAAqC,CAC1E,EACA,oDACA,KACA,SACA,UAA8B,IAAgB,gEAC9C,CACA,KAEA,UACA,cACA,YACA,GAAkC,iCAClC,0BACA,KACA,aACA,GAAkC,iCAClC,0BACA,KACA,aACA,GAAkC,iCAClC,0BACA,KACA,eACA,qBACA,qBACA,qBACA,GAAkC,8BAClC,wBACA,KACA,SACA,UAA8B,IAAgB,gEAC9C,CACA,KAEA,WACA,cACA,cACA,YACA,GAAkC,gBAClC,0BACA,KACA,eACA,qBACA,qBACA,qBACA,GAAkC,YAClC,wBACA,KACA,SACA,UAA8B,IAAgB,gEAC9C,CACA,KAEA,SACA,UAAsB,IAAgB,+DACtC,CACA,iBAAa,cACb,EAKmC,GACnC,GAAsB,MAGtB,OAFA,aACA,aACA,2DACA,CAAC,EAAC,cCxFF,0BAEA,MADA,kBACA,OACA,UACA,YAEA,YAA4B,EAAS,CAAG,OAAH,EAAG,EAAa,EASrD,OARA,GACA,iBACA,EAIA,OAHA,SAAyB,MAAkB,EAK3C,CACA,EACA,cAQA,EANA,MADA,kBACA,OACA,UACA,YAEA,wBACA,MAEA,mCACA,UACA,cACA,qBACA,qBACA,qBACA,KACA,SACA,6EACA,CACA,0DACA,CACA,oCACA,8BACA,8EAEA,uCACA,kBACA,CACA,CACA,gCACA,MACA,UACA,eACA,UACA,KACA,aACA,YACA,mBACA,YACA,KACA,aACA,YACA,mBACA,YACA,KACA,aACA,YACA,mBACA,YACA,KACA,SACA,6EACA,CACA,4BACA,sBACA,gBACA,MACA,CAAa,8BAEb,iBACA,sDACA,MACA,CAAS,uBACT,CACA,+BAMA,MALA,SACA,uBACA,sBACA,sBACA,EACA,wCACA,MACA,6EAEA,2BACA,kBACA,aACA,YACA,CAAa,yBAEb,0BACA,kBACA,aACA,YACA,CAAa,yBAEb,0BACA,kBACA,aACA,YACA,CAAa,yBAEb,yBACA,kBACA,YACA,YACA,CAAa,wBAEb,CACA,MACA,8EAQA,OANA,EAIA,OAHA,SAA+B,MAAkB,EAKjD,CACA,EACA,EAAe,aACf,4BAGQ,QAAW,IAFnB,SAKA,GAAQ,QAAW,KACnB,qBACA,kBAEA,uDACA,IACA,aACA,CACA,SACA,0BACA,OAEA,CAEA,gBAA+B,aAAe,EAC9C,eACA,CACA,GAAQ,QAAK,WACb,IACmB,OAAM,MAEzB,WAEA,2BACA,CAAC,EAAC,gDClKF,MAAe,QACf,aAAwB,YAAc,EACtC,UACA,YACA,YACA,YACA,YAAqB,cACrB,aACA,YACA,YACA,YAAqB,wDACrB,aACA,YACA,YACA,YAAqB,2BACrB,aACA,YACA,YACA,YAAqB,uCACrB,eACA,YACA,OAAqB,eACrB,SACA,UAAsB,IAAgB,QAAQ,GAAK,4DACnD,CACA,CAAC,EAAC,gDCzBF,MAAe,kBAYf,EAXA,qCACA,8EAEA,uBACA,eAEA,2BACA,mBACA,iDACA,qGASA,aALA,EADA,WACA,+CAGA,EAEA,SACA,aACA,UAAsB,IAAgB,gCAAgC,EAAU,sBAEhF,iBACA,2CAAyD,EAAU,eAEnE,2BACA,2CAAyD,EAAU,+BAEnE,CACA,sBACA,CAAC,EAAC,wDE9BK,cACP,yBACA,kDAAyE,IAAO,YAChF,oBACA,CAAS,EAET,QACA,yBACA,GAAkB,IAAO,YAEzB,0DACA,IACA,ODHO,YACP,yBACA,gCAEA,cACA,2BACA,YAAoB,WAAmB,IACvC,qBAEA,QACA,ECP2B,EAC3B,CACA,MACA,oEACA,CACA,CACO,cACP,cAIA,CAHA,oBACA,GAAoB,IAAO,YAE3B,+BACA,YAAoC,oCAA0C,EAEnE,CD5BJ,WC4BgB,CD3BvB,iCACA,oBAGA,SACA,YAAoB,WAAkB,GAFtC,MAGA,qDAHA,SAKA,uBACA,GCkBuB,yDACvB,yFC7BO,uBACP,+BACA,oCACA,KACA,WACA,gCACA,gDACA,CACA,CACO,kBACP,8CACA,sCACA,OACA,OACA,oBACA,qCACA,SAAyB,OAAS,4BAA0B,EAC5D,aACA,cACA,cACA,CACA,CACO,kBACP,8BACA,uBACA,MACA,OACA,oBACA,qCACA,SAAyB,aAAS,sBAA0B,EAC5D,aACA,cACA,cACA,CACA,CACO,kBACP,uCACA,gCAEO,kBACP,qCACA,6BACA,CAYO,kBACP,8BACA,uBAEO,kBACP,6BACA,wBAiBO,kBACP,8BACA,uCACA,mDACA,4DACA,UACA,CACA,CAQO,kBACP,oDACA,yDACA,qCACA,UACA,CACA", "sources": ["webpack://_N_E/./node_modules/drizzle-orm/sql/functions/aggregate.js", "webpack://_N_E/./node_modules/jose/dist/webapi/lib/sign.js", "webpack://_N_E/./node_modules/jose/dist/webapi/jws/flattened/sign.js", "webpack://_N_E/./node_modules/jose/dist/webapi/jws/compact/sign.js", "webpack://_N_E/./node_modules/jose/dist/webapi/jwt/sign.js", "webpack://_N_E/./node_modules/jose/dist/webapi/lib/verify.js", "webpack://_N_E/./node_modules/jose/dist/webapi/lib/validate_algorithms.js", "webpack://_N_E/./node_modules/jose/dist/webapi/jws/flattened/verify.js", "webpack://_N_E/./node_modules/jose/dist/webapi/jws/compact/verify.js", "webpack://_N_E/./node_modules/jose/dist/webapi/jwt/verify.js", "webpack://_N_E/./node_modules/jose/dist/webapi/lib/buffer_utils.js", "webpack://_N_E/./node_modules/jose/dist/webapi/lib/check_key_length.js", "webpack://_N_E/./node_modules/jose/dist/webapi/lib/check_key_type.js", "webpack://_N_E/./node_modules/jose/dist/webapi/lib/crypto_key.js", "webpack://_N_E/./node_modules/jose/dist/webapi/lib/get_sign_verify_key.js", "webpack://_N_E/./node_modules/jose/dist/webapi/lib/invalid_key_input.js", "webpack://_N_E/./node_modules/jose/dist/webapi/lib/is_disjoint.js", "webpack://_N_E/./node_modules/jose/dist/webapi/lib/is_jwk.js", "webpack://_N_E/./node_modules/jose/dist/webapi/lib/is_key_like.js", "webpack://_N_E/./node_modules/jose/dist/webapi/lib/is_object.js", "webpack://_N_E/./node_modules/jose/dist/webapi/lib/epoch.js", "webpack://_N_E/./node_modules/jose/dist/webapi/lib/secs.js", "webpack://_N_E/./node_modules/jose/dist/webapi/lib/jwt_claims_set.js", "webpack://_N_E/./node_modules/jose/dist/webapi/lib/jwk_to_key.js", "webpack://_N_E/./node_modules/jose/dist/webapi/lib/normalize_key.js", "webpack://_N_E/./node_modules/jose/dist/webapi/lib/subtle_dsa.js", "webpack://_N_E/./node_modules/jose/dist/webapi/lib/validate_crit.js", "webpack://_N_E/./node_modules/jose/dist/webapi/lib/base64.js", "webpack://_N_E/./node_modules/jose/dist/webapi/util/base64url.js", "webpack://_N_E/./node_modules/jose/dist/webapi/util/errors.js"], "sourcesContent": ["import { Column } from \"../../column.js\";\nimport { is } from \"../../entity.js\";\nimport { sql } from \"../sql.js\";\nfunction count(expression) {\n  return sql`count(${expression || sql.raw(\"*\")})`.mapWith(Number);\n}\nfunction countDistinct(expression) {\n  return sql`count(distinct ${expression})`.mapWith(Number);\n}\nfunction avg(expression) {\n  return sql`avg(${expression})`.mapWith(String);\n}\nfunction avgDistinct(expression) {\n  return sql`avg(distinct ${expression})`.mapWith(String);\n}\nfunction sum(expression) {\n  return sql`sum(${expression})`.mapWith(String);\n}\nfunction sumDistinct(expression) {\n  return sql`sum(distinct ${expression})`.mapWith(String);\n}\nfunction max(expression) {\n  return sql`max(${expression})`.mapWith(is(expression, Column) ? expression : String);\n}\nfunction min(expression) {\n  return sql`min(${expression})`.mapWith(is(expression, Column) ? expression : String);\n}\nexport {\n  avg,\n  avgDistinct,\n  count,\n  countDistinct,\n  max,\n  min,\n  sum,\n  sumDistinct\n};\n//# sourceMappingURL=aggregate.js.map", "import subtleAlgorithm from './subtle_dsa.js';\nimport checkKey<PERSON>ength from './check_key_length.js';\nimport getSignKey from './get_sign_verify_key.js';\nexport default async (alg, key, data) => {\n    const cryptoKey = await getSignKey(alg, key, 'sign');\n    checkKeyLength(alg, cryptoKey);\n    const signature = await crypto.subtle.sign(subtleAlgorithm(alg, cryptoKey.algorithm), cryptoKey, data);\n    return new Uint8Array(signature);\n};\n", "import { encode as b64u } from '../../util/base64url.js';\nimport sign from '../../lib/sign.js';\nimport isDisjoint from '../../lib/is_disjoint.js';\nimport { JWSInvalid } from '../../util/errors.js';\nimport { encoder, decoder, concat } from '../../lib/buffer_utils.js';\nimport checkKeyType from '../../lib/check_key_type.js';\nimport validateCrit from '../../lib/validate_crit.js';\nimport normalizeKey from '../../lib/normalize_key.js';\nexport class FlattenedSign {\n    #payload;\n    #protectedHeader;\n    #unprotectedHeader;\n    constructor(payload) {\n        if (!(payload instanceof Uint8Array)) {\n            throw new TypeError('payload must be an instance of Uint8Array');\n        }\n        this.#payload = payload;\n    }\n    setProtectedHeader(protectedHeader) {\n        if (this.#protectedHeader) {\n            throw new TypeError('setProtectedHeader can only be called once');\n        }\n        this.#protectedHeader = protectedHeader;\n        return this;\n    }\n    setUnprotectedHeader(unprotectedHeader) {\n        if (this.#unprotectedHeader) {\n            throw new TypeError('setUnprotectedHeader can only be called once');\n        }\n        this.#unprotectedHeader = unprotectedHeader;\n        return this;\n    }\n    async sign(key, options) {\n        if (!this.#protectedHeader && !this.#unprotectedHeader) {\n            throw new JWSInvalid('either setProtectedHeader or setUnprotectedHeader must be called before #sign()');\n        }\n        if (!isDisjoint(this.#protectedHeader, this.#unprotectedHeader)) {\n            throw new JWSInvalid('JWS Protected and JWS Unprotected Header Parameter names must be disjoint');\n        }\n        const joseHeader = {\n            ...this.#protectedHeader,\n            ...this.#unprotectedHeader,\n        };\n        const extensions = validateCrit(JWSInvalid, new Map([['b64', true]]), options?.crit, this.#protectedHeader, joseHeader);\n        let b64 = true;\n        if (extensions.has('b64')) {\n            b64 = this.#protectedHeader.b64;\n            if (typeof b64 !== 'boolean') {\n                throw new JWSInvalid('The \"b64\" (base64url-encode payload) Header Parameter must be a boolean');\n            }\n        }\n        const { alg } = joseHeader;\n        if (typeof alg !== 'string' || !alg) {\n            throw new JWSInvalid('JWS \"alg\" (Algorithm) Header Parameter missing or invalid');\n        }\n        checkKeyType(alg, key, 'sign');\n        let payload = this.#payload;\n        if (b64) {\n            payload = encoder.encode(b64u(payload));\n        }\n        let protectedHeader;\n        if (this.#protectedHeader) {\n            protectedHeader = encoder.encode(b64u(JSON.stringify(this.#protectedHeader)));\n        }\n        else {\n            protectedHeader = encoder.encode('');\n        }\n        const data = concat(protectedHeader, encoder.encode('.'), payload);\n        const k = await normalizeKey(key, alg);\n        const signature = await sign(alg, k, data);\n        const jws = {\n            signature: b64u(signature),\n            payload: '',\n        };\n        if (b64) {\n            jws.payload = decoder.decode(payload);\n        }\n        if (this.#unprotectedHeader) {\n            jws.header = this.#unprotectedHeader;\n        }\n        if (this.#protectedHeader) {\n            jws.protected = decoder.decode(protectedHeader);\n        }\n        return jws;\n    }\n}\n", "import { FlattenedSign } from '../flattened/sign.js';\nexport class CompactSign {\n    #flattened;\n    constructor(payload) {\n        this.#flattened = new FlattenedSign(payload);\n    }\n    setProtectedHeader(protectedHeader) {\n        this.#flattened.setProtectedHeader(protectedHeader);\n        return this;\n    }\n    async sign(key, options) {\n        const jws = await this.#flattened.sign(key, options);\n        if (jws.payload === undefined) {\n            throw new TypeError('use the flattened module for creating JWS with b64: false');\n        }\n        return `${jws.protected}.${jws.payload}.${jws.signature}`;\n    }\n}\n", "import { CompactSign } from '../jws/compact/sign.js';\nimport { JWTInvalid } from '../util/errors.js';\nimport { JWTClaimsBuilder } from '../lib/jwt_claims_set.js';\nexport class SignJWT {\n    #protectedHeader;\n    #jwt;\n    constructor(payload = {}) {\n        this.#jwt = new JWTClaimsBuilder(payload);\n    }\n    setIssuer(issuer) {\n        this.#jwt.iss = issuer;\n        return this;\n    }\n    setSubject(subject) {\n        this.#jwt.sub = subject;\n        return this;\n    }\n    setAudience(audience) {\n        this.#jwt.aud = audience;\n        return this;\n    }\n    setJti(jwtId) {\n        this.#jwt.jti = jwtId;\n        return this;\n    }\n    setNotBefore(input) {\n        this.#jwt.nbf = input;\n        return this;\n    }\n    setExpirationTime(input) {\n        this.#jwt.exp = input;\n        return this;\n    }\n    setIssuedAt(input) {\n        this.#jwt.iat = input;\n        return this;\n    }\n    setProtectedHeader(protectedHeader) {\n        this.#protectedHeader = protectedHeader;\n        return this;\n    }\n    async sign(key, options) {\n        const sig = new CompactSign(this.#jwt.data());\n        sig.setProtectedHeader(this.#protectedHeader);\n        if (Array.isArray(this.#protectedHeader?.crit) &&\n            this.#protectedHeader.crit.includes('b64') &&\n            this.#protectedHeader.b64 === false) {\n            throw new JWTInvalid('JWTs MUST NOT use unencoded payload');\n        }\n        return sig.sign(key, options);\n    }\n}\n", "import subtleAlgorithm from './subtle_dsa.js';\nimport check<PERSON><PERSON><PERSON>ength from './check_key_length.js';\nimport getVerifyKey from './get_sign_verify_key.js';\nexport default async (alg, key, signature, data) => {\n    const cryptoKey = await getVerifyKey(alg, key, 'verify');\n    checkKeyLength(alg, cryptoKey);\n    const algorithm = subtleAlgorithm(alg, cryptoKey.algorithm);\n    try {\n        return await crypto.subtle.verify(algorithm, cryptoKey, signature, data);\n    }\n    catch {\n        return false;\n    }\n};\n", "export default (option, algorithms) => {\n    if (algorithms !== undefined &&\n        (!Array.isArray(algorithms) || algorithms.some((s) => typeof s !== 'string'))) {\n        throw new TypeError(`\"${option}\" option must be an array of strings`);\n    }\n    if (!algorithms) {\n        return undefined;\n    }\n    return new Set(algorithms);\n};\n", "import { decode as b64u } from '../../util/base64url.js';\nimport verify from '../../lib/verify.js';\nimport { JOSEAlgNotAllowed, JWSInvalid, JWSSignatureVerificationFailed } from '../../util/errors.js';\nimport { concat, encoder, decoder } from '../../lib/buffer_utils.js';\nimport isDisjoint from '../../lib/is_disjoint.js';\nimport isObject from '../../lib/is_object.js';\nimport checkKeyType from '../../lib/check_key_type.js';\nimport validateCrit from '../../lib/validate_crit.js';\nimport validateAlgorithms from '../../lib/validate_algorithms.js';\nimport normalizeKey from '../../lib/normalize_key.js';\nexport async function flattenedVerify(jws, key, options) {\n    if (!isObject(jws)) {\n        throw new JWSInvalid('Flattened JWS must be an object');\n    }\n    if (jws.protected === undefined && jws.header === undefined) {\n        throw new JWSInvalid('Flattened JWS must have either of the \"protected\" or \"header\" members');\n    }\n    if (jws.protected !== undefined && typeof jws.protected !== 'string') {\n        throw new JWSInvalid('JWS Protected Header incorrect type');\n    }\n    if (jws.payload === undefined) {\n        throw new JWSInvalid('JWS Payload missing');\n    }\n    if (typeof jws.signature !== 'string') {\n        throw new JWSInvalid('JWS Signature missing or incorrect type');\n    }\n    if (jws.header !== undefined && !isObject(jws.header)) {\n        throw new JWSInvalid('JWS Unprotected Header incorrect type');\n    }\n    let parsedProt = {};\n    if (jws.protected) {\n        try {\n            const protectedHeader = b64u(jws.protected);\n            parsedProt = JSON.parse(decoder.decode(protectedHeader));\n        }\n        catch {\n            throw new JWSInvalid('JWS Protected Header is invalid');\n        }\n    }\n    if (!isDisjoint(parsedProt, jws.header)) {\n        throw new JWSInvalid('JWS Protected and JWS Unprotected Header Parameter names must be disjoint');\n    }\n    const joseHeader = {\n        ...parsedProt,\n        ...jws.header,\n    };\n    const extensions = validateCrit(JWSInvalid, new Map([['b64', true]]), options?.crit, parsedProt, joseHeader);\n    let b64 = true;\n    if (extensions.has('b64')) {\n        b64 = parsedProt.b64;\n        if (typeof b64 !== 'boolean') {\n            throw new JWSInvalid('The \"b64\" (base64url-encode payload) Header Parameter must be a boolean');\n        }\n    }\n    const { alg } = joseHeader;\n    if (typeof alg !== 'string' || !alg) {\n        throw new JWSInvalid('JWS \"alg\" (Algorithm) Header Parameter missing or invalid');\n    }\n    const algorithms = options && validateAlgorithms('algorithms', options.algorithms);\n    if (algorithms && !algorithms.has(alg)) {\n        throw new JOSEAlgNotAllowed('\"alg\" (Algorithm) Header Parameter value not allowed');\n    }\n    if (b64) {\n        if (typeof jws.payload !== 'string') {\n            throw new JWSInvalid('JWS Payload must be a string');\n        }\n    }\n    else if (typeof jws.payload !== 'string' && !(jws.payload instanceof Uint8Array)) {\n        throw new JWSInvalid('JWS Payload must be a string or an Uint8Array instance');\n    }\n    let resolvedKey = false;\n    if (typeof key === 'function') {\n        key = await key(parsedProt, jws);\n        resolvedKey = true;\n    }\n    checkKeyType(alg, key, 'verify');\n    const data = concat(encoder.encode(jws.protected ?? ''), encoder.encode('.'), typeof jws.payload === 'string' ? encoder.encode(jws.payload) : jws.payload);\n    let signature;\n    try {\n        signature = b64u(jws.signature);\n    }\n    catch {\n        throw new JWSInvalid('Failed to base64url decode the signature');\n    }\n    const k = await normalizeKey(key, alg);\n    const verified = await verify(alg, k, signature, data);\n    if (!verified) {\n        throw new JWSSignatureVerificationFailed();\n    }\n    let payload;\n    if (b64) {\n        try {\n            payload = b64u(jws.payload);\n        }\n        catch {\n            throw new JWSInvalid('Failed to base64url decode the payload');\n        }\n    }\n    else if (typeof jws.payload === 'string') {\n        payload = encoder.encode(jws.payload);\n    }\n    else {\n        payload = jws.payload;\n    }\n    const result = { payload };\n    if (jws.protected !== undefined) {\n        result.protectedHeader = parsedProt;\n    }\n    if (jws.header !== undefined) {\n        result.unprotectedHeader = jws.header;\n    }\n    if (resolvedKey) {\n        return { ...result, key: k };\n    }\n    return result;\n}\n", "import { flattenedVerify } from '../flattened/verify.js';\nimport { JWSInvalid } from '../../util/errors.js';\nimport { decoder } from '../../lib/buffer_utils.js';\nexport async function compactVerify(jws, key, options) {\n    if (jws instanceof Uint8Array) {\n        jws = decoder.decode(jws);\n    }\n    if (typeof jws !== 'string') {\n        throw new JWSInvalid('Compact JWS must be a string or Uint8Array');\n    }\n    const { 0: protectedHeader, 1: payload, 2: signature, length } = jws.split('.');\n    if (length !== 3) {\n        throw new JWSInvalid('Invalid Compact JWS');\n    }\n    const verified = await flattenedVerify({ payload, protected: protectedHeader, signature }, key, options);\n    const result = { payload: verified.payload, protectedHeader: verified.protectedHeader };\n    if (typeof key === 'function') {\n        return { ...result, key: verified.key };\n    }\n    return result;\n}\n", "import { compactVerify } from '../jws/compact/verify.js';\nimport { validateClaimsSet } from '../lib/jwt_claims_set.js';\nimport { JWTInvalid } from '../util/errors.js';\nexport async function jwtVerify(jwt, key, options) {\n    const verified = await compactVerify(jwt, key, options);\n    if (verified.protectedHeader.crit?.includes('b64') && verified.protectedHeader.b64 === false) {\n        throw new JWTInvalid('JWTs MUST NOT use unencoded payload');\n    }\n    const payload = validateClaimsSet(verified.protectedHeader, verified.payload, options);\n    const result = { payload, protectedHeader: verified.protectedHeader };\n    if (typeof key === 'function') {\n        return { ...result, key: verified.key };\n    }\n    return result;\n}\n", "export const encoder = new TextEncoder();\nexport const decoder = new TextDecoder();\nconst MAX_INT32 = 2 ** 32;\nexport function concat(...buffers) {\n    const size = buffers.reduce((acc, { length }) => acc + length, 0);\n    const buf = new Uint8Array(size);\n    let i = 0;\n    for (const buffer of buffers) {\n        buf.set(buffer, i);\n        i += buffer.length;\n    }\n    return buf;\n}\nfunction writeUInt32BE(buf, value, offset) {\n    if (value < 0 || value >= MAX_INT32) {\n        throw new RangeError(`value must be >= 0 and <= ${MAX_INT32 - 1}. Received ${value}`);\n    }\n    buf.set([value >>> 24, value >>> 16, value >>> 8, value & 0xff], offset);\n}\nexport function uint64be(value) {\n    const high = Math.floor(value / MAX_INT32);\n    const low = value % MAX_INT32;\n    const buf = new Uint8Array(8);\n    writeUInt32BE(buf, high, 0);\n    writeUInt32BE(buf, low, 4);\n    return buf;\n}\nexport function uint32be(value) {\n    const buf = new Uint8Array(4);\n    writeUInt32BE(buf, value);\n    return buf;\n}\n", "export default (alg, key) => {\n    if (alg.startsWith('RS') || alg.startsWith('PS')) {\n        const { modulusLength } = key.algorithm;\n        if (typeof modulusLength !== 'number' || modulusLength < 2048) {\n            throw new TypeError(`${alg} requires key modulusLength to be 2048 bits or larger`);\n        }\n    }\n};\n", "import { withAlg as invalidKeyInput } from './invalid_key_input.js';\nimport isKey<PERSON>ike from './is_key_like.js';\nimport * as jwk from './is_jwk.js';\nconst tag = (key) => key?.[Symbol.toStringTag];\nconst jwkMatchesOp = (alg, key, usage) => {\n    if (key.use !== undefined) {\n        let expected;\n        switch (usage) {\n            case 'sign':\n            case 'verify':\n                expected = 'sig';\n                break;\n            case 'encrypt':\n            case 'decrypt':\n                expected = 'enc';\n                break;\n        }\n        if (key.use !== expected) {\n            throw new TypeError(`Invalid key for this operation, its \"use\" must be \"${expected}\" when present`);\n        }\n    }\n    if (key.alg !== undefined && key.alg !== alg) {\n        throw new TypeError(`Invalid key for this operation, its \"alg\" must be \"${alg}\" when present`);\n    }\n    if (Array.isArray(key.key_ops)) {\n        let expectedKeyOp;\n        switch (true) {\n            case usage === 'sign' || usage === 'verify':\n            case alg === 'dir':\n            case alg.includes('CBC-HS'):\n                expectedKeyOp = usage;\n                break;\n            case alg.startsWith('PBES2'):\n                expectedKeyOp = 'deriveBits';\n                break;\n            case /^A\\d{3}(?:GCM)?(?:KW)?$/.test(alg):\n                if (!alg.includes('GCM') && alg.endsWith('KW')) {\n                    expectedKeyOp = usage === 'encrypt' ? 'wrapKey' : 'unwrapKey';\n                }\n                else {\n                    expectedKeyOp = usage;\n                }\n                break;\n            case usage === 'encrypt' && alg.startsWith('RSA'):\n                expectedKeyOp = 'wrapKey';\n                break;\n            case usage === 'decrypt':\n                expectedKeyOp = alg.startsWith('RSA') ? 'unwrapKey' : 'deriveBits';\n                break;\n        }\n        if (expectedKeyOp && key.key_ops?.includes?.(expectedKeyOp) === false) {\n            throw new TypeError(`Invalid key for this operation, its \"key_ops\" must include \"${expectedKeyOp}\" when present`);\n        }\n    }\n    return true;\n};\nconst symmetricTypeCheck = (alg, key, usage) => {\n    if (key instanceof Uint8Array)\n        return;\n    if (jwk.isJWK(key)) {\n        if (jwk.isSecretJWK(key) && jwkMatchesOp(alg, key, usage))\n            return;\n        throw new TypeError(`JSON Web Key for symmetric algorithms must have JWK \"kty\" (Key Type) equal to \"oct\" and the JWK \"k\" (Key Value) present`);\n    }\n    if (!isKeyLike(key)) {\n        throw new TypeError(invalidKeyInput(alg, key, 'CryptoKey', 'KeyObject', 'JSON Web Key', 'Uint8Array'));\n    }\n    if (key.type !== 'secret') {\n        throw new TypeError(`${tag(key)} instances for symmetric algorithms must be of type \"secret\"`);\n    }\n};\nconst asymmetricTypeCheck = (alg, key, usage) => {\n    if (jwk.isJWK(key)) {\n        switch (usage) {\n            case 'decrypt':\n            case 'sign':\n                if (jwk.isPrivateJWK(key) && jwkMatchesOp(alg, key, usage))\n                    return;\n                throw new TypeError(`JSON Web Key for this operation be a private JWK`);\n            case 'encrypt':\n            case 'verify':\n                if (jwk.isPublicJWK(key) && jwkMatchesOp(alg, key, usage))\n                    return;\n                throw new TypeError(`JSON Web Key for this operation be a public JWK`);\n        }\n    }\n    if (!isKeyLike(key)) {\n        throw new TypeError(invalidKeyInput(alg, key, 'CryptoKey', 'KeyObject', 'JSON Web Key'));\n    }\n    if (key.type === 'secret') {\n        throw new TypeError(`${tag(key)} instances for asymmetric algorithms must not be of type \"secret\"`);\n    }\n    if (key.type === 'public') {\n        switch (usage) {\n            case 'sign':\n                throw new TypeError(`${tag(key)} instances for asymmetric algorithm signing must be of type \"private\"`);\n            case 'decrypt':\n                throw new TypeError(`${tag(key)} instances for asymmetric algorithm decryption must be of type \"private\"`);\n            default:\n                break;\n        }\n    }\n    if (key.type === 'private') {\n        switch (usage) {\n            case 'verify':\n                throw new TypeError(`${tag(key)} instances for asymmetric algorithm verifying must be of type \"public\"`);\n            case 'encrypt':\n                throw new TypeError(`${tag(key)} instances for asymmetric algorithm encryption must be of type \"public\"`);\n            default:\n                break;\n        }\n    }\n};\nexport default (alg, key, usage) => {\n    const symmetric = alg.startsWith('HS') ||\n        alg === 'dir' ||\n        alg.startsWith('PBES2') ||\n        /^A(?:128|192|256)(?:GCM)?(?:KW)?$/.test(alg) ||\n        /^A(?:128|192|256)CBC-HS(?:256|384|512)$/.test(alg);\n    if (symmetric) {\n        symmetricTypeCheck(alg, key, usage);\n    }\n    else {\n        asymmetricTypeCheck(alg, key, usage);\n    }\n};\n", "function unusable(name, prop = 'algorithm.name') {\n    return new TypeError(`CryptoKey does not support this operation, its ${prop} must be ${name}`);\n}\nfunction isAlgorithm(algorithm, name) {\n    return algorithm.name === name;\n}\nfunction getHashLength(hash) {\n    return parseInt(hash.name.slice(4), 10);\n}\nfunction getNamedCurve(alg) {\n    switch (alg) {\n        case 'ES256':\n            return 'P-256';\n        case 'ES384':\n            return 'P-384';\n        case 'ES512':\n            return 'P-521';\n        default:\n            throw new Error('unreachable');\n    }\n}\nfunction checkUsage(key, usage) {\n    if (usage && !key.usages.includes(usage)) {\n        throw new TypeError(`CryptoKey does not support this operation, its usages must include ${usage}.`);\n    }\n}\nexport function checkSigCryptoKey(key, alg, usage) {\n    switch (alg) {\n        case 'HS256':\n        case 'HS384':\n        case 'HS512': {\n            if (!isAlgorithm(key.algorithm, 'HMAC'))\n                throw unusable('HMAC');\n            const expected = parseInt(alg.slice(2), 10);\n            const actual = getHashLength(key.algorithm.hash);\n            if (actual !== expected)\n                throw unusable(`SHA-${expected}`, 'algorithm.hash');\n            break;\n        }\n        case 'RS256':\n        case 'RS384':\n        case 'RS512': {\n            if (!isAlgorithm(key.algorithm, 'RSASSA-PKCS1-v1_5'))\n                throw unusable('RSASSA-PKCS1-v1_5');\n            const expected = parseInt(alg.slice(2), 10);\n            const actual = getHashLength(key.algorithm.hash);\n            if (actual !== expected)\n                throw unusable(`SHA-${expected}`, 'algorithm.hash');\n            break;\n        }\n        case 'PS256':\n        case 'PS384':\n        case 'PS512': {\n            if (!isAlgorithm(key.algorithm, 'RSA-PSS'))\n                throw unusable('RSA-PSS');\n            const expected = parseInt(alg.slice(2), 10);\n            const actual = getHashLength(key.algorithm.hash);\n            if (actual !== expected)\n                throw unusable(`SHA-${expected}`, 'algorithm.hash');\n            break;\n        }\n        case 'Ed25519':\n        case 'EdDSA': {\n            if (!isAlgorithm(key.algorithm, 'Ed25519'))\n                throw unusable('Ed25519');\n            break;\n        }\n        case 'ES256':\n        case 'ES384':\n        case 'ES512': {\n            if (!isAlgorithm(key.algorithm, 'ECDSA'))\n                throw unusable('ECDSA');\n            const expected = getNamedCurve(alg);\n            const actual = key.algorithm.namedCurve;\n            if (actual !== expected)\n                throw unusable(expected, 'algorithm.namedCurve');\n            break;\n        }\n        default:\n            throw new TypeError('CryptoKey does not support this operation');\n    }\n    checkUsage(key, usage);\n}\nexport function checkEncCryptoKey(key, alg, usage) {\n    switch (alg) {\n        case 'A128GCM':\n        case 'A192GCM':\n        case 'A256GCM': {\n            if (!isAlgorithm(key.algorithm, 'AES-GCM'))\n                throw unusable('AES-GCM');\n            const expected = parseInt(alg.slice(1, 4), 10);\n            const actual = key.algorithm.length;\n            if (actual !== expected)\n                throw unusable(expected, 'algorithm.length');\n            break;\n        }\n        case 'A128KW':\n        case 'A192KW':\n        case 'A256KW': {\n            if (!isAlgorithm(key.algorithm, 'AES-KW'))\n                throw unusable('AES-KW');\n            const expected = parseInt(alg.slice(1, 4), 10);\n            const actual = key.algorithm.length;\n            if (actual !== expected)\n                throw unusable(expected, 'algorithm.length');\n            break;\n        }\n        case 'ECDH': {\n            switch (key.algorithm.name) {\n                case 'ECDH':\n                case 'X25519':\n                    break;\n                default:\n                    throw unusable('ECDH or X25519');\n            }\n            break;\n        }\n        case 'PBES2-HS256+A128KW':\n        case 'PBES2-HS384+A192KW':\n        case 'PBES2-HS512+A256KW':\n            if (!isAlgorithm(key.algorithm, 'PBKDF2'))\n                throw unusable('PBKDF2');\n            break;\n        case 'RSA-OAEP':\n        case 'RSA-OAEP-256':\n        case 'RSA-OAEP-384':\n        case 'RSA-OAEP-512': {\n            if (!isAlgorithm(key.algorithm, 'RSA-OAEP'))\n                throw unusable('RSA-OAEP');\n            const expected = parseInt(alg.slice(9), 10) || 1;\n            const actual = getHashLength(key.algorithm.hash);\n            if (actual !== expected)\n                throw unusable(`SHA-${expected}`, 'algorithm.hash');\n            break;\n        }\n        default:\n            throw new TypeError('CryptoKey does not support this operation');\n    }\n    checkUsage(key, usage);\n}\n", "import { checkSig<PERSON>rypt<PERSON><PERSON><PERSON> } from './crypto_key.js';\nimport invalidKeyInput from './invalid_key_input.js';\nexport default async (alg, key, usage) => {\n    if (key instanceof Uint8Array) {\n        if (!alg.startsWith('HS')) {\n            throw new TypeError(invalidKeyInput(key, 'CryptoKey', 'KeyObject', 'JSON Web Key'));\n        }\n        return crypto.subtle.importKey('raw', key, { hash: `SHA-${alg.slice(-3)}`, name: 'HM<PERSON>' }, false, [usage]);\n    }\n    checkSigCryptoKey(key, alg, usage);\n    return key;\n};\n", "function message(msg, actual, ...types) {\n    types = types.filter(<PERSON><PERSON><PERSON>);\n    if (types.length > 2) {\n        const last = types.pop();\n        msg += `one of type ${types.join(', ')}, or ${last}.`;\n    }\n    else if (types.length === 2) {\n        msg += `one of type ${types[0]} or ${types[1]}.`;\n    }\n    else {\n        msg += `of type ${types[0]}.`;\n    }\n    if (actual == null) {\n        msg += ` Received ${actual}`;\n    }\n    else if (typeof actual === 'function' && actual.name) {\n        msg += ` Received function ${actual.name}`;\n    }\n    else if (typeof actual === 'object' && actual != null) {\n        if (actual.constructor?.name) {\n            msg += ` Received an instance of ${actual.constructor.name}`;\n        }\n    }\n    return msg;\n}\nexport default (actual, ...types) => {\n    return message('Key must be ', actual, ...types);\n};\nexport function withAlg(alg, actual, ...types) {\n    return message(`Key for the ${alg} algorithm must be `, actual, ...types);\n}\n", "export default (...headers) => {\n    const sources = headers.filter(Boolean);\n    if (sources.length === 0 || sources.length === 1) {\n        return true;\n    }\n    let acc;\n    for (const header of sources) {\n        const parameters = Object.keys(header);\n        if (!acc || acc.size === 0) {\n            acc = new Set(parameters);\n            continue;\n        }\n        for (const parameter of parameters) {\n            if (acc.has(parameter)) {\n                return false;\n            }\n            acc.add(parameter);\n        }\n    }\n    return true;\n};\n", "import isObject from './is_object.js';\nexport function isJWK(key) {\n    return isObject(key) && typeof key.kty === 'string';\n}\nexport function isPrivateJWK(key) {\n    return key.kty !== 'oct' && typeof key.d === 'string';\n}\nexport function isPublicJWK(key) {\n    return key.kty !== 'oct' && typeof key.d === 'undefined';\n}\nexport function isSecretJWK(key) {\n    return key.kty === 'oct' && typeof key.k === 'string';\n}\n", "export function assertCrypto<PERSON><PERSON>(key) {\n    if (!isCrypto<PERSON>ey(key)) {\n        throw new Error('CryptoKey instance expected');\n    }\n}\nexport function isCrypto<PERSON>ey(key) {\n    return key?.[Symbol.toStringTag] === 'CryptoKey';\n}\nexport function isKeyObject(key) {\n    return key?.[Symbol.toStringTag] === 'KeyObject';\n}\nexport default (key) => {\n    return isCryptoKey(key) || isKeyObject(key);\n};\n", "function isObjectLike(value) {\n    return typeof value === 'object' && value !== null;\n}\nexport default (input) => {\n    if (!isObjectLike(input) || Object.prototype.toString.call(input) !== '[object Object]') {\n        return false;\n    }\n    if (Object.getPrototypeOf(input) === null) {\n        return true;\n    }\n    let proto = input;\n    while (Object.getPrototypeOf(proto) !== null) {\n        proto = Object.getPrototypeOf(proto);\n    }\n    return Object.getPrototypeOf(input) === proto;\n};\n", "export default (date) => Math.floor(date.getTime() / 1000);\n", "const minute = 60;\nconst hour = minute * 60;\nconst day = hour * 24;\nconst week = day * 7;\nconst year = day * 365.25;\nconst REGEX = /^(\\+|\\-)? ?(\\d+|\\d+\\.\\d+) ?(seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)(?: (ago|from now))?$/i;\nexport default (str) => {\n    const matched = REGEX.exec(str);\n    if (!matched || (matched[4] && matched[1])) {\n        throw new TypeError('Invalid time period format');\n    }\n    const value = parseFloat(matched[2]);\n    const unit = matched[3].toLowerCase();\n    let numericDate;\n    switch (unit) {\n        case 'sec':\n        case 'secs':\n        case 'second':\n        case 'seconds':\n        case 's':\n            numericDate = Math.round(value);\n            break;\n        case 'minute':\n        case 'minutes':\n        case 'min':\n        case 'mins':\n        case 'm':\n            numericDate = Math.round(value * minute);\n            break;\n        case 'hour':\n        case 'hours':\n        case 'hr':\n        case 'hrs':\n        case 'h':\n            numericDate = Math.round(value * hour);\n            break;\n        case 'day':\n        case 'days':\n        case 'd':\n            numericDate = Math.round(value * day);\n            break;\n        case 'week':\n        case 'weeks':\n        case 'w':\n            numericDate = Math.round(value * week);\n            break;\n        default:\n            numericDate = Math.round(value * year);\n            break;\n    }\n    if (matched[1] === '-' || matched[4] === 'ago') {\n        return -numericDate;\n    }\n    return numericDate;\n};\n", "import { JWTClaimValidationFailed, JW<PERSON>xpired, JWTInvalid } from '../util/errors.js';\nimport { decoder } from './buffer_utils.js';\nimport epoch from './epoch.js';\nimport secs from './secs.js';\nimport isObject from './is_object.js';\nimport { encoder } from './buffer_utils.js';\nfunction validateInput(label, input) {\n    if (!Number.isFinite(input)) {\n        throw new TypeError(`Invalid ${label} input`);\n    }\n    return input;\n}\nconst normalizeTyp = (value) => {\n    if (value.includes('/')) {\n        return value.toLowerCase();\n    }\n    return `application/${value.toLowerCase()}`;\n};\nconst checkAudiencePresence = (audPayload, audOption) => {\n    if (typeof audPayload === 'string') {\n        return audOption.includes(audPayload);\n    }\n    if (Array.isArray(audPayload)) {\n        return audOption.some(Set.prototype.has.bind(new Set(audPayload)));\n    }\n    return false;\n};\nexport function validateClaimsSet(protectedHeader, encodedPayload, options = {}) {\n    let payload;\n    try {\n        payload = JSON.parse(decoder.decode(encodedPayload));\n    }\n    catch {\n    }\n    if (!isObject(payload)) {\n        throw new JWTInvalid('JWT Claims Set must be a top-level JSON object');\n    }\n    const { typ } = options;\n    if (typ &&\n        (typeof protectedHeader.typ !== 'string' ||\n            normalizeTyp(protectedHeader.typ) !== normalizeTyp(typ))) {\n        throw new JWTClaimValidationFailed('unexpected \"typ\" JWT header value', payload, 'typ', 'check_failed');\n    }\n    const { requiredClaims = [], issuer, subject, audience, maxTokenAge } = options;\n    const presenceCheck = [...requiredClaims];\n    if (maxTokenAge !== undefined)\n        presenceCheck.push('iat');\n    if (audience !== undefined)\n        presenceCheck.push('aud');\n    if (subject !== undefined)\n        presenceCheck.push('sub');\n    if (issuer !== undefined)\n        presenceCheck.push('iss');\n    for (const claim of new Set(presenceCheck.reverse())) {\n        if (!(claim in payload)) {\n            throw new JWTClaimValidationFailed(`missing required \"${claim}\" claim`, payload, claim, 'missing');\n        }\n    }\n    if (issuer &&\n        !(Array.isArray(issuer) ? issuer : [issuer]).includes(payload.iss)) {\n        throw new JWTClaimValidationFailed('unexpected \"iss\" claim value', payload, 'iss', 'check_failed');\n    }\n    if (subject && payload.sub !== subject) {\n        throw new JWTClaimValidationFailed('unexpected \"sub\" claim value', payload, 'sub', 'check_failed');\n    }\n    if (audience &&\n        !checkAudiencePresence(payload.aud, typeof audience === 'string' ? [audience] : audience)) {\n        throw new JWTClaimValidationFailed('unexpected \"aud\" claim value', payload, 'aud', 'check_failed');\n    }\n    let tolerance;\n    switch (typeof options.clockTolerance) {\n        case 'string':\n            tolerance = secs(options.clockTolerance);\n            break;\n        case 'number':\n            tolerance = options.clockTolerance;\n            break;\n        case 'undefined':\n            tolerance = 0;\n            break;\n        default:\n            throw new TypeError('Invalid clockTolerance option type');\n    }\n    const { currentDate } = options;\n    const now = epoch(currentDate || new Date());\n    if ((payload.iat !== undefined || maxTokenAge) && typeof payload.iat !== 'number') {\n        throw new JWTClaimValidationFailed('\"iat\" claim must be a number', payload, 'iat', 'invalid');\n    }\n    if (payload.nbf !== undefined) {\n        if (typeof payload.nbf !== 'number') {\n            throw new JWTClaimValidationFailed('\"nbf\" claim must be a number', payload, 'nbf', 'invalid');\n        }\n        if (payload.nbf > now + tolerance) {\n            throw new JWTClaimValidationFailed('\"nbf\" claim timestamp check failed', payload, 'nbf', 'check_failed');\n        }\n    }\n    if (payload.exp !== undefined) {\n        if (typeof payload.exp !== 'number') {\n            throw new JWTClaimValidationFailed('\"exp\" claim must be a number', payload, 'exp', 'invalid');\n        }\n        if (payload.exp <= now - tolerance) {\n            throw new JWTExpired('\"exp\" claim timestamp check failed', payload, 'exp', 'check_failed');\n        }\n    }\n    if (maxTokenAge) {\n        const age = now - payload.iat;\n        const max = typeof maxTokenAge === 'number' ? maxTokenAge : secs(maxTokenAge);\n        if (age - tolerance > max) {\n            throw new JWTExpired('\"iat\" claim timestamp check failed (too far in the past)', payload, 'iat', 'check_failed');\n        }\n        if (age < 0 - tolerance) {\n            throw new JWTClaimValidationFailed('\"iat\" claim timestamp check failed (it should be in the past)', payload, 'iat', 'check_failed');\n        }\n    }\n    return payload;\n}\nexport class JWTClaimsBuilder {\n    #payload;\n    constructor(payload) {\n        if (!isObject(payload)) {\n            throw new TypeError('JWT Claims Set MUST be an object');\n        }\n        this.#payload = structuredClone(payload);\n    }\n    data() {\n        return encoder.encode(JSON.stringify(this.#payload));\n    }\n    get iss() {\n        return this.#payload.iss;\n    }\n    set iss(value) {\n        this.#payload.iss = value;\n    }\n    get sub() {\n        return this.#payload.sub;\n    }\n    set sub(value) {\n        this.#payload.sub = value;\n    }\n    get aud() {\n        return this.#payload.aud;\n    }\n    set aud(value) {\n        this.#payload.aud = value;\n    }\n    set jti(value) {\n        this.#payload.jti = value;\n    }\n    set nbf(value) {\n        if (typeof value === 'number') {\n            this.#payload.nbf = validateInput('setNotBefore', value);\n        }\n        else if (value instanceof Date) {\n            this.#payload.nbf = validateInput('setNotBefore', epoch(value));\n        }\n        else {\n            this.#payload.nbf = epoch(new Date()) + secs(value);\n        }\n    }\n    set exp(value) {\n        if (typeof value === 'number') {\n            this.#payload.exp = validateInput('setExpirationTime', value);\n        }\n        else if (value instanceof Date) {\n            this.#payload.exp = validateInput('setExpirationTime', epoch(value));\n        }\n        else {\n            this.#payload.exp = epoch(new Date()) + secs(value);\n        }\n    }\n    set iat(value) {\n        if (typeof value === 'undefined') {\n            this.#payload.iat = epoch(new Date());\n        }\n        else if (value instanceof Date) {\n            this.#payload.iat = validateInput('setIssuedAt', epoch(value));\n        }\n        else if (typeof value === 'string') {\n            this.#payload.iat = validateInput('setIssuedAt', epoch(new Date()) + secs(value));\n        }\n        else {\n            this.#payload.iat = validateInput('setIssuedAt', value);\n        }\n    }\n}\n", "import { JOSENotSupported } from '../util/errors.js';\nfunction subtleMapping(jwk) {\n    let algorithm;\n    let keyUsages;\n    switch (jwk.kty) {\n        case 'RSA': {\n            switch (jwk.alg) {\n                case 'PS256':\n                case 'PS384':\n                case 'PS512':\n                    algorithm = { name: 'RSA-PSS', hash: `SHA-${jwk.alg.slice(-3)}` };\n                    keyUsages = jwk.d ? ['sign'] : ['verify'];\n                    break;\n                case 'RS256':\n                case 'RS384':\n                case 'RS512':\n                    algorithm = { name: 'RSASSA-PKCS1-v1_5', hash: `SHA-${jwk.alg.slice(-3)}` };\n                    keyUsages = jwk.d ? ['sign'] : ['verify'];\n                    break;\n                case 'RSA-OAEP':\n                case 'RSA-OAEP-256':\n                case 'RSA-OAEP-384':\n                case 'RSA-OAEP-512':\n                    algorithm = {\n                        name: 'RSA-OAEP',\n                        hash: `SHA-${parseInt(jwk.alg.slice(-3), 10) || 1}`,\n                    };\n                    keyUsages = jwk.d ? ['decrypt', 'unwrapKey'] : ['encrypt', 'wrapKey'];\n                    break;\n                default:\n                    throw new JOSENotSupported('Invalid or unsupported JWK \"alg\" (Algorithm) Parameter value');\n            }\n            break;\n        }\n        case 'EC': {\n            switch (jwk.alg) {\n                case 'ES256':\n                    algorithm = { name: 'ECDSA', namedCurve: 'P-256' };\n                    keyUsages = jwk.d ? ['sign'] : ['verify'];\n                    break;\n                case 'ES384':\n                    algorithm = { name: 'ECDSA', namedCurve: 'P-384' };\n                    keyUsages = jwk.d ? ['sign'] : ['verify'];\n                    break;\n                case 'ES512':\n                    algorithm = { name: 'ECDSA', namedCurve: 'P-521' };\n                    keyUsages = jwk.d ? ['sign'] : ['verify'];\n                    break;\n                case 'ECDH-ES':\n                case 'ECDH-ES+A128KW':\n                case 'ECDH-ES+A192KW':\n                case 'ECDH-ES+A256KW':\n                    algorithm = { name: 'ECDH', namedCurve: jwk.crv };\n                    keyUsages = jwk.d ? ['deriveBits'] : [];\n                    break;\n                default:\n                    throw new JOSENotSupported('Invalid or unsupported JWK \"alg\" (Algorithm) Parameter value');\n            }\n            break;\n        }\n        case 'OKP': {\n            switch (jwk.alg) {\n                case 'Ed25519':\n                case 'EdDSA':\n                    algorithm = { name: 'Ed25519' };\n                    keyUsages = jwk.d ? ['sign'] : ['verify'];\n                    break;\n                case 'ECDH-ES':\n                case 'ECDH-ES+A128KW':\n                case 'ECDH-ES+A192KW':\n                case 'ECDH-ES+A256KW':\n                    algorithm = { name: jwk.crv };\n                    keyUsages = jwk.d ? ['deriveBits'] : [];\n                    break;\n                default:\n                    throw new JOSENotSupported('Invalid or unsupported JWK \"alg\" (Algorithm) Parameter value');\n            }\n            break;\n        }\n        default:\n            throw new JOSENotSupported('Invalid or unsupported JWK \"kty\" (Key Type) Parameter value');\n    }\n    return { algorithm, keyUsages };\n}\nexport default async (jwk) => {\n    if (!jwk.alg) {\n        throw new TypeError('\"alg\" argument is required when \"jwk.alg\" is not present');\n    }\n    const { algorithm, keyUsages } = subtleMapping(jwk);\n    const keyData = { ...jwk };\n    delete keyData.alg;\n    delete keyData.use;\n    return crypto.subtle.importKey('jwk', keyData, algorithm, jwk.ext ?? (jwk.d ? false : true), jwk.key_ops ?? keyUsages);\n};\n", "import { isJWK } from './is_jwk.js';\nimport { decode } from '../util/base64url.js';\nimport importJWK from './jwk_to_key.js';\nimport { isCryptoKey, isKeyObject } from './is_key_like.js';\nlet cache;\nconst handleJWK = async (key, jwk, alg, freeze = false) => {\n    cache ||= new WeakMap();\n    let cached = cache.get(key);\n    if (cached?.[alg]) {\n        return cached[alg];\n    }\n    const cryptoKey = await importJWK({ ...jwk, alg });\n    if (freeze)\n        Object.freeze(key);\n    if (!cached) {\n        cache.set(key, { [alg]: cryptoKey });\n    }\n    else {\n        cached[alg] = cryptoKey;\n    }\n    return cryptoKey;\n};\nconst handleKeyObject = (keyObject, alg) => {\n    cache ||= new WeakMap();\n    let cached = cache.get(keyObject);\n    if (cached?.[alg]) {\n        return cached[alg];\n    }\n    const isPublic = keyObject.type === 'public';\n    const extractable = isPublic ? true : false;\n    let cryptoKey;\n    if (keyObject.asymmetricKeyType === 'x25519') {\n        switch (alg) {\n            case 'ECDH-ES':\n            case 'ECDH-ES+A128KW':\n            case 'ECDH-ES+A192KW':\n            case 'ECDH-ES+A256KW':\n                break;\n            default:\n                throw new TypeError('given KeyObject instance cannot be used for this algorithm');\n        }\n        cryptoKey = keyObject.toCryptoKey(keyObject.asymmetricKeyType, extractable, isPublic ? [] : ['deriveBits']);\n    }\n    if (keyObject.asymmetricKeyType === 'ed25519') {\n        if (alg !== 'EdDSA' && alg !== 'Ed25519') {\n            throw new TypeError('given KeyObject instance cannot be used for this algorithm');\n        }\n        cryptoKey = keyObject.toCryptoKey(keyObject.asymmetricKeyType, extractable, [\n            isPublic ? 'verify' : 'sign',\n        ]);\n    }\n    if (keyObject.asymmetricKeyType === 'rsa') {\n        let hash;\n        switch (alg) {\n            case 'RSA-OAEP':\n                hash = 'SHA-1';\n                break;\n            case 'RS256':\n            case 'PS256':\n            case 'RSA-OAEP-256':\n                hash = 'SHA-256';\n                break;\n            case 'RS384':\n            case 'PS384':\n            case 'RSA-OAEP-384':\n                hash = 'SHA-384';\n                break;\n            case 'RS512':\n            case 'PS512':\n            case 'RSA-OAEP-512':\n                hash = 'SHA-512';\n                break;\n            default:\n                throw new TypeError('given KeyObject instance cannot be used for this algorithm');\n        }\n        if (alg.startsWith('RSA-OAEP')) {\n            return keyObject.toCryptoKey({\n                name: 'RSA-OAEP',\n                hash,\n            }, extractable, isPublic ? ['encrypt'] : ['decrypt']);\n        }\n        cryptoKey = keyObject.toCryptoKey({\n            name: alg.startsWith('PS') ? 'RSA-PSS' : 'RSASSA-PKCS1-v1_5',\n            hash,\n        }, extractable, [isPublic ? 'verify' : 'sign']);\n    }\n    if (keyObject.asymmetricKeyType === 'ec') {\n        const nist = new Map([\n            ['prime256v1', 'P-256'],\n            ['secp384r1', 'P-384'],\n            ['secp521r1', 'P-521'],\n        ]);\n        const namedCurve = nist.get(keyObject.asymmetricKeyDetails?.namedCurve);\n        if (!namedCurve) {\n            throw new TypeError('given KeyObject instance cannot be used for this algorithm');\n        }\n        if (alg === 'ES256' && namedCurve === 'P-256') {\n            cryptoKey = keyObject.toCryptoKey({\n                name: 'ECDSA',\n                namedCurve,\n            }, extractable, [isPublic ? 'verify' : 'sign']);\n        }\n        if (alg === 'ES384' && namedCurve === 'P-384') {\n            cryptoKey = keyObject.toCryptoKey({\n                name: 'ECDSA',\n                namedCurve,\n            }, extractable, [isPublic ? 'verify' : 'sign']);\n        }\n        if (alg === 'ES512' && namedCurve === 'P-521') {\n            cryptoKey = keyObject.toCryptoKey({\n                name: 'ECDSA',\n                namedCurve,\n            }, extractable, [isPublic ? 'verify' : 'sign']);\n        }\n        if (alg.startsWith('ECDH-ES')) {\n            cryptoKey = keyObject.toCryptoKey({\n                name: 'ECDH',\n                namedCurve,\n            }, extractable, isPublic ? [] : ['deriveBits']);\n        }\n    }\n    if (!cryptoKey) {\n        throw new TypeError('given KeyObject instance cannot be used for this algorithm');\n    }\n    if (!cached) {\n        cache.set(keyObject, { [alg]: cryptoKey });\n    }\n    else {\n        cached[alg] = cryptoKey;\n    }\n    return cryptoKey;\n};\nexport default async (key, alg) => {\n    if (key instanceof Uint8Array) {\n        return key;\n    }\n    if (isCryptoKey(key)) {\n        return key;\n    }\n    if (isKeyObject(key)) {\n        if (key.type === 'secret') {\n            return key.export();\n        }\n        if ('toCryptoKey' in key && typeof key.toCryptoKey === 'function') {\n            try {\n                return handleKeyObject(key, alg);\n            }\n            catch (err) {\n                if (err instanceof TypeError) {\n                    throw err;\n                }\n            }\n        }\n        let jwk = key.export({ format: 'jwk' });\n        return handleJWK(key, jwk, alg);\n    }\n    if (isJWK(key)) {\n        if (key.k) {\n            return decode(key.k);\n        }\n        return handleJWK(key, key, alg, true);\n    }\n    throw new Error('unreachable');\n};\n", "import { JOSENotSupported } from '../util/errors.js';\nexport default (alg, algorithm) => {\n    const hash = `SHA-${alg.slice(-3)}`;\n    switch (alg) {\n        case 'HS256':\n        case 'HS384':\n        case 'HS512':\n            return { hash, name: '<PERSON><PERSON>' };\n        case 'PS256':\n        case 'PS384':\n        case 'PS512':\n            return { hash, name: 'RSA-PSS', saltLength: parseInt(alg.slice(-3), 10) >> 3 };\n        case 'RS256':\n        case 'RS384':\n        case 'RS512':\n            return { hash, name: 'RSASSA-PKCS1-v1_5' };\n        case 'ES256':\n        case 'ES384':\n        case 'ES512':\n            return { hash, name: 'ECDS<PERSON>', namedCurve: algorithm.namedCurve };\n        case 'Ed25519':\n        case 'EdDSA':\n            return { name: 'Ed25519' };\n        default:\n            throw new JOSENotSupported(`alg ${alg} is not supported either by JOSE or your javascript runtime`);\n    }\n};\n", "import { JOSENotSupported, JWEInvalid, JWSInvalid } from '../util/errors.js';\nexport default (Err, recognizedDefault, recognizedOption, protectedHeader, joseHeader) => {\n    if (joseHeader.crit !== undefined && protectedHeader?.crit === undefined) {\n        throw new Err('\"crit\" (Critical) Header Parameter MUST be integrity protected');\n    }\n    if (!protectedHeader || protectedHeader.crit === undefined) {\n        return new Set();\n    }\n    if (!Array.isArray(protectedHeader.crit) ||\n        protectedHeader.crit.length === 0 ||\n        protectedHeader.crit.some((input) => typeof input !== 'string' || input.length === 0)) {\n        throw new Err('\"crit\" (Critical) Header Parameter MUST be an array of non-empty strings when present');\n    }\n    let recognized;\n    if (recognizedOption !== undefined) {\n        recognized = new Map([...Object.entries(recognizedOption), ...recognizedDefault.entries()]);\n    }\n    else {\n        recognized = recognizedDefault;\n    }\n    for (const parameter of protectedHeader.crit) {\n        if (!recognized.has(parameter)) {\n            throw new JOSENotSupported(`Extension Header Parameter \"${parameter}\" is not recognized`);\n        }\n        if (joseHeader[parameter] === undefined) {\n            throw new Err(`Extension Header Parameter \"${parameter}\" is missing`);\n        }\n        if (recognized.get(parameter) && protectedHeader[parameter] === undefined) {\n            throw new Err(`Extension Header Parameter \"${parameter}\" MUST be integrity protected`);\n        }\n    }\n    return new Set(protectedHeader.crit);\n};\n", "export function encodeBase64(input) {\n    if (Uint8Array.prototype.toBase64) {\n        return input.toBase64();\n    }\n    const CHUNK_SIZE = 0x8000;\n    const arr = [];\n    for (let i = 0; i < input.length; i += CHUNK_SIZE) {\n        arr.push(String.fromCharCode.apply(null, input.subarray(i, i + CHUNK_SIZE)));\n    }\n    return btoa(arr.join(''));\n}\nexport function decodeBase64(encoded) {\n    if (Uint8Array.fromBase64) {\n        return Uint8Array.fromBase64(encoded);\n    }\n    const binary = atob(encoded);\n    const bytes = new Uint8Array(binary.length);\n    for (let i = 0; i < binary.length; i++) {\n        bytes[i] = binary.charCodeAt(i);\n    }\n    return bytes;\n}\n", "import { encoder, decoder } from '../lib/buffer_utils.js';\nimport { encodeBase64, decodeBase64 } from '../lib/base64.js';\nexport function decode(input) {\n    if (Uint8Array.fromBase64) {\n        return Uint8Array.fromBase64(typeof input === 'string' ? input : decoder.decode(input), {\n            alphabet: 'base64url',\n        });\n    }\n    let encoded = input;\n    if (encoded instanceof Uint8Array) {\n        encoded = decoder.decode(encoded);\n    }\n    encoded = encoded.replace(/-/g, '+').replace(/_/g, '/').replace(/\\s/g, '');\n    try {\n        return decodeBase64(encoded);\n    }\n    catch {\n        throw new TypeError('The input to be decoded is not correctly encoded.');\n    }\n}\nexport function encode(input) {\n    let unencoded = input;\n    if (typeof unencoded === 'string') {\n        unencoded = encoder.encode(unencoded);\n    }\n    if (Uint8Array.prototype.toBase64) {\n        return unencoded.toBase64({ alphabet: 'base64url', omitPadding: true });\n    }\n    return encodeBase64(unencoded).replace(/=/g, '').replace(/\\+/g, '-').replace(/\\//g, '_');\n}\n", "export class JOSEError extends Error {\n    static code = 'ERR_JOSE_GENERIC';\n    code = 'ERR_JOSE_GENERIC';\n    constructor(message, options) {\n        super(message, options);\n        this.name = this.constructor.name;\n        Error.captureStackTrace?.(this, this.constructor);\n    }\n}\nexport class JWTClaimValidationFailed extends JOSEError {\n    static code = 'ERR_JWT_CLAIM_VALIDATION_FAILED';\n    code = 'ERR_JWT_CLAIM_VALIDATION_FAILED';\n    claim;\n    reason;\n    payload;\n    constructor(message, payload, claim = 'unspecified', reason = 'unspecified') {\n        super(message, { cause: { claim, reason, payload } });\n        this.claim = claim;\n        this.reason = reason;\n        this.payload = payload;\n    }\n}\nexport class JWTExpired extends JOSEError {\n    static code = 'ERR_JWT_EXPIRED';\n    code = 'ERR_JWT_EXPIRED';\n    claim;\n    reason;\n    payload;\n    constructor(message, payload, claim = 'unspecified', reason = 'unspecified') {\n        super(message, { cause: { claim, reason, payload } });\n        this.claim = claim;\n        this.reason = reason;\n        this.payload = payload;\n    }\n}\nexport class JOSEAlgNotAllowed extends J<PERSON><PERSON><PERSON>r {\n    static code = 'ERR_JOSE_ALG_NOT_ALLOWED';\n    code = 'ERR_JOSE_ALG_NOT_ALLOWED';\n}\nexport class JOSENotSupported extends JOSEError {\n    static code = 'ERR_JOSE_NOT_SUPPORTED';\n    code = 'ERR_JOSE_NOT_SUPPORTED';\n}\nexport class JWEDecryptionFailed extends JOSEError {\n    static code = 'ERR_JWE_DECRYPTION_FAILED';\n    code = 'ERR_JWE_DECRYPTION_FAILED';\n    constructor(message = 'decryption operation failed', options) {\n        super(message, options);\n    }\n}\nexport class JWEInvalid extends JOSEError {\n    static code = 'ERR_JWE_INVALID';\n    code = 'ERR_JWE_INVALID';\n}\nexport class JWSInvalid extends JOSEError {\n    static code = 'ERR_JWS_INVALID';\n    code = 'ERR_JWS_INVALID';\n}\nexport class JWTInvalid extends JOSEError {\n    static code = 'ERR_JWT_INVALID';\n    code = 'ERR_JWT_INVALID';\n}\nexport class JWKInvalid extends JOSEError {\n    static code = 'ERR_JWK_INVALID';\n    code = 'ERR_JWK_INVALID';\n}\nexport class JWKSInvalid extends JOSEError {\n    static code = 'ERR_JWKS_INVALID';\n    code = 'ERR_JWKS_INVALID';\n}\nexport class JWKSNoMatchingKey extends JOSEError {\n    static code = 'ERR_JWKS_NO_MATCHING_KEY';\n    code = 'ERR_JWKS_NO_MATCHING_KEY';\n    constructor(message = 'no applicable key found in the JSON Web Key Set', options) {\n        super(message, options);\n    }\n}\nexport class JWKSMultipleMatchingKeys extends JOSEError {\n    [Symbol.asyncIterator];\n    static code = 'ERR_JWKS_MULTIPLE_MATCHING_KEYS';\n    code = 'ERR_JWKS_MULTIPLE_MATCHING_KEYS';\n    constructor(message = 'multiple matching keys found in the JSON Web Key Set', options) {\n        super(message, options);\n    }\n}\nexport class JWKSTimeout extends JOSEError {\n    static code = 'ERR_JWKS_TIMEOUT';\n    code = 'ERR_JWKS_TIMEOUT';\n    constructor(message = 'request timed out', options) {\n        super(message, options);\n    }\n}\nexport class JWSSignatureVerificationFailed extends JOSEError {\n    static code = 'ERR_JWS_SIGNATURE_VERIFICATION_FAILED';\n    code = 'ERR_JWS_SIGNATURE_VERIFICATION_FAILED';\n    constructor(message = 'signature verification failed', options) {\n        super(message, options);\n    }\n}\n"], "names": [], "sourceRoot": "", "ignoreList": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29]}