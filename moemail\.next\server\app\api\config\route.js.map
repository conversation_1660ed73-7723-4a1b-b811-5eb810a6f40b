{"version": 3, "file": "app/api/config/route.js", "mappings": "sFAAA,8DCAA,oHGAA,2SFKO,IAAMA,EAAU,OAEhB,eAAeC,IACpB,IAAMC,EAAMC,CAAAA,EAAAA,EAAAA,iBAAAA,CAAiBA,GAAGD,GAAG,CAC7B,CAACE,EAAaC,EAAcC,EAAcC,EAAU,CAAG,MAAMC,QAAQC,GAAG,CAAC,CAC7EP,EAAIQ,WAAW,CAACC,GAAG,CAAC,gBACpBT,EAAIQ,WAAW,CAACC,GAAG,CAAC,iBACpBT,EAAIQ,WAAW,CAACC,GAAG,CAAC,iBACpBT,EAAIQ,WAAW,CAACC,GAAG,CAAC,cACrB,EAED,OAAOC,SAASC,IAAI,CAAC,CACnBT,YAAaA,GAAeU,EAAAA,EAAKA,CAACC,QAAQ,CAC1CV,aAAcA,GAAgB,cAC9BC,aAAcA,GAAgB,GAC9BC,UAAWA,GAAaS,EAAAA,CAAYA,CAACC,iBAAiB,CAACC,QAAQ,EACjE,EACF,CAEO,eAAeC,EAAKC,CAAgB,EAGzC,GAAI,CAFc,MAAMC,CAAAA,EAAAA,EAAAA,EAAAA,CAAeA,CAACC,EAAAA,EAAWA,CAACC,aAAa,EAG/D,OAAOX,SAASC,IAAI,CAAC,CACnBW,MAAO,MACT,EAAG,CAAEC,OAAQ,GAAI,GAGnB,GAAM,aAAErB,CAAW,cAAEC,CAAY,cAAEC,CAAY,WAAEC,CAAS,CAAE,CAAG,MAAMa,EAAQP,IAAI,GAOjF,GAAI,CAAC,CAACC,EAAAA,EAAKA,CAACY,IAAI,CAAEZ,EAAAA,EAAKA,CAACa,MAAM,CAAEb,EAAAA,EAAKA,CAACC,QAAQ,CAAC,CAACa,QAAQ,CAACxB,GACvD,OAAOQ,IAD8D,KACrDC,IAAI,CAAC,CAAEW,MAAO,OAAQ,EAAG,CAAEC,OAAQ,GAAI,GAGzD,IAAMvB,EAAMC,CAAAA,EAAAA,EAAAA,iBAAAA,CAAiBA,GAAGD,GAAG,CAQnC,OAPA,MAAMM,QAAQC,GAAG,CAAC,CAChBP,EAAIQ,WAAW,CAACmB,GAAG,CAAC,eAAgBzB,GACpCF,EAAIQ,WAAW,CAACmB,GAAG,CAAC,gBAAiBxB,GACrCH,EAAIQ,WAAW,CAACmB,GAAG,CAAC,gBAAiBvB,GACrCJ,EAAIQ,WAAW,CAACmB,GAAG,CAAC,aAActB,GACnC,EAEMK,SAASC,IAAI,CAAC,CAAEiB,SAAS,CAAK,EACvC,CC9CA,UAAwB,qBAAmB,EAC3C,YACA,KAAc,GAAS,WACvB,yBACA,uBACA,iBACA,iCACA,CAAK,CACL,oFACA,iBAVA,GAWA,QAAY,EACZ,CAAC,EAID,kBAAQ,wCAAsD,EAC9D,aACA,MAAW,QAAW,EACtB,mBACA,sBACA,CAAK,CACL,CCpBA,kEACA,EAFA,4BAEA,4BACA,MACI,QAA8B,EAClC,yBACA,0BACA,wBACA,gBAAyB,OAAqB,EAC9C,uBACA,CAAS,CACT,CAAK,EAEE,MAAqB,EAC5B,EAAe,GAAsB,MAAM,EAAkB,CAC7D,QAD6D,GAf7D,CAAoB,MAAQ,OAcM,CAdN,CAAW,sBAA2B,aAAe,kDAAyD,uOAAsQ,2RAAuT,kBAAkB,QAAQ,uDAA2D,0DAA8D,iBAAsB,gBAAkB,sEAAgF,kBAAoB,uCAA6C,MAAQ,iBAAmB,2BAA+B,gHAAkI,uBAAyB,8FAA0G,aAAiB,WAAa,sEAA6E,uBAAuB,iCAAmC,QAAQ,EAAE,SAAW,oBAAsB,QAAQ,GAAG,4EAAgF,WAAa,SAAW,2CAAiD,UAAY,+BAAqC,UAAY,oCAA0C,QAAU,uCAA6C,OAAS,yCAA+C,QAAU,0CAAiD,MAAQ,+CAAsD,iBAAmB,ywBAA+5B,4CAA8C,kNAAqQ,qBAAyB,s+CAA0/C,oIAiB7wJ,CAAC,CAAC,EAAC,+ECvBI,IAAMd,EAAe,CAC1BC,kBAAmB,GACnBc,cAAe,GACjB,EAAU,ECHoB,CAC5BC,YAAa,EACbC,QAAS,IACTC,YAAa,IACbC,OAAQ,CACNC,YAAa,aACf,CACF,EAAU", "sources": ["webpack://_N_E/external commonjs \"node:async_hooks\"", "webpack://_N_E/external commonjs \"node:buffer\"", "webpack://_N_E/./app/api/config/route.ts", "webpack://_N_E/./app/api/config/route.ts?8906", "webpack://_N_E/?4af8", "webpack://_N_E/./app/config/email.ts", "webpack://_N_E/./app/config/webhook.ts", "webpack://_N_E/./app/config/index.ts"], "sourcesContent": ["module.exports = require(\"node:async_hooks\");", "module.exports = require(\"node:buffer\");", "import { PERMISSIONS, Role, ROLES } from \"@/lib/permissions\"\r\nimport { getRequestContext } from \"@cloudflare/next-on-pages\"\r\nimport { EMAIL_CONFIG } from \"@/config\"\r\nimport { checkPermission } from \"@/lib/auth\"\r\n\r\nexport const runtime = \"edge\"\r\n\r\nexport async function GET() {\r\n  const env = getRequestContext().env\r\n  const [defaultRole, emailDomains, adminContact, maxEmails] = await Promise.all([\r\n    env.SITE_CONFIG.get(\"DEFAULT_ROLE\"),\r\n    env.SITE_CONFIG.get(\"EMAIL_DOMAINS\"),\r\n    env.SITE_CONFIG.get(\"ADMIN_CONTACT\"),\r\n    env.SITE_CONFIG.get(\"MAX_EMAILS\")\r\n  ])\r\n\r\n  return Response.json({\r\n    defaultRole: defaultRole || ROLES.CIVILIAN,\r\n    emailDomains: emailDomains || \"moemail.app\",\r\n    adminContact: adminContact || \"\",\r\n    maxEmails: maxEmails || EMAIL_CONFIG.MAX_ACTIVE_EMAILS.toString()\r\n  })\r\n}\r\n\r\nexport async function POST(request: Request) {\r\n  const canAccess = await checkPermission(PERMISSIONS.MANAGE_CONFIG)\r\n\r\n  if (!canAccess) {\r\n    return Response.json({\r\n      error: \"权限不足\"\r\n    }, { status: 403 })\r\n  }\r\n\r\n  const { defaultRole, emailDomains, adminContact, maxEmails } = await request.json() as { \r\n    defaultRole: Exclude<Role, typeof ROLES.EMPEROR>,\r\n    emailDomains: string,\r\n    adminContact: string,\r\n    maxEmails: string\r\n  }\r\n  \r\n  if (![ROLES.DUKE, ROLES.KNIGHT, ROLES.CIVILIAN].includes(defaultRole)) {\r\n    return Response.json({ error: \"无效的角色\" }, { status: 400 })\r\n  }\r\n\r\n  const env = getRequestContext().env\r\n  await Promise.all([\r\n    env.SITE_CONFIG.put(\"DEFAULT_ROLE\", defaultRole),\r\n    env.SITE_CONFIG.put(\"EMAIL_DOMAINS\", emailDomains),\r\n    env.SITE_CONFIG.put(\"ADMIN_CONTACT\", adminContact),\r\n    env.SITE_CONFIG.put(\"MAX_EMAILS\", maxEmails)\r\n  ])\r\n\r\n  return Response.json({ success: true })\r\n} ", "import { AppRouteRouteModule } from \"next/dist/server/route-modules/app-route/module.compiled\";\nimport { RouteKind } from \"next/dist/server/route-kind\";\nimport { patchFetch as _patchFetch } from \"next/dist/server/lib/patch-fetch\";\nimport * as userland from \"F:\\\\CODE\\\\Project\\\\tempmail\\\\moemail\\\\app\\\\api\\\\config\\\\route.ts\";\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new AppRouteRouteModule({\n    definition: {\n        kind: RouteKind.APP_ROUTE,\n        page: \"/api/config/route\",\n        pathname: \"/api/config\",\n        filename: \"route\",\n        bundlePath: \"app/api/config/route\"\n    },\n    resolvedPagePath: \"F:\\\\CODE\\\\Project\\\\tempmail\\\\moemail\\\\app\\\\api\\\\config\\\\route.ts\",\n    nextConfigOutput,\n    userland\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return _patchFetch({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\nexport { routeModule, workAsyncStorage, workUnitAsyncStorage, serverHooks, patchFetch,  };\n\n//# sourceMappingURL=app-route.js.map", "var _self___RSC_MANIFEST;\nimport { createServerModuleMap } from \"next/dist/server/app-render/action-utils\";\nimport { setReferenceManifestsSingleton } from \"next/dist/server/app-render/encryption-utils\";\nimport { EdgeRouteModuleWrapper } from \"next/dist/server/web/edge-route-module-wrapper\";\n// Import the userland code.\nimport * as module from \"next-app-loader?name=app%2Fapi%2Fconfig%2Froute&page=%2Fapi%2Fconfig%2Froute&pagePath=private-next-app-dir%2Fapi%2Fconfig%2Froute.ts&appDir=F%3A%5CCODE%5CProject%5Ctempmail%5Cmoemail%5Capp&appPaths=%2Fapi%2Fconfig%2Froute&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!private-next-app-dir/api/config/route.ts?__next_edge_ssr_entry__\";\nconst nextConfig = {\"env\":{},\"eslint\":{\"ignoreDuringBuilds\":false},\"typescript\":{\"ignoreBuildErrors\":false,\"tsconfigPath\":\"tsconfig.json\"},\"distDir\":\".next\",\"cleanDistDir\":true,\"assetPrefix\":\"\",\"cacheMaxMemorySize\":52428800,\"configOrigin\":\"next.config.ts\",\"useFileSystemPublicRoutes\":true,\"generateEtags\":true,\"pageExtensions\":[\"tsx\",\"ts\",\"jsx\",\"js\"],\"poweredByHeader\":true,\"compress\":true,\"images\":{\"deviceSizes\":[640,750,828,1080,1200,1920,2048,3840],\"imageSizes\":[16,32,48,64,96,128,256,384],\"path\":\"/_next/image\",\"loader\":\"default\",\"loaderFile\":\"\",\"domains\":[],\"disableStaticImages\":false,\"minimumCacheTTL\":60,\"formats\":[\"image/webp\"],\"dangerouslyAllowSVG\":false,\"contentSecurityPolicy\":\"script-src 'none'; frame-src 'none'; sandbox;\",\"contentDispositionType\":\"attachment\",\"remotePatterns\":[{\"protocol\":\"https\",\"hostname\":\"avatars.githubusercontent.com\"}],\"unoptimized\":false},\"devIndicators\":{\"appIsrStatus\":true,\"buildActivity\":true,\"buildActivityPosition\":\"bottom-right\"},\"onDemandEntries\":{\"maxInactiveAge\":60000,\"pagesBufferLength\":5},\"amp\":{\"canonicalBase\":\"\"},\"basePath\":\"\",\"sassOptions\":{},\"trailingSlash\":false,\"i18n\":null,\"productionBrowserSourceMaps\":false,\"excludeDefaultMomentLocales\":true,\"serverRuntimeConfig\":{},\"publicRuntimeConfig\":{},\"reactProductionProfiling\":false,\"reactStrictMode\":null,\"reactMaxHeadersLength\":6000,\"httpAgentOptions\":{\"keepAlive\":true},\"logging\":{},\"expireTime\":31536000,\"staticPageGenerationTimeout\":60,\"modularizeImports\":{\"@mui/icons-material\":{\"transform\":\"@mui/icons-material/{{member}}\"},\"lodash\":{\"transform\":\"lodash/{{member}}\"}},\"outputFileTracingRoot\":\"F:\\\\CODE\\\\Project\\\\tempmail\\\\moemail\",\"experimental\":{\"cacheLife\":{\"default\":{\"stale\":300,\"revalidate\":900,\"expire\":4294967294},\"seconds\":{\"stale\":0,\"revalidate\":1,\"expire\":60},\"minutes\":{\"stale\":300,\"revalidate\":60,\"expire\":3600},\"hours\":{\"stale\":300,\"revalidate\":3600,\"expire\":86400},\"days\":{\"stale\":300,\"revalidate\":86400,\"expire\":604800},\"weeks\":{\"stale\":300,\"revalidate\":604800,\"expire\":2592000},\"max\":{\"stale\":300,\"revalidate\":2592000,\"expire\":4294967294}},\"cacheHandlers\":{},\"cssChunking\":true,\"multiZoneDraftMode\":false,\"appNavFailHandling\":false,\"prerenderEarlyExit\":true,\"serverMinification\":true,\"serverSourceMaps\":false,\"linkNoTouchStart\":false,\"caseSensitiveRoutes\":false,\"clientSegmentCache\":false,\"preloadEntriesOnStart\":true,\"clientRouterFilter\":true,\"clientRouterFilterRedirects\":false,\"fetchCacheKeyPrefix\":\"\",\"middlewarePrefetch\":\"flexible\",\"optimisticClientCache\":true,\"manualClientBasePath\":false,\"cpus\":11,\"memoryBasedWorkersCount\":false,\"imgOptConcurrency\":null,\"imgOptTimeoutInSeconds\":7,\"imgOptMaxInputPixels\":268402689,\"imgOptSequentialRead\":null,\"isrFlushToDisk\":true,\"workerThreads\":false,\"optimizeCss\":false,\"nextScriptWorkers\":false,\"scrollRestoration\":false,\"externalDir\":false,\"disableOptimizedLoading\":false,\"gzipSize\":true,\"craCompat\":false,\"esmExternals\":true,\"fullySpecified\":false,\"swcTraceProfiling\":false,\"forceSwcTransforms\":false,\"largePageDataBytes\":128000,\"turbo\":{\"root\":\"F:\\\\CODE\\\\Project\\\\tempmail\\\\moemail\"},\"typedRoutes\":false,\"typedEnv\":false,\"parallelServerCompiles\":false,\"parallelServerBuildTraces\":false,\"ppr\":false,\"authInterrupts\":false,\"reactOwnerStack\":false,\"webpackMemoryOptimizations\":false,\"optimizeServerReact\":true,\"useEarlyImport\":false,\"staleTimes\":{\"dynamic\":0,\"static\":300},\"serverComponentsHmrCache\":true,\"staticGenerationMaxConcurrency\":8,\"staticGenerationMinPagesPerWorker\":25,\"dynamicIO\":false,\"inlineCss\":false,\"optimizePackageImports\":[\"lucide-react\",\"date-fns\",\"lodash-es\",\"ramda\",\"antd\",\"react-bootstrap\",\"ahooks\",\"@ant-design/icons\",\"@headlessui/react\",\"@headlessui-float/react\",\"@heroicons/react/20/solid\",\"@heroicons/react/24/solid\",\"@heroicons/react/24/outline\",\"@visx/visx\",\"@tremor/react\",\"rxjs\",\"@mui/material\",\"@mui/icons-material\",\"recharts\",\"react-use\",\"effect\",\"@effect/schema\",\"@effect/platform\",\"@effect/platform-node\",\"@effect/platform-browser\",\"@effect/platform-bun\",\"@effect/sql\",\"@effect/sql-mssql\",\"@effect/sql-mysql2\",\"@effect/sql-pg\",\"@effect/sql-squlite-node\",\"@effect/sql-squlite-bun\",\"@effect/sql-squlite-wasm\",\"@effect/sql-squlite-react-native\",\"@effect/rpc\",\"@effect/rpc-http\",\"@effect/typeclass\",\"@effect/experimental\",\"@effect/opentelemetry\",\"@material-ui/core\",\"@material-ui/icons\",\"@tabler/icons-react\",\"mui-core\",\"react-icons/ai\",\"react-icons/bi\",\"react-icons/bs\",\"react-icons/cg\",\"react-icons/ci\",\"react-icons/di\",\"react-icons/fa\",\"react-icons/fa6\",\"react-icons/fc\",\"react-icons/fi\",\"react-icons/gi\",\"react-icons/go\",\"react-icons/gr\",\"react-icons/hi\",\"react-icons/hi2\",\"react-icons/im\",\"react-icons/io\",\"react-icons/io5\",\"react-icons/lia\",\"react-icons/lib\",\"react-icons/lu\",\"react-icons/md\",\"react-icons/pi\",\"react-icons/ri\",\"react-icons/rx\",\"react-icons/si\",\"react-icons/sl\",\"react-icons/tb\",\"react-icons/tfi\",\"react-icons/ti\",\"react-icons/vsc\",\"react-icons/wi\"]},\"bundlePagesRouterDependencies\":false,\"configFile\":\"F:\\\\CODE\\\\Project\\\\tempmail\\\\moemail\\\\next.config.ts\",\"configFileName\":\"next.config.ts\"}\nconst maybeJSONParse = (str)=>str ? JSON.parse(str) : undefined;\nconst rscManifest = (_self___RSC_MANIFEST = self.__RSC_MANIFEST) == null ? void 0 : _self___RSC_MANIFEST[\"/api/config/route\"];\nconst rscServerManifest = maybeJSONParse(self.__RSC_SERVER_MANIFEST);\nif (rscManifest && rscServerManifest) {\n    setReferenceManifestsSingleton({\n        page: \"/api/config/route\",\n        clientReferenceManifest: rscManifest,\n        serverActionsManifest: rscServerManifest,\n        serverModuleMap: createServerModuleMap({\n            serverActionsManifest: rscServerManifest\n        })\n    });\n}\nexport const ComponentMod = module;\nexport default EdgeRouteModuleWrapper.wrap(module.routeModule, {\n    nextConfig\n});\n\n//# sourceMappingURL=edge-app-route.js.map", "export const EMAIL_CONFIG = {\r\n  MAX_ACTIVE_EMAILS: 30, // Maximum number of active emails\r\n  POLL_INTERVAL: 10_000, // Polling interval in milliseconds\r\n} as const\r\n\r\nexport type EmailConfig = typeof EMAIL_CONFIG ", "export const WEBHOOK_CONFIG = {\r\n  MAX_RETRIES: 3, // Maximum retry count\r\n  TIMEOUT: 10_000, // Timeout time (milliseconds)\r\n  RETRY_DELAY: 1000, // Retry delay (milliseconds)\r\n  EVENTS: {\r\n    NEW_MESSAGE: 'new_message',\r\n  }\r\n} as const\r\n\r\nexport type WebhookConfig = typeof WEBHOOK_CONFIG ", "export * from './email'\r\nexport * from './webhook'"], "names": ["runtime", "GET", "env", "getRequestContext", "defaultRole", "emailDomains", "adminContact", "maxEmails", "Promise", "all", "SITE_CONFIG", "get", "Response", "json", "ROLES", "CIVILIAN", "EMAIL_CONFIG", "MAX_ACTIVE_EMAILS", "toString", "POST", "request", "checkPermission", "PERMISSIONS", "MANAGE_CONFIG", "error", "status", "DUKE", "KNIGHT", "includes", "put", "success", "POLL_INTERVAL", "MAX_RETRIES", "TIMEOUT", "RETRY_DELAY", "EVENTS", "NEW_MESSAGE"], "sourceRoot": "", "ignoreList": []}