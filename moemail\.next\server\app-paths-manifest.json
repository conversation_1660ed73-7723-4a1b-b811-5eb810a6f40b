{"/favicon.ico/route": "app/favicon.ico/route.js", "/_not-found/page": "app/_not-found/page.js", "/api/auth/[...auth]/route": "app/api/auth/[...auth]/route.js", "/api/auth/register/route": "app/api/auth/register/route.js", "/api/api-keys/route": "app/api/api-keys/route.js", "/api/email-credentials/[id]/route": "app/api/email-credentials/[id]/route.js", "/api/config/route": "app/api/config/route.js", "/api/api-keys/[id]/route": "app/api/api-keys/[id]/route.js", "/api/email-credentials/bindings/route": "app/api/email-credentials/bindings/route.js", "/api/email-credentials/create/route": "app/api/email-credentials/create/route.js", "/api/email-credentials/extract-user/route": "app/api/email-credentials/extract-user/route.js", "/api/email-credentials/route": "app/api/email-credentials/route.js", "/api/email-credentials/extract/route": "app/api/email-credentials/extract/route.js", "/api/emails/[id]/[messageId]/route": "app/api/emails/[id]/[messageId]/route.js", "/api/emails/bind/route": "app/api/emails/bind/route.js", "/api/emails/[id]/route": "app/api/emails/[id]/route.js", "/api/roles/init-emperor/route": "app/api/roles/init-emperor/route.js", "/api/emails/generate/route": "app/api/emails/generate/route.js", "/api/roles/promote/route": "app/api/roles/promote/route.js", "/api/roles/users/route": "app/api/roles/users/route.js", "/api/webhook/test/route": "app/api/webhook/test/route.js", "/api/emails/route": "app/api/emails/route.js", "/api/webhook/route": "app/api/webhook/route.js", "/login/page": "app/login/page.js", "/page": "app/page.js", "/profile/page": "app/profile/page.js", "/moe/page": "app/moe/page.js"}