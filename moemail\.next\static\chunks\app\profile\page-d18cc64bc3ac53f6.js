(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[636],{4510:(e,s,a)=>{Promise.resolve().then(a.bind(a,3515)),Promise.resolve().then(a.bind(a,4812)),Promise.resolve().then(a.bind(a,1665)),Promise.resolve().then(a.bind(a,5368))},4812:(e,s,a)=>{"use strict";a.d(s,{ProfileCard:()=>eh});var t=a(5155),i=a(5565),r=a(9393),l=a(4822),n=a(7401);let c=(0,n.A)("Crown",[["path",{d:"M11.562 3.266a.5.5 0 0 1 .876 0L15.39 8.87a1 1 0 0 0 1.516.294L21.183 5.5a.5.5 0 0 1 .798.519l-2.834 10.246a1 1 0 0 1-.956.734H5.81a1 1 0 0 1-.957-.734L2.02 6.02a.5.5 0 0 1 .798-.519l4.276 3.664a1 1 0 0 0 1.516-.294z",key:"1vdc57"}],["path",{d:"M5 21h14",key:"11awu3"}]]),d=(0,n.A)("Gem",[["path",{d:"M6 3h12l4 6-10 13L2 9Z",key:"1pcd5k"}],["path",{d:"M11 3 8 9l4 13 4-13-3-6",key:"1fcu3u"}],["path",{d:"M2 9h20",key:"16fsjt"}]]),o=(0,n.A)("Sword",[["polyline",{points:"14.5 17.5 3 6 3 3 6 3 17.5 14.5",key:"1hfsw2"}],["line",{x1:"13",x2:"19",y1:"19",y2:"13",key:"1vrmhu"}],["line",{x1:"16",x2:"20",y1:"16",y2:"20",key:"1bron3"}],["line",{x1:"19",x2:"21",y1:"21",y2:"19",key:"13pww6"}]]);var m=a(2699),x=a(1626);let h=(0,n.A)("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]);var p=a(6462),j=a(6046),u=a(2115),y=a(9367),f=a(477),g=a(3610),v=a(8068),N=a(8166),w=a(1488),b=a(858),k=a(7510),A=a(3360),C="Switch",[S,I]=(0,N.A)(C),[E,P]=S(C),O=u.forwardRef((e,s)=>{let{__scopeSwitch:a,name:i,checked:r,defaultChecked:l,required:n,disabled:c,value:d="on",onCheckedChange:o,form:m,...x}=e,[h,p]=u.useState(null),j=(0,v.s)(s,e=>p(e)),y=u.useRef(!1),f=!h||m||!!h.closest("form"),[N=!1,b]=(0,w.i)({prop:r,defaultProp:l,onChange:o});return(0,t.jsxs)(E,{scope:a,checked:N,disabled:c,children:[(0,t.jsx)(A.sG.button,{type:"button",role:"switch","aria-checked":N,"aria-required":n,"data-state":M(N),"data-disabled":c?"":void 0,disabled:c,value:d,...x,ref:j,onClick:(0,g.m)(e.onClick,e=>{b(e=>!e),f&&(y.current=e.isPropagationStopped(),y.current||e.stopPropagation())})}),f&&(0,t.jsx)(_,{control:h,bubbles:!y.current,name:i,value:d,checked:N,required:n,disabled:c,form:m,style:{transform:"translateX(-100%)"}})]})});O.displayName=C;var T="SwitchThumb",K=u.forwardRef((e,s)=>{let{__scopeSwitch:a,...i}=e,r=P(T,a);return(0,t.jsx)(A.sG.span,{"data-state":M(r.checked),"data-disabled":r.disabled?"":void 0,...i,ref:s})});K.displayName=T;var _=e=>{let{control:s,checked:a,bubbles:i=!0,...r}=e,l=u.useRef(null),n=(0,b.Z)(a),c=(0,k.X)(s);return u.useEffect(()=>{let e=l.current,s=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(n!==a&&s){let t=new Event("click",{bubbles:i});s.call(e,a),e.dispatchEvent(t)}},[n,a,i]),(0,t.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:a,...r,tabIndex:-1,ref:l,style:{...e.style,...c,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})};function M(e){return e?"checked":"unchecked"}var R=a(2558);let U=u.forwardRef((e,s)=>{let{className:a,...i}=e;return(0,t.jsx)(O,{className:(0,R.cn)("peer inline-flex h-5 w-9 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent shadow-sm transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input",a),...i,ref:s,children:(0,t.jsx)(K,{className:(0,R.cn)("pointer-events-none block h-4 w-4 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-4 data-[state=unchecked]:translate-x-0")})})});U.displayName=O.displayName;var L=a(7762),$=a(4505);let J=(0,n.A)("Send",[["path",{d:"M14.536 21.686a.5.5 0 0 0 .937-.024l6.5-19a.496.496 0 0 0-.635-.635l-19 6.5a.5.5 0 0 0-.024.937l7.93 3.18a2 2 0 0 1 1.112 1.11z",key:"1ffxy3"}],["path",{d:"m21.854 2.147-10.94 10.939",key:"12cjpa"}]]),H=(0,n.A)("ChevronUp",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]]);var z=a(1719),Y=a(2354);function X(){let[e,s]=(0,u.useState)(!1),[a,i]=(0,u.useState)(""),[l,n]=(0,u.useState)(!1),[c,d]=(0,u.useState)(!1),[o,m]=(0,u.useState)(!1),[x,h]=(0,u.useState)(!0),{toast:p}=(0,L.dj)();if((0,u.useEffect)(()=>{fetch("/api/webhook").then(e=>e.json()).then(e=>{s(e.enabled),i(e.url)}).catch(console.error).finally(()=>h(!1))},[]),x)return(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center mx-auto",children:(0,t.jsx)($.A,{className:"w-6 h-6 text-primary animate-spin"})}),(0,t.jsx)("div",{children:(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"加载中..."})})]});let j=async s=>{if(s.preventDefault(),a){n(!0);try{if(!(await fetch("/api/webhook",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({url:a,enabled:e})})).ok)throw Error("Failed to save");p({title:"保存成功",description:"Webhook 配置已更新"})}catch(e){p({title:"保存失败",description:"请稍后重试",variant:"destructive"})}finally{n(!1)}}},g=async()=>{if(a){d(!0);try{if(!(await fetch("/api/webhook/test",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({url:a})})).ok)throw Error("测试失败");p({title:"测试成功",description:"Webhook 调用成功,请检查目标服务器是否收到请求"})}catch(e){p({title:"测试失败",description:"请检查 URL 是否正确且可访问",variant:"destructive"})}finally{d(!1)}}};return(0,t.jsxs)("form",{onSubmit:j,className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"space-y-0.5",children:[(0,t.jsx)(f.J,{children:"启用 Webhook"}),(0,t.jsx)("div",{className:"text-sm text-muted-foreground",children:"当收到新邮件时通知指定的 URL"})]}),(0,t.jsx)(U,{checked:e,onCheckedChange:s})]}),e&&(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(f.J,{htmlFor:"webhook-url",children:"Webhook URL"}),(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsx)(y.p,{id:"webhook-url",placeholder:"https://example.com/webhook",value:a,onChange:e=>i(e.target.value),type:"url",required:!0}),(0,t.jsx)(r.$,{type:"submit",disabled:l,className:"flex-shrink-0",children:l?(0,t.jsx)($.A,{className:"w-4 h-4 animate-spin"}):"保存"}),(0,t.jsx)(Y.Bc,{children:(0,t.jsxs)(Y.m_,{children:[(0,t.jsx)(Y.k$,{asChild:!0,children:(0,t.jsx)(r.$,{type:"button",variant:"outline",onClick:g,disabled:c||!a,children:c?(0,t.jsx)($.A,{className:"w-4 h-4 animate-spin"}):(0,t.jsx)(J,{className:"w-4 h-4"})})}),(0,t.jsx)(Y.ZI,{children:(0,t.jsx)("p",{children:"发送测试消息到此 Webhook"})})]})})]}),(0,t.jsx)("p",{className:"text-xs text-muted-foreground",children:"我们会向此 URL 发送 POST 请求,包含新邮件的相关信息"})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("button",{type:"button",className:"flex items-center gap-1 text-sm text-muted-foreground hover:text-foreground transition-colors",onClick:()=>m(!o),children:[o?(0,t.jsx)(H,{className:"w-4 h-4"}):(0,t.jsx)(z.A,{className:"w-4 h-4"}),"查看数据格式说明"]}),o&&(0,t.jsxs)("div",{className:"rounded-md bg-muted p-4 text-sm space-y-3",children:[(0,t.jsx)("p",{children:"当收到新邮件时，我们会向配置的 URL 发送 POST 请求，请求头包含:"}),(0,t.jsxs)("pre",{className:"bg-background p-2 rounded text-xs",children:["Content-Type: application/json","\n","X-Webhook-Event: new_message"]}),(0,t.jsx)("p",{children:"请求体示例:"}),(0,t.jsx)("pre",{className:"bg-background p-2 rounded text-xs overflow-auto",children:'{\n  "emailId": "email-uuid",\n  "messageId": "message-uuid",\n  "fromAddress": "<EMAIL>",\n  "subject": "邮件主题",\n  "content": "邮件文本内容",\n  "html": "邮件HTML内容",\n  "receivedAt": "2024-01-01T12:00:00.000Z",\n  "toAddress": "your-email@'.concat(window.location.host,'"\n}')})]})]})]})]})}var G=a(2619),D=a(8941);let V={[G.gg.DUKE]:d,[G.gg.KNIGHT]:o,[G.gg.CIVILIAN]:m.A},W={[G.gg.DUKE]:"公爵",[G.gg.KNIGHT]:"骑士",[G.gg.CIVILIAN]:"平民"};function q(){let[e,s]=(0,u.useState)(""),[a,i]=(0,u.useState)(!1),[l,n]=(0,u.useState)(G.gg.KNIGHT),{toast:c}=(0,L.dj)(),x=async()=>{if(e){i(!0);try{let a=await fetch("/api/roles/users",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({searchText:e})}),t=await a.json();if(!a.ok)throw Error(t.error||"未知错误");if(!t.user){c({title:"未找到用户",description:"请确认用户名或邮箱地址是否正确",variant:"destructive"});return}if(t.user.role===l){c({title:"用户已是".concat(W[l]),description:"无需重复设置"});return}let i=await fetch("/api/roles/promote",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({userId:t.user.id,roleName:l})});if(!i.ok){let e=await i.json();throw Error(e.error||"设置失败")}c({title:"设置成功",description:"已将用户 ".concat(t.user.username||t.user.email," 设为").concat(W[l])}),s("")}catch(e){c({title:"设置失败",description:e instanceof Error?e.message:"请稍后重试",variant:"destructive"})}finally{i(!1)}}},h=V[l];return(0,t.jsxs)("div",{className:"bg-background rounded-lg border-2 border-primary/20 p-6",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2 mb-6",children:[(0,t.jsx)(h,{className:"w-5 h-5 text-primary"}),(0,t.jsx)("h2",{className:"text-lg font-semibold",children:"角色管理"})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex gap-4",children:[(0,t.jsx)("div",{className:"flex-1",children:(0,t.jsx)(y.p,{value:e,onChange:e=>s(e.target.value),placeholder:"输入用户名或邮箱"})}),(0,t.jsxs)(D.l6,{value:l,onValueChange:e=>n(e),children:[(0,t.jsx)(D.bq,{className:"w-32",children:(0,t.jsx)(D.yv,{})}),(0,t.jsxs)(D.gC,{children:[(0,t.jsx)(D.eb,{value:G.gg.DUKE,children:(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(d,{className:"w-4 h-4"}),"公爵"]})}),(0,t.jsx)(D.eb,{value:G.gg.KNIGHT,children:(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(o,{className:"w-4 h-4"}),"骑士"]})}),(0,t.jsx)(D.eb,{value:G.gg.CIVILIAN,children:(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(m.A,{className:"w-4 h-4"}),"平民"]})})]})]})]}),(0,t.jsx)(r.$,{onClick:x,disabled:a||!e.trim(),className:"w-full",children:a?(0,t.jsx)($.A,{className:"w-4 h-4 animate-spin"}):"设为".concat(W[l])})]})]})}function Z(){var e;let{data:s}=(0,l.wV)(),a=null==s?void 0:null===(e=s.user)||void 0===e?void 0:e.roles;return{checkPermission:e=>!!a&&(0,G._m)(a.map(e=>e.name),e),hasRole:e=>!!a&&a.some(s=>s.name===e),roles:a}}var B=a(8200);function F(){let[e,s]=(0,u.useState)(""),[a,i]=(0,u.useState)(""),[l,n]=(0,u.useState)(""),[c,d]=(0,u.useState)(B.q.MAX_ACTIVE_EMAILS.toString()),[o,m]=(0,u.useState)(!1),{toast:x}=(0,L.dj)();(0,u.useEffect)(()=>{p()},[]);let p=async()=>{let e=await fetch("/api/config");if(e.ok){let a=await e.json();s(a.defaultRole),i(a.emailDomains),n(a.adminContact),d(a.maxEmails||B.q.MAX_ACTIVE_EMAILS.toString())}},j=async()=>{m(!0);try{if(!(await fetch("/api/config",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({defaultRole:e,emailDomains:a,adminContact:l,maxEmails:c||B.q.MAX_ACTIVE_EMAILS.toString()})})).ok)throw Error("保存失败");x({title:"保存成功",description:"网站设置已更新"})}catch(e){x({title:"保存失败",description:e instanceof Error?e.message:"请稍后重试",variant:"destructive"})}finally{m(!1)}};return(0,t.jsxs)("div",{className:"bg-background rounded-lg border-2 border-primary/20 p-6",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2 mb-6",children:[(0,t.jsx)(h,{className:"w-5 h-5 text-primary"}),(0,t.jsx)("h2",{className:"text-lg font-semibold",children:"网站设置"})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center gap-4",children:[(0,t.jsx)("span",{className:"text-sm",children:"新用户默认角色:"}),(0,t.jsxs)(D.l6,{value:e,onValueChange:s,children:[(0,t.jsx)(D.bq,{className:"w-32",children:(0,t.jsx)(D.yv,{})}),(0,t.jsxs)(D.gC,{children:[(0,t.jsx)(D.eb,{value:G.gg.DUKE,children:"公爵"}),(0,t.jsx)(D.eb,{value:G.gg.KNIGHT,children:"骑士"}),(0,t.jsx)(D.eb,{value:G.gg.CIVILIAN,children:"平民"})]})]})]}),(0,t.jsxs)("div",{className:"flex items-center gap-4",children:[(0,t.jsx)("span",{className:"text-sm",children:"邮箱域名:"}),(0,t.jsx)("div",{className:"flex-1",children:(0,t.jsx)(y.p,{value:a,onChange:e=>i(e.target.value),placeholder:"多个域名用逗号分隔，如: moemail.app,bitibiti.com"})})]}),(0,t.jsxs)("div",{className:"flex items-center gap-4",children:[(0,t.jsx)("span",{className:"text-sm",children:"管理员联系方式:"}),(0,t.jsx)("div",{className:"flex-1",children:(0,t.jsx)(y.p,{value:l,onChange:e=>n(e.target.value),placeholder:"如: 微信号、邮箱等"})})]}),(0,t.jsxs)("div",{className:"flex items-center gap-4",children:[(0,t.jsx)("span",{className:"text-sm",children:"最大邮箱数量:"}),(0,t.jsx)("div",{className:"flex-1",children:(0,t.jsx)(y.p,{type:"number",min:"1",max:"100",value:c,onChange:e=>d(e.target.value),placeholder:"默认为 ".concat(B.q.MAX_ACTIVE_EMAILS)})})]}),(0,t.jsx)(r.$,{onClick:j,disabled:o,className:"w-full",children:"保存"})]})]})}var Q=a(6744),ee=a(3473),es=a(3920),ea=a(5686),et=a(705),ei=a(5023),er=a(3244);function el(){let[e,s]=(0,u.useState)([]),[a,i]=(0,u.useState)(!1),[l,n]=(0,u.useState)(!1),[c,d]=(0,u.useState)(""),[o,m]=(0,u.useState)(null),{toast:x}=(0,L.dj)(),{copyToClipboard:h}=(0,ei.T)(),[p,j]=(0,u.useState)(!1),[g,v]=(0,u.useState)(!0),{checkPermission:N}=Z(),w=N(G.Jj.MANAGE_API_KEY),b=async()=>{try{let e=await fetch("/api/api-keys");if(!e.ok)throw Error("获取 API Keys 失败");let a=await e.json();s(a.apiKeys)}catch(e){console.error(e),x({title:"获取失败",description:"获取 API Keys 列表失败",variant:"destructive"})}finally{v(!1)}};(0,u.useEffect)(()=>{w&&b()},[w]);let{config:k}=(0,er.U)(),A=async()=>{if(c.trim()){i(!0);try{let e=await fetch("/api/api-keys",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({name:c})});if(!e.ok)throw Error("创建 API Key 失败");let s=await e.json();m(s.key),b()}catch(e){x({title:"创建失败",description:e instanceof Error?e.message:"请稍后重试",variant:"destructive"}),n(!1)}finally{i(!1)}}},C=async(e,a)=>{try{if(!(await fetch("/api/api-keys/".concat(e),{method:"PATCH",headers:{"Content-Type":"application/json"},body:JSON.stringify({enabled:a})})).ok)throw Error("更新失败");s(s=>s.map(s=>s.id===e?{...s,enabled:a}:s))}catch(e){console.error(e),x({title:"更新失败",description:"更新 API Key 状态失败",variant:"destructive"})}},S=async e=>{try{if(!(await fetch("/api/api-keys/".concat(e),{method:"DELETE"})).ok)throw Error("删除失败");s(s=>s.filter(s=>s.id!==e)),x({title:"删除成功",description:"API Key 已删除"})}catch(e){console.error(e),x({title:"删除失败",description:"删除 API Key 失败",variant:"destructive"})}};return(0,t.jsxs)("div",{className:"bg-background rounded-lg border-2 border-primary/20 p-6 space-y-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(Q.A,{className:"w-5 h-5 text-primary"}),(0,t.jsx)("h2",{className:"text-lg font-semibold",children:"API Keys"})]}),w&&(0,t.jsxs)(et.lG,{open:l,onOpenChange:n,children:[(0,t.jsx)(et.zM,{asChild:!0,children:(0,t.jsxs)(r.$,{className:"gap-2",onClick:()=>n(!0),children:[(0,t.jsx)(ee.A,{className:"w-4 h-4"}),"创建 API Key"]})}),(0,t.jsxs)(et.Cf,{children:[(0,t.jsxs)(et.c7,{children:[(0,t.jsx)(et.L3,{children:o?"API Key 创建成功":"创建新的 API Key"}),o&&(0,t.jsx)(et.rr,{className:"text-destructive",children:"请立即保存此密钥，它只会显示一次且无法恢复"})]}),o?(0,t.jsx)("div",{className:"space-y-4 py-4",children:(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(f.J,{children:"API Key"}),(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsx)(y.p,{value:o,readOnly:!0,className:"font-mono text-sm"}),(0,t.jsx)(r.$,{variant:"outline",size:"icon",onClick:()=>h(o),children:(0,t.jsx)(es.A,{className:"w-4 h-4"})})]})]})}):(0,t.jsx)("div",{className:"space-y-4 py-4",children:(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(f.J,{children:"名称"}),(0,t.jsx)(y.p,{value:c,onChange:e=>d(e.target.value),placeholder:"为你的 API Key 起个名字"})]})}),(0,t.jsxs)(et.Es,{children:[(0,t.jsx)(et.HM,{asChild:!0,children:(0,t.jsx)(r.$,{variant:"outline",onClick:()=>{n(!1),d(""),m(null)},disabled:a,children:o?"完成":"取消"})}),!o&&(0,t.jsx)(r.$,{onClick:A,disabled:a||!c.trim(),children:a?(0,t.jsx)($.A,{className:"w-4 h-4 animate-spin"}):"创建"})]})]})]})]}),w?(0,t.jsx)("div",{className:"space-y-4",children:g?(0,t.jsxs)("div",{className:"text-center py-8 space-y-3",children:[(0,t.jsx)("div",{className:"w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center mx-auto",children:(0,t.jsx)($.A,{className:"w-6 h-6 text-primary animate-spin"})}),(0,t.jsx)("div",{children:(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"加载中..."})})]}):0===e.length?(0,t.jsxs)("div",{className:"text-center py-8 space-y-3",children:[(0,t.jsx)("div",{className:"w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center mx-auto",children:(0,t.jsx)(Q.A,{className:"w-6 h-6 text-primary"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-lg font-medium",children:"没有 API Keys"}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground mt-1",children:'点击上方的创建 "API Key" 按钮来创建你的第一个 API Key'})]})]}):(0,t.jsxs)(t.Fragment,{children:[e.map(e=>(0,t.jsxs)("div",{className:"flex items-center justify-between p-4 rounded-lg border bg-card",children:[(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsx)("div",{className:"font-medium",children:e.name}),(0,t.jsxs)("div",{className:"text-sm text-muted-foreground",children:["创建于 ",new Date(e.createdAt).toLocaleString()]})]}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(U,{checked:e.enabled,onCheckedChange:s=>C(e.id,s)}),(0,t.jsx)(r.$,{variant:"ghost",size:"icon",onClick:()=>S(e.id),children:(0,t.jsx)(ea.A,{className:"w-4 h-4"})})]})]},e.id)),(0,t.jsxs)("div",{className:"mt-8 space-y-4",children:[(0,t.jsxs)("button",{type:"button",className:"flex items-center gap-1 text-sm text-muted-foreground hover:text-foreground transition-colors",onClick:()=>j(!p),children:[p?(0,t.jsx)(H,{className:"w-4 h-4"}):(0,t.jsx)(z.A,{className:"w-4 h-4"}),"查看使用文档"]}),p&&(0,t.jsxs)("div",{className:"rounded-lg border bg-card p-4 space-y-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("div",{className:"text-sm font-medium",children:"生成临时邮箱"}),(0,t.jsx)(r.$,{variant:"ghost",size:"icon",onClick:()=>h("curl -X POST ".concat(window.location.protocol,"//").concat(window.location.host,'/api/emails/generate \\\n  -H "X-API-Key: YOUR_API_KEY" \\\n  -H "Content-Type: application/json" \\\n  -d \'{\n    "name": "test",\n    "expiryTime": 3600000,\n    "domain": "moemail.app"\n  }\'')),children:(0,t.jsx)(es.A,{className:"w-4 h-4"})})]}),(0,t.jsx)("pre",{className:"text-xs bg-muted/50 rounded-lg p-4 overflow-x-auto",children:"curl -X POST ".concat(window.location.protocol,"//").concat(window.location.host,'/api/emails/generate \\\n  -H "X-API-Key: YOUR_API_KEY" \\\n  -H "Content-Type: application/json" \\\n  -d \'{\n    "name": "test",\n    "expiryTime": 3600000,\n    "domain": "moemail.app"\n  }\'')})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("div",{className:"text-sm font-medium",children:"获取邮箱列表"}),(0,t.jsx)(r.$,{variant:"ghost",size:"icon",onClick:()=>h("curl ".concat(window.location.protocol,"//").concat(window.location.host,'/api/emails?cursor=CURSOR \\\n  -H "X-API-Key: YOUR_API_KEY"')),children:(0,t.jsx)(es.A,{className:"w-4 h-4"})})]}),(0,t.jsx)("pre",{className:"text-xs bg-muted/50 rounded-lg p-4 overflow-x-auto",children:"curl ".concat(window.location.protocol,"//").concat(window.location.host,'/api/emails?cursor=CURSOR \\\n  -H "X-API-Key: YOUR_API_KEY"')})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("div",{className:"text-sm font-medium",children:"获取邮件列表"}),(0,t.jsx)(r.$,{variant:"ghost",size:"icon",onClick:()=>h("curl ".concat(window.location.protocol,"//").concat(window.location.host,'/api/emails/{emailId}?cursor=CURSOR \\\n  -H "X-API-Key: YOUR_API_KEY"')),children:(0,t.jsx)(es.A,{className:"w-4 h-4"})})]}),(0,t.jsx)("pre",{className:"text-xs bg-muted/50 rounded-lg p-4 overflow-x-auto",children:"curl ".concat(window.location.protocol,"//").concat(window.location.host,'/api/emails/{emailId}?cursor=CURSOR \\\n  -H "X-API-Key: YOUR_API_KEY"')})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("div",{className:"text-sm font-medium",children:"获取单封邮件"}),(0,t.jsx)(r.$,{variant:"ghost",size:"icon",onClick:()=>h("curl ".concat(window.location.protocol,"//").concat(window.location.host,'/api/emails/{emailId}/{messageId} \\\n  -H "X-API-Key: YOUR_API_KEY"')),children:(0,t.jsx)(es.A,{className:"w-4 h-4"})})]}),(0,t.jsx)("pre",{className:"text-xs bg-muted/50 rounded-lg p-4 overflow-x-auto",children:"curl ".concat(window.location.protocol,"//").concat(window.location.host,'/api/emails/{emailId}/{messageId} \\\n  -H "X-API-Key: YOUR_API_KEY"')})]}),(0,t.jsxs)("div",{className:"text-xs text-muted-foreground mt-4",children:[(0,t.jsx)("p",{children:"注意："}),(0,t.jsxs)("ul",{className:"list-disc list-inside space-y-1 mt-2",children:[(0,t.jsx)("li",{children:"请将 YOUR_API_KEY 替换为你的实际 API Key"}),(0,t.jsx)("li",{children:"emailId 是邮箱的唯一标识符"}),(0,t.jsx)("li",{children:"messageId 是邮件的唯一标识符"}),(0,t.jsx)("li",{children:"expiryTime 是邮箱的有效期（毫秒），可选值：3600000（1小时）、86400000（1天）、604800000（7天）、0（永久）"}),(0,t.jsx)("li",{children:"domain 是邮箱域名，可通过 /api/emails/domains 获取可用域名列表"}),(0,t.jsx)("li",{children:"cursor 用于分页，从上一次请求的响应中获取 nextCursor"}),(0,t.jsx)("li",{children:"所有请求都需要包含 X-API-Key 请求头"})]})]})]})]})]})}):(0,t.jsxs)("div",{className:"text-center text-muted-foreground py-8",children:[(0,t.jsx)("p",{children:"需要公爵或更高权限才能管理 API Key"}),(0,t.jsx)("p",{className:"mt-2",children:"请联系网站管理员升级您的角色"}),(null==k?void 0:k.adminContact)&&(0,t.jsxs)("p",{className:"mt-2",children:["管理员联系方式：",k.adminContact]})]})]})}var en=a(1773);let ec=(0,n.A)("Eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]),ed=(0,n.A)("EyeOff",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]]),eo=(0,n.A)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]]);function em(){let[e,s]=(0,u.useState)([]),[a,i]=(0,u.useState)([]),[l,n]=(0,u.useState)(!1),[c,d]=(0,u.useState)(!1),[o,m]=(0,u.useState)(!1),[x,h]=(0,u.useState)({jwt:""}),[j,g]=(0,u.useState)(""),[v,N]=(0,u.useState)(null),[w,b]=(0,u.useState)([]),[k,A]=(0,u.useState)([]),[C,S]=(0,u.useState)("input"),[I,E]=(0,u.useState)(!1),[P,O]=(0,u.useState)(!1),[T,K]=(0,u.useState)(!0),[_,M]=(0,u.useState)(!1),{toast:R}=(0,L.dj)(),{checkPermission:U}=Z(),J=U(G.Jj.MANAGE_WEBHOOK),Y=async()=>{if(J)try{let e=await fetch("/api/email-credentials");if(!e.ok)throw Error("获取邮箱凭证失败");let a=await e.json();s(a.credentials)}catch(e){console.error(e),R({title:"获取失败",description:"获取邮箱凭证列表失败",variant:"destructive"})}},X=async()=>{try{let e=await fetch("/api/email-credentials/bindings");if(!e.ok)throw Error("获取邮箱绑定失败");let s=await e.json();i(s.bindings)}catch(e){console.error(e),R({title:"获取失败",description:"获取邮箱绑定列表失败",variant:"destructive"})}},D=async()=>{K(!0),await Promise.all([J?Y():Promise.resolve(),X()]),K(!1),M(!1)};(0,u.useEffect)(()=>{D()},[J]);let{config:V}=(0,er.U)(),W=async()=>{M(!0),await D()},q=async()=>{if(!x.jwt.trim()){R({title:"请输入邮箱凭证",description:"邮箱凭证不能为空",variant:"destructive"});return}n(!0);try{let e=await fetch("/api/email-credentials",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(x)});if(!e.ok){let s=await e.json();throw Error(s.error||"绑定邮箱失败")}R({title:"绑定成功",description:"邮箱绑定成功"}),X(),F()}catch(e){R({title:"绑定失败",description:e instanceof Error?e.message:"请稍后重试",variant:"destructive"})}finally{n(!1)}},B=async()=>{if("input"===C){if(!j.trim()){R({title:"请输入邮箱地址",description:"邮箱地址不能为空",variant:"destructive"});return}}else if(0===k.length){R({title:"请选择邮箱",description:"至少选择一个邮箱",variant:"destructive"});return}n(!0);try{let e=J?"/api/email-credentials/extract":"/api/email-credentials/extract-user",s="input"===C?{emailAddress:j}:{emailAddresses:k},a=await fetch(e,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(s)});if(!a.ok){let e=await a.json();throw Error(e.error||"提取邮箱凭证失败")}let t=await a.json();"input"===C?(N(t.credential),b([])):(b(t.credentials),N(null)),R({title:"提取成功",description:"已提取 ".concat("input"===C?1:t.credentials.length," 个邮箱凭证")})}catch(e){R({title:"提取失败",description:e instanceof Error?e.message:"请稍后重试",variant:"destructive"})}finally{n(!1)}},F=()=>{d(!1),h({jwt:""})},Q=async e=>{try{if(!(await fetch("/api/email-credentials/bindings",{method:"DELETE",headers:{"Content-Type":"application/json"},body:JSON.stringify({emailAddress:e})})).ok)throw Error("解绑失败");i(s=>s.filter(s=>s.emailAddress!==e)),R({title:"解绑成功",description:"邮箱已解绑"})}catch(e){console.error(e),R({title:"解绑失败",description:"解绑邮箱失败",variant:"destructive"})}},ei=async e=>{try{await navigator.clipboard.writeText(e),R({title:"复制成功",description:"已复制到剪贴板"})}catch(e){R({title:"复制失败",description:"无法复制到剪贴板",variant:"destructive"})}};return(0,t.jsxs)("div",{className:"bg-background rounded-lg border-2 border-primary/20 p-6 space-y-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(p.A,{className:"w-5 h-5 text-primary"}),(0,t.jsx)("h2",{className:"text-lg font-semibold",children:"邮箱凭证系统"})]}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(r.$,{variant:"ghost",size:"icon",onClick:W,disabled:_,className:_?"animate-spin":"",children:(0,t.jsx)(en.A,{className:"w-4 h-4"})}),(0,t.jsxs)(et.lG,{open:c,onOpenChange:d,children:[(0,t.jsx)(et.zM,{asChild:!0,children:(0,t.jsxs)(r.$,{className:"gap-2",children:[(0,t.jsx)(ee.A,{className:"w-4 h-4"}),"绑定邮箱"]})}),(0,t.jsxs)(et.Cf,{className:"max-w-md",children:[(0,t.jsxs)(et.c7,{children:[(0,t.jsx)(et.L3,{children:"绑定邮箱"}),(0,t.jsx)(et.rr,{children:"输入邮箱凭证来绑定邮箱到您的账户"})]}),(0,t.jsx)("div",{className:"space-y-4 py-4",children:(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(f.J,{children:"邮箱凭证 (JWT)"}),(0,t.jsx)(y.p,{value:x.jwt,onChange:e=>h(s=>({...s,jwt:e.target.value})),placeholder:"请输入邮箱凭证..."})]})}),(0,t.jsxs)(et.Es,{children:[(0,t.jsx)(et.HM,{asChild:!0,children:(0,t.jsx)(r.$,{variant:"outline",onClick:F,disabled:l,children:"取消"})}),(0,t.jsx)(r.$,{onClick:q,disabled:l,children:l?(0,t.jsx)($.A,{className:"w-4 h-4 animate-spin"}):"绑定"})]})]})]}),J&&(0,t.jsxs)(et.lG,{open:o,onOpenChange:m,children:[(0,t.jsx)(et.zM,{asChild:!0,children:(0,t.jsxs)(r.$,{variant:"outline",className:"gap-2",children:[(0,t.jsx)(ec,{className:"w-4 h-4"}),"提取凭证"]})}),(0,t.jsxs)(et.Cf,{className:"max-w-md",children:[(0,t.jsxs)(et.c7,{children:[(0,t.jsx)(et.L3,{children:"提取邮箱凭证"}),(0,t.jsx)(et.rr,{children:"输入邮箱地址来提取对应的邮箱凭证"})]}),(0,t.jsxs)("div",{className:"space-y-4 py-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(f.J,{children:"邮箱地址"}),(0,t.jsx)(y.p,{type:"email",value:j,onChange:e=>g(e.target.value),placeholder:"<EMAIL>"})]}),v&&(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(f.J,{children:"提取的凭证"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(y.p,{type:I?"text":"password",value:v.jwt,readOnly:!0,className:"pr-20"}),(0,t.jsxs)("div",{className:"absolute right-2 top-0 h-full flex items-center gap-1",children:[(0,t.jsx)(r.$,{type:"button",variant:"ghost",size:"icon",onClick:()=>E(!I),children:I?(0,t.jsx)(ed,{className:"w-4 h-4"}):(0,t.jsx)(ec,{className:"w-4 h-4"})}),(0,t.jsx)(r.$,{type:"button",variant:"ghost",size:"icon",onClick:()=>ei(v.jwt),children:(0,t.jsx)(es.A,{className:"w-4 h-4"})})]})]}),(0,t.jsxs)("div",{className:"text-sm text-muted-foreground",children:["绑定用户数: ",v.bindingCount]})]})]}),(0,t.jsxs)(et.Es,{children:[(0,t.jsx)(et.HM,{asChild:!0,children:(0,t.jsx)(r.$,{variant:"outline",onClick:()=>{m(!1),g(""),N(null),b([]),A([]),S("input"),E(!1)},children:"关闭"})}),(0,t.jsx)(r.$,{onClick:B,disabled:l,children:l?(0,t.jsx)($.A,{className:"w-4 h-4 animate-spin"}):"提取"})]})]})]})]})]}),T?(0,t.jsxs)("div",{className:"text-center py-8 space-y-3",children:[(0,t.jsx)("div",{className:"w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center mx-auto",children:(0,t.jsx)($.A,{className:"w-6 h-6 text-primary animate-spin"})}),(0,t.jsx)("div",{children:(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"加载中..."})})]}):(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)("h3",{className:"text-lg font-medium",children:"我绑定的邮箱"}),0===a.length?(0,t.jsxs)("div",{className:"text-center py-6 space-y-3 border rounded-lg",children:[(0,t.jsx)("div",{className:"w-10 h-10 rounded-full bg-primary/10 flex items-center justify-center mx-auto",children:(0,t.jsx)(p.A,{className:"w-5 h-5 text-primary"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"您还没有绑定任何邮箱"}),(0,t.jsx)("p",{className:"text-xs text-muted-foreground mt-1",children:'点击上方的 "绑定邮箱" 按钮来绑定第一个邮箱'})]})]}):(0,t.jsx)("div",{className:"space-y-2",children:a.map(e=>(0,t.jsxs)("div",{className:"flex items-center justify-between p-3 rounded-lg border bg-card",children:[(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsx)("div",{className:"font-medium",children:e.emailAddress}),(0,t.jsxs)("div",{className:"text-xs text-muted-foreground",children:["绑定于 ",new Date(e.createdAt).toLocaleString()]})]}),(0,t.jsx)(r.$,{variant:"ghost",size:"sm",onClick:()=>Q(e.emailAddress),children:(0,t.jsx)(ea.A,{className:"w-4 h-4"})})]},e.id))})]}),J&&(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)("h3",{className:"text-lg font-medium",children:"系统邮箱凭证 (管理员)"}),0===e.length?(0,t.jsxs)("div",{className:"text-center py-6 space-y-3 border rounded-lg",children:[(0,t.jsx)("div",{className:"w-10 h-10 rounded-full bg-primary/10 flex items-center justify-center mx-auto",children:(0,t.jsx)(p.A,{className:"w-5 h-5 text-primary"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"系统中还没有邮箱凭证"}),(0,t.jsx)("p",{className:"text-xs text-muted-foreground mt-1",children:"创建邮箱时会自动生成对应的凭证"})]})]}):(0,t.jsx)("div",{className:"space-y-2",children:e.map(e=>(0,t.jsxs)("div",{className:"flex items-center justify-between p-4 rounded-lg border bg-card",children:[(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsx)("div",{className:"font-medium",children:e.emailAddress}),(0,t.jsxs)("div",{className:"flex items-center gap-4 text-xs text-muted-foreground",children:[(0,t.jsxs)("span",{children:["创建于 ",new Date(e.createdAt).toLocaleString()]}),e.lastUsedAt&&(0,t.jsxs)("span",{children:["最后使用: ",new Date(e.lastUsedAt).toLocaleString()]}),(0,t.jsxs)("div",{className:"flex items-center gap-1",children:[(0,t.jsx)(eo,{className:"w-3 h-3"}),(0,t.jsxs)("span",{children:[e.bindingCount," 个用户绑定"]})]})]})]}),(0,t.jsx)("div",{className:"flex items-center gap-2",children:(0,t.jsx)("span",{className:"text-xs px-2 py-1 rounded ".concat(e.enabled?"bg-green-100 text-green-800":"bg-gray-100 text-gray-800"),children:e.enabled?"启用":"禁用"})})]},e.id))})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("button",{type:"button",className:"flex items-center gap-1 text-sm text-muted-foreground hover:text-foreground transition-colors",onClick:()=>O(!P),children:[P?(0,t.jsx)(H,{className:"w-4 h-4"}):(0,t.jsx)(z.A,{className:"w-4 h-4"}),"查看使用说明"]}),P&&(0,t.jsxs)("div",{className:"rounded-lg border bg-card p-4 space-y-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("h4",{className:"font-medium",children:"邮箱凭证系统说明"}),(0,t.jsxs)("div",{className:"text-sm text-muted-foreground space-y-2",children:[(0,t.jsxs)("p",{children:["1. ",(0,t.jsx)("strong",{children:"邮箱创建"}),"：系统创建邮箱时会自动生成对应的JWT凭证"]}),(0,t.jsxs)("p",{children:["2. ",(0,t.jsx)("strong",{children:"凭证绑定"}),"：用户可以通过邮箱凭证绑定邮箱到自己的账户"]}),(0,t.jsxs)("p",{children:["3. ",(0,t.jsx)("strong",{children:"凭证提取"}),"：管理员可以提取任何邮箱的凭证供用户使用"]}),(0,t.jsxs)("p",{children:["4. ",(0,t.jsx)("strong",{children:"绑定统计"}),"：显示每个邮箱绑定了多少个用户（不包括管理员）"]})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("h4",{className:"font-medium",children:"使用流程"}),(0,t.jsxs)("div",{className:"text-sm text-muted-foreground space-y-1",children:[(0,t.jsxs)("p",{children:[(0,t.jsx)("strong",{children:"用户："}),' 获取邮箱凭证 → 点击"绑定邮箱" → 输入凭证 → 完成绑定']}),(0,t.jsxs)("p",{children:[(0,t.jsx)("strong",{children:"管理员："})," 查看所有邮箱凭证 → 提取特定邮箱凭证 → 提供给用户"]})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("h4",{className:"font-medium",children:"安全说明"}),(0,t.jsxs)("div",{className:"text-sm text-muted-foreground space-y-1",children:[(0,t.jsx)("p",{children:"• 邮箱凭证是JWT格式，包含邮箱访问权限"}),(0,t.jsx)("p",{children:"• 凭证永久有效，请妥善保管"}),(0,t.jsx)("p",{children:"• 系统会记录凭证使用情况"}),(0,t.jsx)("p",{children:"• 管理员可以查看绑定统计信息"})]})]})]})]})]})]})}let ex={emperor:{name:"皇帝",icon:c},duke:{name:"公爵",icon:d},knight:{name:"骑士",icon:o},civilian:{name:"平民",icon:m.A}};function eh(e){let{user:s}=e,a=(0,j.useRouter)(),{checkPermission:n}=Z(),c=n(G.Jj.MANAGE_WEBHOOK),d=n(G.Jj.PROMOTE_USER),o=n(G.Jj.MANAGE_CONFIG),m=n(G.Jj.MANAGE_EMAIL_CREDENTIALS);return(0,t.jsxs)("div",{className:"max-w-2xl mx-auto space-y-6",children:[(0,t.jsx)("div",{className:"bg-background rounded-lg border-2 border-primary/20 p-6",children:(0,t.jsxs)("div",{className:"flex items-center gap-6",children:[(0,t.jsx)("div",{className:"relative",children:s.image&&(0,t.jsx)(i.default,{src:s.image,alt:s.name||"用户头像",width:80,height:80,className:"rounded-full ring-2 ring-primary/20"})}),(0,t.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("h2",{className:"text-xl font-bold truncate",children:s.name}),s.email&&(0,t.jsxs)("div",{className:"flex items-center gap-1 text-xs text-primary bg-primary/10 px-2 py-0.5 rounded-full flex-shrink-0",children:[(0,t.jsx)(x.A,{className:"w-3 h-3"}),"已关联"]})]}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground truncate mt-1",children:s.email?s.email:"用户名: ".concat(s.username)}),s.roles&&(0,t.jsx)("div",{className:"flex gap-2 mt-2",children:s.roles.map(e=>{let{name:s}=e,a=ex[s],i=a.icon;return(0,t.jsxs)("div",{className:"flex items-center gap-1 text-xs bg-primary/10 text-primary px-2 py-0.5 rounded",title:a.name,children:[(0,t.jsx)(i,{className:"w-3 h-3"}),a.name]},s)})})]})]})}),c&&(0,t.jsxs)("div",{className:"bg-background rounded-lg border-2 border-primary/20 p-6",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2 mb-6",children:[(0,t.jsx)(h,{className:"w-5 h-5 text-primary"}),(0,t.jsx)("h2",{className:"text-lg font-semibold",children:"Webhook 配置"})]}),(0,t.jsx)(X,{})]}),o&&(0,t.jsx)(F,{}),d&&(0,t.jsx)(q,{}),c&&(0,t.jsx)(el,{}),m&&(0,t.jsx)(em,{}),(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 px-1",children:[(0,t.jsxs)(r.$,{onClick:()=>a.push("/moe"),className:"gap-2 flex-1",children:[(0,t.jsx)(p.A,{className:"w-4 h-4"}),"返回邮箱"]}),(0,t.jsx)(r.$,{variant:"outline",onClick:()=>(0,l.CI)({callbackUrl:"/"}),className:"flex-1",children:"退出登录"})]})]})}},2354:(e,s,a)=>{"use strict";a.d(s,{Bc:()=>n,ZI:()=>o,k$:()=>d,m_:()=>c});var t=a(5155),i=a(2115),r=a(2614),l=a(2558);let n=r.Kq,c=r.bL,d=r.l9,o=i.forwardRef((e,s)=>{let{className:a,sideOffset:i=4,...n}=e;return(0,t.jsx)(r.UC,{ref:s,sideOffset:i,className:(0,l.cn)("z-50 overflow-hidden rounded-md bg-primary px-3 py-1.5 text-xs text-primary-foreground animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",a),...n})});o.displayName=r.UC.displayName},2699:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(7401).A)("UserRound",[["circle",{cx:"12",cy:"8",r:"5",key:"1hypcn"}],["path",{d:"M20 21a8 8 0 0 0-16 0",key:"rfgkzh"}]])}},e=>{var s=s=>e(e.s=s);e.O(0,[630,246,568,10,275,86,441,517,358],()=>s(4510)),_N_E=e.O()}]);