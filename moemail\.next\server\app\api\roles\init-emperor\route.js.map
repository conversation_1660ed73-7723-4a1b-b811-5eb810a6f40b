{"version": 3, "file": "app/api/roles/init-emperor/route.js", "mappings": "sFAAA,8DCAA,mHGAA,4SFMO,IAAMA,EAAU,OAAO,eAERC,IACpB,IAAMC,EAAU,MAAMC,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,GAC1B,GAAI,CAACD,GAASE,MAAMC,GAClB,CADsB,MACfC,SAASC,IAAI,CAAC,CAAEC,MAAO,KAAM,EAAG,CAAEC,OAAQ,GAAI,GAGvD,IAAMC,EAAKC,CAAAA,EAAAA,EAAAA,CAAAA,CAAQA,GAEbC,EAAc,MAAMF,EAAGG,KAAK,CAACC,KAAK,CAACC,SAAS,CAAC,CACjDC,MAAOC,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACH,EAAAA,KAAKA,CAACI,IAAI,CAAEC,EAAAA,EAAKA,CAACC,OAAO,EACnCC,KAAM,CACJC,WAAW,CACb,CACF,GAEA,GAAIV,GAAeA,EAAYU,SAAS,CAACC,MAAM,CAAG,EAChD,CADmD,MAC5CjB,SAASC,IAAI,CAAC,CAAEC,MAAO,eAAgB,EAAG,CAAEC,OAAQ,GAAI,GAGjE,GAAI,CACF,IAAMe,EAAkB,MAAMd,EAAGG,KAAK,CAACS,SAAS,CAACP,SAAS,CAAC,CACzDC,MAAOC,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACK,EAAAA,SAASA,CAACG,MAAM,CAAEvB,EAAQE,IAAI,CAACC,EAAE,EAC3CgB,KAAM,CACJK,KAAM,EACR,CACF,GAEA,GAAIF,GAAiBE,KAAKR,OAASC,EAAAA,EAAKA,CAACC,OAAO,CAC9C,CADgD,MACzCd,SAASC,IAAI,CAAC,CAAEoB,QAAS,SAAU,GAG5C,IAAIC,EAAShB,GAAaP,GAC1B,GAAI,CAACuB,EAAQ,CACX,GAAM,CAACC,EAAQ,CAAG,MAAMnB,EAAGoB,MAAM,CAAChB,EAAAA,KAAKA,EACpCiB,MAAM,CAAC,CACNb,KAAMC,EAAAA,EAAKA,CAACC,OAAO,CACnBY,YAAa,WACf,GACCC,SAAS,CAAC,CAAE5B,GAAIS,EAAAA,KAAKA,CAACT,EAAE,GAC3BuB,EAASC,EAAQxB,EAAE,CAKrB,OAFA,MAAM6B,CAAAA,EAAAA,EAAAA,EAAAA,CAAgBA,CAACxB,EAAIR,EAAQE,IAAI,CAACC,EAAE,CAAEuB,GAErCtB,SAASC,IAAI,CAAC,CAAEoB,QAAS,aAAc,EAChD,CAAE,MAAOnB,EAAO,CAEd,OADA2B,QAAQ3B,KAAK,CAAC,gCAAiCA,GACxCF,SAASC,IAAI,CAClB,CAAEC,MAAO,QAAS,EAClB,CAAEC,OAAQ,GAAI,EAElB,CACF,CCrDA,UAAwB,qBAAmB,EAC3C,YACA,KAAc,GAAS,WACvB,qCACA,mCACA,iBACA,6CACA,CAAK,CACL,iGACA,iBAVA,GAWA,QAAY,EACZ,CAAC,EAID,kBAAQ,wCAAsD,EAC9D,aACA,MAAW,QAAW,EACtB,mBACA,sBACA,CAAK,CACL,CCpBA,8EACA,EAFA,4BAEA,4BACA,MACI,QAA8B,EAClC,qCACA,0BACA,wBACA,gBAAyB,OAAqB,EAC9C,uBACA,CAAS,CACT,CAAK,EAEE,MAAqB,EAC5B,EAAe,GAAsB,MAAM,EAAkB,CAC7D,QAD6D,GAf7D,CAAoB,MAAQ,OAcM,CAdN,CAAW,sBAA2B,aAAe,kDAAyD,uOAAsQ,2RAAuT,kBAAkB,QAAQ,uDAA2D,0DAA8D,iBAAsB,gBAAkB,sEAAgF,kBAAoB,uCAA6C,MAAQ,iBAAmB,2BAA+B,gHAAkI,uBAAyB,8FAA0G,aAAiB,WAAa,sEAA6E,uBAAuB,iCAAmC,QAAQ,EAAE,SAAW,oBAAsB,QAAQ,GAAG,4EAAgF,WAAa,SAAW,2CAAiD,UAAY,+BAAqC,UAAY,oCAA0C,QAAU,uCAA6C,OAAS,yCAA+C,QAAU,0CAAiD,MAAQ,+CAAsD,iBAAmB,ywBAA+5B,4CAA8C,kNAAqQ,qBAAyB,s+CAA0/C,oIAiB7wJ,CAAC,CAAC,EAAC", "sources": ["webpack://_N_E/external commonjs \"node:async_hooks\"", "webpack://_N_E/external commonjs \"node:buffer\"", "webpack://_N_E/./app/api/roles/init-emperor/route.ts", "webpack://_N_E/./app/api/roles/init-emperor/route.ts?90e5", "webpack://_N_E/?a977"], "sourcesContent": ["module.exports = require(\"node:async_hooks\");", "module.exports = require(\"node:buffer\");", "import { auth, assignRoleToUser } from \"@/lib/auth\";\r\nimport { createDb } from \"@/lib/db\";\r\nimport { roles, userRoles } from \"@/lib/schema\";\r\nimport { ROLES } from \"@/lib/permissions\";\r\nimport { eq } from \"drizzle-orm\";\r\n\r\nexport const runtime = \"edge\";\r\n\r\nexport async function GET() {\r\n  const session = await auth();\r\n  if (!session?.user?.id) {\r\n    return Response.json({ error: \"未授权\" }, { status: 401 });\r\n  }\r\n\r\n  const db = createDb();\r\n\r\n  const emperorRole = await db.query.roles.findFirst({\r\n    where: eq(roles.name, ROLES.EMPEROR),\r\n    with: {\r\n      userRoles: true,\r\n    },\r\n  });\r\n\r\n  if (emperorRole && emperorRole.userRoles.length > 0) {\r\n    return Response.json({ error: \"已存在皇帝, 谋反将被处死\" }, { status: 400 });\r\n  }\r\n\r\n  try {\r\n    const currentUserRole = await db.query.userRoles.findFirst({\r\n      where: eq(userRoles.userId, session.user.id),\r\n      with: {\r\n        role: true,\r\n      },\r\n    });\r\n\r\n    if (currentUserRole?.role.name === ROLES.EMPEROR) {\r\n      return Response.json({ message: \"你已经是皇帝了\" });\r\n    }\r\n\r\n    let roleId = emperorRole?.id;\r\n    if (!roleId) {\r\n      const [newRole] = await db.insert(roles)\r\n        .values({\r\n          name: ROLES.EMPEROR,\r\n          description: \"皇帝（网站所有者）\",\r\n        })\r\n        .returning({ id: roles.id });\r\n      roleId = newRole.id;\r\n    }\r\n\r\n    await assignRoleToUser(db, session.user.id, roleId);\r\n\r\n    return Response.json({ message: \"登基成功，你已成为皇帝\" });\r\n  } catch (error) {\r\n    console.error(\"Failed to initialize emperor:\", error);\r\n    return Response.json(\r\n      { error: \"登基称帝失败\" },\r\n      { status: 500 }\r\n    );\r\n  }\r\n} ", "import { AppRouteRouteModule } from \"next/dist/server/route-modules/app-route/module.compiled\";\nimport { RouteKind } from \"next/dist/server/route-kind\";\nimport { patchFetch as _patchFetch } from \"next/dist/server/lib/patch-fetch\";\nimport * as userland from \"F:\\\\CODE\\\\Project\\\\tempmail\\\\moemail\\\\app\\\\api\\\\roles\\\\init-emperor\\\\route.ts\";\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new AppRouteRouteModule({\n    definition: {\n        kind: RouteKind.APP_ROUTE,\n        page: \"/api/roles/init-emperor/route\",\n        pathname: \"/api/roles/init-emperor\",\n        filename: \"route\",\n        bundlePath: \"app/api/roles/init-emperor/route\"\n    },\n    resolvedPagePath: \"F:\\\\CODE\\\\Project\\\\tempmail\\\\moemail\\\\app\\\\api\\\\roles\\\\init-emperor\\\\route.ts\",\n    nextConfigOutput,\n    userland\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return _patchFetch({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\nexport { routeModule, workAsyncStorage, workUnitAsyncStorage, serverHooks, patchFetch,  };\n\n//# sourceMappingURL=app-route.js.map", "var _self___RSC_MANIFEST;\nimport { createServerModuleMap } from \"next/dist/server/app-render/action-utils\";\nimport { setReferenceManifestsSingleton } from \"next/dist/server/app-render/encryption-utils\";\nimport { EdgeRouteModuleWrapper } from \"next/dist/server/web/edge-route-module-wrapper\";\n// Import the userland code.\nimport * as module from \"next-app-loader?name=app%2Fapi%2Froles%2Finit-emperor%2Froute&page=%2Fapi%2Froles%2Finit-emperor%2Froute&pagePath=private-next-app-dir%2Fapi%2Froles%2Finit-emperor%2Froute.ts&appDir=F%3A%5CCODE%5CProject%5Ctempmail%5Cmoemail%5Capp&appPaths=%2Fapi%2Froles%2Finit-emperor%2Froute&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!private-next-app-dir/api/roles/init-emperor/route.ts?__next_edge_ssr_entry__\";\nconst nextConfig = {\"env\":{},\"eslint\":{\"ignoreDuringBuilds\":false},\"typescript\":{\"ignoreBuildErrors\":false,\"tsconfigPath\":\"tsconfig.json\"},\"distDir\":\".next\",\"cleanDistDir\":true,\"assetPrefix\":\"\",\"cacheMaxMemorySize\":52428800,\"configOrigin\":\"next.config.ts\",\"useFileSystemPublicRoutes\":true,\"generateEtags\":true,\"pageExtensions\":[\"tsx\",\"ts\",\"jsx\",\"js\"],\"poweredByHeader\":true,\"compress\":true,\"images\":{\"deviceSizes\":[640,750,828,1080,1200,1920,2048,3840],\"imageSizes\":[16,32,48,64,96,128,256,384],\"path\":\"/_next/image\",\"loader\":\"default\",\"loaderFile\":\"\",\"domains\":[],\"disableStaticImages\":false,\"minimumCacheTTL\":60,\"formats\":[\"image/webp\"],\"dangerouslyAllowSVG\":false,\"contentSecurityPolicy\":\"script-src 'none'; frame-src 'none'; sandbox;\",\"contentDispositionType\":\"attachment\",\"remotePatterns\":[{\"protocol\":\"https\",\"hostname\":\"avatars.githubusercontent.com\"}],\"unoptimized\":false},\"devIndicators\":{\"appIsrStatus\":true,\"buildActivity\":true,\"buildActivityPosition\":\"bottom-right\"},\"onDemandEntries\":{\"maxInactiveAge\":60000,\"pagesBufferLength\":5},\"amp\":{\"canonicalBase\":\"\"},\"basePath\":\"\",\"sassOptions\":{},\"trailingSlash\":false,\"i18n\":null,\"productionBrowserSourceMaps\":false,\"excludeDefaultMomentLocales\":true,\"serverRuntimeConfig\":{},\"publicRuntimeConfig\":{},\"reactProductionProfiling\":false,\"reactStrictMode\":null,\"reactMaxHeadersLength\":6000,\"httpAgentOptions\":{\"keepAlive\":true},\"logging\":{},\"expireTime\":31536000,\"staticPageGenerationTimeout\":60,\"modularizeImports\":{\"@mui/icons-material\":{\"transform\":\"@mui/icons-material/{{member}}\"},\"lodash\":{\"transform\":\"lodash/{{member}}\"}},\"outputFileTracingRoot\":\"F:\\\\CODE\\\\Project\\\\tempmail\\\\moemail\",\"experimental\":{\"cacheLife\":{\"default\":{\"stale\":300,\"revalidate\":900,\"expire\":4294967294},\"seconds\":{\"stale\":0,\"revalidate\":1,\"expire\":60},\"minutes\":{\"stale\":300,\"revalidate\":60,\"expire\":3600},\"hours\":{\"stale\":300,\"revalidate\":3600,\"expire\":86400},\"days\":{\"stale\":300,\"revalidate\":86400,\"expire\":604800},\"weeks\":{\"stale\":300,\"revalidate\":604800,\"expire\":2592000},\"max\":{\"stale\":300,\"revalidate\":2592000,\"expire\":4294967294}},\"cacheHandlers\":{},\"cssChunking\":true,\"multiZoneDraftMode\":false,\"appNavFailHandling\":false,\"prerenderEarlyExit\":true,\"serverMinification\":true,\"serverSourceMaps\":false,\"linkNoTouchStart\":false,\"caseSensitiveRoutes\":false,\"clientSegmentCache\":false,\"preloadEntriesOnStart\":true,\"clientRouterFilter\":true,\"clientRouterFilterRedirects\":false,\"fetchCacheKeyPrefix\":\"\",\"middlewarePrefetch\":\"flexible\",\"optimisticClientCache\":true,\"manualClientBasePath\":false,\"cpus\":11,\"memoryBasedWorkersCount\":false,\"imgOptConcurrency\":null,\"imgOptTimeoutInSeconds\":7,\"imgOptMaxInputPixels\":268402689,\"imgOptSequentialRead\":null,\"isrFlushToDisk\":true,\"workerThreads\":false,\"optimizeCss\":false,\"nextScriptWorkers\":false,\"scrollRestoration\":false,\"externalDir\":false,\"disableOptimizedLoading\":false,\"gzipSize\":true,\"craCompat\":false,\"esmExternals\":true,\"fullySpecified\":false,\"swcTraceProfiling\":false,\"forceSwcTransforms\":false,\"largePageDataBytes\":128000,\"turbo\":{\"root\":\"F:\\\\CODE\\\\Project\\\\tempmail\\\\moemail\"},\"typedRoutes\":false,\"typedEnv\":false,\"parallelServerCompiles\":false,\"parallelServerBuildTraces\":false,\"ppr\":false,\"authInterrupts\":false,\"reactOwnerStack\":false,\"webpackMemoryOptimizations\":false,\"optimizeServerReact\":true,\"useEarlyImport\":false,\"staleTimes\":{\"dynamic\":0,\"static\":300},\"serverComponentsHmrCache\":true,\"staticGenerationMaxConcurrency\":8,\"staticGenerationMinPagesPerWorker\":25,\"dynamicIO\":false,\"inlineCss\":false,\"optimizePackageImports\":[\"lucide-react\",\"date-fns\",\"lodash-es\",\"ramda\",\"antd\",\"react-bootstrap\",\"ahooks\",\"@ant-design/icons\",\"@headlessui/react\",\"@headlessui-float/react\",\"@heroicons/react/20/solid\",\"@heroicons/react/24/solid\",\"@heroicons/react/24/outline\",\"@visx/visx\",\"@tremor/react\",\"rxjs\",\"@mui/material\",\"@mui/icons-material\",\"recharts\",\"react-use\",\"effect\",\"@effect/schema\",\"@effect/platform\",\"@effect/platform-node\",\"@effect/platform-browser\",\"@effect/platform-bun\",\"@effect/sql\",\"@effect/sql-mssql\",\"@effect/sql-mysql2\",\"@effect/sql-pg\",\"@effect/sql-squlite-node\",\"@effect/sql-squlite-bun\",\"@effect/sql-squlite-wasm\",\"@effect/sql-squlite-react-native\",\"@effect/rpc\",\"@effect/rpc-http\",\"@effect/typeclass\",\"@effect/experimental\",\"@effect/opentelemetry\",\"@material-ui/core\",\"@material-ui/icons\",\"@tabler/icons-react\",\"mui-core\",\"react-icons/ai\",\"react-icons/bi\",\"react-icons/bs\",\"react-icons/cg\",\"react-icons/ci\",\"react-icons/di\",\"react-icons/fa\",\"react-icons/fa6\",\"react-icons/fc\",\"react-icons/fi\",\"react-icons/gi\",\"react-icons/go\",\"react-icons/gr\",\"react-icons/hi\",\"react-icons/hi2\",\"react-icons/im\",\"react-icons/io\",\"react-icons/io5\",\"react-icons/lia\",\"react-icons/lib\",\"react-icons/lu\",\"react-icons/md\",\"react-icons/pi\",\"react-icons/ri\",\"react-icons/rx\",\"react-icons/si\",\"react-icons/sl\",\"react-icons/tb\",\"react-icons/tfi\",\"react-icons/ti\",\"react-icons/vsc\",\"react-icons/wi\"]},\"bundlePagesRouterDependencies\":false,\"configFile\":\"F:\\\\CODE\\\\Project\\\\tempmail\\\\moemail\\\\next.config.ts\",\"configFileName\":\"next.config.ts\"}\nconst maybeJSONParse = (str)=>str ? JSON.parse(str) : undefined;\nconst rscManifest = (_self___RSC_MANIFEST = self.__RSC_MANIFEST) == null ? void 0 : _self___RSC_MANIFEST[\"/api/roles/init-emperor/route\"];\nconst rscServerManifest = maybeJSONParse(self.__RSC_SERVER_MANIFEST);\nif (rscManifest && rscServerManifest) {\n    setReferenceManifestsSingleton({\n        page: \"/api/roles/init-emperor/route\",\n        clientReferenceManifest: rscManifest,\n        serverActionsManifest: rscServerManifest,\n        serverModuleMap: createServerModuleMap({\n            serverActionsManifest: rscServerManifest\n        })\n    });\n}\nexport const ComponentMod = module;\nexport default EdgeRouteModuleWrapper.wrap(module.routeModule, {\n    nextConfig\n});\n\n//# sourceMappingURL=edge-app-route.js.map"], "names": ["runtime", "GET", "session", "auth", "user", "id", "Response", "json", "error", "status", "db", "createDb", "<PERSON><PERSON><PERSON>", "query", "roles", "<PERSON><PERSON><PERSON><PERSON>", "where", "eq", "name", "ROLES", "EMPEROR", "with", "userRoles", "length", "currentUserRole", "userId", "role", "message", "roleId", "newRole", "insert", "values", "description", "returning", "assignRoleToUser", "console"], "sourceRoot": "", "ignoreList": []}