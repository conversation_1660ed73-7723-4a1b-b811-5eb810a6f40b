{"version": 3, "file": "app/api/emails/[id]/[messageId]/route.js", "mappings": "sFAAA,6DCAA,qHGAA,wTFKO,IAAMA,EAAU,OAAM,eAEPC,EAClBC,CAAgB,CAChB,QAAEC,CAAM,CAA0D,EAEpE,IAAMC,EAAS,MAAMC,CAAAA,EAAAA,EAAAA,EAAAA,CAASA,GAE9B,GAAI,CAACD,EACH,MADW,CACJE,EAAAA,EAAYA,CAACC,IAAI,CACtB,CAAEC,MAAO,KAAM,EACf,CAAEC,OAAQ,GAAI,GAIlB,GAAI,CACF,IAAMC,EAAKC,CAAAA,EAAAA,EAAAA,CAAAA,CAAQA,GACb,IAAEC,CAAE,WAAEC,CAAS,CAAE,CAAG,MAAMV,EAQhC,GAAI,CAPU,MAAMO,CAOR,CAPWI,KAAK,CAACC,MAAM,CAACC,SAAS,CAAC,CAC5CC,MAAOC,CAAAA,EAAAA,EAAAA,EAAAA,CAAGA,CACNC,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACJ,EAAAA,MAAMA,CAACH,EAAE,CAAEA,GACdO,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACJ,EAAAA,MAAMA,CAACX,MAAM,CAAEA,GAExB,GAGE,OAAOE,EAAAA,EAAYA,CAACC,IAAI,CACpB,CAAEC,MAAO,0CAA2C,EACpD,CAAEC,OAAQ,GAAI,GAWpB,GAAG,CAPa,MAAMC,EAAGI,CAOZ,IAPiB,CAACM,QAAQ,CAACJ,SAAS,CAAC,CAChDC,MAAOC,CAAAA,EAAAA,EAAAA,EAAAA,CAAGA,CACNC,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACC,EAAAA,QAAQA,CAACC,OAAO,CAAET,GACrBO,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACC,EAAAA,QAAQA,CAACR,EAAE,CAAEC,GAEtB,GAGE,OAAOP,EAAAA,EAAYA,CAACC,IAAI,CACpB,CAAEC,MAAO,sCAAuC,EAChD,CAAEC,OAAQ,GAAI,GAOpB,OAHA,MAAMC,EAAGY,MAAM,CAACF,EAAAA,QAAQA,EACnBH,KAAK,CAACE,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACC,EAAAA,QAAQA,CAACR,EAAE,CAAEC,IAEpBP,EAAAA,EAAYA,CAACC,IAAI,CAAC,CAAEgB,SAAS,CAAK,EAC3C,CAAE,MAAOf,EAAO,CAEd,OADAgB,QAAQhB,KAAK,CAAC,0BAA2BA,GAClCF,EAAAA,EAAYA,CAACC,IAAI,CACpB,CAAEC,MAAO,0BAA2B,EACpC,CAAEC,OAAQ,GAAI,EAEpB,CACF,CAEO,eAAegB,EAAIC,CAAiB,CAAE,QAAEvB,CAAM,CAA0D,EAC7G,GAAI,CACF,GAAM,IAAES,CAAE,CAAEC,WAAS,CAAE,CAAG,MAAMV,EAC1BO,EAAKC,CAAAA,EAAAA,EAAAA,CAAAA,CAAQA,GACbP,EAAS,MAAMC,CAAAA,EAAAA,EAAAA,EAAAA,CAASA,GAE9B,GAAI,CAACD,EACH,MADW,CACJE,EAAAA,EAAYA,CAACC,IAAI,CACtB,CAAEC,MAAO,KAAM,EACf,CAAEC,OAAQ,GAAI,GAKlB,IAAMkB,EAAQ,MAAMjB,EAAGI,KAAK,CAACC,MAAM,CAACC,SAAS,CAAC,CAC5CC,MAAOE,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACJ,EAAAA,MAAMA,CAACH,EAAE,CAAEA,EACvB,GAEA,GAAI,CAACe,EACH,KADU,EACHrB,EAAAA,EAAYA,CAACC,IAAI,CACtB,CAAEC,MAAO,OAAQ,EACjB,CAAEC,OAAQ,GAAI,GAKlB,IAAMmB,EAAUD,EAAMvB,MAAM,GAAKA,EAG7ByB,GAAU,EAWd,GAVKD,GAOHC,GAAU,CAAC,CANK,CADJ,KACUnB,EAAGI,KAAK,CAACgB,iBAAiB,CAACd,SAAS,CAAC,CACzDC,MAAOC,CAAAA,EAAAA,EAAAA,EAAAA,CAAGA,CACRC,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACW,EAAAA,iBAAiBA,CAAC1B,MAAM,CAAEA,GAC7Be,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACW,EAAAA,iBAAiBA,CAACT,OAAO,CAAET,GAElC,EACYmB,EAGV,CAACH,GAAW,CAACC,EACf,OADwB,EACjBvB,EAAYA,CAACC,IAAI,CACtB,CAAEC,MAAO,OAAQ,EACjB,CAAEC,OAAQ,GAAI,GAIlB,IAAMuB,EAAU,MAAMtB,EAAGI,KAAK,CAACM,QAAQ,CAACJ,SAAS,CAAC,CAChDC,MAAOC,CAAAA,EAAAA,EAAAA,EAAAA,CAAGA,CACRC,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACC,EAAAA,QAAQA,CAACR,EAAE,CAAEC,GAChBM,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACC,EAAAA,QAAQA,CAACC,OAAO,CAAET,GAEzB,GAEA,GAAI,CAACoB,EACH,OAAO1B,EAAAA,EAAYA,CAACC,IAAI,CACtB,CAAEC,MAAO,mBAAoB,EAC7B,CAAEC,OAAQ,GAAI,GAIlB,OAAOH,EAAAA,EAAYA,CAACC,IAAI,CAAC,CACvByB,QAAS,CACPpB,GAAIoB,EAAQpB,EAAE,CACdqB,aAAcD,EAAQE,WAAW,CACjCC,QAASH,EAAQG,OAAO,CACxBC,QAASJ,EAAQI,OAAO,CACxBC,KAAML,EAAQK,IAAI,CAClBC,YAAaN,EAAQO,UAAU,CAACC,OAAO,EACzC,CACF,EACF,CAAE,MAAOhC,EAAO,CAEd,OADAgB,QAAQhB,KAAK,CAAC,2BAA4BA,GACnCF,EAAAA,EAAYA,CAACC,IAAI,CACtB,CAAEC,MAAO,yBAA0B,EACnC,CAAEC,OAAQ,GAAI,EAElB,CACF,CCvIA,UAAwB,qBAAmB,EAC3C,YACA,KAAc,GAAS,WACvB,0CACA,wCACA,iBACA,kDACA,CAAK,CACL,uGACA,iBAVA,GAWA,QAAY,EACZ,CAAC,EAID,kBAAQ,wCAAsD,EAC9D,aACA,MAAW,QAAW,EACtB,mBACA,sBACA,CAAK,CACL,CCpBA,mFACA,GAFA,2BAEA,4BACA,MACI,QAA8B,EAClC,0CACA,0BACA,wBACA,gBAAyB,OAAqB,EAC9C,uBACA,CAAS,CACT,CAAK,EAEE,MAAqB,EAC5B,EAAe,GAAsB,MAAM,EAAkB,CAC7D,QAD6D,GAf7D,CAAoB,MAAQ,OAcM,CAdN,CAAW,sBAA2B,aAAe,kDAAyD,uOAAsQ,2RAAuT,kBAAkB,QAAQ,uDAA2D,0DAA8D,iBAAsB,gBAAkB,sEAAgF,kBAAoB,uCAA6C,MAAQ,iBAAmB,2BAA+B,gHAAkI,uBAAyB,8FAA0G,aAAiB,WAAa,sEAA6E,uBAAuB,iCAAmC,QAAQ,EAAE,SAAW,oBAAsB,QAAQ,GAAG,4EAAgF,WAAa,SAAW,2CAAiD,UAAY,+BAAqC,UAAY,oCAA0C,QAAU,uCAA6C,OAAS,yCAA+C,QAAU,0CAAiD,MAAQ,+CAAsD,iBAAmB,ywBAA+5B,4CAA8C,kNAAqQ,qBAAyB,s+CAA0/C,oIAiB7wJ,CAAC,CAAC,EAAC", "sources": ["webpack://_N_E/external commonjs \"node:async_hooks\"", "webpack://_N_E/external commonjs \"node:buffer\"", "webpack://_N_E/./app/api/emails/[id]/[messageId]/route.ts", "webpack://_N_E/./app/api/emails/[id]/[messageId]/route.ts?567a", "webpack://_N_E/?1ae9"], "sourcesContent": ["module.exports = require(\"node:async_hooks\");", "module.exports = require(\"node:buffer\");", "import { NextResponse } from \"next/server\"\r\nimport { createDb } from \"@/lib/db\"\r\nimport { messages, emails, userEmailBindings } from \"@/lib/schema\"\r\nimport { and, eq } from \"drizzle-orm\"\r\nimport { getUserId } from \"@/lib/apiKey\"\r\nexport const runtime = \"edge\"\r\n\r\nexport async function DELETE(\r\n    request: Request,\r\n    { params }: { params: Promise<{ id: string; messageId: string }> }\r\n) {\r\n  const userId = await getUserId()\r\n\r\n  if (!userId) {\r\n    return NextResponse.json(\r\n      { error: \"未授权\" },\r\n      { status: 401 }\r\n    )\r\n  }\r\n\r\n  try {\r\n    const db = createDb()\r\n    const { id, messageId } = await params\r\n    const email = await db.query.emails.findFirst({\r\n      where: and(\r\n          eq(emails.id, id),\r\n          eq(emails.userId, userId)\r\n      )\r\n    })\r\n\r\n    if (!email) {\r\n      return NextResponse.json(\r\n          { error: \"Email not found or no permission to view\" },\r\n          { status: 403 }\r\n      )\r\n    }\r\n\r\n    const message = await db.query.messages.findFirst({\r\n      where: and(\r\n          eq(messages.emailId, id),\r\n          eq(messages.id, messageId)\r\n      )\r\n    })\r\n\r\n    if(!message) {\r\n      return NextResponse.json(\r\n          { error: \"Message not found or already deleted\" },\r\n          { status: 404 }\r\n      )\r\n    }\r\n\r\n    await db.delete(messages)\r\n        .where(eq(messages.id, messageId))\r\n\r\n    return NextResponse.json({ success: true })\r\n  } catch (error) {\r\n    console.error('Failed to delete email:', error)\r\n    return NextResponse.json(\r\n        { error: \"Failed to delete message\" },\r\n        { status: 500 }\r\n    )\r\n  }\r\n}\r\n\r\nexport async function GET(_request: Request, { params }: { params: Promise<{ id: string; messageId: string }> }) {\r\n  try {\r\n    const { id, messageId } = await params\r\n    const db = createDb()\r\n    const userId = await getUserId()\r\n\r\n    if (!userId) {\r\n      return NextResponse.json(\r\n        { error: \"未授权\" },\r\n        { status: 401 }\r\n      )\r\n    }\r\n\r\n    // 检查邮箱权限：临时邮箱或绑定邮箱\r\n    const email = await db.query.emails.findFirst({\r\n      where: eq(emails.id, id)\r\n    })\r\n\r\n    if (!email) {\r\n      return NextResponse.json(\r\n        { error: \"邮箱不存在\" },\r\n        { status: 404 }\r\n      )\r\n    }\r\n\r\n    // 检查是否是用户的临时邮箱\r\n    const isOwner = email.userId === userId\r\n\r\n    // 检查是否是用户绑定的邮箱\r\n    let isBound = false\r\n    if (!isOwner) {\r\n      const binding = await db.query.userEmailBindings.findFirst({\r\n        where: and(\r\n          eq(userEmailBindings.userId, userId),\r\n          eq(userEmailBindings.emailId, id)\r\n        )\r\n      })\r\n      isBound = !!binding\r\n    }\r\n\r\n    if (!isOwner && !isBound) {\r\n      return NextResponse.json(\r\n        { error: \"无权限查看\" },\r\n        { status: 403 }\r\n      )\r\n    }\r\n\r\n    const message = await db.query.messages.findFirst({\r\n      where: and(\r\n        eq(messages.id, messageId),\r\n        eq(messages.emailId, id)\r\n      )\r\n    })\r\n    \r\n    if (!message) {\r\n      return NextResponse.json(\r\n        { error: \"Message not found\" },\r\n        { status: 404 }\r\n      )\r\n    }\r\n    \r\n    return NextResponse.json({ \r\n      message: {\r\n        id: message.id,\r\n        from_address: message.fromAddress,\r\n        subject: message.subject,\r\n        content: message.content,\r\n        html: message.html,\r\n        received_at: message.receivedAt.getTime()\r\n      }\r\n    })\r\n  } catch (error) {\r\n    console.error('Failed to fetch message:', error)\r\n    return NextResponse.json(\r\n      { error: \"Failed to fetch message\" },\r\n      { status: 500 }\r\n    )\r\n  }\r\n} ", "import { AppRouteRouteModule } from \"next/dist/server/route-modules/app-route/module.compiled\";\nimport { RouteKind } from \"next/dist/server/route-kind\";\nimport { patchFetch as _patchFetch } from \"next/dist/server/lib/patch-fetch\";\nimport * as userland from \"F:\\\\CODE\\\\Project\\\\tempmail\\\\moemail\\\\app\\\\api\\\\emails\\\\[id]\\\\[messageId]\\\\route.ts\";\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new AppRouteRouteModule({\n    definition: {\n        kind: RouteKind.APP_ROUTE,\n        page: \"/api/emails/[id]/[messageId]/route\",\n        pathname: \"/api/emails/[id]/[messageId]\",\n        filename: \"route\",\n        bundlePath: \"app/api/emails/[id]/[messageId]/route\"\n    },\n    resolvedPagePath: \"F:\\\\CODE\\\\Project\\\\tempmail\\\\moemail\\\\app\\\\api\\\\emails\\\\[id]\\\\[messageId]\\\\route.ts\",\n    nextConfigOutput,\n    userland\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return _patchFetch({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\nexport { routeModule, workAsyncStorage, workUnitAsyncStorage, serverHooks, patchFetch,  };\n\n//# sourceMappingURL=app-route.js.map", "var _self___RSC_MANIFEST;\nimport { createServerModuleMap } from \"next/dist/server/app-render/action-utils\";\nimport { setReferenceManifestsSingleton } from \"next/dist/server/app-render/encryption-utils\";\nimport { EdgeRouteModuleWrapper } from \"next/dist/server/web/edge-route-module-wrapper\";\n// Import the userland code.\nimport * as module from \"next-app-loader?name=app%2Fapi%2Femails%2F%5Bid%5D%2F%5BmessageId%5D%2Froute&page=%2Fapi%2Femails%2F%5Bid%5D%2F%5BmessageId%5D%2Froute&pagePath=private-next-app-dir%2Fapi%2Femails%2F%5Bid%5D%2F%5BmessageId%5D%2Froute.ts&appDir=F%3A%5CCODE%5CProject%5Ctempmail%5Cmoemail%5Capp&appPaths=%2Fapi%2Femails%2F%5Bid%5D%2F%5BmessageId%5D%2Froute&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!private-next-app-dir/api/emails/[id]/[messageId]/route.ts?__next_edge_ssr_entry__\";\nconst nextConfig = {\"env\":{},\"eslint\":{\"ignoreDuringBuilds\":false},\"typescript\":{\"ignoreBuildErrors\":false,\"tsconfigPath\":\"tsconfig.json\"},\"distDir\":\".next\",\"cleanDistDir\":true,\"assetPrefix\":\"\",\"cacheMaxMemorySize\":52428800,\"configOrigin\":\"next.config.ts\",\"useFileSystemPublicRoutes\":true,\"generateEtags\":true,\"pageExtensions\":[\"tsx\",\"ts\",\"jsx\",\"js\"],\"poweredByHeader\":true,\"compress\":true,\"images\":{\"deviceSizes\":[640,750,828,1080,1200,1920,2048,3840],\"imageSizes\":[16,32,48,64,96,128,256,384],\"path\":\"/_next/image\",\"loader\":\"default\",\"loaderFile\":\"\",\"domains\":[],\"disableStaticImages\":false,\"minimumCacheTTL\":60,\"formats\":[\"image/webp\"],\"dangerouslyAllowSVG\":false,\"contentSecurityPolicy\":\"script-src 'none'; frame-src 'none'; sandbox;\",\"contentDispositionType\":\"attachment\",\"remotePatterns\":[{\"protocol\":\"https\",\"hostname\":\"avatars.githubusercontent.com\"}],\"unoptimized\":false},\"devIndicators\":{\"appIsrStatus\":true,\"buildActivity\":true,\"buildActivityPosition\":\"bottom-right\"},\"onDemandEntries\":{\"maxInactiveAge\":60000,\"pagesBufferLength\":5},\"amp\":{\"canonicalBase\":\"\"},\"basePath\":\"\",\"sassOptions\":{},\"trailingSlash\":false,\"i18n\":null,\"productionBrowserSourceMaps\":false,\"excludeDefaultMomentLocales\":true,\"serverRuntimeConfig\":{},\"publicRuntimeConfig\":{},\"reactProductionProfiling\":false,\"reactStrictMode\":null,\"reactMaxHeadersLength\":6000,\"httpAgentOptions\":{\"keepAlive\":true},\"logging\":{},\"expireTime\":31536000,\"staticPageGenerationTimeout\":60,\"modularizeImports\":{\"@mui/icons-material\":{\"transform\":\"@mui/icons-material/{{member}}\"},\"lodash\":{\"transform\":\"lodash/{{member}}\"}},\"outputFileTracingRoot\":\"F:\\\\CODE\\\\Project\\\\tempmail\\\\moemail\",\"experimental\":{\"cacheLife\":{\"default\":{\"stale\":300,\"revalidate\":900,\"expire\":4294967294},\"seconds\":{\"stale\":0,\"revalidate\":1,\"expire\":60},\"minutes\":{\"stale\":300,\"revalidate\":60,\"expire\":3600},\"hours\":{\"stale\":300,\"revalidate\":3600,\"expire\":86400},\"days\":{\"stale\":300,\"revalidate\":86400,\"expire\":604800},\"weeks\":{\"stale\":300,\"revalidate\":604800,\"expire\":2592000},\"max\":{\"stale\":300,\"revalidate\":2592000,\"expire\":4294967294}},\"cacheHandlers\":{},\"cssChunking\":true,\"multiZoneDraftMode\":false,\"appNavFailHandling\":false,\"prerenderEarlyExit\":true,\"serverMinification\":true,\"serverSourceMaps\":false,\"linkNoTouchStart\":false,\"caseSensitiveRoutes\":false,\"clientSegmentCache\":false,\"preloadEntriesOnStart\":true,\"clientRouterFilter\":true,\"clientRouterFilterRedirects\":false,\"fetchCacheKeyPrefix\":\"\",\"middlewarePrefetch\":\"flexible\",\"optimisticClientCache\":true,\"manualClientBasePath\":false,\"cpus\":11,\"memoryBasedWorkersCount\":false,\"imgOptConcurrency\":null,\"imgOptTimeoutInSeconds\":7,\"imgOptMaxInputPixels\":268402689,\"imgOptSequentialRead\":null,\"isrFlushToDisk\":true,\"workerThreads\":false,\"optimizeCss\":false,\"nextScriptWorkers\":false,\"scrollRestoration\":false,\"externalDir\":false,\"disableOptimizedLoading\":false,\"gzipSize\":true,\"craCompat\":false,\"esmExternals\":true,\"fullySpecified\":false,\"swcTraceProfiling\":false,\"forceSwcTransforms\":false,\"largePageDataBytes\":128000,\"turbo\":{\"root\":\"F:\\\\CODE\\\\Project\\\\tempmail\\\\moemail\"},\"typedRoutes\":false,\"typedEnv\":false,\"parallelServerCompiles\":false,\"parallelServerBuildTraces\":false,\"ppr\":false,\"authInterrupts\":false,\"reactOwnerStack\":false,\"webpackMemoryOptimizations\":false,\"optimizeServerReact\":true,\"useEarlyImport\":false,\"staleTimes\":{\"dynamic\":0,\"static\":300},\"serverComponentsHmrCache\":true,\"staticGenerationMaxConcurrency\":8,\"staticGenerationMinPagesPerWorker\":25,\"dynamicIO\":false,\"inlineCss\":false,\"optimizePackageImports\":[\"lucide-react\",\"date-fns\",\"lodash-es\",\"ramda\",\"antd\",\"react-bootstrap\",\"ahooks\",\"@ant-design/icons\",\"@headlessui/react\",\"@headlessui-float/react\",\"@heroicons/react/20/solid\",\"@heroicons/react/24/solid\",\"@heroicons/react/24/outline\",\"@visx/visx\",\"@tremor/react\",\"rxjs\",\"@mui/material\",\"@mui/icons-material\",\"recharts\",\"react-use\",\"effect\",\"@effect/schema\",\"@effect/platform\",\"@effect/platform-node\",\"@effect/platform-browser\",\"@effect/platform-bun\",\"@effect/sql\",\"@effect/sql-mssql\",\"@effect/sql-mysql2\",\"@effect/sql-pg\",\"@effect/sql-squlite-node\",\"@effect/sql-squlite-bun\",\"@effect/sql-squlite-wasm\",\"@effect/sql-squlite-react-native\",\"@effect/rpc\",\"@effect/rpc-http\",\"@effect/typeclass\",\"@effect/experimental\",\"@effect/opentelemetry\",\"@material-ui/core\",\"@material-ui/icons\",\"@tabler/icons-react\",\"mui-core\",\"react-icons/ai\",\"react-icons/bi\",\"react-icons/bs\",\"react-icons/cg\",\"react-icons/ci\",\"react-icons/di\",\"react-icons/fa\",\"react-icons/fa6\",\"react-icons/fc\",\"react-icons/fi\",\"react-icons/gi\",\"react-icons/go\",\"react-icons/gr\",\"react-icons/hi\",\"react-icons/hi2\",\"react-icons/im\",\"react-icons/io\",\"react-icons/io5\",\"react-icons/lia\",\"react-icons/lib\",\"react-icons/lu\",\"react-icons/md\",\"react-icons/pi\",\"react-icons/ri\",\"react-icons/rx\",\"react-icons/si\",\"react-icons/sl\",\"react-icons/tb\",\"react-icons/tfi\",\"react-icons/ti\",\"react-icons/vsc\",\"react-icons/wi\"]},\"bundlePagesRouterDependencies\":false,\"configFile\":\"F:\\\\CODE\\\\Project\\\\tempmail\\\\moemail\\\\next.config.ts\",\"configFileName\":\"next.config.ts\"}\nconst maybeJSONParse = (str)=>str ? JSON.parse(str) : undefined;\nconst rscManifest = (_self___RSC_MANIFEST = self.__RSC_MANIFEST) == null ? void 0 : _self___RSC_MANIFEST[\"/api/emails/[id]/[messageId]/route\"];\nconst rscServerManifest = maybeJSONParse(self.__RSC_SERVER_MANIFEST);\nif (rscManifest && rscServerManifest) {\n    setReferenceManifestsSingleton({\n        page: \"/api/emails/[id]/[messageId]/route\",\n        clientReferenceManifest: rscManifest,\n        serverActionsManifest: rscServerManifest,\n        serverModuleMap: createServerModuleMap({\n            serverActionsManifest: rscServerManifest\n        })\n    });\n}\nexport const ComponentMod = module;\nexport default EdgeRouteModuleWrapper.wrap(module.routeModule, {\n    nextConfig\n});\n\n//# sourceMappingURL=edge-app-route.js.map"], "names": ["runtime", "DELETE", "request", "params", "userId", "getUserId", "NextResponse", "json", "error", "status", "db", "createDb", "id", "messageId", "query", "emails", "<PERSON><PERSON><PERSON><PERSON>", "where", "and", "eq", "messages", "emailId", "delete", "success", "console", "GET", "_request", "email", "isOwner", "isBound", "userEmailBindings", "binding", "message", "from_address", "fromAddress", "subject", "content", "html", "received_at", "receivedAt", "getTime"], "sourceRoot": "", "ignoreList": []}