{"version": 3, "file": "app/api/webhook/route.js", "mappings": "sFAAA,8DCAA,oHGAA,sTFMO,IAAMA,EAAU,OAAM,EAEPC,EAAAA,CAACA,CAACC,MAAM,CAAC,CAC7BC,IAAKF,EAAAA,CAACA,CAACG,MAAM,GAAGD,GAAG,GACnBE,QAASJ,EAAAA,CAACA,CAACK,OAAO,EACpB,GAEO,eAAeC,IACpB,IAAMC,EAAU,MAAMC,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,GAEpBC,EAAKC,CAAAA,EAAAA,EAAAA,CAAAA,CAAQA,GACbC,EAAU,MAAMF,EAAGG,KAAK,CAACC,QAAQ,CAACC,SAAS,CAAC,CAChDC,MAAOC,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACH,EAAAA,QAAQA,CAACI,MAAM,CAAEV,EAASW,IAAI,CAAEC,EAAE,CAC9C,GAEA,OAAOC,SAASC,IAAI,CAACV,GAAW,CAAEP,SAAS,EAAOF,IAAK,EAAG,EAC5D,CAEO,eAAeoB,EAAKC,CAAgB,EACzC,IAAMhB,EAAU,MAAMC,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,GAC1B,GAAI,CAACD,GAASW,MAAMC,GAClB,CADsB,MACfC,SAASC,IAAI,CAAC,CAAEG,MAAO,cAAe,EAAG,CAAEC,OAAQ,GAAI,GAGhE,GAAI,CACF,IAAMC,EAAO,MAAMH,EAAQF,IAAI,GACzB,KAAEnB,CAAG,SAAEE,CAAO,CAAE,CAAGuB,EAAcC,KAAK,CAACF,GAEvCjB,EAAKC,CAAAA,EAAAA,EAAAA,CAAAA,CAAQA,GACbmB,EAAM,IAAIC,KAyBhB,OAvBwB,MAAMrB,EAAGG,KAAK,CAACC,QAAQ,CAACC,SAAS,CAAC,CACxDC,MAAOC,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACH,EAAAA,QAAQA,CAACI,MAAM,CAAEV,EAAQW,IAAI,CAACC,EAAE,CAC5C,GAGE,MAAMV,EACHsB,MAAM,CAAClB,EAAAA,QAAQA,EACfmB,GAAG,CAAC,CACH9B,MACAE,UACA6B,UAAWJ,CACb,GACCd,KAAK,CAACC,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACH,EAAAA,QAAQA,CAACI,MAAM,CAAEV,EAAQW,IAAI,CAACC,EAAE,GAE5C,MAAMV,EACHyB,MAAM,CAACrB,EAAAA,QAAQA,EACfsB,MAAM,CAAC,CACNlB,OAAQV,EAAQW,IAAI,CAACC,EAAE,KACvBjB,UACAE,CACF,GAGGgB,SAASC,IAAI,CAAC,CAAEe,QAAS,EAAK,EACvC,CAAE,MAAOZ,EAAO,CAEd,OADAa,QAAQb,KAAK,CAAC,0BAA2BA,GAClCJ,SAASC,IAAI,CAClB,CAAEG,MAAO,iBAAkB,EAC3B,CAAEC,OAAQ,GAAI,EAElB,CACF,CC7DA,UAAwB,qBAAmB,EAC3C,YACA,KAAc,GAAS,WACvB,0BACA,wBACA,iBACA,kCACA,CAAK,CACL,qFACA,iBAVA,GAWA,QAAY,EACZ,CAAC,EAID,kBAAQ,wCAAsD,EAC9D,aACA,MAAW,QAAW,EACtB,mBACA,sBACA,CAAK,CACL,CCpBA,mEACA,EAFA,4BAEA,4BACA,MACI,QAA8B,EAClC,0BACA,0BACA,wBACA,gBAAyB,OAAqB,EAC9C,uBACA,CAAS,CACT,CAAK,EAEE,MAAqB,EAC5B,EAAe,GAAsB,MAAM,EAAkB,CAC7D,QAD6D,GAf7D,CAAoB,MAAQ,OAcM,CAdN,CAAW,sBAA2B,aAAe,kDAAyD,uOAAsQ,2RAAuT,kBAAkB,QAAQ,uDAA2D,0DAA8D,iBAAsB,gBAAkB,sEAAgF,kBAAoB,uCAA6C,MAAQ,iBAAmB,2BAA+B,gHAAkI,uBAAyB,8FAA0G,aAAiB,WAAa,sEAA6E,uBAAuB,iCAAmC,QAAQ,EAAE,SAAW,oBAAsB,QAAQ,GAAG,4EAAgF,WAAa,SAAW,2CAAiD,UAAY,+BAAqC,UAAY,oCAA0C,QAAU,uCAA6C,OAAS,yCAA+C,QAAU,0CAAiD,MAAQ,+CAAsD,iBAAmB,ywBAA+5B,4CAA8C,kNAAqQ,qBAAyB,s+CAA0/C,oIAiB7wJ,CAAC,CAAC,EAAC", "sources": ["webpack://_N_E/external commonjs \"node:async_hooks\"", "webpack://_N_E/external commonjs \"node:buffer\"", "webpack://_N_E/./app/api/webhook/route.ts", "webpack://_N_E/./app/api/webhook/route.ts?c7f3", "webpack://_N_E/?d401"], "sourcesContent": ["module.exports = require(\"node:async_hooks\");", "module.exports = require(\"node:buffer\");", "import { auth } from \"@/lib/auth\"\r\nimport { createDb } from \"@/lib/db\"\r\nimport { webhooks } from \"@/lib/schema\"\r\nimport { eq } from \"drizzle-orm\"\r\nimport { z } from \"zod\"\r\n\r\nexport const runtime = \"edge\"\r\n\r\nconst webhookSchema = z.object({\r\n  url: z.string().url(),\r\n  enabled: z.boolean()\r\n})\r\n\r\nexport async function GET() {\r\n  const session = await auth()\r\n\r\n  const db = createDb()\r\n  const webhook = await db.query.webhooks.findFirst({\r\n    where: eq(webhooks.userId, session!.user!.id!)\r\n  })\r\n\r\n  return Response.json(webhook || { enabled: false, url: \"\" })\r\n}\r\n\r\nexport async function POST(request: Request) {\r\n  const session = await auth()\r\n  if (!session?.user?.id) {\r\n    return Response.json({ error: \"Unauthorized\" }, { status: 401 })\r\n  }\r\n\r\n  try {\r\n    const body = await request.json()\r\n    const { url, enabled } = webhookSchema.parse(body)\r\n    \r\n    const db = createDb()\r\n    const now = new Date()\r\n\r\n    const existingWebhook = await db.query.webhooks.findFirst({\r\n      where: eq(webhooks.userId, session.user.id)\r\n    })\r\n\r\n    if (existingWebhook) {\r\n      await db\r\n        .update(webhooks)\r\n        .set({\r\n          url,\r\n          enabled,\r\n          updatedAt: now\r\n        })\r\n        .where(eq(webhooks.userId, session.user.id))\r\n    } else {\r\n      await db\r\n        .insert(webhooks)\r\n        .values({\r\n          userId: session.user.id,\r\n          url,\r\n          enabled,\r\n        })\r\n    }\r\n\r\n    return Response.json({ success: true })\r\n  } catch (error) {\r\n    console.error(\"Failed to save webhook:\", error)\r\n    return Response.json(\r\n      { error: \"Invalid request\" },\r\n      { status: 400 }\r\n    )\r\n  }\r\n} ", "import { AppRouteRouteModule } from \"next/dist/server/route-modules/app-route/module.compiled\";\nimport { RouteKind } from \"next/dist/server/route-kind\";\nimport { patchFetch as _patchFetch } from \"next/dist/server/lib/patch-fetch\";\nimport * as userland from \"F:\\\\CODE\\\\Project\\\\tempmail\\\\moemail\\\\app\\\\api\\\\webhook\\\\route.ts\";\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new AppRouteRouteModule({\n    definition: {\n        kind: RouteKind.APP_ROUTE,\n        page: \"/api/webhook/route\",\n        pathname: \"/api/webhook\",\n        filename: \"route\",\n        bundlePath: \"app/api/webhook/route\"\n    },\n    resolvedPagePath: \"F:\\\\CODE\\\\Project\\\\tempmail\\\\moemail\\\\app\\\\api\\\\webhook\\\\route.ts\",\n    nextConfigOutput,\n    userland\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return _patchFetch({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\nexport { routeModule, workAsyncStorage, workUnitAsyncStorage, serverHooks, patchFetch,  };\n\n//# sourceMappingURL=app-route.js.map", "var _self___RSC_MANIFEST;\nimport { createServerModuleMap } from \"next/dist/server/app-render/action-utils\";\nimport { setReferenceManifestsSingleton } from \"next/dist/server/app-render/encryption-utils\";\nimport { EdgeRouteModuleWrapper } from \"next/dist/server/web/edge-route-module-wrapper\";\n// Import the userland code.\nimport * as module from \"next-app-loader?name=app%2Fapi%2Fwebhook%2Froute&page=%2Fapi%2Fwebhook%2Froute&pagePath=private-next-app-dir%2Fapi%2Fwebhook%2Froute.ts&appDir=F%3A%5CCODE%5CProject%5Ctempmail%5Cmoemail%5Capp&appPaths=%2Fapi%2Fwebhook%2Froute&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!private-next-app-dir/api/webhook/route.ts?__next_edge_ssr_entry__\";\nconst nextConfig = {\"env\":{},\"eslint\":{\"ignoreDuringBuilds\":false},\"typescript\":{\"ignoreBuildErrors\":false,\"tsconfigPath\":\"tsconfig.json\"},\"distDir\":\".next\",\"cleanDistDir\":true,\"assetPrefix\":\"\",\"cacheMaxMemorySize\":52428800,\"configOrigin\":\"next.config.ts\",\"useFileSystemPublicRoutes\":true,\"generateEtags\":true,\"pageExtensions\":[\"tsx\",\"ts\",\"jsx\",\"js\"],\"poweredByHeader\":true,\"compress\":true,\"images\":{\"deviceSizes\":[640,750,828,1080,1200,1920,2048,3840],\"imageSizes\":[16,32,48,64,96,128,256,384],\"path\":\"/_next/image\",\"loader\":\"default\",\"loaderFile\":\"\",\"domains\":[],\"disableStaticImages\":false,\"minimumCacheTTL\":60,\"formats\":[\"image/webp\"],\"dangerouslyAllowSVG\":false,\"contentSecurityPolicy\":\"script-src 'none'; frame-src 'none'; sandbox;\",\"contentDispositionType\":\"attachment\",\"remotePatterns\":[{\"protocol\":\"https\",\"hostname\":\"avatars.githubusercontent.com\"}],\"unoptimized\":false},\"devIndicators\":{\"appIsrStatus\":true,\"buildActivity\":true,\"buildActivityPosition\":\"bottom-right\"},\"onDemandEntries\":{\"maxInactiveAge\":60000,\"pagesBufferLength\":5},\"amp\":{\"canonicalBase\":\"\"},\"basePath\":\"\",\"sassOptions\":{},\"trailingSlash\":false,\"i18n\":null,\"productionBrowserSourceMaps\":false,\"excludeDefaultMomentLocales\":true,\"serverRuntimeConfig\":{},\"publicRuntimeConfig\":{},\"reactProductionProfiling\":false,\"reactStrictMode\":null,\"reactMaxHeadersLength\":6000,\"httpAgentOptions\":{\"keepAlive\":true},\"logging\":{},\"expireTime\":31536000,\"staticPageGenerationTimeout\":60,\"modularizeImports\":{\"@mui/icons-material\":{\"transform\":\"@mui/icons-material/{{member}}\"},\"lodash\":{\"transform\":\"lodash/{{member}}\"}},\"outputFileTracingRoot\":\"F:\\\\CODE\\\\Project\\\\tempmail\\\\moemail\",\"experimental\":{\"cacheLife\":{\"default\":{\"stale\":300,\"revalidate\":900,\"expire\":4294967294},\"seconds\":{\"stale\":0,\"revalidate\":1,\"expire\":60},\"minutes\":{\"stale\":300,\"revalidate\":60,\"expire\":3600},\"hours\":{\"stale\":300,\"revalidate\":3600,\"expire\":86400},\"days\":{\"stale\":300,\"revalidate\":86400,\"expire\":604800},\"weeks\":{\"stale\":300,\"revalidate\":604800,\"expire\":2592000},\"max\":{\"stale\":300,\"revalidate\":2592000,\"expire\":4294967294}},\"cacheHandlers\":{},\"cssChunking\":true,\"multiZoneDraftMode\":false,\"appNavFailHandling\":false,\"prerenderEarlyExit\":true,\"serverMinification\":true,\"serverSourceMaps\":false,\"linkNoTouchStart\":false,\"caseSensitiveRoutes\":false,\"clientSegmentCache\":false,\"preloadEntriesOnStart\":true,\"clientRouterFilter\":true,\"clientRouterFilterRedirects\":false,\"fetchCacheKeyPrefix\":\"\",\"middlewarePrefetch\":\"flexible\",\"optimisticClientCache\":true,\"manualClientBasePath\":false,\"cpus\":11,\"memoryBasedWorkersCount\":false,\"imgOptConcurrency\":null,\"imgOptTimeoutInSeconds\":7,\"imgOptMaxInputPixels\":268402689,\"imgOptSequentialRead\":null,\"isrFlushToDisk\":true,\"workerThreads\":false,\"optimizeCss\":false,\"nextScriptWorkers\":false,\"scrollRestoration\":false,\"externalDir\":false,\"disableOptimizedLoading\":false,\"gzipSize\":true,\"craCompat\":false,\"esmExternals\":true,\"fullySpecified\":false,\"swcTraceProfiling\":false,\"forceSwcTransforms\":false,\"largePageDataBytes\":128000,\"turbo\":{\"root\":\"F:\\\\CODE\\\\Project\\\\tempmail\\\\moemail\"},\"typedRoutes\":false,\"typedEnv\":false,\"parallelServerCompiles\":false,\"parallelServerBuildTraces\":false,\"ppr\":false,\"authInterrupts\":false,\"reactOwnerStack\":false,\"webpackMemoryOptimizations\":false,\"optimizeServerReact\":true,\"useEarlyImport\":false,\"staleTimes\":{\"dynamic\":0,\"static\":300},\"serverComponentsHmrCache\":true,\"staticGenerationMaxConcurrency\":8,\"staticGenerationMinPagesPerWorker\":25,\"dynamicIO\":false,\"inlineCss\":false,\"optimizePackageImports\":[\"lucide-react\",\"date-fns\",\"lodash-es\",\"ramda\",\"antd\",\"react-bootstrap\",\"ahooks\",\"@ant-design/icons\",\"@headlessui/react\",\"@headlessui-float/react\",\"@heroicons/react/20/solid\",\"@heroicons/react/24/solid\",\"@heroicons/react/24/outline\",\"@visx/visx\",\"@tremor/react\",\"rxjs\",\"@mui/material\",\"@mui/icons-material\",\"recharts\",\"react-use\",\"effect\",\"@effect/schema\",\"@effect/platform\",\"@effect/platform-node\",\"@effect/platform-browser\",\"@effect/platform-bun\",\"@effect/sql\",\"@effect/sql-mssql\",\"@effect/sql-mysql2\",\"@effect/sql-pg\",\"@effect/sql-squlite-node\",\"@effect/sql-squlite-bun\",\"@effect/sql-squlite-wasm\",\"@effect/sql-squlite-react-native\",\"@effect/rpc\",\"@effect/rpc-http\",\"@effect/typeclass\",\"@effect/experimental\",\"@effect/opentelemetry\",\"@material-ui/core\",\"@material-ui/icons\",\"@tabler/icons-react\",\"mui-core\",\"react-icons/ai\",\"react-icons/bi\",\"react-icons/bs\",\"react-icons/cg\",\"react-icons/ci\",\"react-icons/di\",\"react-icons/fa\",\"react-icons/fa6\",\"react-icons/fc\",\"react-icons/fi\",\"react-icons/gi\",\"react-icons/go\",\"react-icons/gr\",\"react-icons/hi\",\"react-icons/hi2\",\"react-icons/im\",\"react-icons/io\",\"react-icons/io5\",\"react-icons/lia\",\"react-icons/lib\",\"react-icons/lu\",\"react-icons/md\",\"react-icons/pi\",\"react-icons/ri\",\"react-icons/rx\",\"react-icons/si\",\"react-icons/sl\",\"react-icons/tb\",\"react-icons/tfi\",\"react-icons/ti\",\"react-icons/vsc\",\"react-icons/wi\"]},\"bundlePagesRouterDependencies\":false,\"configFile\":\"F:\\\\CODE\\\\Project\\\\tempmail\\\\moemail\\\\next.config.ts\",\"configFileName\":\"next.config.ts\"}\nconst maybeJSONParse = (str)=>str ? JSON.parse(str) : undefined;\nconst rscManifest = (_self___RSC_MANIFEST = self.__RSC_MANIFEST) == null ? void 0 : _self___RSC_MANIFEST[\"/api/webhook/route\"];\nconst rscServerManifest = maybeJSONParse(self.__RSC_SERVER_MANIFEST);\nif (rscManifest && rscServerManifest) {\n    setReferenceManifestsSingleton({\n        page: \"/api/webhook/route\",\n        clientReferenceManifest: rscManifest,\n        serverActionsManifest: rscServerManifest,\n        serverModuleMap: createServerModuleMap({\n            serverActionsManifest: rscServerManifest\n        })\n    });\n}\nexport const ComponentMod = module;\nexport default EdgeRouteModuleWrapper.wrap(module.routeModule, {\n    nextConfig\n});\n\n//# sourceMappingURL=edge-app-route.js.map"], "names": ["runtime", "z", "object", "url", "string", "enabled", "boolean", "GET", "session", "auth", "db", "createDb", "webhook", "query", "webhooks", "<PERSON><PERSON><PERSON><PERSON>", "where", "eq", "userId", "user", "id", "Response", "json", "POST", "request", "error", "status", "body", "webhookSchema", "parse", "now", "Date", "update", "set", "updatedAt", "insert", "values", "success", "console"], "sourceRoot": "", "ignoreList": []}