{"version": 3, "file": "app/api/webhook/test/route.js", "mappings": "qFAAA,8DCAA,oHIAA,gQHkBO,eAAeA,EAAYC,CAAW,CAAEC,CAAuB,EACpE,IAAIC,EAA0B,KAE9B,IAAK,IAAIC,EAAI,EAAGA,EAAIC,EAAAA,CAAcA,CAACC,WAAW,CAAEF,IAAK,GAC/C,CACF,IAAMG,EAAa,IAAIC,gBACjBC,EAAYC,WAAW,IAAMH,EAAWI,KAAK,GAAIN,EAAAA,CAAcA,CAACO,OAAO,EAEvEC,EAAW,MAAMC,MAAMb,EAAK,CAChCc,OAAQ,OACRC,QAAS,CACP,eAAgB,mBAChB,kBAAmBd,EAAQe,KAAK,EAElCC,KAAMC,KAAKC,SAAS,CAAClB,EAAQmB,IAAI,EACjCC,OAAQf,EAAWe,MAAM,GAK3B,GAFAC,aAAad,GAETI,EAASW,EAAE,CACb,CADe,MACR,EAGTrB,EAAY,MAAU,CAAC,oBAAoB,EAAEU,EAASY,MAAM,EAAE,CAChE,CAAE,MAAOC,EAAO,CACdvB,EAAYuB,EAERtB,EAAIC,EAAAA,CAAcA,CAACC,WAAW,CAAG,GACnC,MAAM,IAAIqB,QAAQC,GAAWlB,WAAWkB,EAASvB,EAAAA,CAAcA,CAACwB,WAAW,EAE/E,CAGF,MAAM1B,CACR,eChDO,IAAM2B,EAAU,OAAM,EAEVC,EAAAA,CAACA,CAACC,MAAM,CAAC,CAC1B/B,IAAK8B,EAAAA,CAACA,CAACE,MAAM,GAAGhC,GAAG,EACrB,GAEO,eAAeiC,EAAKC,CAAgB,EACzC,GAAI,CACF,IAAMjB,EAAO,MAAMiB,EAAQC,IAAI,GACzB,KAAEnC,CAAG,CAAE,CAAGoC,EAAWC,KAAK,CAACpB,GAgBjC,OAdA,MAAMlB,EAAYC,EAAK,CACrBgB,MADejB,EACRK,CAAcA,CAACkC,MAAM,CAACC,WAAW,CACxCnB,KAAM,CACJoB,QAAS,YACTC,UAAW,YACXC,YAAa,qBACbC,QAAS,aACTC,QAAS,wBACTC,KAAM,gDACNC,WAAY,uBACZC,UAAW,uBACb,CACF,GAEOC,SAASb,IAAI,CAAC,CAAEc,SAAS,CAAK,EACvC,CAAE,MAAOxB,EAAO,CAEd,OADAyB,QAAQzB,KAAK,CAAC,0BAA2BA,GAClCuB,SAASb,IAAI,CAClB,CAAEV,MAAO,wBAAyB,EAClC,CAAED,OAAQ,GAAI,EAElB,CACF,CC/BA,UAAwB,qBAAmB,EAC3C,YACA,KAAc,GAAS,WACvB,+BACA,6BACA,iBACA,uCACA,CAAK,CACL,2FACA,iBAVA,GAWA,QAAY,EACZ,CAAC,EAID,kBAAQ,wCAAsD,EAC9D,aACA,MAAW,QAAW,EACtB,mBACA,sBACA,CAAK,CACL,CCpBA,wEACA,EAFA,4BAEA,4BACA,MACI,QAA8B,EAClC,+BACA,0BACA,wBACA,gBAAyB,OAAqB,EAC9C,uBACA,CAAS,CACT,CAAK,EAEE,MAAqB,EAC5B,EAAe,GAAsB,MAAM,EAAkB,CAC7D,QAD6D,GAf7D,CAAoB,MAAQ,OAcM,CAdN,CAAW,sBAA2B,aAAe,kDAAyD,uOAAsQ,2RAAuT,kBAAkB,QAAQ,uDAA2D,0DAA8D,iBAAsB,gBAAkB,sEAAgF,kBAAoB,uCAA6C,MAAQ,iBAAmB,2BAA+B,gHAAkI,uBAAyB,8FAA0G,aAAiB,WAAa,sEAA6E,uBAAuB,iCAAmC,QAAQ,EAAE,SAAW,oBAAsB,QAAQ,GAAG,4EAAgF,WAAa,SAAW,2CAAiD,UAAY,+BAAqC,UAAY,oCAA0C,QAAU,uCAA6C,OAAS,yCAA+C,QAAU,0CAAiD,MAAQ,+CAAsD,iBAAmB,ywBAA+5B,4CAA8C,kNAAqQ,qBAAyB,s+CAA0/C,oIAiB7wJ,CAAC,CAAC,EAAC,+ECvBI,IAAM2B,EAAe,CAC1BC,kBAAmB,GACnBC,cAAe,GACjB,EAAU,ECHoB,CAC5BhD,YAAa,EACbM,QAAS,IACTiB,YAAa,IACbU,OAAQ,CACNC,YAAa,aACf,CACF,EAAU", "sources": ["webpack://_N_E/external commonjs \"node:async_hooks\"", "webpack://_N_E/external commonjs \"node:buffer\"", "webpack://_N_E/./app/lib/webhook.ts", "webpack://_N_E/./app/api/webhook/test/route.ts", "webpack://_N_E/./app/api/webhook/test/route.ts?b255", "webpack://_N_E/?eaef", "webpack://_N_E/./app/config/email.ts", "webpack://_N_E/./app/config/webhook.ts", "webpack://_N_E/./app/config/index.ts"], "sourcesContent": ["module.exports = require(\"node:async_hooks\");", "module.exports = require(\"node:buffer\");", "import { WEBH<PERSON><PERSON>_CONFIG } from \"@/config\"\r\n\r\nexport interface EmailMessage {\r\n  emailId: string\r\n  messageId: string\r\n  fromAddress: string\r\n  subject: string\r\n  content: string\r\n  html: string\r\n  receivedAt: string\r\n  toAddress: string\r\n}\r\n\r\nexport interface WebhookPayload {\r\n  event: typeof WEBHOOK_CONFIG.EVENTS[keyof typeof WEBHOOK_CONFIG.EVENTS]\r\n  data: EmailMessage\r\n}\r\n\r\nexport async function callWebhook(url: string, payload: WebhookPayload) {\r\n  let lastError: Error | null = null\r\n  \r\n  for (let i = 0; i < WEBHOOK_CONFIG.MAX_RETRIES; i++) {\r\n    try {\r\n      const controller = new AbortController()\r\n      const timeoutId = setTimeout(() => controller.abort(), WEBHOOK_CONFIG.TIMEOUT)\r\n\r\n      const response = await fetch(url, {\r\n        method: \"POST\",\r\n        headers: {\r\n          \"Content-Type\": \"application/json\",\r\n          \"X-Webhook-Event\": payload.event,\r\n        },\r\n        body: JSON.stringify(payload.data),\r\n        signal: controller.signal,\r\n      })\r\n\r\n      clearTimeout(timeoutId)\r\n\r\n      if (response.ok) {\r\n        return true\r\n      }\r\n\r\n      lastError = new Error(`HTTP error! status: ${response.status}`)\r\n    } catch (error) {\r\n      lastError = error as Error\r\n      \r\n      if (i < WEBHOOK_CONFIG.MAX_RETRIES - 1) {\r\n        await new Promise(resolve => setTimeout(resolve, WEBHOOK_CONFIG.RETRY_DELAY))\r\n      }\r\n    }\r\n  }\r\n\r\n  throw lastError\r\n} ", "import { callWebhook } from \"@/lib/webhook\"\r\nimport { WEBHOOK_CONFIG } from \"@/config\"\r\nimport { z } from \"zod\"\r\nimport { EmailMessage } from \"@/lib/webhook\"\r\n\r\nexport const runtime = \"edge\"\r\n\r\nconst testSchema = z.object({\r\n  url: z.string().url()\r\n})\r\n\r\nexport async function POST(request: Request) {\r\n  try {\r\n    const body = await request.json()\r\n    const { url } = testSchema.parse(body)\r\n\r\n    await callWebhook(url, {\r\n      event: WEBHOOK_CONFIG.EVENTS.NEW_MESSAGE,\r\n      data: {\r\n        emailId: \"123456789\",\r\n        messageId: '987654321',\r\n        fromAddress: \"<EMAIL>\",\r\n        subject: \"Test Email\",\r\n        content: \"This is a test email.\",\r\n        html: \"<p>This is a <strong>test</strong> email.</p>\",\r\n        receivedAt: \"2023-03-01T12:00:00Z\",\r\n        toAddress: \"<EMAIL>\"\r\n      } as EmailMessage\r\n    })\r\n\r\n    return Response.json({ success: true })\r\n  } catch (error) {\r\n    console.error(\"Failed to test webhook:\", error)\r\n    return Response.json(\r\n      { error: \"Failed to test webhook\" },\r\n      { status: 400 }\r\n    )\r\n  }\r\n} ", "import { AppRouteRouteModule } from \"next/dist/server/route-modules/app-route/module.compiled\";\nimport { RouteKind } from \"next/dist/server/route-kind\";\nimport { patchFetch as _patchFetch } from \"next/dist/server/lib/patch-fetch\";\nimport * as userland from \"F:\\\\CODE\\\\Project\\\\tempmail\\\\moemail\\\\app\\\\api\\\\webhook\\\\test\\\\route.ts\";\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new AppRouteRouteModule({\n    definition: {\n        kind: RouteKind.APP_ROUTE,\n        page: \"/api/webhook/test/route\",\n        pathname: \"/api/webhook/test\",\n        filename: \"route\",\n        bundlePath: \"app/api/webhook/test/route\"\n    },\n    resolvedPagePath: \"F:\\\\CODE\\\\Project\\\\tempmail\\\\moemail\\\\app\\\\api\\\\webhook\\\\test\\\\route.ts\",\n    nextConfigOutput,\n    userland\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return _patchFetch({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\nexport { routeModule, workAsyncStorage, workUnitAsyncStorage, serverHooks, patchFetch,  };\n\n//# sourceMappingURL=app-route.js.map", "var _self___RSC_MANIFEST;\nimport { createServerModuleMap } from \"next/dist/server/app-render/action-utils\";\nimport { setReferenceManifestsSingleton } from \"next/dist/server/app-render/encryption-utils\";\nimport { EdgeRouteModuleWrapper } from \"next/dist/server/web/edge-route-module-wrapper\";\n// Import the userland code.\nimport * as module from \"next-app-loader?name=app%2Fapi%2Fwebhook%2Ftest%2Froute&page=%2Fapi%2Fwebhook%2Ftest%2Froute&pagePath=private-next-app-dir%2Fapi%2Fwebhook%2Ftest%2Froute.ts&appDir=F%3A%5CCODE%5CProject%5Ctempmail%5Cmoemail%5Capp&appPaths=%2Fapi%2Fwebhook%2Ftest%2Froute&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!private-next-app-dir/api/webhook/test/route.ts?__next_edge_ssr_entry__\";\nconst nextConfig = {\"env\":{},\"eslint\":{\"ignoreDuringBuilds\":false},\"typescript\":{\"ignoreBuildErrors\":false,\"tsconfigPath\":\"tsconfig.json\"},\"distDir\":\".next\",\"cleanDistDir\":true,\"assetPrefix\":\"\",\"cacheMaxMemorySize\":52428800,\"configOrigin\":\"next.config.ts\",\"useFileSystemPublicRoutes\":true,\"generateEtags\":true,\"pageExtensions\":[\"tsx\",\"ts\",\"jsx\",\"js\"],\"poweredByHeader\":true,\"compress\":true,\"images\":{\"deviceSizes\":[640,750,828,1080,1200,1920,2048,3840],\"imageSizes\":[16,32,48,64,96,128,256,384],\"path\":\"/_next/image\",\"loader\":\"default\",\"loaderFile\":\"\",\"domains\":[],\"disableStaticImages\":false,\"minimumCacheTTL\":60,\"formats\":[\"image/webp\"],\"dangerouslyAllowSVG\":false,\"contentSecurityPolicy\":\"script-src 'none'; frame-src 'none'; sandbox;\",\"contentDispositionType\":\"attachment\",\"remotePatterns\":[{\"protocol\":\"https\",\"hostname\":\"avatars.githubusercontent.com\"}],\"unoptimized\":false},\"devIndicators\":{\"appIsrStatus\":true,\"buildActivity\":true,\"buildActivityPosition\":\"bottom-right\"},\"onDemandEntries\":{\"maxInactiveAge\":60000,\"pagesBufferLength\":5},\"amp\":{\"canonicalBase\":\"\"},\"basePath\":\"\",\"sassOptions\":{},\"trailingSlash\":false,\"i18n\":null,\"productionBrowserSourceMaps\":false,\"excludeDefaultMomentLocales\":true,\"serverRuntimeConfig\":{},\"publicRuntimeConfig\":{},\"reactProductionProfiling\":false,\"reactStrictMode\":null,\"reactMaxHeadersLength\":6000,\"httpAgentOptions\":{\"keepAlive\":true},\"logging\":{},\"expireTime\":31536000,\"staticPageGenerationTimeout\":60,\"modularizeImports\":{\"@mui/icons-material\":{\"transform\":\"@mui/icons-material/{{member}}\"},\"lodash\":{\"transform\":\"lodash/{{member}}\"}},\"outputFileTracingRoot\":\"F:\\\\CODE\\\\Project\\\\tempmail\\\\moemail\",\"experimental\":{\"cacheLife\":{\"default\":{\"stale\":300,\"revalidate\":900,\"expire\":4294967294},\"seconds\":{\"stale\":0,\"revalidate\":1,\"expire\":60},\"minutes\":{\"stale\":300,\"revalidate\":60,\"expire\":3600},\"hours\":{\"stale\":300,\"revalidate\":3600,\"expire\":86400},\"days\":{\"stale\":300,\"revalidate\":86400,\"expire\":604800},\"weeks\":{\"stale\":300,\"revalidate\":604800,\"expire\":2592000},\"max\":{\"stale\":300,\"revalidate\":2592000,\"expire\":4294967294}},\"cacheHandlers\":{},\"cssChunking\":true,\"multiZoneDraftMode\":false,\"appNavFailHandling\":false,\"prerenderEarlyExit\":true,\"serverMinification\":true,\"serverSourceMaps\":false,\"linkNoTouchStart\":false,\"caseSensitiveRoutes\":false,\"clientSegmentCache\":false,\"preloadEntriesOnStart\":true,\"clientRouterFilter\":true,\"clientRouterFilterRedirects\":false,\"fetchCacheKeyPrefix\":\"\",\"middlewarePrefetch\":\"flexible\",\"optimisticClientCache\":true,\"manualClientBasePath\":false,\"cpus\":11,\"memoryBasedWorkersCount\":false,\"imgOptConcurrency\":null,\"imgOptTimeoutInSeconds\":7,\"imgOptMaxInputPixels\":268402689,\"imgOptSequentialRead\":null,\"isrFlushToDisk\":true,\"workerThreads\":false,\"optimizeCss\":false,\"nextScriptWorkers\":false,\"scrollRestoration\":false,\"externalDir\":false,\"disableOptimizedLoading\":false,\"gzipSize\":true,\"craCompat\":false,\"esmExternals\":true,\"fullySpecified\":false,\"swcTraceProfiling\":false,\"forceSwcTransforms\":false,\"largePageDataBytes\":128000,\"turbo\":{\"root\":\"F:\\\\CODE\\\\Project\\\\tempmail\\\\moemail\"},\"typedRoutes\":false,\"typedEnv\":false,\"parallelServerCompiles\":false,\"parallelServerBuildTraces\":false,\"ppr\":false,\"authInterrupts\":false,\"reactOwnerStack\":false,\"webpackMemoryOptimizations\":false,\"optimizeServerReact\":true,\"useEarlyImport\":false,\"staleTimes\":{\"dynamic\":0,\"static\":300},\"serverComponentsHmrCache\":true,\"staticGenerationMaxConcurrency\":8,\"staticGenerationMinPagesPerWorker\":25,\"dynamicIO\":false,\"inlineCss\":false,\"optimizePackageImports\":[\"lucide-react\",\"date-fns\",\"lodash-es\",\"ramda\",\"antd\",\"react-bootstrap\",\"ahooks\",\"@ant-design/icons\",\"@headlessui/react\",\"@headlessui-float/react\",\"@heroicons/react/20/solid\",\"@heroicons/react/24/solid\",\"@heroicons/react/24/outline\",\"@visx/visx\",\"@tremor/react\",\"rxjs\",\"@mui/material\",\"@mui/icons-material\",\"recharts\",\"react-use\",\"effect\",\"@effect/schema\",\"@effect/platform\",\"@effect/platform-node\",\"@effect/platform-browser\",\"@effect/platform-bun\",\"@effect/sql\",\"@effect/sql-mssql\",\"@effect/sql-mysql2\",\"@effect/sql-pg\",\"@effect/sql-squlite-node\",\"@effect/sql-squlite-bun\",\"@effect/sql-squlite-wasm\",\"@effect/sql-squlite-react-native\",\"@effect/rpc\",\"@effect/rpc-http\",\"@effect/typeclass\",\"@effect/experimental\",\"@effect/opentelemetry\",\"@material-ui/core\",\"@material-ui/icons\",\"@tabler/icons-react\",\"mui-core\",\"react-icons/ai\",\"react-icons/bi\",\"react-icons/bs\",\"react-icons/cg\",\"react-icons/ci\",\"react-icons/di\",\"react-icons/fa\",\"react-icons/fa6\",\"react-icons/fc\",\"react-icons/fi\",\"react-icons/gi\",\"react-icons/go\",\"react-icons/gr\",\"react-icons/hi\",\"react-icons/hi2\",\"react-icons/im\",\"react-icons/io\",\"react-icons/io5\",\"react-icons/lia\",\"react-icons/lib\",\"react-icons/lu\",\"react-icons/md\",\"react-icons/pi\",\"react-icons/ri\",\"react-icons/rx\",\"react-icons/si\",\"react-icons/sl\",\"react-icons/tb\",\"react-icons/tfi\",\"react-icons/ti\",\"react-icons/vsc\",\"react-icons/wi\"]},\"bundlePagesRouterDependencies\":false,\"configFile\":\"F:\\\\CODE\\\\Project\\\\tempmail\\\\moemail\\\\next.config.ts\",\"configFileName\":\"next.config.ts\"}\nconst maybeJSONParse = (str)=>str ? JSON.parse(str) : undefined;\nconst rscManifest = (_self___RSC_MANIFEST = self.__RSC_MANIFEST) == null ? void 0 : _self___RSC_MANIFEST[\"/api/webhook/test/route\"];\nconst rscServerManifest = maybeJSONParse(self.__RSC_SERVER_MANIFEST);\nif (rscManifest && rscServerManifest) {\n    setReferenceManifestsSingleton({\n        page: \"/api/webhook/test/route\",\n        clientReferenceManifest: rscManifest,\n        serverActionsManifest: rscServerManifest,\n        serverModuleMap: createServerModuleMap({\n            serverActionsManifest: rscServerManifest\n        })\n    });\n}\nexport const ComponentMod = module;\nexport default EdgeRouteModuleWrapper.wrap(module.routeModule, {\n    nextConfig\n});\n\n//# sourceMappingURL=edge-app-route.js.map", "export const EMAIL_CONFIG = {\r\n  MAX_ACTIVE_EMAILS: 30, // Maximum number of active emails\r\n  POLL_INTERVAL: 10_000, // Polling interval in milliseconds\r\n} as const\r\n\r\nexport type EmailConfig = typeof EMAIL_CONFIG ", "export const WEBHOOK_CONFIG = {\r\n  MAX_RETRIES: 3, // Maximum retry count\r\n  TIMEOUT: 10_000, // Timeout time (milliseconds)\r\n  RETRY_DELAY: 1000, // Retry delay (milliseconds)\r\n  EVENTS: {\r\n    NEW_MESSAGE: 'new_message',\r\n  }\r\n} as const\r\n\r\nexport type WebhookConfig = typeof WEBHOOK_CONFIG ", "export * from './email'\r\nexport * from './webhook'"], "names": ["callWebhook", "url", "payload", "lastError", "i", "WEBHOOK_CONFIG", "MAX_RETRIES", "controller", "AbortController", "timeoutId", "setTimeout", "abort", "TIMEOUT", "response", "fetch", "method", "headers", "event", "body", "JSON", "stringify", "data", "signal", "clearTimeout", "ok", "status", "error", "Promise", "resolve", "RETRY_DELAY", "runtime", "z", "object", "string", "POST", "request", "json", "testSchema", "parse", "EVENTS", "NEW_MESSAGE", "emailId", "messageId", "fromAddress", "subject", "content", "html", "receivedAt", "to<PERSON><PERSON><PERSON>", "Response", "success", "console", "EMAIL_CONFIG", "MAX_ACTIVE_EMAILS", "POLL_INTERVAL"], "sourceRoot": "", "ignoreList": []}