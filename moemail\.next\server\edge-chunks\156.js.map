{"version": 3, "file": "edge-chunks/156.js", "mappings": "kKA+CO,IAAMA,EAAY,UAEvB,IAAMC,EAASC,CADK,MAAMC,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,EAAAA,EACNC,GAAG,CAAC,aAE/B,GAAIH,EAAQ,OAAOA,EAEnB,IAAMI,EAAU,MAAMC,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,GAE1B,OAAOD,GAASE,KAAKC,EACvB,EAAC,uOCxDD,IAAMC,EAAS,CACb,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACD,eCGD,IAAMC,EAA0C,CAC9C,CAACC,EAAAA,EAAKA,CAACC,OAAO,CAAC,CAAE,YACjB,CAACD,EAAAA,EAAKA,CAACE,IAAI,CAAC,CAAE,WACd,CAACF,EAAAA,EAAKA,CAACG,MAAM,CAAC,CAAE,WAChB,CAACH,EAAAA,EAAKA,CAACI,QAAQ,CAAC,CAAE,UACpB,EAEMC,EAAiB,SACD,MAAMC,CAAAA,EAAAA,EAAAA,iBAAAA,CAAiBA,GAAGC,GAAG,CAACC,WAAW,CAACf,GAAG,CAAC,kBAC3CO,EAAAA,EAAKA,CAACG,MAAM,CAAGH,EAAAA,EAAKA,CAACG,MAAM,CAAGH,EAAAA,EAAKA,CAACI,QAAQ,CAGrE,eAAeK,EAAiBC,CAAM,CAAEC,CAAc,EACpD,IAAIC,EAAO,MAAMF,EAAGG,KAAK,CAACC,KAAK,CAACC,SAAS,CAAC,CACxCC,MAAOC,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACH,EAAAA,KAAKA,CAACI,IAAI,CAAEP,EACxB,GAEA,GAAI,CAACC,EAAM,CACT,GAAM,CAACO,EAAQ,CAAG,MAAMT,EAAGU,MAAM,CAACN,EAAAA,KAAKA,EACpCO,MAAM,CAAC,CACNH,KAAMP,EACNW,YAAavB,CAAiB,CAACY,EAAS,GAEzCY,SAAS,GACZX,EAAOO,CACT,CAEA,OAAOP,CACT,CAEO,eAAeY,EAAiBd,CAAM,CAAEpB,CAAc,CAAEmC,CAAc,EAC3E,MAAMf,EAAGgB,MAAM,CAACC,EAAAA,SAASA,EACtBX,KAAK,CAACC,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACU,EAAAA,SAASA,CAACrC,MAAM,CAAEA,IAE9B,MAAMoB,EAAGU,MAAM,CAACO,EAAAA,SAASA,EACtBN,MAAM,CAAC,QACN/B,SACAmC,CACF,EACJ,CAEO,eAAeG,EAAYtC,CAAc,EAC9C,IAAMoB,EAAKmB,CAAAA,EAAAA,EAAAA,CAAAA,CAAQA,GAKnB,MAAOC,CAJiB,MAAMpB,EAAGG,KAAK,CAACc,SAAS,CAACI,QAAQ,CAAC,CACxDf,MAAOC,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACU,EAAAA,SAASA,CAACrC,MAAM,CAAEA,GAC5B0C,KAAM,CAAEpB,MAAM,CAAK,CACrB,GACsB,CAAC,EAAE,CAACA,IAAI,CAACM,IACjC,CAEO,eAAee,EAAgBC,CAAsB,EAC1D,IAAM5C,EAAS,MAAMD,CAAAA,EAAAA,EAAAA,EAAAA,CAASA,GAE9B,GAAI,CAACC,EAAQ,OAAO,EAEpB,IAAMoB,EAAKmB,CAAAA,EAAAA,EAAAA,CAAAA,CAAQA,GAMbM,EAAgBL,CALE,MAAMpB,EAAGG,KAAK,CAACc,SAAS,CAACI,QAAQ,CAAC,CACxDf,MAAOC,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACU,EAAAA,SAASA,CAACrC,MAAM,CAAEA,GAC5B0C,KAAM,CAAEpB,MAAM,CAAK,CACrB,IAEsCwB,GAAG,CAACC,GAAMA,EAAGzB,IAAI,CAACM,IAAI,EAC5D,MAAOoB,CAAAA,EAAAA,EAAAA,EAAAA,CAAaA,CAACH,EAAyBD,EAChD,CAEO,GAAM,CACXK,SAAU,KAAEC,CAAG,MAAEC,CAAI,CAAE,CACvB9C,MAAI,QACJ+C,CAAM,SACNC,CAAO,CACR,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAQA,CAAC,IAAO,EAClBC,OAAQC,QAAQvC,GAAG,CAACwC,WAAW,CAC/BC,QAASC,CAAAA,EAAAA,EAAAA,CAAAA,CAAcA,CAACpB,CAAAA,EAAAA,EAAAA,CAAAA,CAAQA,GAAI,CAClCqB,WAAYC,EAAAA,KAAKA,CACjBC,cAAeC,EAAAA,QAAQA,GAEzBC,UAAW,CACTC,CAAAA,EAAAA,EAAAA,CAAAA,CAAMA,CAAC,CACLC,SAAUV,QAAQvC,GAAG,CAACkD,cAAc,CACpCC,aAAcZ,QAAQvC,GAAG,CAACoD,kBAAkB,GAE9CC,CAAAA,EAAAA,EAAAA,CAAAA,CAAmBA,CAAC,CAClB1C,KAAM,cACN2C,YAAa,CACXC,SAAU,CAAEC,MAAO,MAAOC,KAAM,OAAQC,YAAa,QAAS,EAC9DC,SAAU,CAAEH,MAAO,KAAMC,KAAM,WAAYC,YAAa,OAAQ,CAClE,EACA,MAAME,UAAUN,CAAW,EACzB,GAAI,CAACA,EACH,MAAM,KADU,CACA,aAGlB,GAAM,UAAEC,CAAQ,UAAEI,CAAQ,CAAE,CAAGL,EAE/B,GAAI,CACFO,EAAAA,CAAUA,CAACC,KAAK,CAAC,UAAEP,WAAUI,CAAS,EAExC,CAAE,MAAOI,EAAO,CACd,MAAM,MAAU,UAClB,CAEA,IAAM5D,EAAKmB,CAAAA,EAAAA,EAAAA,CAAAA,CAAQA,GAEbjC,EAAO,MAAMc,EAAGG,KAAK,CAACsC,KAAK,CAACpC,SAAS,CAAC,CAC1CC,MAAOC,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACkC,EAAAA,KAAKA,CAACW,QAAQ,CAAEA,EAC5B,GAEA,GAAI,CAAClE,GAKD,CADY,EAJL,IAIW2E,CAAAA,EACR,EADQA,CAAAA,CAAeA,CAACL,EAAoBtE,EAAKsE,QAAQ,EAHrE,MAAUM,MAAM,YAQlB,MAAO,CACL,GAAG5E,CAAI,CACPsE,cAAUO,CACZ,CACF,CACF,GACD,CACDC,OAAQ,CACN,MAAMhC,OAAO,MAAE9C,CAAI,CAAE,EACnB,GAAKA,CAAD,CAAMC,EAAE,CAEZ,CAFc,EAEV,CACF,IAAMa,EAAKmB,CAAAA,EAAAA,EAAAA,CAAAA,CAAQA,GAKnB,GAJqB,CAIjB8C,KAJuBjE,EAAGG,KAAK,CAACc,SAAS,CAACZ,SAAS,CAAC,CACtDC,MAAOC,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACU,EAAAA,SAASA,CAACrC,MAAM,CAAEM,EAAKC,EAAE,CACrC,GAEkB,OAElB,IAAM+E,EAAc,MAAMvE,IACpBO,EAAO,MAAMH,EAAiBC,EAAIkE,EACxC,OAAMpD,EAAiBd,EAAId,EAAKC,EAAE,CAAEe,EAAKf,EAAE,CAC7C,CAAE,MAAOyE,EAAO,CACdO,QAAQP,KAAK,CAAC,wBAAyBA,EACzC,CACF,CACF,EACAQ,UAAW,CACHC,IAAN,MAAU,OAAEC,CAAK,MAAEpF,CAAI,CAAE,IACnBA,IACFoF,EADQ,EACA,CAAGpF,EAAKC,EAAE,CAClBmF,EAAM9D,IAAI,CAAGtB,EAAKsB,IAAI,EAAItB,EAAKkE,QAAQ,CACvCkB,EAAMlB,QAAQ,CAAGlE,EAAKkE,QAAQ,CAC9BkB,EAAMC,KAAK,CAAGrF,EAAKqF,KAAK,EDtJzB,SAASC,CAA8B,EAC5C,IAAMC,CCqJ6CD,CDrJnChE,CAAI,CAAC,EAAE,CAACkE,WAAW,GAE7BC,EAAaC,MAAMC,IAAI,CAACrE,GAAMsE,MAAM,CACxC,CAACC,EAAKC,IAASD,EAAMC,EAAKC,UAAU,CAAC,GAAI,GACvC7F,EAAO8F,MAAM,CAEXC,EAAkB/F,CAAM,CAACuF,EAAW,CAEpCS,EAAM,CAAC;;2CAE4B,EAAED,EAAgB;;;;;;;;;;;;;QAarD,EAAEV,QAAQ;;;EAGhB,CAAC,CAACY,IAAI,GAGAC,EADU,IAAIC,cACEC,MAAM,CAACJ,GACvBK,EAASC,EAAOb,IAADa,CAAMJ,GAAOK,QAAQ,CAAC,UAE3C,MAAO,CAAC,0BAA0B,EAAEF,EAAAA,CAAQ,ECqHQnB,EAAM9D,IAAI,GAEnD8D,GAET,MAAMtF,QAAQ,CAAEA,SAAO,OAAEsF,CAAK,CAAE,EAC9B,GAAIA,GAAStF,EAAQE,IAAI,CAAE,CACzBF,EAAQE,IAAI,CAACC,EAAE,CAAGmF,EAAMnF,EAAE,CAC1BH,EAAQE,IAAI,CAACsB,IAAI,CAAG8D,EAAM9D,IAAI,CAC9BxB,EAAQE,IAAI,CAACkE,QAAQ,CAAGkB,EAAMlB,QAAQ,CACtCpE,EAAQE,IAAI,CAACqF,KAAK,CAAGD,EAAMC,KAAK,CAEhC,IAAMvE,EAAKmB,CAAAA,EAAAA,EAAAA,CAAAA,CAAQA,GACfC,EAAkB,MAAMpB,EAAGG,KAAK,CAACc,SAAS,CAACI,QAAQ,CAAC,CACtDf,MAAOC,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACU,EAAAA,SAASA,CAACrC,MAAM,CAAEI,EAAQE,IAAI,CAACC,EAAE,EAC3CmC,KAAM,CAAEpB,MAAM,CAAK,CACrB,GAEA,GAAI,CAACkB,EAAgB8D,MAAM,CAAE,CAC3B,IAAMhB,EAAc,MAAMvE,IACpBO,EAAO,MAAMH,EAAiBC,EAAIkE,EACxC,OAAMpD,EAAiBd,EAAIhB,EAAQE,IAAI,CAACC,EAAE,CAAEe,EAAKf,EAAE,EACnDiC,EAAkB,CAAC,CACjBxC,OAAQI,EAAQE,IAAI,CAACC,EAAE,CACvB4B,OAAQb,EAAKf,EAAE,CACfyG,UAAW,IAAIC,KACf3F,KAAMA,CACR,EAAE,CAGJlB,EAAQE,IAAI,CAACkB,KAAK,CAAGgB,EAAgBM,GAAG,CAACC,GAAO,OACxCA,EAAGzB,IAAI,CAACM,IAAI,CACpB,EACF,CAEA,OAAOxB,CACT,CACF,EACAA,QAAS,CACP8G,SAAU,KACZ,EACF,GAEO,CAFJ,cAEmBC,EAAS3C,CAAgB,CAAEI,CAAgB,EAC/D,IAAMxD,EAAKmB,CAAAA,EAAAA,EAAAA,CAAAA,CAAQA,GAMnB,GAJiB,CAIb6E,KAJmBhG,EAAGG,GAIZ,EAJiB,CAACsC,KAAK,CAACpC,SAAS,CAAC,CAC9CC,MAAOC,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACkC,EAAAA,KAAKA,CAACW,QAAQ,CAAEA,EAC5B,GAGE,MAAM,MAAU,UAGlB,IAAM6C,EAAiB,MAAMC,CAAAA,EAAAA,EAAAA,CAAAA,CAAYA,CAAC1C,GAEpC,CAACtE,EAAK,CAAG,MAAMc,EAAGU,MAAM,CAAC+B,EAAAA,KAAKA,EACjC9B,MAAM,CAAC,UACNyC,EACAI,SAAUyC,CACZ,GACCpF,SAAS,GAEZ,OAAO3B,CACT,yEC9NO,IAAMiC,EAAW,IAAMgF,CAAAA,EAAAA,EAAAA,CAAAA,CAAOA,CAACvG,CAAAA,EAAAA,EAAAA,iBAAAA,CAAiBA,GAAGC,GAAG,CAACuG,EAAE,CAAE,CAAEC,MAAMA,CAAAA,CAAC,GAAE,gCAAHA,qBCJnE,IAAM/G,EAAQ,CACnBC,QAAS,UACTC,KAAM,OACNC,OAAQ,SACRC,SAAU,UACZ,EAAW,EAIgB,CACzB4G,aAAc,eACdC,eAAgB,iBAChBC,aAAc,eACdC,cAAe,gBACfC,eAAgB,iBAChBC,yBAA0B,0BAC5B,EAAW,EAIiD,CAC1D,CAACrH,EAAMC,OAAO,CAAC,CAAEqH,OAAOjG,MAAM,CAACkG,GAC/B,CAACvH,EAAME,IAAI,CAAC,CAAE,CACZqH,EAAYP,YAAY,CACxBO,EAAYN,cAAc,CAC1BM,EAAYH,cAAc,CAC1BG,EAAYF,wBAAwB,CACrC,CACD,CAACrH,EAAMG,MAAM,CAAC,CAAE,CACdoH,EAAYP,YAAY,CACxBO,EAAYN,cAAc,CAC1BM,EAAYF,wBAAwB,CACrC,CACD,CAACrH,EAAMI,QAAQ,CAAC,CAAE,CAChBmH,EAAYP,YAAY,CACxBO,EAAYF,wBAAwB,CACrC,EACQ,SAEK/E,EAAcX,CAAiB,CAAEO,CAAsB,EACrE,OAAOP,EAAU6F,IAAI,CAAC5G,GAAQ6G,CAAgB,CAAC7G,EAAK,EAAE8G,SAASxF,GACjE,gcCpCO,IAAMiB,EAAQwE,CAAAA,EAAAA,EAAAA,CAAAA,CAAWA,CAAC,OAAQ,CACvC9H,GAAI+H,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,MACNC,UAAU,GACVC,UAAU,CAAC,IAAMC,OAAOC,UAAU,IACrC9G,KAAM0G,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,QACXK,MAAOL,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,SAASM,MAAM,GAC3BC,cAAeC,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,gBAAiB,CAAEC,KAAM,cAAe,GAC/DpD,MAAO2C,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,SACZ9D,SAAU8D,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,YAAYM,MAAM,GACjChE,SAAU0D,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,WACjB,GAAE,EACsBD,CAAAA,EAAAA,EAAAA,CAAAA,CAAWA,CACjC,UACA,CACErI,OAAQsI,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,UACVU,OAAO,GACPC,UAAU,CAAC,IAAMpF,EAAMtD,EAAE,CAAE,CAAE2I,SAAU,SAAU,GACpDxE,KAAM4D,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,QAAQa,KAAK,GAAuBH,OAAO,GACtDI,SAAUd,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,YAAYU,OAAO,GAClCK,kBAAmBf,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,qBAAqBU,OAAO,GACpDM,cAAehB,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,iBACpBiB,aAAcjB,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,gBACnBkB,WAAYV,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,cACpBW,WAAYnB,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,cACjBoB,MAAOpB,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,SACZqB,SAAUrB,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,YACfsB,cAAetB,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,gBACtB,EACA,GAAc,EACZuB,KADY,OACCtB,CAAAA,EAAAA,EAAAA,EAAAA,CAAUA,CAAC,CACtBuB,QAAS,CAACC,EAAQX,QAAQ,CAAEW,EAAQV,iBAAiB,CAAC,GAE1D,GAGWW,CAFZ,CAEqB3B,CAAAA,EAAAA,EAAAA,CAAAA,CAAWA,CAAC,QAAS,CACzC9H,GAAI+H,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,MAAMC,UAAU,GAAGC,UAAU,CAAC,IAAMC,OAAOC,UAAU,IAC9DuB,QAAS3B,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,WAAWU,OAAO,GAAGJ,MAAM,GACzC5I,OAAQsI,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,UAAUW,UAAU,CAAC,IAAMpF,EAAMtD,EAAE,CAAE,CAAE2I,SAAU,SAAU,GACxElC,UAAW8B,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,aAAc,CAAEC,KAAM,cAAe,GACrDC,OAAO,GACPR,UAAU,CAAC,IAAM,IAAIvB,MACxBiD,UAAWpB,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,aAAc,CAAEC,KAAM,cAAe,GAAGC,OAAO,EACpE,EAAG,GAAY,EACbmB,GADa,UACCC,CAAAA,EAAAA,EAAAA,EAAAA,CAAKA,CAAC,wBAAwBC,EAAE,CAACC,EAAMJ,SAAS,CAChE,IAAG,EAEqB7B,CAAAA,EAAAA,EAAAA,CAAAA,CAAWA,CAAC,UAAW,CAC7C9H,GAAI+H,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,MAAMC,UAAU,GAAGC,UAAU,CAAC,IAAMC,OAAOC,UAAU,IAC9D6B,QAASjC,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,WACXU,OAAO,GACPC,UAAU,CAAC,IAAMe,EAAOzJ,EAAE,CAAE,CAAE2I,SAAU,SAAU,GACrDsB,YAAalC,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,gBAAgBU,OAAO,GACzCyB,QAASnC,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,WAAWU,OAAO,GAChC0B,QAASpC,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,WAAWU,OAAO,GAChC2B,KAAMrC,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,QACXsC,WAAY9B,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,cAAe,CAAEC,KAAM,cAAe,GACvDC,OAAO,GACPR,UAAU,CAAC,IAAM,IAAIvB,KAC1B,EAAG,GAAY,EACb4D,GADa,QACDT,CAAAA,EAAAA,EAAAA,EAAAA,CAAKA,CAAC,wBAAwBC,EAAE,CAACC,EAAMC,OAAO,EAC5D,GAEaO,CAFV,CAEqBzC,CAAAA,EAAAA,EAAAA,CAAAA,CAAWA,CAAC,UAAW,CAC7C9H,GAAI+H,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,MAAMC,UAAU,GAAGC,UAAU,CAAC,IAAMC,OAAOC,UAAU,IAC9D1I,OAAQsI,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,WACVU,OAAO,GACPC,UAAU,CAAC,IAAMpF,EAAMtD,EAAE,CAAE,CAAE2I,SAAU,SAAU,GACpD6B,IAAKzC,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,OAAOU,OAAO,GACxBgC,QAASlC,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,UAAW,CAAEC,KAAM,SAAU,GAAGC,OAAO,GAAGiC,OAAO,EAAC,GACnEjE,UAAW8B,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,aAAc,CAAEC,KAAM,cAAe,GACrDC,OAAO,GACPR,UAAU,CAAC,IAAM,IAAIvB,MACxBiE,UAAWpC,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,aAAc,CAAEC,KAAM,cAAe,GACrDC,OAAO,GACPR,UAAU,CAAC,IAAM,IAAIvB,KAC1B,GAAE,EAEmBoB,CAAAA,EAAAA,EAAAA,CAAAA,CAAWA,CAAC,OAAQ,CACvC9H,GAAI+H,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,MAAMC,UAAU,GAAGC,UAAU,CAAC,IAAMC,OAAOC,UAAU,IAC9D9G,KAAM0G,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,QAAQU,OAAO,GAC1BhH,YAAasG,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,eAClBtB,UAAW8B,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,aAAc,CAAEC,KAAM,WAAY,GAAGP,UAAU,CAAC,IAAM,IAAIvB,MAC7EiE,UAAWpC,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,aAAc,CAAEC,KAAM,WAAY,GAAGP,UAAU,CAAC,IAAM,IAAIvB,KAC/E,GAAG,EAEsBoB,CAAAA,EAAAA,EAAAA,CAAAA,CAAWA,CAAC,YAAa,CAChDrI,OAAQsI,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,WAAWU,OAAO,GAAGC,UAAU,CAAC,IAAMpF,EAAMtD,EAAE,CAAE,CAAE2I,SAAU,SAAU,GACnF/G,OAAQmG,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,WAAWU,OAAO,GAAGC,UAAU,CAAC,IAAMzH,EAAMjB,EAAE,CAAE,CAAE2I,SAAU,SAAU,GACnFlC,UAAW8B,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,aAAc,CAAEC,KAAM,WAAY,GAAGP,UAAU,CAAC,IAAM,IAAIvB,KAC/E,EAAG,GAAY,EACbkE,GADa,CACT5C,EAAAA,EAAAA,EAAAA,CAAUA,CAAC,CAAEuB,QAAS,CAACQ,EAAMtK,MAAM,CAAEsK,EAAMnI,MAAM,CAAC,GACxD,GAEaiJ,CAFT,CAEmB/C,CAAAA,EAAAA,EAAAA,CAAAA,CAAWA,CAAC,WAAY,CAC7C9H,GAAI+H,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,MAAMC,UAAU,GAAGC,UAAU,CAAC,IAAMC,OAAOC,UAAU,IAC9D1I,OAAQsI,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,WAAWU,OAAO,GAAGC,UAAU,CAAC,IAAMpF,EAAMtD,EAAE,EAC3DqB,KAAM0G,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,QAAQU,OAAO,GAC1BqC,IAAK/C,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,OAAOU,OAAO,GAAGJ,MAAM,GACjC5B,UAAW8B,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,aAAc,CAAEC,KAAM,WAAY,GAAGP,UAAU,CAAC,IAAM,IAAIvB,MAC7EiD,UAAWpB,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,aAAc,CAAEC,KAAM,WAAY,GACrDiC,QAASlC,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,UAAW,CAAEC,KAAM,SAAU,GAAGC,OAAO,GAAGiC,OAAO,EAAC,EACrE,EAAG,GAAY,EACbK,GADa,cACKC,CAAAA,EAAAA,EAAAA,EAAAA,CAAWA,CAAC,uBAAuBlB,EAAE,CAACC,EAAM1I,IAAI,CAAE0I,EAAMtK,MAAM,EAClF,GAEawL,CAFT,CAE4BC,CAAAA,EAAAA,EAAAA,EAAAA,CAASA,CAACL,EAAS,CAAC,KAAEM,CAAG,CAAE,GAAM,EAC/DpL,KAAMoL,EAAI7H,EAAO,CACf8H,OAAQ,CAACP,EAAQpL,MAAM,CAAC,CACxBiJ,WAAY,CAACpF,EAAMtD,EAAE,CAAC,GAE1B,GAGaqL,CAHT,CAG4BvD,CAAAA,EAAAA,EAAAA,CAAAA,CAAWA,CAAC,oBAAqB,CAC/D9H,GAAI+H,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,MAAMC,UAAU,GAAGC,UAAU,CAAC,IAAMC,OAAOC,UAAU,IAC9D1I,OAAQsI,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,WAAWU,OAAO,GAAGC,UAAU,CAAC,IAAMpF,EAAMtD,EAAE,CAAE,CAAE2I,SAAU,SAAU,GACnF2C,aAAcvD,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,iBAAiBU,OAAO,GAC3CpH,KAAM0G,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,QAAQU,OAAO,GAC1BvD,IAAK6C,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,OAAOU,OAAO,GACxBgC,QAASlC,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,UAAW,CAAEC,KAAM,SAAU,GAAGC,OAAO,GAAGiC,OAAO,EAAC,GACnEjE,UAAW8B,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,aAAc,CAAEC,KAAM,WAAY,GAAGP,UAAU,CAAC,IAAM,IAAIvB,MAC7EiD,UAAWpB,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,aAAc,CAAEC,KAAM,WAAY,GACrD+C,WAAYhD,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,eAAgB,CAAEC,KAAM,WAAY,EAC1D,EAAG,GAAY,EACbgD,GADa,SACAR,CAAAA,EAAAA,EAAAA,EAAAA,CAAWA,CAAC,wBAAwBlB,EAAE,CAACC,EAAMuB,YAAY,EACtEG,eAAgBT,CAAAA,EAAAA,EAAAA,EAAAA,CAAWA,CAAC,wCAAwClB,EAAE,CAACC,EAAM1I,IAAI,CAAE0I,EAAMtK,MAAM,EACjG,GAEaiM,CAFT,CAEqCR,CAAAA,EAAAA,EAAAA,EAAAA,CAASA,CAACG,EAAkB,CAAC,MAAEM,CAAI,CAAE,GAAM,EAClFC,SAAUD,EAAKE,GACjB,GAGaA,CAHT,CAG6B/D,CAAAA,EAAAA,EAAAA,CAAAA,CAAWA,CAAC,sBAAuB,CAClE9H,GAAI+H,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,MAAMC,UAAU,GAAGC,UAAU,CAAC,IAAMC,OAAOC,UAAU,IAC9D1I,OAAQsI,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,WAAWU,OAAO,GAAGC,UAAU,CAAC,IAAMpF,EAAMtD,EAAE,CAAE,CAAE2I,SAAU,SAAU,GACnFqB,QAASjC,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,YAAYU,OAAO,GAAGC,UAAU,CAAC,IAAMe,EAAOzJ,EAAE,CAAE,CAAE2I,SAAU,SAAU,GACtFmD,aAAc/D,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,iBAAiBW,UAAU,CAAC,IAAM2C,EAAiBrL,EAAE,CAAE,CAAE2I,SAAU,UAAW,GACjGlC,UAAW8B,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,aAAc,CAAEC,KAAM,WAAY,GAAGP,UAAU,CAAC,IAAM,IAAIvB,KAC/E,EAAIqD,GAAW,EACbgC,GADa,aACIf,CAAAA,EAAAA,EAAAA,EAAAA,CAAWA,CAAC,6BAA6BlB,EAAE,CAACC,EAAMtK,MAAM,CAAEsK,EAAMC,OAAO,EAC1F,GAEagC,CAFT,CAEsCd,CAAAA,EAAAA,EAAAA,EAAAA,CAASA,CAACW,EAAmB,CAAC,KAAEV,CAAG,CAAE,GAAM,EACnFpL,KAAMoL,EAAI7H,EAAO,CACf8H,OAAQ,CAACS,EAAkBpM,MAAM,CAAC,CAClCiJ,WAAY,CAACpF,EAAMtD,EAAE,CAAC,GAExBoI,MAAO+C,EAAI1B,EAAQ,CACjB2B,OAAQ,CAACS,EAAkB7B,OAAO,CAAC,CACnCtB,WAAY,CAACe,EAAOzJ,EAAE,CAAC,GAEzBiM,WAAYd,EAAIE,EAAkB,CAChCD,OAAQ,CAACS,EAAkBC,YAAY,CAAC,CACxCpD,WAAY,CAAC2C,EAAiBrL,EAAE,CAAC,GAErC,GAGakM,CAHT,CAGkCpE,CAAAA,EAAAA,EAAAA,CAAAA,CAAWA,CAAC,2BAA4B,CAC5E9H,GAAI+H,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,MAAMC,UAAU,GAAGC,UAAU,CAAC,IAAMC,OAAOC,UAAU,IAC9DC,MAAOL,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,SAASU,OAAO,GAC5B0D,KAAMpE,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,QAAQU,OAAO,GAC1BhC,UAAW8B,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,aAAc,CAAEC,KAAM,WAAY,GAAGP,UAAU,CAAC,IAAM,IAAIvB,MAC7EiD,UAAWpB,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,aAAc,CAAEC,KAAM,WAAY,GAAGC,OAAO,GAC/D2D,KAAM7D,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,OAAQ,CAAEC,KAAM,SAAU,GAAGC,OAAO,GAAGiC,OAAO,EAAC,EAC/D,EAAG,GAAY,EACb2B,GADa,MACHxC,CAAAA,EAAAA,EAAAA,EAAAA,CAAKA,CAAC,gCAAgCC,EAAE,CAACC,EAAM3B,KAAK,EAC9DwB,aAAcC,CAAAA,EAAAA,EAAAA,EAAAA,CAAKA,CAAC,qCAAqCC,EAAE,CAACC,EAAMJ,SAAS,EAC7E,GAEa2C,CAFT,CAE8BpB,CAAAA,EAAAA,EAAAA,EAAAA,CAASA,CAACpJ,EAAW,CAAC,KAAEqJ,CAAG,CAAE,GAAM,EACnEpL,KAAMoL,EAAI7H,EAAO,CACf8H,OAAQ,CAACtJ,EAAUrC,MAAM,CAAC,CAC1BiJ,WAAY,CAACpF,EAAMtD,EAAE,CAAC,GAExBe,KAAMoK,EAAIlK,EAAO,CACfmK,OAAQ,CAACtJ,EAAUF,MAAM,CAAC,CAC1B8G,WAAY,CAACzH,EAAMjB,EAAE,CAAC,EAE1B,IAAI,EAE0BkL,CAAAA,EAAAA,EAAAA,EAAAA,CAASA,CAAC5H,EAAO,CAAC,MAAEqI,CAAI,CAAE,GAAM,EAC5D7J,UAAW6J,EAAK7J,GAChB+I,QAASc,EAAKd,GAChB,GAEa0B,CAFT,CAE0BrB,CAAAA,EAAAA,EAAAA,EAAAA,CAASA,CAACjK,EAAO,CAAC,CAAE0K,MAAI,CAAE,GAAM,EAC5D7J,UAAW6J,EAAK7J,GAClB,IAAI,2EChMG,SAAS0K,EAAG,GAAGC,CAAoB,EACxC,MAAOC,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAACC,CAAAA,EAAAA,EAAAA,CAAAA,CAAIA,CAACF,GACtB,CAEO,eAAe1F,EAAa1C,CAAgB,EACjD,IAAMuI,EAAU,IAAIxG,YACdyG,EAAO5J,QAAQvC,GAAG,CAACwC,WAAW,EAAI,GAClC4J,EAAOF,EAAQvG,MAAM,CAAChC,EAAWwI,GAEvC,OAAOE,KAAKC,OAAOC,YAAY,IAAI,IAAIC,WAD1B,MAAMhF,OAAOiF,MAAM,CAACC,MAAM,CAAC,UAAWN,KAErD,CAEO,eAAepI,EAAgBL,CAAgB,CAAEyC,CAAsB,EAE5E,OADa,MAAMC,EAAa1C,KAChByC,CAClB,kDChBO,IAAMvC,EAAa8I,EAAAA,CAACA,CAACC,MAAM,CAAC,CACjCrJ,SAAUoJ,EAAAA,CAACA,CAACE,MAAM,GACfC,GAAG,CAAC,EAAG,WACPC,GAAG,CAAC,GAAI,gBACRC,KAAK,CAAC,mBAAoB,uBAC1BC,MAAM,CAACC,GAAO,CAACA,EAAI/F,QAAQ,CAAC,KAAM,cACrCxD,SAAUgJ,EAAAA,CAACA,CAACE,MAAM,GACfC,GAAG,CAAC,EAAG,eACZ,GAAE", "sources": ["webpack://_N_E/./app/lib/apiKey.ts", "webpack://_N_E/./app/lib/avatar.ts", "webpack://_N_E/./app/lib/auth.ts", "webpack://_N_E/./app/lib/db.ts", "webpack://_N_E/./app/lib/permissions.ts", "webpack://_N_E/./app/lib/schema.ts", "webpack://_N_E/./app/lib/utils.ts", "webpack://_N_E/./app/lib/validation.ts"], "sourcesContent": ["import { createDb } from \"./db\"\r\nimport { api<PERSON><PERSON>s } from \"./schema\"\r\nimport { eq, and, gt } from \"drizzle-orm\"\r\nimport { NextResponse } from \"next/server\"\r\nimport type { User } from \"next-auth\"\r\nimport { auth } from \"./auth\"\r\nimport { headers } from \"next/headers\"\r\n\r\nasync function getUserByApiKey(key: string): Promise<User | null> {\r\n  const db = createDb()\r\n  const apiKey = await db.query.apiKeys.findFirst({\r\n    where: and(\r\n      eq(apiKeys.key, key),\r\n      eq(apiKeys.enabled, true),\r\n      gt(apiKeys.expiresAt, new Date())\r\n    ),\r\n    with: {\r\n      user: true\r\n    }\r\n  })\r\n\r\n  if (!apiKey) return null\r\n\r\n  return apiKey.user\r\n}\r\n\r\nexport async function handleApiKeyAuth(apiKey: string, pathname: string) {\r\n  if (!pathname.startsWith('/api/emails') && !pathname.startsWith('/api/config')) {\r\n    return NextResponse.json(\r\n      { error: \"无权限查看\" },\r\n      { status: 403 }\r\n    )\r\n  }\r\n\r\n  const user = await getUserByApiKey(apiKey)\r\n  if (!user?.id) {\r\n    return NextResponse.json(\r\n      { error: \"无效的 API Key\" },\r\n      { status: 401 }\r\n    )\r\n  }\r\n\r\n  const response = NextResponse.next()\r\n  response.headers.set(\"X-User-Id\", user.id)\r\n  return response\r\n}\r\n\r\nexport const getUserId = async () => {\r\n  const headersList = await headers()\r\n  const userId = headersList.get(\"X-User-Id\")\r\n\r\n  if (userId) return userId\r\n\r\n  const session = await auth()\r\n\r\n  return session?.user.id\r\n}\r\n\r\nexport const getEmailAddress = async () => {\r\n  const headersList = await headers()\r\n  return headersList.get(\"X-Email-Address\")\r\n}\r\n\r\nexport const getCredentialId = async () => {\r\n  const headersList = await headers()\r\n  return headersList.get(\"X-Credential-Id\")\r\n}\r\n", "const COLORS = [\r\n  '#2196F3', // 蓝色\r\n  '#009688', // 青色\r\n  '#9C27B0', // 紫色\r\n  '#F44336', // 红色\r\n  '#673AB7', // 深紫色\r\n  '#3F51B5', // 靛蓝\r\n  '#4CAF50', // 绿色\r\n  '#FF5722', // 深橙\r\n  '#795548', // 棕色\r\n  '#607D8B', // 蓝灰\r\n];\r\n\r\nexport function generateAvatarUrl(name: string): string {\r\n  const initial = name[0].toUpperCase();\r\n  \r\n  const colorIndex = Array.from(name).reduce(\r\n    (acc, char) => acc + char.charCodeAt(0), 0\r\n  ) % COLORS.length;\r\n  \r\n  const backgroundColor = COLORS[colorIndex];\r\n  \r\n  const svg = `\r\n    <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 100 100\" width=\"100\" height=\"100\">\r\n      <rect width=\"100\" height=\"100\" fill=\"${backgroundColor}\"/>\r\n      <text \r\n        x=\"50%\" \r\n        y=\"50%\" \r\n        fill=\"white\" \r\n        font-family=\"system-ui, -apple-system, sans-serif\" \r\n        font-size=\"45\" \r\n        font-weight=\"500\"\r\n        text-anchor=\"middle\"\r\n        alignment-baseline=\"central\"\r\n        dominant-baseline=\"central\"\r\n        style=\"text-transform: uppercase\"\r\n      >\r\n        ${initial}\r\n      </text>\r\n    </svg>\r\n  `.trim();\r\n\r\n  const encoder = new TextEncoder();\r\n  const bytes = encoder.encode(svg);\r\n  const base64 = Buffer.from(bytes).toString('base64');\r\n  \r\n  return `data:image/svg+xml;base64,${base64}`;\r\n} ", "import NextAuth from \"next-auth\"\r\nimport GitHub from \"next-auth/providers/github\"\r\nimport { Dr<PERSON>zleAdapter } from \"@auth/drizzle-adapter\"\r\nimport { createDb, Db } from \"./db\"\r\nimport { accounts, users, roles, userRoles } from \"./schema\"\r\nimport { eq } from \"drizzle-orm\"\r\nimport { getRequestContext } from \"@cloudflare/next-on-pages\"\r\nimport { Permission, hasPermission, ROLES, Role } from \"./permissions\"\r\nimport CredentialsProvider from \"next-auth/providers/credentials\"\r\nimport { hashPassword, comparePassword } from \"@/lib/utils\"\r\nimport { authSchema } from \"@/lib/validation\"\r\nimport { generateAvatarUrl } from \"./avatar\"\r\nimport { getUserId } from \"./apiKey\"\r\n\r\nconst ROLE_DESCRIPTIONS: Record<Role, string> = {\r\n  [ROLES.EMPEROR]: \"皇帝（网站所有者）\",\r\n  [ROLES.DUKE]: \"公爵（超级用户）\",\r\n  [ROLES.KNIGHT]: \"骑士（高级用户）\",\r\n  [ROLES.CIVILIAN]: \"平民（普通用户）\",\r\n}\r\n\r\nconst getDefaultRole = async (): Promise<Role> => {\r\n  const defaultRole = await getRequestContext().env.SITE_CONFIG.get(\"DEFAULT_ROLE\")\r\n  return defaultRole === ROLES.KNIGHT ? ROLES.KNIGHT : ROLES.CIVILIAN\r\n}\r\n\r\nasync function findOrCreateRole(db: Db, roleName: Role) {\r\n  let role = await db.query.roles.findFirst({\r\n    where: eq(roles.name, roleName),\r\n  })\r\n\r\n  if (!role) {\r\n    const [newRole] = await db.insert(roles)\r\n      .values({\r\n        name: roleName,\r\n        description: ROLE_DESCRIPTIONS[roleName],\r\n      })\r\n      .returning()\r\n    role = newRole\r\n  }\r\n\r\n  return role\r\n}\r\n\r\nexport async function assignRoleToUser(db: Db, userId: string, roleId: string) {\r\n  await db.delete(userRoles)\r\n    .where(eq(userRoles.userId, userId))\r\n\r\n  await db.insert(userRoles)\r\n    .values({\r\n      userId,\r\n      roleId,\r\n    })\r\n}\r\n\r\nexport async function getUserRole(userId: string) {\r\n  const db = createDb()\r\n  const userRoleRecords = await db.query.userRoles.findMany({\r\n    where: eq(userRoles.userId, userId),\r\n    with: { role: true },\r\n  })\r\n  return userRoleRecords[0].role.name\r\n}\r\n\r\nexport async function checkPermission(permission: Permission) {\r\n  const userId = await getUserId()\r\n\r\n  if (!userId) return false\r\n\r\n  const db = createDb()\r\n  const userRoleRecords = await db.query.userRoles.findMany({\r\n    where: eq(userRoles.userId, userId),\r\n    with: { role: true },\r\n  })\r\n\r\n  const userRoleNames = userRoleRecords.map(ur => ur.role.name)\r\n  return hasPermission(userRoleNames as Role[], permission)\r\n}\r\n\r\nexport const {\r\n  handlers: { GET, POST },\r\n  auth,\r\n  signIn,\r\n  signOut\r\n} = NextAuth(() => ({\r\n  secret: process.env.AUTH_SECRET,\r\n  adapter: DrizzleAdapter(createDb(), {\r\n    usersTable: users,\r\n    accountsTable: accounts,\r\n  }),\r\n  providers: [\r\n    GitHub({\r\n      clientId: process.env.AUTH_GITHUB_ID,\r\n      clientSecret: process.env.AUTH_GITHUB_SECRET,\r\n    }),\r\n    CredentialsProvider({\r\n      name: \"Credentials\",\r\n      credentials: {\r\n        username: { label: \"用户名\", type: \"text\", placeholder: \"请输入用户名\" },\r\n        password: { label: \"密码\", type: \"password\", placeholder: \"请输入密码\" },\r\n      },\r\n      async authorize(credentials) {\r\n        if (!credentials) {\r\n          throw new Error(\"请输入用户名和密码\")\r\n        }\r\n\r\n        const { username, password } = credentials\r\n\r\n        try {\r\n          authSchema.parse({ username, password })\r\n        // eslint-disable-next-line @typescript-eslint/no-unused-vars\r\n        } catch (error) {\r\n          throw new Error(\"输入格式不正确\")\r\n        }\r\n\r\n        const db = createDb()\r\n\r\n        const user = await db.query.users.findFirst({\r\n          where: eq(users.username, username as string),\r\n        })\r\n\r\n        if (!user) {\r\n          throw new Error(\"用户名或密码错误\")\r\n        }\r\n\r\n        const isValid = await comparePassword(password as string, user.password as string)\r\n        if (!isValid) {\r\n          throw new Error(\"用户名或密码错误\")\r\n        }\r\n\r\n        return {\r\n          ...user,\r\n          password: undefined,\r\n        }\r\n      },\r\n    }),\r\n  ],\r\n  events: {\r\n    async signIn({ user }) {\r\n      if (!user.id) return\r\n\r\n      try {\r\n        const db = createDb()\r\n        const existingRole = await db.query.userRoles.findFirst({\r\n          where: eq(userRoles.userId, user.id),\r\n        })\r\n\r\n        if (existingRole) return\r\n\r\n        const defaultRole = await getDefaultRole()\r\n        const role = await findOrCreateRole(db, defaultRole)\r\n        await assignRoleToUser(db, user.id, role.id)\r\n      } catch (error) {\r\n        console.error('Error assigning role:', error)\r\n      }\r\n    },\r\n  },\r\n  callbacks: {\r\n    async jwt({ token, user }) {\r\n      if (user) {\r\n        token.id = user.id\r\n        token.name = user.name || user.username\r\n        token.username = user.username\r\n        token.image = user.image || generateAvatarUrl(token.name as string)\r\n      }\r\n      return token\r\n    },\r\n    async session({ session, token }) {\r\n      if (token && session.user) {\r\n        session.user.id = token.id as string\r\n        session.user.name = token.name as string\r\n        session.user.username = token.username as string\r\n        session.user.image = token.image as string\r\n\r\n        const db = createDb()\r\n        let userRoleRecords = await db.query.userRoles.findMany({\r\n          where: eq(userRoles.userId, session.user.id),\r\n          with: { role: true },\r\n        })\r\n  \r\n        if (!userRoleRecords.length) {\r\n          const defaultRole = await getDefaultRole()\r\n          const role = await findOrCreateRole(db, defaultRole)\r\n          await assignRoleToUser(db, session.user.id, role.id)\r\n          userRoleRecords = [{\r\n            userId: session.user.id,\r\n            roleId: role.id,\r\n            createdAt: new Date(),\r\n            role: role\r\n          }]\r\n        }\r\n  \r\n        session.user.roles = userRoleRecords.map(ur => ({\r\n          name: ur.role.name,\r\n        }))\r\n      }\r\n\r\n      return session\r\n    },\r\n  },\r\n  session: {\r\n    strategy: \"jwt\",\r\n  },\r\n}))\r\n\r\nexport async function register(username: string, password: string) {\r\n  const db = createDb()\r\n  \r\n  const existing = await db.query.users.findFirst({\r\n    where: eq(users.username, username)\r\n  })\r\n\r\n  if (existing) {\r\n    throw new Error(\"用户名已存在\")\r\n  }\r\n\r\n  const hashedPassword = await hashPassword(password)\r\n  \r\n  const [user] = await db.insert(users)\r\n    .values({\r\n      username,\r\n      password: hashedPassword,\r\n    })\r\n    .returning()\r\n\r\n  return user\r\n}\r\n", "import { getRequestContext } from \"@cloudflare/next-on-pages\"\r\nimport { drizzle } from \"drizzle-orm/d1\"\r\nimport * as schema from \"./schema\"\r\n\r\nexport const createDb = () => drizzle(getRequestContext().env.DB, { schema })\r\n\r\nexport type Db = ReturnType<typeof createDb>\r\n", "export const ROLES = {\r\n  EMPEROR: 'emperor',\r\n  <PERSON>U<PERSON>: 'duke',\r\n  <PERSON><PERSON><PERSON><PERSON>: 'knight',\r\n  CIVILIAN: 'civilian',\r\n} as const;\r\n\r\nexport type Role = typeof ROLES[keyof typeof ROLES];\r\n\r\nexport const PERMISSIONS = {\r\n  MANAGE_EMAIL: 'manage_email',\r\n  <PERSON><PERSON><PERSON>_WEBHOOK: 'manage_webhook',\r\n  PROMOTE_USER: 'promote_user',\r\n  <PERSON><PERSON><PERSON>_CONFIG: 'manage_config',\r\n  MANAGE_API_KEY: 'manage_api_key',\r\n  MANAGE_EMAIL_CREDENTIALS: 'manage_email_credentials',\r\n} as const;\r\n\r\nexport type Permission = typeof PERMISSIONS[keyof typeof PERMISSIONS];\r\n\r\nexport const ROLE_PERMISSIONS: Record<Role, Permission[]> = {\r\n  [ROLES.EMPEROR]: Object.values(PERMISSIONS),\r\n  [ROLES.DUKE]: [\r\n    PERMISSIONS.MANAGE_EMAIL,\r\n    PERMISSIONS.MANAGE_WEBHOOK,\r\n    PERMISSIONS.MANAGE_API_KEY,\r\n    PERMISSIONS.MANAGE_EMAIL_CREDENTIALS,\r\n  ],\r\n  [ROLES.KNIGHT]: [\r\n    PERMISSIONS.MANAGE_EMAIL,\r\n    PERMISSIONS.MANAGE_WEBHOOK,\r\n    PERMISSIONS.MANAGE_EMAIL_CREDENTIALS,\r\n  ],\r\n  [ROLES.CIVILIAN]: [\r\n    PERMISSIONS.MANAGE_EMAIL,\r\n    PERMISSIONS.MANAGE_EMAIL_CREDENTIALS,\r\n  ],\r\n} as const;\r\n\r\nexport function hasPermission(userRoles: Role[], permission: Permission): boolean {\r\n  return userRoles.some(role => ROLE_PERMISSIONS[role]?.includes(permission));\r\n} ", "import { integer, sqliteTable, text, primaryKey, uniqueIndex, index } from \"drizzle-orm/sqlite-core\"\r\nimport type { AdapterAccountType } from \"next-auth/adapters\"\r\nimport { relations } from 'drizzle-orm';\r\n\r\n// https://authjs.dev/getting-started/adapters/drizzle\r\nexport const users = sqliteTable(\"user\", {\r\n  id: text(\"id\")\r\n    .primaryKey()\r\n    .$defaultFn(() => crypto.randomUUID()),\r\n  name: text(\"name\"),\r\n  email: text(\"email\").unique(),\r\n  emailVerified: integer(\"emailVerified\", { mode: \"timestamp_ms\" }),\r\n  image: text(\"image\"),\r\n  username: text(\"username\").unique(),\r\n  password: text(\"password\"),\r\n})\r\nexport const accounts = sqliteTable(\r\n  \"account\",\r\n  {\r\n    userId: text(\"userId\")\r\n      .notNull()\r\n      .references(() => users.id, { onDelete: \"cascade\" }),\r\n    type: text(\"type\").$type<AdapterAccountType>().notNull(),\r\n    provider: text(\"provider\").notNull(),\r\n    providerAccountId: text(\"providerAccountId\").notNull(),\r\n    refresh_token: text(\"refresh_token\"),\r\n    access_token: text(\"access_token\"),\r\n    expires_at: integer(\"expires_at\"),\r\n    token_type: text(\"token_type\"),\r\n    scope: text(\"scope\"),\r\n    id_token: text(\"id_token\"),\r\n    session_state: text(\"session_state\"),\r\n  },\r\n  (account) => ({\r\n    compoundKey: primaryKey({\r\n      columns: [account.provider, account.providerAccountId],\r\n    }),\r\n  })\r\n)\r\n\r\nexport const emails = sqliteTable(\"email\", {\r\n  id: text(\"id\").primaryKey().$defaultFn(() => crypto.randomUUID()),\r\n  address: text(\"address\").notNull().unique(),\r\n  userId: text(\"userId\").references(() => users.id, { onDelete: \"cascade\" }),\r\n  createdAt: integer(\"created_at\", { mode: \"timestamp_ms\" })\r\n    .notNull()\r\n    .$defaultFn(() => new Date()),\r\n  expiresAt: integer(\"expires_at\", { mode: \"timestamp_ms\" }).notNull(),\r\n}, (table) => ({\r\n  expiresAtIdx: index(\"email_expires_at_idx\").on(table.expiresAt),\r\n}))\r\n\r\nexport const messages = sqliteTable(\"message\", {\r\n  id: text(\"id\").primaryKey().$defaultFn(() => crypto.randomUUID()),\r\n  emailId: text(\"emailId\")\r\n    .notNull()\r\n    .references(() => emails.id, { onDelete: \"cascade\" }),\r\n  fromAddress: text(\"from_address\").notNull(),\r\n  subject: text(\"subject\").notNull(),\r\n  content: text(\"content\").notNull(),\r\n  html: text(\"html\"),\r\n  receivedAt: integer(\"received_at\", { mode: \"timestamp_ms\" })\r\n    .notNull()\r\n    .$defaultFn(() => new Date()),\r\n}, (table) => ({\r\n  emailIdIdx: index(\"message_email_id_idx\").on(table.emailId),\r\n}))\r\n\r\nexport const webhooks = sqliteTable('webhook', {\r\n  id: text('id').primaryKey().$defaultFn(() => crypto.randomUUID()),\r\n  userId: text('user_id')\r\n    .notNull()\r\n    .references(() => users.id, { onDelete: \"cascade\" }),\r\n  url: text('url').notNull(),\r\n  enabled: integer('enabled', { mode: 'boolean' }).notNull().default(true),\r\n  createdAt: integer('created_at', { mode: 'timestamp_ms' })\r\n    .notNull()\r\n    .$defaultFn(() => new Date()),\r\n  updatedAt: integer('updated_at', { mode: 'timestamp_ms' })\r\n    .notNull()\r\n    .$defaultFn(() => new Date()),\r\n})\r\n\r\nexport const roles = sqliteTable(\"role\", {\r\n  id: text(\"id\").primaryKey().$defaultFn(() => crypto.randomUUID()),\r\n  name: text(\"name\").notNull(),\r\n  description: text(\"description\"),\r\n  createdAt: integer(\"created_at\", { mode: \"timestamp\" }).$defaultFn(() => new Date()),\r\n  updatedAt: integer(\"updated_at\", { mode: \"timestamp\" }).$defaultFn(() => new Date()),\r\n});\r\n\r\nexport const userRoles = sqliteTable(\"user_role\", {\r\n  userId: text(\"user_id\").notNull().references(() => users.id, { onDelete: \"cascade\" }),\r\n  roleId: text(\"role_id\").notNull().references(() => roles.id, { onDelete: \"cascade\" }),\r\n  createdAt: integer(\"created_at\", { mode: \"timestamp\" }).$defaultFn(() => new Date()),\r\n}, (table) => ({\r\n  pk: primaryKey({ columns: [table.userId, table.roleId] }),\r\n}));\r\n\r\nexport const apiKeys = sqliteTable('api_keys', {\r\n  id: text('id').primaryKey().$defaultFn(() => crypto.randomUUID()),\r\n  userId: text('user_id').notNull().references(() => users.id),\r\n  name: text('name').notNull(),\r\n  key: text('key').notNull().unique(),\r\n  createdAt: integer('created_at', { mode: 'timestamp' }).$defaultFn(() => new Date()),\r\n  expiresAt: integer('expires_at', { mode: 'timestamp' }),\r\n  enabled: integer('enabled', { mode: 'boolean' }).notNull().default(true),\r\n}, (table) => ({\r\n  nameUserIdUnique: uniqueIndex('name_user_id_unique').on(table.name, table.userId)\r\n}));\r\n\r\nexport const apiKeysRelations = relations(apiKeys, ({ one }) => ({\r\n  user: one(users, {\r\n    fields: [apiKeys.userId],\r\n    references: [users.id],\r\n  }),\r\n}));\r\n\r\n// 邮箱凭证表（系统内部邮箱的JWT凭证）\r\nexport const emailCredentials = sqliteTable('email_credentials', {\r\n  id: text('id').primaryKey().$defaultFn(() => crypto.randomUUID()),\r\n  userId: text('user_id').notNull().references(() => users.id, { onDelete: 'cascade' }),\r\n  emailAddress: text('email_address').notNull(), // 邮箱地址\r\n  name: text('name').notNull(), // 凭证名称\r\n  jwt: text('jwt').notNull(), // JWT凭证\r\n  enabled: integer('enabled', { mode: 'boolean' }).notNull().default(true),\r\n  createdAt: integer('created_at', { mode: 'timestamp' }).$defaultFn(() => new Date()),\r\n  expiresAt: integer('expires_at', { mode: 'timestamp' }),\r\n  lastUsedAt: integer('last_used_at', { mode: 'timestamp' }),\r\n}, (table) => ({\r\n  emailUnique: uniqueIndex('email_address_unique').on(table.emailAddress),\r\n  nameUserUnique: uniqueIndex('email_credential_name_user_id_unique').on(table.name, table.userId),\r\n}));\r\n\r\nexport const emailCredentialsRelations = relations(emailCredentials, ({ many }) => ({\r\n  bindings: many(userEmailBindings),\r\n}));\r\n\r\n// 用户邮箱绑定表（用户通过邮箱凭证绑定邮箱）\r\nexport const userEmailBindings = sqliteTable('user_email_bindings', {\r\n  id: text('id').primaryKey().$defaultFn(() => crypto.randomUUID()),\r\n  userId: text('user_id').notNull().references(() => users.id, { onDelete: 'cascade' }),\r\n  emailId: text('email_id').notNull().references(() => emails.id, { onDelete: 'cascade' }),\r\n  credentialId: text('credential_id').references(() => emailCredentials.id, { onDelete: 'set null' }),\r\n  createdAt: integer('created_at', { mode: 'timestamp' }).$defaultFn(() => new Date()),\r\n}, (table) => ({\r\n  userEmailUnique: uniqueIndex('user_email_binding_unique').on(table.userId, table.emailId)\r\n}));\r\n\r\nexport const userEmailBindingsRelations = relations(userEmailBindings, ({ one }) => ({\r\n  user: one(users, {\r\n    fields: [userEmailBindings.userId],\r\n    references: [users.id],\r\n  }),\r\n  email: one(emails, {\r\n    fields: [userEmailBindings.emailId],\r\n    references: [emails.id],\r\n  }),\r\n  credential: one(emailCredentials, {\r\n    fields: [userEmailBindings.credentialId],\r\n    references: [emailCredentials.id],\r\n  }),\r\n}));\r\n\r\n// 邮箱验证码表\r\nexport const emailVerificationCodes = sqliteTable('email_verification_codes', {\r\n  id: text('id').primaryKey().$defaultFn(() => crypto.randomUUID()),\r\n  email: text('email').notNull(),\r\n  code: text('code').notNull(),\r\n  createdAt: integer('created_at', { mode: 'timestamp' }).$defaultFn(() => new Date()),\r\n  expiresAt: integer('expires_at', { mode: 'timestamp' }).notNull(),\r\n  used: integer('used', { mode: 'boolean' }).notNull().default(false),\r\n}, (table) => ({\r\n  emailIdx: index('email_verification_email_idx').on(table.email),\r\n  expiresAtIdx: index('email_verification_expires_at_idx').on(table.expiresAt),\r\n}));\r\n\r\nexport const userRolesRelations = relations(userRoles, ({ one }) => ({\r\n  user: one(users, {\r\n    fields: [userRoles.userId],\r\n    references: [users.id],\r\n  }),\r\n  role: one(roles, {\r\n    fields: [userRoles.roleId],\r\n    references: [roles.id],\r\n  }),\r\n}));\r\n\r\nexport const usersRelations = relations(users, ({ many }) => ({\r\n  userRoles: many(userRoles),\r\n  apiKeys: many(apiKeys),\r\n}));\r\n\r\nexport const rolesRelations = relations(roles, ({ many }) => ({\r\n  userRoles: many(userRoles),\r\n}));", "import { type ClassValue, clsx } from \"clsx\"\r\nimport { twMerge } from \"tailwind-merge\"\r\n\r\nexport function cn(...inputs: ClassValue[]) {\r\n  return twMerge(clsx(inputs))\r\n}\r\n\r\nexport async function hashPassword(password: string): Promise<string> {\r\n  const encoder = new TextEncoder()\r\n  const salt = process.env.AUTH_SECRET || ''\r\n  const data = encoder.encode(password + salt)\r\n  const hash = await crypto.subtle.digest('SHA-256', data)\r\n  return btoa(String.fromCharCode(...new Uint8Array(hash)))\r\n}\r\n\r\nexport async function comparePassword(password: string, hashedPassword: string): Promise<boolean> {\r\n  const hash = await hashPassword(password)\r\n  return hash === hashedPassword\r\n}", "import { z } from \"zod\"\r\n\r\nexport const authSchema = z.object({\r\n  username: z.string()\r\n    .min(1, \"用户名不能为空\")\r\n    .max(20, \"用户名不能超过20个字符\")\r\n    .regex(/^[a-zA-Z0-9_-]+$/, \"用户名只能包含字母、数字、下划线和横杠\")\r\n    .refine(val => !val.includes('@'), \"用户名不能是邮箱格式\"),\r\n  password: z.string()\r\n    .min(8, \"密码长度必须大于等于8位\")\r\n})\r\n\r\nexport type AuthSchema = z.infer<typeof authSchema>"], "names": ["getUserId", "userId", "headersList", "headers", "get", "session", "auth", "user", "id", "COLORS", "ROLE_DESCRIPTIONS", "ROLES", "EMPEROR", "DUKE", "KNIGHT", "CIVILIAN", "getDefaultRole", "getRequestContext", "env", "SITE_CONFIG", "findOrCreateRole", "db", "<PERSON><PERSON><PERSON>", "role", "query", "roles", "<PERSON><PERSON><PERSON><PERSON>", "where", "eq", "name", "newRole", "insert", "values", "description", "returning", "assignRoleToUser", "roleId", "delete", "userRoles", "getUserRole", "createDb", "userRoleRecords", "find<PERSON>any", "with", "checkPermission", "permission", "userRoleNames", "map", "ur", "hasPermission", "handlers", "GET", "POST", "signIn", "signOut", "NextAuth", "secret", "process", "AUTH_SECRET", "adapter", "DrizzleAdapter", "usersTable", "users", "accountsTable", "accounts", "providers", "GitHub", "clientId", "AUTH_GITHUB_ID", "clientSecret", "AUTH_GITHUB_SECRET", "CredentialsProvider", "credentials", "username", "label", "type", "placeholder", "password", "authorize", "authSchema", "parse", "error", "comparePassword", "Error", "undefined", "events", "existingRole", "defaultRole", "console", "callbacks", "jwt", "token", "image", "generateAvatarUrl", "initial", "toUpperCase", "colorIndex", "Array", "from", "reduce", "acc", "char", "charCodeAt", "length", "backgroundColor", "svg", "trim", "bytes", "TextEncoder", "encode", "base64", "<PERSON><PERSON><PERSON>", "toString", "createdAt", "Date", "strategy", "register", "existing", "hashedPassword", "hashPassword", "drizzle", "DB", "schema", "MANAGE_EMAIL", "MANAGE_WEBHOOK", "PROMOTE_USER", "MANAGE_CONFIG", "MANAGE_API_KEY", "MANAGE_EMAIL_CREDENTIALS", "Object", "PERMISSIONS", "some", "ROLE_PERMISSIONS", "includes", "sqliteTable", "text", "<PERSON><PERSON><PERSON>", "$defaultFn", "crypto", "randomUUID", "email", "unique", "emailVerified", "integer", "mode", "notNull", "references", "onDelete", "$type", "provider", "providerAccountId", "refresh_token", "access_token", "expires_at", "token_type", "scope", "id_token", "session_state", "<PERSON><PERSON><PERSON>", "columns", "account", "emails", "address", "expiresAt", "expiresAtIdx", "index", "on", "table", "emailId", "fromAddress", "subject", "content", "html", "receivedAt", "emailIdIdx", "webhooks", "url", "enabled", "default", "updatedAt", "pk", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "key", "nameUserIdUnique", "uniqueIndex", "apiKeysRelations", "relations", "one", "fields", "emailCredentials", "emailAddress", "lastUsedAt", "emailUnique", "nameUserUnique", "emailCredentialsRelations", "many", "bindings", "userEmailBindings", "credentialId", "userEmailUnique", "userEmailBindingsRelations", "credential", "emailVerificationCodes", "code", "used", "emailIdx", "userRolesRelations", "rolesRelations", "cn", "inputs", "twMerge", "clsx", "encoder", "salt", "data", "btoa", "String", "fromCharCode", "Uint8Array", "subtle", "digest", "z", "object", "string", "min", "max", "regex", "refine", "val"], "sourceRoot": "", "ignoreList": []}