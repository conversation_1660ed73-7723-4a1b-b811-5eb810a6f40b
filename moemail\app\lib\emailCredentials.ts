import { createDb } from './db'
import { emailCredentials, userEmailBindings, emails, users } from './schema'
import { eq, and, desc, count, ne } from 'drizzle-orm'
import { JWTUtils } from './jwt'
import { nanoid } from 'nanoid'

export interface EmailCredentialInfo {
  id: string
  userId: string
  emailAddress: string
  name: string
  jwt: string
  enabled: boolean
  createdAt: Date
  expiresAt?: Date | null
  lastUsedAt: Date | null
  bindingCount?: number // 绑定的用户数量（不包括管理员）
}

export interface UserEmailBinding {
  id: string
  userId: string
  emailId: string
  emailAddress?: string // 从关联查询中获取
  credentialId?: string | null
  createdAt: Date
  enabled?: boolean // 虚拟字段，默认为 true
}

export class EmailCredentialManager {
  // 为邮箱创建凭证（邮箱创建时自动调用）
  static async createCredentialForEmail(emailAddress: string, userId: string, name?: string): Promise<EmailCredentialInfo> {
    const db = createDb()

    // 检查邮箱是否已存在凭证
    const existingCredential = await db.query.emailCredentials.findFirst({
      where: eq(emailCredentials.emailAddress, emailAddress)
    })

    if (existingCredential) {
      return existingCredential as EmailCredentialInfo
    }

    // 查找对应的邮箱记录，并验证是否属于当前用户
    const emailRecord = await db.query.emails.findFirst({
      where: and(
        eq(emails.address, emailAddress),
        eq(emails.userId, userId)
      )
    })

    if (!emailRecord) {
      throw new Error('邮箱地址不存在或不属于当前用户')
    }

    // 生成JWT
    const credentialId = crypto.randomUUID()
    const jwt = await JWTUtils.signEmailCredential({
      emailAddress,
      emailId: emailRecord.id,
      credentialId
    })

    // 创建凭证记录
    const [credential] = await db.insert(emailCredentials).values({
      id: credentialId,
      userId,
      emailAddress,
      name: name || `${emailAddress} 的凭证`,
      jwt,
      enabled: true,
      createdAt: new Date(),
    }).returning()

    return credential as EmailCredentialInfo
  }

  // 获取所有邮箱凭证（管理员用）
  static async getAllCredentials(): Promise<EmailCredentialInfo[]> {
    const db = createDb()

    const credentials = await db.query.emailCredentials.findMany({
      orderBy: desc(emailCredentials.createdAt)
    })

    // 为每个凭证计算绑定用户数量（不包括管理员）
    const credentialsWithCount = await Promise.all(
      credentials.map(async (credential) => {
        const bindingCount = await this.getBindingCount(credential.emailAddress)
        return {
          ...credential,
          bindingCount
        } as EmailCredentialInfo
      })
    )

    return credentialsWithCount
  }

  // 根据邮箱地址获取凭证
  static async getCredentialByEmail(emailAddress: string): Promise<EmailCredentialInfo | null> {
    const db = createDb()

    const credential = await db.query.emailCredentials.findFirst({
      where: eq(emailCredentials.emailAddress, emailAddress)
    })

    if (!credential) {
      return null
    }

    const bindingCount = await this.getBindingCount(emailAddress)

    return {
      ...credential,
      bindingCount
    } as EmailCredentialInfo
  }

  // 获取邮箱的绑定用户数量（不包括管理员）
  static async getBindingCount(emailAddress: string): Promise<number> {
    const db = createDb()

    const result = await db
      .select({ count: count() })
      .from(userEmailBindings)
      .leftJoin(users, eq(userEmailBindings.userId, users.id))
      .where(
        and(
          eq(userEmailBindings.emailAddress, emailAddress),
          eq(userEmailBindings.enabled, true),
          ne(users.role, 'admin') // 排除管理员
        )
      )

    return result[0]?.count || 0
  }

  // 验证JWT凭证
  static async verifyCredential(token: string): Promise<EmailCredentialInfo | null> {
    const payload = await JWTUtils.verifyEmailCredential(token)
    if (!payload) {
      return null
    }

    const db = createDb()

    // 获取凭证信息
    const credential = await db.query.emailCredentials.findFirst({
      where: and(
        eq(emailCredentials.id, payload.credentialId),
        eq(emailCredentials.enabled, true)
      )
    })

    if (!credential) {
      return null
    }

    // 更新最后使用时间
    await db.update(emailCredentials)
      .set({ lastUsedAt: new Date() })
      .where(eq(emailCredentials.id, credential.id))

    return credential as EmailCredentialInfo
  }

  // 用户通过邮箱凭证绑定邮箱
  static async bindEmailByCredential(userId: string, jwt: string): Promise<UserEmailBinding> {
    const db = createDb()

    // 验证JWT凭证
    const credential = await this.verifyCredential(jwt)
    if (!credential) {
      throw new Error('无效的邮箱凭证')
    }

    // 查找对应的邮箱记录
    const emailRecord = await db.query.emails.findFirst({
      where: eq(emails.address, credential.emailAddress)
    })

    if (!emailRecord) {
      throw new Error('邮箱记录不存在')
    }

    // 检查用户是否已绑定此邮箱
    const existingBinding = await db.query.userEmailBindings.findFirst({
      where: and(
        eq(userEmailBindings.userId, userId),
        eq(userEmailBindings.emailId, emailRecord.id)
      )
    })

    if (existingBinding) {
      throw new Error('您已绑定此邮箱')
    }

    // 创建绑定记录
    const [binding] = await db.insert(userEmailBindings).values({
      id: crypto.randomUUID(),
      userId,
      emailId: emailRecord.id,
      credentialId: credential.id,
      createdAt: new Date(),
    }).returning()

    return {
      ...binding,
      emailAddress: credential.emailAddress,
      enabled: true
    } as UserEmailBinding
  }

  // 获取用户绑定的邮箱列表
  static async getUserBindings(userId: string): Promise<UserEmailBinding[]> {
    const db = createDb()

    const bindings = await db.query.userEmailBindings.findMany({
      where: eq(userEmailBindings.userId, userId),
      orderBy: desc(userEmailBindings.createdAt),
      with: {
        email: true // 关联查询邮箱信息
      }
    })

    return bindings.map(binding => ({
      ...binding,
      emailAddress: binding.email?.address,
      enabled: true // 默认启用
    })) as UserEmailBinding[]
  }

  // 解绑邮箱
  static async unbindEmail(userId: string, emailAddress: string): Promise<boolean> {
    const db = createDb()

    // 先查找邮箱记录
    const emailRecord = await db.query.emails.findFirst({
      where: eq(emails.address, emailAddress)
    })

    if (!emailRecord) {
      return false
    }

    const result = await db.delete(userEmailBindings)
      .where(and(
        eq(userEmailBindings.userId, userId),
        eq(userEmailBindings.emailId, emailRecord.id)
      ))

    return result.changes > 0
  }

  // 根据ID获取凭证
  static async getCredentialById(credentialId: string): Promise<EmailCredentialInfo | null> {
    const db = createDb()

    const credential = await db.query.emailCredentials.findFirst({
      where: eq(emailCredentials.id, credentialId)
    })

    return credential as EmailCredentialInfo | null
  }
}


