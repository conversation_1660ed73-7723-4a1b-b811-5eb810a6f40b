import { auth } from "@/lib/auth"
import { NextResponse } from "next/server"
import { checkPermission } from "@/lib/auth"
import { PERMISSIONS } from "@/lib/permissions"
import { EmailCredentialManager } from "@/lib/emailCredentials"
import { z } from "zod"

export const runtime = "edge"

// 提取邮箱凭证的请求体验证
const extractCredentialSchema = z.object({
  emailAddress: z.string().email("无效的邮箱地址").optional(),
  emailAddresses: z.array(z.string().email("无效的邮箱地址")).optional(),
}).refine(data => data.emailAddress || (data.emailAddresses && data.emailAddresses.length > 0), {
  message: "必须提供邮箱地址或邮箱地址列表"
})

// 提取邮箱凭证（管理员）
export async function POST(request: Request) {
  const hasPermission = await checkPermission(PERMISSIONS.MANAGE_WEBHOOK)
  if (!hasPermission) {
    return NextResponse.json({ error: "权限不足" }, { status: 403 })
  }

  const session = await auth()
  if (!session?.user?.id) {
    return NextResponse.json({ error: "未授权" }, { status: 401 })
  }

  try {
    const body = await request.json()
    const validatedData = extractCredentialSchema.parse(body)

    // 处理单个邮箱地址
    if (validatedData.emailAddress) {
      const credential = await EmailCredentialManager.getCredentialByEmail(
        validatedData.emailAddress
      )

      if (!credential) {
        return NextResponse.json(
          { error: "邮箱凭证不存在" },
          { status: 404 }
        )
      }

      return NextResponse.json({
        success: true,
        credential: {
          id: credential.id,
          emailAddress: credential.emailAddress,
          jwt: credential.jwt,
          enabled: credential.enabled,
          createdAt: credential.createdAt.toISOString(),
          lastUsedAt: credential.lastUsedAt?.toISOString() || null,
          bindingCount: credential.bindingCount || 0
        }
      })
    }

    // 处理多个邮箱地址
    if (validatedData.emailAddresses) {
      const credentials = []
      for (const emailAddress of validatedData.emailAddresses) {
        const credential = await EmailCredentialManager.getCredentialByEmail(emailAddress)
        if (credential) {
          credentials.push({
            id: credential.id,
            emailAddress: credential.emailAddress,
            jwt: credential.jwt,
            enabled: credential.enabled,
            createdAt: credential.createdAt.toISOString(),
            lastUsedAt: credential.lastUsedAt?.toISOString() || null,
            bindingCount: credential.bindingCount || 0
          })
        }
      }

      return NextResponse.json({
        success: true,
        credentials
      })
    }

    return NextResponse.json(
      { error: "无效的请求参数" },
      { status: 400 }
    )
  } catch (error) {
    console.error("Failed to extract email credential:", error)

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: error.errors[0].message },
        { status: 400 }
      )
    }

    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: "提取邮箱凭证失败" },
      { status: 500 }
    )
  }
}
