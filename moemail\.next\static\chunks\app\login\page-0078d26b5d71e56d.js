(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[520],{6515:(e,s,t)=>{Promise.resolve().then(t.bind(t,7447))},7447:(e,s,t)=>{"use strict";t.d(s,{LoginForm:()=>S});var r=t(5155),a=t(2115),i=t(4822),l=t(7762),n=t(9393),d=t(9367),c=t(2558);let o=a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,r.jsx)("div",{ref:s,className:(0,c.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",t),...a})});o.displayName="Card";let u=a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,r.jsx)("div",{ref:s,className:(0,c.cn)("flex flex-col space-y-1.5 p-6",t),...a})});u.displayName="CardHeader";let m=a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,r.jsx)("h3",{ref:s,className:(0,c.cn)("text-2xl font-semibold leading-none tracking-tight",t),...a})});m.displayName="CardTitle";let p=a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,r.jsx)("p",{ref:s,className:(0,c.cn)("text-sm text-muted-foreground",t),...a})});p.displayName="CardDescription";let f=a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,r.jsx)("div",{ref:s,className:(0,c.cn)("p-6 pt-0",t),...a})});f.displayName="CardContent",a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,r.jsx)("div",{ref:s,className:(0,c.cn)("flex items-center p-6 pt-0",t),...a})}).displayName="CardFooter";var x=t(1420);let v=x.bL,h=a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,r.jsx)(x.B8,{ref:s,className:(0,c.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",t),...a})});h.displayName=x.B8.displayName;let g=a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,r.jsx)(x.l9,{ref:s,className:(0,c.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",t),...a})});g.displayName=x.l9.displayName;let b=a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,r.jsx)(x.UC,{ref:s,className:(0,c.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",t),...a})});b.displayName=x.UC.displayName;var N=t(2699),j=t(6693),w=t(4505),y=t(1626);function S(){let[e,s]=(0,a.useState)(""),[t,x]=(0,a.useState)(""),[S,A]=(0,a.useState)(""),[T,C]=(0,a.useState)(!1),[O,k]=(0,a.useState)({}),{toast:_}=(0,l.dj)(),E=()=>{let s={};return e||(s.username="请输入用户名"),t||(s.password="请输入密码"),e.includes("@")&&(s.username="用户名不能包含 @ 符号"),t&&t.length<8&&(s.password="密码长度必须大于等于8位"),k(s),0===Object.keys(s).length},R=()=>{let s={};return e||(s.username="请输入用户名"),t||(s.password="请输入密码"),e.includes("@")&&(s.username="用户名不能包含 @ 符号"),t&&t.length<8&&(s.password="密码长度必须大于等于8位"),S||(s.confirmPassword="请确认密码"),t!==S&&(s.confirmPassword="两次输入的密码不一致"),k(s),0===Object.keys(s).length},D=async()=>{if(E()){C(!0);try{let s=await (0,i.Jv)("credentials",{username:e,password:t,redirect:!1});if(null==s?void 0:s.error){_({title:"登录失败",description:"用户名或密码错误",variant:"destructive"}),C(!1);return}window.location.href="/"}catch(e){_({title:"登录失败",description:e instanceof Error?e.message:"请稍后重试",variant:"destructive"}),C(!1)}}},I=async()=>{if(R()){C(!0);try{let s=await fetch("/api/auth/register",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({username:e,password:t})}),r=await s.json();if(!s.ok){_({title:"注册失败",description:r.error||"请稍后重试",variant:"destructive"}),C(!1);return}let a=await (0,i.Jv)("credentials",{username:e,password:t,redirect:!1});if(null==a?void 0:a.error){_({title:"登录失败",description:"自动登录失败，请手动登录",variant:"destructive"}),C(!1);return}window.location.href="/"}catch(e){_({title:"注册失败",description:e instanceof Error?e.message:"请稍后重试",variant:"destructive"}),C(!1)}}};return(0,r.jsxs)(o,{className:"w-[95%] max-w-lg border-2 border-primary/20",children:[(0,r.jsxs)(u,{className:"space-y-2",children:[(0,r.jsx)(m,{className:"text-2xl text-center bg-gradient-to-r from-primary to-purple-600 bg-clip-text text-transparent",children:"欢迎使用 MoeMail"}),(0,r.jsx)(p,{className:"text-center",children:"萌萌哒临时邮箱服务 (。・∀・)ノ"})]}),(0,r.jsx)(f,{className:"px-6",children:(0,r.jsxs)(v,{defaultValue:"login",className:"w-full",onValueChange:()=>{s(""),x(""),A(""),k({})},children:[(0,r.jsxs)(h,{className:"grid w-full grid-cols-2 mb-6",children:[(0,r.jsx)(g,{value:"login",children:"登录"}),(0,r.jsx)(g,{value:"register",children:"注册"})]}),(0,r.jsxs)("div",{className:"min-h-[220px]",children:[(0,r.jsxs)(b,{value:"login",className:"space-y-4 mt-0",children:[(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{className:"space-y-1.5",children:[(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("div",{className:"absolute left-2.5 top-2 text-muted-foreground",children:(0,r.jsx)(N.A,{className:"h-5 w-5"})}),(0,r.jsx)(d.p,{className:(0,c.cn)("h-9 pl-9 pr-3",O.username&&"border-destructive focus-visible:ring-destructive"),placeholder:"用户名",value:e,onChange:e=>{s(e.target.value),k({})},disabled:T})]}),O.username&&(0,r.jsx)("p",{className:"text-xs text-destructive",children:O.username})]}),(0,r.jsxs)("div",{className:"space-y-1.5",children:[(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("div",{className:"absolute left-2.5 top-2 text-muted-foreground",children:(0,r.jsx)(j.A,{className:"h-5 w-5"})}),(0,r.jsx)(d.p,{className:(0,c.cn)("h-9 pl-9 pr-3",O.password&&"border-destructive focus-visible:ring-destructive"),type:"password",placeholder:"密码",value:t,onChange:e=>{x(e.target.value),k({})},disabled:T})]}),O.password&&(0,r.jsx)("p",{className:"text-xs text-destructive",children:O.password})]})]}),(0,r.jsxs)("div",{className:"space-y-3 pt-1",children:[(0,r.jsxs)(n.$,{className:"w-full",onClick:D,disabled:T,children:[T&&(0,r.jsx)(w.A,{className:"mr-2 h-4 w-4 animate-spin"}),"登录"]}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("div",{className:"absolute inset-0 flex items-center",children:(0,r.jsx)("span",{className:"w-full border-t"})}),(0,r.jsx)("div",{className:"relative flex justify-center text-xs uppercase",children:(0,r.jsx)("span",{className:"bg-background px-2 text-muted-foreground",children:"或者"})})]}),(0,r.jsxs)(n.$,{variant:"outline",className:"w-full",onClick:()=>{(0,i.Jv)("github",{callbackUrl:"/"})},children:[(0,r.jsx)(y.A,{className:"mr-2 h-4 w-4"}),"使用 GitHub 账号登录"]})]})]}),(0,r.jsxs)(b,{value:"register",className:"space-y-4 mt-0",children:[(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{className:"space-y-1.5",children:[(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("div",{className:"absolute left-2.5 top-2 text-muted-foreground",children:(0,r.jsx)(N.A,{className:"h-5 w-5"})}),(0,r.jsx)(d.p,{className:(0,c.cn)("h-9 pl-9 pr-3",O.username&&"border-destructive focus-visible:ring-destructive"),placeholder:"用户名",value:e,onChange:e=>{s(e.target.value),k({})},disabled:T})]}),O.username&&(0,r.jsx)("p",{className:"text-xs text-destructive",children:O.username})]}),(0,r.jsxs)("div",{className:"space-y-1.5",children:[(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("div",{className:"absolute left-2.5 top-2 text-muted-foreground",children:(0,r.jsx)(j.A,{className:"h-5 w-5"})}),(0,r.jsx)(d.p,{className:(0,c.cn)("h-9 pl-9 pr-3",O.password&&"border-destructive focus-visible:ring-destructive"),type:"password",placeholder:"密码",value:t,onChange:e=>{x(e.target.value),k({})},disabled:T})]}),O.password&&(0,r.jsx)("p",{className:"text-xs text-destructive",children:O.password})]}),(0,r.jsxs)("div",{className:"space-y-1.5",children:[(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("div",{className:"absolute left-2.5 top-2 text-muted-foreground",children:(0,r.jsx)(j.A,{className:"h-5 w-5"})}),(0,r.jsx)(d.p,{className:(0,c.cn)("h-9 pl-9 pr-3",O.confirmPassword&&"border-destructive focus-visible:ring-destructive"),type:"password",placeholder:"确认密码",value:S,onChange:e=>{A(e.target.value),k({})},disabled:T})]}),O.confirmPassword&&(0,r.jsx)("p",{className:"text-xs text-destructive",children:O.confirmPassword})]})]}),(0,r.jsx)("div",{className:"space-y-3 pt-1",children:(0,r.jsxs)(n.$,{className:"w-full",onClick:I,disabled:T,children:[T&&(0,r.jsx)(w.A,{className:"mr-2 h-4 w-4 animate-spin"}),"注册"]})})]})]})]})})]})}},9393:(e,s,t)=>{"use strict";t.d(s,{$:()=>c,r:()=>d});var r=t(5155),a=t(2115),i=t(2317),l=t(1027),n=t(2558);let d=(0,l.F)("inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground shadow hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90",outline:"border border-input bg-transparent shadow-sm hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2",sm:"h-8 rounded-md px-3 text-xs",lg:"h-10 rounded-md px-8",icon:"h-9 w-9"}},defaultVariants:{variant:"default",size:"default"}}),c=a.forwardRef((e,s)=>{let{className:t,variant:a,size:l,asChild:c=!1,...o}=e,u=c?i.DX:"button";return(0,r.jsx)(u,{className:(0,n.cn)(d({variant:a,size:l,className:t})),ref:s,...o})});c.displayName="Button"},9367:(e,s,t)=>{"use strict";t.d(s,{p:()=>l});var r=t(5155),a=t(2115),i=t(2558);let l=a.forwardRef((e,s)=>{let{className:t,type:a,...l}=e;return(0,r.jsx)("input",{type:a,className:(0,i.cn)("flex h-9 w-full rounded-md border border-input bg-transparent px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50",t),ref:s,...l})});l.displayName="Input"},7762:(e,s,t)=>{"use strict";t.d(s,{dj:()=>m});var r=t(2115);let a=0,i=new Map,l=e=>{if(i.has(e))return;let s=setTimeout(()=>{i.delete(e),o({type:"REMOVE_TOAST",toastId:e})},1e6);i.set(e,s)},n=(e,s)=>{switch(s.type){case"ADD_TOAST":return{...e,toasts:[s.toast,...e.toasts].slice(0,1)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===s.toast.id?{...e,...s.toast}:e)};case"DISMISS_TOAST":{let{toastId:t}=s;return t?l(t):e.toasts.forEach(e=>{l(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===t||void 0===t?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===s.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==s.toastId)}}},d=[],c={toasts:[]};function o(e){c=n(c,e),d.forEach(e=>{e(c)})}function u(e){let{...s}=e,t=(a=(a+1)%Number.MAX_VALUE).toString(),r=()=>o({type:"DISMISS_TOAST",toastId:t});return o({type:"ADD_TOAST",toast:{...s,id:t,open:!0,onOpenChange:e=>{e||r()}}}),{id:t,dismiss:r,update:e=>o({type:"UPDATE_TOAST",toast:{...e,id:t}})}}function m(){let[e,s]=r.useState(c);return r.useEffect(()=>(d.push(s),()=>{let e=d.indexOf(s);e>-1&&d.splice(e,1)}),[e]),{...e,toast:u,dismiss:e=>o({type:"DISMISS_TOAST",toastId:e})}}},2558:(e,s,t)=>{"use strict";t.d(s,{cn:()=>i});var r=t(3463),a=t(9795);function i(){for(var e=arguments.length,s=Array(e),t=0;t<e;t++)s[t]=arguments[t];return(0,a.QP)((0,r.$)(s))}t(2818)}},e=>{var s=s=>e(e.s=s);e.O(0,[630,689,441,517,358],()=>s(6515)),_N_E=e.O()}]);