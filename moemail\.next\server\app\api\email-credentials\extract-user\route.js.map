{"version": 3, "file": "app/api/email-credentials/extract-user/route.js", "mappings": "sFAAA,8DCAA,oHGAA,iSFKO,IAAMA,EAAU,OAAM,EAGOC,EAAAA,CAACA,CAACC,MAAM,CAAC,CAC3CC,eAAgBF,EAAAA,CAACA,CAACG,KAAK,CAACH,EAAAA,CAACA,CAACI,MAAM,GAAGC,KAAK,CAAC,YAAYC,GAAG,CAAC,EAAG,WAC9D,GAGO,eAAeC,EAAKC,CAAgB,EACzC,IAAMC,EAAU,MAAMC,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,GAC1B,GAAI,CAACD,GAASE,MAAMC,GAClB,CADsB,MACfC,EAAAA,EAAYA,CAACC,IAAI,CAAC,CAAEC,MAAO,KAAM,EAAG,CAAEC,OAAQ,GAAI,GAG3D,GAAI,CACF,IAAMC,EAAO,MAAMT,EAAQM,IAAI,GACzBI,EAAgBC,EAA4BC,KAAK,CAACH,GAIlDI,EAAqBC,CADN,MAAMC,EAAAA,sBAAsBA,CAACC,eAAe,CAACf,EAAQE,IAAI,CAACC,GAAE,EACzCa,GAAG,CAACC,GAAWA,EAAQC,YAAY,EAGrEC,EAAgBV,EAAchB,cAAc,CAAC2B,MAAM,CACvDxB,GAAS,CAACgB,EAAmBS,QAAQ,CAACzB,IAGxC,GAAIuB,EAAcG,MAAM,CAAG,EACzB,CAD4B,MACrBlB,EAAAA,EAAYA,CAACC,IAAI,CACtB,CAAEC,MAAO,CAAC,gBAAgB,EAAEa,EAAcI,IAAI,CAAC,OAAQ,EACvD,CAAEhB,OAAQ,GAAI,GAKlB,IAAMiB,EAAc,EAAE,CACtB,IAAK,IAAMN,KAAgBT,EAAchB,cAAc,CAAE,CACvD,IAAMgC,EAAa,MAAMX,EAAAA,sBAAsBA,CAACY,oBAAoB,CAACR,GACjEO,GACFD,EAAYG,IAAI,CAAC,CACfxB,CAFY,EAERsB,EAAWtB,EAAE,CACjBe,aAAcO,EAAWP,YAAY,CACrCU,IAAKH,EAAWG,GAAG,CACnBC,QAASJ,EAAWI,OAAO,CAC3BC,UAAWL,EAAWK,SAAS,CAACC,WAAW,GAC3CC,WAAYP,EAAWO,UAAU,EAAED,eAAiB,KACpDE,aAAcR,EAAWQ,YAAY,EAAI,CAC3C,EAEJ,CAEA,OAAO7B,EAAAA,EAAYA,CAACC,IAAI,CAAC,CACvB6B,QAAS,eACTV,CACF,EACF,CAAE,MAAOlB,EAAO,CAGd,GAFA6B,QAAQ7B,KAAK,CAAC,4CAA6CA,GAEvDA,aAAiBf,EAAAA,CAACA,CAAC6C,QAAQ,CAC7B,CAD+B,MACxBhC,EAAAA,EAAYA,CAACC,IAAI,CACtB,CAAEC,MAAOA,EAAM+B,MAAM,CAAC,EAAE,CAACC,OAAO,EAChC,CAAE/B,OAAQ,GAAI,GAIlB,GAAID,aAAiBiC,MACnB,CAD0B,MACnBnC,EAAAA,EAAYA,CAACC,IAAI,CACtB,CAAEC,MAAOA,EAAMgC,OAAO,EACtB,CAAE/B,OAAQ,GAAI,GAIlB,OAAOH,EAAAA,EAAYA,CAACC,IAAI,CACtB,CAAEC,MAAO,UAAW,EACpB,CAAEC,OAAQ,GAAI,EAElB,CACF,CC3EA,UAAwB,qBAAmB,EAC3C,YACA,KAAc,GAAS,WACvB,iDACA,+CACA,iBACA,yDACA,CAAK,CACL,6GACA,iBAVA,GAWA,QAAY,EACZ,CAAC,EAID,kBAAQ,wCAAsD,EAC9D,aACA,MAAW,QAAW,EACtB,mBACA,sBACA,CAAK,CACL,CCpBA,0FACA,EAFA,4BAEA,4BACA,MACI,QAA8B,EAClC,iDACA,0BACA,wBACA,gBAAyB,OAAqB,EAC9C,uBACA,CAAS,CACT,CAAK,EAEE,MAAqB,EAC5B,EAAe,GAAsB,MAAM,EAAkB,CAC7D,QAD6D,GAf7D,CAAoB,MAAQ,OAcM,CAdN,CAAW,sBAA2B,aAAe,kDAAyD,uOAAsQ,2RAAuT,kBAAkB,QAAQ,uDAA2D,0DAA8D,iBAAsB,gBAAkB,sEAAgF,kBAAoB,uCAA6C,MAAQ,iBAAmB,2BAA+B,gHAAkI,uBAAyB,8FAA0G,aAAiB,WAAa,sEAA6E,uBAAuB,iCAAmC,QAAQ,EAAE,SAAW,oBAAsB,QAAQ,GAAG,4EAAgF,WAAa,SAAW,2CAAiD,UAAY,+BAAqC,UAAY,oCAA0C,QAAU,uCAA6C,OAAS,yCAA+C,QAAU,0CAAiD,MAAQ,+CAAsD,iBAAmB,ywBAA+5B,4CAA8C,kNAAqQ,qBAAyB,s+CAA0/C,oIAiB7wJ,CAAC,CAAC,EAAC,6KCJI,OAAMiC,EACX,OAAeC,WAAwB,CACrC,IAAMC,EAASC,QAAQC,GAAG,CAACC,UAAU,EAAIF,QAAQC,GAAG,CAACE,WAAW,CAChE,GAAI,CAACJ,EACH,MAAUH,MAAM,8DAElB,OAAO,IAAIQ,cAAcC,MAAM,CAACN,EAClC,CAGA,aAAaO,oBAAoBC,CAA4C,CAAmB,CAC9F,IAAMC,EAAMC,KAAKC,KAAK,CAACC,KAAKH,GAAG,GAAK,KAEpC,OAAO,MAAM,IAAII,EAAAA,CAAOA,CAAC,CACvB,GAAGL,CAAO,CACVM,IAAKL,CAEP,GACGM,kBAAkB,CAAC,CAAEC,IAAK,OAAQ,GAClCC,WAAW,CAACR,GAEZS,CADD,GACK,CAAC,IAAI,CAACnB,CADD,QACU,GACxB,CAGA,aAAaoB,SAASX,CAAyC,CAAmB,CAChF,IAAMC,EAAMC,KAAKC,KAAK,CAACC,KAAKH,GAAG,GAAK,KAC9BW,EAAMX,EAAO,KAAK,EAExB,GAF6B,IAEtB,CAF2B,GAAI,EAEzB,IAAII,EAAAA,CAAOA,CAFwB,CAG9C,GAAGL,CAAO,KACVY,EACAN,IAAKL,CACP,GACGM,kBAAkB,CAAC,CAAEC,IAAK,OAAQ,GAClCC,WAAW,CAACR,GACZY,iBAAiB,CAACD,GAClBF,IAAI,CAAC,IAAI,CAACnB,SAAS,GACxB,CAGA,aAAauB,sBAAsBC,CAAa,CAA0C,CACxF,GAAI,CACF,GAAM,SAAEf,CAAO,CAAE,CAAG,MAAMgB,CAAAA,EAAAA,EAAAA,CAAAA,CAASA,CAACD,EAAO,IAAI,CAACxB,SAAS,IAIzD,OAAOS,CACT,CAAE,MAAO5C,EAAO,CAEd,OADA6B,QAAQ7B,KAAK,CAAC,2BAA4BA,GACnC,IACT,CACF,CAGA,aAAa6D,WAAWF,CAAa,CAA+B,CAClE,GAAI,CACF,GAAM,SAAEf,CAAO,CAAE,CAAG,MAAMgB,CAAAA,EAAAA,EAAAA,CAAAA,CAASA,CAACD,EAAO,IAAI,CAACxB,SAAS,IAGnDU,EAAMC,KAAKC,KAAK,CAACC,KAAKH,GAAG,GAAK,KACpC,GAAID,EAAQY,GAAG,EAAIZ,EAAQY,GAAG,CAAGX,EAC/B,GADoC,IAC7B,KAGT,OAAOD,CACT,CAAE,MAAO5C,EAAO,CAEd,OADA6B,QAAQ7B,KAAK,CAAC,2BAA4BA,GACnC,IACT,CACF,CAGA,OAAO8D,0BAAmC,CACxC,OAAOhB,KAAKC,KAAK,CAAC,IAAyB,IAAhBD,KAAKiB,MAAM,IAAaC,QAAQ,EAC7D,CACF,CClEO,MAAMxD,EAEX,aAAayD,yBAAyBrD,CAAoB,CAAEsD,CAAc,CAAEC,CAAa,CAAgC,CACvH,IAAMC,EAAKC,CAAAA,EAAAA,EAAAA,CAAAA,CAAQA,GAGbC,EAAqB,MAAMF,EAAGG,KAAK,CAACC,gBAAgB,CAACC,SAAS,CAAC,CACnEC,MAAOC,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACH,EAAAA,gBAAgBA,CAAC5D,YAAY,CAAEA,EAC3C,GAEA,GAAI0D,EACF,OAAOA,EAIT,IAAMM,EAAc,GALI,GAKER,EAAGG,KAAK,CAACM,MAAM,CAACJ,SAAS,CAAC,CAClDC,MAAOI,CAAAA,EAAAA,EAAAA,EAAAA,CAAGA,CACRH,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACE,EAAAA,MAAMA,CAACE,OAAO,CAAEnE,GACnB+D,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACE,EAAAA,MAAMA,CAACX,MAAM,CAAEA,GAEtB,GAEA,GAAI,CAACU,EACH,MAAM,KADU,CACA,mBAIlB,IAAMI,EAAeC,OAAOC,UAAU,GAChC5D,EAAM,MAAMY,EAASS,MAADT,aAAoB,CAAC,cAC7CtB,EACAuE,QAASP,EAAY/E,EAAE,cACvBmF,CACF,GAGM,CAAC7D,EAAW,CAAG,MAAMiD,EAAGgB,MAAM,CAACZ,EAAAA,gBAAgBA,EAAEa,MAAM,CAAC,CAC5DxF,GAAImF,SACJd,eACAtD,EACAuD,KAAMA,GAAQ,GAAGvD,EAAa,IAAI,CAAC,KACnCU,EACAC,SAAS,EACTC,UAAW,IAAIwB,IACjB,GAAGsC,SAAS,GAEZ,OAAOnE,CACT,CAGA,aAAaoE,mBAAoD,CAC/D,IAAMnB,EAAKC,CAAAA,EAAAA,EAAAA,CAAAA,CAAQA,GAEbnD,EAAc,MAAMkD,EAAGG,KAAK,CAACC,gBAAgB,CAACgB,QAAQ,CAAC,CAC3DC,QAASC,CAAAA,EAAAA,EAAAA,CAAAA,CAAIA,CAAClB,EAAAA,gBAAgBA,CAAChD,SAAS,CAC1C,GAaA,OAV6B,MAAMmE,QAAQC,GAAG,CAC5C1E,EAAYR,GAAG,CAAC,MAAOS,IACrB,IAAMQ,EAAe,MAAM,IAAI,CAACkE,eAAe,CAAC1E,EAAWP,YAAY,EACvE,MAAO,CACL,GAAGO,CAAU,cACbQ,CACF,CACF,GAIJ,CAGA,aAAaP,qBAAqBR,CAAoB,CAAuC,CAC3F,IAAMwD,EAAKC,CAAAA,EAAAA,EAAAA,CAAAA,CAAQA,GAEblD,EAAa,MAAMiD,EAAGG,KAAK,CAACC,gBAAgB,CAACC,SAAS,CAAC,CAC3DC,MAAOC,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACH,EAAAA,gBAAgBA,CAAC5D,YAAY,CAAEA,EAC3C,GAEA,GAAI,CAACO,EACH,OAAO,GADQ,EAIjB,IAAMQ,EAAe,MAAM,IAAI,CAACkE,eAAe,CAACjF,GAEhD,MAAO,CACL,GAAGO,CAAU,cACbQ,CACF,CACF,CAGA,aAAakE,gBAAgBjF,CAAoB,CAAmB,CAClE,IAAMwD,EAAKC,CAAAA,EAAAA,EAAAA,CAAAA,CAAQA,GAEbyB,EAAS,MAAM1B,EAClB2B,MAAM,CAAC,CAAEC,MAAOA,CAAAA,EAAAA,EAAAA,EAAAA,CAAKA,EAAG,GACxBC,IAAI,CAACC,EAAAA,iBAAiBA,EACtBC,QAAQ,CAACC,EAAAA,KAAKA,CAAEzB,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACuB,EAAAA,iBAAiBA,CAAChC,MAAM,CAAEkC,EAAAA,KAAKA,CAACvG,EAAE,GACrD6E,KAAK,CACJI,CAAAA,EAAAA,EAAAA,EAAAA,CAAGA,CACDH,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACuB,EAAAA,iBAAiBA,CAACtF,YAAY,CAAEA,GACnC+D,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACuB,EAAAA,iBAAiBA,CAAC3E,OAAO,EAAE,GAC9B8E,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACD,EAAAA,KAAKA,CAACE,IAAI,CAAE,SAAS,EAI9B,MAJsC,CAI/BR,CAAM,CAAC,EAAE,EAAEE,OAAS,CAC7B,CAGA,aAAaO,iBAAiB5C,CAAa,CAAuC,CAChF,IAAMf,EAAU,MAAMV,EAASwB,MAADxB,eAAsB,CAACyB,GACrD,GAAI,CAACf,EACH,OADY,KAId,IAAMwB,EAAKC,CAAAA,EAAAA,EAAAA,CAAAA,CAAQA,GAGblD,EAAa,MAAMiD,EAAGG,KAAK,CAACC,gBAAgB,CAACC,SAAS,CAAC,CAC3DC,MAAOI,CAAAA,EAAAA,EAAAA,EAAAA,CAAGA,CACRH,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACH,EAAAA,gBAAgBA,CAAC3E,EAAE,CAAE+C,EAAQoC,YAAY,EAC5CL,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACH,EAAAA,gBAAgBA,CAACjD,OAAO,EAAE,GAEjC,UAEA,GAKA,CALI,KAKE6C,EAAGoC,CALQ,KAKF,CAAChC,EAAAA,gBAAgBA,EAC7BiC,GAAG,CAAC,CAAE/E,WAAY,IAAIsB,IAAO,GAC7B0B,KAAK,CAACC,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACH,EAAAA,gBAAgBA,CAAC3E,EAAE,CAAEsB,EAAWtB,EAAE,GAEvCsB,GARE,IASX,CAGA,aAAauF,sBAAsBxC,CAAc,CAAE5C,CAAW,CAA6B,CACzF,IAAM8C,EAAKC,CAAAA,EAAAA,EAAAA,CAAAA,CAAQA,GAGblD,EAAa,MAAM,IAAI,CAACoF,gBAAgB,CAACjF,GAC/C,GAAI,CAACH,EACH,MAAM,IADS,EACC,WAIlB,IAAMyD,EAAc,MAAMR,EAAGG,KAAK,CAACM,MAAM,CAACJ,SAAS,CAAC,CAClDC,MAAOC,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACE,EAAAA,MAAMA,CAACE,OAAO,CAAE5D,EAAWP,YAAY,CACnD,GAEA,GAAI,CAACgE,EACH,MAAU3C,KADM,CACA,WAWlB,GAPwB,CAOpB0E,KAP0BvC,EAAGG,KAAK,CAAC2B,IAOlB,aAPmC,CAACzB,SAAS,CAAC,CACjEC,MAAOI,CAAAA,EAAAA,EAAAA,EAAAA,CAAGA,CACRH,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACuB,EAAAA,iBAAiBA,CAAChC,MAAM,CAAEA,GAC7BS,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACuB,EAAAA,iBAAiBA,CAACf,OAAO,CAAEP,EAAY/E,EAAE,EAEhD,GAGE,MAAUoC,MAAM,WAIlB,GAAM,CAACtB,EAAQ,CAAG,MAAMyD,EAAGgB,MAAM,CAACc,EAAAA,iBAAiBA,EAAEb,MAAM,CAAC,CAC1DxF,GAAIoF,OAAOC,UAAU,UACrBhB,EACAiB,QAASP,EAAY/E,EAAE,CACvBmF,aAAc7D,EAAWtB,EAAE,CAC3B2B,UAAW,IAAIwB,IACjB,GAAGsC,SAAS,GAEZ,MAAO,CACL,GAAG3E,CAAO,CACVC,aAAcO,EAAWP,YAAY,CACrCW,SAAS,CACX,CACF,CAGA,aAAad,gBAAgByD,CAAc,CAA+B,CACxE,IAAME,EAAKC,CAAAA,EAAAA,EAAAA,CAAAA,CAAQA,GAUnB,MAAOuC,CARU,MAAMxC,EAAGG,KAAK,CAAC2B,iBAAiB,CAACV,QAAQ,CAAC,CACzDd,MAAOC,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACuB,EAAAA,iBAAiBA,CAAChC,MAAM,CAAEA,GACpCuB,QAASC,CAAAA,EAAAA,EAAAA,CAAAA,CAAIA,CAACQ,EAAAA,iBAAiBA,CAAC1E,SAAS,EACzCqF,KAAM,CACJvH,OAAO,CACT,CACF,GAFgB,CAIAoB,GAAG,CAACC,GAAY,EAC9B,CALyB,EAKtBA,CAAO,CADoB,aAEhBA,EAAQrB,KAAK,EAAEyF,QAC7BxD,SAAS,EACX,EACF,CAFkB,OAAO,MAKZuF,YAAY5C,CAAc,CAAEtD,CAAoB,CAAoB,CAC/E,IAAMwD,EAAKC,CAAAA,EAAAA,EAAAA,CAAAA,CAAQA,GAGbO,EAAc,MAAMR,EAAGG,KAAK,CAACM,MAAM,CAACJ,SAAS,CAAC,CAClDC,MAAOC,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACE,EAAAA,MAAMA,CAACE,OAAO,CAAEnE,EAC5B,SAEA,EAAKgE,EAAD,CAUGkB,CANQ,MAAM1B,EAAG2C,CAJN,KAIY,CAACb,EAAAA,iBAAiBA,EAC7CxB,KAAK,CAACI,CAAAA,EAAAA,EAAAA,EAAAA,CAAGA,CACRH,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACuB,EAAAA,iBAAiBA,CAAChC,MAAM,CAAEA,GAC7BS,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACuB,EAAAA,iBAAiBA,CAACf,OAAO,CAAEP,EAAY/E,EAAE,KAGlCmH,OAAO,CAAG,CAC1B,CAGA,aAAaC,kBAAkBjC,CAAoB,CAAuC,CACxF,IAAMZ,EAAKC,CAAAA,EAAAA,EAAAA,CAAAA,CAAQA,GAMnB,OAJmB,MAAMD,EAAGG,KAAK,CAACC,gBAAgB,CAACC,SAAS,CAAC,CAC3DC,MAAOC,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACH,EAAAA,gBAAgBA,CAAC3E,EAAE,CAAEmF,EACjC,EAGF,CACF", "sources": ["webpack://_N_E/external commonjs \"node:async_hooks\"", "webpack://_N_E/external commonjs \"node:buffer\"", "webpack://_N_E/./app/api/email-credentials/extract-user/route.ts", "webpack://_N_E/./app/api/email-credentials/extract-user/route.ts?0117", "webpack://_N_E/?f054", "webpack://_N_E/./app/lib/jwt.ts", "webpack://_N_E/./app/lib/emailCredentials.ts"], "sourcesContent": ["module.exports = require(\"node:async_hooks\");", "module.exports = require(\"node:buffer\");", "import { auth } from \"@/lib/auth\"\nimport { NextResponse } from \"next/server\"\nimport { EmailCredentialManager } from \"@/lib/emailCredentials\"\nimport { z } from \"zod\"\n\nexport const runtime = \"edge\"\n\n// 用户提取自己绑定邮箱凭证的请求体验证\nconst extractUserCredentialSchema = z.object({\n  emailAddresses: z.array(z.string().email(\"无效的邮箱地址\")).min(1, \"至少选择一个邮箱\"),\n})\n\n// 用户提取自己绑定邮箱的凭证\nexport async function POST(request: Request) {\n  const session = await auth()\n  if (!session?.user?.id) {\n    return NextResponse.json({ error: \"未授权\" }, { status: 401 })\n  }\n\n  try {\n    const body = await request.json()\n    const validatedData = extractUserCredentialSchema.parse(body)\n\n    // 获取用户绑定的邮箱列表\n    const userBindings = await EmailCredentialManager.getUserBindings(session.user.id)\n    const userEmailAddresses = userBindings.map(binding => binding.emailAddress)\n\n    // 验证用户只能提取自己绑定的邮箱凭证\n    const invalidEmails = validatedData.emailAddresses.filter(\n      email => !userEmailAddresses.includes(email)\n    )\n\n    if (invalidEmails.length > 0) {\n      return NextResponse.json(\n        { error: `您没有权限提取以下邮箱的凭证: ${invalidEmails.join(', ')}` },\n        { status: 403 }\n      )\n    }\n\n    // 提取凭证\n    const credentials = []\n    for (const emailAddress of validatedData.emailAddresses) {\n      const credential = await EmailCredentialManager.getCredentialByEmail(emailAddress)\n      if (credential) {\n        credentials.push({\n          id: credential.id,\n          emailAddress: credential.emailAddress,\n          jwt: credential.jwt,\n          enabled: credential.enabled,\n          createdAt: credential.createdAt.toISOString(),\n          lastUsedAt: credential.lastUsedAt?.toISOString() || null,\n          bindingCount: credential.bindingCount || 0\n        })\n      }\n    }\n\n    return NextResponse.json({\n      success: true,\n      credentials\n    })\n  } catch (error) {\n    console.error(\"Failed to extract user email credentials:\", error)\n\n    if (error instanceof z.ZodError) {\n      return NextResponse.json(\n        { error: error.errors[0].message },\n        { status: 400 }\n      )\n    }\n\n    if (error instanceof Error) {\n      return NextResponse.json(\n        { error: error.message },\n        { status: 400 }\n      )\n    }\n\n    return NextResponse.json(\n      { error: \"提取邮箱凭证失败\" },\n      { status: 500 }\n    )\n  }\n}\n", "import { AppRouteRouteModule } from \"next/dist/server/route-modules/app-route/module.compiled\";\nimport { RouteKind } from \"next/dist/server/route-kind\";\nimport { patchFetch as _patchFetch } from \"next/dist/server/lib/patch-fetch\";\nimport * as userland from \"F:\\\\CODE\\\\Project\\\\tempmail\\\\moemail\\\\app\\\\api\\\\email-credentials\\\\extract-user\\\\route.ts\";\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new AppRouteRouteModule({\n    definition: {\n        kind: RouteKind.APP_ROUTE,\n        page: \"/api/email-credentials/extract-user/route\",\n        pathname: \"/api/email-credentials/extract-user\",\n        filename: \"route\",\n        bundlePath: \"app/api/email-credentials/extract-user/route\"\n    },\n    resolvedPagePath: \"F:\\\\CODE\\\\Project\\\\tempmail\\\\moemail\\\\app\\\\api\\\\email-credentials\\\\extract-user\\\\route.ts\",\n    nextConfigOutput,\n    userland\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return _patchFetch({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\nexport { routeModule, workAsyncStorage, workUnitAsyncStorage, serverHooks, patchFetch,  };\n\n//# sourceMappingURL=app-route.js.map", "var _self___RSC_MANIFEST;\nimport { createServerModuleMap } from \"next/dist/server/app-render/action-utils\";\nimport { setReferenceManifestsSingleton } from \"next/dist/server/app-render/encryption-utils\";\nimport { EdgeRouteModuleWrapper } from \"next/dist/server/web/edge-route-module-wrapper\";\n// Import the userland code.\nimport * as module from \"next-app-loader?name=app%2Fapi%2Femail-credentials%2Fextract-user%2Froute&page=%2Fapi%2Femail-credentials%2Fextract-user%2Froute&pagePath=private-next-app-dir%2Fapi%2Femail-credentials%2Fextract-user%2Froute.ts&appDir=F%3A%5CCODE%5CProject%5Ctempmail%5Cmoemail%5Capp&appPaths=%2Fapi%2Femail-credentials%2Fextract-user%2Froute&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!private-next-app-dir/api/email-credentials/extract-user/route.ts?__next_edge_ssr_entry__\";\nconst nextConfig = {\"env\":{},\"eslint\":{\"ignoreDuringBuilds\":false},\"typescript\":{\"ignoreBuildErrors\":false,\"tsconfigPath\":\"tsconfig.json\"},\"distDir\":\".next\",\"cleanDistDir\":true,\"assetPrefix\":\"\",\"cacheMaxMemorySize\":52428800,\"configOrigin\":\"next.config.ts\",\"useFileSystemPublicRoutes\":true,\"generateEtags\":true,\"pageExtensions\":[\"tsx\",\"ts\",\"jsx\",\"js\"],\"poweredByHeader\":true,\"compress\":true,\"images\":{\"deviceSizes\":[640,750,828,1080,1200,1920,2048,3840],\"imageSizes\":[16,32,48,64,96,128,256,384],\"path\":\"/_next/image\",\"loader\":\"default\",\"loaderFile\":\"\",\"domains\":[],\"disableStaticImages\":false,\"minimumCacheTTL\":60,\"formats\":[\"image/webp\"],\"dangerouslyAllowSVG\":false,\"contentSecurityPolicy\":\"script-src 'none'; frame-src 'none'; sandbox;\",\"contentDispositionType\":\"attachment\",\"remotePatterns\":[{\"protocol\":\"https\",\"hostname\":\"avatars.githubusercontent.com\"}],\"unoptimized\":false},\"devIndicators\":{\"appIsrStatus\":true,\"buildActivity\":true,\"buildActivityPosition\":\"bottom-right\"},\"onDemandEntries\":{\"maxInactiveAge\":60000,\"pagesBufferLength\":5},\"amp\":{\"canonicalBase\":\"\"},\"basePath\":\"\",\"sassOptions\":{},\"trailingSlash\":false,\"i18n\":null,\"productionBrowserSourceMaps\":false,\"excludeDefaultMomentLocales\":true,\"serverRuntimeConfig\":{},\"publicRuntimeConfig\":{},\"reactProductionProfiling\":false,\"reactStrictMode\":null,\"reactMaxHeadersLength\":6000,\"httpAgentOptions\":{\"keepAlive\":true},\"logging\":{},\"expireTime\":31536000,\"staticPageGenerationTimeout\":60,\"modularizeImports\":{\"@mui/icons-material\":{\"transform\":\"@mui/icons-material/{{member}}\"},\"lodash\":{\"transform\":\"lodash/{{member}}\"}},\"outputFileTracingRoot\":\"F:\\\\CODE\\\\Project\\\\tempmail\\\\moemail\",\"experimental\":{\"cacheLife\":{\"default\":{\"stale\":300,\"revalidate\":900,\"expire\":4294967294},\"seconds\":{\"stale\":0,\"revalidate\":1,\"expire\":60},\"minutes\":{\"stale\":300,\"revalidate\":60,\"expire\":3600},\"hours\":{\"stale\":300,\"revalidate\":3600,\"expire\":86400},\"days\":{\"stale\":300,\"revalidate\":86400,\"expire\":604800},\"weeks\":{\"stale\":300,\"revalidate\":604800,\"expire\":2592000},\"max\":{\"stale\":300,\"revalidate\":2592000,\"expire\":4294967294}},\"cacheHandlers\":{},\"cssChunking\":true,\"multiZoneDraftMode\":false,\"appNavFailHandling\":false,\"prerenderEarlyExit\":true,\"serverMinification\":true,\"serverSourceMaps\":false,\"linkNoTouchStart\":false,\"caseSensitiveRoutes\":false,\"clientSegmentCache\":false,\"preloadEntriesOnStart\":true,\"clientRouterFilter\":true,\"clientRouterFilterRedirects\":false,\"fetchCacheKeyPrefix\":\"\",\"middlewarePrefetch\":\"flexible\",\"optimisticClientCache\":true,\"manualClientBasePath\":false,\"cpus\":11,\"memoryBasedWorkersCount\":false,\"imgOptConcurrency\":null,\"imgOptTimeoutInSeconds\":7,\"imgOptMaxInputPixels\":268402689,\"imgOptSequentialRead\":null,\"isrFlushToDisk\":true,\"workerThreads\":false,\"optimizeCss\":false,\"nextScriptWorkers\":false,\"scrollRestoration\":false,\"externalDir\":false,\"disableOptimizedLoading\":false,\"gzipSize\":true,\"craCompat\":false,\"esmExternals\":true,\"fullySpecified\":false,\"swcTraceProfiling\":false,\"forceSwcTransforms\":false,\"largePageDataBytes\":128000,\"turbo\":{\"root\":\"F:\\\\CODE\\\\Project\\\\tempmail\\\\moemail\"},\"typedRoutes\":false,\"typedEnv\":false,\"parallelServerCompiles\":false,\"parallelServerBuildTraces\":false,\"ppr\":false,\"authInterrupts\":false,\"reactOwnerStack\":false,\"webpackMemoryOptimizations\":false,\"optimizeServerReact\":true,\"useEarlyImport\":false,\"staleTimes\":{\"dynamic\":0,\"static\":300},\"serverComponentsHmrCache\":true,\"staticGenerationMaxConcurrency\":8,\"staticGenerationMinPagesPerWorker\":25,\"dynamicIO\":false,\"inlineCss\":false,\"optimizePackageImports\":[\"lucide-react\",\"date-fns\",\"lodash-es\",\"ramda\",\"antd\",\"react-bootstrap\",\"ahooks\",\"@ant-design/icons\",\"@headlessui/react\",\"@headlessui-float/react\",\"@heroicons/react/20/solid\",\"@heroicons/react/24/solid\",\"@heroicons/react/24/outline\",\"@visx/visx\",\"@tremor/react\",\"rxjs\",\"@mui/material\",\"@mui/icons-material\",\"recharts\",\"react-use\",\"effect\",\"@effect/schema\",\"@effect/platform\",\"@effect/platform-node\",\"@effect/platform-browser\",\"@effect/platform-bun\",\"@effect/sql\",\"@effect/sql-mssql\",\"@effect/sql-mysql2\",\"@effect/sql-pg\",\"@effect/sql-squlite-node\",\"@effect/sql-squlite-bun\",\"@effect/sql-squlite-wasm\",\"@effect/sql-squlite-react-native\",\"@effect/rpc\",\"@effect/rpc-http\",\"@effect/typeclass\",\"@effect/experimental\",\"@effect/opentelemetry\",\"@material-ui/core\",\"@material-ui/icons\",\"@tabler/icons-react\",\"mui-core\",\"react-icons/ai\",\"react-icons/bi\",\"react-icons/bs\",\"react-icons/cg\",\"react-icons/ci\",\"react-icons/di\",\"react-icons/fa\",\"react-icons/fa6\",\"react-icons/fc\",\"react-icons/fi\",\"react-icons/gi\",\"react-icons/go\",\"react-icons/gr\",\"react-icons/hi\",\"react-icons/hi2\",\"react-icons/im\",\"react-icons/io\",\"react-icons/io5\",\"react-icons/lia\",\"react-icons/lib\",\"react-icons/lu\",\"react-icons/md\",\"react-icons/pi\",\"react-icons/ri\",\"react-icons/rx\",\"react-icons/si\",\"react-icons/sl\",\"react-icons/tb\",\"react-icons/tfi\",\"react-icons/ti\",\"react-icons/vsc\",\"react-icons/wi\"]},\"bundlePagesRouterDependencies\":false,\"configFile\":\"F:\\\\CODE\\\\Project\\\\tempmail\\\\moemail\\\\next.config.ts\",\"configFileName\":\"next.config.ts\"}\nconst maybeJSONParse = (str)=>str ? JSON.parse(str) : undefined;\nconst rscManifest = (_self___RSC_MANIFEST = self.__RSC_MANIFEST) == null ? void 0 : _self___RSC_MANIFEST[\"/api/email-credentials/extract-user/route\"];\nconst rscServerManifest = maybeJSONParse(self.__RSC_SERVER_MANIFEST);\nif (rscManifest && rscServerManifest) {\n    setReferenceManifestsSingleton({\n        page: \"/api/email-credentials/extract-user/route\",\n        clientReferenceManifest: rscManifest,\n        serverActionsManifest: rscServerManifest,\n        serverModuleMap: createServerModuleMap({\n            serverActionsManifest: rscServerManifest\n        })\n    });\n}\nexport const ComponentMod = module;\nexport default EdgeRouteModuleWrapper.wrap(module.routeModule, {\n    nextConfig\n});\n\n//# sourceMappingURL=edge-app-route.js.map", "import { SignJWT, jwtVerify } from 'jose'\n\n// JWT payload types\nexport interface EmailCredentialPayload {\n  emailAddress: string\n  emailId: string\n  credentialId: string\n  iat: number\n  // exp字段不存在，邮箱凭证JWT永久有效\n}\n\nexport interface UserPayload {\n  userEmail: string\n  userId: string\n  exp: number\n  iat: number\n}\n\n// JWT utilities\nexport class JWTUtils {\n  private static getSecret(): Uint8Array {\n    const secret = process.env.JWT_SECRET || process.env.AUTH_SECRET\n    if (!secret) {\n      throw new Error('JWT_SECRET or AUTH_SECRET environment variable is required')\n    }\n    return new TextEncoder().encode(secret)\n  }\n\n  // 生成邮箱凭证JWT（永久有效，与cloudflare_temp_email保持一致）\n  static async signEmailCredential(payload: Omit<EmailCredentialPayload, 'iat'>): Promise<string> {\n    const now = Math.floor(Date.now() / 1000)\n\n    return await new SignJWT({\n      ...payload,\n      iat: now,\n      // 不设置exp，使JWT永久有效（与原项目保持一致）\n    })\n      .setProtectedHeader({ alg: 'HS256' })\n      .setIssuedAt(now)\n      // 不设置过期时间\n      .sign(this.getSecret())\n  }\n\n  // 生成用户JWT\n  static async signUser(payload: Omit<UserPayload, 'exp' | 'iat'>): Promise<string> {\n    const now = Math.floor(Date.now() / 1000)\n    const exp = now + (90 * 24 * 60 * 60) // 90 days\n    \n    return await new SignJWT({\n      ...payload,\n      exp,\n      iat: now,\n    })\n      .setProtectedHeader({ alg: 'HS256' })\n      .setIssuedAt(now)\n      .setExpirationTime(exp)\n      .sign(this.getSecret())\n  }\n\n  // 验证邮箱凭证JWT\n  static async verifyEmailCredential(token: string): Promise<EmailCredentialPayload | null> {\n    try {\n      const { payload } = await jwtVerify(token, this.getSecret())\n\n      // 邮箱凭证JWT永久有效，不检查过期时间\n      // 这与cloudflare_temp_email项目保持一致\n      return payload as EmailCredentialPayload\n    } catch (error) {\n      console.error('JWT verification failed:', error)\n      return null\n    }\n  }\n\n  // 验证用户JWT\n  static async verifyUser(token: string): Promise<UserPayload | null> {\n    try {\n      const { payload } = await jwtVerify(token, this.getSecret())\n      \n      // 检查是否过期\n      const now = Math.floor(Date.now() / 1000)\n      if (payload.exp && payload.exp < now) {\n        return null\n      }\n\n      return payload as UserPayload\n    } catch (error) {\n      console.error('JWT verification failed:', error)\n      return null\n    }\n  }\n\n  // 生成验证码\n  static generateVerificationCode(): string {\n    return Math.floor(100000 + Math.random() * 900000).toString()\n  }\n}\n\n// 邮箱凭证相关的工具函数\nexport class EmailCredentialUtils {\n  // 生成邮箱凭证名称\n  static generateCredentialName(emailAddress: string): string {\n    const timestamp = new Date().toISOString().slice(0, 10)\n    const shortAddress = emailAddress.split('@')[0].slice(0, 8)\n    return `${shortAddress}-${timestamp}`\n  }\n\n  // 检查邮箱地址格式\n  static isValidEmail(email: string): boolean {\n    const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/\n    return emailRegex.test(email)\n  }\n\n  // 检查是否为临时邮箱域名\n  static async isTemporaryEmailDomain(email: string, allowedDomains?: string[]): Promise<boolean> {\n    const domain = email.split('@')[1]?.toLowerCase()\n    if (!domain) return false\n\n    // 如果有配置允许的域名列表，检查是否在列表中\n    if (allowedDomains && allowedDomains.length > 0) {\n      return allowedDomains.some(allowedDomain => \n        domain === allowedDomain.toLowerCase() || \n        domain.endsWith('.' + allowedDomain.toLowerCase())\n      )\n    }\n\n    return true // 默认允许所有域名\n  }\n}\n", "import { createDb } from './db'\nimport { emailCredentials, userEmailBindings, emails, users } from './schema'\nimport { eq, and, desc, count, ne } from 'drizzle-orm'\nimport { JWTUtils } from './jwt'\nimport { nanoid } from 'nanoid'\n\nexport interface EmailCredentialInfo {\n  id: string\n  userId: string\n  emailAddress: string\n  name: string\n  jwt: string\n  enabled: boolean\n  createdAt: Date\n  expiresAt?: Date | null\n  lastUsedAt: Date | null\n  bindingCount?: number // 绑定的用户数量（不包括管理员）\n}\n\nexport interface UserEmailBinding {\n  id: string\n  userId: string\n  emailId: string\n  emailAddress?: string // 从关联查询中获取\n  credentialId?: string | null\n  createdAt: Date\n  enabled?: boolean // 虚拟字段，默认为 true\n}\n\nexport class EmailCredentialManager {\n  // 为邮箱创建凭证（邮箱创建时自动调用）\n  static async createCredentialForEmail(emailAddress: string, userId: string, name?: string): Promise<EmailCredentialInfo> {\n    const db = createDb()\n\n    // 检查邮箱是否已存在凭证\n    const existingCredential = await db.query.emailCredentials.findFirst({\n      where: eq(emailCredentials.emailAddress, emailAddress)\n    })\n\n    if (existingCredential) {\n      return existingCredential as EmailCredentialInfo\n    }\n\n    // 查找对应的邮箱记录，并验证是否属于当前用户\n    const emailRecord = await db.query.emails.findFirst({\n      where: and(\n        eq(emails.address, emailAddress),\n        eq(emails.userId, userId)\n      )\n    })\n\n    if (!emailRecord) {\n      throw new Error('邮箱地址不存在或不属于当前用户')\n    }\n\n    // 生成JWT\n    const credentialId = crypto.randomUUID()\n    const jwt = await JWTUtils.signEmailCredential({\n      emailAddress,\n      emailId: emailRecord.id,\n      credentialId\n    })\n\n    // 创建凭证记录\n    const [credential] = await db.insert(emailCredentials).values({\n      id: credentialId,\n      userId,\n      emailAddress,\n      name: name || `${emailAddress} 的凭证`,\n      jwt,\n      enabled: true,\n      createdAt: new Date(),\n    }).returning()\n\n    return credential as EmailCredentialInfo\n  }\n\n  // 获取所有邮箱凭证（管理员用）\n  static async getAllCredentials(): Promise<EmailCredentialInfo[]> {\n    const db = createDb()\n\n    const credentials = await db.query.emailCredentials.findMany({\n      orderBy: desc(emailCredentials.createdAt)\n    })\n\n    // 为每个凭证计算绑定用户数量（不包括管理员）\n    const credentialsWithCount = await Promise.all(\n      credentials.map(async (credential) => {\n        const bindingCount = await this.getBindingCount(credential.emailAddress)\n        return {\n          ...credential,\n          bindingCount\n        } as EmailCredentialInfo\n      })\n    )\n\n    return credentialsWithCount\n  }\n\n  // 根据邮箱地址获取凭证\n  static async getCredentialByEmail(emailAddress: string): Promise<EmailCredentialInfo | null> {\n    const db = createDb()\n\n    const credential = await db.query.emailCredentials.findFirst({\n      where: eq(emailCredentials.emailAddress, emailAddress)\n    })\n\n    if (!credential) {\n      return null\n    }\n\n    const bindingCount = await this.getBindingCount(emailAddress)\n\n    return {\n      ...credential,\n      bindingCount\n    } as EmailCredentialInfo\n  }\n\n  // 获取邮箱的绑定用户数量（不包括管理员）\n  static async getBindingCount(emailAddress: string): Promise<number> {\n    const db = createDb()\n\n    const result = await db\n      .select({ count: count() })\n      .from(userEmailBindings)\n      .leftJoin(users, eq(userEmailBindings.userId, users.id))\n      .where(\n        and(\n          eq(userEmailBindings.emailAddress, emailAddress),\n          eq(userEmailBindings.enabled, true),\n          ne(users.role, 'admin') // 排除管理员\n        )\n      )\n\n    return result[0]?.count || 0\n  }\n\n  // 验证JWT凭证\n  static async verifyCredential(token: string): Promise<EmailCredentialInfo | null> {\n    const payload = await JWTUtils.verifyEmailCredential(token)\n    if (!payload) {\n      return null\n    }\n\n    const db = createDb()\n\n    // 获取凭证信息\n    const credential = await db.query.emailCredentials.findFirst({\n      where: and(\n        eq(emailCredentials.id, payload.credentialId),\n        eq(emailCredentials.enabled, true)\n      )\n    })\n\n    if (!credential) {\n      return null\n    }\n\n    // 更新最后使用时间\n    await db.update(emailCredentials)\n      .set({ lastUsedAt: new Date() })\n      .where(eq(emailCredentials.id, credential.id))\n\n    return credential as EmailCredentialInfo\n  }\n\n  // 用户通过邮箱凭证绑定邮箱\n  static async bindEmailByCredential(userId: string, jwt: string): Promise<UserEmailBinding> {\n    const db = createDb()\n\n    // 验证JWT凭证\n    const credential = await this.verifyCredential(jwt)\n    if (!credential) {\n      throw new Error('无效的邮箱凭证')\n    }\n\n    // 查找对应的邮箱记录\n    const emailRecord = await db.query.emails.findFirst({\n      where: eq(emails.address, credential.emailAddress)\n    })\n\n    if (!emailRecord) {\n      throw new Error('邮箱记录不存在')\n    }\n\n    // 检查用户是否已绑定此邮箱\n    const existingBinding = await db.query.userEmailBindings.findFirst({\n      where: and(\n        eq(userEmailBindings.userId, userId),\n        eq(userEmailBindings.emailId, emailRecord.id)\n      )\n    })\n\n    if (existingBinding) {\n      throw new Error('您已绑定此邮箱')\n    }\n\n    // 创建绑定记录\n    const [binding] = await db.insert(userEmailBindings).values({\n      id: crypto.randomUUID(),\n      userId,\n      emailId: emailRecord.id,\n      credentialId: credential.id,\n      createdAt: new Date(),\n    }).returning()\n\n    return {\n      ...binding,\n      emailAddress: credential.emailAddress,\n      enabled: true\n    } as UserEmailBinding\n  }\n\n  // 获取用户绑定的邮箱列表\n  static async getUserBindings(userId: string): Promise<UserEmailBinding[]> {\n    const db = createDb()\n\n    const bindings = await db.query.userEmailBindings.findMany({\n      where: eq(userEmailBindings.userId, userId),\n      orderBy: desc(userEmailBindings.createdAt),\n      with: {\n        email: true // 关联查询邮箱信息\n      }\n    })\n\n    return bindings.map(binding => ({\n      ...binding,\n      emailAddress: binding.email?.address,\n      enabled: true // 默认启用\n    })) as UserEmailBinding[]\n  }\n\n  // 解绑邮箱\n  static async unbindEmail(userId: string, emailAddress: string): Promise<boolean> {\n    const db = createDb()\n\n    // 先查找邮箱记录\n    const emailRecord = await db.query.emails.findFirst({\n      where: eq(emails.address, emailAddress)\n    })\n\n    if (!emailRecord) {\n      return false\n    }\n\n    const result = await db.delete(userEmailBindings)\n      .where(and(\n        eq(userEmailBindings.userId, userId),\n        eq(userEmailBindings.emailId, emailRecord.id)\n      ))\n\n    return result.changes > 0\n  }\n\n  // 根据ID获取凭证\n  static async getCredentialById(credentialId: string): Promise<EmailCredentialInfo | null> {\n    const db = createDb()\n\n    const credential = await db.query.emailCredentials.findFirst({\n      where: eq(emailCredentials.id, credentialId)\n    })\n\n    return credential as EmailCredentialInfo | null\n  }\n}\n\n\n"], "names": ["runtime", "z", "object", "emailAddresses", "array", "string", "email", "min", "POST", "request", "session", "auth", "user", "id", "NextResponse", "json", "error", "status", "body", "validatedData", "extractUserCredentialSchema", "parse", "userEmailAddresses", "userBindings", "EmailCredentialManager", "getUserBindings", "map", "binding", "emailAddress", "invalidEmails", "filter", "includes", "length", "join", "credentials", "credential", "getCredentialByEmail", "push", "jwt", "enabled", "createdAt", "toISOString", "lastUsedAt", "bindingCount", "success", "console", "ZodError", "errors", "message", "Error", "JWTUtils", "getSecret", "secret", "process", "env", "JWT_SECRET", "AUTH_SECRET", "TextEncoder", "encode", "signEmailCredential", "payload", "now", "Math", "floor", "Date", "SignJWT", "iat", "setProtectedHeader", "alg", "setIssuedAt", "sign", "signUser", "exp", "setExpirationTime", "verifyEmailCredential", "token", "jwtVerify", "verifyUser", "generateVerificationCode", "random", "toString", "createCredentialForEmail", "userId", "name", "db", "createDb", "existingCredential", "query", "emailCredentials", "<PERSON><PERSON><PERSON><PERSON>", "where", "eq", "emailRecord", "emails", "and", "address", "credentialId", "crypto", "randomUUID", "emailId", "insert", "values", "returning", "getAllCredentials", "find<PERSON>any", "orderBy", "desc", "Promise", "all", "getBindingCount", "result", "select", "count", "from", "userEmailBindings", "leftJoin", "users", "ne", "role", "verifyCredential", "update", "set", "bindEmailByCredential", "existingBinding", "bindings", "with", "unbindEmail", "delete", "changes", "getCredentialById"], "sourceRoot": "", "ignoreList": []}