if(!self.define){let e,s={};const t=(t,i)=>(t=new URL(t+".js",i).href,s[t]||new Promise((s=>{if("document"in self){const e=document.createElement("script");e.src=t,e.onload=s,document.head.appendChild(e)}else e=t,importScripts(t),s()})).then((()=>{let e=s[t];if(!e)throw new Error(`Module ${t} didn’t register its module`);return e})));self.define=(i,n)=>{const c=e||("document"in self?document.currentScript.src:"")||location.href;if(s[c])return;let a={};const r=e=>t(e,c),o={module:{uri:c},exports:a,require:r};s[c]=Promise.all(i.map((e=>o[e]||r(e)))).then((e=>(n(...e),a)))}}define(["./workbox-4754cb34"],(function(e){"use strict";importScripts(),self.skipWaiting(),e.clientsClaim(),e.precacheAndRoute([{url:"/_next/app-build-manifest.json",revision:"3c9597140633bf961d5ad9b42d2897b5"},{url:"/_next/static/Zk7-vvwP3pl-Kc16kMPf3/_buildManifest.js",revision:"00b5a66ecbc3038fc3dc385e7ee5f295"},{url:"/_next/static/Zk7-vvwP3pl-Kc16kMPf3/_ssgManifest.js",revision:"b6652df95db52feb4daf4eca35380933"},{url:"/_next/static/chunks/203.2b4c1ee4fbe3a7cf.js",revision:"2b4c1ee4fbe3a7cf"},{url:"/_next/static/chunks/218.57a830a2c55ba802.js",revision:"57a830a2c55ba802"},{url:"/_next/static/chunks/239-e44801bab8add6b5.js",revision:"Zk7-vvwP3pl-Kc16kMPf3"},{url:"/_next/static/chunks/246-84251b1aee1d265d.js",revision:"Zk7-vvwP3pl-Kc16kMPf3"},{url:"/_next/static/chunks/275-bc06d0baa4c25e29.js",revision:"Zk7-vvwP3pl-Kc16kMPf3"},{url:"/_next/static/chunks/4bd1b696-0fc7bc03a1e1bb60.js",revision:"Zk7-vvwP3pl-Kc16kMPf3"},{url:"/_next/static/chunks/517-75781f6294bb6ea0.js",revision:"Zk7-vvwP3pl-Kc16kMPf3"},{url:"/_next/static/chunks/568-c3a46231e1635408.js",revision:"Zk7-vvwP3pl-Kc16kMPf3"},{url:"/_next/static/chunks/630-46b099f4c85bc19f.js",revision:"Zk7-vvwP3pl-Kc16kMPf3"},{url:"/_next/static/chunks/689-dd6e9689efbc63b7.js",revision:"Zk7-vvwP3pl-Kc16kMPf3"},{url:"/_next/static/chunks/86-d41a30bc3c2ecf16.js",revision:"Zk7-vvwP3pl-Kc16kMPf3"},{url:"/_next/static/chunks/899-7d7dc226b87e39d3.js",revision:"Zk7-vvwP3pl-Kc16kMPf3"},{url:"/_next/static/chunks/app/_not-found/page-2d2f6a5d21af9765.js",revision:"Zk7-vvwP3pl-Kc16kMPf3"},{url:"/_next/static/chunks/app/api/api-keys/%5Bid%5D/route-15e512f788c20073.js",revision:"Zk7-vvwP3pl-Kc16kMPf3"},{url:"/_next/static/chunks/app/api/api-keys/route-4b537c470cd1d941.js",revision:"Zk7-vvwP3pl-Kc16kMPf3"},{url:"/_next/static/chunks/app/api/auth/%5B...auth%5D/route-6f0f1a124bfdee0d.js",revision:"Zk7-vvwP3pl-Kc16kMPf3"},{url:"/_next/static/chunks/app/api/auth/register/route-38f31969fd762ec4.js",revision:"Zk7-vvwP3pl-Kc16kMPf3"},{url:"/_next/static/chunks/app/api/config/route-4aff506a53803179.js",revision:"Zk7-vvwP3pl-Kc16kMPf3"},{url:"/_next/static/chunks/app/api/email-credentials/bindings/route-7e423a31ce40d7ff.js",revision:"Zk7-vvwP3pl-Kc16kMPf3"},{url:"/_next/static/chunks/app/api/email-credentials/extract-user/route-3a59544ee3156f61.js",revision:"Zk7-vvwP3pl-Kc16kMPf3"},{url:"/_next/static/chunks/app/api/email-credentials/extract/route-1ce92fa9e42b7a4b.js",revision:"Zk7-vvwP3pl-Kc16kMPf3"},{url:"/_next/static/chunks/app/api/email-credentials/route-6d75aed03e791412.js",revision:"Zk7-vvwP3pl-Kc16kMPf3"},{url:"/_next/static/chunks/app/api/emails/%5Bid%5D/%5BmessageId%5D/route-2d48951a305d5874.js",revision:"Zk7-vvwP3pl-Kc16kMPf3"},{url:"/_next/static/chunks/app/api/emails/%5Bid%5D/route-9b3f032c23547c8b.js",revision:"Zk7-vvwP3pl-Kc16kMPf3"},{url:"/_next/static/chunks/app/api/emails/bind/route-79647200bc20d82d.js",revision:"Zk7-vvwP3pl-Kc16kMPf3"},{url:"/_next/static/chunks/app/api/emails/generate/route-47c3e8205aec8daf.js",revision:"Zk7-vvwP3pl-Kc16kMPf3"},{url:"/_next/static/chunks/app/api/emails/route-f5b0d08b308d1def.js",revision:"Zk7-vvwP3pl-Kc16kMPf3"},{url:"/_next/static/chunks/app/api/roles/init-emperor/route-52ed2ab9870a68b9.js",revision:"Zk7-vvwP3pl-Kc16kMPf3"},{url:"/_next/static/chunks/app/api/roles/promote/route-b904aeeb81974c40.js",revision:"Zk7-vvwP3pl-Kc16kMPf3"},{url:"/_next/static/chunks/app/api/roles/users/route-b723441ef91f61f1.js",revision:"Zk7-vvwP3pl-Kc16kMPf3"},{url:"/_next/static/chunks/app/api/webhook/route-933a9b959183f9e6.js",revision:"Zk7-vvwP3pl-Kc16kMPf3"},{url:"/_next/static/chunks/app/api/webhook/test/route-b1e76d715d0c79c9.js",revision:"Zk7-vvwP3pl-Kc16kMPf3"},{url:"/_next/static/chunks/app/layout-bbd45b635c07fc4f.js",revision:"Zk7-vvwP3pl-Kc16kMPf3"},{url:"/_next/static/chunks/app/login/page-0078d26b5d71e56d.js",revision:"Zk7-vvwP3pl-Kc16kMPf3"},{url:"/_next/static/chunks/app/moe/page-9105e29e23bf2f8c.js",revision:"Zk7-vvwP3pl-Kc16kMPf3"},{url:"/_next/static/chunks/app/page-83b4f4b481786351.js",revision:"Zk7-vvwP3pl-Kc16kMPf3"},{url:"/_next/static/chunks/app/profile/page-90a54f9d95afd543.js",revision:"Zk7-vvwP3pl-Kc16kMPf3"},{url:"/_next/static/chunks/framework-6b27c2b7aa38af2d.js",revision:"Zk7-vvwP3pl-Kc16kMPf3"},{url:"/_next/static/chunks/main-56535577560c530e.js",revision:"Zk7-vvwP3pl-Kc16kMPf3"},{url:"/_next/static/chunks/main-app-7cb221b10ad507ce.js",revision:"Zk7-vvwP3pl-Kc16kMPf3"},{url:"/_next/static/chunks/pages/_app-04c695bc05fa7935.js",revision:"Zk7-vvwP3pl-Kc16kMPf3"},{url:"/_next/static/chunks/pages/_error-da21db4e7d4f6e09.js",revision:"Zk7-vvwP3pl-Kc16kMPf3"},{url:"/_next/static/chunks/polyfills-42372ed130431b0a.js",revision:"846118c33b2c0e922d7b3a7676f81f6f"},{url:"/_next/static/chunks/webpack-6c19dd8c5b4d1467.js",revision:"Zk7-vvwP3pl-Kc16kMPf3"},{url:"/_next/static/css/79558e15584d183d.css",revision:"79558e15584d183d"},{url:"/fonts/zpix.ttf",revision:"04cd0a606f67cf86e1075ff1b4d0b108"},{url:"/icons/icon-192x192.png",revision:"a40c836f3f904b63a1722fd0ae7db9bf"},{url:"/icons/icon-512x512.png",revision:"0e067d29946ced1568867073562bc875"},{url:"/manifest.json",revision:"5198a8da820275e7d6f99ca5ca656c5c"}],{ignoreURLParametersMatching:[]}),e.cleanupOutdatedCaches(),e.registerRoute("/",new e.NetworkFirst({cacheName:"start-url",plugins:[{cacheWillUpdate:async({request:e,response:s,event:t,state:i})=>s&&"opaqueredirect"===s.type?new Response(s.body,{status:200,statusText:"OK",headers:s.headers}):s}]}),"GET"),e.registerRoute(/^https:\/\/fonts\.(?:gstatic)\.com\/.*/i,new e.CacheFirst({cacheName:"google-fonts-webfonts",plugins:[new e.ExpirationPlugin({maxEntries:4,maxAgeSeconds:31536e3})]}),"GET"),e.registerRoute(/^https:\/\/fonts\.(?:googleapis)\.com\/.*/i,new e.StaleWhileRevalidate({cacheName:"google-fonts-stylesheets",plugins:[new e.ExpirationPlugin({maxEntries:4,maxAgeSeconds:604800})]}),"GET"),e.registerRoute(/\.(?:eot|otf|ttc|ttf|woff|woff2|font.css)$/i,new e.StaleWhileRevalidate({cacheName:"static-font-assets",plugins:[new e.ExpirationPlugin({maxEntries:4,maxAgeSeconds:604800})]}),"GET"),e.registerRoute(/\.(?:jpg|jpeg|gif|png|svg|ico|webp)$/i,new e.StaleWhileRevalidate({cacheName:"static-image-assets",plugins:[new e.ExpirationPlugin({maxEntries:64,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(/\/_next\/image\?url=.+$/i,new e.StaleWhileRevalidate({cacheName:"next-image",plugins:[new e.ExpirationPlugin({maxEntries:64,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(/\.(?:mp3|wav|ogg)$/i,new e.CacheFirst({cacheName:"static-audio-assets",plugins:[new e.RangeRequestsPlugin,new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(/\.(?:mp4)$/i,new e.CacheFirst({cacheName:"static-video-assets",plugins:[new e.RangeRequestsPlugin,new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(/\.(?:js)$/i,new e.StaleWhileRevalidate({cacheName:"static-js-assets",plugins:[new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(/\.(?:css|less)$/i,new e.StaleWhileRevalidate({cacheName:"static-style-assets",plugins:[new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(/\/_next\/data\/.+\/.+\.json$/i,new e.StaleWhileRevalidate({cacheName:"next-data",plugins:[new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(/\.(?:json|xml|csv)$/i,new e.NetworkFirst({cacheName:"static-data-assets",plugins:[new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400})]}),"GET"),e.registerRoute((({url:e})=>{if(!(self.origin===e.origin))return!1;const s=e.pathname;return!s.startsWith("/api/auth/")&&!!s.startsWith("/api/")}),new e.NetworkFirst({cacheName:"apis",networkTimeoutSeconds:10,plugins:[new e.ExpirationPlugin({maxEntries:16,maxAgeSeconds:86400})]}),"GET"),e.registerRoute((({url:e})=>{if(!(self.origin===e.origin))return!1;return!e.pathname.startsWith("/api/")}),new e.NetworkFirst({cacheName:"others",networkTimeoutSeconds:10,plugins:[new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400})]}),"GET"),e.registerRoute((({url:e})=>!(self.origin===e.origin)),new e.NetworkFirst({cacheName:"cross-origin",networkTimeoutSeconds:10,plugins:[new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:3600})]}),"GET")}));
