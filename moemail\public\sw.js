if(!self.define){let e,s={};const a=(a,t)=>(a=new URL(a+".js",t).href,s[a]||new Promise((s=>{if("document"in self){const e=document.createElement("script");e.src=a,e.onload=s,document.head.appendChild(e)}else e=a,importScripts(a),s()})).then((()=>{let e=s[a];if(!e)throw new Error(`Module ${a} didn’t register its module`);return e})));self.define=(t,i)=>{const n=e||("document"in self?document.currentScript.src:"")||location.href;if(s[n])return;let c={};const r=e=>a(e,n),o={module:{uri:n},exports:c,require:r};s[n]=Promise.all(t.map((e=>o[e]||r(e)))).then((e=>(i(...e),c)))}}define(["./workbox-4754cb34"],(function(e){"use strict";importScripts(),self.skipWaiting(),e.clientsClaim(),e.precacheAndRoute([{url:"/_next/app-build-manifest.json",revision:"a6329151da5d77948ffda78000ffddcf"},{url:"/_next/static/YWJCGa64f5zMxOTdYC91M/_buildManifest.js",revision:"ff018801f1d501698b2b295dfc4ca10c"},{url:"/_next/static/YWJCGa64f5zMxOTdYC91M/_ssgManifest.js",revision:"b6652df95db52feb4daf4eca35380933"},{url:"/_next/static/chunks/10-9daf880bdebe9030.js",revision:"YWJCGa64f5zMxOTdYC91M"},{url:"/_next/static/chunks/203.2b4c1ee4fbe3a7cf.js",revision:"2b4c1ee4fbe3a7cf"},{url:"/_next/static/chunks/218.57a830a2c55ba802.js",revision:"57a830a2c55ba802"},{url:"/_next/static/chunks/246-84251b1aee1d265d.js",revision:"YWJCGa64f5zMxOTdYC91M"},{url:"/_next/static/chunks/275-bc06d0baa4c25e29.js",revision:"YWJCGa64f5zMxOTdYC91M"},{url:"/_next/static/chunks/4bd1b696-0fc7bc03a1e1bb60.js",revision:"YWJCGa64f5zMxOTdYC91M"},{url:"/_next/static/chunks/517-75781f6294bb6ea0.js",revision:"YWJCGa64f5zMxOTdYC91M"},{url:"/_next/static/chunks/568-c3a46231e1635408.js",revision:"YWJCGa64f5zMxOTdYC91M"},{url:"/_next/static/chunks/630-46b099f4c85bc19f.js",revision:"YWJCGa64f5zMxOTdYC91M"},{url:"/_next/static/chunks/689-dd6e9689efbc63b7.js",revision:"YWJCGa64f5zMxOTdYC91M"},{url:"/_next/static/chunks/86-879bda5563650e6e.js",revision:"YWJCGa64f5zMxOTdYC91M"},{url:"/_next/static/chunks/899-7d7dc226b87e39d3.js",revision:"YWJCGa64f5zMxOTdYC91M"},{url:"/_next/static/chunks/app/_not-found/page-2d2f6a5d21af9765.js",revision:"YWJCGa64f5zMxOTdYC91M"},{url:"/_next/static/chunks/app/api/api-keys/%5Bid%5D/route-988b838281016c0d.js",revision:"YWJCGa64f5zMxOTdYC91M"},{url:"/_next/static/chunks/app/api/api-keys/route-217b624ce1d8e0ba.js",revision:"YWJCGa64f5zMxOTdYC91M"},{url:"/_next/static/chunks/app/api/auth/%5B...auth%5D/route-cb6563c0af57e9f0.js",revision:"YWJCGa64f5zMxOTdYC91M"},{url:"/_next/static/chunks/app/api/auth/register/route-6c8b4adbfb5fdd36.js",revision:"YWJCGa64f5zMxOTdYC91M"},{url:"/_next/static/chunks/app/api/config/route-a5c2457056e3f88b.js",revision:"YWJCGa64f5zMxOTdYC91M"},{url:"/_next/static/chunks/app/api/email-credentials/%5Bid%5D/route-6fc6cab82a5f6cf2.js",revision:"YWJCGa64f5zMxOTdYC91M"},{url:"/_next/static/chunks/app/api/email-credentials/bindings/route-586c6f10173a4025.js",revision:"YWJCGa64f5zMxOTdYC91M"},{url:"/_next/static/chunks/app/api/email-credentials/create/route-070bb8659badea24.js",revision:"YWJCGa64f5zMxOTdYC91M"},{url:"/_next/static/chunks/app/api/email-credentials/extract-user/route-550432ed739a6dce.js",revision:"YWJCGa64f5zMxOTdYC91M"},{url:"/_next/static/chunks/app/api/email-credentials/extract/route-90c59cb6227b0f34.js",revision:"YWJCGa64f5zMxOTdYC91M"},{url:"/_next/static/chunks/app/api/email-credentials/route-fba2bd23f94761b4.js",revision:"YWJCGa64f5zMxOTdYC91M"},{url:"/_next/static/chunks/app/api/emails/%5Bid%5D/%5BmessageId%5D/route-aa53cabb4c9f2fbe.js",revision:"YWJCGa64f5zMxOTdYC91M"},{url:"/_next/static/chunks/app/api/emails/%5Bid%5D/route-d0afc475299ab038.js",revision:"YWJCGa64f5zMxOTdYC91M"},{url:"/_next/static/chunks/app/api/emails/bind/route-2ab1af7cdb1d137f.js",revision:"YWJCGa64f5zMxOTdYC91M"},{url:"/_next/static/chunks/app/api/emails/generate/route-c28a4f95dea8b446.js",revision:"YWJCGa64f5zMxOTdYC91M"},{url:"/_next/static/chunks/app/api/emails/route-de342c0b4a6207d2.js",revision:"YWJCGa64f5zMxOTdYC91M"},{url:"/_next/static/chunks/app/api/roles/init-emperor/route-07163eb5aeecbe77.js",revision:"YWJCGa64f5zMxOTdYC91M"},{url:"/_next/static/chunks/app/api/roles/promote/route-ed8b39adf2a6bce5.js",revision:"YWJCGa64f5zMxOTdYC91M"},{url:"/_next/static/chunks/app/api/roles/users/route-483133cfd7a186a0.js",revision:"YWJCGa64f5zMxOTdYC91M"},{url:"/_next/static/chunks/app/api/webhook/route-68768125f56755cc.js",revision:"YWJCGa64f5zMxOTdYC91M"},{url:"/_next/static/chunks/app/api/webhook/test/route-f4385bffa5c74d94.js",revision:"YWJCGa64f5zMxOTdYC91M"},{url:"/_next/static/chunks/app/layout-bbd45b635c07fc4f.js",revision:"YWJCGa64f5zMxOTdYC91M"},{url:"/_next/static/chunks/app/login/page-0078d26b5d71e56d.js",revision:"YWJCGa64f5zMxOTdYC91M"},{url:"/_next/static/chunks/app/moe/page-e2ec7a2b970965b0.js",revision:"YWJCGa64f5zMxOTdYC91M"},{url:"/_next/static/chunks/app/page-83b4f4b481786351.js",revision:"YWJCGa64f5zMxOTdYC91M"},{url:"/_next/static/chunks/app/profile/page-d18cc64bc3ac53f6.js",revision:"YWJCGa64f5zMxOTdYC91M"},{url:"/_next/static/chunks/framework-6b27c2b7aa38af2d.js",revision:"YWJCGa64f5zMxOTdYC91M"},{url:"/_next/static/chunks/main-app-7cb221b10ad507ce.js",revision:"YWJCGa64f5zMxOTdYC91M"},{url:"/_next/static/chunks/main-b6e7e90e691debb2.js",revision:"YWJCGa64f5zMxOTdYC91M"},{url:"/_next/static/chunks/pages/_app-04c695bc05fa7935.js",revision:"YWJCGa64f5zMxOTdYC91M"},{url:"/_next/static/chunks/pages/_error-da21db4e7d4f6e09.js",revision:"YWJCGa64f5zMxOTdYC91M"},{url:"/_next/static/chunks/polyfills-42372ed130431b0a.js",revision:"846118c33b2c0e922d7b3a7676f81f6f"},{url:"/_next/static/chunks/webpack-6c19dd8c5b4d1467.js",revision:"YWJCGa64f5zMxOTdYC91M"},{url:"/_next/static/css/92ece4816dc5aa72.css",revision:"92ece4816dc5aa72"},{url:"/fonts/zpix.ttf",revision:"04cd0a606f67cf86e1075ff1b4d0b108"},{url:"/icons/icon-192x192.png",revision:"a40c836f3f904b63a1722fd0ae7db9bf"},{url:"/icons/icon-512x512.png",revision:"0e067d29946ced1568867073562bc875"},{url:"/manifest.json",revision:"5198a8da820275e7d6f99ca5ca656c5c"}],{ignoreURLParametersMatching:[]}),e.cleanupOutdatedCaches(),e.registerRoute("/",new e.NetworkFirst({cacheName:"start-url",plugins:[{cacheWillUpdate:async({request:e,response:s,event:a,state:t})=>s&&"opaqueredirect"===s.type?new Response(s.body,{status:200,statusText:"OK",headers:s.headers}):s}]}),"GET"),e.registerRoute(/^https:\/\/fonts\.(?:gstatic)\.com\/.*/i,new e.CacheFirst({cacheName:"google-fonts-webfonts",plugins:[new e.ExpirationPlugin({maxEntries:4,maxAgeSeconds:31536e3})]}),"GET"),e.registerRoute(/^https:\/\/fonts\.(?:googleapis)\.com\/.*/i,new e.StaleWhileRevalidate({cacheName:"google-fonts-stylesheets",plugins:[new e.ExpirationPlugin({maxEntries:4,maxAgeSeconds:604800})]}),"GET"),e.registerRoute(/\.(?:eot|otf|ttc|ttf|woff|woff2|font.css)$/i,new e.StaleWhileRevalidate({cacheName:"static-font-assets",plugins:[new e.ExpirationPlugin({maxEntries:4,maxAgeSeconds:604800})]}),"GET"),e.registerRoute(/\.(?:jpg|jpeg|gif|png|svg|ico|webp)$/i,new e.StaleWhileRevalidate({cacheName:"static-image-assets",plugins:[new e.ExpirationPlugin({maxEntries:64,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(/\/_next\/image\?url=.+$/i,new e.StaleWhileRevalidate({cacheName:"next-image",plugins:[new e.ExpirationPlugin({maxEntries:64,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(/\.(?:mp3|wav|ogg)$/i,new e.CacheFirst({cacheName:"static-audio-assets",plugins:[new e.RangeRequestsPlugin,new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(/\.(?:mp4)$/i,new e.CacheFirst({cacheName:"static-video-assets",plugins:[new e.RangeRequestsPlugin,new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(/\.(?:js)$/i,new e.StaleWhileRevalidate({cacheName:"static-js-assets",plugins:[new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(/\.(?:css|less)$/i,new e.StaleWhileRevalidate({cacheName:"static-style-assets",plugins:[new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(/\/_next\/data\/.+\/.+\.json$/i,new e.StaleWhileRevalidate({cacheName:"next-data",plugins:[new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(/\.(?:json|xml|csv)$/i,new e.NetworkFirst({cacheName:"static-data-assets",plugins:[new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400})]}),"GET"),e.registerRoute((({url:e})=>{if(!(self.origin===e.origin))return!1;const s=e.pathname;return!s.startsWith("/api/auth/")&&!!s.startsWith("/api/")}),new e.NetworkFirst({cacheName:"apis",networkTimeoutSeconds:10,plugins:[new e.ExpirationPlugin({maxEntries:16,maxAgeSeconds:86400})]}),"GET"),e.registerRoute((({url:e})=>{if(!(self.origin===e.origin))return!1;return!e.pathname.startsWith("/api/")}),new e.NetworkFirst({cacheName:"others",networkTimeoutSeconds:10,plugins:[new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400})]}),"GET"),e.registerRoute((({url:e})=>!(self.origin===e.origin)),new e.NetworkFirst({cacheName:"cross-origin",networkTimeoutSeconds:10,plugins:[new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:3600})]}),"GET")}));
