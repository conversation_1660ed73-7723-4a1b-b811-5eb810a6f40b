import { drizzle } from 'drizzle-orm/d1'
import { getRequestContext } from '@cloudflare/next-on-pages'
import { users, roles, userRoles } from '../app/lib/schema'
import { eq, and } from 'drizzle-orm'
import { ROLES } from '../app/lib/permissions'

// 模拟 Cloudflare 环境
const mockEnv = {
  DB: null as any
}

function createDb() {
  return drizzle(mockEnv.DB, { schema: { users, roles, userRoles } })
}

async function listUsers() {
  const db = createDb()
  
  const usersWithRoles = await db.query.users.findMany({
    with: {
      userRoles: {
        with: {
          role: true
        }
      }
    }
  })
  
  console.log('用户列表:')
  usersWithRoles.forEach(user => {
    const roleNames = user.userRoles.map(ur => ur.role.name).join(', ')
    console.log(`- ${user.username || user.email}: ${roleNames || '无角色'}`)
  })
}

async function setUserRole(userIdentifier: string, roleName: string) {
  const db = createDb()
  
  // 查找用户
  const user = await db.query.users.findFirst({
    where: userIdentifier.includes('@') 
      ? eq(users.email, userIdentifier)
      : eq(users.username, userIdentifier)
  })
  
  if (!user) {
    console.error(`用户不存在: ${userIdentifier}`)
    return
  }
  
  // 查找角色
  const role = await db.query.roles.findFirst({
    where: eq(roles.name, roleName)
  })
  
  if (!role) {
    console.error(`角色不存在: ${roleName}`)
    console.log('可用角色:', Object.values(ROLES))
    return
  }
  
  // 删除现有角色
  await db.delete(userRoles).where(eq(userRoles.userId, user.id))
  
  // 添加新角色
  await db.insert(userRoles).values({
    userId: user.id,
    roleId: role.id
  })
  
  console.log(`已将用户 ${userIdentifier} 的角色设置为 ${roleName}`)
}

async function main() {
  const args = process.argv.slice(2)
  
  if (args.length === 0) {
    console.log('用法:')
    console.log('  查看用户: npm run script:roles list')
    console.log('  设置角色: npm run script:roles set <用户名或邮箱> <角色>')
    console.log('  可用角色:', Object.values(ROLES))
    return
  }
  
  const command = args[0]
  
  if (command === 'list') {
    await listUsers()
  } else if (command === 'set' && args.length === 3) {
    await setUserRole(args[1], args[2])
  } else {
    console.error('无效的命令')
  }
}

main().catch(console.error)
