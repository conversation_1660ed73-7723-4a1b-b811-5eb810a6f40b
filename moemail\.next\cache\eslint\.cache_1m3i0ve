[{"F:\\CODE\\Project\\tempmail\\moemail\\app\\api\\api-keys\\route.ts": "1", "F:\\CODE\\Project\\tempmail\\moemail\\app\\api\\api-keys\\[id]\\route.ts": "2", "F:\\CODE\\Project\\tempmail\\moemail\\app\\api\\auth\\register\\route.ts": "3", "F:\\CODE\\Project\\tempmail\\moemail\\app\\api\\auth\\[...auth]\\route.ts": "4", "F:\\CODE\\Project\\tempmail\\moemail\\app\\api\\config\\route.ts": "5", "F:\\CODE\\Project\\tempmail\\moemail\\app\\api\\email-credentials\\bindings\\route.ts": "6", "F:\\CODE\\Project\\tempmail\\moemail\\app\\api\\email-credentials\\extract\\route.ts": "7", "F:\\CODE\\Project\\tempmail\\moemail\\app\\api\\email-credentials\\route.ts": "8", "F:\\CODE\\Project\\tempmail\\moemail\\app\\api\\emails\\bind\\route.ts": "9", "F:\\CODE\\Project\\tempmail\\moemail\\app\\api\\emails\\generate\\route.ts": "10", "F:\\CODE\\Project\\tempmail\\moemail\\app\\api\\emails\\route.ts": "11", "F:\\CODE\\Project\\tempmail\\moemail\\app\\api\\emails\\[id]\\route.ts": "12", "F:\\CODE\\Project\\tempmail\\moemail\\app\\api\\emails\\[id]\\[messageId]\\route.ts": "13", "F:\\CODE\\Project\\tempmail\\moemail\\app\\api\\roles\\init-emperor\\route.ts": "14", "F:\\CODE\\Project\\tempmail\\moemail\\app\\api\\roles\\promote\\route.ts": "15", "F:\\CODE\\Project\\tempmail\\moemail\\app\\api\\roles\\users\\route.ts": "16", "F:\\CODE\\Project\\tempmail\\moemail\\app\\api\\webhook\\route.ts": "17", "F:\\CODE\\Project\\tempmail\\moemail\\app\\api\\webhook\\test\\route.ts": "18", "F:\\CODE\\Project\\tempmail\\moemail\\app\\components\\auth\\login-form.tsx": "19", "F:\\CODE\\Project\\tempmail\\moemail\\app\\components\\auth\\sign-button.tsx": "20", "F:\\CODE\\Project\\tempmail\\moemail\\app\\components\\emails\\bind-email-dialog.tsx": "21", "F:\\CODE\\Project\\tempmail\\moemail\\app\\components\\emails\\create-dialog.tsx": "22", "F:\\CODE\\Project\\tempmail\\moemail\\app\\components\\emails\\email-list.tsx": "23", "F:\\CODE\\Project\\tempmail\\moemail\\app\\components\\emails\\message-list.tsx": "24", "F:\\CODE\\Project\\tempmail\\moemail\\app\\components\\emails\\message-view.tsx": "25", "F:\\CODE\\Project\\tempmail\\moemail\\app\\components\\emails\\three-column-layout.tsx": "26", "F:\\CODE\\Project\\tempmail\\moemail\\app\\components\\float-menu.tsx": "27", "F:\\CODE\\Project\\tempmail\\moemail\\app\\components\\home\\action-button.tsx": "28", "F:\\CODE\\Project\\tempmail\\moemail\\app\\components\\home\\feature-card.tsx": "29", "F:\\CODE\\Project\\tempmail\\moemail\\app\\components\\layout\\header.tsx": "30", "F:\\CODE\\Project\\tempmail\\moemail\\app\\components\\no-permission-dialog.tsx": "31", "F:\\CODE\\Project\\tempmail\\moemail\\app\\components\\profile\\api-key-panel.tsx": "32", "F:\\CODE\\Project\\tempmail\\moemail\\app\\components\\profile\\config-panel.tsx": "33", "F:\\CODE\\Project\\tempmail\\moemail\\app\\components\\profile\\email-credentials-panel.tsx": "34", "F:\\CODE\\Project\\tempmail\\moemail\\app\\components\\profile\\profile-card.tsx": "35", "F:\\CODE\\Project\\tempmail\\moemail\\app\\components\\profile\\promote-panel.tsx": "36", "F:\\CODE\\Project\\tempmail\\moemail\\app\\components\\profile\\webhook-config.tsx": "37", "F:\\CODE\\Project\\tempmail\\moemail\\app\\components\\theme\\theme-provider.tsx": "38", "F:\\CODE\\Project\\tempmail\\moemail\\app\\components\\theme\\theme-toggle.tsx": "39", "F:\\CODE\\Project\\tempmail\\moemail\\app\\components\\ui\\alert-dialog.tsx": "40", "F:\\CODE\\Project\\tempmail\\moemail\\app\\components\\ui\\button.tsx": "41", "F:\\CODE\\Project\\tempmail\\moemail\\app\\components\\ui\\card.tsx": "42", "F:\\CODE\\Project\\tempmail\\moemail\\app\\components\\ui\\dialog.tsx": "43", "F:\\CODE\\Project\\tempmail\\moemail\\app\\components\\ui\\dropdown-menu.tsx": "44", "F:\\CODE\\Project\\tempmail\\moemail\\app\\components\\ui\\input.tsx": "45", "F:\\CODE\\Project\\tempmail\\moemail\\app\\components\\ui\\label.tsx": "46", "F:\\CODE\\Project\\tempmail\\moemail\\app\\components\\ui\\logo.tsx": "47", "F:\\CODE\\Project\\tempmail\\moemail\\app\\components\\ui\\radio-group.tsx": "48", "F:\\CODE\\Project\\tempmail\\moemail\\app\\components\\ui\\select.tsx": "49", "F:\\CODE\\Project\\tempmail\\moemail\\app\\components\\ui\\switch.tsx": "50", "F:\\CODE\\Project\\tempmail\\moemail\\app\\components\\ui\\tabs.tsx": "51", "F:\\CODE\\Project\\tempmail\\moemail\\app\\components\\ui\\toast-action.tsx": "52", "F:\\CODE\\Project\\tempmail\\moemail\\app\\components\\ui\\toast.tsx": "53", "F:\\CODE\\Project\\tempmail\\moemail\\app\\components\\ui\\toaster.tsx": "54", "F:\\CODE\\Project\\tempmail\\moemail\\app\\components\\ui\\tooltip.tsx": "55", "F:\\CODE\\Project\\tempmail\\moemail\\app\\components\\ui\\use-toast.ts": "56", "F:\\CODE\\Project\\tempmail\\moemail\\app\\config\\email.ts": "57", "F:\\CODE\\Project\\tempmail\\moemail\\app\\config\\index.ts": "58", "F:\\CODE\\Project\\tempmail\\moemail\\app\\config\\webhook.ts": "59", "F:\\CODE\\Project\\tempmail\\moemail\\app\\fonts.ts": "60", "F:\\CODE\\Project\\tempmail\\moemail\\app\\hooks\\use-config.ts": "61", "F:\\CODE\\Project\\tempmail\\moemail\\app\\hooks\\use-copy.ts": "62", "F:\\CODE\\Project\\tempmail\\moemail\\app\\hooks\\use-role-permission.ts": "63", "F:\\CODE\\Project\\tempmail\\moemail\\app\\hooks\\use-throttle.ts": "64", "F:\\CODE\\Project\\tempmail\\moemail\\app\\hooks\\use-user-role.ts": "65", "F:\\CODE\\Project\\tempmail\\moemail\\app\\layout.tsx": "66", "F:\\CODE\\Project\\tempmail\\moemail\\app\\lib\\apiKey.ts": "67", "F:\\CODE\\Project\\tempmail\\moemail\\app\\lib\\auth.ts": "68", "F:\\CODE\\Project\\tempmail\\moemail\\app\\lib\\avatar.ts": "69", "F:\\CODE\\Project\\tempmail\\moemail\\app\\lib\\cursor.ts": "70", "F:\\CODE\\Project\\tempmail\\moemail\\app\\lib\\db.ts": "71", "F:\\CODE\\Project\\tempmail\\moemail\\app\\lib\\emailCredentials.ts": "72", "F:\\CODE\\Project\\tempmail\\moemail\\app\\lib\\emailVerification.ts": "73", "F:\\CODE\\Project\\tempmail\\moemail\\app\\lib\\jwt.ts": "74", "F:\\CODE\\Project\\tempmail\\moemail\\app\\lib\\permissions.ts": "75", "F:\\CODE\\Project\\tempmail\\moemail\\app\\lib\\schema.ts": "76", "F:\\CODE\\Project\\tempmail\\moemail\\app\\lib\\utils.ts": "77", "F:\\CODE\\Project\\tempmail\\moemail\\app\\lib\\validation.ts": "78", "F:\\CODE\\Project\\tempmail\\moemail\\app\\lib\\webhook.ts": "79", "F:\\CODE\\Project\\tempmail\\moemail\\app\\login\\page.tsx": "80", "F:\\CODE\\Project\\tempmail\\moemail\\app\\moe\\page.tsx": "81", "F:\\CODE\\Project\\tempmail\\moemail\\app\\page.tsx": "82", "F:\\CODE\\Project\\tempmail\\moemail\\app\\profile\\page.tsx": "83", "F:\\CODE\\Project\\tempmail\\moemail\\app\\providers.tsx": "84", "F:\\CODE\\Project\\tempmail\\moemail\\app\\types\\email.ts": "85", "F:\\CODE\\Project\\tempmail\\moemail\\app\\api\\email-credentials\\extract-user\\route.ts": "86", "F:\\CODE\\Project\\tempmail\\moemail\\app\\api\\email-credentials\\create\\route.ts": "87", "F:\\CODE\\Project\\tempmail\\moemail\\app\\api\\email-credentials\\[id]\\route.ts": "88", "F:\\CODE\\Project\\tempmail\\moemail\\app\\components\\emails\\extract-credential-button.tsx": "89"}, {"size": 2071, "mtime": 1748976427679, "results": "90", "hashOfConfig": "91"}, {"size": 2401, "mtime": 1748976427679, "results": "92", "hashOfConfig": "91"}, {"size": 819, "mtime": 1748976427681, "results": "93", "hashOfConfig": "91"}, {"size": 95, "mtime": 1748976427680, "results": "94", "hashOfConfig": "91"}, {"size": 1776, "mtime": 1748976427681, "results": "95", "hashOfConfig": "91"}, {"size": 2189, "mtime": 1748985905745, "results": "96", "hashOfConfig": "91"}, {"size": 3321, "mtime": 1748988753447, "results": "97", "hashOfConfig": "91"}, {"size": 2654, "mtime": 1748985871984, "results": "98", "hashOfConfig": "91"}, {"size": 1486, "mtime": 1749156050084, "results": "99", "hashOfConfig": "91"}, {"size": 3669, "mtime": 1749156119347, "results": "100", "hashOfConfig": "91"}, {"size": 3133, "mtime": 1748998238089, "results": "101", "hashOfConfig": "91"}, {"size": 4405, "mtime": 1748998528933, "results": "102", "hashOfConfig": "91"}, {"size": 3610, "mtime": 1748998549674, "results": "103", "hashOfConfig": "91"}, {"size": 1703, "mtime": 1748976427683, "results": "104", "hashOfConfig": "91"}, {"size": 1977, "mtime": 1748976427685, "results": "105", "hashOfConfig": "91"}, {"size": 1225, "mtime": 1748976427685, "results": "106", "hashOfConfig": "91"}, {"size": 1654, "mtime": 1748976427685, "results": "107", "hashOfConfig": "91"}, {"size": 1090, "mtime": 1748976427686, "results": "108", "hashOfConfig": "91"}, {"size": 12236, "mtime": 1748976427686, "results": "109", "hashOfConfig": "91"}, {"size": 1623, "mtime": 1748976427686, "results": "110", "hashOfConfig": "91"}, {"size": 3945, "mtime": 1748987402352, "results": "111", "hashOfConfig": "91"}, {"size": 6129, "mtime": 1748976427687, "results": "112", "hashOfConfig": "91"}, {"size": 10519, "mtime": 1748996554324, "results": "113", "hashOfConfig": "91"}, {"size": 9657, "mtime": 1748989436578, "results": "114", "hashOfConfig": "91"}, {"size": 6678, "mtime": 1748976427688, "results": "115", "hashOfConfig": "91"}, {"size": 6135, "mtime": 1748976427688, "results": "116", "hashOfConfig": "91"}, {"size": 1179, "mtime": 1748976427689, "results": "117", "hashOfConfig": "91"}, {"size": 685, "mtime": 1748976427689, "results": "118", "hashOfConfig": "91"}, {"size": 695, "mtime": 1748976427689, "results": "119", "hashOfConfig": "91"}, {"size": 639, "mtime": 1748976427690, "results": "120", "hashOfConfig": "91"}, {"size": 1286, "mtime": 1748976427690, "results": "121", "hashOfConfig": "91"}, {"size": 16824, "mtime": 1748976427691, "results": "122", "hashOfConfig": "91"}, {"size": 4628, "mtime": 1748976427691, "results": "123", "hashOfConfig": "91"}, {"size": 20903, "mtime": 1748988894731, "results": "124", "hashOfConfig": "91"}, {"size": 4530, "mtime": 1748996307340, "results": "125", "hashOfConfig": "91"}, {"size": 4881, "mtime": 1748976427692, "results": "126", "hashOfConfig": "91"}, {"size": 6693, "mtime": 1748976427692, "results": "127", "hashOfConfig": "91"}, {"size": 303, "mtime": 1748976427693, "results": "128", "hashOfConfig": "91"}, {"size": 682, "mtime": 1748976427693, "results": "129", "hashOfConfig": "91"}, {"size": 4564, "mtime": 1748976427693, "results": "130", "hashOfConfig": "91"}, {"size": 1829, "mtime": 1748976427693, "results": "131", "hashOfConfig": "91"}, {"size": 1953, "mtime": 1748976427695, "results": "132", "hashOfConfig": "91"}, {"size": 3927, "mtime": 1748976427695, "results": "133", "hashOfConfig": "91"}, {"size": 1820, "mtime": 1748976427696, "results": "134", "hashOfConfig": "91"}, {"size": 847, "mtime": 1748976427696, "results": "135", "hashOfConfig": "91"}, {"size": 607, "mtime": 1748976427696, "results": "136", "hashOfConfig": "91"}, {"size": 1846, "mtime": 1748976427696, "results": "137", "hashOfConfig": "91"}, {"size": 1524, "mtime": 1748976427697, "results": "138", "hashOfConfig": "91"}, {"size": 3587, "mtime": 1748976427697, "results": "139", "hashOfConfig": "91"}, {"size": 1188, "mtime": 1748976427697, "results": "140", "hashOfConfig": "91"}, {"size": 1949, "mtime": 1748976427698, "results": "141", "hashOfConfig": "91"}, {"size": 969, "mtime": 1748976427698, "results": "142", "hashOfConfig": "91"}, {"size": 4120, "mtime": 1748976427698, "results": "143", "hashOfConfig": "91"}, {"size": 1188, "mtime": 1748976427698, "results": "144", "hashOfConfig": "91"}, {"size": 1169, "mtime": 1748976427698, "results": "145", "hashOfConfig": "91"}, {"size": 4094, "mtime": 1748976427699, "results": "146", "hashOfConfig": "91"}, {"size": 214, "mtime": 1748976427699, "results": "147", "hashOfConfig": "91"}, {"size": 50, "mtime": 1748976427699, "results": "148", "hashOfConfig": "91"}, {"size": 293, "mtime": 1748976427700, "results": "149", "hashOfConfig": "91"}, {"size": 163, "mtime": 1748976427700, "results": "150", "hashOfConfig": "91"}, {"size": 1578, "mtime": 1748976427701, "results": "151", "hashOfConfig": "91"}, {"size": 872, "mtime": 1748976427701, "results": "152", "hashOfConfig": "91"}, {"size": 605, "mtime": 1748976427702, "results": "153", "hashOfConfig": "91"}, {"size": 406, "mtime": 1748976427702, "results": "154", "hashOfConfig": "91"}, {"size": 478, "mtime": 1748976427702, "results": "155", "hashOfConfig": "91"}, {"size": 3244, "mtime": 1748976427702, "results": "156", "hashOfConfig": "91"}, {"size": 1689, "mtime": 1748977664134, "results": "157", "hashOfConfig": "91"}, {"size": 6487, "mtime": 1748976427703, "results": "158", "hashOfConfig": "91"}, {"size": 1313, "mtime": 1748976427703, "results": "159", "hashOfConfig": "91"}, {"size": 409, "mtime": 1748976427703, "results": "160", "hashOfConfig": "91"}, {"size": 270, "mtime": 1748976427703, "results": "161", "hashOfConfig": "91"}, {"size": 7305, "mtime": 1748998253511, "results": "162", "hashOfConfig": "91"}, {"size": 5329, "mtime": 1748977542754, "results": "163", "hashOfConfig": "91"}, {"size": 3749, "mtime": 1748986388939, "results": "164", "hashOfConfig": "91"}, {"size": 1227, "mtime": 1748996276467, "results": "165", "hashOfConfig": "91"}, {"size": 7899, "mtime": 1748998018793, "results": "166", "hashOfConfig": "91"}, {"size": 685, "mtime": 1748976427705, "results": "167", "hashOfConfig": "91"}, {"size": 477, "mtime": 1748976427705, "results": "168", "hashOfConfig": "91"}, {"size": 1394, "mtime": 1748976427705, "results": "169", "hashOfConfig": "91"}, {"size": 502, "mtime": 1748976427706, "results": "170", "hashOfConfig": "91"}, {"size": 1008, "mtime": 1748976427706, "results": "171", "hashOfConfig": "91"}, {"size": 2435, "mtime": 1748976427706, "results": "172", "hashOfConfig": "91"}, {"size": 719, "mtime": 1748976427707, "results": "173", "hashOfConfig": "91"}, {"size": 227, "mtime": 1748976427707, "results": "174", "hashOfConfig": "91"}, {"size": 313, "mtime": 1748976427708, "results": "175", "hashOfConfig": "91"}, {"size": 2511, "mtime": 1748988779406, "results": "176", "hashOfConfig": "91"}, {"size": 1425, "mtime": 1748997634113, "results": "177", "hashOfConfig": "91"}, {"size": 1130, "mtime": 1748996613731, "results": "178", "hashOfConfig": "91"}, {"size": 5769, "mtime": 1748997363731, "results": "179", "hashOfConfig": "91"}, {"filePath": "180", "messages": "181", "suppressedMessages": "182", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "jdkrm1", {"filePath": "183", "messages": "184", "suppressedMessages": "185", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "186", "messages": "187", "suppressedMessages": "188", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "189", "messages": "190", "suppressedMessages": "191", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "192", "messages": "193", "suppressedMessages": "194", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "195", "messages": "196", "suppressedMessages": "197", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "198", "messages": "199", "suppressedMessages": "200", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "201", "messages": "202", "suppressedMessages": "203", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "204", "messages": "205", "suppressedMessages": "206", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "207", "messages": "208", "suppressedMessages": "209", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "210", "messages": "211", "suppressedMessages": "212", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "213", "messages": "214", "suppressedMessages": "215", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "216", "messages": "217", "suppressedMessages": "218", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "219", "messages": "220", "suppressedMessages": "221", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "222", "messages": "223", "suppressedMessages": "224", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "225", "messages": "226", "suppressedMessages": "227", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "228", "messages": "229", "suppressedMessages": "230", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "231", "messages": "232", "suppressedMessages": "233", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "234", "messages": "235", "suppressedMessages": "236", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "237", "messages": "238", "suppressedMessages": "239", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "240", "messages": "241", "suppressedMessages": "242", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "243", "messages": "244", "suppressedMessages": "245", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "246", "messages": "247", "suppressedMessages": "248", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "249", "messages": "250", "suppressedMessages": "251", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "252", "messages": "253", "suppressedMessages": "254", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "255", "messages": "256", "suppressedMessages": "257", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "258", "messages": "259", "suppressedMessages": "260", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "261", "messages": "262", "suppressedMessages": "263", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "264", "messages": "265", "suppressedMessages": "266", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "267", "messages": "268", "suppressedMessages": "269", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "270", "messages": "271", "suppressedMessages": "272", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "273", "messages": "274", "suppressedMessages": "275", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "276", "messages": "277", "suppressedMessages": "278", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "279", "messages": "280", "suppressedMessages": "281", "errorCount": 17, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "282", "messages": "283", "suppressedMessages": "284", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "285", "messages": "286", "suppressedMessages": "287", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "288", "messages": "289", "suppressedMessages": "290", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "291", "messages": "292", "suppressedMessages": "293", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "294", "messages": "295", "suppressedMessages": "296", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "297", "messages": "298", "suppressedMessages": "299", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "300", "messages": "301", "suppressedMessages": "302", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "303", "messages": "304", "suppressedMessages": "305", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "306", "messages": "307", "suppressedMessages": "308", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "309", "messages": "310", "suppressedMessages": "311", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "312", "messages": "313", "suppressedMessages": "314", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "315", "messages": "316", "suppressedMessages": "317", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "318", "messages": "319", "suppressedMessages": "320", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "321", "messages": "322", "suppressedMessages": "323", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "324", "messages": "325", "suppressedMessages": "326", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "327", "messages": "328", "suppressedMessages": "329", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "330", "messages": "331", "suppressedMessages": "332", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "333", "messages": "334", "suppressedMessages": "335", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "336", "messages": "337", "suppressedMessages": "338", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "339", "messages": "340", "suppressedMessages": "341", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "342", "messages": "343", "suppressedMessages": "344", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "345", "messages": "346", "suppressedMessages": "347", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "348", "messages": "349", "suppressedMessages": "350", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "351", "messages": "352", "suppressedMessages": "353", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "354", "messages": "355", "suppressedMessages": "356", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "357", "messages": "358", "suppressedMessages": "359", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "360", "messages": "361", "suppressedMessages": "362", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "363", "messages": "364", "suppressedMessages": "365", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "366", "messages": "367", "suppressedMessages": "368", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "369", "messages": "370", "suppressedMessages": "371", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "372", "messages": "373", "suppressedMessages": "374", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "375", "messages": "376", "suppressedMessages": "377", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "378", "messages": "379", "suppressedMessages": "380", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "381", "messages": "382", "suppressedMessages": "383", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "384", "messages": "385", "suppressedMessages": "386", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "387", "messages": "388", "suppressedMessages": "389", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "390", "messages": "391", "suppressedMessages": "392", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "393", "messages": "394", "suppressedMessages": "395", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "396", "messages": "397", "suppressedMessages": "398", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "399", "messages": "400", "suppressedMessages": "401", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "402", "messages": "403", "suppressedMessages": "404", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "405", "messages": "406", "suppressedMessages": "407", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "408", "messages": "409", "suppressedMessages": "410", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "411", "messages": "412", "suppressedMessages": "413", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "414", "messages": "415", "suppressedMessages": "416", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "417", "messages": "418", "suppressedMessages": "419", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "420", "messages": "421", "suppressedMessages": "422", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "423", "messages": "424", "suppressedMessages": "425", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "426", "messages": "427", "suppressedMessages": "428", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "429", "messages": "430", "suppressedMessages": "431", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "432", "messages": "433", "suppressedMessages": "434", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "435", "messages": "436", "suppressedMessages": "437", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "438", "messages": "439", "suppressedMessages": "440", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "441", "messages": "442", "suppressedMessages": "443", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "444", "messages": "445", "suppressedMessages": "446", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "F:\\CODE\\Project\\tempmail\\moemail\\app\\api\\api-keys\\route.ts", [], [], "F:\\CODE\\Project\\tempmail\\moemail\\app\\api\\api-keys\\[id]\\route.ts", [], [], "F:\\CODE\\Project\\tempmail\\moemail\\app\\api\\auth\\register\\route.ts", [], [], "F:\\CODE\\Project\\tempmail\\moemail\\app\\api\\auth\\[...auth]\\route.ts", [], [], "F:\\CODE\\Project\\tempmail\\moemail\\app\\api\\config\\route.ts", [], [], "F:\\CODE\\Project\\tempmail\\moemail\\app\\api\\email-credentials\\bindings\\route.ts", [], [], "F:\\CODE\\Project\\tempmail\\moemail\\app\\api\\email-credentials\\extract\\route.ts", [], [], "F:\\CODE\\Project\\tempmail\\moemail\\app\\api\\email-credentials\\route.ts", [], [], "F:\\CODE\\Project\\tempmail\\moemail\\app\\api\\emails\\bind\\route.ts", [], [], "F:\\CODE\\Project\\tempmail\\moemail\\app\\api\\emails\\generate\\route.ts", [], [], "F:\\CODE\\Project\\tempmail\\moemail\\app\\api\\emails\\route.ts", ["447", "448", "449"], [], "F:\\CODE\\Project\\tempmail\\moemail\\app\\api\\emails\\[id]\\route.ts", [], [], "F:\\CODE\\Project\\tempmail\\moemail\\app\\api\\emails\\[id]\\[messageId]\\route.ts", [], [], "F:\\CODE\\Project\\tempmail\\moemail\\app\\api\\roles\\init-emperor\\route.ts", [], [], "F:\\CODE\\Project\\tempmail\\moemail\\app\\api\\roles\\promote\\route.ts", [], [], "F:\\CODE\\Project\\tempmail\\moemail\\app\\api\\roles\\users\\route.ts", [], [], "F:\\CODE\\Project\\tempmail\\moemail\\app\\api\\webhook\\route.ts", [], [], "F:\\CODE\\Project\\tempmail\\moemail\\app\\api\\webhook\\test\\route.ts", [], [], "F:\\CODE\\Project\\tempmail\\moemail\\app\\components\\auth\\login-form.tsx", [], [], "F:\\CODE\\Project\\tempmail\\moemail\\app\\components\\auth\\sign-button.tsx", [], [], "F:\\CODE\\Project\\tempmail\\moemail\\app\\components\\emails\\bind-email-dialog.tsx", [], [], "F:\\CODE\\Project\\tempmail\\moemail\\app\\components\\emails\\create-dialog.tsx", [], [], "F:\\CODE\\Project\\tempmail\\moemail\\app\\components\\emails\\email-list.tsx", ["450"], [], "F:\\CODE\\Project\\tempmail\\moemail\\app\\components\\emails\\message-list.tsx", [], ["451"], "F:\\CODE\\Project\\tempmail\\moemail\\app\\components\\emails\\message-view.tsx", ["452"], [], "F:\\CODE\\Project\\tempmail\\moemail\\app\\components\\emails\\three-column-layout.tsx", [], [], "F:\\CODE\\Project\\tempmail\\moemail\\app\\components\\float-menu.tsx", [], [], "F:\\CODE\\Project\\tempmail\\moemail\\app\\components\\home\\action-button.tsx", [], [], "F:\\CODE\\Project\\tempmail\\moemail\\app\\components\\home\\feature-card.tsx", [], [], "F:\\CODE\\Project\\tempmail\\moemail\\app\\components\\layout\\header.tsx", [], [], "F:\\CODE\\Project\\tempmail\\moemail\\app\\components\\no-permission-dialog.tsx", [], [], "F:\\CODE\\Project\\tempmail\\moemail\\app\\components\\profile\\api-key-panel.tsx", ["453"], [], "F:\\CODE\\Project\\tempmail\\moemail\\app\\components\\profile\\config-panel.tsx", [], [], "F:\\CODE\\Project\\tempmail\\moemail\\app\\components\\profile\\email-credentials-panel.tsx", ["454", "455", "456", "457", "458", "459", "460", "461", "462", "463", "464", "465", "466", "467", "468", "469", "470", "471"], [], "F:\\CODE\\Project\\tempmail\\moemail\\app\\components\\profile\\profile-card.tsx", [], [], "F:\\CODE\\Project\\tempmail\\moemail\\app\\components\\profile\\promote-panel.tsx", [], [], "F:\\CODE\\Project\\tempmail\\moemail\\app\\components\\profile\\webhook-config.tsx", [], ["472", "473"], "F:\\CODE\\Project\\tempmail\\moemail\\app\\components\\theme\\theme-provider.tsx", [], [], "F:\\CODE\\Project\\tempmail\\moemail\\app\\components\\theme\\theme-toggle.tsx", [], [], "F:\\CODE\\Project\\tempmail\\moemail\\app\\components\\ui\\alert-dialog.tsx", [], [], "F:\\CODE\\Project\\tempmail\\moemail\\app\\components\\ui\\button.tsx", [], [], "F:\\CODE\\Project\\tempmail\\moemail\\app\\components\\ui\\card.tsx", [], [], "F:\\CODE\\Project\\tempmail\\moemail\\app\\components\\ui\\dialog.tsx", [], [], "F:\\CODE\\Project\\tempmail\\moemail\\app\\components\\ui\\dropdown-menu.tsx", [], [], "F:\\CODE\\Project\\tempmail\\moemail\\app\\components\\ui\\input.tsx", [], [], "F:\\CODE\\Project\\tempmail\\moemail\\app\\components\\ui\\label.tsx", [], [], "F:\\CODE\\Project\\tempmail\\moemail\\app\\components\\ui\\logo.tsx", [], [], "F:\\CODE\\Project\\tempmail\\moemail\\app\\components\\ui\\radio-group.tsx", [], [], "F:\\CODE\\Project\\tempmail\\moemail\\app\\components\\ui\\select.tsx", [], [], "F:\\CODE\\Project\\tempmail\\moemail\\app\\components\\ui\\switch.tsx", [], [], "F:\\CODE\\Project\\tempmail\\moemail\\app\\components\\ui\\tabs.tsx", [], [], "F:\\CODE\\Project\\tempmail\\moemail\\app\\components\\ui\\toast-action.tsx", [], [], "F:\\CODE\\Project\\tempmail\\moemail\\app\\components\\ui\\toast.tsx", [], [], "F:\\CODE\\Project\\tempmail\\moemail\\app\\components\\ui\\toaster.tsx", [], [], "F:\\CODE\\Project\\tempmail\\moemail\\app\\components\\ui\\tooltip.tsx", [], [], "F:\\CODE\\Project\\tempmail\\moemail\\app\\components\\ui\\use-toast.ts", [], [], "F:\\CODE\\Project\\tempmail\\moemail\\app\\config\\email.ts", [], [], "F:\\CODE\\Project\\tempmail\\moemail\\app\\config\\index.ts", [], [], "F:\\CODE\\Project\\tempmail\\moemail\\app\\config\\webhook.ts", [], [], "F:\\CODE\\Project\\tempmail\\moemail\\app\\fonts.ts", [], [], "F:\\CODE\\Project\\tempmail\\moemail\\app\\hooks\\use-config.ts", ["474"], [], "F:\\CODE\\Project\\tempmail\\moemail\\app\\hooks\\use-copy.ts", [], [], "F:\\CODE\\Project\\tempmail\\moemail\\app\\hooks\\use-role-permission.ts", [], [], "F:\\CODE\\Project\\tempmail\\moemail\\app\\hooks\\use-throttle.ts", [], [], "F:\\CODE\\Project\\tempmail\\moemail\\app\\hooks\\use-user-role.ts", [], [], "F:\\CODE\\Project\\tempmail\\moemail\\app\\layout.tsx", [], [], "F:\\CODE\\Project\\tempmail\\moemail\\app\\lib\\apiKey.ts", [], [], "F:\\CODE\\Project\\tempmail\\moemail\\app\\lib\\auth.ts", [], ["475"], "F:\\CODE\\Project\\tempmail\\moemail\\app\\lib\\avatar.ts", [], [], "F:\\CODE\\Project\\tempmail\\moemail\\app\\lib\\cursor.ts", [], [], "F:\\CODE\\Project\\tempmail\\moemail\\app\\lib\\db.ts", [], [], "F:\\CODE\\Project\\tempmail\\moemail\\app\\lib\\emailCredentials.ts", ["476"], [], "F:\\CODE\\Project\\tempmail\\moemail\\app\\lib\\emailVerification.ts", [], [], "F:\\CODE\\Project\\tempmail\\moemail\\app\\lib\\jwt.ts", [], [], "F:\\CODE\\Project\\tempmail\\moemail\\app\\lib\\permissions.ts", [], [], "F:\\CODE\\Project\\tempmail\\moemail\\app\\lib\\schema.ts", [], [], "F:\\CODE\\Project\\tempmail\\moemail\\app\\lib\\utils.ts", [], [], "F:\\CODE\\Project\\tempmail\\moemail\\app\\lib\\validation.ts", [], [], "F:\\CODE\\Project\\tempmail\\moemail\\app\\lib\\webhook.ts", [], [], "F:\\CODE\\Project\\tempmail\\moemail\\app\\login\\page.tsx", [], [], "F:\\CODE\\Project\\tempmail\\moemail\\app\\moe\\page.tsx", [], [], "F:\\CODE\\Project\\tempmail\\moemail\\app\\page.tsx", [], [], "F:\\CODE\\Project\\tempmail\\moemail\\app\\profile\\page.tsx", [], [], "F:\\CODE\\Project\\tempmail\\moemail\\app\\providers.tsx", [], [], "F:\\CODE\\Project\\tempmail\\moemail\\app\\types\\email.ts", [], [], "F:\\CODE\\Project\\tempmail\\moemail\\app\\api\\email-credentials\\extract-user\\route.ts", [], [], "F:\\CODE\\Project\\tempmail\\moemail\\app\\api\\email-credentials\\create\\route.ts", [], [], "F:\\CODE\\Project\\tempmail\\moemail\\app\\api\\email-credentials\\[id]\\route.ts", [], [], "F:\\CODE\\Project\\tempmail\\moemail\\app\\components\\emails\\extract-credential-button.tsx", ["477", "478"], [], {"ruleId": "479", "severity": 2, "message": "480", "line": 2, "column": 23, "nodeType": null, "messageId": "481", "endLine": 2, "endColumn": 25}, {"ruleId": "479", "severity": 2, "message": "482", "line": 2, "column": 27, "nodeType": null, "messageId": "481", "endLine": 2, "endColumn": 29}, {"ruleId": "479", "severity": 2, "message": "483", "line": 2, "column": 31, "nodeType": null, "messageId": "481", "endLine": 2, "endColumn": 34}, {"ruleId": "484", "severity": 1, "message": "485", "line": 150, "column": 6, "nodeType": "486", "endLine": 150, "endColumn": 15, "suggestions": "487"}, {"ruleId": "484", "severity": 1, "message": "488", "line": 214, "column": 6, "nodeType": "486", "endLine": 214, "endColumn": 16, "suggestions": "489", "suppressions": "490"}, {"ruleId": "484", "severity": 1, "message": "491", "line": 148, "column": 6, "nodeType": "486", "endLine": 148, "endColumn": 38, "suggestions": "492"}, {"ruleId": "484", "severity": 1, "message": "493", "line": 69, "column": 6, "nodeType": "486", "endLine": 69, "endColumn": 23, "suggestions": "494"}, {"ruleId": "479", "severity": 2, "message": "495", "line": 6, "column": 100, "nodeType": null, "messageId": "481", "endLine": 6, "endColumn": 105}, {"ruleId": "479", "severity": 2, "message": "496", "line": 18, "column": 10, "nodeType": null, "messageId": "481", "endLine": 18, "endColumn": 16}, {"ruleId": "479", "severity": 2, "message": "497", "line": 20, "column": 10, "nodeType": null, "messageId": "481", "endLine": 20, "endColumn": 16}, {"ruleId": "479", "severity": 2, "message": "498", "line": 20, "column": 18, "nodeType": null, "messageId": "481", "endLine": 20, "endColumn": 31}, {"ruleId": "479", "severity": 2, "message": "499", "line": 20, "column": 33, "nodeType": null, "messageId": "481", "endLine": 20, "endColumn": 43}, {"ruleId": "479", "severity": 2, "message": "500", "line": 20, "column": 45, "nodeType": null, "messageId": "481", "endLine": 20, "endColumn": 58}, {"ruleId": "479", "severity": 2, "message": "501", "line": 20, "column": 60, "nodeType": null, "messageId": "481", "endLine": 20, "endColumn": 71}, {"ruleId": "479", "severity": 2, "message": "502", "line": 24, "column": 10, "nodeType": null, "messageId": "481", "endLine": 24, "endColumn": 18}, {"ruleId": "479", "severity": 2, "message": "503", "line": 25, "column": 10, "nodeType": null, "messageId": "481", "endLine": 25, "endColumn": 20}, {"ruleId": "479", "severity": 2, "message": "504", "line": 25, "column": 22, "nodeType": null, "messageId": "481", "endLine": 25, "endColumn": 36}, {"ruleId": "479", "severity": 2, "message": "505", "line": 55, "column": 10, "nodeType": null, "messageId": "481", "endLine": 55, "endColumn": 30}, {"ruleId": "484", "severity": 1, "message": "506", "line": 111, "column": 6, "nodeType": "486", "endLine": 111, "endColumn": 33, "suggestions": "507"}, {"ruleId": "479", "severity": 2, "message": "508", "line": 113, "column": 11, "nodeType": null, "messageId": "481", "endLine": 113, "endColumn": 17}, {"ruleId": "479", "severity": 2, "message": "509", "line": 272, "column": 14, "nodeType": null, "messageId": "481", "endLine": 272, "endColumn": 19}, {"ruleId": "510", "severity": 2, "message": "511", "line": 444, "column": 27, "nodeType": "512", "messageId": "513", "suggestions": "514"}, {"ruleId": "510", "severity": 2, "message": "511", "line": 444, "column": 32, "nodeType": "512", "messageId": "513", "suggestions": "515"}, {"ruleId": "510", "severity": 2, "message": "511", "line": 550, "column": 56, "nodeType": "512", "messageId": "513", "suggestions": "516"}, {"ruleId": "510", "severity": 2, "message": "511", "line": 550, "column": 61, "nodeType": "512", "messageId": "513", "suggestions": "517"}, {"ruleId": "479", "severity": 2, "message": "518", "line": 69, "column": 14, "nodeType": null, "messageId": "481", "endLine": 69, "endColumn": 20, "suppressions": "519"}, {"ruleId": "479", "severity": 2, "message": "518", "line": 97, "column": 14, "nodeType": null, "messageId": "481", "endLine": 97, "endColumn": 20, "suppressions": "520"}, {"ruleId": "484", "severity": 1, "message": "521", "line": 59, "column": 6, "nodeType": "486", "endLine": 59, "endColumn": 35, "suggestions": "522"}, {"ruleId": "479", "severity": 2, "message": "509", "line": 112, "column": 18, "nodeType": null, "messageId": "481", "endLine": 112, "endColumn": 23, "suppressions": "523"}, {"ruleId": "479", "severity": 2, "message": "524", "line": 5, "column": 10, "nodeType": null, "messageId": "481", "endLine": 5, "endColumn": 16}, {"ruleId": "479", "severity": 2, "message": "525", "line": 13, "column": 3, "nodeType": null, "messageId": "481", "endLine": 13, "endColumn": 16}, {"ruleId": "479", "severity": 2, "message": "509", "line": 97, "column": 14, "nodeType": null, "messageId": "481", "endLine": 97, "endColumn": 19}, "@typescript-eslint/no-unused-vars", "'lt' is defined but never used.", "unusedVar", "'or' is defined but never used.", "'sql' is defined but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchEmails'. Either include it or remove the dependency array.", "ArrayExpression", ["526"], "React Hook useEffect has missing dependencies: 'fetchMessages' and 'startPolling'. Either include them or remove the dependency array.", ["527"], ["528"], "React Hook useEffect has a missing dependency: 'updateIframeContent'. Either include it or remove the dependency array.", ["529"], "React Hook useEffect has a missing dependency: 'fetchApiKeys'. Either include it or remove the dependency array.", ["530"], "'Check' is defined but never used.", "'Switch' is defined but never used.", "'Select' is defined but never used.", "'SelectContent' is defined but never used.", "'SelectItem' is defined but never used.", "'SelectTrigger' is defined but never used.", "'SelectValue' is defined but never used.", "'Checkbox' is defined but never used.", "'RadioGroup' is defined but never used.", "'RadioGroupItem' is defined but never used.", "'extractedCredentials' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchData'. Either include it or remove the dependency array.", ["531"], "'config' is assigned a value but never used.", "'error' is defined but never used.", "react/no-unescaped-entities", "`\"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`.", "JSXText", "unescapedEntityAlts", ["532", "533", "534", "535"], ["536", "537", "538", "539"], ["540", "541", "542", "543"], ["544", "545", "546", "547"], "'_error' is defined but never used.", ["548"], ["549"], "React Hook useEffect has a missing dependency: 'store'. Either include it or remove the dependency array.", ["550"], ["551"], "'nanoid' is defined but never used.", "'DialogTrigger' is defined but never used.", {"desc": "552", "fix": "553"}, {"desc": "554", "fix": "555"}, {"kind": "556", "justification": "557"}, {"desc": "558", "fix": "559"}, {"desc": "560", "fix": "561"}, {"desc": "562", "fix": "563"}, {"messageId": "564", "data": "565", "fix": "566", "desc": "567"}, {"messageId": "564", "data": "568", "fix": "569", "desc": "570"}, {"messageId": "564", "data": "571", "fix": "572", "desc": "573"}, {"messageId": "564", "data": "574", "fix": "575", "desc": "576"}, {"messageId": "564", "data": "577", "fix": "578", "desc": "567"}, {"messageId": "564", "data": "579", "fix": "580", "desc": "570"}, {"messageId": "564", "data": "581", "fix": "582", "desc": "573"}, {"messageId": "564", "data": "583", "fix": "584", "desc": "576"}, {"messageId": "564", "data": "585", "fix": "586", "desc": "567"}, {"messageId": "564", "data": "587", "fix": "588", "desc": "570"}, {"messageId": "564", "data": "589", "fix": "590", "desc": "573"}, {"messageId": "564", "data": "591", "fix": "592", "desc": "576"}, {"messageId": "564", "data": "593", "fix": "594", "desc": "567"}, {"messageId": "564", "data": "595", "fix": "596", "desc": "570"}, {"messageId": "564", "data": "597", "fix": "598", "desc": "573"}, {"messageId": "564", "data": "599", "fix": "600", "desc": "576"}, {"kind": "556", "justification": "557"}, {"kind": "556", "justification": "557"}, {"desc": "601", "fix": "602"}, {"kind": "556", "justification": "557"}, "Update the dependencies array to be: [fetchEmails, session]", {"range": "603", "text": "604"}, "Update the dependencies array to be: [email.id, fetchMessages, startPolling]", {"range": "605", "text": "606"}, "directive", "", "Update the dependencies array to be: [message.html, viewMode, theme, updateIframeContent]", {"range": "607", "text": "608"}, "Update the dependencies array to be: [canManageApiKey, fetchApiKeys]", {"range": "609", "text": "610"}, "Update the dependencies array to be: [canManageEmailCredentials, fetchData]", {"range": "611", "text": "612"}, "replaceWithAlt", {"alt": "613"}, {"range": "614", "text": "615"}, "Replace with `&quot;`.", {"alt": "616"}, {"range": "617", "text": "618"}, "Replace with `&ldquo;`.", {"alt": "619"}, {"range": "620", "text": "621"}, "Replace with `&#34;`.", {"alt": "622"}, {"range": "623", "text": "624"}, "Replace with `&rdquo;`.", {"alt": "613"}, {"range": "625", "text": "626"}, {"alt": "616"}, {"range": "627", "text": "628"}, {"alt": "619"}, {"range": "629", "text": "630"}, {"alt": "622"}, {"range": "631", "text": "632"}, {"alt": "613"}, {"range": "633", "text": "634"}, {"alt": "616"}, {"range": "635", "text": "636"}, {"alt": "619"}, {"range": "637", "text": "638"}, {"alt": "622"}, {"range": "639", "text": "640"}, {"alt": "613"}, {"range": "641", "text": "642"}, {"alt": "616"}, {"range": "643", "text": "644"}, {"alt": "619"}, {"range": "645", "text": "646"}, {"alt": "622"}, {"range": "647", "text": "648"}, "Update the dependencies array to be: [store, store.config, store.loading]", {"range": "649", "text": "650"}, [4490, 4499], "[fetchEmails, session]", [5856, 5866], "[email.id, fetchMessages, startPolling]", [4489, 4521], "[message.html, viewMode, theme, updateIframeContent]", [2104, 2121], "[canManage<PERSON><PERSON><PERSON><PERSON>, fetchApiKeys]", [3657, 3684], "[canManageEmailCredentials, fetchData]", "&quot;", [14174, 14237], "\n                    点击上方的 &quot;绑定邮箱\" 按钮来绑定第一个邮箱\n                  ", "&ldquo;", [14174, 14237], "\n                    点击上方的 &ldquo;绑定邮箱\" 按钮来绑定第一个邮箱\n                  ", "&#34;", [14174, 14237], "\n                    点击上方的 &#34;绑定邮箱\" 按钮来绑定第一个邮箱\n                  ", "&rdquo;", [14174, 14237], "\n                    点击上方的 &rdquo;绑定邮箱\" 按钮来绑定第一个邮箱\n                  ", [14174, 14237], "\n                    点击上方的 \"绑定邮箱&quot; 按钮来绑定第一个邮箱\n                  ", [14174, 14237], "\n                    点击上方的 \"绑定邮箱&ldquo; 按钮来绑定第一个邮箱\n                  ", [14174, 14237], "\n                    点击上方的 \"绑定邮箱&#34; 按钮来绑定第一个邮箱\n                  ", [14174, 14237], "\n                    点击上方的 \"绑定邮箱&rdquo; 按钮来绑定第一个邮箱\n                  ", [18965, 18997], " 获取邮箱凭证 → 点击&quot;绑定邮箱\" → 输入凭证 → 完成绑定", [18965, 18997], " 获取邮箱凭证 → 点击&ldquo;绑定邮箱\" → 输入凭证 → 完成绑定", [18965, 18997], " 获取邮箱凭证 → 点击&#34;绑定邮箱\" → 输入凭证 → 完成绑定", [18965, 18997], " 获取邮箱凭证 → 点击&rdquo;绑定邮箱\" → 输入凭证 → 完成绑定", [18965, 18997], " 获取邮箱凭证 → 点击\"绑定邮箱&quot; → 输入凭证 → 完成绑定", [18965, 18997], " 获取邮箱凭证 → 点击\"绑定邮箱&ldquo; → 输入凭证 → 完成绑定", [18965, 18997], " 获取邮箱凭证 → 点击\"绑定邮箱&#34; → 输入凭证 → 完成绑定", [18965, 18997], " 获取邮箱凭证 → 点击\"绑定邮箱&rdquo; → 输入凭证 → 完成绑定", [1502, 1531], "[store, store.config, store.loading]"]