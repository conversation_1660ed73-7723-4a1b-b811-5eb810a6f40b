"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[275],{1626:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(7401).A)("Github",[["path",{d:"M15 22v-4a4.8 4.8 0 0 0-1-3.5c3 0 6-2 6-5.5.08-1.25-.27-2.48-1-3.5.28-1.15.28-2.35 0-3.5 0 0-1 0-3 1.5-2.64-.5-5.36-.5-8 0C6 2 5 2 5 2c-.3 1.15-.3 2.35 0 3.5A5.403 5.403 0 0 0 4 9c0 3.5 3 5.5 6 5.5-.39.49-.68 1.05-.85 1.65-.17.6-.22 1.23-.15 1.85v4",key:"tonef"}],["path",{d:"M9 18c-4.51 2-5-2-7-2",key:"9comsn"}]])},9674:(e,t,r)=>{r.d(t,{lg:()=>m,qW:()=>p,bL:()=>y});var n,o=r(2115),i=r(3610),l=r(3360),a=r(8068),s=r(1524),u=r(5155),d="dismissableLayer.update",c=o.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),p=o.forwardRef((e,t)=>{var r,p;let{disableOutsidePointerEvents:v=!1,onEscapeKeyDown:y,onPointerDownOutside:m,onFocusOutside:g,onInteractOutside:x,onDismiss:w,...b}=e,E=o.useContext(c),[C,T]=o.useState(null),P=null!==(p=null==C?void 0:C.ownerDocument)&&void 0!==p?p:null===(r=globalThis)||void 0===r?void 0:r.document,[,L]=o.useState({}),k=(0,a.s)(t,e=>T(e)),j=Array.from(E.layers),[R]=[...E.layersWithOutsidePointerEventsDisabled].slice(-1),A=j.indexOf(R),O=C?j.indexOf(C):-1,D=E.layersWithOutsidePointerEventsDisabled.size>0,N=O>=A,S=function(e){var t;let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null===(t=globalThis)||void 0===t?void 0:t.document,n=(0,s.c)(e),i=o.useRef(!1),l=o.useRef(()=>{});return o.useEffect(()=>{let e=e=>{if(e.target&&!i.current){let t=function(){h("dismissableLayer.pointerDownOutside",n,o,{discrete:!0})},o={originalEvent:e};"touch"===e.pointerType?(r.removeEventListener("click",l.current),l.current=t,r.addEventListener("click",l.current,{once:!0})):t()}else r.removeEventListener("click",l.current);i.current=!1},t=window.setTimeout(()=>{r.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(t),r.removeEventListener("pointerdown",e),r.removeEventListener("click",l.current)}},[r,n]),{onPointerDownCapture:()=>i.current=!0}}(e=>{let t=e.target,r=[...E.branches].some(e=>e.contains(t));!N||r||(null==m||m(e),null==x||x(e),e.defaultPrevented||null==w||w())},P),_=function(e){var t;let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null===(t=globalThis)||void 0===t?void 0:t.document,n=(0,s.c)(e),i=o.useRef(!1);return o.useEffect(()=>{let e=e=>{e.target&&!i.current&&h("dismissableLayer.focusOutside",n,{originalEvent:e},{discrete:!1})};return r.addEventListener("focusin",e),()=>r.removeEventListener("focusin",e)},[r,n]),{onFocusCapture:()=>i.current=!0,onBlurCapture:()=>i.current=!1}}(e=>{let t=e.target;[...E.branches].some(e=>e.contains(t))||(null==g||g(e),null==x||x(e),e.defaultPrevented||null==w||w())},P);return!function(e,t=globalThis?.document){let r=(0,s.c)(e);o.useEffect(()=>{let e=e=>{"Escape"===e.key&&r(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[r,t])}(e=>{O!==E.layers.size-1||(null==y||y(e),!e.defaultPrevented&&w&&(e.preventDefault(),w()))},P),o.useEffect(()=>{if(C)return v&&(0===E.layersWithOutsidePointerEventsDisabled.size&&(n=P.body.style.pointerEvents,P.body.style.pointerEvents="none"),E.layersWithOutsidePointerEventsDisabled.add(C)),E.layers.add(C),f(),()=>{v&&1===E.layersWithOutsidePointerEventsDisabled.size&&(P.body.style.pointerEvents=n)}},[C,P,v,E]),o.useEffect(()=>()=>{C&&(E.layers.delete(C),E.layersWithOutsidePointerEventsDisabled.delete(C),f())},[C,E]),o.useEffect(()=>{let e=()=>L({});return document.addEventListener(d,e),()=>document.removeEventListener(d,e)},[]),(0,u.jsx)(l.sG.div,{...b,ref:k,style:{pointerEvents:D?N?"auto":"none":void 0,...e.style},onFocusCapture:(0,i.m)(e.onFocusCapture,_.onFocusCapture),onBlurCapture:(0,i.m)(e.onBlurCapture,_.onBlurCapture),onPointerDownCapture:(0,i.m)(e.onPointerDownCapture,S.onPointerDownCapture)})});p.displayName="DismissableLayer";var v=o.forwardRef((e,t)=>{let r=o.useContext(c),n=o.useRef(null),i=(0,a.s)(t,n);return o.useEffect(()=>{let e=n.current;if(e)return r.branches.add(e),()=>{r.branches.delete(e)}},[r.branches]),(0,u.jsx)(l.sG.div,{...e,ref:i})});function f(){let e=new CustomEvent(d);document.dispatchEvent(e)}function h(e,t,r,n){let{discrete:o}=n,i=r.originalEvent.target,a=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:r});t&&i.addEventListener(e,t,{once:!0}),o?(0,l.hO)(i,a):i.dispatchEvent(a)}v.displayName="DismissableLayerBranch";var y=p,m=v},7323:(e,t,r)=>{r.d(t,{Z:()=>s});var n=r(2115),o=r(7650),i=r(3360),l=r(6611),a=r(5155),s=n.forwardRef((e,t)=>{var r,s;let{container:u,...d}=e,[c,p]=n.useState(!1);(0,l.N)(()=>p(!0),[]);let v=u||c&&(null===(s=globalThis)||void 0===s?void 0:null===(r=s.document)||void 0===r?void 0:r.body);return v?o.createPortal((0,a.jsx)(i.sG.div,{...d,ref:t}),v):null});s.displayName="Portal"},2614:(e,t,r)=>{r.d(t,{UC:()=>ec,Kq:()=>es,bL:()=>eu,l9:()=>ed});var n=r(2115),o=r(3610),i=r(8068),l=r(8166),a=r(9674),s=r(7668),u=r(6932),d=r(7205),c=r(3360),p=r(5155),v=n.forwardRef((e,t)=>{let{children:r,width:n=10,height:o=5,...i}=e;return(0,p.jsx)(c.sG.svg,{...i,ref:t,width:n,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?r:(0,p.jsx)("polygon",{points:"0,0 30,0 15,10"})})});v.displayName="Arrow";var f=r(1524),h=r(6611),y=r(7510),m="Popper",[g,x]=(0,l.A)(m),[w,b]=g(m),E=e=>{let{__scopePopper:t,children:r}=e,[o,i]=n.useState(null);return(0,p.jsx)(w,{scope:t,anchor:o,onAnchorChange:i,children:r})};E.displayName=m;var C="PopperAnchor",T=n.forwardRef((e,t)=>{let{__scopePopper:r,virtualRef:o,...l}=e,a=b(C,r),s=n.useRef(null),u=(0,i.s)(t,s);return n.useEffect(()=>{a.onAnchorChange((null==o?void 0:o.current)||s.current)}),o?null:(0,p.jsx)(c.sG.div,{...l,ref:u})});T.displayName=C;var P="PopperContent",[L,k]=g(P),j=n.forwardRef((e,t)=>{var r,o,l,a,s,v,m,g;let{__scopePopper:x,side:w="bottom",sideOffset:E=0,align:C="center",alignOffset:T=0,arrowPadding:k=0,avoidCollisions:j=!0,collisionBoundary:R=[],collisionPadding:A=0,sticky:O="partial",hideWhenDetached:_=!1,updatePositionStrategy:B="optimized",onPlaced:M,...W}=e,F=b(P,x),[G,H]=n.useState(null),I=(0,i.s)(t,e=>H(e)),[z,Y]=n.useState(null),X=(0,y.X)(z),U=null!==(m=null==X?void 0:X.width)&&void 0!==m?m:0,q=null!==(g=null==X?void 0:X.height)&&void 0!==g?g:0,V="number"==typeof A?A:{top:0,right:0,bottom:0,left:0,...A},K=Array.isArray(R)?R:[R],Z=K.length>0,J={padding:V,boundary:K.filter(D),altBoundary:Z},{refs:Q,floatingStyles:$,placement:ee,isPositioned:et,middlewareData:er}=(0,u.we)({strategy:"fixed",placement:w+("center"!==C?"-"+C:""),whileElementsMounted:function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,d.ll)(...t,{animationFrame:"always"===B})},elements:{reference:F.anchor},middleware:[(0,u.cY)({mainAxis:E+q,alignmentAxis:T}),j&&(0,u.BN)({mainAxis:!0,crossAxis:!1,limiter:"partial"===O?(0,u.ER)():void 0,...J}),j&&(0,u.UU)({...J}),(0,u.Ej)({...J,apply:e=>{let{elements:t,rects:r,availableWidth:n,availableHeight:o}=e,{width:i,height:l}=r.reference,a=t.floating.style;a.setProperty("--radix-popper-available-width","".concat(n,"px")),a.setProperty("--radix-popper-available-height","".concat(o,"px")),a.setProperty("--radix-popper-anchor-width","".concat(i,"px")),a.setProperty("--radix-popper-anchor-height","".concat(l,"px"))}}),z&&(0,u.UE)({element:z,padding:k}),N({arrowWidth:U,arrowHeight:q}),_&&(0,u.jD)({strategy:"referenceHidden",...J})]}),[en,eo]=S(ee),ei=(0,f.c)(M);(0,h.N)(()=>{et&&(null==ei||ei())},[et,ei]);let el=null===(r=er.arrow)||void 0===r?void 0:r.x,ea=null===(o=er.arrow)||void 0===o?void 0:o.y,es=(null===(l=er.arrow)||void 0===l?void 0:l.centerOffset)!==0,[eu,ed]=n.useState();return(0,h.N)(()=>{G&&ed(window.getComputedStyle(G).zIndex)},[G]),(0,p.jsx)("div",{ref:Q.setFloating,"data-radix-popper-content-wrapper":"",style:{...$,transform:et?$.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:eu,"--radix-popper-transform-origin":[null===(a=er.transformOrigin)||void 0===a?void 0:a.x,null===(s=er.transformOrigin)||void 0===s?void 0:s.y].join(" "),...(null===(v=er.hide)||void 0===v?void 0:v.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,p.jsx)(L,{scope:x,placedSide:en,onArrowChange:Y,arrowX:el,arrowY:ea,shouldHideArrow:es,children:(0,p.jsx)(c.sG.div,{"data-side":en,"data-align":eo,...W,ref:I,style:{...W.style,animation:et?void 0:"none"}})})})});j.displayName=P;var R="PopperArrow",A={top:"bottom",right:"left",bottom:"top",left:"right"},O=n.forwardRef(function(e,t){let{__scopePopper:r,...n}=e,o=k(R,r),i=A[o.placedSide];return(0,p.jsx)("span",{ref:o.onArrowChange,style:{position:"absolute",left:o.arrowX,top:o.arrowY,[i]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[o.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[o.placedSide],visibility:o.shouldHideArrow?"hidden":void 0},children:(0,p.jsx)(v,{...n,ref:t,style:{...n.style,display:"block"}})})});function D(e){return null!==e}O.displayName=R;var N=e=>({name:"transformOrigin",options:e,fn(t){var r,n,o,i,l;let{placement:a,rects:s,middlewareData:u}=t,d=(null===(r=u.arrow)||void 0===r?void 0:r.centerOffset)!==0,c=d?0:e.arrowWidth,p=d?0:e.arrowHeight,[v,f]=S(a),h={start:"0%",center:"50%",end:"100%"}[f],y=(null!==(i=null===(n=u.arrow)||void 0===n?void 0:n.x)&&void 0!==i?i:0)+c/2,m=(null!==(l=null===(o=u.arrow)||void 0===o?void 0:o.y)&&void 0!==l?l:0)+p/2,g="",x="";return"bottom"===v?(g=d?h:"".concat(y,"px"),x="".concat(-p,"px")):"top"===v?(g=d?h:"".concat(y,"px"),x="".concat(s.floating.height+p,"px")):"right"===v?(g="".concat(-p,"px"),x=d?h:"".concat(m,"px")):"left"===v&&(g="".concat(s.floating.width+p,"px"),x=d?h:"".concat(m,"px")),{data:{x:g,y:x}}}});function S(e){let[t,r="center"]=e.split("-");return[t,r]}r(7323);var _=r(7028),B=r(2317),M=r(1488),W=r(3543),[F,G]=(0,l.A)("Tooltip",[x]),H=x(),I="TooltipProvider",z="tooltip.open",[Y,X]=F(I),U=e=>{let{__scopeTooltip:t,delayDuration:r=700,skipDelayDuration:o=300,disableHoverableContent:i=!1,children:l}=e,[a,s]=n.useState(!0),u=n.useRef(!1),d=n.useRef(0);return n.useEffect(()=>{let e=d.current;return()=>window.clearTimeout(e)},[]),(0,p.jsx)(Y,{scope:t,isOpenDelayed:a,delayDuration:r,onOpen:n.useCallback(()=>{window.clearTimeout(d.current),s(!1)},[]),onClose:n.useCallback(()=>{window.clearTimeout(d.current),d.current=window.setTimeout(()=>s(!0),o)},[o]),isPointerInTransitRef:u,onPointerInTransitChange:n.useCallback(e=>{u.current=e},[]),disableHoverableContent:i,children:l})};U.displayName=I;var q="Tooltip",[V,K]=F(q),Z=e=>{let{__scopeTooltip:t,children:r,open:o,defaultOpen:i=!1,onOpenChange:l,disableHoverableContent:a,delayDuration:u}=e,d=X(q,e.__scopeTooltip),c=H(t),[v,f]=n.useState(null),h=(0,s.B)(),y=n.useRef(0),m=null!=a?a:d.disableHoverableContent,g=null!=u?u:d.delayDuration,x=n.useRef(!1),[w=!1,b]=(0,M.i)({prop:o,defaultProp:i,onChange:e=>{e?(d.onOpen(),document.dispatchEvent(new CustomEvent(z))):d.onClose(),null==l||l(e)}}),C=n.useMemo(()=>w?x.current?"delayed-open":"instant-open":"closed",[w]),T=n.useCallback(()=>{window.clearTimeout(y.current),y.current=0,x.current=!1,b(!0)},[b]),P=n.useCallback(()=>{window.clearTimeout(y.current),y.current=0,b(!1)},[b]),L=n.useCallback(()=>{window.clearTimeout(y.current),y.current=window.setTimeout(()=>{x.current=!0,b(!0),y.current=0},g)},[g,b]);return n.useEffect(()=>()=>{y.current&&(window.clearTimeout(y.current),y.current=0)},[]),(0,p.jsx)(E,{...c,children:(0,p.jsx)(V,{scope:t,contentId:h,open:w,stateAttribute:C,trigger:v,onTriggerChange:f,onTriggerEnter:n.useCallback(()=>{d.isOpenDelayed?L():T()},[d.isOpenDelayed,L,T]),onTriggerLeave:n.useCallback(()=>{m?P():(window.clearTimeout(y.current),y.current=0)},[P,m]),onOpen:T,onClose:P,disableHoverableContent:m,children:r})})};Z.displayName=q;var J="TooltipTrigger",Q=n.forwardRef((e,t)=>{let{__scopeTooltip:r,...l}=e,a=K(J,r),s=X(J,r),u=H(r),d=n.useRef(null),v=(0,i.s)(t,d,a.onTriggerChange),f=n.useRef(!1),h=n.useRef(!1),y=n.useCallback(()=>f.current=!1,[]);return n.useEffect(()=>()=>document.removeEventListener("pointerup",y),[y]),(0,p.jsx)(T,{asChild:!0,...u,children:(0,p.jsx)(c.sG.button,{"aria-describedby":a.open?a.contentId:void 0,"data-state":a.stateAttribute,...l,ref:v,onPointerMove:(0,o.m)(e.onPointerMove,e=>{"touch"===e.pointerType||h.current||s.isPointerInTransitRef.current||(a.onTriggerEnter(),h.current=!0)}),onPointerLeave:(0,o.m)(e.onPointerLeave,()=>{a.onTriggerLeave(),h.current=!1}),onPointerDown:(0,o.m)(e.onPointerDown,()=>{f.current=!0,document.addEventListener("pointerup",y,{once:!0})}),onFocus:(0,o.m)(e.onFocus,()=>{f.current||a.onOpen()}),onBlur:(0,o.m)(e.onBlur,a.onClose),onClick:(0,o.m)(e.onClick,a.onClose)})})});Q.displayName=J;var[$,ee]=F("TooltipPortal",{forceMount:void 0}),et="TooltipContent",er=n.forwardRef((e,t)=>{let r=ee(et,e.__scopeTooltip),{forceMount:n=r.forceMount,side:o="top",...i}=e,l=K(et,e.__scopeTooltip);return(0,p.jsx)(_.C,{present:n||l.open,children:l.disableHoverableContent?(0,p.jsx)(el,{side:o,...i,ref:t}):(0,p.jsx)(en,{side:o,...i,ref:t})})}),en=n.forwardRef((e,t)=>{let r=K(et,e.__scopeTooltip),o=X(et,e.__scopeTooltip),l=n.useRef(null),a=(0,i.s)(t,l),[s,u]=n.useState(null),{trigger:d,onClose:c}=r,v=l.current,{onPointerInTransitChange:f}=o,h=n.useCallback(()=>{u(null),f(!1)},[f]),y=n.useCallback((e,t)=>{let r=e.currentTarget,n={x:e.clientX,y:e.clientY},o=function(e,t){let r=Math.abs(t.top-e.y),n=Math.abs(t.bottom-e.y),o=Math.abs(t.right-e.x),i=Math.abs(t.left-e.x);switch(Math.min(r,n,o,i)){case i:return"left";case o:return"right";case r:return"top";case n:return"bottom";default:throw Error("unreachable")}}(n,r.getBoundingClientRect());u(function(e){let t=e.slice();return t.sort((e,t)=>e.x<t.x?-1:e.x>t.x?1:e.y<t.y?-1:e.y>t.y?1:0),function(e){if(e.length<=1)return e.slice();let t=[];for(let r=0;r<e.length;r++){let n=e[r];for(;t.length>=2;){let e=t[t.length-1],r=t[t.length-2];if((e.x-r.x)*(n.y-r.y)>=(e.y-r.y)*(n.x-r.x))t.pop();else break}t.push(n)}t.pop();let r=[];for(let t=e.length-1;t>=0;t--){let n=e[t];for(;r.length>=2;){let e=r[r.length-1],t=r[r.length-2];if((e.x-t.x)*(n.y-t.y)>=(e.y-t.y)*(n.x-t.x))r.pop();else break}r.push(n)}return(r.pop(),1===t.length&&1===r.length&&t[0].x===r[0].x&&t[0].y===r[0].y)?t:t.concat(r)}(t)}([...function(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:5,n=[];switch(t){case"top":n.push({x:e.x-r,y:e.y+r},{x:e.x+r,y:e.y+r});break;case"bottom":n.push({x:e.x-r,y:e.y-r},{x:e.x+r,y:e.y-r});break;case"left":n.push({x:e.x+r,y:e.y-r},{x:e.x+r,y:e.y+r});break;case"right":n.push({x:e.x-r,y:e.y-r},{x:e.x-r,y:e.y+r})}return n}(n,o),...function(e){let{top:t,right:r,bottom:n,left:o}=e;return[{x:o,y:t},{x:r,y:t},{x:r,y:n},{x:o,y:n}]}(t.getBoundingClientRect())])),f(!0)},[f]);return n.useEffect(()=>()=>h(),[h]),n.useEffect(()=>{if(d&&v){let e=e=>y(e,v),t=e=>y(e,d);return d.addEventListener("pointerleave",e),v.addEventListener("pointerleave",t),()=>{d.removeEventListener("pointerleave",e),v.removeEventListener("pointerleave",t)}}},[d,v,y,h]),n.useEffect(()=>{if(s){let e=e=>{let t=e.target,r={x:e.clientX,y:e.clientY},n=(null==d?void 0:d.contains(t))||(null==v?void 0:v.contains(t)),o=!function(e,t){let{x:r,y:n}=e,o=!1;for(let e=0,i=t.length-1;e<t.length;i=e++){let l=t[e].x,a=t[e].y,s=t[i].x,u=t[i].y;a>n!=u>n&&r<(s-l)*(n-a)/(u-a)+l&&(o=!o)}return o}(r,s);n?h():o&&(h(),c())};return document.addEventListener("pointermove",e),()=>document.removeEventListener("pointermove",e)}},[d,v,s,c,h]),(0,p.jsx)(el,{...e,ref:a})}),[eo,ei]=F(q,{isInside:!1}),el=n.forwardRef((e,t)=>{let{__scopeTooltip:r,children:o,"aria-label":i,onEscapeKeyDown:l,onPointerDownOutside:s,...u}=e,d=K(et,r),c=H(r),{onClose:v}=d;return n.useEffect(()=>(document.addEventListener(z,v),()=>document.removeEventListener(z,v)),[v]),n.useEffect(()=>{if(d.trigger){let e=e=>{let t=e.target;(null==t?void 0:t.contains(d.trigger))&&v()};return window.addEventListener("scroll",e,{capture:!0}),()=>window.removeEventListener("scroll",e,{capture:!0})}},[d.trigger,v]),(0,p.jsx)(a.qW,{asChild:!0,disableOutsidePointerEvents:!1,onEscapeKeyDown:l,onPointerDownOutside:s,onFocusOutside:e=>e.preventDefault(),onDismiss:v,children:(0,p.jsxs)(j,{"data-state":d.stateAttribute,...c,...u,ref:t,style:{...u.style,"--radix-tooltip-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-tooltip-content-available-width":"var(--radix-popper-available-width)","--radix-tooltip-content-available-height":"var(--radix-popper-available-height)","--radix-tooltip-trigger-width":"var(--radix-popper-anchor-width)","--radix-tooltip-trigger-height":"var(--radix-popper-anchor-height)"},children:[(0,p.jsx)(B.xV,{children:o}),(0,p.jsx)(eo,{scope:r,isInside:!0,children:(0,p.jsx)(W.b,{id:d.contentId,role:"tooltip",children:i||o})})]})})});er.displayName=et;var ea="TooltipArrow";n.forwardRef((e,t)=>{let{__scopeTooltip:r,...n}=e,o=H(r);return ei(ea,r).isInside?null:(0,p.jsx)(O,{...o,...n,ref:t})}).displayName=ea;var es=U,eu=Z,ed=Q,ec=er},3543:(e,t,r)=>{r.d(t,{b:()=>a,s:()=>l});var n=r(2115),o=r(3360),i=r(5155),l=n.forwardRef((e,t)=>(0,i.jsx)(o.sG.span,{...e,ref:t,style:{position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal",...e.style}}));l.displayName="VisuallyHidden";var a=l}}]);