(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[164],{65521:e=>{"use strict";e.exports=require("node:async_hooks")},25356:e=>{"use strict";e.exports=require("node:buffer")},29730:(e,t,i)=>{"use strict";i.r(t),i.d(t,{ComponentMod:()=>M,default:()=>O});var a,r={};i.r(r),i.d(r,{GET:()=>S,POST:()=>I,runtime:()=>E});var s={};i.r(s),i.d(s,{patchFetch:()=>_,routeModule:()=>h,serverHooks:()=>T,workAsyncStorage:()=>x,workUnitAsyncStorage:()=>C});var o=i(26312),n=i(35471),c=i(22680),l=i(65954),f=i(14139),u=i(43113),p=i(17451),m=i(69179),d=i(4908),g=i(83553);let E="edge";async function S(){let e=(0,m.getRequestContext)().env,[t,i,a,r]=await Promise.all([e.SITE_CONFIG.get("DEFAULT_ROLE"),e.SITE_CONFIG.get("EMAIL_DOMAINS"),e.SITE_CONFIG.get("ADMIN_CONTACT"),e.SITE_CONFIG.get("MAX_EMAILS")]);return Response.json({defaultRole:t||p.gg.CIVILIAN,emailDomains:i||"moemail.app",adminContact:a||"",maxEmails:r||d.q.MAX_ACTIVE_EMAILS.toString()})}async function I(e){if(!await (0,g.Yj)(p.Jj.MANAGE_CONFIG))return Response.json({error:"权限不足"},{status:403});let{defaultRole:t,emailDomains:i,adminContact:a,maxEmails:r}=await e.json();if(![p.gg.DUKE,p.gg.KNIGHT,p.gg.CIVILIAN].includes(t))return Response.json({error:"无效的角色"},{status:400});let s=(0,m.getRequestContext)().env;return await Promise.all([s.SITE_CONFIG.put("DEFAULT_ROLE",t),s.SITE_CONFIG.put("EMAIL_DOMAINS",i),s.SITE_CONFIG.put("ADMIN_CONTACT",a),s.SITE_CONFIG.put("MAX_EMAILS",r)]),Response.json({success:!0})}let h=new l.AppRouteRouteModule({definition:{kind:f.A.APP_ROUTE,page:"/api/config/route",pathname:"/api/config",filename:"route",bundlePath:"app/api/config/route"},resolvedPagePath:"F:\\CODE\\Project\\tempmail\\moemail\\app\\api\\config\\route.ts",nextConfigOutput:"",userland:r}),{workAsyncStorage:x,workUnitAsyncStorage:C,serverHooks:T}=h;function _(){return(0,u.V5)({workAsyncStorage:x,workUnitAsyncStorage:C})}let v=null==(a=self.__RSC_MANIFEST)?void 0:a["/api/config/route"],A=(e=>e?JSON.parse(e):void 0)(self.__RSC_SERVER_MANIFEST);v&&A&&(0,n.fQ)({page:"/api/config/route",clientReferenceManifest:v,serverActionsManifest:A,serverModuleMap:(0,o.e)({serverActionsManifest:A})});let M=s,O=c.s.wrap(h,{nextConfig:{env:{},eslint:{ignoreDuringBuilds:!1},typescript:{ignoreBuildErrors:!1,tsconfigPath:"tsconfig.json"},distDir:".next",cleanDistDir:!0,assetPrefix:"",cacheMaxMemorySize:0x3200000,configOrigin:"next.config.ts",useFileSystemPublicRoutes:!0,generateEtags:!0,pageExtensions:["tsx","ts","jsx","js"],poweredByHeader:!0,compress:!0,images:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",loaderFile:"",domains:[],disableStaticImages:!1,minimumCacheTTL:60,formats:["image/webp"],dangerouslyAllowSVG:!1,contentSecurityPolicy:"script-src 'none'; frame-src 'none'; sandbox;",contentDispositionType:"attachment",remotePatterns:[{protocol:"https",hostname:"avatars.githubusercontent.com"}],unoptimized:!1},devIndicators:{appIsrStatus:!0,buildActivity:!0,buildActivityPosition:"bottom-right"},onDemandEntries:{maxInactiveAge:6e4,pagesBufferLength:5},amp:{canonicalBase:""},basePath:"",sassOptions:{},trailingSlash:!1,i18n:null,productionBrowserSourceMaps:!1,excludeDefaultMomentLocales:!0,serverRuntimeConfig:{},publicRuntimeConfig:{},reactProductionProfiling:!1,reactStrictMode:null,reactMaxHeadersLength:6e3,httpAgentOptions:{keepAlive:!0},logging:{},expireTime:31536e3,staticPageGenerationTimeout:60,modularizeImports:{"@mui/icons-material":{transform:"@mui/icons-material/{{member}}"},lodash:{transform:"lodash/{{member}}"}},outputFileTracingRoot:"F:\\CODE\\Project\\tempmail\\moemail",experimental:{cacheLife:{default:{stale:300,revalidate:900,expire:0xfffffffe},seconds:{stale:0,revalidate:1,expire:60},minutes:{stale:300,revalidate:60,expire:3600},hours:{stale:300,revalidate:3600,expire:86400},days:{stale:300,revalidate:86400,expire:604800},weeks:{stale:300,revalidate:604800,expire:2592e3},max:{stale:300,revalidate:2592e3,expire:0xfffffffe}},cacheHandlers:{},cssChunking:!0,multiZoneDraftMode:!1,appNavFailHandling:!1,prerenderEarlyExit:!0,serverMinification:!0,serverSourceMaps:!1,linkNoTouchStart:!1,caseSensitiveRoutes:!1,clientSegmentCache:!1,preloadEntriesOnStart:!0,clientRouterFilter:!0,clientRouterFilterRedirects:!1,fetchCacheKeyPrefix:"",middlewarePrefetch:"flexible",optimisticClientCache:!0,manualClientBasePath:!1,cpus:11,memoryBasedWorkersCount:!1,imgOptConcurrency:null,imgOptTimeoutInSeconds:7,imgOptMaxInputPixels:0xfff8001,imgOptSequentialRead:null,isrFlushToDisk:!0,workerThreads:!1,optimizeCss:!1,nextScriptWorkers:!1,scrollRestoration:!1,externalDir:!1,disableOptimizedLoading:!1,gzipSize:!0,craCompat:!1,esmExternals:!0,fullySpecified:!1,swcTraceProfiling:!1,forceSwcTransforms:!1,largePageDataBytes:128e3,turbo:{root:"F:\\CODE\\Project\\tempmail\\moemail"},typedRoutes:!1,typedEnv:!1,parallelServerCompiles:!1,parallelServerBuildTraces:!1,ppr:!1,authInterrupts:!1,reactOwnerStack:!1,webpackMemoryOptimizations:!1,optimizeServerReact:!0,useEarlyImport:!1,staleTimes:{dynamic:0,static:300},serverComponentsHmrCache:!0,staticGenerationMaxConcurrency:8,staticGenerationMinPagesPerWorker:25,dynamicIO:!1,inlineCss:!1,optimizePackageImports:["lucide-react","date-fns","lodash-es","ramda","antd","react-bootstrap","ahooks","@ant-design/icons","@headlessui/react","@headlessui-float/react","@heroicons/react/20/solid","@heroicons/react/24/solid","@heroicons/react/24/outline","@visx/visx","@tremor/react","rxjs","@mui/material","@mui/icons-material","recharts","react-use","effect","@effect/schema","@effect/platform","@effect/platform-node","@effect/platform-browser","@effect/platform-bun","@effect/sql","@effect/sql-mssql","@effect/sql-mysql2","@effect/sql-pg","@effect/sql-squlite-node","@effect/sql-squlite-bun","@effect/sql-squlite-wasm","@effect/sql-squlite-react-native","@effect/rpc","@effect/rpc-http","@effect/typeclass","@effect/experimental","@effect/opentelemetry","@material-ui/core","@material-ui/icons","@tabler/icons-react","mui-core","react-icons/ai","react-icons/bi","react-icons/bs","react-icons/cg","react-icons/ci","react-icons/di","react-icons/fa","react-icons/fa6","react-icons/fc","react-icons/fi","react-icons/gi","react-icons/go","react-icons/gr","react-icons/hi","react-icons/hi2","react-icons/im","react-icons/io","react-icons/io5","react-icons/lia","react-icons/lib","react-icons/lu","react-icons/md","react-icons/pi","react-icons/ri","react-icons/rx","react-icons/si","react-icons/sl","react-icons/tb","react-icons/tfi","react-icons/ti","react-icons/vsc","react-icons/wi"]},bundlePagesRouterDependencies:!1,configFile:"F:\\CODE\\Project\\tempmail\\moemail\\next.config.ts",configFileName:"next.config.ts"}})},96487:()=>{},78335:()=>{},4908:(e,t,i)=>{"use strict";i.d(t,{q:()=>a,V:()=>r});let a={MAX_ACTIVE_EMAILS:30,POLL_INTERVAL:1e4},r={MAX_RETRIES:3,TIMEOUT:1e4,RETRY_DELAY:1e3,EVENTS:{NEW_MESSAGE:"new_message"}}}},e=>{var t=t=>e(e.s=t);e.O(0,[730,752,899,498,220,156],()=>t(29730));var i=e.O();(_ENTRIES="undefined"==typeof _ENTRIES?{}:_ENTRIES)["middleware_app/api/config/route"]=i}]);
//# sourceMappingURL=route.js.map