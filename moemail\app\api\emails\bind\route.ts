import { NextRequest, NextResponse } from "next/server"
import { auth } from "@/lib/auth"
import { db } from "@/lib/db"
import { emailCredentials, userEmailBindings } from "@/lib/schema"
import { eq, and } from "drizzle-orm"
import { decrypt } from "@/lib/emailCredentials"
import { nanoid } from "nanoid"

export const runtime = "edge"

export async function POST(request: NextRequest) {
  try {
    const session = await auth()
    if (!session?.user?.id) {
      return NextResponse.json({ error: "未授权" }, { status: 401 })
    }

    const { email, password } = await request.json()

    if (!email || !password) {
      return NextResponse.json({ error: "邮箱地址和密码都是必填项" }, { status: 400 })
    }

    // 查找匹配的邮箱凭证
    const credentials = await db
      .select()
      .from(emailCredentials)
      .where(and(
        eq(emailCredentials.email, email),
        eq(emailCredentials.enabled, true)
      ))

    if (credentials.length === 0) {
      return NextResponse.json({ 
        error: "未找到匹配的邮箱凭证，请联系管理员添加此邮箱的支持" 
      }, { status: 404 })
    }

    const credential = credentials[0]

    // 验证密码
    try {
      const decryptedPassword = decrypt(credential.encryptedPassword)
      if (decryptedPassword !== password) {
        return NextResponse.json({ error: "邮箱密码不正确" }, { status: 401 })
      }
    } catch (error) {
      console.error("密码解密失败:", error)
      return NextResponse.json({ error: "密码验证失败" }, { status: 500 })
    }

    // 检查是否已经绑定
    const existingBinding = await db
      .select()
      .from(userEmailBindings)
      .where(and(
        eq(userEmailBindings.userId, session.user.id),
        eq(userEmailBindings.email, email)
      ))

    if (existingBinding.length > 0) {
      return NextResponse.json({ error: "此邮箱已经绑定" }, { status: 409 })
    }

    // 创建绑定记录
    const bindingId = nanoid()
    await db.insert(userEmailBindings).values({
      id: bindingId,
      userId: session.user.id,
      credentialId: credential.id,
      email: email,
      createdAt: new Date(),
      lastSyncAt: null,
      enabled: true
    })

    // 更新凭证的最后使用时间
    await db
      .update(emailCredentials)
      .set({ lastUsed: new Date() })
      .where(eq(emailCredentials.id, credential.id))

    return NextResponse.json({ 
      success: true,
      message: "邮箱绑定成功",
      bindingId 
    })

  } catch (error) {
    console.error("绑定邮箱失败:", error)
    return NextResponse.json({ 
      error: "绑定邮箱失败，请稍后重试" 
    }, { status: 500 })
  }
}
