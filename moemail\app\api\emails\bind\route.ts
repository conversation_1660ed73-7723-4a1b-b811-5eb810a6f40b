import { NextRequest, NextResponse } from "next/server"
import { auth } from "@/lib/auth"
import { EmailCredentialManager } from "@/lib/emailCredentials"
import { z } from "zod"

export const runtime = "edge"

const bindEmailSchema = z.object({
  jwt: z.string().min(1, "邮箱凭证不能为空"),
})

export async function POST(request: NextRequest) {
  try {
    const session = await auth()
    if (!session?.user?.id) {
      return NextResponse.json({ error: "未授权" }, { status: 401 })
    }

    const body = await request.json()
    const validatedData = bindEmailSchema.parse(body)

    // 使用邮箱凭证管理器进行绑定
    const binding = await EmailCredentialManager.bindEmailByCredential(
      session.user.id,
      validatedData.jwt
    )

    return NextResponse.json({
      success: true,
      message: "邮箱绑定成功",
      binding: {
        id: binding.id,
        emailAddress: binding.emailAddress,
        createdAt: binding.createdAt.toISOString()
      }
    })

  } catch (error) {
    console.error("绑定邮箱失败:", error)

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: error.errors[0].message },
        { status: 400 }
      )
    }

    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      )
    }

    return NextResponse.json({
      error: "绑定邮箱失败，请稍后重试"
    }, { status: 500 })
  }
}
