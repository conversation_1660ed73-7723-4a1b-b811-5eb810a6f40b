# 邮箱凭证功能部署指南

本文档说明了在添加邮箱凭证功能后的部署变动和注意事项。

## 🔄 部署变动总结

### ✅ 无需变动的配置

- **环境变量**：现有的 `AUTH_SECRET` 足够使用，无需额外配置
- **Cloudflare配置**：API Token、Account ID、D1数据库、KV存储等配置保持不变
- **部署脚本**：现有的GitHub Actions和部署脚本完全兼容
- **域名和网络配置**：无需修改

### 📋 必需的数据库迁移

添加了以下新表，需要执行数据库迁移：

1. **email_credentials** - 邮箱凭证表
2. **user_email_bindings** - 用户邮箱绑定表  
3. **email_verification_codes** - 邮箱验证码表

### 🚀 部署步骤

#### 方式一：自动部署（推荐）

如果你使用现有的GitHub Actions自动部署：

1. **推送代码**到仓库
2. **创建tag**触发部署：
   ```bash
   git tag v1.1.0-email-credentials
   git push origin v1.1.0-email-credentials
   ```
3. **等待部署完成** - 部署脚本会自动执行数据库迁移

#### 方式二：手动部署

1. **更新代码**：
   ```bash
   git pull origin main
   pnpm install
   ```

2. **执行数据库迁移**：
   ```bash
   # 本地测试
   pnpm db:migrate-local
   
   # 生产环境
   pnpm db:migrate-remote
   ```

3. **部署应用**：
   ```bash
   pnpm dlx tsx scripts/deploy/index.ts
   ```

### 🔧 可选配置

#### JWT密钥配置

虽然不是必需的，但可以设置专用的JWT密钥：

```bash
# 在GitHub Secrets中添加（可选）
JWT_SECRET=your-jwt-secret-here
```

如果不设置，系统会自动使用现有的 `AUTH_SECRET`。

#### 邮件发送服务配置

为了发送邮箱验证码，可以配置邮件服务：

```bash
# 使用Resend服务（推荐）
RESEND_API_KEY=your-resend-api-key

# 或使用SMTP服务
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
```

**注意**：如果不配置邮件服务，验证码会输出到控制台日志中（仅用于开发测试）。

### 📊 新功能验证

部署完成后，可以通过以下方式验证邮箱凭证功能：

1. **登录管理面板**
2. **访问"API密钥"页面** - 应该能看到新的"邮箱凭证"选项卡
3. **创建邮箱凭证**：
   - 选择一个已创建的邮箱地址
   - 发送验证码
   - 输入验证码创建凭证
4. **测试API访问**：
   ```bash
   curl -H "X-Email-Credential: your-jwt-token" \
        https://your-domain.com/api/emails
   ```

### 🔍 故障排除

#### 数据库迁移失败

如果迁移失败，可以手动执行SQL：

```bash
# 连接到D1数据库
wrangler d1 execute your-database-name --file=drizzle/email-credentials-migration.sql
```

#### JWT验证失败

检查以下配置：

1. 确保 `AUTH_SECRET` 环境变量已设置
2. 检查JWT token格式是否正确
3. 验证token是否已过期

#### 邮件发送失败

1. 检查邮件服务配置
2. 验证API密钥是否有效
3. 查看控制台日志获取详细错误信息

### 📈 性能考虑

- **JWT验证**：在边缘计算环境中性能优异
- **数据库查询**：新增的索引确保查询性能
- **缓存策略**：JWT token自带过期时间，减少数据库查询

### 🔒 安全注意事项

1. **JWT密钥安全**：确保 `AUTH_SECRET` 足够复杂且保密
2. **验证码有效期**：默认5分钟，可根据需要调整
3. **权限控制**：邮箱凭证只能访问邮件相关API
4. **审计日志**：系统会记录凭证的使用时间
5. **凭证时效**：邮箱凭证JWT**永久有效**（与cloudflare_temp_email保持一致），但可以通过管理面板禁用或删除

### 📞 技术支持

如果在部署过程中遇到问题：

1. 检查GitHub Actions日志
2. 查看Cloudflare Workers日志
3. 参考现有的部署文档
4. 提交Issue到项目仓库

---

**总结**：邮箱凭证功能的添加对现有部署流程影响最小，主要是数据库结构的扩展。现有的环境变量和配置完全兼容，只需要执行一次数据库迁移即可。
