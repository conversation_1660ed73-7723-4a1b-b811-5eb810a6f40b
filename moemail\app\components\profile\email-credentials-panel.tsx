"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Mail, Plus, Loader2, Trash2, Eye, EyeOff, ChevronDown, ChevronUp, Users, <PERSON>fresh<PERSON><PERSON>, <PERSON><PERSON>, <PERSON> } from "lucide-react"
import { useToast } from "@/components/ui/use-toast"
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogFooter,
  DialogDescription,
  DialogClose,
} from "@/components/ui/dialog"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { useRolePermission } from "@/hooks/use-role-permission"
import { PERMISSIONS } from "@/lib/permissions"
import { useConfig } from "@/hooks/use-config"
import { Checkbox } from "@/components/ui/checkbox"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"

type EmailCredential = {
  id: string
  emailAddress: string
  jwt: string
  enabled: boolean
  createdAt: string
  lastUsedAt: string | null
  bindingCount: number
}

type UserEmailBinding = {
  id: string
  emailAddress: string
  createdAt: string
  enabled: boolean
}

export function EmailCredentialsPanel() {
  const [credentials, setCredentials] = useState<EmailCredential[]>([])
  const [userBindings, setUserBindings] = useState<UserEmailBinding[]>([])
  const [loading, setLoading] = useState(false)
  const [bindDialogOpen, setBindDialogOpen] = useState(false)
  const [extractDialogOpen, setExtractDialogOpen] = useState(false)
  const [newBinding, setNewBinding] = useState({
    jwt: ""
  })
  const [extractEmail, setExtractEmail] = useState("")
  const [extractedCredential, setExtractedCredential] = useState<EmailCredential | null>(null)
  const [extractedCredentials, setExtractedCredentials] = useState<EmailCredential[]>([])
  const [selectedEmails, setSelectedEmails] = useState<string[]>([])
  const [extractMode, setExtractMode] = useState<'input' | 'select'>('input')
  const [showJWT, setShowJWT] = useState(false)
  const [showExamples, setShowExamples] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const [refreshing, setRefreshing] = useState(false)
  const { toast } = useToast()
  const { checkPermission } = useRolePermission()
  const canManageEmailCredentials = checkPermission(PERMISSIONS.MANAGE_WEBHOOK)

  const fetchCredentials = async () => {
    if (!canManageEmailCredentials) return
    try {
      const res = await fetch("/api/email-credentials")
      if (!res.ok) throw new Error("获取邮箱凭证失败")
      const data = await res.json() as { credentials: EmailCredential[] }
      setCredentials(data.credentials)
    } catch (error) {
      console.error(error)
      toast({
        title: "获取失败",
        description: "获取邮箱凭证列表失败",
        variant: "destructive"
      })
    }
  }

  const fetchUserBindings = async () => {
    try {
      const res = await fetch("/api/email-credentials/bindings")
      if (!res.ok) throw new Error("获取邮箱绑定失败")
      const data = await res.json() as { bindings: UserEmailBinding[] }
      setUserBindings(data.bindings)
    } catch (error) {
      console.error(error)
      toast({
        title: "获取失败",
        description: "获取邮箱绑定列表失败",
        variant: "destructive"
      })
    }
  }

  const fetchData = async () => {
    setIsLoading(true)
    await Promise.all([
      canManageEmailCredentials ? fetchCredentials() : Promise.resolve(),
      fetchUserBindings()
    ])
    setIsLoading(false)
    setRefreshing(false)
  }

  useEffect(() => {
    fetchData()
  }, [canManageEmailCredentials])

  const { config } = useConfig()

  const handleRefresh = async () => {
    setRefreshing(true)
    await fetchData()
  }

  const bindEmail = async () => {
    if (!newBinding.jwt.trim()) {
      toast({
        title: "请输入邮箱凭证",
        description: "邮箱凭证不能为空",
        variant: "destructive"
      })
      return
    }

    setLoading(true)
    try {
      const res = await fetch("/api/email-credentials", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(newBinding)
      })

      if (!res.ok) {
        const error = await res.json()
        throw new Error(error.error || "绑定邮箱失败")
      }

      toast({
        title: "绑定成功",
        description: "邮箱绑定成功"
      })

      fetchUserBindings()
      handleBindDialogClose()
    } catch (error) {
      toast({
        title: "绑定失败",
        description: error instanceof Error ? error.message : "请稍后重试",
        variant: "destructive"
      })
    } finally {
      setLoading(false)
    }
  }

  const extractCredential = async () => {
    if (extractMode === 'input') {
      if (!extractEmail.trim()) {
        toast({
          title: "请输入邮箱地址",
          description: "邮箱地址不能为空",
          variant: "destructive"
        })
        return
      }
    } else {
      if (selectedEmails.length === 0) {
        toast({
          title: "请选择邮箱",
          description: "至少选择一个邮箱",
          variant: "destructive"
        })
        return
      }
    }

    setLoading(true)
    try {
      const endpoint = canManageEmailCredentials ? "/api/email-credentials/extract" : "/api/email-credentials/extract-user"
      const requestBody = extractMode === 'input'
        ? { emailAddress: extractEmail }
        : { emailAddresses: selectedEmails }

      const res = await fetch(endpoint, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(requestBody)
      })

      if (!res.ok) {
        const error = await res.json()
        throw new Error(error.error || "提取邮箱凭证失败")
      }

      const data = await res.json()

      if (extractMode === 'input') {
        setExtractedCredential(data.credential)
        setExtractedCredentials([])
      } else {
        setExtractedCredentials(data.credentials)
        setExtractedCredential(null)
      }

      toast({
        title: "提取成功",
        description: `已提取 ${extractMode === 'input' ? 1 : data.credentials.length} 个邮箱凭证`
      })
    } catch (error) {
      toast({
        title: "提取失败",
        description: error instanceof Error ? error.message : "请稍后重试",
        variant: "destructive"
      })
    } finally {
      setLoading(false)
    }
  }

  const handleBindDialogClose = () => {
    setBindDialogOpen(false)
    setNewBinding({ jwt: "" })
  }

  const handleExtractDialogClose = () => {
    setExtractDialogOpen(false)
    setExtractEmail("")
    setExtractedCredential(null)
    setExtractedCredentials([])
    setSelectedEmails([])
    setExtractMode('input')
    setShowJWT(false)
  }

  const unbindEmail = async (emailAddress: string) => {
    try {
      const res = await fetch("/api/email-credentials/bindings", {
        method: "DELETE",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ emailAddress })
      })

      if (!res.ok) throw new Error("解绑失败")

      setUserBindings(bindings => bindings.filter(binding => binding.emailAddress !== emailAddress))
      toast({
        title: "解绑成功",
        description: "邮箱已解绑"
      })
    } catch (error) {
      console.error(error)
      toast({
        title: "解绑失败",
        description: "解绑邮箱失败",
        variant: "destructive"
      })
    }
  }

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text)
      toast({
        title: "复制成功",
        description: "已复制到剪贴板"
      })
    } catch (error) {
      toast({
        title: "复制失败",
        description: "无法复制到剪贴板",
        variant: "destructive"
      })
    }
  }

  return (
    <div className="bg-background rounded-lg border-2 border-primary/20 p-6 space-y-6">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-2">
          <Mail className="w-5 h-5 text-primary" />
          <h2 className="text-lg font-semibold">邮箱凭证系统</h2>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="ghost"
            size="icon"
            onClick={handleRefresh}
            disabled={refreshing}
            className={refreshing ? "animate-spin" : ""}
          >
            <RefreshCw className="w-4 h-4" />
          </Button>

          {/* 用户绑定邮箱按钮 */}
          <Dialog open={bindDialogOpen} onOpenChange={setBindDialogOpen}>
            <DialogTrigger asChild>
              <Button className="gap-2">
                <Plus className="w-4 h-4" />
                绑定邮箱
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-md">
              <DialogHeader>
                <DialogTitle>绑定邮箱</DialogTitle>
                <DialogDescription>
                  输入邮箱凭证来绑定邮箱到您的账户
                </DialogDescription>
              </DialogHeader>

              <div className="space-y-4 py-4">
                <div className="space-y-2">
                  <Label>邮箱凭证 (JWT)</Label>
                  <Input
                    value={newBinding.jwt}
                    onChange={(e) => setNewBinding(prev => ({ ...prev, jwt: e.target.value }))}
                    placeholder="请输入邮箱凭证..."
                  />
                </div>
              </div>

              <DialogFooter>
                <DialogClose asChild>
                  <Button variant="outline" onClick={handleBindDialogClose} disabled={loading}>
                    取消
                  </Button>
                </DialogClose>
                <Button onClick={bindEmail} disabled={loading}>
                  {loading ? <Loader2 className="w-4 h-4 animate-spin" /> : "绑定"}
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>

          {/* 管理员提取凭证按钮 */}
          {canManageEmailCredentials && (
            <Dialog open={extractDialogOpen} onOpenChange={setExtractDialogOpen}>
              <DialogTrigger asChild>
                <Button variant="outline" className="gap-2">
                  <Eye className="w-4 h-4" />
                  提取凭证
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-md">
                <DialogHeader>
                  <DialogTitle>提取邮箱凭证</DialogTitle>
                  <DialogDescription>
                    输入邮箱地址来提取对应的邮箱凭证
                  </DialogDescription>
                </DialogHeader>

                <div className="space-y-4 py-4">
                  <div className="space-y-2">
                    <Label>邮箱地址</Label>
                    <Input
                      type="email"
                      value={extractEmail}
                      onChange={(e) => setExtractEmail(e.target.value)}
                      placeholder="<EMAIL>"
                    />
                  </div>

                  {extractedCredential && (
                    <div className="space-y-2">
                      <Label>提取的凭证</Label>
                      <div className="relative">
                        <Input
                          type={showJWT ? "text" : "password"}
                          value={extractedCredential.jwt}
                          readOnly
                          className="pr-20"
                        />
                        <div className="absolute right-2 top-0 h-full flex items-center gap-1">
                          <Button
                            type="button"
                            variant="ghost"
                            size="icon"
                            onClick={() => setShowJWT(!showJWT)}
                          >
                            {showJWT ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                          </Button>
                          <Button
                            type="button"
                            variant="ghost"
                            size="icon"
                            onClick={() => copyToClipboard(extractedCredential.jwt)}
                          >
                            <Copy className="w-4 h-4" />
                          </Button>
                        </div>
                      </div>
                      <div className="text-sm text-muted-foreground">
                        绑定用户数: {extractedCredential.bindingCount}
                      </div>
                    </div>
                  )}
                </div>

                <DialogFooter>
                  <DialogClose asChild>
                    <Button variant="outline" onClick={handleExtractDialogClose}>
                      关闭
                    </Button>
                  </DialogClose>
                  <Button onClick={extractCredential} disabled={loading}>
                    {loading ? <Loader2 className="w-4 h-4 animate-spin" /> : "提取"}
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          )}
        </div>
      </div>

      {/* 主要内容区域 */}
      {isLoading ? (
        <div className="text-center py-8 space-y-3">
          <div className="w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center mx-auto">
            <Loader2 className="w-6 h-6 text-primary animate-spin" />
          </div>
          <div>
            <p className="text-sm text-muted-foreground">加载中...</p>
          </div>
        </div>
      ) : (
        <div className="space-y-6">
          {/* 用户绑定的邮箱列表 */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">我绑定的邮箱</h3>
            {userBindings.length === 0 ? (
              <div className="text-center py-6 space-y-3 border rounded-lg">
                <div className="w-10 h-10 rounded-full bg-primary/10 flex items-center justify-center mx-auto">
                  <Mail className="w-5 h-5 text-primary" />
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">
                    您还没有绑定任何邮箱
                  </p>
                  <p className="text-xs text-muted-foreground mt-1">
                    点击上方的 "绑定邮箱" 按钮来绑定第一个邮箱
                  </p>
                </div>
              </div>
            ) : (
              <div className="space-y-2">
                {userBindings.map((binding) => (
                  <div
                    key={binding.id}
                    className="flex items-center justify-between p-3 rounded-lg border bg-card"
                  >
                    <div className="space-y-1">
                      <div className="font-medium">{binding.emailAddress}</div>
                      <div className="text-xs text-muted-foreground">
                        绑定于 {new Date(binding.createdAt).toLocaleString()}
                      </div>
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => unbindEmail(binding.emailAddress)}
                    >
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* 管理员邮箱凭证列表 */}
          {canManageEmailCredentials && (
            <div className="space-y-4">
              <h3 className="text-lg font-medium">系统邮箱凭证 (管理员)</h3>
              {credentials.length === 0 ? (
                <div className="text-center py-6 space-y-3 border rounded-lg">
                  <div className="w-10 h-10 rounded-full bg-primary/10 flex items-center justify-center mx-auto">
                    <Mail className="w-5 h-5 text-primary" />
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">
                      系统中还没有邮箱凭证
                    </p>
                    <p className="text-xs text-muted-foreground mt-1">
                      创建邮箱时会自动生成对应的凭证
                    </p>
                  </div>
                </div>
              ) : (
                <div className="space-y-2">
                  {credentials.map((credential) => (
                    <div
                      key={credential.id}
                      className="flex items-center justify-between p-4 rounded-lg border bg-card"
                    >
                      <div className="space-y-1">
                        <div className="font-medium">{credential.emailAddress}</div>
                        <div className="flex items-center gap-4 text-xs text-muted-foreground">
                          <span>创建于 {new Date(credential.createdAt).toLocaleString()}</span>
                          {credential.lastUsedAt && (
                            <span>最后使用: {new Date(credential.lastUsedAt).toLocaleString()}</span>
                          )}
                          <div className="flex items-center gap-1">
                            <Users className="w-3 h-3" />
                            <span>{credential.bindingCount} 个用户绑定</span>
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <span className={`text-xs px-2 py-1 rounded ${credential.enabled ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}`}>
                          {credential.enabled ? '启用' : '禁用'}
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          )}

          {/* 使用说明 */}
          <div className="space-y-4">
            <button
              type="button"
              className="flex items-center gap-1 text-sm text-muted-foreground hover:text-foreground transition-colors"
              onClick={() => setShowExamples(!showExamples)}
            >
              {showExamples ? <ChevronUp className="w-4 h-4" /> : <ChevronDown className="w-4 h-4" />}
              查看使用说明
            </button>

            {showExamples && (
              <div className="rounded-lg border bg-card p-4 space-y-4">
                <div className="space-y-2">
                  <h4 className="font-medium">邮箱凭证系统说明</h4>
                  <div className="text-sm text-muted-foreground space-y-2">
                    <p>1. <strong>邮箱创建</strong>：系统创建邮箱时会自动生成对应的JWT凭证</p>
                    <p>2. <strong>凭证绑定</strong>：用户可以通过邮箱凭证绑定邮箱到自己的账户</p>
                    <p>3. <strong>凭证提取</strong>：管理员可以提取任何邮箱的凭证供用户使用</p>
                    <p>4. <strong>绑定统计</strong>：显示每个邮箱绑定了多少个用户（不包括管理员）</p>
                  </div>
                </div>

                <div className="space-y-2">
                  <h4 className="font-medium">使用流程</h4>
                  <div className="text-sm text-muted-foreground space-y-1">
                    <p><strong>用户：</strong> 获取邮箱凭证 → 点击"绑定邮箱" → 输入凭证 → 完成绑定</p>
                    <p><strong>管理员：</strong> 查看所有邮箱凭证 → 提取特定邮箱凭证 → 提供给用户</p>
                  </div>
                </div>

                <div className="space-y-2">
                  <h4 className="font-medium">安全说明</h4>
                  <div className="text-sm text-muted-foreground space-y-1">
                    <p>• 邮箱凭证是JWT格式，包含邮箱访问权限</p>
                    <p>• 凭证永久有效，请妥善保管</p>
                    <p>• 系统会记录凭证使用情况</p>
                    <p>• 管理员可以查看绑定统计信息</p>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  )
}
