"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[672],{75629:(e,t,n)=>{n.d(t,{Eq:()=>s});var r=new WeakMap,o=new WeakMap,i={},l=0,a=function(e){return e&&(e.host||a(e.parentNode))},u=function(e,t,n,u){var s=(Array.isArray(e)?e:[e]).map(function(e){if(t.contains(e))return e;var n=a(e);return n&&t.contains(n)?n:(console.error("aria-hidden",e,"in not contained inside",t,". Doing nothing"),null)}).filter(function(e){return!!e});i[n]||(i[n]=new WeakMap);var c=i[n],d=[],f=new Set,p=new Set(s),v=function(e){!e||f.has(e)||(f.add(e),v(e.parentNode))};s.forEach(v);var m=function(e){!e||p.has(e)||Array.prototype.forEach.call(e.children,function(e){if(f.has(e))m(e);else try{var t=e.getAttribute(u),i=null!==t&&"false"!==t,l=(r.get(e)||0)+1,a=(c.get(e)||0)+1;r.set(e,l),c.set(e,a),d.push(e),1===l&&i&&o.set(e,!0),1===a&&e.setAttribute(n,"true"),i||e.setAttribute(u,"true")}catch(t){console.error("aria-hidden: cannot operate on ",e,t)}})};return m(t),f.clear(),l++,function(){d.forEach(function(e){var t=r.get(e)-1,i=c.get(e)-1;r.set(e,t),c.set(e,i),t||(o.has(e)||e.removeAttribute(u),o.delete(e)),i||e.removeAttribute(n)}),--l||(r=new WeakMap,r=new WeakMap,o=new WeakMap,i={})}},s=function(e,t,n){void 0===n&&(n="data-aria-hidden");var r,o=Array.from(Array.isArray(e)?e:[e]),i=t||(r=e,"undefined"==typeof document?null:(Array.isArray(r)?r[0]:r).ownerDocument.body);return i?(o.push.apply(o,Array.from(i.querySelectorAll("[aria-live]"))),u(o,i,n,"aria-hidden")):function(){return null}}},44505:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(93823).A)("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},88539:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(93823).A)("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},37800:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(93823).A)("Copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]])},84690:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(93823).A)("Key",[["path",{d:"m15.5 7.5 2.3 2.3a1 1 0 0 0 1.4 0l2.1-2.1a1 1 0 0 0 0-1.4L19 4",key:"g0fldk"}],["path",{d:"m21 2-9.6 9.6",key:"1j0ho8"}],["circle",{cx:"7.5",cy:"15.5",r:"5.5",key:"yqb3hr"}]])},88563:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(93823).A)("LoaderCircle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},11965:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(93823).A)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},18866:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(93823).A)("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},75456:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(93823).A)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},20324:(e,t,n)=>{n.d(t,{A:()=>K});var r,o,i=function(){return(i=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)};function l(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)0>t.indexOf(r[o])&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n}Object.create,Object.create;var a=("function"==typeof SuppressedError&&SuppressedError,n(57845)),u="right-scroll-bar-position",s="width-before-scroll-bar";function c(e,t){return"function"==typeof e?e(t):e&&(e.current=t),e}var d="undefined"!=typeof window?a.useLayoutEffect:a.useEffect,f=new WeakMap;function p(e){return e}var v=function(e){void 0===e&&(e={});var t,n,r,o=(void 0===t&&(t=p),n=[],r=!1,{read:function(){if(r)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return n.length?n[n.length-1]:null},useMedium:function(e){var o=t(e,r);return n.push(o),function(){n=n.filter(function(e){return e!==o})}},assignSyncMedium:function(e){for(r=!0;n.length;){var t=n;n=[],t.forEach(e)}n={push:function(t){return e(t)},filter:function(){return n}}},assignMedium:function(e){r=!0;var t=[];if(n.length){var o=n;n=[],o.forEach(e),t=n}var i=function(){var n=t;t=[],n.forEach(e)},l=function(){return Promise.resolve().then(i)};l(),n={push:function(e){t.push(e),l()},filter:function(e){return t=t.filter(e),n}}}});return o.options=i({async:!0,ssr:!1},e),o}(),m=function(){},h=a.forwardRef(function(e,t){var n,r,o,u,s=a.useRef(null),p=a.useState({onScrollCapture:m,onWheelCapture:m,onTouchMoveCapture:m}),h=p[0],y=p[1],g=e.forwardProps,w=e.children,b=e.className,x=e.removeScrollBar,E=e.enabled,C=e.shards,S=e.sideCar,R=e.noRelative,N=e.noIsolation,j=e.inert,P=e.allowPinchZoom,D=e.as,k=e.gapMode,A=l(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),O=(n=[s,t],r=function(e){return n.forEach(function(t){return c(t,e)})},(o=(0,a.useState)(function(){return{value:null,callback:r,facade:{get current(){return o.value},set current(value){var e=o.value;e!==value&&(o.value=value,o.callback(value,e))}}}})[0]).callback=r,u=o.facade,d(function(){var e=f.get(u);if(e){var t=new Set(e),r=new Set(n),o=u.current;t.forEach(function(e){r.has(e)||c(e,null)}),r.forEach(function(e){t.has(e)||c(e,o)})}f.set(u,n)},[n]),u),T=i(i({},A),h);return a.createElement(a.Fragment,null,E&&a.createElement(S,{sideCar:v,removeScrollBar:x,shards:C,noRelative:R,noIsolation:N,inert:j,setCallbacks:y,allowPinchZoom:!!P,lockRef:s,gapMode:k}),g?a.cloneElement(a.Children.only(w),i(i({},T),{ref:O})):a.createElement(void 0===D?"div":D,i({},T,{className:b,ref:O}),w))});h.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},h.classNames={fullWidth:s,zeroRight:u};var y=function(e){var t=e.sideCar,n=l(e,["sideCar"]);if(!t)throw Error("Sidecar: please provide `sideCar` property to import the right car");var r=t.read();if(!r)throw Error("Sidecar medium not found");return a.createElement(r,i({},n))};y.isSideCarExport=!0;var g=function(){var e=0,t=null;return{add:function(r){if(0==e&&(t=function(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=o||n.nc;return t&&e.setAttribute("nonce",t),e}())){var i,l;(i=t).styleSheet?i.styleSheet.cssText=r:i.appendChild(document.createTextNode(r)),l=t,(document.head||document.getElementsByTagName("head")[0]).appendChild(l)}e++},remove:function(){--e||!t||(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},w=function(){var e=g();return function(t,n){a.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},b=function(){var e=w();return function(t){return e(t.styles,t.dynamic),null}},x={left:0,top:0,right:0,gap:0},E=function(e){return parseInt(e||"",10)||0},C=function(e){var t=window.getComputedStyle(document.body),n=t["padding"===e?"paddingLeft":"marginLeft"],r=t["padding"===e?"paddingTop":"marginTop"],o=t["padding"===e?"paddingRight":"marginRight"];return[E(n),E(r),E(o)]},S=function(e){if(void 0===e&&(e="margin"),"undefined"==typeof window)return x;var t=C(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}},R=b(),N="data-scroll-locked",j=function(e,t,n,r){var o=e.left,i=e.top,l=e.right,a=e.gap;return void 0===n&&(n="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(r,";\n   padding-right: ").concat(a,"px ").concat(r,";\n  }\n  body[").concat(N,"] {\n    overflow: hidden ").concat(r,";\n    overscroll-behavior: contain;\n    ").concat([t&&"position: relative ".concat(r,";"),"margin"===n&&"\n    padding-left: ".concat(o,"px;\n    padding-top: ").concat(i,"px;\n    padding-right: ").concat(l,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(a,"px ").concat(r,";\n    "),"padding"===n&&"padding-right: ".concat(a,"px ").concat(r,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(u," {\n    right: ").concat(a,"px ").concat(r,";\n  }\n  \n  .").concat(s," {\n    margin-right: ").concat(a,"px ").concat(r,";\n  }\n  \n  .").concat(u," .").concat(u," {\n    right: 0 ").concat(r,";\n  }\n  \n  .").concat(s," .").concat(s," {\n    margin-right: 0 ").concat(r,";\n  }\n  \n  body[").concat(N,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(a,"px;\n  }\n")},P=function(){var e=parseInt(document.body.getAttribute(N)||"0",10);return isFinite(e)?e:0},D=function(){a.useEffect(function(){return document.body.setAttribute(N,(P()+1).toString()),function(){var e=P()-1;e<=0?document.body.removeAttribute(N):document.body.setAttribute(N,e.toString())}},[])},k=function(e){var t=e.noRelative,n=e.noImportant,r=e.gapMode,o=void 0===r?"margin":r;D();var i=a.useMemo(function(){return S(o)},[o]);return a.createElement(R,{styles:j(i,!t,o,n?"":"!important")})},A=!1;if("undefined"!=typeof window)try{var O=Object.defineProperty({},"passive",{get:function(){return A=!0,!0}});window.addEventListener("test",O,O),window.removeEventListener("test",O,O)}catch(e){A=!1}var T=!!A&&{passive:!1},M=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return"hidden"!==n[t]&&!(n.overflowY===n.overflowX&&"TEXTAREA"!==e.tagName&&"visible"===n[t])},L=function(e,t){var n=t.ownerDocument,r=t;do{if("undefined"!=typeof ShadowRoot&&r instanceof ShadowRoot&&(r=r.host),I(e,r)){var o=_(e,r);if(o[1]>o[2])return!0}r=r.parentNode}while(r&&r!==n.body);return!1},I=function(e,t){return"v"===e?M(t,"overflowY"):M(t,"overflowX")},_=function(e,t){return"v"===e?[t.scrollTop,t.scrollHeight,t.clientHeight]:[t.scrollLeft,t.scrollWidth,t.clientWidth]},W=function(e,t,n,r,o){var i,l=(i=window.getComputedStyle(t).direction,"h"===e&&"rtl"===i?-1:1),a=l*r,u=n.target,s=t.contains(u),c=!1,d=a>0,f=0,p=0;do{if(!u)break;var v=_(e,u),m=v[0],h=v[1]-v[2]-l*m;(m||h)&&I(e,u)&&(f+=h,p+=m);var y=u.parentNode;u=y&&y.nodeType===Node.DOCUMENT_FRAGMENT_NODE?y.host:y}while(!s&&u!==document.body||s&&(t.contains(u)||t===u));return d&&(o&&1>Math.abs(f)||!o&&a>f)?c=!0:!d&&(o&&1>Math.abs(p)||!o&&-a>p)&&(c=!0),c},F=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},$=function(e){return[e.deltaX,e.deltaY]},B=function(e){return e&&"current"in e?e.current:e},H=0,V=[];let U=(r=function(e){var t=a.useRef([]),n=a.useRef([0,0]),r=a.useRef(),o=a.useState(H++)[0],i=a.useState(b)[0],l=a.useRef(e);a.useEffect(function(){l.current=e},[e]),a.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var t=(function(e,t,n){if(n||2==arguments.length)for(var r,o=0,i=t.length;o<i;o++)!r&&o in t||(r||(r=Array.prototype.slice.call(t,0,o)),r[o]=t[o]);return e.concat(r||Array.prototype.slice.call(t))})([e.lockRef.current],(e.shards||[]).map(B),!0).filter(Boolean);return t.forEach(function(e){return e.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),t.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var u=a.useCallback(function(e,t){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!l.current.allowPinchZoom;var o,i=F(e),a=n.current,u="deltaX"in e?e.deltaX:a[0]-i[0],s="deltaY"in e?e.deltaY:a[1]-i[1],c=e.target,d=Math.abs(u)>Math.abs(s)?"h":"v";if("touches"in e&&"h"===d&&"range"===c.type)return!1;var f=L(d,c);if(!f)return!0;if(f?o=d:(o="v"===d?"h":"v",f=L(d,c)),!f)return!1;if(!r.current&&"changedTouches"in e&&(u||s)&&(r.current=o),!o)return!0;var p=r.current||o;return W(p,t,e,"h"===p?u:s,!0)},[]),s=a.useCallback(function(e){if(V.length&&V[V.length-1]===i){var n="deltaY"in e?$(e):F(e),r=t.current.filter(function(t){var r;return t.name===e.type&&(t.target===e.target||e.target===t.shadowParent)&&(r=t.delta)[0]===n[0]&&r[1]===n[1]})[0];if(r&&r.should){e.cancelable&&e.preventDefault();return}if(!r){var o=(l.current.shards||[]).map(B).filter(Boolean).filter(function(t){return t.contains(e.target)});(o.length>0?u(e,o[0]):!l.current.noIsolation)&&e.cancelable&&e.preventDefault()}}},[]),c=a.useCallback(function(e,n,r,o){var i={name:e,delta:n,target:r,should:o,shadowParent:function(e){for(var t=null;null!==e;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}(r)};t.current.push(i),setTimeout(function(){t.current=t.current.filter(function(e){return e!==i})},1)},[]),d=a.useCallback(function(e){n.current=F(e),r.current=void 0},[]),f=a.useCallback(function(t){c(t.type,$(t),t.target,u(t,e.lockRef.current))},[]),p=a.useCallback(function(t){c(t.type,F(t),t.target,u(t,e.lockRef.current))},[]);a.useEffect(function(){return V.push(i),e.setCallbacks({onScrollCapture:f,onWheelCapture:f,onTouchMoveCapture:p}),document.addEventListener("wheel",s,T),document.addEventListener("touchmove",s,T),document.addEventListener("touchstart",d,T),function(){V=V.filter(function(e){return e!==i}),document.removeEventListener("wheel",s,T),document.removeEventListener("touchmove",s,T),document.removeEventListener("touchstart",d,T)}},[]);var v=e.removeScrollBar,m=e.inert;return a.createElement(a.Fragment,null,m?a.createElement(i,{styles:"\n  .block-interactivity-".concat(o," {pointer-events: none;}\n  .allow-interactivity-").concat(o," {pointer-events: all;}\n")}):null,v?a.createElement(k,{noRelative:e.noRelative,gapMode:e.gapMode}):null)},v.useMedium(r),y);var z=a.forwardRef(function(e,t){return a.createElement(h,i({},e,{ref:t,sideCar:U}))});z.classNames=h.classNames;let K=z},93185:(e,t,n)=>{n.d(t,{bm:()=>eb,UC:()=>ey,VY:()=>ew,hJ:()=>eh,ZL:()=>em,bL:()=>ep,hE:()=>eg,l9:()=>ev,G$:()=>es,Hs:()=>I});var r,o=n(57845),i=n.t(o,2);function l(e,t,{checkForDefaultPrevented:n=!0}={}){return function(r){if(e?.(r),!1===n||!r.defaultPrevented)return t?.(r)}}function a(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function u(...e){return t=>{let n=!1,r=e.map(e=>{let r=a(e,t);return n||"function"!=typeof r||(n=!0),r});if(n)return()=>{for(let t=0;t<r.length;t++){let n=r[t];"function"==typeof n?n():a(e[t],null)}}}}function s(...e){return o.useCallback(u(...e),e)}var c=n(37785),d=globalThis?.document?o.useLayoutEffect:()=>{},f=i[" useId ".trim().toString()]||(()=>void 0),p=0;function v(e){let[t,n]=o.useState(f());return d(()=>{e||n(e=>e??String(p++))},[e]),e||(t?`radix-${t}`:"")}var m=i[" useInsertionEffect ".trim().toString()]||d,h=(Symbol("RADIX:SYNC_STATE"),n(75724));function y(e){let t=function(e){let t=o.forwardRef((e,t)=>{let{children:n,...r}=e;if(o.isValidElement(n)){let e,i;let l=(e=Object.getOwnPropertyDescriptor(n.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?n.ref:(e=Object.getOwnPropertyDescriptor(n,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?n.props.ref:n.props.ref||n.ref,a=function(e,t){let n={...t};for(let r in t){let o=e[r],i=t[r];/^on[A-Z]/.test(r)?o&&i?n[r]=(...e)=>{let t=i(...e);return o(...e),t}:o&&(n[r]=o):"style"===r?n[r]={...o,...i}:"className"===r&&(n[r]=[o,i].filter(Boolean).join(" "))}return{...e,...n}}(r,n.props);return n.type!==o.Fragment&&(a.ref=t?u(t,l):l),o.cloneElement(n,a)}return o.Children.count(n)>1?o.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),n=o.forwardRef((e,n)=>{let{children:r,...i}=e,l=o.Children.toArray(r),a=l.find(w);if(a){let e=a.props.children,r=l.map(t=>t!==a?t:o.Children.count(e)>1?o.Children.only(null):o.isValidElement(e)?e.props.children:null);return(0,c.jsx)(t,{...i,ref:n,children:o.isValidElement(e)?o.cloneElement(e,void 0,r):null})}return(0,c.jsx)(t,{...i,ref:n,children:r})});return n.displayName=`${e}.Slot`,n}var g=Symbol("radix.slottable");function w(e){return o.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===g}var b=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let n=y(`Primitive.${t}`),r=o.forwardRef((e,r)=>{let{asChild:o,...i}=e,l=o?n:t;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,c.jsx)(l,{...i,ref:r})});return r.displayName=`Primitive.${t}`,{...e,[t]:r}},{});function x(e){let t=o.useRef(e);return o.useEffect(()=>{t.current=e}),o.useMemo(()=>(...e)=>t.current?.(...e),[])}var E="dismissableLayer.update",C=o.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),S=o.forwardRef((e,t)=>{let{disableOutsidePointerEvents:n=!1,onEscapeKeyDown:i,onPointerDownOutside:a,onFocusOutside:u,onInteractOutside:d,onDismiss:f,...p}=e,v=o.useContext(C),[m,h]=o.useState(null),y=m?.ownerDocument??globalThis?.document,[,g]=o.useState({}),w=s(t,e=>h(e)),S=Array.from(v.layers),[j]=[...v.layersWithOutsidePointerEventsDisabled].slice(-1),P=S.indexOf(j),D=m?S.indexOf(m):-1,k=v.layersWithOutsidePointerEventsDisabled.size>0,A=D>=P,O=function(e,t=globalThis?.document){let n=x(e),r=o.useRef(!1),i=o.useRef(()=>{});return o.useEffect(()=>{let e=e=>{if(e.target&&!r.current){let r=function(){N("dismissableLayer.pointerDownOutside",n,o,{discrete:!0})},o={originalEvent:e};"touch"===e.pointerType?(t.removeEventListener("click",i.current),i.current=r,t.addEventListener("click",i.current,{once:!0})):r()}else t.removeEventListener("click",i.current);r.current=!1},o=window.setTimeout(()=>{t.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(o),t.removeEventListener("pointerdown",e),t.removeEventListener("click",i.current)}},[t,n]),{onPointerDownCapture:()=>r.current=!0}}(e=>{let t=e.target,n=[...v.branches].some(e=>e.contains(t));!A||n||(a?.(e),d?.(e),e.defaultPrevented||f?.())},y),T=function(e,t=globalThis?.document){let n=x(e),r=o.useRef(!1);return o.useEffect(()=>{let e=e=>{e.target&&!r.current&&N("dismissableLayer.focusOutside",n,{originalEvent:e},{discrete:!1})};return t.addEventListener("focusin",e),()=>t.removeEventListener("focusin",e)},[t,n]),{onFocusCapture:()=>r.current=!0,onBlurCapture:()=>r.current=!1}}(e=>{let t=e.target;[...v.branches].some(e=>e.contains(t))||(u?.(e),d?.(e),e.defaultPrevented||f?.())},y);return!function(e,t=globalThis?.document){let n=x(e);o.useEffect(()=>{let e=e=>{"Escape"===e.key&&n(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[n,t])}(e=>{D!==v.layers.size-1||(i?.(e),!e.defaultPrevented&&f&&(e.preventDefault(),f()))},y),o.useEffect(()=>{if(m)return n&&(0===v.layersWithOutsidePointerEventsDisabled.size&&(r=y.body.style.pointerEvents,y.body.style.pointerEvents="none"),v.layersWithOutsidePointerEventsDisabled.add(m)),v.layers.add(m),R(),()=>{n&&1===v.layersWithOutsidePointerEventsDisabled.size&&(y.body.style.pointerEvents=r)}},[m,y,n,v]),o.useEffect(()=>()=>{m&&(v.layers.delete(m),v.layersWithOutsidePointerEventsDisabled.delete(m),R())},[m,v]),o.useEffect(()=>{let e=()=>g({});return document.addEventListener(E,e),()=>document.removeEventListener(E,e)},[]),(0,c.jsx)(b.div,{...p,ref:w,style:{pointerEvents:k?A?"auto":"none":void 0,...e.style},onFocusCapture:l(e.onFocusCapture,T.onFocusCapture),onBlurCapture:l(e.onBlurCapture,T.onBlurCapture),onPointerDownCapture:l(e.onPointerDownCapture,O.onPointerDownCapture)})});function R(){let e=new CustomEvent(E);document.dispatchEvent(e)}function N(e,t,n,{discrete:r}){let o=n.originalEvent.target,i=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});(t&&o.addEventListener(e,t,{once:!0}),r)?o&&h.flushSync(()=>o.dispatchEvent(i)):o.dispatchEvent(i)}S.displayName="DismissableLayer",o.forwardRef((e,t)=>{let n=o.useContext(C),r=o.useRef(null),i=s(t,r);return o.useEffect(()=>{let e=r.current;if(e)return n.branches.add(e),()=>{n.branches.delete(e)}},[n.branches]),(0,c.jsx)(b.div,{...e,ref:i})}).displayName="DismissableLayerBranch";var j=n(60560),P=o.forwardRef((e,t)=>{let{container:n,...r}=e,[i,l]=o.useState(!1);d(()=>l(!0),[]);let a=n||i&&globalThis?.document?.body;return a?h.createPortal((0,c.jsx)(b.div,{...r,ref:t}),a):null});P.displayName="Portal";var D=e=>{let{present:t,children:n}=e,r=function(e){var t,n;let[r,i]=o.useState(),l=o.useRef(null),a=o.useRef(e),u=o.useRef("none"),[s,c]=(t=e?"mounted":"unmounted",n={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},o.useReducer((e,t)=>n[e][t]??e,t));return o.useEffect(()=>{let e=k(l.current);u.current="mounted"===s?e:"none"},[s]),d(()=>{let t=l.current,n=a.current;if(n!==e){let r=u.current,o=k(t);e?c("MOUNT"):"none"===o||t?.display==="none"?c("UNMOUNT"):n&&r!==o?c("ANIMATION_OUT"):c("UNMOUNT"),a.current=e}},[e,c]),d(()=>{if(r){let e;let t=r.ownerDocument.defaultView??window,n=n=>{let o=k(l.current).includes(n.animationName);if(n.target===r&&o&&(c("ANIMATION_END"),!a.current)){let n=r.style.animationFillMode;r.style.animationFillMode="forwards",e=t.setTimeout(()=>{"forwards"===r.style.animationFillMode&&(r.style.animationFillMode=n)})}},o=e=>{e.target===r&&(u.current=k(l.current))};return r.addEventListener("animationstart",o),r.addEventListener("animationcancel",n),r.addEventListener("animationend",n),()=>{t.clearTimeout(e),r.removeEventListener("animationstart",o),r.removeEventListener("animationcancel",n),r.removeEventListener("animationend",n)}}c("ANIMATION_END")},[r,c]),{isPresent:["mounted","unmountSuspended"].includes(s),ref:o.useCallback(e=>{l.current=e?getComputedStyle(e):null,i(e)},[])}}(t),i="function"==typeof n?n({present:r.isPresent}):o.Children.only(n),l=s(r.ref,function(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(n=(t=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning)?e.props.ref:e.props.ref||e.ref}(i));return"function"==typeof n||r.isPresent?o.cloneElement(i,{ref:l}):null};function k(e){return e?.animationName||"none"}D.displayName="Presence";var A=n(9692),O=n(20324),T=n(75629),M="Dialog",[L,I]=function(e,t=[]){let n=[],r=()=>{let t=n.map(e=>o.createContext(e));return function(n){let r=n?.[e]||t;return o.useMemo(()=>({[`__scope${e}`]:{...n,[e]:r}}),[n,r])}};return r.scopeName=e,[function(t,r){let i=o.createContext(r),l=n.length;n=[...n,r];let a=t=>{let{scope:n,children:r,...a}=t,u=n?.[e]?.[l]||i,s=o.useMemo(()=>a,Object.values(a));return(0,c.jsx)(u.Provider,{value:s,children:r})};return a.displayName=t+"Provider",[a,function(n,a){let u=a?.[e]?.[l]||i,s=o.useContext(u);if(s)return s;if(void 0!==r)return r;throw Error(`\`${n}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let n=()=>{let n=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let r=n.reduce((t,{useScope:n,scopeName:r})=>{let o=n(e)[`__scope${r}`];return{...t,...o}},{});return o.useMemo(()=>({[`__scope${t.scopeName}`]:r}),[r])}};return n.scopeName=t.scopeName,n}(r,...t)]}(M),[_,W]=L(M),F=e=>{let{__scopeDialog:t,children:n,open:r,defaultOpen:i,onOpenChange:l,modal:a=!0}=e,u=o.useRef(null),s=o.useRef(null),[d,f]=function({prop:e,defaultProp:t,onChange:n=()=>{},caller:r}){let[i,l,a]=function({defaultProp:e,onChange:t}){let[n,r]=o.useState(e),i=o.useRef(n),l=o.useRef(t);return m(()=>{l.current=t},[t]),o.useEffect(()=>{i.current!==n&&(l.current?.(n),i.current=n)},[n,i]),[n,r,l]}({defaultProp:t,onChange:n}),u=void 0!==e,s=u?e:i;{let t=o.useRef(void 0!==e);o.useEffect(()=>{let e=t.current;if(e!==u){let t=u?"controlled":"uncontrolled";console.warn(`${r} is changing from ${e?"controlled":"uncontrolled"} to ${t}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}t.current=u},[u,r])}return[s,o.useCallback(t=>{if(u){let n="function"==typeof t?t(e):t;n!==e&&a.current?.(n)}else l(t)},[u,e,l,a])]}({prop:r,defaultProp:i??!1,onChange:l,caller:M});return(0,c.jsx)(_,{scope:t,triggerRef:u,contentRef:s,contentId:v(),titleId:v(),descriptionId:v(),open:d,onOpenChange:f,onOpenToggle:o.useCallback(()=>f(e=>!e),[f]),modal:a,children:n})};F.displayName=M;var $="DialogTrigger",B=o.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=W($,n),i=s(t,o.triggerRef);return(0,c.jsx)(b.button,{type:"button","aria-haspopup":"dialog","aria-expanded":o.open,"aria-controls":o.contentId,"data-state":ea(o.open),...r,ref:i,onClick:l(e.onClick,o.onOpenToggle)})});B.displayName=$;var H="DialogPortal",[V,U]=L(H,{forceMount:void 0}),z=e=>{let{__scopeDialog:t,forceMount:n,children:r,container:i}=e,l=W(H,t);return(0,c.jsx)(V,{scope:t,forceMount:n,children:o.Children.map(r,e=>(0,c.jsx)(D,{present:n||l.open,children:(0,c.jsx)(P,{asChild:!0,container:i,children:e})}))})};z.displayName=H;var K="DialogOverlay",Y=o.forwardRef((e,t)=>{let n=U(K,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,i=W(K,e.__scopeDialog);return i.modal?(0,c.jsx)(D,{present:r||i.open,children:(0,c.jsx)(X,{...o,ref:t})}):null});Y.displayName=K;var q=y("DialogOverlay.RemoveScroll"),X=o.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=W(K,n);return(0,c.jsx)(O.A,{as:q,allowPinchZoom:!0,shards:[o.contentRef],children:(0,c.jsx)(b.div,{"data-state":ea(o.open),...r,ref:t,style:{pointerEvents:"auto",...r.style}})})}),Z="DialogContent",G=o.forwardRef((e,t)=>{let n=U(Z,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,i=W(Z,e.__scopeDialog);return(0,c.jsx)(D,{present:r||i.open,children:i.modal?(0,c.jsx)(J,{...o,ref:t}):(0,c.jsx)(Q,{...o,ref:t})})});G.displayName=Z;var J=o.forwardRef((e,t)=>{let n=W(Z,e.__scopeDialog),r=o.useRef(null),i=s(t,n.contentRef,r);return o.useEffect(()=>{let e=r.current;if(e)return(0,T.Eq)(e)},[]),(0,c.jsx)(ee,{...e,ref:i,trapFocus:n.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:l(e.onCloseAutoFocus,e=>{e.preventDefault(),n.triggerRef.current?.focus()}),onPointerDownOutside:l(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,n=0===t.button&&!0===t.ctrlKey;(2===t.button||n)&&e.preventDefault()}),onFocusOutside:l(e.onFocusOutside,e=>e.preventDefault())})}),Q=o.forwardRef((e,t)=>{let n=W(Z,e.__scopeDialog),r=o.useRef(!1),i=o.useRef(!1);return(0,c.jsx)(ee,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{e.onCloseAutoFocus?.(t),t.defaultPrevented||(r.current||n.triggerRef.current?.focus(),t.preventDefault()),r.current=!1,i.current=!1},onInteractOutside:t=>{e.onInteractOutside?.(t),t.defaultPrevented||(r.current=!0,"pointerdown"!==t.detail.originalEvent.type||(i.current=!0));let o=t.target;n.triggerRef.current?.contains(o)&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&i.current&&t.preventDefault()}})}),ee=o.forwardRef((e,t)=>{let{__scopeDialog:n,trapFocus:r,onOpenAutoFocus:i,onCloseAutoFocus:l,...a}=e,u=W(Z,n),d=o.useRef(null),f=s(t,d);return(0,A.Oh)(),(0,c.jsxs)(c.Fragment,{children:[(0,c.jsx)(j.n,{asChild:!0,loop:!0,trapped:r,onMountAutoFocus:i,onUnmountAutoFocus:l,children:(0,c.jsx)(S,{role:"dialog",id:u.contentId,"aria-describedby":u.descriptionId,"aria-labelledby":u.titleId,"data-state":ea(u.open),...a,ref:f,onDismiss:()=>u.onOpenChange(!1)})}),(0,c.jsxs)(c.Fragment,{children:[(0,c.jsx)(ed,{titleId:u.titleId}),(0,c.jsx)(ef,{contentRef:d,descriptionId:u.descriptionId})]})]})}),et="DialogTitle",en=o.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=W(et,n);return(0,c.jsx)(b.h2,{id:o.titleId,...r,ref:t})});en.displayName=et;var er="DialogDescription",eo=o.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=W(er,n);return(0,c.jsx)(b.p,{id:o.descriptionId,...r,ref:t})});eo.displayName=er;var ei="DialogClose",el=o.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=W(ei,n);return(0,c.jsx)(b.button,{type:"button",...r,ref:t,onClick:l(e.onClick,()=>o.onOpenChange(!1))})});function ea(e){return e?"open":"closed"}el.displayName=ei;var eu="DialogTitleWarning",[es,ec]=function(e,t){let n=o.createContext(t),r=e=>{let{children:t,...r}=e,i=o.useMemo(()=>r,Object.values(r));return(0,c.jsx)(n.Provider,{value:i,children:t})};return r.displayName=e+"Provider",[r,function(r){let i=o.useContext(n);if(i)return i;if(void 0!==t)return t;throw Error(`\`${r}\` must be used within \`${e}\``)}]}(eu,{contentName:Z,titleName:et,docsSlug:"dialog"}),ed=({titleId:e})=>{let t=ec(eu),n=`\`${t.contentName}\` requires a \`${t.titleName}\` for the component to be accessible for screen reader users.

If you want to hide the \`${t.titleName}\`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/${t.docsSlug}`;return o.useEffect(()=>{e&&!document.getElementById(e)&&console.error(n)},[n,e]),null},ef=({contentRef:e,descriptionId:t})=>{let n=ec("DialogDescriptionWarning"),r=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${n.contentName}}.`;return o.useEffect(()=>{let n=e.current?.getAttribute("aria-describedby");t&&n&&!document.getElementById(t)&&console.warn(r)},[r,e,t]),null},ep=F,ev=B,em=z,eh=Y,ey=G,eg=en,ew=eo,eb=el},9692:(e,t,n)=>{n.d(t,{Oh:()=>i});var r=n(57845),o=0;function i(){r.useEffect(()=>{let e=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",e[0]??l()),document.body.insertAdjacentElement("beforeend",e[1]??l()),o++,()=>{1===o&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),o--}},[])}function l(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}},60560:(e,t,n)=>{n.d(t,{n:()=>v});var r=n(57845);function o(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function i(...e){return t=>{let n=!1,r=e.map(e=>{let r=o(e,t);return n||"function"!=typeof r||(n=!0),r});if(n)return()=>{for(let t=0;t<r.length;t++){let n=r[t];"function"==typeof n?n():o(e[t],null)}}}}n(75724);var l=n(37785),a=Symbol("radix.slottable");function u(e){return r.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===a}var s=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let n=function(e){let t=function(e){let t=r.forwardRef((e,t)=>{let{children:n,...o}=e;if(r.isValidElement(n)){let e,l;let a=(e=Object.getOwnPropertyDescriptor(n.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?n.ref:(e=Object.getOwnPropertyDescriptor(n,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?n.props.ref:n.props.ref||n.ref,u=function(e,t){let n={...t};for(let r in t){let o=e[r],i=t[r];/^on[A-Z]/.test(r)?o&&i?n[r]=(...e)=>{let t=i(...e);return o(...e),t}:o&&(n[r]=o):"style"===r?n[r]={...o,...i}:"className"===r&&(n[r]=[o,i].filter(Boolean).join(" "))}return{...e,...n}}(o,n.props);return n.type!==r.Fragment&&(u.ref=t?i(t,a):a),r.cloneElement(n,u)}return r.Children.count(n)>1?r.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),n=r.forwardRef((e,n)=>{let{children:o,...i}=e,a=r.Children.toArray(o),s=a.find(u);if(s){let e=s.props.children,o=a.map(t=>t!==s?t:r.Children.count(e)>1?r.Children.only(null):r.isValidElement(e)?e.props.children:null);return(0,l.jsx)(t,{...i,ref:n,children:r.isValidElement(e)?r.cloneElement(e,void 0,o):null})}return(0,l.jsx)(t,{...i,ref:n,children:o})});return n.displayName=`${e}.Slot`,n}(`Primitive.${t}`),o=r.forwardRef((e,r)=>{let{asChild:o,...i}=e,a=o?n:t;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,l.jsx)(a,{...i,ref:r})});return o.displayName=`Primitive.${t}`,{...e,[t]:o}},{});function c(e){let t=r.useRef(e);return r.useEffect(()=>{t.current=e}),r.useMemo(()=>(...e)=>t.current?.(...e),[])}var d="focusScope.autoFocusOnMount",f="focusScope.autoFocusOnUnmount",p={bubbles:!1,cancelable:!0},v=r.forwardRef((e,t)=>{let{loop:n=!1,trapped:o=!1,onMountAutoFocus:a,onUnmountAutoFocus:u,...v}=e,[w,b]=r.useState(null),x=c(a),E=c(u),C=r.useRef(null),S=function(...e){return r.useCallback(i(...e),e)}(t,e=>b(e)),R=r.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;r.useEffect(()=>{if(o){let e=function(e){if(R.paused||!w)return;let t=e.target;w.contains(t)?C.current=t:y(C.current,{select:!0})},t=function(e){if(R.paused||!w)return;let t=e.relatedTarget;null===t||w.contains(t)||y(C.current,{select:!0})};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let n=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&y(w)});return w&&n.observe(w,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),n.disconnect()}}},[o,w,R.paused]),r.useEffect(()=>{if(w){g.add(R);let e=document.activeElement;if(!w.contains(e)){let t=new CustomEvent(d,p);w.addEventListener(d,x),w.dispatchEvent(t),t.defaultPrevented||(function(e,{select:t=!1}={}){let n=document.activeElement;for(let r of e)if(y(r,{select:t}),document.activeElement!==n)return}(m(w).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&y(w))}return()=>{w.removeEventListener(d,x),setTimeout(()=>{let t=new CustomEvent(f,p);w.addEventListener(f,E),w.dispatchEvent(t),t.defaultPrevented||y(e??document.body,{select:!0}),w.removeEventListener(f,E),g.remove(R)},0)}}},[w,x,E,R]);let N=r.useCallback(e=>{if(!n&&!o||R.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,r=document.activeElement;if(t&&r){let t=e.currentTarget,[o,i]=function(e){let t=m(e);return[h(t,e),h(t.reverse(),e)]}(t);o&&i?e.shiftKey||r!==i?e.shiftKey&&r===o&&(e.preventDefault(),n&&y(i,{select:!0})):(e.preventDefault(),n&&y(o,{select:!0})):r===t&&e.preventDefault()}},[n,o,R.paused]);return(0,l.jsx)(s.div,{tabIndex:-1,...v,ref:S,onKeyDown:N})});function m(e){let t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function h(e,t){for(let n of e)if(!function(e,{upTo:t}){if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===t||e!==t);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(n,{upTo:t}))return n}function y(e,{select:t=!1}={}){if(e&&e.focus){var n;let r=document.activeElement;e.focus({preventScroll:!0}),e!==r&&(n=e)instanceof HTMLInputElement&&"select"in n&&t&&e.select()}}v.displayName="FocusScope";var g=function(){let e=[];return{add(t){let n=e[0];t!==n&&n?.pause(),(e=w(e,t)).unshift(t)},remove(t){e=w(e,t),e[0]?.resume()}}}();function w(e,t){let n=[...e],r=n.indexOf(t);return -1!==r&&n.splice(r,1),n}},23049:(e,t,n)=>{n.d(t,{b:()=>a});var r=n(57845),o=n(39706),i=n(37785),l=r.forwardRef((e,t)=>(0,i.jsx)(o.sG.label,{...e,ref:t,onMouseDown:t=>{t.target.closest("button, input, select, textarea")||(e.onMouseDown?.(t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));l.displayName="Label";var a=l},13997:(e,t,n)=>{n.d(t,{UC:()=>e4,In:()=>e7,q7:()=>e8,VF:()=>tt,p4:()=>te,ZL:()=>e3,bL:()=>e2,l9:()=>e5,WT:()=>e9,LM:()=>e6});var r,o=n(57845),i=n.t(o,2),l=n(75724);function a(e,[t,n]){return Math.min(n,Math.max(t,e))}function u(e,t,{checkForDefaultPrevented:n=!0}={}){return function(r){if(e?.(r),!1===n||!r.defaultPrevented)return t?.(r)}}var s=n(37785);function c(e,t=[]){let n=[],r=()=>{let t=n.map(e=>o.createContext(e));return function(n){let r=n?.[e]||t;return o.useMemo(()=>({[`__scope${e}`]:{...n,[e]:r}}),[n,r])}};return r.scopeName=e,[function(t,r){let i=o.createContext(r),l=n.length;n=[...n,r];let a=t=>{let{scope:n,children:r,...a}=t,u=n?.[e]?.[l]||i,c=o.useMemo(()=>a,Object.values(a));return(0,s.jsx)(u.Provider,{value:c,children:r})};return a.displayName=t+"Provider",[a,function(n,a){let u=a?.[e]?.[l]||i,s=o.useContext(u);if(s)return s;if(void 0!==r)return r;throw Error(`\`${n}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let n=()=>{let n=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let r=n.reduce((t,{useScope:n,scopeName:r})=>{let o=n(e)[`__scope${r}`];return{...t,...o}},{});return o.useMemo(()=>({[`__scope${t.scopeName}`]:r}),[r])}};return n.scopeName=t.scopeName,n}(r,...t)]}function d(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function f(...e){return t=>{let n=!1,r=e.map(e=>{let r=d(e,t);return n||"function"!=typeof r||(n=!0),r});if(n)return()=>{for(let t=0;t<r.length;t++){let n=r[t];"function"==typeof n?n():d(e[t],null)}}}}function p(...e){return o.useCallback(f(...e),e)}function v(e){let t=function(e){let t=o.forwardRef((e,t)=>{let{children:n,...r}=e;if(o.isValidElement(n)){let e,i;let l=(e=Object.getOwnPropertyDescriptor(n.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?n.ref:(e=Object.getOwnPropertyDescriptor(n,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?n.props.ref:n.props.ref||n.ref,a=function(e,t){let n={...t};for(let r in t){let o=e[r],i=t[r];/^on[A-Z]/.test(r)?o&&i?n[r]=(...e)=>{let t=i(...e);return o(...e),t}:o&&(n[r]=o):"style"===r?n[r]={...o,...i}:"className"===r&&(n[r]=[o,i].filter(Boolean).join(" "))}return{...e,...n}}(r,n.props);return n.type!==o.Fragment&&(a.ref=t?f(t,l):l),o.cloneElement(n,a)}return o.Children.count(n)>1?o.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),n=o.forwardRef((e,n)=>{let{children:r,...i}=e,l=o.Children.toArray(r),a=l.find(h);if(a){let e=a.props.children,r=l.map(t=>t!==a?t:o.Children.count(e)>1?o.Children.only(null):o.isValidElement(e)?e.props.children:null);return(0,s.jsx)(t,{...i,ref:n,children:o.isValidElement(e)?o.cloneElement(e,void 0,r):null})}return(0,s.jsx)(t,{...i,ref:n,children:r})});return n.displayName=`${e}.Slot`,n}var m=Symbol("radix.slottable");function h(e){return o.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===m}var y=o.createContext(void 0),g=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let n=v(`Primitive.${t}`),r=o.forwardRef((e,r)=>{let{asChild:o,...i}=e,l=o?n:t;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,s.jsx)(l,{...i,ref:r})});return r.displayName=`Primitive.${t}`,{...e,[t]:r}},{});function w(e){let t=o.useRef(e);return o.useEffect(()=>{t.current=e}),o.useMemo(()=>(...e)=>t.current?.(...e),[])}var b="dismissableLayer.update",x=o.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),E=o.forwardRef((e,t)=>{let{disableOutsidePointerEvents:n=!1,onEscapeKeyDown:i,onPointerDownOutside:l,onFocusOutside:a,onInteractOutside:c,onDismiss:d,...f}=e,v=o.useContext(x),[m,h]=o.useState(null),y=m?.ownerDocument??globalThis?.document,[,E]=o.useState({}),R=p(t,e=>h(e)),N=Array.from(v.layers),[j]=[...v.layersWithOutsidePointerEventsDisabled].slice(-1),P=N.indexOf(j),D=m?N.indexOf(m):-1,k=v.layersWithOutsidePointerEventsDisabled.size>0,A=D>=P,O=function(e,t=globalThis?.document){let n=w(e),r=o.useRef(!1),i=o.useRef(()=>{});return o.useEffect(()=>{let e=e=>{if(e.target&&!r.current){let r=function(){S("dismissableLayer.pointerDownOutside",n,o,{discrete:!0})},o={originalEvent:e};"touch"===e.pointerType?(t.removeEventListener("click",i.current),i.current=r,t.addEventListener("click",i.current,{once:!0})):r()}else t.removeEventListener("click",i.current);r.current=!1},o=window.setTimeout(()=>{t.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(o),t.removeEventListener("pointerdown",e),t.removeEventListener("click",i.current)}},[t,n]),{onPointerDownCapture:()=>r.current=!0}}(e=>{let t=e.target,n=[...v.branches].some(e=>e.contains(t));!A||n||(l?.(e),c?.(e),e.defaultPrevented||d?.())},y),T=function(e,t=globalThis?.document){let n=w(e),r=o.useRef(!1);return o.useEffect(()=>{let e=e=>{e.target&&!r.current&&S("dismissableLayer.focusOutside",n,{originalEvent:e},{discrete:!1})};return t.addEventListener("focusin",e),()=>t.removeEventListener("focusin",e)},[t,n]),{onFocusCapture:()=>r.current=!0,onBlurCapture:()=>r.current=!1}}(e=>{let t=e.target;[...v.branches].some(e=>e.contains(t))||(a?.(e),c?.(e),e.defaultPrevented||d?.())},y);return!function(e,t=globalThis?.document){let n=w(e);o.useEffect(()=>{let e=e=>{"Escape"===e.key&&n(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[n,t])}(e=>{D!==v.layers.size-1||(i?.(e),!e.defaultPrevented&&d&&(e.preventDefault(),d()))},y),o.useEffect(()=>{if(m)return n&&(0===v.layersWithOutsidePointerEventsDisabled.size&&(r=y.body.style.pointerEvents,y.body.style.pointerEvents="none"),v.layersWithOutsidePointerEventsDisabled.add(m)),v.layers.add(m),C(),()=>{n&&1===v.layersWithOutsidePointerEventsDisabled.size&&(y.body.style.pointerEvents=r)}},[m,y,n,v]),o.useEffect(()=>()=>{m&&(v.layers.delete(m),v.layersWithOutsidePointerEventsDisabled.delete(m),C())},[m,v]),o.useEffect(()=>{let e=()=>E({});return document.addEventListener(b,e),()=>document.removeEventListener(b,e)},[]),(0,s.jsx)(g.div,{...f,ref:R,style:{pointerEvents:k?A?"auto":"none":void 0,...e.style},onFocusCapture:u(e.onFocusCapture,T.onFocusCapture),onBlurCapture:u(e.onBlurCapture,T.onBlurCapture),onPointerDownCapture:u(e.onPointerDownCapture,O.onPointerDownCapture)})});function C(){let e=new CustomEvent(b);document.dispatchEvent(e)}function S(e,t,n,{discrete:r}){let o=n.originalEvent.target,i=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});(t&&o.addEventListener(e,t,{once:!0}),r)?o&&l.flushSync(()=>o.dispatchEvent(i)):o.dispatchEvent(i)}E.displayName="DismissableLayer",o.forwardRef((e,t)=>{let n=o.useContext(x),r=o.useRef(null),i=p(t,r);return o.useEffect(()=>{let e=r.current;if(e)return n.branches.add(e),()=>{n.branches.delete(e)}},[n.branches]),(0,s.jsx)(g.div,{...e,ref:i})}).displayName="DismissableLayerBranch";var R=n(9692),N=n(60560),j=globalThis?.document?o.useLayoutEffect:()=>{},P=i[" useId ".trim().toString()]||(()=>void 0),D=0;function k(e){let[t,n]=o.useState(P());return j(()=>{e||n(e=>e??String(D++))},[e]),e||(t?`radix-${t}`:"")}var A=n(99272),O=n(17076),T=o.forwardRef((e,t)=>{let{children:n,width:r=10,height:o=5,...i}=e;return(0,s.jsx)(g.svg,{...i,ref:t,width:r,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:(0,s.jsx)("polygon",{points:"0,0 30,0 15,10"})})});T.displayName="Arrow";var M="Popper",[L,I]=c(M),[_,W]=L(M),F=e=>{let{__scopePopper:t,children:n}=e,[r,i]=o.useState(null);return(0,s.jsx)(_,{scope:t,anchor:r,onAnchorChange:i,children:n})};F.displayName=M;var $="PopperAnchor",B=o.forwardRef((e,t)=>{let{__scopePopper:n,virtualRef:r,...i}=e,l=W($,n),a=o.useRef(null),u=p(t,a);return o.useEffect(()=>{l.onAnchorChange(r?.current||a.current)}),r?null:(0,s.jsx)(g.div,{...i,ref:u})});B.displayName=$;var H="PopperContent",[V,U]=L(H),z=o.forwardRef((e,t)=>{let{__scopePopper:n,side:r="bottom",sideOffset:i=0,align:l="center",alignOffset:a=0,arrowPadding:u=0,avoidCollisions:c=!0,collisionBoundary:d=[],collisionPadding:f=0,sticky:v="partial",hideWhenDetached:m=!1,updatePositionStrategy:h="optimized",onPlaced:y,...b}=e,x=W(H,n),[E,C]=o.useState(null),S=p(t,e=>C(e)),[R,N]=o.useState(null),P=function(e){let[t,n]=o.useState(void 0);return j(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let r,o;if(!Array.isArray(t)||!t.length)return;let i=t[0];if("borderBoxSize"in i){let e=i.borderBoxSize,t=Array.isArray(e)?e[0]:e;r=t.inlineSize,o=t.blockSize}else r=e.offsetWidth,o=e.offsetHeight;n({width:r,height:o})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}n(void 0)},[e]),t}(R),D=P?.width??0,k=P?.height??0,T="number"==typeof f?f:{top:0,right:0,bottom:0,left:0,...f},M=Array.isArray(d)?d:[d],L=M.length>0,I={padding:T,boundary:M.filter(X),altBoundary:L},{refs:_,floatingStyles:F,placement:$,isPositioned:B,middlewareData:U}=(0,A.we)({strategy:"fixed",placement:r+("center"!==l?"-"+l:""),whileElementsMounted:(...e)=>(0,O.ll)(...e,{animationFrame:"always"===h}),elements:{reference:x.anchor},middleware:[(0,A.cY)({mainAxis:i+k,alignmentAxis:a}),c&&(0,A.BN)({mainAxis:!0,crossAxis:!1,limiter:"partial"===v?(0,A.ER)():void 0,...I}),c&&(0,A.UU)({...I}),(0,A.Ej)({...I,apply:({elements:e,rects:t,availableWidth:n,availableHeight:r})=>{let{width:o,height:i}=t.reference,l=e.floating.style;l.setProperty("--radix-popper-available-width",`${n}px`),l.setProperty("--radix-popper-available-height",`${r}px`),l.setProperty("--radix-popper-anchor-width",`${o}px`),l.setProperty("--radix-popper-anchor-height",`${i}px`)}}),R&&(0,A.UE)({element:R,padding:u}),Z({arrowWidth:D,arrowHeight:k}),m&&(0,A.jD)({strategy:"referenceHidden",...I})]}),[z,K]=G($),Y=w(y);j(()=>{B&&Y?.()},[B,Y]);let q=U.arrow?.x,J=U.arrow?.y,Q=U.arrow?.centerOffset!==0,[ee,et]=o.useState();return j(()=>{E&&et(window.getComputedStyle(E).zIndex)},[E]),(0,s.jsx)("div",{ref:_.setFloating,"data-radix-popper-content-wrapper":"",style:{...F,transform:B?F.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:ee,"--radix-popper-transform-origin":[U.transformOrigin?.x,U.transformOrigin?.y].join(" "),...U.hide?.referenceHidden&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,s.jsx)(V,{scope:n,placedSide:z,onArrowChange:N,arrowX:q,arrowY:J,shouldHideArrow:Q,children:(0,s.jsx)(g.div,{"data-side":z,"data-align":K,...b,ref:S,style:{...b.style,animation:B?void 0:"none"}})})})});z.displayName=H;var K="PopperArrow",Y={top:"bottom",right:"left",bottom:"top",left:"right"},q=o.forwardRef(function(e,t){let{__scopePopper:n,...r}=e,o=U(K,n),i=Y[o.placedSide];return(0,s.jsx)("span",{ref:o.onArrowChange,style:{position:"absolute",left:o.arrowX,top:o.arrowY,[i]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[o.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[o.placedSide],visibility:o.shouldHideArrow?"hidden":void 0},children:(0,s.jsx)(T,{...r,ref:t,style:{...r.style,display:"block"}})})});function X(e){return null!==e}q.displayName=K;var Z=e=>({name:"transformOrigin",options:e,fn(t){let{placement:n,rects:r,middlewareData:o}=t,i=o.arrow?.centerOffset!==0,l=i?0:e.arrowWidth,a=i?0:e.arrowHeight,[u,s]=G(n),c={start:"0%",center:"50%",end:"100%"}[s],d=(o.arrow?.x??0)+l/2,f=(o.arrow?.y??0)+a/2,p="",v="";return"bottom"===u?(p=i?c:`${d}px`,v=`${-a}px`):"top"===u?(p=i?c:`${d}px`,v=`${r.floating.height+a}px`):"right"===u?(p=`${-a}px`,v=i?c:`${f}px`):"left"===u&&(p=`${r.floating.width+a}px`,v=i?c:`${f}px`),{data:{x:p,y:v}}}});function G(e){let[t,n="center"]=e.split("-");return[t,n]}var J=o.forwardRef((e,t)=>{let{container:n,...r}=e,[i,a]=o.useState(!1);j(()=>a(!0),[]);let u=n||i&&globalThis?.document?.body;return u?l.createPortal((0,s.jsx)(g.div,{...r,ref:t}),u):null});J.displayName="Portal";var Q=i[" useInsertionEffect ".trim().toString()]||j;function ee({prop:e,defaultProp:t,onChange:n=()=>{},caller:r}){let[i,l,a]=function({defaultProp:e,onChange:t}){let[n,r]=o.useState(e),i=o.useRef(n),l=o.useRef(t);return Q(()=>{l.current=t},[t]),o.useEffect(()=>{i.current!==n&&(l.current?.(n),i.current=n)},[n,i]),[n,r,l]}({defaultProp:t,onChange:n}),u=void 0!==e,s=u?e:i;{let t=o.useRef(void 0!==e);o.useEffect(()=>{let e=t.current;if(e!==u){let t=u?"controlled":"uncontrolled";console.warn(`${r} is changing from ${e?"controlled":"uncontrolled"} to ${t}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}t.current=u},[u,r])}return[s,o.useCallback(t=>{if(u){let n="function"==typeof t?t(e):t;n!==e&&a.current?.(n)}else l(t)},[u,e,l,a])]}Symbol("RADIX:SYNC_STATE");var et=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"});o.forwardRef((e,t)=>(0,s.jsx)(g.span,{...e,ref:t,style:{...et,...e.style}})).displayName="VisuallyHidden";var en=n(75629),er=n(20324),eo=[" ","Enter","ArrowUp","ArrowDown"],ei=[" ","Enter"],el="Select",[ea,eu,es]=function(e){let t=e+"CollectionProvider",[n,r]=c(t),[i,l]=n(t,{collectionRef:{current:null},itemMap:new Map}),a=e=>{let{scope:t,children:n}=e,r=o.useRef(null),l=o.useRef(new Map).current;return(0,s.jsx)(i,{scope:t,itemMap:l,collectionRef:r,children:n})};a.displayName=t;let u=e+"CollectionSlot",d=v(u),f=o.forwardRef((e,t)=>{let{scope:n,children:r}=e,o=p(t,l(u,n).collectionRef);return(0,s.jsx)(d,{ref:o,children:r})});f.displayName=u;let m=e+"CollectionItemSlot",h="data-radix-collection-item",y=v(m),g=o.forwardRef((e,t)=>{let{scope:n,children:r,...i}=e,a=o.useRef(null),u=p(t,a),c=l(m,n);return o.useEffect(()=>(c.itemMap.set(a,{ref:a,...i}),()=>void c.itemMap.delete(a))),(0,s.jsx)(y,{[h]:"",ref:u,children:r})});return g.displayName=m,[{Provider:a,Slot:f,ItemSlot:g},function(t){let n=l(e+"CollectionConsumer",t);return o.useCallback(()=>{let e=n.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll(`[${h}]`));return Array.from(n.itemMap.values()).sort((e,n)=>t.indexOf(e.ref.current)-t.indexOf(n.ref.current))},[n.collectionRef,n.itemMap])},r]}(el),[ec,ed]=c(el,[es,I]),ef=I(),[ep,ev]=ec(el),[em,eh]=ec(el),ey=e=>{let{__scopeSelect:t,children:n,open:r,defaultOpen:i,onOpenChange:l,value:a,defaultValue:u,onValueChange:c,dir:d,name:f,autoComplete:p,disabled:v,required:m,form:h}=e,g=ef(t),[w,b]=o.useState(null),[x,E]=o.useState(null),[C,S]=o.useState(!1),R=function(e){let t=o.useContext(y);return e||t||"ltr"}(d),[N,j]=ee({prop:r,defaultProp:i??!1,onChange:l,caller:el}),[P,D]=ee({prop:a,defaultProp:u,onChange:c,caller:el}),A=o.useRef(null),O=!w||h||!!w.closest("form"),[T,M]=o.useState(new Set),L=Array.from(T).map(e=>e.props.value).join(";");return(0,s.jsx)(F,{...g,children:(0,s.jsxs)(ep,{required:m,scope:t,trigger:w,onTriggerChange:b,valueNode:x,onValueNodeChange:E,valueNodeHasChildren:C,onValueNodeHasChildrenChange:S,contentId:k(),value:P,onValueChange:D,open:N,onOpenChange:j,dir:R,triggerPointerDownPosRef:A,disabled:v,children:[(0,s.jsx)(ea.Provider,{scope:t,children:(0,s.jsx)(em,{scope:e.__scopeSelect,onNativeOptionAdd:o.useCallback(e=>{M(t=>new Set(t).add(e))},[]),onNativeOptionRemove:o.useCallback(e=>{M(t=>{let n=new Set(t);return n.delete(e),n})},[]),children:n})}),O?(0,s.jsxs)(eJ,{"aria-hidden":!0,required:m,tabIndex:-1,name:f,autoComplete:p,value:P,onChange:e=>D(e.target.value),disabled:v,form:h,children:[void 0===P?(0,s.jsx)("option",{value:""}):null,Array.from(T)]},L):null]})})};ey.displayName=el;var eg="SelectTrigger",ew=o.forwardRef((e,t)=>{let{__scopeSelect:n,disabled:r=!1,...i}=e,l=ef(n),a=ev(eg,n),c=a.disabled||r,d=p(t,a.onTriggerChange),f=eu(n),v=o.useRef("touch"),[m,h,y]=e0(e=>{let t=f().filter(e=>!e.disabled),n=t.find(e=>e.value===a.value),r=e1(t,e,n);void 0!==r&&a.onValueChange(r.value)}),w=e=>{c||(a.onOpenChange(!0),y()),e&&(a.triggerPointerDownPosRef.current={x:Math.round(e.pageX),y:Math.round(e.pageY)})};return(0,s.jsx)(B,{asChild:!0,...l,children:(0,s.jsx)(g.button,{type:"button",role:"combobox","aria-controls":a.contentId,"aria-expanded":a.open,"aria-required":a.required,"aria-autocomplete":"none",dir:a.dir,"data-state":a.open?"open":"closed",disabled:c,"data-disabled":c?"":void 0,"data-placeholder":eQ(a.value)?"":void 0,...i,ref:d,onClick:u(i.onClick,e=>{e.currentTarget.focus(),"mouse"!==v.current&&w(e)}),onPointerDown:u(i.onPointerDown,e=>{v.current=e.pointerType;let t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),0===e.button&&!1===e.ctrlKey&&"mouse"===e.pointerType&&(w(e),e.preventDefault())}),onKeyDown:u(i.onKeyDown,e=>{let t=""!==m.current;e.ctrlKey||e.altKey||e.metaKey||1!==e.key.length||h(e.key),(!t||" "!==e.key)&&eo.includes(e.key)&&(w(),e.preventDefault())})})})});ew.displayName=eg;var eb="SelectValue",ex=o.forwardRef((e,t)=>{let{__scopeSelect:n,className:r,style:o,children:i,placeholder:l="",...a}=e,u=ev(eb,n),{onValueNodeHasChildrenChange:c}=u,d=void 0!==i,f=p(t,u.onValueNodeChange);return j(()=>{c(d)},[c,d]),(0,s.jsx)(g.span,{...a,ref:f,style:{pointerEvents:"none"},children:eQ(u.value)?(0,s.jsx)(s.Fragment,{children:l}):i})});ex.displayName=eb;var eE=o.forwardRef((e,t)=>{let{__scopeSelect:n,children:r,...o}=e;return(0,s.jsx)(g.span,{"aria-hidden":!0,...o,ref:t,children:r||"▼"})});eE.displayName="SelectIcon";var eC=e=>(0,s.jsx)(J,{asChild:!0,...e});eC.displayName="SelectPortal";var eS="SelectContent",eR=o.forwardRef((e,t)=>{let n=ev(eS,e.__scopeSelect),[r,i]=o.useState();return(j(()=>{i(new DocumentFragment)},[]),n.open)?(0,s.jsx)(eD,{...e,ref:t}):r?l.createPortal((0,s.jsx)(eN,{scope:e.__scopeSelect,children:(0,s.jsx)(ea.Slot,{scope:e.__scopeSelect,children:(0,s.jsx)("div",{children:e.children})})}),r):null});eR.displayName=eS;var[eN,ej]=ec(eS),eP=v("SelectContent.RemoveScroll"),eD=o.forwardRef((e,t)=>{let{__scopeSelect:n,position:r="item-aligned",onCloseAutoFocus:i,onEscapeKeyDown:l,onPointerDownOutside:a,side:c,sideOffset:d,align:f,alignOffset:v,arrowPadding:m,collisionBoundary:h,collisionPadding:y,sticky:g,hideWhenDetached:w,avoidCollisions:b,...x}=e,C=ev(eS,n),[S,j]=o.useState(null),[P,D]=o.useState(null),k=p(t,e=>j(e)),[A,O]=o.useState(null),[T,M]=o.useState(null),L=eu(n),[I,_]=o.useState(!1),W=o.useRef(!1);o.useEffect(()=>{if(S)return(0,en.Eq)(S)},[S]),(0,R.Oh)();let F=o.useCallback(e=>{let[t,...n]=L().map(e=>e.ref.current),[r]=n.slice(-1),o=document.activeElement;for(let n of e)if(n===o||(n?.scrollIntoView({block:"nearest"}),n===t&&P&&(P.scrollTop=0),n===r&&P&&(P.scrollTop=P.scrollHeight),n?.focus(),document.activeElement!==o))return},[L,P]),$=o.useCallback(()=>F([A,S]),[F,A,S]);o.useEffect(()=>{I&&$()},[I,$]);let{onOpenChange:B,triggerPointerDownPosRef:H}=C;o.useEffect(()=>{if(S){let e={x:0,y:0},t=t=>{e={x:Math.abs(Math.round(t.pageX)-(H.current?.x??0)),y:Math.abs(Math.round(t.pageY)-(H.current?.y??0))}},n=n=>{e.x<=10&&e.y<=10?n.preventDefault():S.contains(n.target)||B(!1),document.removeEventListener("pointermove",t),H.current=null};return null!==H.current&&(document.addEventListener("pointermove",t),document.addEventListener("pointerup",n,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",t),document.removeEventListener("pointerup",n,{capture:!0})}}},[S,B,H]),o.useEffect(()=>{let e=()=>B(!1);return window.addEventListener("blur",e),window.addEventListener("resize",e),()=>{window.removeEventListener("blur",e),window.removeEventListener("resize",e)}},[B]);let[V,U]=e0(e=>{let t=L().filter(e=>!e.disabled),n=t.find(e=>e.ref.current===document.activeElement),r=e1(t,e,n);r&&setTimeout(()=>r.ref.current.focus())}),z=o.useCallback((e,t,n)=>{let r=!W.current&&!n;(void 0!==C.value&&C.value===t||r)&&(O(e),r&&(W.current=!0))},[C.value]),K=o.useCallback(()=>S?.focus(),[S]),Y=o.useCallback((e,t,n)=>{let r=!W.current&&!n;(void 0!==C.value&&C.value===t||r)&&M(e)},[C.value]),q="popper"===r?eA:ek,X=q===eA?{side:c,sideOffset:d,align:f,alignOffset:v,arrowPadding:m,collisionBoundary:h,collisionPadding:y,sticky:g,hideWhenDetached:w,avoidCollisions:b}:{};return(0,s.jsx)(eN,{scope:n,content:S,viewport:P,onViewportChange:D,itemRefCallback:z,selectedItem:A,onItemLeave:K,itemTextRefCallback:Y,focusSelectedItem:$,selectedItemText:T,position:r,isPositioned:I,searchRef:V,children:(0,s.jsx)(er.A,{as:eP,allowPinchZoom:!0,children:(0,s.jsx)(N.n,{asChild:!0,trapped:C.open,onMountAutoFocus:e=>{e.preventDefault()},onUnmountAutoFocus:u(i,e=>{C.trigger?.focus({preventScroll:!0}),e.preventDefault()}),children:(0,s.jsx)(E,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:l,onPointerDownOutside:a,onFocusOutside:e=>e.preventDefault(),onDismiss:()=>C.onOpenChange(!1),children:(0,s.jsx)(q,{role:"listbox",id:C.contentId,"data-state":C.open?"open":"closed",dir:C.dir,onContextMenu:e=>e.preventDefault(),...x,...X,onPlaced:()=>_(!0),ref:k,style:{display:"flex",flexDirection:"column",outline:"none",...x.style},onKeyDown:u(x.onKeyDown,e=>{let t=e.ctrlKey||e.altKey||e.metaKey;if("Tab"===e.key&&e.preventDefault(),t||1!==e.key.length||U(e.key),["ArrowUp","ArrowDown","Home","End"].includes(e.key)){let t=L().filter(e=>!e.disabled).map(e=>e.ref.current);if(["ArrowUp","End"].includes(e.key)&&(t=t.slice().reverse()),["ArrowUp","ArrowDown"].includes(e.key)){let n=e.target,r=t.indexOf(n);t=t.slice(r+1)}setTimeout(()=>F(t)),e.preventDefault()}})})})})})})});eD.displayName="SelectContentImpl";var ek=o.forwardRef((e,t)=>{let{__scopeSelect:n,onPlaced:r,...i}=e,l=ev(eS,n),u=ej(eS,n),[c,d]=o.useState(null),[f,v]=o.useState(null),m=p(t,e=>v(e)),h=eu(n),y=o.useRef(!1),w=o.useRef(!0),{viewport:b,selectedItem:x,selectedItemText:E,focusSelectedItem:C}=u,S=o.useCallback(()=>{if(l.trigger&&l.valueNode&&c&&f&&b&&x&&E){let e=l.trigger.getBoundingClientRect(),t=f.getBoundingClientRect(),n=l.valueNode.getBoundingClientRect(),o=E.getBoundingClientRect();if("rtl"!==l.dir){let r=o.left-t.left,i=n.left-r,l=e.left-i,u=e.width+l,s=Math.max(u,t.width),d=a(i,[10,Math.max(10,window.innerWidth-10-s)]);c.style.minWidth=u+"px",c.style.left=d+"px"}else{let r=t.right-o.right,i=window.innerWidth-n.right-r,l=window.innerWidth-e.right-i,u=e.width+l,s=Math.max(u,t.width),d=a(i,[10,Math.max(10,window.innerWidth-10-s)]);c.style.minWidth=u+"px",c.style.right=d+"px"}let i=h(),u=window.innerHeight-20,s=b.scrollHeight,d=window.getComputedStyle(f),p=parseInt(d.borderTopWidth,10),v=parseInt(d.paddingTop,10),m=parseInt(d.borderBottomWidth,10),g=p+v+s+parseInt(d.paddingBottom,10)+m,w=Math.min(5*x.offsetHeight,g),C=window.getComputedStyle(b),S=parseInt(C.paddingTop,10),R=parseInt(C.paddingBottom,10),N=e.top+e.height/2-10,j=x.offsetHeight/2,P=p+v+(x.offsetTop+j);if(P<=N){let e=i.length>0&&x===i[i.length-1].ref.current;c.style.bottom="0px";let t=Math.max(u-N,j+(e?R:0)+(f.clientHeight-b.offsetTop-b.offsetHeight)+m);c.style.height=P+t+"px"}else{let e=i.length>0&&x===i[0].ref.current;c.style.top="0px";let t=Math.max(N,p+b.offsetTop+(e?S:0)+j);c.style.height=t+(g-P)+"px",b.scrollTop=P-N+b.offsetTop}c.style.margin="10px 0",c.style.minHeight=w+"px",c.style.maxHeight=u+"px",r?.(),requestAnimationFrame(()=>y.current=!0)}},[h,l.trigger,l.valueNode,c,f,b,x,E,l.dir,r]);j(()=>S(),[S]);let[R,N]=o.useState();j(()=>{f&&N(window.getComputedStyle(f).zIndex)},[f]);let P=o.useCallback(e=>{e&&!0===w.current&&(S(),C?.(),w.current=!1)},[S,C]);return(0,s.jsx)(eO,{scope:n,contentWrapper:c,shouldExpandOnScrollRef:y,onScrollButtonChange:P,children:(0,s.jsx)("div",{ref:d,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:R},children:(0,s.jsx)(g.div,{...i,ref:m,style:{boxSizing:"border-box",maxHeight:"100%",...i.style}})})})});ek.displayName="SelectItemAlignedPosition";var eA=o.forwardRef((e,t)=>{let{__scopeSelect:n,align:r="start",collisionPadding:o=10,...i}=e,l=ef(n);return(0,s.jsx)(z,{...l,...i,ref:t,align:r,collisionPadding:o,style:{boxSizing:"border-box",...i.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});eA.displayName="SelectPopperPosition";var[eO,eT]=ec(eS,{}),eM="SelectViewport",eL=o.forwardRef((e,t)=>{let{__scopeSelect:n,nonce:r,...i}=e,l=ej(eM,n),a=eT(eM,n),c=p(t,l.onViewportChange),d=o.useRef(0);return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:r}),(0,s.jsx)(ea.Slot,{scope:n,children:(0,s.jsx)(g.div,{"data-radix-select-viewport":"",role:"presentation",...i,ref:c,style:{position:"relative",flex:1,overflow:"hidden auto",...i.style},onScroll:u(i.onScroll,e=>{let t=e.currentTarget,{contentWrapper:n,shouldExpandOnScrollRef:r}=a;if(r?.current&&n){let e=Math.abs(d.current-t.scrollTop);if(e>0){let r=window.innerHeight-20,o=Math.max(parseFloat(n.style.minHeight),parseFloat(n.style.height));if(o<r){let i=o+e,l=Math.min(r,i),a=i-l;n.style.height=l+"px","0px"===n.style.bottom&&(t.scrollTop=a>0?a:0,n.style.justifyContent="flex-end")}}}d.current=t.scrollTop})})})]})});eL.displayName=eM;var eI="SelectGroup",[e_,eW]=ec(eI);o.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e,o=k();return(0,s.jsx)(e_,{scope:n,id:o,children:(0,s.jsx)(g.div,{role:"group","aria-labelledby":o,...r,ref:t})})}).displayName=eI;var eF="SelectLabel";o.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e,o=eW(eF,n);return(0,s.jsx)(g.div,{id:o.id,...r,ref:t})}).displayName=eF;var e$="SelectItem",[eB,eH]=ec(e$),eV=o.forwardRef((e,t)=>{let{__scopeSelect:n,value:r,disabled:i=!1,textValue:l,...a}=e,c=ev(e$,n),d=ej(e$,n),f=c.value===r,[v,m]=o.useState(l??""),[h,y]=o.useState(!1),w=p(t,e=>d.itemRefCallback?.(e,r,i)),b=k(),x=o.useRef("touch"),E=()=>{i||(c.onValueChange(r),c.onOpenChange(!1))};if(""===r)throw Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return(0,s.jsx)(eB,{scope:n,value:r,disabled:i,textId:b,isSelected:f,onItemTextChange:o.useCallback(e=>{m(t=>t||(e?.textContent??"").trim())},[]),children:(0,s.jsx)(ea.ItemSlot,{scope:n,value:r,disabled:i,textValue:v,children:(0,s.jsx)(g.div,{role:"option","aria-labelledby":b,"data-highlighted":h?"":void 0,"aria-selected":f&&h,"data-state":f?"checked":"unchecked","aria-disabled":i||void 0,"data-disabled":i?"":void 0,tabIndex:i?void 0:-1,...a,ref:w,onFocus:u(a.onFocus,()=>y(!0)),onBlur:u(a.onBlur,()=>y(!1)),onClick:u(a.onClick,()=>{"mouse"!==x.current&&E()}),onPointerUp:u(a.onPointerUp,()=>{"mouse"===x.current&&E()}),onPointerDown:u(a.onPointerDown,e=>{x.current=e.pointerType}),onPointerMove:u(a.onPointerMove,e=>{x.current=e.pointerType,i?d.onItemLeave?.():"mouse"===x.current&&e.currentTarget.focus({preventScroll:!0})}),onPointerLeave:u(a.onPointerLeave,e=>{e.currentTarget===document.activeElement&&d.onItemLeave?.()}),onKeyDown:u(a.onKeyDown,e=>{d.searchRef?.current!==""&&" "===e.key||(ei.includes(e.key)&&E()," "===e.key&&e.preventDefault())})})})})});eV.displayName=e$;var eU="SelectItemText",ez=o.forwardRef((e,t)=>{let{__scopeSelect:n,className:r,style:i,...a}=e,u=ev(eU,n),c=ej(eU,n),d=eH(eU,n),f=eh(eU,n),[v,m]=o.useState(null),h=p(t,e=>m(e),d.onItemTextChange,e=>c.itemTextRefCallback?.(e,d.value,d.disabled)),y=v?.textContent,w=o.useMemo(()=>(0,s.jsx)("option",{value:d.value,disabled:d.disabled,children:y},d.value),[d.disabled,d.value,y]),{onNativeOptionAdd:b,onNativeOptionRemove:x}=f;return j(()=>(b(w),()=>x(w)),[b,x,w]),(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(g.span,{id:d.textId,...a,ref:h}),d.isSelected&&u.valueNode&&!u.valueNodeHasChildren?l.createPortal(a.children,u.valueNode):null]})});ez.displayName=eU;var eK="SelectItemIndicator",eY=o.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e;return eH(eK,n).isSelected?(0,s.jsx)(g.span,{"aria-hidden":!0,...r,ref:t}):null});eY.displayName=eK;var eq="SelectScrollUpButton";o.forwardRef((e,t)=>{let n=ej(eq,e.__scopeSelect),r=eT(eq,e.__scopeSelect),[i,l]=o.useState(!1),a=p(t,r.onScrollButtonChange);return j(()=>{if(n.viewport&&n.isPositioned){let e=function(){l(t.scrollTop>0)},t=n.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[n.viewport,n.isPositioned]),i?(0,s.jsx)(eZ,{...e,ref:a,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=n;e&&t&&(e.scrollTop=e.scrollTop-t.offsetHeight)}}):null}).displayName=eq;var eX="SelectScrollDownButton";o.forwardRef((e,t)=>{let n=ej(eX,e.__scopeSelect),r=eT(eX,e.__scopeSelect),[i,l]=o.useState(!1),a=p(t,r.onScrollButtonChange);return j(()=>{if(n.viewport&&n.isPositioned){let e=function(){let e=t.scrollHeight-t.clientHeight;l(Math.ceil(t.scrollTop)<e)},t=n.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[n.viewport,n.isPositioned]),i?(0,s.jsx)(eZ,{...e,ref:a,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=n;e&&t&&(e.scrollTop=e.scrollTop+t.offsetHeight)}}):null}).displayName=eX;var eZ=o.forwardRef((e,t)=>{let{__scopeSelect:n,onAutoScroll:r,...i}=e,l=ej("SelectScrollButton",n),a=o.useRef(null),c=eu(n),d=o.useCallback(()=>{null!==a.current&&(window.clearInterval(a.current),a.current=null)},[]);return o.useEffect(()=>()=>d(),[d]),j(()=>{let e=c().find(e=>e.ref.current===document.activeElement);e?.ref.current?.scrollIntoView({block:"nearest"})},[c]),(0,s.jsx)(g.div,{"aria-hidden":!0,...i,ref:t,style:{flexShrink:0,...i.style},onPointerDown:u(i.onPointerDown,()=>{null===a.current&&(a.current=window.setInterval(r,50))}),onPointerMove:u(i.onPointerMove,()=>{l.onItemLeave?.(),null===a.current&&(a.current=window.setInterval(r,50))}),onPointerLeave:u(i.onPointerLeave,()=>{d()})})});o.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e;return(0,s.jsx)(g.div,{"aria-hidden":!0,...r,ref:t})}).displayName="SelectSeparator";var eG="SelectArrow";o.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e,o=ef(n),i=ev(eG,n),l=ej(eG,n);return i.open&&"popper"===l.position?(0,s.jsx)(q,{...o,...r,ref:t}):null}).displayName=eG;var eJ=o.forwardRef(({__scopeSelect:e,value:t,...n},r)=>{let i=o.useRef(null),l=p(r,i),a=function(e){let t=o.useRef({value:e,previous:e});return o.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}(t);return o.useEffect(()=>{let e=i.current;if(!e)return;let n=Object.getOwnPropertyDescriptor(window.HTMLSelectElement.prototype,"value").set;if(a!==t&&n){let r=new Event("change",{bubbles:!0});n.call(e,t),e.dispatchEvent(r)}},[a,t]),(0,s.jsx)(g.select,{...n,style:{...et,...n.style},ref:l,defaultValue:t})});function eQ(e){return""===e||void 0===e}function e0(e){let t=w(e),n=o.useRef(""),r=o.useRef(0),i=o.useCallback(e=>{let o=n.current+e;t(o),function e(t){n.current=t,window.clearTimeout(r.current),""!==t&&(r.current=window.setTimeout(()=>e(""),1e3))}(o)},[t]),l=o.useCallback(()=>{n.current="",window.clearTimeout(r.current)},[]);return o.useEffect(()=>()=>window.clearTimeout(r.current),[]),[n,i,l]}function e1(e,t,n){var r;let o=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,i=(r=Math.max(n?e.indexOf(n):-1,0),e.map((t,n)=>e[(r+n)%e.length]));1===o.length&&(i=i.filter(e=>e!==n));let l=i.find(e=>e.textValue.toLowerCase().startsWith(o.toLowerCase()));return l!==n?l:void 0}eJ.displayName="SelectBubbleInput";var e2=ey,e5=ew,e9=ex,e7=eE,e3=eC,e4=eR,e6=eL,e8=eV,te=ez,tt=eY},60834:(e,t,n)=>{n.d(t,{Z:()=>o});var r=n(57845);function o(e){let t=r.useRef({value:e,previous:e});return r.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}},38144:(e,t,n)=>{n.d(t,{v:()=>u});var r=n(57845);let o=e=>{let t;let n=new Set,r=(e,r)=>{let o="function"==typeof e?e(t):e;if(!Object.is(o,t)){let e=t;t=(null!=r?r:"object"!=typeof o||null===o)?o:Object.assign({},t,o),n.forEach(n=>n(t,e))}},o=()=>t,i={setState:r,getState:o,getInitialState:()=>l,subscribe:e=>(n.add(e),()=>n.delete(e))},l=t=e(r,o,i);return i},i=e=>e?o(e):o,l=e=>e,a=e=>{let t=i(e),n=e=>(function(e,t=l){let n=r.useSyncExternalStore(e.subscribe,()=>t(e.getState()),()=>t(e.getInitialState()));return r.useDebugValue(n),n})(t,e);return Object.assign(n,t),n},u=e=>e?a(e):a}}]);
//# sourceMappingURL=672.js.map