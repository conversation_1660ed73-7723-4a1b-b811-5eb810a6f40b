import { auth } from "@/lib/auth"
import { NextResponse } from "next/server"
import { checkPermission } from "@/lib/auth"
import { PERMISSIONS } from "@/lib/permissions"
import { EmailCredentialManager } from "@/lib/emailCredentials"
import { z } from "zod"

export const runtime = "edge"

// 用户绑定邮箱的请求体验证
const bindEmailSchema = z.object({
  jwt: z.string().min(1, "邮箱凭证不能为空"),
})

// 获取所有邮箱凭证（管理员）
export async function GET() {
  const hasPermission = await checkPermission(PERMISSIONS.MANAGE_WEBHOOK)
  if (!hasPermission) {
    return NextResponse.json({ error: "权限不足" }, { status: 403 })
  }

  const session = await auth()
  if (!session?.user?.id) {
    return NextResponse.json({ error: "未授权" }, { status: 401 })
  }

  try {
    const credentials = await EmailCredentialManager.getAllCredentials()

    return NextResponse.json({
      credentials: credentials.map(credential => ({
        id: credential.id,
        emailAddress: credential.emailAddress,
        jwt: credential.jwt,
        enabled: credential.enabled,
        createdAt: credential.createdAt.toISOString(),
        lastUsedAt: credential.lastUsedAt?.toISOString() || null,
        bindingCount: credential.bindingCount || 0
      }))
    })
  } catch (error) {
    console.error("Failed to fetch email credentials:", error)
    return NextResponse.json(
      { error: "获取邮箱凭证失败" },
      { status: 500 }
    )
  }
}

// 用户绑定邮箱（通过邮箱凭证）
export async function POST(request: Request) {
  const session = await auth()
  if (!session?.user?.id) {
    return NextResponse.json({ error: "未授权" }, { status: 401 })
  }

  try {
    const body = await request.json()
    const validatedData = bindEmailSchema.parse(body)

    const binding = await EmailCredentialManager.bindEmailByCredential(
      session.user.id,
      validatedData.jwt
    )

    return NextResponse.json({
      success: true,
      message: "邮箱绑定成功",
      binding: {
        id: binding.id,
        emailAddress: binding.emailAddress,
        createdAt: binding.createdAt.toISOString()
      }
    })
  } catch (error) {
    console.error("Failed to bind email:", error)

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: error.errors[0].message },
        { status: 400 }
      )
    }

    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: "绑定邮箱失败" },
      { status: 500 }
    )
  }
}
