{"version": 3, "file": "app/api/emails/generate/route.js", "mappings": "sFAAA,8DCAA,oHIAA,uTHKO,IAAMA,EAAiC,CAC5C,CAAEC,MAAO,MAAOC,MAAO,IAAe,EACtC,CAD8B,KAAK,CAC1B,OAAQA,MAAO,KAAoB,EAAb,CAC7BD,IADkC,EAC3B,GADgC,EAC1BC,MAAO,MAAwB,CAAjB,CAC7B,CAAED,GADgC,GACzB,EAD8B,GACxBC,EAD6B,IACtB,CAAE,EACzB,0DCEM,IAAMC,EAAU,OAAM,eAEPC,EAAKC,CAAgB,EACzC,IAAMC,EAAKC,CAAAA,EAAAA,EAAAA,CAAAA,CAAQA,GACbC,EAAMC,CAAAA,EAAAA,EAAAA,iBAAAA,CAAiBA,GAAGD,GAAG,CAE7BE,EAAS,MAAMC,CAAAA,EAAAA,EAAAA,EAAAA,CAASA,GACxBC,EAAW,MAAMC,CAAAA,EAAAA,EAAAA,EAAAA,CAAWA,CAACH,GAEnC,GAAI,CACF,GAAIE,IAAaE,EAAAA,EAAKA,CAACC,OAAO,CAAE,CAC9B,IAAMC,EAAY,MAAMR,EAAIS,WAAW,CAACC,GAAG,CAAC,eAAiBC,EAAAA,CAAYA,CAACC,iBAAiB,CAACC,QAAQ,GAC9FC,EAAoB,MAAMhB,EAC7BiB,MAAM,CAAC,CAAEC,MAAOC,CAAAA,EAAAA,EAAAA,EAAAA,CAAW,CAAC,QAAQ,CAAC,GACrCC,IAAI,CAACC,EAAAA,MAAMA,EACXC,KAAK,CACJC,CAAAA,EAAAA,EAAAA,EAAAA,CAAGA,CACDC,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACH,EAAAA,MAAMA,CAACjB,MAAM,CAAEA,GAClBqB,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACJ,EAAAA,MAAMA,CAACK,SAAS,CAAE,IAAIC,QAI/B,GAAIC,OAAOZ,CAAiB,CAAC,EAAE,CAACE,KAAK,GAAKU,OAAOlB,GAC/C,OAAOmB,EADoD,EACxCA,CAACC,IAAI,CACtB,CAAEC,MAAO,CAAC,aAAa,EAAErB,EAAU,CAAC,CAAC,EACrC,CAAEsB,OAAQ,GAAI,EAGpB,CAEA,GAAM,MAAEC,CAAI,YAAEC,CAAU,QAAEC,CAAM,CAAE,CAAG,MAAMpC,EAAQ+B,IAAI,GAMvD,GAAI,CAACpC,EAAe0C,IAAI,CAACC,GAAUA,EAAOzC,EAAvBF,GAA4B,GAAKwC,GAClD,OAAOL,EAAAA,CADwD,CAC5CA,CAACC,IAAI,CACtB,CAAEC,MAAO,SAAU,EACnB,CAAEC,OAAQ,GAAI,GAIlB,IAAMM,EAAe,MAAMpC,EAAIS,WAAW,CAACC,GAAG,CAAC,iBACzC2B,EAAUD,EAAeA,EAAaE,KAAK,CAAC,KAAO,CAAC,cAAc,CAExE,GAAI,CAACD,GAAW,CAACA,EAAQE,QAAQ,CAACN,GAChC,MADyC,CAClCN,EAAAA,EAAYA,CAACC,IAAI,CACtB,CAAEC,MAAO,OAAQ,EACjB,CAAEC,OAAQ,GAAI,GAIlB,IAAMU,EAAU,GAAGT,GAAQU,CAAAA,EAAAA,EAAAA,EAAAA,CAAMA,CAAC,GAAG,CAAC,EAAER,EAAAA,CAAQ,CAKhD,GAJsB,CAIlBS,KAJwB5C,EAAG6C,KAAK,CAACxB,EAIlB,IAJwB,CAACyB,SAAS,CAAC,CACpDxB,MAAOE,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACL,CAAAA,EAAAA,EAAAA,EAAAA,CAAG,CAAC,MAAM,EAAEE,EAAAA,MAAMA,CAACqB,OAAO,CAAC,CAAC,CAAC,CAAEA,EAAQK,WAAW,GAC9D,GAGE,OAAOlB,EAAAA,EAAYA,CAACC,IAAI,CACtB,CAAEC,MAAO,WAAY,EACrB,CAAEC,OAAQ,GAAI,GAIlB,IAAMgB,EAAM,IAAIrB,KACVsB,MACEtB,KADuB,IAAfO,EACH,2BACAc,EAAIE,CAAb,IAAIvB,EAAgB,GAAKO,GASvBiB,EAAS,MAAMnD,EAAGoD,MAAM,CAAC/B,EAAAA,MAAMA,EAClCgC,MAAM,CAACC,SAPRZ,EACAa,UAAWP,EACXtB,UAAWuB,EACX7C,OAAQA,CACV,GAIGoD,SAAS,CAAC,CAAEC,GAAIpC,EAAAA,MAAMA,CAACoC,EAAE,CAAEf,QAASrB,EAAAA,MAAMA,CAACqB,OAAQ,GAGtD,GAAI,CACF,GAAM,wBAAEgB,CAAsB,CAAE,CAAG,MAAM,uCAAgC,CACnEC,EAAa,MAAMD,EAAuBE,wBAAwB,CAACT,CAAM,CAAC,EAAE,CAACT,OAAO,CAAEtC,EAG5F,OAAMsD,EAAuBG,qBAAqB,CAACzD,EAASuD,EAAWG,GAAG,CAC5E,CAAE,MAAO/B,EAAO,CACdgC,QAAQhC,KAAK,CAAC,qDAAsDA,EAEtE,CAEA,OAAOF,EAAAA,EAAYA,CAACC,IAAI,CAAC,CACvB2B,GAAIN,CAAM,CAAC,EAAE,CAACM,EAAE,CAChBO,MAAOb,CAAM,CAAC,EAAE,CAACT,OACnB,EACF,CAAE,MAAOX,EAAO,CAEd,OADAgC,QAAQhC,KAAK,CAAC,4BAA6BA,GACpCF,EAAAA,EAAYA,CAACC,IAAI,CACtB,CAAEC,MAAO,QAAS,EAClB,CAAEC,OAAQ,GAAI,EAElB,CACF,CC7GA,UAAwB,qBAAmB,EAC3C,YACA,KAAc,GAAS,WACvB,kCACA,gCACA,iBACA,0CACA,CAAK,CACL,8FACA,iBAVA,GAWA,QAAY,EACZ,CAAC,EAID,kBAAQ,wCAAsD,EAC9D,aACA,MAAW,QAAW,EACtB,mBACA,sBACA,CAAK,CACL,CCpBA,2EACA,EAFA,4BAEA,4BACA,MACI,QAA8B,EAClC,kCACA,0BACA,wBACA,gBAAyB,OAAqB,EAC9C,uBACA,CAAS,CACT,CAAK,EAEE,MAAqB,EAC5B,EAAe,GAAsB,MAAM,EAAkB,CAC7D,QAD6D,GAf7D,CAAoB,MAAQ,OAcM,CAdN,CAAW,sBAA2B,aAAe,kDAAyD,uOAAsQ,2RAAuT,kBAAkB,QAAQ,uDAA2D,0DAA8D,iBAAsB,gBAAkB,sEAAgF,kBAAoB,uCAA6C,MAAQ,iBAAmB,2BAA+B,gHAAkI,uBAAyB,8FAA0G,aAAiB,WAAa,sEAA6E,uBAAuB,iCAAmC,QAAQ,EAAE,SAAW,oBAAsB,QAAQ,GAAG,4EAAgF,WAAa,SAAW,2CAAiD,UAAY,+BAAqC,UAAY,oCAA0C,QAAU,uCAA6C,OAAS,yCAA+C,QAAU,0CAAiD,MAAQ,+CAAsD,iBAAmB,ywBAA+5B,4CAA8C,kNAAqQ,qBAAyB,s+CAA0/C,oIAiB7wJ,CAAC,CAAC,EAAC,+ECvBI,IAAMnB,EAAe,CAC1BC,kBAAmB,GACnBmD,cAAe,GACjB,ECHaC,EAAiB,CAC5BC,YAAa,EACbC,QAAS,IACTC,YAAa,IACbC,OAAQ,CACNC,YAAa,aACf,CACF,EAAU,oJEYH,OAAMC,EACX,OAAeC,WAAwB,CACrC,IAAMC,EAASC,QAAQzE,GAAG,CAAC0E,UAAU,EAAID,QAAQzE,GAAG,CAAC2E,WAAW,CAChE,GAAI,CAACH,EACH,MADW,MACK,8DAElB,OAAO,IAAII,cAAcC,MAAM,CAACL,EAClC,CAGA,aAAaM,oBAAoBC,CAA4C,CAAmB,CAC9F,IAAMjC,EAAMkC,KAAKC,KAAK,CAACxD,KAAKqB,GAAG,GAAK,KAEpC,OAAO,MAAM,IAAIoC,EAAAA,CAAOA,CAAC,CACvB,GAAGH,CAAO,CACVI,IAAKrC,CAEP,GACGsC,kBAAkB,CAAC,CAAEC,IAAK,OAAQ,GAClCC,WAAW,CAACxC,GAEZyC,CADD,GACK,CAAC,IAAI,CAAChB,CADD,QACU,GACxB,CAGA,aAAaiB,SAAST,CAAyC,CAAmB,CAChF,IAAMjC,EAAMkC,KAAKC,KAAK,CAACxD,KAAKqB,GAAG,GAAK,KAC9B2C,EAAM3C,EAAO,KAAK,EAExB,GAF6B,IAEtB,CAF2B,GAAI,EAEzB,IAAIoC,EAAAA,CAAOA,CAAC,CACvB,GAAGH,CAAO,KACVU,EACAN,IAAKrC,CACP,GACGsC,kBAAkB,CAAC,CAAEC,IAAK,OAAQ,GAClCC,WAAW,CAACxC,GACZ4C,iBAAiB,CAACD,GAClBF,IAAI,CAAC,IAAI,CAAChB,SAAS,GACxB,CAGA,aAAaoB,sBAAsBC,CAAa,CAA0C,CACxF,GAAI,CACF,GAAM,SAAEb,CAAO,CAAE,CAAG,MAAMc,CAAAA,EAAAA,EAAAA,CAAAA,CAASA,CAACD,EAAO,IAAI,CAACrB,SAAS,IAIzD,OAAOQ,CACT,CAAE,MAAOlD,EAAO,CAEd,OADAgC,QAAQhC,KAAK,CAAC,2BAA4BA,GACnC,IACT,CACF,CAGA,aAAaiE,WAAWF,CAAa,CAA+B,CAClE,GAAI,CACF,GAAM,CAAEb,SAAO,CAAE,CAAG,MAAMc,CAAAA,EAAAA,EAAAA,CAAAA,CAASA,CAACD,EAAO,IAAI,CAACrB,SAAS,IAGnDzB,EAAMkC,KAAKC,KAAK,CAACxD,KAAKqB,GAAG,GAAK,KACpC,GAAIiC,EAAQU,GAAG,EAAIV,EAAQU,GAAG,CAAG3C,EAC/B,GADoC,IAC7B,KAGT,OAAOiC,CACT,CAAE,MAAOlD,EAAO,CAEd,OADAgC,QAAQhC,KAAK,CAAC,2BAA4BA,GACnC,IACT,CACF,CAGA,OAAOkE,0BAAmC,CACxC,OAAOf,KAAKC,KAAK,CAAC,IAAyB,IAAhBD,KAAKgB,MAAM,IAAanF,QAAQ,EAC7D,CACF,CClEO,MAAM2C,EAEX,aAAaE,yBAAyBuC,CAAoB,CAAE/F,CAAc,CAAE6B,CAAa,CAAgC,CACvH,IAAMjC,EAAKC,CAAAA,EAAAA,EAAAA,CAAAA,CAAQA,GAGbmG,EAAqB,MAAMpG,EAAG6C,KAAK,CAACwD,gBAAgB,CAACvD,SAAS,CAAC,CACnExB,MAAOE,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC6E,EAAAA,gBAAgBA,CAACF,YAAY,CAAEA,EAC3C,GAEA,GAAIC,EACF,OAAOA,EAIT,IAAME,EAAc,GALI,GAKEtG,EAAG6C,KAAK,CAACxB,MAAM,CAACyB,SAAS,CAAC,CAClDxB,MAAOC,CAAAA,EAAAA,EAAAA,EAAAA,CAAGA,CACRC,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACH,EAAAA,MAAMA,CAACqB,OAAO,CAAEyD,GACnB3E,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACH,EAAAA,MAAMA,CAACjB,MAAM,CAAEA,GAEtB,GAEA,GAAI,CAACkG,EACH,MAAUC,KADM,CACA,mBAIlB,IAAMC,EAAeC,OAAOC,UAAU,GAChC5C,EAAM,MAAMU,EAASQ,MAADR,aAAoB,CAAC,CAC7C2B,eACAQ,QAASL,EAAY7C,EAAE,cACvB+C,CACF,GAGM,CAAC7C,EAAW,CAAG,MAAM3D,EAAGoD,MAAM,CAACiD,EAAAA,gBAAgBA,EAAEhD,MAAM,CAAC,CAC5DI,GAAI+C,SACJpG,eACA+F,EACAlE,KAAMA,GAAQ,GAAGkE,EAAa,IAAI,CAAC,KACnCrC,EACA8C,QAAS,GACTrD,UAAW,IAAI5B,IACjB,GAAG6B,SAAS,GAEZ,OAAOG,CACT,CAGA,aAAakD,mBAAoD,CAC/D,IAAM7G,EAAKC,CAAAA,EAAAA,EAAAA,CAAAA,CAAQA,GAEb6G,EAAc,MAAM9G,EAAG6C,KAAK,CAACwD,gBAAgB,CAACU,QAAQ,CAAC,CAC3DC,QAASC,CAAAA,EAAAA,EAAAA,CAAAA,CAAIA,CAACZ,EAAAA,gBAAgBA,CAAC9C,SAAS,CAC1C,GAaA,OAV6B,MAAM2D,QAAQC,GAAG,CAC5CL,EAAYM,GAAG,CAAC,MAAOzD,IACrB,IAAM0D,EAAe,MAAM,IAAI,CAACC,eAAe,CAAC3D,EAAWwC,YAAY,EACvE,MAAO,CACL,GAAGxC,CAAU,CACb0D,cACF,CACF,GAIJ,CAGA,aAAaE,qBAAqBpB,CAAoB,CAAuC,CAC3F,IAAMnG,EAAKC,CAAAA,EAAAA,EAAAA,CAAAA,CAAQA,GAEb0D,EAAa,MAAM3D,EAAG6C,KAAK,CAACwD,gBAAgB,CAACvD,SAAS,CAAC,CAC3DxB,MAAOE,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC6E,EAAAA,gBAAgBA,CAACF,YAAY,CAAEA,EAC3C,GAEA,GAAI,CAACxC,EACH,OAAO,GADQ,EAIjB,IAAM0D,EAAe,MAAM,IAAI,CAACC,eAAe,CAACnB,GAEhD,MAAO,CACL,GAAGxC,CAAU,cACb0D,CACF,CACF,CAGA,aAAaC,gBAAgBnB,CAAoB,CAAmB,CAClE,IAAMnG,EAAKC,CAAAA,EAAAA,EAAAA,CAAAA,CAAQA,GAEbkD,EAAS,MAAMnD,EAClBiB,MAAM,CAAC,CAAEC,MAAOA,CAAAA,EAAAA,EAAAA,EAAAA,CAAKA,EAAG,GACxBE,IAAI,CAACoG,EAAAA,iBAAiBA,EACtBC,QAAQ,CAACC,EAAAA,KAAKA,CAAElG,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACgG,EAAAA,iBAAiBA,CAACpH,MAAM,CAAEsH,EAAAA,KAAKA,CAACjE,EAAE,GACrDnC,KAAK,CACJC,CAAAA,EAAAA,EAAAA,EAAAA,CAAGA,CACDC,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACgG,EAAAA,iBAAiBA,CAACrB,YAAY,CAAEA,GACnC3E,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACgG,EAAAA,iBAAiBA,CAACZ,OAAO,EAAE,GAC9Be,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACD,EAAAA,KAAKA,CAACE,IAAI,CAAE,SAAS,EAI9B,MAJsC,CAI/BzE,CAAM,CAAC,EAAE,EAAEjC,OAAS,CAC7B,CAGA,aAAa2G,iBAAiB/B,CAAa,CAAuC,CAChF,IAAMb,EAAU,MAAMT,EAASqB,MAADrB,eAAsB,CAACsB,GACrD,GAAI,CAACb,EACH,OADY,KAId,IAAMjF,EAAKC,CAAAA,EAAAA,EAAAA,CAAAA,CAAQA,GAGb0D,EAAa,MAAM3D,EAAG6C,KAAK,CAACwD,gBAAgB,CAACvD,SAAS,CAAC,CAC3DxB,MAAOC,CAAAA,EAAAA,EAAAA,EAAAA,CAAGA,CACRC,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC6E,EAAAA,gBAAgBA,CAAC5C,EAAE,CAAEwB,EAAQuB,YAAY,EAC5ChF,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC6E,EAAAA,gBAAgBA,CAACO,OAAO,EAAE,GAEjC,UAEA,GAKA,CALI,KAKE5G,EAAG8H,CALQ,KAKF,CAACzB,EAAAA,gBAAgBA,EAC7B0B,GAAG,CAAC,CAAEC,WAAY,IAAIrG,IAAO,GAC7BL,KAAK,CAACE,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC6E,EAAAA,gBAAgBA,CAAC5C,EAAE,CAAEE,EAAWF,EAAE,GAEvCE,GARE,IASX,CAGA,aAAaE,sBAAsBzD,CAAc,CAAE0D,CAAW,CAA6B,CACzF,IAAM9D,EAAKC,CAAAA,EAAAA,EAAAA,CAAAA,CAAQA,GAGb0D,EAAa,MAAM,IAAI,CAACkE,gBAAgB,CAAC/D,GAC/C,GAAI,CAACH,EACH,MAAM,IADS,EACC,WAIlB,IAAM2C,EAAc,MAAMtG,EAAG6C,KAAK,CAACxB,MAAM,CAACyB,SAAS,CAAC,CAClDxB,MAAOE,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACH,EAAAA,MAAMA,CAACqB,OAAO,CAAEiB,EAAWwC,YAAY,CACnD,GAEA,GAAI,CAACG,EACH,MAAM,KADU,CACA,WAWlB,GAPwB,CAOpB2B,KAP0BjI,EAAG6C,KAAK,CAAC2E,IAOlB,aAPmC,CAAC1E,SAAS,CAAC,CACjExB,MAAOC,CAAAA,EAAAA,EAAAA,EAAAA,CAAGA,CACRC,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACgG,EAAAA,iBAAiBA,CAACpH,MAAM,CAAEA,GAC7BoB,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACgG,EAAAA,iBAAiBA,CAACb,OAAO,CAAEL,EAAY7C,EAAE,EAEhD,GAGE,MAAM,MAAU,WAIlB,GAAM,CAACyE,EAAQ,CAAG,MAAMlI,EAAGoD,MAAM,CAACoE,EAAAA,iBAAiBA,EAAEnE,MAAM,CAAC,CAC1DI,GAAIgD,OAAOC,UAAU,UACrBtG,EACAuG,QAASL,EAAY7C,EAAE,CACvB+C,aAAc7C,EAAWF,EAAE,CAC3BF,UAAW,IAAI5B,IACjB,GAAG6B,SAAS,GAEZ,MAAO,CACL,GAAG0E,CAAO,CACV/B,aAAcxC,EAAWwC,YAAY,CACrCS,SAAS,CACX,CACF,CAGA,aAAauB,gBAAgB/H,CAAc,CAA+B,CACxE,IAAMJ,EAAKC,CAAAA,EAAAA,EAAAA,CAAAA,CAAQA,GAUnB,MAAOmI,CARU,MAAMpI,EAAG6C,KAAK,CAAC2E,iBAAiB,CAACT,QAAQ,CAAC,CACzDzF,MAAOE,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACgG,EAAAA,iBAAiBA,CAACpH,MAAM,CAAEA,GACpC4G,QAASC,CAAAA,EAAAA,EAAAA,CAAAA,CAAIA,CAACO,EAAAA,iBAAiBA,CAACjE,SAAS,EACzC8E,KAAM,CACJrE,OAAO,CACT,CACF,GAFgB,CAIAoD,GAAG,CAACc,GAAY,EAC9B,CALyB,EAKtBA,CAAO,CADoB,aAEhBA,EAAQlE,KAAK,EAAEtB,QAC7BkE,SAAS,EACX,EACF,CAFkB,OAAO,MAKZ0B,YAAYlI,CAAc,CAAE+F,CAAoB,CAAoB,CAC/E,IAAMnG,EAAKC,CAAAA,EAAAA,EAAAA,CAAAA,CAAQA,GAGbqG,EAAc,MAAMtG,EAAG6C,KAAK,CAACxB,MAAM,CAACyB,SAAS,CAAC,CAClDxB,MAAOE,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACH,EAAAA,MAAMA,CAACqB,OAAO,CAAEyD,EAC5B,SAEA,EAAKG,EAAD,CAUGnD,CANQ,MAAMnD,EAAGuI,CAJN,KAIY,CAACf,EAAAA,iBAAiBA,EAC7ClG,KAAK,CAACC,CAAAA,EAAAA,EAAAA,EAAAA,CAAGA,CACRC,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACgG,EAAAA,iBAAiBA,CAACpH,MAAM,CAAEA,GAC7BoB,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACgG,EAAAA,iBAAiBA,CAACb,OAAO,CAAEL,EAAY7C,EAAE,KAGlC+E,OAAO,CAAG,CAC1B,CAGA,aAAaC,kBAAkBjC,CAAoB,CAAuC,CACxF,IAAMxG,EAAKC,CAAAA,EAAAA,EAAAA,CAAAA,CAAQA,GAMnB,OAJmB,MAAMD,EAAG6C,KAAK,CAACwD,gBAAgB,CAACvD,SAAS,CAAC,CAC3DxB,MAAOE,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC6E,EAAAA,gBAAgBA,CAAC5C,EAAE,CAAE+C,EACjC,EAGF,CACF,iDErPO,eACP,SACA,+CACA,UACA,GDvBA,kECuB2B,UAE3B,QACA", "sources": ["webpack://_N_E/external commonjs \"node:async_hooks\"", "webpack://_N_E/external commonjs \"node:buffer\"", "webpack://_N_E/./app/types/email.ts", "webpack://_N_E/./app/api/emails/generate/route.ts", "webpack://_N_E/./app/api/emails/generate/route.ts?58f6", "webpack://_N_E/?3710", "webpack://_N_E/./app/config/email.ts", "webpack://_N_E/./app/config/webhook.ts", "webpack://_N_E/./app/config/index.ts", "webpack://_N_E/./app/lib/jwt.ts", "webpack://_N_E/./app/lib/emailCredentials.ts", "webpack://_N_E/./node_modules/nanoid/url-alphabet/index.js", "webpack://_N_E/./node_modules/nanoid/index.browser.js"], "sourcesContent": ["module.exports = require(\"node:async_hooks\");", "module.exports = require(\"node:buffer\");", "export interface ExpiryOption {\r\n  label: string\r\n  value: number\r\n}\r\n\r\nexport const EXPIRY_OPTIONS: ExpiryOption[] = [\r\n  { label: '1小时', value: 1000 * 60 * 60 },\r\n  { label: '24小时', value: 1000 * 60 * 60 * 24 },\r\n  { label: '3天', value: 1000 * 60 * 60 * 24 * 3 },\r\n  { label: '永久', value: 0 }\r\n]\r\n", "import { NextResponse } from \"next/server\"\r\nimport { nanoid } from \"nanoid\"\r\nimport { createDb } from \"@/lib/db\"\r\nimport { emails } from \"@/lib/schema\"\r\nimport { eq, and, gt, sql } from \"drizzle-orm\"\r\nimport { EXPIRY_OPTIONS } from \"@/types/email\"\r\nimport { EMAIL_CONFIG } from \"@/config\"\r\nimport { getRequestContext } from \"@cloudflare/next-on-pages\"\r\nimport { getUserId } from \"@/lib/apiKey\"\r\nimport { getUserRole } from \"@/lib/auth\"\r\nimport { ROLES } from \"@/lib/permissions\"\r\n\r\nexport const runtime = \"edge\"\r\n\r\nexport async function POST(request: Request) {\r\n  const db = createDb()\r\n  const env = getRequestContext().env\r\n\r\n  const userId = await getUserId()\r\n  const userRole = await getUserRole(userId!)\r\n\r\n  try {\r\n    if (userRole !== ROLES.EMPEROR) {\r\n      const maxEmails = await env.SITE_CONFIG.get(\"MAX_EMAILS\") || EMAIL_CONFIG.MAX_ACTIVE_EMAILS.toString()\r\n      const activeEmailsCount = await db\r\n        .select({ count: sql<number>`count(*)` })\r\n        .from(emails)\r\n        .where(\r\n          and(\r\n            eq(emails.userId, userId!),\r\n            gt(emails.expiresAt, new Date())\r\n          )\r\n        )\r\n      \r\n      if (Number(activeEmailsCount[0].count) >= Number(maxEmails)) {\r\n        return NextResponse.json(\r\n          { error: `已达到最大邮箱数量限制 (${maxEmails})` },\r\n          { status: 403 }\r\n        )\r\n      }\r\n    }\r\n\r\n    const { name, expiryTime, domain } = await request.json<{ \r\n      name: string\r\n      expiryTime: number\r\n      domain: string\r\n    }>()\r\n\r\n    if (!EXPIRY_OPTIONS.some(option => option.value === expiryTime)) {\r\n      return NextResponse.json(\r\n        { error: \"无效的过期时间\" },\r\n        { status: 400 }\r\n      )\r\n    }\r\n\r\n    const domainString = await env.SITE_CONFIG.get(\"EMAIL_DOMAINS\")\r\n    const domains = domainString ? domainString.split(',') : [\"moemail.app\"]\r\n\r\n    if (!domains || !domains.includes(domain)) {\r\n      return NextResponse.json(\r\n        { error: \"无效的域名\" },\r\n        { status: 400 }\r\n      )\r\n    }\r\n\r\n    const address = `${name || nanoid(8)}@${domain}`\r\n    const existingEmail = await db.query.emails.findFirst({\r\n      where: eq(sql`LOWER(${emails.address})`, address.toLowerCase())\r\n    })\r\n\r\n    if (existingEmail) {\r\n      return NextResponse.json(\r\n        { error: \"该邮箱地址已被使用\" },\r\n        { status: 409 }\r\n      )\r\n    }\r\n\r\n    const now = new Date()\r\n    const expires = expiryTime === 0 \r\n      ? new Date('9999-01-01T00:00:00.000Z')\r\n      : new Date(now.getTime() + expiryTime)\r\n    \r\n    const emailData: typeof emails.$inferInsert = {\r\n      address,\r\n      createdAt: now,\r\n      expiresAt: expires,\r\n      userId: userId!\r\n    }\r\n    \r\n    const result = await db.insert(emails)\r\n      .values(emailData)\r\n      .returning({ id: emails.id, address: emails.address })\r\n\r\n    // 自动为新创建的邮箱创建凭证并绑定到用户\r\n    try {\r\n      const { EmailCredentialManager } = await import('@/lib/emailCredentials')\r\n      const credential = await EmailCredentialManager.createCredentialForEmail(result[0].address, userId!)\r\n\r\n      // 自动绑定邮箱到当前用户\r\n      await EmailCredentialManager.bindEmailByCredential(userId!, credential.jwt)\r\n    } catch (error) {\r\n      console.error('Failed to create email credential or bind to user:', error)\r\n      // 不影响邮箱创建，只记录错误\r\n    }\r\n\r\n    return NextResponse.json({\r\n      id: result[0].id,\r\n      email: result[0].address\r\n    })\r\n  } catch (error) {\r\n    console.error('Failed to generate email:', error)\r\n    return NextResponse.json(\r\n      { error: \"创建邮箱失败\" },\r\n      { status: 500 }\r\n    )\r\n  }\r\n} ", "import { AppRouteRouteModule } from \"next/dist/server/route-modules/app-route/module.compiled\";\nimport { RouteKind } from \"next/dist/server/route-kind\";\nimport { patchFetch as _patchFetch } from \"next/dist/server/lib/patch-fetch\";\nimport * as userland from \"F:\\\\CODE\\\\Project\\\\tempmail\\\\moemail\\\\app\\\\api\\\\emails\\\\generate\\\\route.ts\";\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new AppRouteRouteModule({\n    definition: {\n        kind: RouteKind.APP_ROUTE,\n        page: \"/api/emails/generate/route\",\n        pathname: \"/api/emails/generate\",\n        filename: \"route\",\n        bundlePath: \"app/api/emails/generate/route\"\n    },\n    resolvedPagePath: \"F:\\\\CODE\\\\Project\\\\tempmail\\\\moemail\\\\app\\\\api\\\\emails\\\\generate\\\\route.ts\",\n    nextConfigOutput,\n    userland\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return _patchFetch({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\nexport { routeModule, workAsyncStorage, workUnitAsyncStorage, serverHooks, patchFetch,  };\n\n//# sourceMappingURL=app-route.js.map", "var _self___RSC_MANIFEST;\nimport { createServerModuleMap } from \"next/dist/server/app-render/action-utils\";\nimport { setReferenceManifestsSingleton } from \"next/dist/server/app-render/encryption-utils\";\nimport { EdgeRouteModuleWrapper } from \"next/dist/server/web/edge-route-module-wrapper\";\n// Import the userland code.\nimport * as module from \"next-app-loader?name=app%2Fapi%2Femails%2Fgenerate%2Froute&page=%2Fapi%2Femails%2Fgenerate%2Froute&pagePath=private-next-app-dir%2Fapi%2Femails%2Fgenerate%2Froute.ts&appDir=F%3A%5CCODE%5CProject%5Ctempmail%5Cmoemail%5Capp&appPaths=%2Fapi%2Femails%2Fgenerate%2Froute&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!private-next-app-dir/api/emails/generate/route.ts?__next_edge_ssr_entry__\";\nconst nextConfig = {\"env\":{},\"eslint\":{\"ignoreDuringBuilds\":false},\"typescript\":{\"ignoreBuildErrors\":false,\"tsconfigPath\":\"tsconfig.json\"},\"distDir\":\".next\",\"cleanDistDir\":true,\"assetPrefix\":\"\",\"cacheMaxMemorySize\":52428800,\"configOrigin\":\"next.config.ts\",\"useFileSystemPublicRoutes\":true,\"generateEtags\":true,\"pageExtensions\":[\"tsx\",\"ts\",\"jsx\",\"js\"],\"poweredByHeader\":true,\"compress\":true,\"images\":{\"deviceSizes\":[640,750,828,1080,1200,1920,2048,3840],\"imageSizes\":[16,32,48,64,96,128,256,384],\"path\":\"/_next/image\",\"loader\":\"default\",\"loaderFile\":\"\",\"domains\":[],\"disableStaticImages\":false,\"minimumCacheTTL\":60,\"formats\":[\"image/webp\"],\"dangerouslyAllowSVG\":false,\"contentSecurityPolicy\":\"script-src 'none'; frame-src 'none'; sandbox;\",\"contentDispositionType\":\"attachment\",\"remotePatterns\":[{\"protocol\":\"https\",\"hostname\":\"avatars.githubusercontent.com\"}],\"unoptimized\":false},\"devIndicators\":{\"appIsrStatus\":true,\"buildActivity\":true,\"buildActivityPosition\":\"bottom-right\"},\"onDemandEntries\":{\"maxInactiveAge\":60000,\"pagesBufferLength\":5},\"amp\":{\"canonicalBase\":\"\"},\"basePath\":\"\",\"sassOptions\":{},\"trailingSlash\":false,\"i18n\":null,\"productionBrowserSourceMaps\":false,\"excludeDefaultMomentLocales\":true,\"serverRuntimeConfig\":{},\"publicRuntimeConfig\":{},\"reactProductionProfiling\":false,\"reactStrictMode\":null,\"reactMaxHeadersLength\":6000,\"httpAgentOptions\":{\"keepAlive\":true},\"logging\":{},\"expireTime\":31536000,\"staticPageGenerationTimeout\":60,\"modularizeImports\":{\"@mui/icons-material\":{\"transform\":\"@mui/icons-material/{{member}}\"},\"lodash\":{\"transform\":\"lodash/{{member}}\"}},\"outputFileTracingRoot\":\"F:\\\\CODE\\\\Project\\\\tempmail\\\\moemail\",\"experimental\":{\"cacheLife\":{\"default\":{\"stale\":300,\"revalidate\":900,\"expire\":4294967294},\"seconds\":{\"stale\":0,\"revalidate\":1,\"expire\":60},\"minutes\":{\"stale\":300,\"revalidate\":60,\"expire\":3600},\"hours\":{\"stale\":300,\"revalidate\":3600,\"expire\":86400},\"days\":{\"stale\":300,\"revalidate\":86400,\"expire\":604800},\"weeks\":{\"stale\":300,\"revalidate\":604800,\"expire\":2592000},\"max\":{\"stale\":300,\"revalidate\":2592000,\"expire\":4294967294}},\"cacheHandlers\":{},\"cssChunking\":true,\"multiZoneDraftMode\":false,\"appNavFailHandling\":false,\"prerenderEarlyExit\":true,\"serverMinification\":true,\"serverSourceMaps\":false,\"linkNoTouchStart\":false,\"caseSensitiveRoutes\":false,\"clientSegmentCache\":false,\"preloadEntriesOnStart\":true,\"clientRouterFilter\":true,\"clientRouterFilterRedirects\":false,\"fetchCacheKeyPrefix\":\"\",\"middlewarePrefetch\":\"flexible\",\"optimisticClientCache\":true,\"manualClientBasePath\":false,\"cpus\":11,\"memoryBasedWorkersCount\":false,\"imgOptConcurrency\":null,\"imgOptTimeoutInSeconds\":7,\"imgOptMaxInputPixels\":268402689,\"imgOptSequentialRead\":null,\"isrFlushToDisk\":true,\"workerThreads\":false,\"optimizeCss\":false,\"nextScriptWorkers\":false,\"scrollRestoration\":false,\"externalDir\":false,\"disableOptimizedLoading\":false,\"gzipSize\":true,\"craCompat\":false,\"esmExternals\":true,\"fullySpecified\":false,\"swcTraceProfiling\":false,\"forceSwcTransforms\":false,\"largePageDataBytes\":128000,\"turbo\":{\"root\":\"F:\\\\CODE\\\\Project\\\\tempmail\\\\moemail\"},\"typedRoutes\":false,\"typedEnv\":false,\"parallelServerCompiles\":false,\"parallelServerBuildTraces\":false,\"ppr\":false,\"authInterrupts\":false,\"reactOwnerStack\":false,\"webpackMemoryOptimizations\":false,\"optimizeServerReact\":true,\"useEarlyImport\":false,\"staleTimes\":{\"dynamic\":0,\"static\":300},\"serverComponentsHmrCache\":true,\"staticGenerationMaxConcurrency\":8,\"staticGenerationMinPagesPerWorker\":25,\"dynamicIO\":false,\"inlineCss\":false,\"optimizePackageImports\":[\"lucide-react\",\"date-fns\",\"lodash-es\",\"ramda\",\"antd\",\"react-bootstrap\",\"ahooks\",\"@ant-design/icons\",\"@headlessui/react\",\"@headlessui-float/react\",\"@heroicons/react/20/solid\",\"@heroicons/react/24/solid\",\"@heroicons/react/24/outline\",\"@visx/visx\",\"@tremor/react\",\"rxjs\",\"@mui/material\",\"@mui/icons-material\",\"recharts\",\"react-use\",\"effect\",\"@effect/schema\",\"@effect/platform\",\"@effect/platform-node\",\"@effect/platform-browser\",\"@effect/platform-bun\",\"@effect/sql\",\"@effect/sql-mssql\",\"@effect/sql-mysql2\",\"@effect/sql-pg\",\"@effect/sql-squlite-node\",\"@effect/sql-squlite-bun\",\"@effect/sql-squlite-wasm\",\"@effect/sql-squlite-react-native\",\"@effect/rpc\",\"@effect/rpc-http\",\"@effect/typeclass\",\"@effect/experimental\",\"@effect/opentelemetry\",\"@material-ui/core\",\"@material-ui/icons\",\"@tabler/icons-react\",\"mui-core\",\"react-icons/ai\",\"react-icons/bi\",\"react-icons/bs\",\"react-icons/cg\",\"react-icons/ci\",\"react-icons/di\",\"react-icons/fa\",\"react-icons/fa6\",\"react-icons/fc\",\"react-icons/fi\",\"react-icons/gi\",\"react-icons/go\",\"react-icons/gr\",\"react-icons/hi\",\"react-icons/hi2\",\"react-icons/im\",\"react-icons/io\",\"react-icons/io5\",\"react-icons/lia\",\"react-icons/lib\",\"react-icons/lu\",\"react-icons/md\",\"react-icons/pi\",\"react-icons/ri\",\"react-icons/rx\",\"react-icons/si\",\"react-icons/sl\",\"react-icons/tb\",\"react-icons/tfi\",\"react-icons/ti\",\"react-icons/vsc\",\"react-icons/wi\"]},\"bundlePagesRouterDependencies\":false,\"configFile\":\"F:\\\\CODE\\\\Project\\\\tempmail\\\\moemail\\\\next.config.ts\",\"configFileName\":\"next.config.ts\"}\nconst maybeJSONParse = (str)=>str ? JSON.parse(str) : undefined;\nconst rscManifest = (_self___RSC_MANIFEST = self.__RSC_MANIFEST) == null ? void 0 : _self___RSC_MANIFEST[\"/api/emails/generate/route\"];\nconst rscServerManifest = maybeJSONParse(self.__RSC_SERVER_MANIFEST);\nif (rscManifest && rscServerManifest) {\n    setReferenceManifestsSingleton({\n        page: \"/api/emails/generate/route\",\n        clientReferenceManifest: rscManifest,\n        serverActionsManifest: rscServerManifest,\n        serverModuleMap: createServerModuleMap({\n            serverActionsManifest: rscServerManifest\n        })\n    });\n}\nexport const ComponentMod = module;\nexport default EdgeRouteModuleWrapper.wrap(module.routeModule, {\n    nextConfig\n});\n\n//# sourceMappingURL=edge-app-route.js.map", "export const EMAIL_CONFIG = {\r\n  MAX_ACTIVE_EMAILS: 30, // Maximum number of active emails\r\n  POLL_INTERVAL: 10_000, // Polling interval in milliseconds\r\n} as const\r\n\r\nexport type EmailConfig = typeof EMAIL_CONFIG ", "export const WEBHOOK_CONFIG = {\r\n  MAX_RETRIES: 3, // Maximum retry count\r\n  TIMEOUT: 10_000, // Timeout time (milliseconds)\r\n  RETRY_DELAY: 1000, // Retry delay (milliseconds)\r\n  EVENTS: {\r\n    NEW_MESSAGE: 'new_message',\r\n  }\r\n} as const\r\n\r\nexport type WebhookConfig = typeof WEBHOOK_CONFIG ", "export * from './email'\r\nexport * from './webhook'", "import { SignJWT, jwtVerify } from 'jose'\n\n// JWT payload types\nexport interface EmailCredentialPayload {\n  emailAddress: string\n  emailId: string\n  credentialId: string\n  iat: number\n  // exp字段不存在，邮箱凭证JWT永久有效\n}\n\nexport interface UserPayload {\n  userEmail: string\n  userId: string\n  exp: number\n  iat: number\n}\n\n// JWT utilities\nexport class JWTUtils {\n  private static getSecret(): Uint8Array {\n    const secret = process.env.JWT_SECRET || process.env.AUTH_SECRET\n    if (!secret) {\n      throw new Error('JWT_SECRET or AUTH_SECRET environment variable is required')\n    }\n    return new TextEncoder().encode(secret)\n  }\n\n  // 生成邮箱凭证JWT（永久有效，与cloudflare_temp_email保持一致）\n  static async signEmailCredential(payload: Omit<EmailCredentialPayload, 'iat'>): Promise<string> {\n    const now = Math.floor(Date.now() / 1000)\n\n    return await new SignJWT({\n      ...payload,\n      iat: now,\n      // 不设置exp，使JWT永久有效（与原项目保持一致）\n    })\n      .setProtectedHeader({ alg: 'HS256' })\n      .setIssuedAt(now)\n      // 不设置过期时间\n      .sign(this.getSecret())\n  }\n\n  // 生成用户JWT\n  static async signUser(payload: Omit<UserPayload, 'exp' | 'iat'>): Promise<string> {\n    const now = Math.floor(Date.now() / 1000)\n    const exp = now + (90 * 24 * 60 * 60) // 90 days\n    \n    return await new SignJWT({\n      ...payload,\n      exp,\n      iat: now,\n    })\n      .setProtectedHeader({ alg: 'HS256' })\n      .setIssuedAt(now)\n      .setExpirationTime(exp)\n      .sign(this.getSecret())\n  }\n\n  // 验证邮箱凭证JWT\n  static async verifyEmailCredential(token: string): Promise<EmailCredentialPayload | null> {\n    try {\n      const { payload } = await jwtVerify(token, this.getSecret())\n\n      // 邮箱凭证JWT永久有效，不检查过期时间\n      // 这与cloudflare_temp_email项目保持一致\n      return payload as EmailCredentialPayload\n    } catch (error) {\n      console.error('JWT verification failed:', error)\n      return null\n    }\n  }\n\n  // 验证用户JWT\n  static async verifyUser(token: string): Promise<UserPayload | null> {\n    try {\n      const { payload } = await jwtVerify(token, this.getSecret())\n      \n      // 检查是否过期\n      const now = Math.floor(Date.now() / 1000)\n      if (payload.exp && payload.exp < now) {\n        return null\n      }\n\n      return payload as UserPayload\n    } catch (error) {\n      console.error('JWT verification failed:', error)\n      return null\n    }\n  }\n\n  // 生成验证码\n  static generateVerificationCode(): string {\n    return Math.floor(100000 + Math.random() * 900000).toString()\n  }\n}\n\n// 邮箱凭证相关的工具函数\nexport class EmailCredentialUtils {\n  // 生成邮箱凭证名称\n  static generateCredentialName(emailAddress: string): string {\n    const timestamp = new Date().toISOString().slice(0, 10)\n    const shortAddress = emailAddress.split('@')[0].slice(0, 8)\n    return `${shortAddress}-${timestamp}`\n  }\n\n  // 检查邮箱地址格式\n  static isValidEmail(email: string): boolean {\n    const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/\n    return emailRegex.test(email)\n  }\n\n  // 检查是否为临时邮箱域名\n  static async isTemporaryEmailDomain(email: string, allowedDomains?: string[]): Promise<boolean> {\n    const domain = email.split('@')[1]?.toLowerCase()\n    if (!domain) return false\n\n    // 如果有配置允许的域名列表，检查是否在列表中\n    if (allowedDomains && allowedDomains.length > 0) {\n      return allowedDomains.some(allowedDomain => \n        domain === allowedDomain.toLowerCase() || \n        domain.endsWith('.' + allowedDomain.toLowerCase())\n      )\n    }\n\n    return true // 默认允许所有域名\n  }\n}\n", "import { createDb } from './db'\nimport { emailCredentials, userEmailBindings, emails, users } from './schema'\nimport { eq, and, desc, count, ne } from 'drizzle-orm'\nimport { JWTUtils } from './jwt'\nimport { nanoid } from 'nanoid'\n\nexport interface EmailCredentialInfo {\n  id: string\n  userId: string\n  emailAddress: string\n  name: string\n  jwt: string\n  enabled: boolean\n  createdAt: Date\n  expiresAt?: Date | null\n  lastUsedAt: Date | null\n  bindingCount?: number // 绑定的用户数量（不包括管理员）\n}\n\nexport interface UserEmailBinding {\n  id: string\n  userId: string\n  emailId: string\n  emailAddress?: string // 从关联查询中获取\n  credentialId?: string | null\n  createdAt: Date\n  enabled?: boolean // 虚拟字段，默认为 true\n}\n\nexport class EmailCredentialManager {\n  // 为邮箱创建凭证（邮箱创建时自动调用）\n  static async createCredentialForEmail(emailAddress: string, userId: string, name?: string): Promise<EmailCredentialInfo> {\n    const db = createDb()\n\n    // 检查邮箱是否已存在凭证\n    const existingCredential = await db.query.emailCredentials.findFirst({\n      where: eq(emailCredentials.emailAddress, emailAddress)\n    })\n\n    if (existingCredential) {\n      return existingCredential as EmailCredentialInfo\n    }\n\n    // 查找对应的邮箱记录，并验证是否属于当前用户\n    const emailRecord = await db.query.emails.findFirst({\n      where: and(\n        eq(emails.address, emailAddress),\n        eq(emails.userId, userId)\n      )\n    })\n\n    if (!emailRecord) {\n      throw new Error('邮箱地址不存在或不属于当前用户')\n    }\n\n    // 生成JWT\n    const credentialId = crypto.randomUUID()\n    const jwt = await JWTUtils.signEmailCredential({\n      emailAddress,\n      emailId: emailRecord.id,\n      credentialId\n    })\n\n    // 创建凭证记录\n    const [credential] = await db.insert(emailCredentials).values({\n      id: credentialId,\n      userId,\n      emailAddress,\n      name: name || `${emailAddress} 的凭证`,\n      jwt,\n      enabled: true,\n      createdAt: new Date(),\n    }).returning()\n\n    return credential as EmailCredentialInfo\n  }\n\n  // 获取所有邮箱凭证（管理员用）\n  static async getAllCredentials(): Promise<EmailCredentialInfo[]> {\n    const db = createDb()\n\n    const credentials = await db.query.emailCredentials.findMany({\n      orderBy: desc(emailCredentials.createdAt)\n    })\n\n    // 为每个凭证计算绑定用户数量（不包括管理员）\n    const credentialsWithCount = await Promise.all(\n      credentials.map(async (credential) => {\n        const bindingCount = await this.getBindingCount(credential.emailAddress)\n        return {\n          ...credential,\n          bindingCount\n        } as EmailCredentialInfo\n      })\n    )\n\n    return credentialsWithCount\n  }\n\n  // 根据邮箱地址获取凭证\n  static async getCredentialByEmail(emailAddress: string): Promise<EmailCredentialInfo | null> {\n    const db = createDb()\n\n    const credential = await db.query.emailCredentials.findFirst({\n      where: eq(emailCredentials.emailAddress, emailAddress)\n    })\n\n    if (!credential) {\n      return null\n    }\n\n    const bindingCount = await this.getBindingCount(emailAddress)\n\n    return {\n      ...credential,\n      bindingCount\n    } as EmailCredentialInfo\n  }\n\n  // 获取邮箱的绑定用户数量（不包括管理员）\n  static async getBindingCount(emailAddress: string): Promise<number> {\n    const db = createDb()\n\n    const result = await db\n      .select({ count: count() })\n      .from(userEmailBindings)\n      .leftJoin(users, eq(userEmailBindings.userId, users.id))\n      .where(\n        and(\n          eq(userEmailBindings.emailAddress, emailAddress),\n          eq(userEmailBindings.enabled, true),\n          ne(users.role, 'admin') // 排除管理员\n        )\n      )\n\n    return result[0]?.count || 0\n  }\n\n  // 验证JWT凭证\n  static async verifyCredential(token: string): Promise<EmailCredentialInfo | null> {\n    const payload = await JWTUtils.verifyEmailCredential(token)\n    if (!payload) {\n      return null\n    }\n\n    const db = createDb()\n\n    // 获取凭证信息\n    const credential = await db.query.emailCredentials.findFirst({\n      where: and(\n        eq(emailCredentials.id, payload.credentialId),\n        eq(emailCredentials.enabled, true)\n      )\n    })\n\n    if (!credential) {\n      return null\n    }\n\n    // 更新最后使用时间\n    await db.update(emailCredentials)\n      .set({ lastUsedAt: new Date() })\n      .where(eq(emailCredentials.id, credential.id))\n\n    return credential as EmailCredentialInfo\n  }\n\n  // 用户通过邮箱凭证绑定邮箱\n  static async bindEmailByCredential(userId: string, jwt: string): Promise<UserEmailBinding> {\n    const db = createDb()\n\n    // 验证JWT凭证\n    const credential = await this.verifyCredential(jwt)\n    if (!credential) {\n      throw new Error('无效的邮箱凭证')\n    }\n\n    // 查找对应的邮箱记录\n    const emailRecord = await db.query.emails.findFirst({\n      where: eq(emails.address, credential.emailAddress)\n    })\n\n    if (!emailRecord) {\n      throw new Error('邮箱记录不存在')\n    }\n\n    // 检查用户是否已绑定此邮箱\n    const existingBinding = await db.query.userEmailBindings.findFirst({\n      where: and(\n        eq(userEmailBindings.userId, userId),\n        eq(userEmailBindings.emailId, emailRecord.id)\n      )\n    })\n\n    if (existingBinding) {\n      throw new Error('您已绑定此邮箱')\n    }\n\n    // 创建绑定记录\n    const [binding] = await db.insert(userEmailBindings).values({\n      id: crypto.randomUUID(),\n      userId,\n      emailId: emailRecord.id,\n      credentialId: credential.id,\n      createdAt: new Date(),\n    }).returning()\n\n    return {\n      ...binding,\n      emailAddress: credential.emailAddress,\n      enabled: true\n    } as UserEmailBinding\n  }\n\n  // 获取用户绑定的邮箱列表\n  static async getUserBindings(userId: string): Promise<UserEmailBinding[]> {\n    const db = createDb()\n\n    const bindings = await db.query.userEmailBindings.findMany({\n      where: eq(userEmailBindings.userId, userId),\n      orderBy: desc(userEmailBindings.createdAt),\n      with: {\n        email: true // 关联查询邮箱信息\n      }\n    })\n\n    return bindings.map(binding => ({\n      ...binding,\n      emailAddress: binding.email?.address,\n      enabled: true // 默认启用\n    })) as UserEmailBinding[]\n  }\n\n  // 解绑邮箱\n  static async unbindEmail(userId: string, emailAddress: string): Promise<boolean> {\n    const db = createDb()\n\n    // 先查找邮箱记录\n    const emailRecord = await db.query.emails.findFirst({\n      where: eq(emails.address, emailAddress)\n    })\n\n    if (!emailRecord) {\n      return false\n    }\n\n    const result = await db.delete(userEmailBindings)\n      .where(and(\n        eq(userEmailBindings.userId, userId),\n        eq(userEmailBindings.emailId, emailRecord.id)\n      ))\n\n    return result.changes > 0\n  }\n\n  // 根据ID获取凭证\n  static async getCredentialById(credentialId: string): Promise<EmailCredentialInfo | null> {\n    const db = createDb()\n\n    const credential = await db.query.emailCredentials.findFirst({\n      where: eq(emailCredentials.id, credentialId)\n    })\n\n    return credential as EmailCredentialInfo | null\n  }\n}\n\n\n", "export const urlAlphabet =\n  'useandom-26T198340PX75pxJACKVERYMINDBUSHWOLF_GQZbfghjklqvwyzrict'\n", "import { urlAlphabet as scopedUrlAlphabet } from './url-alphabet/index.js'\nexport { urlAlphabet } from './url-alphabet/index.js'\nexport let random = bytes => crypto.getRandomValues(new Uint8Array(bytes))\nexport let customRandom = (alphabet, defaultSize, getRandom) => {\n  let mask = (2 << Math.log2(alphabet.length - 1)) - 1\n  let step = -~((1.6 * mask * defaultSize) / alphabet.length)\n  return (size = defaultSize) => {\n    let id = ''\n    while (true) {\n      let bytes = getRandom(step)\n      let j = step | 0\n      while (j--) {\n        id += alphabet[bytes[j] & mask] || ''\n        if (id.length >= size) return id\n      }\n    }\n  }\n}\nexport let customAlphabet = (alphabet, size = 21) =>\n  customRandom(alphabet, size | 0, random)\nexport let nanoid = (size = 21) => {\n  let id = ''\n  let bytes = crypto.getRandomValues(new Uint8Array((size |= 0)))\n  while (size--) {\n    id += scopedUrlAlphabet[bytes[size] & 63]\n  }\n  return id\n}\n"], "names": ["EXPIRY_OPTIONS", "label", "value", "runtime", "POST", "request", "db", "createDb", "env", "getRequestContext", "userId", "getUserId", "userRole", "getUserRole", "ROLES", "EMPEROR", "maxEmails", "SITE_CONFIG", "get", "EMAIL_CONFIG", "MAX_ACTIVE_EMAILS", "toString", "activeEmailsCount", "select", "count", "sql", "from", "emails", "where", "and", "eq", "gt", "expiresAt", "Date", "Number", "NextResponse", "json", "error", "status", "name", "expiryTime", "domain", "some", "option", "domainString", "domains", "split", "includes", "address", "nanoid", "existingEmail", "query", "<PERSON><PERSON><PERSON><PERSON>", "toLowerCase", "now", "expires", "getTime", "result", "insert", "values", "emailData", "createdAt", "returning", "id", "EmailCredentialManager", "credential", "createCredentialForEmail", "bindEmailByCredential", "jwt", "console", "email", "POLL_INTERVAL", "WEBHOOK_CONFIG", "MAX_RETRIES", "TIMEOUT", "RETRY_DELAY", "EVENTS", "NEW_MESSAGE", "JWTUtils", "getSecret", "secret", "process", "JWT_SECRET", "AUTH_SECRET", "TextEncoder", "encode", "signEmailCredential", "payload", "Math", "floor", "SignJWT", "iat", "setProtectedHeader", "alg", "setIssuedAt", "sign", "signUser", "exp", "setExpirationTime", "verifyEmailCredential", "token", "jwtVerify", "verifyUser", "generateVerificationCode", "random", "emailAddress", "existingCredential", "emailCredentials", "emailRecord", "Error", "credentialId", "crypto", "randomUUID", "emailId", "enabled", "getAllCredentials", "credentials", "find<PERSON>any", "orderBy", "desc", "Promise", "all", "map", "bindingCount", "getBindingCount", "getCredentialByEmail", "userEmailBindings", "leftJoin", "users", "ne", "role", "verifyCredential", "update", "set", "lastUsedAt", "existingBinding", "binding", "getUserBindings", "bindings", "with", "unbindEmail", "delete", "changes", "getCredentialById"], "sourceRoot": "", "ignoreList": [11, 12]}