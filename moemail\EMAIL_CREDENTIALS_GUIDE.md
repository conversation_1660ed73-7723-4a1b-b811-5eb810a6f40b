# 邮箱凭证功能使用指南

本文档详细说明了从cloudflare_temp_email项目移植过来的邮箱凭证功能。

## 📋 功能概述

邮箱凭证功能允许用户为特定的邮箱地址生成JWT token，用于API访问认证。这个功能完全兼容cloudflare_temp_email项目的设计。

### 🔑 核心特性

- **JWT认证**：为每个邮箱地址生成独立的JWT token
- **永久有效**：JWT token永久有效（与原项目保持一致）
- **权限控制**：只能访问邮件相关的API接口
- **邮箱验证**：创建凭证前需要验证邮箱所有权
- **管理功能**：支持启用/禁用、删除、重新生成token

## 🚀 使用流程

### 1. 创建邮箱凭证

1. **登录管理面板**
2. **进入"API密钥"页面**
3. **切换到"邮箱凭证"选项卡**
4. **点击"创建邮箱凭证"**
5. **选择邮箱地址**：从您已创建的邮箱中选择
6. **发送验证码**：点击"发送验证码"按钮
7. **输入验证码**：查收邮件并输入6位验证码
8. **创建凭证**：验证成功后自动生成JWT token

### 2. 使用邮箱凭证

在API请求中添加认证头：

```bash
curl -H "X-Email-Credential: your-jwt-token" \
     https://your-domain.com/api/emails
```

### 3. 管理邮箱凭证

- **查看凭证列表**：显示所有已创建的凭证
- **启用/禁用**：临时禁用凭证而不删除
- **重新生成**：生成新的JWT token（旧token失效）
- **删除凭证**：永久删除凭证

## 🔐 时效性说明

### ⏰ **邮箱凭证JWT：永久有效**

- **设计原理**：与cloudflare_temp_email项目保持完全一致
- **无过期时间**：JWT token中不包含`exp`字段
- **安全控制**：通过数据库中的`enabled`字段控制有效性
- **撤销机制**：可以通过管理面板禁用或删除凭证

### 🕐 **其他时效设置**

- **验证码**：5分钟有效期
- **用户JWT**：90天有效期（用于管理面板登录）

## 🔒 安全机制

### 权限限制

邮箱凭证只能访问以下API：
- `/api/emails` - 邮件管理相关
- `/api/config` - 系统配置查询

### 验证流程

1. **邮箱所有权验证**：必须验证邮箱验证码
2. **用户权限检查**：只能为自己的邮箱创建凭证
3. **邮箱状态检查**：邮箱必须未过期且有效

### 审计功能

- **使用记录**：记录每次使用时间
- **创建日志**：记录凭证创建时间
- **操作追踪**：记录启用/禁用/删除操作

## 📡 API接口

### 获取邮箱凭证列表

```http
GET /api/email-credentials
Authorization: Bearer session-token
```

### 创建邮箱凭证

```http
POST /api/email-credentials
Content-Type: application/json
Authorization: Bearer session-token

{
  "emailAddress": "<EMAIL>",
  "name": "我的凭证",
  "verificationCode": "123456"
}
```

### 发送验证码

```http
POST /api/email-credentials/send-verification
Content-Type: application/json
Authorization: Bearer session-token

{
  "emailAddress": "<EMAIL>"
}
```

### 管理单个凭证

```http
# 获取凭证详情
GET /api/email-credentials/{id}

# 更新凭证（启用/禁用、重新生成JWT）
PATCH /api/email-credentials/{id}
Content-Type: application/json

{
  "enabled": false,
  "regenerateJWT": true
}

# 删除凭证
DELETE /api/email-credentials/{id}
```

## 🔧 技术实现

### JWT结构

```json
{
  "emailAddress": "<EMAIL>",
  "emailId": "email-uuid",
  "userId": "user-uuid", 
  "credentialId": "credential-uuid",
  "iat": 1703123456
}
```

### 数据库表结构

- **email_credentials**：凭证主表
- **user_email_bindings**：用户邮箱绑定关系
- **email_verification_codes**：验证码记录

### 中间件认证

系统会自动识别`X-Email-Credential`请求头并进行认证：

1. 验证JWT签名
2. 检查凭证是否存在且启用
3. 检查权限范围
4. 更新使用时间

## ⚠️ 注意事项

### 安全建议

1. **妥善保管JWT token**：token永久有效，请安全存储
2. **定期检查凭证**：及时删除不需要的凭证
3. **监控使用情况**：关注异常的API调用
4. **权限最小化**：只为必要的邮箱创建凭证

### 使用限制

1. **邮箱所有权**：只能为自己的邮箱创建凭证
2. **API范围**：只能访问邮件相关API
3. **验证要求**：必须通过邮箱验证码验证

### 故障排除

1. **JWT验证失败**：检查token格式和密钥配置
2. **权限不足**：确认访问的API在允许范围内
3. **验证码问题**：检查邮件发送配置
4. **凭证无效**：确认凭证未被禁用或删除

## 🔄 与原项目的兼容性

本实现与cloudflare_temp_email项目完全兼容：

- **JWT结构**：相同的payload结构
- **时效设置**：相同的永久有效策略
- **权限控制**：相同的API访问限制
- **认证流程**：相同的验证机制

这确保了从原项目迁移过来的用户可以无缝使用现有的JWT token。

---

**总结**：邮箱凭证功能提供了一种安全、灵活的API访问方式，特别适合自动化脚本和第三方集成使用。JWT token永久有效的设计简化了token管理，同时通过数据库控制确保了安全性。
