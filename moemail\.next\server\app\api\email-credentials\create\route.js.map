{"version": 3, "file": "app/api/email-credentials/create/route.js", "mappings": "sFAAA,8DCAA,oHGAA,iSFKO,IAAMA,EAAU,OAAM,EAEEC,EAAAA,CAACA,CAACC,MAAM,CAAC,CACtCC,aAAcF,EAAAA,CAACA,CAACG,MAAM,GAAGC,KAAK,CAAC,UACjC,GAEO,eAAeC,EAAKC,CAAgB,EACzC,IAAMC,EAAU,MAAMC,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,GAC1B,GAAI,CAACD,GAASE,MAAMC,GAClB,CADsB,MACfC,EAAAA,EAAYA,CAACC,IAAI,CAAC,CAAEC,MAAO,KAAM,EAAG,CAAEC,OAAQ,GAAI,GAG3D,GAAI,CACF,IAAMC,EAAO,MAAMT,EAAQM,IAAI,GACzBI,EAAgBC,EAAuBC,KAAK,CAACH,GAG7CI,EAAa,MAAMC,EAAAA,sBAAsBA,CAACC,wBAAwB,CACtEL,EAAcd,YAAY,CAC1BK,EAAQE,IAAI,CAACC,EAAE,EAGjB,OAAOC,EAAAA,EAAYA,CAACC,IAAI,CAAC,CACvBU,QAAS,GACTC,IAAKJ,EAAWI,GAAG,CACnBC,aAAcL,EAAWT,EAAE,CAC3Be,QAAS,UACX,EACF,CAAE,MAAOZ,EAAO,CAGd,GAFAa,QAAQb,KAAK,CAAC,qCAAsCA,GAEhDA,aAAiBb,EAAAA,CAACA,CAAC2B,QAAQ,CAC7B,CAD+B,MACxBhB,EAAAA,EAAYA,CAACC,IAAI,CACtB,CAAEC,MAAOA,EAAMe,MAAM,CAAC,EAAE,CAACH,OAAO,EAChC,CAAEX,OAAQ,GAAI,GAIlB,GAAID,aAAiBgB,MACnB,CAD0B,MACnBlB,EAAAA,EAAYA,CAACC,IAAI,CACtB,CAAEC,MAAOA,EAAMY,OAAO,EACtB,CAAEX,OAAQ,GAAI,GAIlB,OAAOH,EAAAA,EAAYA,CAACC,IAAI,CACtB,CAAEC,MAAO,UAAW,EACpB,CAAEC,OAAQ,GAAI,EAElB,CACF,CChDA,UAAwB,qBAAmB,EAC3C,YACA,KAAc,GAAS,WACvB,2CACA,yCACA,iBACA,mDACA,CAAK,CACL,uGACA,iBAVA,GAWA,QAAY,EACZ,CAAC,EAID,kBAAQ,wCAAsD,EAC9D,aACA,MAAW,QAAW,EACtB,mBACA,sBACA,CAAK,CACL,CCpBA,oFACA,EAFA,4BAEA,4BACA,MACI,QAA8B,EAClC,2CACA,0BACA,wBACA,gBAAyB,OAAqB,EAC9C,uBACA,CAAS,CACT,CAAK,EAEE,MAAqB,EAC5B,EAAe,GAAsB,MAAM,EAAkB,CAC7D,QAD6D,GAf7D,CAAoB,MAAQ,OAcM,CAdN,CAAW,sBAA2B,aAAe,kDAAyD,uOAAsQ,2RAAuT,kBAAkB,QAAQ,uDAA2D,0DAA8D,iBAAsB,gBAAkB,sEAAgF,kBAAoB,uCAA6C,MAAQ,iBAAmB,2BAA+B,gHAAkI,uBAAyB,8FAA0G,aAAiB,WAAa,sEAA6E,uBAAuB,iCAAmC,QAAQ,EAAE,SAAW,oBAAsB,QAAQ,GAAG,4EAAgF,WAAa,SAAW,2CAAiD,UAAY,+BAAqC,UAAY,oCAA0C,QAAU,uCAA6C,OAAS,yCAA+C,QAAU,0CAAiD,MAAQ,+CAAsD,iBAAmB,ywBAA+5B,4CAA8C,kNAAqQ,qBAAyB,s+CAA0/C,oIAiB7wJ,CAAC,CAAC,EAAC,6KCJI,OAAMgB,EACX,OAAeC,WAAwB,CACrC,IAAMC,EAASC,QAAQC,GAAG,CAACC,UAAU,EAAIF,QAAQC,GAAG,CAACE,WAAW,CAChE,GAAI,CAACJ,EACH,MADW,MACK,8DAElB,OAAO,IAAIK,cAAcC,MAAM,CAACN,EAClC,CAGA,aAAaO,oBAAoBC,CAA4C,CAAmB,CAC9F,IAAMC,EAAMC,KAAKC,KAAK,CAACC,KAAKH,GAAG,GAAK,KAEpC,OAAO,MAAM,IAAII,EAAAA,CAAOA,CAAC,CACvB,GAAGL,CAAO,CACVM,IAAKL,CAEP,GACGM,kBAAkB,CAAC,CAAEC,IAAK,OAAQ,GAClCC,WAAW,CAACR,GAEZS,CADD,GACK,CAAC,IAAI,CAACnB,CADD,QACU,GACxB,CAGA,aAAaoB,SAASX,CAAyC,CAAmB,CAChF,IAAMC,EAAMC,KAAKC,KAAK,CAACC,KAAKH,GAAG,GAAK,KAC9BW,EAAMX,EAAO,KAAK,EAExB,GAF6B,IAEtB,CAF2B,GAAI,EAEzB,IAAII,EAAAA,CAAOA,CAFwB,CAG9C,GAAGL,CAAO,KACVY,EACAN,IAAKL,CACP,GACGM,kBAAkB,CAAC,CAAEC,IAAK,OAAQ,GAClCC,WAAW,CAACR,GACZY,iBAAiB,CAACD,GAClBF,IAAI,CAAC,IAAI,CAACnB,SAAS,GACxB,CAGA,aAAauB,sBAAsBC,CAAa,CAA0C,CACxF,GAAI,CACF,GAAM,SAAEf,CAAO,CAAE,CAAG,MAAMgB,CAAAA,EAAAA,EAAAA,CAAAA,CAASA,CAACD,EAAO,IAAI,CAACxB,SAAS,IAIzD,OAAOS,CACT,CAAE,MAAO3B,EAAO,CAEd,OADAa,QAAQb,KAAK,CAAC,2BAA4BA,GACnC,IACT,CACF,CAGA,aAAa4C,WAAWF,CAAa,CAA+B,CAClE,GAAI,CACF,GAAM,SAAEf,CAAO,CAAE,CAAG,MAAMgB,CAAAA,EAAAA,EAAAA,CAAAA,CAASA,CAACD,EAAO,IAAI,CAACxB,SAAS,IAGnDU,EAAMC,KAAKC,KAAK,CAACC,KAAKH,GAAG,GAAK,KACpC,GAAID,EAAQY,GAAG,EAAIZ,EAAQY,GAAG,CAAGX,EAC/B,GADoC,IAC7B,KAGT,OAAOD,CACT,CAAE,MAAO3B,EAAO,CAEd,OADAa,QAAQb,KAAK,CAAC,2BAA4BA,GACnC,IACT,CACF,CAGA,OAAO6C,0BAAmC,CACxC,OAAOhB,KAAKC,KAAK,CAAC,IAAyB,IAAhBD,KAAKiB,MAAM,IAAaC,QAAQ,EAC7D,CACF,CClEO,MAAMxC,EAEX,aAAaC,yBAAyBnB,CAAoB,CAAE2D,CAAc,CAAEC,CAAa,CAAgC,CACvH,IAAMC,EAAKC,CAAAA,EAAAA,EAAAA,CAAAA,CAAQA,GAGbC,EAAqB,MAAMF,EAAGG,KAAK,CAACC,gBAAgB,CAACC,SAAS,CAAC,CACnEC,MAAOC,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACH,EAAAA,gBAAgBA,CAACjE,YAAY,CAAEA,EAC3C,GAEA,GAAI+D,EACF,OAAOA,EAIT,IAAMM,EAAc,GALI,GAKER,EAAGG,KAAK,CAACM,MAAM,CAACJ,SAAS,CAAC,CAClDC,MAAOI,CAAAA,EAAAA,EAAAA,EAAAA,CAAGA,CACRH,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACE,EAAAA,MAAMA,CAACE,OAAO,CAAExE,GACnBoE,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACE,EAAAA,MAAMA,CAACX,MAAM,CAAEA,GAEtB,GAEA,GAAI,CAACU,EACH,MAAM,KADU,CACA,mBAIlB,IAAM/C,EAAemD,OAAOC,UAAU,GAChCrD,EAAM,MAAMO,EAASS,MAADT,aAAoB,CAAC,cAC7C5B,EACA2E,QAASN,EAAY7D,EAAE,CACvBc,cACF,GAGM,CAACL,EAAW,CAAG,MAAM4C,EAAGe,MAAM,CAACX,EAAAA,gBAAgBA,EAAEY,MAAM,CAAC,CAC5DrE,GAAIc,SACJqC,eACA3D,EACA4D,KAAMA,GAAQ,GAAG5D,EAAa,IAAI,CAAC,KACnCqB,EACAyD,SAAS,EACTC,UAAW,IAAIrC,IACjB,GAAGsC,SAAS,GAEZ,OAAO/D,CACT,CAGA,aAAagE,mBAAoD,CAC/D,IAAMpB,EAAKC,CAAAA,EAAAA,EAAAA,CAAAA,CAAQA,GAEboB,EAAc,MAAMrB,EAAGG,KAAK,CAACC,gBAAgB,CAACkB,QAAQ,CAAC,CAC3DC,QAASC,CAAAA,EAAAA,EAAAA,CAAAA,CAAIA,CAACpB,EAAAA,gBAAgBA,CAACc,SAAS,CAC1C,GAaA,OAV6B,MAAMO,QAAQC,GAAG,CAC5CL,EAAYM,GAAG,CAAC,MAAOvE,IACrB,IAAMwE,EAAe,MAAM,IAAI,CAACC,eAAe,CAACzE,EAAWjB,YAAY,EACvE,MAAO,CACL,GAAGiB,CAAU,cACbwE,CACF,CACF,GAIJ,CAGA,aAAaE,qBAAqB3F,CAAoB,CAAuC,CAC3F,IAAM6D,EAAKC,CAAAA,EAAAA,EAAAA,CAAAA,CAAQA,GAEb7C,EAAa,MAAM4C,EAAGG,KAAK,CAACC,gBAAgB,CAACC,SAAS,CAAC,CAC3DC,MAAOC,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACH,EAAAA,gBAAgBA,CAACjE,YAAY,CAAEA,EAC3C,GAEA,GAAI,CAACiB,EACH,OAAO,GADQ,EAIjB,IAAMwE,EAAe,MAAM,IAAI,CAACC,eAAe,CAAC1F,GAEhD,MAAO,CACL,GAAGiB,CAAU,cACbwE,CACF,CACF,CAGA,aAAaC,gBAAgB1F,CAAoB,CAAmB,CAClE,IAAM6D,EAAKC,CAAAA,EAAAA,EAAAA,CAAAA,CAAQA,GAEb8B,EAAS,MAAM/B,EAClBgC,MAAM,CAAC,CAAEC,MAAOA,CAAAA,EAAAA,EAAAA,EAAAA,CAAKA,EAAG,GACxBC,IAAI,CAACC,EAAAA,iBAAiBA,EACtBC,QAAQ,CAACC,EAAAA,KAAKA,CAAE9B,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC4B,EAAAA,iBAAiBA,CAACrC,MAAM,CAAEuC,EAAAA,KAAKA,CAAC1F,EAAE,GACrD2D,KAAK,CACJI,CAAAA,EAAAA,EAAAA,EAAAA,CAAGA,CACDH,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC4B,EAAAA,iBAAiBA,CAAChG,YAAY,CAAEA,GACnCoE,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC4B,EAAAA,iBAAiBA,CAAClB,OAAO,EAAE,GAC9BqB,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACD,EAAAA,KAAKA,CAACE,IAAI,CAAE,SAAS,EAI9B,MAJsC,CAI/BR,CAAM,CAAC,EAAE,EAAEE,OAAS,CAC7B,CAGA,aAAaO,iBAAiBhD,CAAa,CAAuC,CAChF,IAAMf,EAAU,MAAMV,EAASwB,MAADxB,eAAsB,CAACyB,GACrD,GAAI,CAACf,EACH,OADY,KAId,IAAMuB,EAAKC,CAAAA,EAAAA,EAAAA,CAAAA,CAAQA,GAGb7C,EAAa,MAAM4C,EAAGG,KAAK,CAACC,gBAAgB,CAACC,SAAS,CAAC,CAC3DC,MAAOI,CAAAA,EAAAA,EAAAA,EAAAA,CAAGA,CACRH,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACH,EAAAA,gBAAgBA,CAACzD,EAAE,CAAE8B,EAAQhB,YAAY,EAC5C8C,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACH,EAAAA,gBAAgBA,CAACa,OAAO,EAAE,GAEjC,UAEA,GAKA,CALI,KAKEjB,EAAGyC,CALQ,KAKF,CAACrC,EAAAA,gBAAgBA,EAC7BsC,GAAG,CAAC,CAAEC,WAAY,IAAI9D,IAAO,GAC7ByB,KAAK,CAACC,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACH,EAAAA,gBAAgBA,CAACzD,EAAE,CAAES,EAAWT,EAAE,GAEvCS,GARE,IASX,CAGA,aAAawF,sBAAsB9C,CAAc,CAAEtC,CAAW,CAA6B,CACzF,IAAMwC,EAAKC,CAAAA,EAAAA,EAAAA,CAAAA,CAAQA,GAGb7C,EAAa,MAAM,IAAI,CAACoF,gBAAgB,CAAChF,GAC/C,GAAI,CAACJ,EACH,MAAUU,IADK,EACC,WAIlB,IAAM0C,EAAc,MAAMR,EAAGG,KAAK,CAACM,MAAM,CAACJ,SAAS,CAAC,CAClDC,MAAOC,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACE,EAAAA,MAAMA,CAACE,OAAO,CAAEvD,EAAWjB,YAAY,CACnD,GAEA,GAAI,CAACqE,EACH,MAAM,KADU,CACA,WAWlB,GAPwB,CAOpBqC,KAP0B7C,EAAGG,KAAK,CAACgC,IAOlB,aAPmC,CAAC9B,SAAS,CAAC,CACjEC,MAAOI,CAAAA,EAAAA,EAAAA,EAAAA,CAAGA,CACRH,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC4B,EAAAA,iBAAiBA,CAACrC,MAAM,CAAEA,GAC7BS,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC4B,EAAAA,iBAAiBA,CAACrB,OAAO,CAAEN,EAAY7D,EAAE,EAEhD,GAGE,MAAM,MAAU,WAIlB,GAAM,CAACmG,EAAQ,CAAG,MAAM9C,EAAGe,MAAM,CAACoB,EAAAA,iBAAiBA,EAAEnB,MAAM,CAAC,CAC1DrE,GAAIiE,OAAOC,UAAU,UACrBf,EACAgB,QAASN,EAAY7D,EAAE,CACvBc,aAAcL,EAAWT,EAAE,CAC3BuE,UAAW,IAAIrC,IACjB,GAAGsC,SAAS,GAEZ,MAAO,CACL,GAAG2B,CAAO,CACV3G,aAAciB,EAAWjB,YAAY,CACrC8E,SAAS,CACX,CACF,CAGA,aAAa8B,gBAAgBjD,CAAc,CAA+B,CACxE,IAAME,EAAKC,CAAAA,EAAAA,EAAAA,CAAAA,CAAQA,GAUnB,MAAO+C,CARU,MAAMhD,EAAGG,KAAK,CAACgC,iBAAiB,CAACb,QAAQ,CAAC,CACzDhB,MAAOC,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC4B,EAAAA,iBAAiBA,CAACrC,MAAM,CAAEA,GACpCyB,QAASC,CAAAA,EAAAA,EAAAA,CAAAA,CAAIA,CAACW,EAAAA,iBAAiBA,CAACjB,SAAS,EACzC+B,KAAM,CACJ5G,OAAO,CACT,CACF,GAFgB,CAIAsF,GAAG,CAACmB,GAAY,EAC9B,CALyB,EAKtBA,CAAO,CADoB,aAEhBA,EAAQzG,KAAK,EAAEsE,QAC7BM,SAAS,EACX,EACF,CAGA,OALyB,MAKZiC,YAAYpD,CAAc,CAAE3D,CAAoB,CAAoB,CAC/E,IAAM6D,EAAKC,CAAAA,EAAAA,EAAAA,CAAAA,CAAQA,GAGbO,EAAc,MAAMR,EAAGG,KAAK,CAACM,MAAM,CAACJ,SAAS,CAAC,CAClDC,MAAOC,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACE,EAAAA,MAAMA,CAACE,OAAO,CAAExE,EAC5B,SAEA,EAAKqE,EAAD,CAUGuB,CANQ,MAAM/B,EAAGmD,CAJN,KAIY,CAAChB,EAAAA,iBAAiBA,EAC7C7B,KAAK,CAACI,CAAAA,EAAAA,EAAAA,EAAAA,CAAGA,CACRH,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC4B,EAAAA,iBAAiBA,CAACrC,MAAM,CAAEA,GAC7BS,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC4B,EAAAA,iBAAiBA,CAACrB,OAAO,CAAEN,EAAY7D,EAAE,KAGlCyG,OAAO,CAAG,CAC1B,CAGA,aAAaC,kBAAkB5F,CAAoB,CAAuC,CACxF,IAAMuC,EAAKC,CAAAA,EAAAA,EAAAA,CAAAA,CAAQA,GAMnB,OAJmB,MAAMD,EAAGG,KAAK,CAACC,gBAAgB,CAACC,SAAS,CAAC,CAC3DC,MAAOC,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACH,EAAAA,gBAAgBA,CAACzD,EAAE,CAAEc,EACjC,EAGF,CACF", "sources": ["webpack://_N_E/external commonjs \"node:async_hooks\"", "webpack://_N_E/external commonjs \"node:buffer\"", "webpack://_N_E/./app/api/email-credentials/create/route.ts", "webpack://_N_E/./app/api/email-credentials/create/route.ts?901e", "webpack://_N_E/?b9b7", "webpack://_N_E/./app/lib/jwt.ts", "webpack://_N_E/./app/lib/emailCredentials.ts"], "sourcesContent": ["module.exports = require(\"node:async_hooks\");", "module.exports = require(\"node:buffer\");", "import { auth } from \"@/lib/auth\"\nimport { NextResponse } from \"next/server\"\nimport { EmailCredentialManager } from \"@/lib/emailCredentials\"\nimport { z } from \"zod\"\n\nexport const runtime = \"edge\"\n\nconst createCredentialSchema = z.object({\n  emailAddress: z.string().email(\"无效的邮箱地址\"),\n})\n\nexport async function POST(request: Request) {\n  const session = await auth()\n  if (!session?.user?.id) {\n    return NextResponse.json({ error: \"未授权\" }, { status: 401 })\n  }\n\n  try {\n    const body = await request.json()\n    const validatedData = createCredentialSchema.parse(body)\n\n    // 验证邮箱是否属于当前用户\n    const credential = await EmailCredentialManager.createCredentialForEmail(\n      validatedData.emailAddress,\n      session.user.id\n    )\n\n    return NextResponse.json({\n      success: true,\n      jwt: credential.jwt,\n      credentialId: credential.id,\n      message: \"邮箱凭证创建成功\"\n    })\n  } catch (error) {\n    console.error(\"Failed to create email credential:\", error)\n\n    if (error instanceof z.ZodError) {\n      return NextResponse.json(\n        { error: error.errors[0].message },\n        { status: 400 }\n      )\n    }\n\n    if (error instanceof Error) {\n      return NextResponse.json(\n        { error: error.message },\n        { status: 400 }\n      )\n    }\n\n    return NextResponse.json(\n      { error: \"创建邮箱凭证失败\" },\n      { status: 500 }\n    )\n  }\n}\n", "import { AppRouteRouteModule } from \"next/dist/server/route-modules/app-route/module.compiled\";\nimport { RouteKind } from \"next/dist/server/route-kind\";\nimport { patchFetch as _patchFetch } from \"next/dist/server/lib/patch-fetch\";\nimport * as userland from \"F:\\\\CODE\\\\Project\\\\tempmail\\\\moemail\\\\app\\\\api\\\\email-credentials\\\\create\\\\route.ts\";\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new AppRouteRouteModule({\n    definition: {\n        kind: RouteKind.APP_ROUTE,\n        page: \"/api/email-credentials/create/route\",\n        pathname: \"/api/email-credentials/create\",\n        filename: \"route\",\n        bundlePath: \"app/api/email-credentials/create/route\"\n    },\n    resolvedPagePath: \"F:\\\\CODE\\\\Project\\\\tempmail\\\\moemail\\\\app\\\\api\\\\email-credentials\\\\create\\\\route.ts\",\n    nextConfigOutput,\n    userland\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return _patchFetch({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\nexport { routeModule, workAsyncStorage, workUnitAsyncStorage, serverHooks, patchFetch,  };\n\n//# sourceMappingURL=app-route.js.map", "var _self___RSC_MANIFEST;\nimport { createServerModuleMap } from \"next/dist/server/app-render/action-utils\";\nimport { setReferenceManifestsSingleton } from \"next/dist/server/app-render/encryption-utils\";\nimport { EdgeRouteModuleWrapper } from \"next/dist/server/web/edge-route-module-wrapper\";\n// Import the userland code.\nimport * as module from \"next-app-loader?name=app%2Fapi%2Femail-credentials%2Fcreate%2Froute&page=%2Fapi%2Femail-credentials%2Fcreate%2Froute&pagePath=private-next-app-dir%2Fapi%2Femail-credentials%2Fcreate%2Froute.ts&appDir=F%3A%5CCODE%5CProject%5Ctempmail%5Cmoemail%5Capp&appPaths=%2Fapi%2Femail-credentials%2Fcreate%2Froute&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!private-next-app-dir/api/email-credentials/create/route.ts?__next_edge_ssr_entry__\";\nconst nextConfig = {\"env\":{},\"eslint\":{\"ignoreDuringBuilds\":false},\"typescript\":{\"ignoreBuildErrors\":false,\"tsconfigPath\":\"tsconfig.json\"},\"distDir\":\".next\",\"cleanDistDir\":true,\"assetPrefix\":\"\",\"cacheMaxMemorySize\":52428800,\"configOrigin\":\"next.config.ts\",\"useFileSystemPublicRoutes\":true,\"generateEtags\":true,\"pageExtensions\":[\"tsx\",\"ts\",\"jsx\",\"js\"],\"poweredByHeader\":true,\"compress\":true,\"images\":{\"deviceSizes\":[640,750,828,1080,1200,1920,2048,3840],\"imageSizes\":[16,32,48,64,96,128,256,384],\"path\":\"/_next/image\",\"loader\":\"default\",\"loaderFile\":\"\",\"domains\":[],\"disableStaticImages\":false,\"minimumCacheTTL\":60,\"formats\":[\"image/webp\"],\"dangerouslyAllowSVG\":false,\"contentSecurityPolicy\":\"script-src 'none'; frame-src 'none'; sandbox;\",\"contentDispositionType\":\"attachment\",\"remotePatterns\":[{\"protocol\":\"https\",\"hostname\":\"avatars.githubusercontent.com\"}],\"unoptimized\":false},\"devIndicators\":{\"appIsrStatus\":true,\"buildActivity\":true,\"buildActivityPosition\":\"bottom-right\"},\"onDemandEntries\":{\"maxInactiveAge\":60000,\"pagesBufferLength\":5},\"amp\":{\"canonicalBase\":\"\"},\"basePath\":\"\",\"sassOptions\":{},\"trailingSlash\":false,\"i18n\":null,\"productionBrowserSourceMaps\":false,\"excludeDefaultMomentLocales\":true,\"serverRuntimeConfig\":{},\"publicRuntimeConfig\":{},\"reactProductionProfiling\":false,\"reactStrictMode\":null,\"reactMaxHeadersLength\":6000,\"httpAgentOptions\":{\"keepAlive\":true},\"logging\":{},\"expireTime\":31536000,\"staticPageGenerationTimeout\":60,\"modularizeImports\":{\"@mui/icons-material\":{\"transform\":\"@mui/icons-material/{{member}}\"},\"lodash\":{\"transform\":\"lodash/{{member}}\"}},\"outputFileTracingRoot\":\"F:\\\\CODE\\\\Project\\\\tempmail\\\\moemail\",\"experimental\":{\"cacheLife\":{\"default\":{\"stale\":300,\"revalidate\":900,\"expire\":4294967294},\"seconds\":{\"stale\":0,\"revalidate\":1,\"expire\":60},\"minutes\":{\"stale\":300,\"revalidate\":60,\"expire\":3600},\"hours\":{\"stale\":300,\"revalidate\":3600,\"expire\":86400},\"days\":{\"stale\":300,\"revalidate\":86400,\"expire\":604800},\"weeks\":{\"stale\":300,\"revalidate\":604800,\"expire\":2592000},\"max\":{\"stale\":300,\"revalidate\":2592000,\"expire\":4294967294}},\"cacheHandlers\":{},\"cssChunking\":true,\"multiZoneDraftMode\":false,\"appNavFailHandling\":false,\"prerenderEarlyExit\":true,\"serverMinification\":true,\"serverSourceMaps\":false,\"linkNoTouchStart\":false,\"caseSensitiveRoutes\":false,\"clientSegmentCache\":false,\"preloadEntriesOnStart\":true,\"clientRouterFilter\":true,\"clientRouterFilterRedirects\":false,\"fetchCacheKeyPrefix\":\"\",\"middlewarePrefetch\":\"flexible\",\"optimisticClientCache\":true,\"manualClientBasePath\":false,\"cpus\":11,\"memoryBasedWorkersCount\":false,\"imgOptConcurrency\":null,\"imgOptTimeoutInSeconds\":7,\"imgOptMaxInputPixels\":268402689,\"imgOptSequentialRead\":null,\"isrFlushToDisk\":true,\"workerThreads\":false,\"optimizeCss\":false,\"nextScriptWorkers\":false,\"scrollRestoration\":false,\"externalDir\":false,\"disableOptimizedLoading\":false,\"gzipSize\":true,\"craCompat\":false,\"esmExternals\":true,\"fullySpecified\":false,\"swcTraceProfiling\":false,\"forceSwcTransforms\":false,\"largePageDataBytes\":128000,\"turbo\":{\"root\":\"F:\\\\CODE\\\\Project\\\\tempmail\\\\moemail\"},\"typedRoutes\":false,\"typedEnv\":false,\"parallelServerCompiles\":false,\"parallelServerBuildTraces\":false,\"ppr\":false,\"authInterrupts\":false,\"reactOwnerStack\":false,\"webpackMemoryOptimizations\":false,\"optimizeServerReact\":true,\"useEarlyImport\":false,\"staleTimes\":{\"dynamic\":0,\"static\":300},\"serverComponentsHmrCache\":true,\"staticGenerationMaxConcurrency\":8,\"staticGenerationMinPagesPerWorker\":25,\"dynamicIO\":false,\"inlineCss\":false,\"optimizePackageImports\":[\"lucide-react\",\"date-fns\",\"lodash-es\",\"ramda\",\"antd\",\"react-bootstrap\",\"ahooks\",\"@ant-design/icons\",\"@headlessui/react\",\"@headlessui-float/react\",\"@heroicons/react/20/solid\",\"@heroicons/react/24/solid\",\"@heroicons/react/24/outline\",\"@visx/visx\",\"@tremor/react\",\"rxjs\",\"@mui/material\",\"@mui/icons-material\",\"recharts\",\"react-use\",\"effect\",\"@effect/schema\",\"@effect/platform\",\"@effect/platform-node\",\"@effect/platform-browser\",\"@effect/platform-bun\",\"@effect/sql\",\"@effect/sql-mssql\",\"@effect/sql-mysql2\",\"@effect/sql-pg\",\"@effect/sql-squlite-node\",\"@effect/sql-squlite-bun\",\"@effect/sql-squlite-wasm\",\"@effect/sql-squlite-react-native\",\"@effect/rpc\",\"@effect/rpc-http\",\"@effect/typeclass\",\"@effect/experimental\",\"@effect/opentelemetry\",\"@material-ui/core\",\"@material-ui/icons\",\"@tabler/icons-react\",\"mui-core\",\"react-icons/ai\",\"react-icons/bi\",\"react-icons/bs\",\"react-icons/cg\",\"react-icons/ci\",\"react-icons/di\",\"react-icons/fa\",\"react-icons/fa6\",\"react-icons/fc\",\"react-icons/fi\",\"react-icons/gi\",\"react-icons/go\",\"react-icons/gr\",\"react-icons/hi\",\"react-icons/hi2\",\"react-icons/im\",\"react-icons/io\",\"react-icons/io5\",\"react-icons/lia\",\"react-icons/lib\",\"react-icons/lu\",\"react-icons/md\",\"react-icons/pi\",\"react-icons/ri\",\"react-icons/rx\",\"react-icons/si\",\"react-icons/sl\",\"react-icons/tb\",\"react-icons/tfi\",\"react-icons/ti\",\"react-icons/vsc\",\"react-icons/wi\"]},\"bundlePagesRouterDependencies\":false,\"configFile\":\"F:\\\\CODE\\\\Project\\\\tempmail\\\\moemail\\\\next.config.ts\",\"configFileName\":\"next.config.ts\"}\nconst maybeJSONParse = (str)=>str ? JSON.parse(str) : undefined;\nconst rscManifest = (_self___RSC_MANIFEST = self.__RSC_MANIFEST) == null ? void 0 : _self___RSC_MANIFEST[\"/api/email-credentials/create/route\"];\nconst rscServerManifest = maybeJSONParse(self.__RSC_SERVER_MANIFEST);\nif (rscManifest && rscServerManifest) {\n    setReferenceManifestsSingleton({\n        page: \"/api/email-credentials/create/route\",\n        clientReferenceManifest: rscManifest,\n        serverActionsManifest: rscServerManifest,\n        serverModuleMap: createServerModuleMap({\n            serverActionsManifest: rscServerManifest\n        })\n    });\n}\nexport const ComponentMod = module;\nexport default EdgeRouteModuleWrapper.wrap(module.routeModule, {\n    nextConfig\n});\n\n//# sourceMappingURL=edge-app-route.js.map", "import { SignJWT, jwtVerify } from 'jose'\n\n// JWT payload types\nexport interface EmailCredentialPayload {\n  emailAddress: string\n  emailId: string\n  credentialId: string\n  iat: number\n  // exp字段不存在，邮箱凭证JWT永久有效\n}\n\nexport interface UserPayload {\n  userEmail: string\n  userId: string\n  exp: number\n  iat: number\n}\n\n// JWT utilities\nexport class JWTUtils {\n  private static getSecret(): Uint8Array {\n    const secret = process.env.JWT_SECRET || process.env.AUTH_SECRET\n    if (!secret) {\n      throw new Error('JWT_SECRET or AUTH_SECRET environment variable is required')\n    }\n    return new TextEncoder().encode(secret)\n  }\n\n  // 生成邮箱凭证JWT（永久有效，与cloudflare_temp_email保持一致）\n  static async signEmailCredential(payload: Omit<EmailCredentialPayload, 'iat'>): Promise<string> {\n    const now = Math.floor(Date.now() / 1000)\n\n    return await new SignJWT({\n      ...payload,\n      iat: now,\n      // 不设置exp，使JWT永久有效（与原项目保持一致）\n    })\n      .setProtectedHeader({ alg: 'HS256' })\n      .setIssuedAt(now)\n      // 不设置过期时间\n      .sign(this.getSecret())\n  }\n\n  // 生成用户JWT\n  static async signUser(payload: Omit<UserPayload, 'exp' | 'iat'>): Promise<string> {\n    const now = Math.floor(Date.now() / 1000)\n    const exp = now + (90 * 24 * 60 * 60) // 90 days\n    \n    return await new SignJWT({\n      ...payload,\n      exp,\n      iat: now,\n    })\n      .setProtectedHeader({ alg: 'HS256' })\n      .setIssuedAt(now)\n      .setExpirationTime(exp)\n      .sign(this.getSecret())\n  }\n\n  // 验证邮箱凭证JWT\n  static async verifyEmailCredential(token: string): Promise<EmailCredentialPayload | null> {\n    try {\n      const { payload } = await jwtVerify(token, this.getSecret())\n\n      // 邮箱凭证JWT永久有效，不检查过期时间\n      // 这与cloudflare_temp_email项目保持一致\n      return payload as EmailCredentialPayload\n    } catch (error) {\n      console.error('JWT verification failed:', error)\n      return null\n    }\n  }\n\n  // 验证用户JWT\n  static async verifyUser(token: string): Promise<UserPayload | null> {\n    try {\n      const { payload } = await jwtVerify(token, this.getSecret())\n      \n      // 检查是否过期\n      const now = Math.floor(Date.now() / 1000)\n      if (payload.exp && payload.exp < now) {\n        return null\n      }\n\n      return payload as UserPayload\n    } catch (error) {\n      console.error('JWT verification failed:', error)\n      return null\n    }\n  }\n\n  // 生成验证码\n  static generateVerificationCode(): string {\n    return Math.floor(100000 + Math.random() * 900000).toString()\n  }\n}\n\n// 邮箱凭证相关的工具函数\nexport class EmailCredentialUtils {\n  // 生成邮箱凭证名称\n  static generateCredentialName(emailAddress: string): string {\n    const timestamp = new Date().toISOString().slice(0, 10)\n    const shortAddress = emailAddress.split('@')[0].slice(0, 8)\n    return `${shortAddress}-${timestamp}`\n  }\n\n  // 检查邮箱地址格式\n  static isValidEmail(email: string): boolean {\n    const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/\n    return emailRegex.test(email)\n  }\n\n  // 检查是否为临时邮箱域名\n  static async isTemporaryEmailDomain(email: string, allowedDomains?: string[]): Promise<boolean> {\n    const domain = email.split('@')[1]?.toLowerCase()\n    if (!domain) return false\n\n    // 如果有配置允许的域名列表，检查是否在列表中\n    if (allowedDomains && allowedDomains.length > 0) {\n      return allowedDomains.some(allowedDomain => \n        domain === allowedDomain.toLowerCase() || \n        domain.endsWith('.' + allowedDomain.toLowerCase())\n      )\n    }\n\n    return true // 默认允许所有域名\n  }\n}\n", "import { createDb } from './db'\nimport { emailCredentials, userEmailBindings, emails, users } from './schema'\nimport { eq, and, desc, count, ne } from 'drizzle-orm'\nimport { JWTUtils } from './jwt'\nimport { nanoid } from 'nanoid'\n\nexport interface EmailCredentialInfo {\n  id: string\n  userId: string\n  emailAddress: string\n  name: string\n  jwt: string\n  enabled: boolean\n  createdAt: Date\n  expiresAt?: Date | null\n  lastUsedAt: Date | null\n  bindingCount?: number // 绑定的用户数量（不包括管理员）\n}\n\nexport interface UserEmailBinding {\n  id: string\n  userId: string\n  emailId: string\n  emailAddress?: string // 从关联查询中获取\n  credentialId?: string | null\n  createdAt: Date\n  enabled?: boolean // 虚拟字段，默认为 true\n}\n\nexport class EmailCredentialManager {\n  // 为邮箱创建凭证（邮箱创建时自动调用）\n  static async createCredentialForEmail(emailAddress: string, userId: string, name?: string): Promise<EmailCredentialInfo> {\n    const db = createDb()\n\n    // 检查邮箱是否已存在凭证\n    const existingCredential = await db.query.emailCredentials.findFirst({\n      where: eq(emailCredentials.emailAddress, emailAddress)\n    })\n\n    if (existingCredential) {\n      return existingCredential as EmailCredentialInfo\n    }\n\n    // 查找对应的邮箱记录，并验证是否属于当前用户\n    const emailRecord = await db.query.emails.findFirst({\n      where: and(\n        eq(emails.address, emailAddress),\n        eq(emails.userId, userId)\n      )\n    })\n\n    if (!emailRecord) {\n      throw new Error('邮箱地址不存在或不属于当前用户')\n    }\n\n    // 生成JWT\n    const credentialId = crypto.randomUUID()\n    const jwt = await JWTUtils.signEmailCredential({\n      emailAddress,\n      emailId: emailRecord.id,\n      credentialId\n    })\n\n    // 创建凭证记录\n    const [credential] = await db.insert(emailCredentials).values({\n      id: credentialId,\n      userId,\n      emailAddress,\n      name: name || `${emailAddress} 的凭证`,\n      jwt,\n      enabled: true,\n      createdAt: new Date(),\n    }).returning()\n\n    return credential as EmailCredentialInfo\n  }\n\n  // 获取所有邮箱凭证（管理员用）\n  static async getAllCredentials(): Promise<EmailCredentialInfo[]> {\n    const db = createDb()\n\n    const credentials = await db.query.emailCredentials.findMany({\n      orderBy: desc(emailCredentials.createdAt)\n    })\n\n    // 为每个凭证计算绑定用户数量（不包括管理员）\n    const credentialsWithCount = await Promise.all(\n      credentials.map(async (credential) => {\n        const bindingCount = await this.getBindingCount(credential.emailAddress)\n        return {\n          ...credential,\n          bindingCount\n        } as EmailCredentialInfo\n      })\n    )\n\n    return credentialsWithCount\n  }\n\n  // 根据邮箱地址获取凭证\n  static async getCredentialByEmail(emailAddress: string): Promise<EmailCredentialInfo | null> {\n    const db = createDb()\n\n    const credential = await db.query.emailCredentials.findFirst({\n      where: eq(emailCredentials.emailAddress, emailAddress)\n    })\n\n    if (!credential) {\n      return null\n    }\n\n    const bindingCount = await this.getBindingCount(emailAddress)\n\n    return {\n      ...credential,\n      bindingCount\n    } as EmailCredentialInfo\n  }\n\n  // 获取邮箱的绑定用户数量（不包括管理员）\n  static async getBindingCount(emailAddress: string): Promise<number> {\n    const db = createDb()\n\n    const result = await db\n      .select({ count: count() })\n      .from(userEmailBindings)\n      .leftJoin(users, eq(userEmailBindings.userId, users.id))\n      .where(\n        and(\n          eq(userEmailBindings.emailAddress, emailAddress),\n          eq(userEmailBindings.enabled, true),\n          ne(users.role, 'admin') // 排除管理员\n        )\n      )\n\n    return result[0]?.count || 0\n  }\n\n  // 验证JWT凭证\n  static async verifyCredential(token: string): Promise<EmailCredentialInfo | null> {\n    const payload = await JWTUtils.verifyEmailCredential(token)\n    if (!payload) {\n      return null\n    }\n\n    const db = createDb()\n\n    // 获取凭证信息\n    const credential = await db.query.emailCredentials.findFirst({\n      where: and(\n        eq(emailCredentials.id, payload.credentialId),\n        eq(emailCredentials.enabled, true)\n      )\n    })\n\n    if (!credential) {\n      return null\n    }\n\n    // 更新最后使用时间\n    await db.update(emailCredentials)\n      .set({ lastUsedAt: new Date() })\n      .where(eq(emailCredentials.id, credential.id))\n\n    return credential as EmailCredentialInfo\n  }\n\n  // 用户通过邮箱凭证绑定邮箱\n  static async bindEmailByCredential(userId: string, jwt: string): Promise<UserEmailBinding> {\n    const db = createDb()\n\n    // 验证JWT凭证\n    const credential = await this.verifyCredential(jwt)\n    if (!credential) {\n      throw new Error('无效的邮箱凭证')\n    }\n\n    // 查找对应的邮箱记录\n    const emailRecord = await db.query.emails.findFirst({\n      where: eq(emails.address, credential.emailAddress)\n    })\n\n    if (!emailRecord) {\n      throw new Error('邮箱记录不存在')\n    }\n\n    // 检查用户是否已绑定此邮箱\n    const existingBinding = await db.query.userEmailBindings.findFirst({\n      where: and(\n        eq(userEmailBindings.userId, userId),\n        eq(userEmailBindings.emailId, emailRecord.id)\n      )\n    })\n\n    if (existingBinding) {\n      throw new Error('您已绑定此邮箱')\n    }\n\n    // 创建绑定记录\n    const [binding] = await db.insert(userEmailBindings).values({\n      id: crypto.randomUUID(),\n      userId,\n      emailId: emailRecord.id,\n      credentialId: credential.id,\n      createdAt: new Date(),\n    }).returning()\n\n    return {\n      ...binding,\n      emailAddress: credential.emailAddress,\n      enabled: true\n    } as UserEmailBinding\n  }\n\n  // 获取用户绑定的邮箱列表\n  static async getUserBindings(userId: string): Promise<UserEmailBinding[]> {\n    const db = createDb()\n\n    const bindings = await db.query.userEmailBindings.findMany({\n      where: eq(userEmailBindings.userId, userId),\n      orderBy: desc(userEmailBindings.createdAt),\n      with: {\n        email: true // 关联查询邮箱信息\n      }\n    })\n\n    return bindings.map(binding => ({\n      ...binding,\n      emailAddress: binding.email?.address,\n      enabled: true // 默认启用\n    })) as UserEmailBinding[]\n  }\n\n  // 解绑邮箱\n  static async unbindEmail(userId: string, emailAddress: string): Promise<boolean> {\n    const db = createDb()\n\n    // 先查找邮箱记录\n    const emailRecord = await db.query.emails.findFirst({\n      where: eq(emails.address, emailAddress)\n    })\n\n    if (!emailRecord) {\n      return false\n    }\n\n    const result = await db.delete(userEmailBindings)\n      .where(and(\n        eq(userEmailBindings.userId, userId),\n        eq(userEmailBindings.emailId, emailRecord.id)\n      ))\n\n    return result.changes > 0\n  }\n\n  // 根据ID获取凭证\n  static async getCredentialById(credentialId: string): Promise<EmailCredentialInfo | null> {\n    const db = createDb()\n\n    const credential = await db.query.emailCredentials.findFirst({\n      where: eq(emailCredentials.id, credentialId)\n    })\n\n    return credential as EmailCredentialInfo | null\n  }\n}\n\n\n"], "names": ["runtime", "z", "object", "emailAddress", "string", "email", "POST", "request", "session", "auth", "user", "id", "NextResponse", "json", "error", "status", "body", "validatedData", "createCredentialSchema", "parse", "credential", "EmailCredentialManager", "createCredentialForEmail", "success", "jwt", "credentialId", "message", "console", "ZodError", "errors", "Error", "JWTUtils", "getSecret", "secret", "process", "env", "JWT_SECRET", "AUTH_SECRET", "TextEncoder", "encode", "signEmailCredential", "payload", "now", "Math", "floor", "Date", "SignJWT", "iat", "setProtectedHeader", "alg", "setIssuedAt", "sign", "signUser", "exp", "setExpirationTime", "verifyEmailCredential", "token", "jwtVerify", "verifyUser", "generateVerificationCode", "random", "toString", "userId", "name", "db", "createDb", "existingCredential", "query", "emailCredentials", "<PERSON><PERSON><PERSON><PERSON>", "where", "eq", "emailRecord", "emails", "and", "address", "crypto", "randomUUID", "emailId", "insert", "values", "enabled", "createdAt", "returning", "getAllCredentials", "credentials", "find<PERSON>any", "orderBy", "desc", "Promise", "all", "map", "bindingCount", "getBindingCount", "getCredentialByEmail", "result", "select", "count", "from", "userEmailBindings", "leftJoin", "users", "ne", "role", "verifyCredential", "update", "set", "lastUsedAt", "bindEmailByCredential", "existingBinding", "binding", "getUserBindings", "bindings", "with", "unbindEmail", "delete", "changes", "getCredentialById"], "sourceRoot": "", "ignoreList": []}