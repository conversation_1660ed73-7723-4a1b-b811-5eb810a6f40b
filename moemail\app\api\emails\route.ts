import { createDb } from "@/lib/db"
import { and, eq, gt, lt, or, sql } from "drizzle-orm"
import { NextResponse } from "next/server"
import { emails, userEmailBindings } from "@/lib/schema"
import { encodeCursor, decodeCursor } from "@/lib/cursor"
import { getUserId } from "@/lib/apiKey"

export const runtime = "edge"

const PAGE_SIZE = 20

export async function GET(request: Request) {
  const userId = await getUserId()

  if (!userId) {
    return NextResponse.json(
      { error: "未授权" },
      { status: 401 }
    )
  }

  const { searchParams } = new URL(request.url)
  const cursor = searchParams.get('cursor')

  const db = createDb()

  try {
    // 获取临时邮箱
    const tempEmailConditions = and(
      eq(emails.userId, userId),
      gt(emails.expiresAt, new Date())
    )

    const tempEmailResults = await db.query.emails.findMany({
      where: tempEmailConditions,
      orderBy: (emails, { desc }) => [
        desc(emails.createdAt),
        desc(emails.id)
      ]
    })

    // 获取绑定邮箱
    const boundEmailResults = await db.query.userEmailBindings.findMany({
      where: eq(userEmailBindings.userId, userId),
      orderBy: (bindings, { desc }) => [
        desc(bindings.createdAt)
      ],
      with: {
        email: true // 关联查询邮箱信息
      }
    })

    // 合并结果，转换为统一格式
    const tempEmails = tempEmailResults.map(email => ({
      id: email.id,
      address: email.address,
      createdAt: email.createdAt.getTime(),
      expiresAt: email.expiresAt.getTime(),
      type: 'temporary' as const
    }))

    const boundEmails = boundEmailResults.map(binding => ({
      id: binding.email?.id || binding.id,
      address: binding.email?.address || '',
      createdAt: binding.createdAt.getTime(),
      expiresAt: 9999999999999, // 绑定邮箱永不过期
      type: 'bound' as const,
      credentialId: binding.credentialId
    }))

    // 合并并按创建时间排序
    const allEmails = [...tempEmails, ...boundEmails].sort((a, b) => b.createdAt - a.createdAt)

    // 应用分页
    let emailList = allEmails
    if (cursor) {
      const { timestamp, id } = decodeCursor(cursor)
      const cursorIndex = allEmails.findIndex(email =>
        email.createdAt < timestamp || (email.createdAt === timestamp && email.id < id)
      )
      if (cursorIndex > -1) {
        emailList = allEmails.slice(cursorIndex)
      }
    }

    const hasMore = emailList.length > PAGE_SIZE
    const nextCursor = hasMore
      ? encodeCursor(
          emailList[PAGE_SIZE - 1].createdAt,
          emailList[PAGE_SIZE - 1].id
        )
      : null
    const finalEmailList = hasMore ? emailList.slice(0, PAGE_SIZE) : emailList

    return NextResponse.json({
      emails: finalEmailList,
      nextCursor,
      total: allEmails.length
    })
  } catch (error) {
    console.error('Failed to fetch user emails:', error)
    return NextResponse.json(
      { error: "Failed to fetch emails" },
      { status: 500 }
    )
  }
} 