{"version": 3, "file": "edge-chunks/86.js", "mappings": "4GAaM,MAAQ,cAAgB,CAAC,OAAS,EACtC,CAAC,MAAQ,EAAE,EAAG,CAA6C,+CAAK,SAAU,EAC1E,CAAC,UAAY,EAAE,OAAQ,CAAoB,sBAAK,SAAU,EAC1D,CAAC,OAAQ,CAAE,GAAI,CAAM,OAAI,CAAK,MAAI,IAAM,IAAI,IAAM,KAAK,SAAU,EAClE,oCCJK,MAAO,cAAgB,CAAC,MAAQ,EACpC,CAAC,OAAQ,CAAE,MAAO,KAAM,CAAQ,WAAM,CAAG,KAAK,EAAG,CAAK,MAAI,CAAK,OAAK,SAAU,EAC9E,CAAC,MAAQ,EAAE,EAAG,CAA6C,+CAAK,SAAU,EAC3E,oCCHK,MAAO,cAAgB,CAAC,MAAQ,EACpC,CAAC,MAAQ,EAAE,EAAG,CAAsC,wCAAK,SAAU,EACpE,oCCFK,MAAM,cAAgB,CAAC,KAAO,EAClC,CAAC,QAAU,EAAE,EAAI,MAAM,CAAI,OAAM,CAAG,KAAK,GAAK,UAAU,EACxD,CAAC,MAAQ,EAAE,EAAG,CAAW,aAAK,SAAU,EACxC,CAAC,MAAQ,EAAE,EAAG,CAAY,cAAK,SAAU,EACzC,CAAC,MAAQ,EAAE,EAAG,CAAwB,0BAAK,SAAU,EACrD,CAAC,MAAQ,EAAE,EAAG,CAA0B,4BAAK,SAAU,EACvD,CAAC,MAAQ,EAAE,EAAG,CAAW,aAAK,SAAU,EACxC,CAAC,MAAQ,EAAE,EAAG,CAAY,cAAK,SAAU,EACzC,CAAC,MAAQ,EAAE,EAAG,CAAyB,2BAAK,SAAU,EACtD,CAAC,MAAQ,EAAE,EAAG,CAAyB,2BAAK,SAAU,EACvD,mDE4ED,SAASA,EACPC,CAAoC,EAEpC,YAA0CC,IAAlCD,EAAsBE,OAAO,CAwBvC,SAASC,EAAOC,CAAU,SACxB,KAAiB,IAANA,EACFA,EAEQ,KAHa,KAGH,OAAhBA,EACFC,OAAOC,QAAQ,CAACF,GAAKA,EAAIG,IAEjB,UAAb,OAAOH,GAAkB,WAAWI,IAAI,CAACJ,GACpCK,CADwC,QAC/BL,EAAG,IAEdG,GACT,sCCxHA,IAAMG,EAAuC,KAAO,EAC9CC,EADkDC,KACV,EAE/B,EAFmCC,OAE1BC,CAHUC,CAGCC,CAAsB,CAF7BD,CAD2CH,GACZC,CAiBvDI,EAdF,GAAM,aAAEA,CAAW,yBAAEC,CAAuB,CAAE,CAAGF,EAEjD,SAASG,IACP,GAAIF,GAAeA,EAAYG,gBAAgB,CAAE,CAC/C,IAAMC,EAAeC,EAAAA,QAAQA,CAACC,OAAO,CACnCC,MAAMC,IAAI,CAACR,EAAYG,gBAAgB,EAA0BM,MAAM,CACrEC,UAGJV,EAAYW,UAAU,CAACV,EAAwBG,EAAcL,GAC/D,CACF,CA2CA,OAxCEC,OAAAA,EAAAA,GAAAA,IAAAA,EAAAA,EAAaG,gBAAAA,GAAbH,EAA+BY,GAAG,CAACb,EAAMc,QAAQ,EACjDX,IAGFT,EAA0B,SACxBO,EACA,OADAA,MAAAA,CAAAA,EAAAA,GAAAA,IAAAA,EAAAA,EAAaG,gBAAAA,GAAbH,EAA+BY,GAAG,CAACb,EAAMc,QAAQ,EAC1C,SACLb,CAAAA,OAAAA,GAAAA,EAAAA,KAAAA,EAAAA,EAAaG,gBAAAA,GAAbH,EAA+Bc,MAAM,CAACf,EAAMc,QAAQ,CACtD,CACF,GAOApB,EAA0B,KACpBO,GACFA,GAAYe,OADG,OACW,CAAGb,CAAAA,EAExB,KACDF,GACFA,GAAYe,OADG,OACW,CAAGb,CAAAA,CAEjC,IAGFR,EAAoB,KACdM,GAAeA,EAAYe,cAAc,EAAE,CAC7Cf,EAAYe,cAAc,GAC1Bf,EAAYe,cAAc,CAAG,MAExB,KACDf,GAAeA,EAAYe,cAAc,EAAE,CAC7Cf,EAAYe,cAAc,GAC1Bf,EAAYe,cAAc,CAAG,KAEjC,IAGK,IACT,2BErDA,SAASC,EACPC,CAAoC,CACpCC,CAA2C,QAG3C,iBAAWA,GAAsB,UAA2B,OAApBA,EAC/BD,EAGLC,EAAMC,IAAI,GAAKC,EAAAA,QAAc,CACxBH,CAD0B,CACrBI,MAAM,CAChB,UACc,CAACf,OAAO,CAACY,EAAMnB,KAAK,CAACc,QAAQ,EAAES,MAAM,CACjD,CAEEC,EACAC,IAEA,UACE,OAAOA,GACkB,UAAzB,OAAOA,EAEAD,EAEFA,EAAaF,MAAM,CAACG,GAE7B,EAAE,GAIDP,EAAKI,MAAM,CAACH,EACrB,CAEA,IAAMO,EAAY,CAAC,OAAQ,YAAa,UAAW,WAAW,CAsE9D,SAASC,EACPC,CAAoD,CACpD5B,CAAQ,EAER,GAAM,WAAE6B,CAAS,CAAE,CAAG7B,EACtB,OAAO4B,EACJL,MAAM,CAACN,EAAkB,EAAE,EAC3Ba,OAAO,GACPR,MAAM,CAACS,CAzHL,SAASA,CAA6B,EAAjBF,KAAAA,IAAAA,GAAAA,IAAY,GACtC,IAAMG,EAAO,CAAC,UAACC,OAAAA,CAAKC,QAAQ,SAAY,WAAa,CAMrD,OALKL,GACHG,EAAKG,IAAI,CACP,CAFY,EAEZ,OAACF,OAAAA,CAAKG,KAAK,WAAWC,QAAQ,sBAAyB,aAGpDL,EACT,EAiHwBH,GAAWC,OAAO,IACrCpB,MAAM,CAAC4B,SAxEHA,EACP,IAAMC,EAAO,IAAIC,IACXC,EAAO,IAAID,IACXE,EAAY,IAAIF,IAChBG,EAAsD,CAAC,EAE7D,OAAO,IACL,IAAIC,GAAW,EACXC,GAAS,EAEb,GAAIC,EAAEC,GAAG,EAAqB,UAAjB,OAAOD,EAAEC,GAAG,EAAiBD,EAAEC,GAAG,CAACC,OAAO,CAAC,KAAO,EAAG,CAChEH,GAAS,EACT,IAAME,EAAMD,EAAEC,GAAG,CAACE,KAAK,CAACH,EAAEC,GAAG,CAACC,OAAO,CAAC,KAAO,GACzCT,EAAKW,GAAG,CAACH,GACXH,GAAW,EAEXL,EAAK1B,GAAG,CAACkC,EAEb,CAGA,OAAQD,EAAE1B,IAAI,EACZ,IAAK,QACL,IAAK,OACCqB,EAAKS,GAAG,CAACJ,EAAE1B,IAAI,EACjBwB,CADoB,CACT,GAEXH,EAAK5B,GAAG,CAACiC,EAAE1B,IAAI,EAEjB,KACF,KAAK,OACH,IAAK,IAAI+B,EAAI,EAAGC,EAAM1B,EAAU2B,MAAM,CAAEF,EAAIC,EAAKD,IAAK,CACpD,IAAMG,EAAW5B,CAAS,CAACyB,EAAE,CAC7B,GAAKL,CAAD,CAAG9C,KAAK,CAACuD,cAAc,CAACD,IAE5B,GAAiB,IAFsB,OAEX,CAAxBA,EACEZ,EAAUQ,GAAG,CAACI,GAChBV,EAAW,GAEXF,EAAU7B,CAHiB,EAGd,CAACyC,OAEX,CACL,IAAME,EAAWV,EAAE9C,KAAK,CAACsD,EAAS,CAC5BG,EAAad,CAAc,CAACW,EAAS,EAAI,IAAId,GAC9Cc,EAAa,SAAbA,GAAuB,CAACT,CAAAA,CAAAA,CAAK,CAAMY,EAAWP,GAAG,CAACM,GACrDZ,EAAW,IAEXa,EAHgE,GAGlD,CAACD,GACfb,CAAc,CAACW,EAAS,CAAGG,EAE/B,EACF,CAEJ,CAEA,OAAOb,CACT,CACF,KAgBKd,OAAO,GACP4B,GAAG,CAAC,CAACC,EAA4BR,KAChC,IAAMJ,EAAMY,EAAEZ,GAAG,EAAII,EACrB,GACES,CAAAA,GAAoB,IACZC,GAAG,CAACC,qBAAqB,EACjC,CAACjC,GAGY,QAFb,CAEE8B,EAAEvC,IAAI,EACNuC,EAAE3D,KAAK,CAAC,IAAO,EAEf,CAAC,CADD,kCACqC,2BAA2B,CAAC+D,IAAI,CACnE,GAASJ,EAAE3D,KAAK,CAAC,IAAO,CAACgE,OAF+D,GAErD,CAACC,IAEtC,CACA,IAAMC,EAAW,CAAE,GAAIP,EAAE3D,KAAK,EAAI,CAAC,CAAC,EAOpC,OANAkE,CAAQ,CAAC,YAAY,CAAGA,EAAS,IAAO,CACxCA,CADgC,CACvB,IAAO,EAAR,IAAWjF,EAGnBiF,CAAQ,CAAC,uBAAuB,CAAG,GAE5B7C,EAAAA,YAAkB,CAACsC,EAAGO,EAC/B,CAiBF,OAAO7C,EAAAA,YAAkB,CAACsC,EAAG,KAAEZ,CAAI,EACrC,EACJ,CAoBA,MAdA,MAceoB,GAdNA,CAAgD,EAA3C,aAAErD,CAAQ,CAAiC,CAA3C,EACNsD,EAAWC,CAAAA,EAAAA,EAAAA,UAAAA,CAAUA,CAACC,EAAAA,eAAeA,EACrCrE,EAAcoE,CAAAA,EAAAA,EAAAA,UAAAA,CAAUA,CAACE,EAAAA,kBAAkBA,EACjD,MACE,UAACC,EAAAA,CACCtE,OADKsE,iBACoB7C,EACzB1B,YAAaA,EACb4B,UDhMC,SAAS4C,CAAY,CCgMAA,CDhMA,aAC1BC,GAAW,CAAK,QAChBC,GAAS,CAAK,UACdC,GAAW,CAAK,CACjB,CAJ2B,WAIxB,CAAC,EAJuB,EAK1B,OAAOF,GAAaC,GAAUC,CAChC,EC0L6BR,YAEtBtD,GAGP,2BCnMA,SAAS+D,EAAc,CAKM,EALN,WACrBC,CAAM,KACN9F,CAAG,GAFiB6F,IAGpBE,CAAK,SACLC,CAAO,CACoB,CALN,EA0ErB,OAAUF,EAAOG,IAAI,CAAC,QAAOC,mBAAmBlG,GAAK,MAAK+F,EAAM,OAC9DC,CAAAA,EAAW,IAAC,GAERhB,UAAU,CAAC,wBAEX,GAFsCJ,CAErC,EAMKuB,EAR8D,GACnE,CAAqC,YAOd,EAAG,IAAtBN,YCnDb,IAAMO,EAAYxB,CAAAA,YAAAA,CAAAA,IAAAA,IAAAA,IAAAA,KAAAA,KAAAA,KAAAA,KAAAA,KAAAA,CAAAA,WAAAA,CAAAA,GAAAA,GAAAA,GAAAA,GAAAA,GAAAA,IAAAA,IAAAA,IAAAA,CAAAA,KAAAA,eAAAA,OAAAA,UAAAA,oBAAAA,CAAAA,EAAAA,YAAAA,CAAAA,CAAAA,CAA6B,CAyB/C,SAASyB,EACPC,CAA2B,CAC3BC,CAA6B,CAC7BC,CAAqD,CACrDC,CAA2E,CAC3EC,CAAqC,CACrCC,CAAoB,CACpBC,CAA8B,EAE9B,IAAM5G,EAAMsG,MAAAA,EAAAA,KAAAA,EAAAA,EAAKtG,GAAG,CACfsG,GAAOA,CAAG,CAAC,kBAAkB,GAAKtG,IAGvCsG,CAH4C,CAGxC,kBAAkB,CAAGtG,EAEzB6G,CADU,WAAYP,EAAMA,EAAIQ,MAAM,GAAKC,QAAQC,OAAO,IACxDC,KAAK,CAAC,KAAO,GAAGC,IAAI,CAAC,KACrB,GAAI,EAAKC,aAAa,EAAKb,EAAD,WAAgB,EAAE,GAQxB,SAAS,CAAzBC,GACFG,GAAgB,GAEdF,MAAAA,EAAAA,KAAAA,EAAAA,EAAWY,OAAO,CAAE,CAItB,IAAMC,EAAQ,IAAIC,MAAM,QACxBC,OAAOC,cAAc,CAACH,EAAO,SAAU,CAAEI,UAAU,EAAOC,MAAOpB,CAAI,GACrE,IAAIqB,GAAY,EACZC,EAAU,GACdpB,EAAUY,OAAO,CAAC,CAChB,GAAGC,CAAK,CACRQ,YAAaR,EACbS,cAAexB,EACfyB,OAAQzB,EACR0B,mBAAoB,IAAML,EAC1BM,qBAAsB,IAAML,EAC5BM,QAAS,KAAO,EAChBC,eAAgB,KACdR,GAAY,EACZN,EAAMc,cAAc,EACtB,EACAC,gBAAiB,KACfR,GAAU,EACVP,EAAMe,eAAe,EACvB,CACF,EACF,EACI3B,MAAAA,EAAAA,KAAAA,EAAAA,EAAsBW,OAAO,EAAE,CACjCX,EAAqBW,OAAO,CAACd,GAkDjC,GACF,CAEA,SAAS+B,EACPC,CAAsB,SAEtB,EAAYC,EAAR5G,CAAW4G,CAIN,CAAED,CAJO,cAIO,EAIlB,CAAEE,cAAeF,CAAc,CACxC,CA7IIG,WAAmBC,qBAAqB,CAAG,GA+I/C,IAAMC,EAAeC,CAAAA,EAAAA,EAAAA,QAAfD,EAAeC,CAAUA,CAC7B,GAwBEC,IAzBEF,IAEF,KACE3I,CAAG,CACH8I,QAAM,OACNC,CAAK,QACLC,CAAM,OACNjD,CAAK,UACLkD,CAAQ,WACRC,CAAS,OACTC,CAAK,eACLb,CAAa,aACb/B,CAAW,SACX6C,CAAO,aACPzC,CAAW,MACX0C,CAAI,WACJ7C,CAAS,sBACTC,CAAoB,iBACpBC,CAAe,gBACf4C,CAAc,YACd1C,CAAU,QACV2C,CAAM,SACNC,CAAO,CACP,GAAGC,EACJ,GAGKC,EAASC,CAAAA,EAAAA,EAAAA,WAAAA,CAAWA,CACxB,IACOrD,IAGDkD,CAHM,GAQRlD,EAAItG,GALO,CAKDsG,EAAItG,GAAAA,EAYZsG,EAAIsD,QAAQ,EAAE,EAEdtD,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,GAGN,EACA,CACE5G,EACAuG,EACAC,EACAC,EACAC,EACA8C,EACA7C,EACAC,EACD,EAGGiD,EAAMC,CAAAA,EAAAA,EAAAA,CAAAA,CAAYA,CAACjB,EAAca,GAEvC,MACE,UAACpD,MAAAA,CACE,GAAGmD,CAAI,CACP,GAAGpB,EAAgBC,EAAc,CAIlCc,QAASA,EACTrD,MAAOA,EACPiD,OAAQA,EACRC,SAAUA,EACVc,YAAWV,EAAO,OAAS,IAC3BH,UAAWA,EACXC,MAAOA,EAOPJ,MAAOA,EACPD,OAAQA,EACR9I,IAAKA,EACL6J,IAAKA,EACLN,OAAQ,IAENlD,EADYgB,EAAMS,UAEhBxB,GAF6B,CAG7BC,EACAC,EACAC,EACAC,EACAC,EACAC,EAEJ,EACA4C,QAAS,IAEPF,GAAe,GACK,SAAS,CAAzB/C,GAEFG,EAAgB,IAEd8C,GACFA,EAAQnC,EAEZ,EAHe,CAMrB,GAGF,SAAS2C,EAAa,CAMrB,EANqB,gBACpBC,CAAW,eACXC,CAAa,CAId,CANqB,EAOdC,EAAO,CACXC,GAAI,QACJC,YAAaH,EAAcpB,MAAM,CACjCwB,WAAYJ,EAAcnB,KAAK,CAC/BwB,YAAaL,EAAcK,WAAW,CACtCC,eAAgBN,EAAcM,cAAc,CAC5C,GAAGnC,EAAgB6B,EAAc5B,aAAa,CAAC,SAGjD,GAAmBmC,EAAAA,OAAgB,EAAE,EAEnCA,OAAgB,CACdP,EAAclK,GAAG,CACjB,GAGK,MAIP,UAACmF,EAAAA,EAAIA,QACH,UAACuF,OAAAA,CAOCC,IAAI,UAMJC,KAAMV,EAAcpB,MAAM,MAAG7I,EAAYiK,EAAclK,GAAG,CACzD,GAAGmK,CAAI,EAZN,UACAD,EAAclK,GAAG,CACjBkK,EAAcpB,MAAM,CACpBoB,EAAcnB,KAAK,GAa7B,CAOO,ICzUP,EDyUa8B,CAAQjC,EAAAA,EAAAA,GAARiC,OAAQjC,CCzUNiC,CD0Ub,CAAC7J,CADU6J,CACHhC,CC1UUgC,EAAA,ED2UhB,IAAMC,EAAczF,CAAAA,EAAAA,EAAAA,UAAAA,CAAUA,CAAC0F,EAAAA,aAAaA,EAItCC,EAAgB3F,CAAAA,EAAAA,EAAAA,UAAAA,CAAUA,CAAC4F,EAAAA,kBAAkBA,EAC7CnF,EAASoF,CAAAA,EAAAA,EAAAA,OAAAA,CAAOA,CAAC,KACrB,IAAMvG,EAAIyB,GAAa4E,GAAiBG,EAAAA,CAAkBA,CACpDC,EAAW,IAAIzG,EAAE0G,WAAW,IAAK1G,EAAE2F,UAAU,CAAC,CAACgB,IAAI,CAAC,CAACC,EAAGC,IAAMD,EAAIC,GAClEH,EAAc1G,EAAE0G,WAAW,CAACC,IAAI,CAAC,CAACC,EAAGC,IAAMD,EAAIC,GACrD,MAAO,CAAE,GAAG7G,CAAC,UAAEyG,cAAUC,CAAY,CACvC,EAAG,CAACL,EAAc,EAEZ,QAAEzB,CAAM,mBAAEkC,CAAiB,CAAE,CAAGzK,EAChCwF,EAAYkF,CAAAA,EAAAA,EAAAA,MAAAA,CAAMA,CAACnC,GAEzB1I,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACR2F,EAAUY,OAAO,CAAGmC,CACtB,EAAG,CAACA,EAAO,EAEX,IAAM9C,EAAuBiF,CAAAA,EAAAA,EAAAA,MAAAA,CAAMA,CAACD,GAEpC5K,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACR4F,EAAqBW,OAAO,CAAGqE,CACjC,EAAG,CAACA,EAAkB,EAEtB,GAAM,CAACE,EAAcjF,EAAgB,CAAGkF,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GAC3C,CAACC,EAAavC,EAAe,CAAGsC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,IAEzC,CAAE5K,MAAOkJ,CAAa,CAAEjH,KAAM6I,CAAO,CAAE,CL1J1C,SAASC,CA0BD,CACbC,CAKC,QA/BD,IA0CIlG,CK+GyDiG,CL3CzDE,EACAC,EA/GJ,IADyBH,CAEvB/L,CAAG,OACH+I,CAAK,aACLpC,GAAc,CAAK,UACnBwF,GAAW,CAAK,CAChB/C,SAAO,WACPF,CAAS,SACTlD,CAAO,OACPD,CAAK,QACLiD,CAAM,MACNK,GAAO,CAAK,OACZF,CAAK,aACLiD,CAAW,QACX7C,CAAM,mBACNkC,CAAiB,aACjBlF,EAAc,OAAO,CACrB8F,aAAW,eACX/D,CAAa,UACbW,EAAW,OAAO,QAClBqD,CAAM,WACNC,CAAS,gBACTC,CAAc,cACdC,CAAY,UACZC,CAAQ,CACR,GAAGjD,EACQ,CAzBb,EAyCM,SAAEkD,CAAO,aAAEd,CAAW,cAAEF,CAAY,eAAE9F,CAAa,CAAE,CAAGmG,EAE1DrH,EAAIgI,GAAWxB,EAAAA,CAAkBA,CACrC,GAAI,aAAcxG,EAChBmB,CADmB,CACVnB,MACJ,CACL,IAAMyG,EAAW,IAAIzG,EAAE0G,WAAW,IAAK1G,EAAE2F,UAAU,CAAC,CAACgB,IAAI,CAAC,CAACC,EAAGC,IAAMD,EAAIC,GAClEH,EAAc1G,EAAE0G,WAAW,CAACC,IAAI,CAAC,CAACC,EAAGC,IAAMD,EAAIC,GACrD1F,EAAS,CAAE,GAAGnB,CAAC,UAAEyG,cAAUC,CAAY,CACzC,CAEA,GAAI,SAAOxF,EACT,EADwC,IAClC,MACJ,yIAGJ,IAAI+G,EAAgCnD,EAAKmD,MAAM,EAAI/G,CAGnD,QAAO4D,EAAKmD,MAAM,CAClB,OAAQnD,EAAaX,MAAM,CAI3B,IAAM+D,EAAkB,uBAAwBD,EAEhD,GAAIC,GACF,GAAsB,UAAU,CADb,EACRD,MAAM,CACf,MAAUE,yBACW9M,EAAlB,IAAsB,gCACpB,iEAEP,KACK,CAIL,IAAM+M,EAAoBH,EAC1BA,EAAS,IACP,GAAM,CAAE9G,OAAQkH,CAAC,CAAE,GAAG7C,EAAM,CAAG8C,EAC/B,OAAOF,EAAkB5C,EAC3B,CACF,CAEA,GAAImC,EAAQ,CACK,QAAQ,CAAnBA,IACFjD,GAAO,GAUT,IAAM6D,EARoE,CACxEC,UAAW,CAAEC,SAAU,OAAQpE,OAAQ,MAAO,EAC9CqE,WAAY,CAAEtH,MAAO,OAAQiD,OAAQ,MAAO,CAC9C,CAKiC,CAACsD,EAAO,CACrCY,IACF/D,EAAQ,CAAE,GAAGA,CAAK,CAAE,CADL,EACQ+D,CAAW,CAAC,EAErC,IAAMI,EARoD,CACxDD,WAAY,QACZhE,KAAM,OACR,CAKiC,CAACiD,EAAO,CACrCgB,GAAe,CAACvE,IAClBA,EAAQuE,CADiB,CAG7B,CAEA,IAAIC,EAAY,GACZC,EAAWrN,EAAO4F,GAClB0H,EAAYtN,EAAO6I,GAGvB,GA9OE,CAFoBhJ,CAElBA,CA8OeA,CAhP6B,GAG/B,YACdD,KADMC,IACND,EAAgBC,QACf0N,CARoCzN,IAQlBD,EARUA,GAQVA,CAAAA,CAAmB,CA4OvC,IAAM2N,EAAkB5N,EAAgBC,GAAOA,EAAIE,OAAO,CAAGF,EAE7D,GAAI,CAAC2N,EAAgB3N,GAAG,CACtB,CADwB,KAClB,MACH,8IAA6I4N,KAAKC,SAAS,CAC1JF,IAIN,GAAI,CAACA,EAAgB3E,MAAM,EAAI,CAAC2E,EAAgB5H,KAAK,CACnD,CADqD,KAC/C,MACH,2JAA0J6H,KAAKC,SAAS,CACvKF,IAUN,GALA1B,EAAY0B,EAAgB1B,SAAS,CACrCC,EAAayB,EAAgBzB,UAAU,CACvCG,EAAcA,GAAesB,EAAgBtB,WAAW,CACxDkB,EAAYI,EAAgB3N,GAAG,CAE3B,CAACqJ,GACH,GADS,GACSoE,GAGX,GAAID,GAHM,CAGOC,CAHK,CAGM,CACjC,IAAMK,EAAQN,EAAWG,EAAgB5H,KAAK,CAC9C0H,EAAYM,KAAKC,KAAK,CAACL,EAAgB3E,MAAM,CAAG8E,EAClD,MAAO,GAAI,CAACN,GAAYC,EAAW,CACjC,IAAMK,EAAQL,EAAYE,EAAgB3E,MAAM,CAChDwE,EAAWO,KAAKC,KAAK,CAACL,EAAgB5H,KAAK,CAAG+H,EAChD,OAREN,EAAWG,EAAgB5H,KAAK,CAChC0H,EAAYE,EAAgB3E,MAAM,CASxC,CAGA,IAAIiF,EACF,CAAC9B,IAAyB,QAAZ/C,CAAAA,GAAsB,SAAOA,CAAY,EAAU,CAC/D,CAACpJ,CAJLA,EAAqB,UAAf,OAAOA,EAAmBA,EAAMuN,CAAAA,GAI1BvN,EAAIgF,UAAU,CAAC,UAAYhF,EAAIgF,UAAU,CAAC,WAAU,CAE9D2B,GAAc,EACdsH,GAAS,GAEPnI,EAAOa,WAAW,EAAE,CACtBA,GAAc,GAGdkG,GACA,CAAC/G,EAAOoI,mBAAmB,EAC3BlO,EAAImO,KAAK,CAAC,IAAK,EAAE,CAAC,EAAE,CAACC,QAAQ,CAAC,SAC9B,CAGAzH,GAAc,GAGhB,IAAM0H,EAAalO,EAAO6F,GAyMpBsI,EAAW/G,OAAOgH,MAAM,CAC5BlF,EACI,CACEmF,SAAU,WACVxF,OAAQ,OACRjD,MAAO,OACP0I,KAAM,EACNC,IAAK,EACLC,MAAO,EACPC,OAAQ,YACRrC,iBACAC,CACF,EACA,CAAC,EACLX,EAAc,CAAC,EAAI,CAAEgD,MAAO,aAAc,EAC1C1F,GAGI2F,EACJ,GAAiC,UAAhBvI,EAWb,KAVgB,SAAhBA,EACG,yCAAwCwI,SDpnBnB,CAc/B,EAd+B,GConBkCA,UDnnBhEvB,CAAQ,WACRC,CAAS,WACTxB,CAAS,YACTC,CAAU,aACVG,CAAW,CACXE,WAAS,CAQV,CAd+B,EAgBxByC,EAAW/C,EAAwB,GAAZA,EAAiBuB,EACxCyB,EAAY/C,EAA0B,GAAbA,EAAkBuB,EAE3CyB,EACJF,GAAYC,EAAa,gBAAeD,EAAS,IAAGC,EAAU,IAAK,GASrE,mDAAoDC,EAA5C,QAAoD,8FAA2FC,MAAI,oQAAiQA,MAAI,qEARpYD,EACxB,OACc,YAAd3C,EACE,IAKuf6C,OAJze,UAAd7C,EACE,iBACA,QAEygB,sCAAqCF,EAAY,iBACpkB,ECslBmE,UACvDmB,YACAC,YACAxB,EACAC,aACAG,YAAaA,GAAe,GAC5BE,UAAW+B,EAAS/B,SAAS,GAC5B,KACF,QAAOhG,EAAY,KAAI,EAGTuI,EACnB,CACEO,eAAgBf,EAAS/B,CALwB,QAKf,EAAI,QACtC+C,mBAAoBhB,EAAS9B,cAAc,EAAI,UAC/C+C,iBAAkB,4BAClBT,CACF,EACA,CAAC,EAeC5E,EAAgBsF,SAldE,CAQR,EARQ,WACxB1J,CAAM,KACN9F,CAAG,aACH2G,CAAW,OACXZ,CAAK,SACLC,CAAO,OACP+C,CAAK,QACL6D,CAAM,CACU,CARQ,EASxB,GAAIjG,EACF,MAAO,CAAE3G,IADM,EACD8I,YAAQ7I,EAAW8I,MAAO9I,MAAU,EAGpD,GAAM,QAAEwP,CAAM,MAAEC,CAAI,CAAE,CAxExB,SAASC,CAC+B,CACtC5J,CAAyB,CACzBgD,CAAyB,EAFzB,gBAAEsC,CAAW,UAAED,CAAQ,CAAe,CAAtC,EAIA,GAAIrC,EAAO,CAET,IAAM6G,EAAkB,qBAClBC,EAAe,EAAE,CACvB,IAAK,IAAIC,EAAQA,EAAQF,EAAgBG,IAAI,CAAChH,GAAS+G,EACrDD,EAAa1M,EAD+C,EAC3C,CAAC1C,SAASqP,CAAK,CAAC,EAAE,GAErC,GAAID,EAAaxL,MAAM,CAAE,CACvB,IAAM2L,EAA4C,IAA5BjC,KAAKkC,GAAG,IAAIJ,GAClC,MAAO,CACLJ,OAAQrE,EAAS1J,MAAM,CAAC,GAAOwO,GAAK7E,CAAW,CAAC,EAAE,CAAG2E,GACrDN,KAAM,GACR,CACF,CACA,MAAO,CAAED,OAAQrE,EAAUsE,KAAM,GAAI,CACvC,OACA,UAAI,OAAO3J,EACF,CAAE0J,OAAQpE,EAAaqE,KAAM,GAAI,EAkBnC,CAAED,OAfM,IACV,IAAIjM,IACL,CAQCuC,EAAe,EAARA,EAA0B,CAACrB,GAAG,CACnCyL,GAAM/E,EAASgF,CADa,GACT,CAAC,GAAOvJ,GAAKsJ,IAAM/E,CAAQ,CAACA,EAAS/G,MAAM,CAAG,EAAE,GAGzE,CACgBqL,KAAM,GAAI,CAC7B,EA+BqC5J,EAAQC,EAAOgD,GAC5CsH,EA7CmE,EA6CrDhM,MAAM,CAAG,EAE7B,MAAO,CACL0E,MAAO,GAAmB,MAAT2G,EAAyB3G,EAAV,QAChCD,OAAQ2G,EACL/K,GAAG,CACF,CAACyL,EAAGhM,IACCyI,EAAO,QAAE9G,EAAQ9F,cAAKgG,EAASD,MAAOoK,CAAE,GAAG,KACnC,CAATT,KAAAA,EAAeS,EAAIhM,EAAAA,CAAAA,CAAI,CACtBuL,GAENY,IAAI,CAAC,MAQRtQ,IAAK4M,EAAO,CAAE9G,SAAQ9F,cAAKgG,EAASD,MAAO0J,CAAM,CAACY,EAAK,EACzD,CACF,EA+ayC,QACrCvK,MACA9F,cACA2G,EACAZ,MAAOyH,EACPxH,QAASqI,QACTtF,SACA6D,CACF,GA4BA,MAAO,CAAE5L,MAde,CACtB,GAAGyI,CAAI,CACPL,QAAS6E,EAAS,OAAS7E,gBAC3Bd,EACAvC,MAAOyH,EACPxE,OAAQyE,WACRxE,YACAC,EACAC,MAAO,CAAE,GAAGmF,CAAQ,CAAE,GAAGiC,CAAiB,EAC1CxH,MAAOmB,EAAcnB,KAAK,CAC1BD,OAAQoB,EAAcpB,MAAM,CAC5B9I,IAAKoM,GAAelC,EAAclK,GAAG,EAGvBiD,KADH,aAAE0D,WAAawF,cAAU5F,OAAa8C,CAAK,CACnC,CACvB,EKrTgErI,EAAO,CACjE6E,aAAaA,CDhTJA,CCgTIA,CACb8G,QAAS7G,GADID,YAEb8F,CDlTsB9F,EAAA,WCmTtBgG,CACF,GAEA,MACE,iCAEI,UAAClD,EAAAA,CACE,GAAGuB,CAAa,CACjBvD,YAAamF,EAAQnF,WAAW,CAChCJ,YAAauF,EAAQvF,WAAW,CAChC8C,KAAMyC,EAAQzC,IAAI,CAClB7C,UAAWA,EACXC,qBAAsBA,EACtBC,gBAAiBA,EACjB4C,eAAgBA,EAChB1C,WAAY5F,EAAM+H,KAAK,CACvBc,IAAKhB,IAGRiD,EAAQK,QAAQ,CACf,UAACnC,EAAAA,CACCC,YAnDY,CAmDCA,EACbC,cAAeA,IAEf,OAGV,GACD,4HI9YD,IAAMsG,EAAmB,sCCzBN,aAAhB,OAAOC,MACNA,KAAKC,mBAAmB,EACxBD,KAAKC,mBAAmB,CAACC,IAAI,CAACC,QAcf,GAbjB,SAAUC,CAaT,CAbgC,MAazBJ,MACNA,KAAKK,kBAAkB,EACvBL,KAAKK,kBAAkB,CAACH,IAAI,CAACC,WAC/B,SAAUG,EAAU,iCEkLtB,SAASC,EAAkBC,CAAkC,QAC3D,UAAI,OAAOA,EACFA,EH5KJ,SAASC,CAA2B,EACzC,GAAI,MAAEC,CAAI,UAAEC,CAAQ,CAAE,CAAGC,EACrBC,EAAWD,EAAOC,QAAQ,EAAI,GAC9BC,EAAWF,EAAOE,QAAQ,EAAI,GAC9BC,EAAOH,EAAOG,IAAI,EAAI,GACtBC,EAAQJ,EAAOI,KAAK,EAAI,GACxBC,GAAuB,EAE3BP,EAAOA,EAAOjL,mBAAmBiL,GAAMQ,OAAO,CAAC,OAAQ,KAAO,IAAM,GAEhEN,EAAOK,IAAI,CACbA,CADe,CACRP,EAAOE,EAAOK,IAAI,CAChBN,IACTM,EAAOP,EAAQ,EADI,EACMnN,CAAV,MAAiB,CAAC,KAAQ,IAAGoN,EAAS,IAAKA,CAAAA,CAAAA,CAAO,EACtDQ,IAAI,EAAE,CACfF,GAAQ,IAAML,EAAOO,IAAAA,GAIrBH,GAA0B,UAAjB,OAAOA,GAClBA,GAAQI,OAAOC,EAAAA,EAAkC,CAACL,GAAAA,EAGpD,IAAIM,EAASV,EAAOU,MAAM,EAAKN,GAAU,IAAGA,GAAY,GAoBxD,OAlBIH,GAAY,CAACA,EAASlD,QAAQ,CAAC,OAAMkD,GAAY,KAGnDD,EAAOW,OAAO,EACZ,EAACV,GAAYd,EAAiBhQ,IAAI,CAAC8Q,EAAAA,CAAAA,CAAQ,EAAe,IAATI,GACnD,EACO,MAAQA,CAAAA,EAAQ,IAAC,GACQ,MAAhBH,CAAQ,CAAC,EAAE,GAAUA,EAAW,IAAMA,CAAAA,GAC5CG,IACVA,EADgB,EACT,EAGLF,GAAoB,MAAZA,CAAI,CAAC,EAAE,GAAUA,EAAO,IAAMA,CAAAA,EACtCO,GAAwB,MAAdA,CAAM,CAAC,EAAE,GAAUA,EAAS,IAAMA,CAAAA,EAKxC,GAAET,EAAWI,GAHrBH,EAAWA,EAASI,OAAO,CAAC,GAGWI,KAHF7L,mBAAAA,GACrC6L,EAASA,EAAOJ,OAAO,CAAC,IAAK,QAEmBH,CAClD,EGmImBP,EACnB,CA+YA,MArYa5O,EAAAA,UAAgB,CAC3B,CAoYa4P,IAAIA,EAAA,EApYRC,CAAmB,CAAErJ,CAAY,MACpC/G,EAwLAK,EAtLJ,GAAM,CACJyI,KAAMuH,CAAQ,CACd/H,GAAIgI,CAAM,CACVtQ,SAAUuQ,CAAY,CACtBC,SAAUC,EAAe,IAAI,CAC7BC,UAAQ,SACRb,CAAO,SACPc,CAAO,CACPC,QAAM,CACNC,SAAO,CACPC,aAAcC,CAAgB,CAC9BC,aAAcC,CAAgB,gBAC9BC,GAAiB,CAAK,CACtB,GAAGC,EACJ,CAAGjS,EAEJc,EAAWuQ,EAGTW,GACC,CAAoB,UAApB,OAAOlR,GAA6C,UAApB,OAAOA,CAAa,EAAO,EAC5D,EACW,SAAXA,CAAYyJ,IAAAA,MAAZzJ,IAAeA,KAGjB,IAAMoR,EAAS7Q,EAAAA,UAAgB,CAAC8Q,EAAAA,gBAAgBA,EAS1CC,EACa,OAAjBb,EAAwBc,EAAAA,EAAYA,CAACC,IAAI,CAAGD,EAAAA,EAAYA,CAACE,IAAI,CAuIzD,MAAE3I,CAAI,IAAER,CAAE,CAAE,CAAG/H,EAAAA,OAAa,CAAC,KACjC,IAAMmR,EAAexC,EAAkBmB,GACvC,MAAO,CACLvH,KAAM4I,EACNpJ,GAAIgI,EAASpB,EAAkBoB,GAAUoB,CAC3C,CACF,EAAG,CAACrB,EAAUC,EAAO,EAEfqB,EAAepR,EAAAA,MAAY,CAASuI,GACpC8I,EAAarR,EAAAA,MAAY,CAAS+H,GAIpC4I,IA4BA7Q,EAAQE,EAAAA,QA5BQ,CA4BOsR,IAAI,CAAC7R,EAAAA,EAYhC,IAAM8R,EAAgBZ,EAClB7Q,GAA0B,UAAjB,OAAOA,GAAsBA,EAAM0H,GAAG,CAC/ChB,EAEE,CAACgL,EAAoBC,EAAWC,EAAa,CD9VhD,SAASC,CAIE,EAJiC,YACjDC,CAAO,YACPC,CAAU,UACVC,CAAQ,CACQ,CAJiC,EAO3C,CAACC,EAASC,EAAW,CAAGzI,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,IACjC0I,EAAa5I,CAAAA,EAAAA,EAAAA,MAAAA,CAAMA,CAAW,MAgCpC,MAAO,CA/BY/B,CAAAA,EAAAA,EAAAA,WAAAA,CAAWA,CAAE4K,IAC9BD,EAAWlN,OAAO,CAAGmN,CACvB,EAAG,EAAE,EA6BeH,EAJCzK,CAAAA,EAAAA,EAAAA,WAAAA,CAAWA,CAAC,KAC/B0K,GAAW,EACb,EAAG,EAAE,EAEqC,ECsT8B,CACpEH,WAAY,OACd,GAEMM,EAA8BnS,EAAAA,WAAiB,CACnD,KAEMqR,EAAWtM,OAAO,GAAKgD,GAAMqJ,EAAarM,OAAO,GAAKwD,CAAAA,GAAM,CAC9DmJ,IACAL,EAAWtM,OAAO,CAAGgD,EACrBqJ,EAAarM,OAAO,CAAGwD,GAGzBiJ,EAAmBY,EACrB,EACA,CAACrK,EAAIQ,EAAMmJ,EAAcF,EAAmB,EAGxCa,EAAS5K,CAAAA,EAAAA,EAAAA,CAAAA,CAAYA,CAAC0K,EAA6BZ,GAGzDvR,EAAAA,SAAe,CAAC,KAmBhB,EAAG,CAAC+H,EAAIQ,EAAMkJ,EAhPUvB,OAgPkBW,EAAQE,EAAgB,EAElE,IAAMuB,EAMF,CACF9K,IAAK6K,EACL/B,QAAQiC,CAAC,EASF5B,GAAqC,YAAnB,OAAOL,GAC5BA,EAAQiC,GAIR5B,GACA7Q,EAAMnB,KAAK,EACoB,YAA/B,OAAOmB,EAAMnB,KAAK,CAAC2R,OAAO,EAE1BxQ,EAAMnB,KAAK,CAAC2R,OAAO,CAACiC,GAGjB1B,IAID0B,EAAEC,EAJO,cAIS,EAItBC,SAlXCA,CACY,CACnB5B,CAAsC,CACtCtI,CAAY,CACZR,CAAU,CACVuH,CAAiB,CACjBc,CAAiB,CACjBC,CAAgB,EAEhB,GAAM,UAAEqC,CAAQ,CAAE,CAAGH,EAAE9M,aAAa,CAGgB,MAA3BiN,EAASC,WAAW,IAErBC,SA3BjBA,CAAuC,EAE9C,IAAMlN,EADcV,EAAMS,aAAa,CACZoN,YAAY,CAAC,UACxC,OACGnN,GAAqB,UAAXA,GACXV,EAAM8N,OAAO,EACb9N,EAAM+N,OAAO,EACb/N,EAAMgO,QAAQ,EACdhO,EAAMiO,MAAM,EACXjO,EAAMQ,WAAW,EAAgC,IAA5BR,EAAMQ,UADiB,CACN,CAAC0N,KAAK,EAkBPX,IAAI,CAK5CA,EAAEzM,cAAc,GAiBhB9F,EAAAA,eAAqB,CAACmT,KAbpB,IAAMC,EAAe/C,MAAAA,GAAAA,EACjB,OAD2B,YACPQ,EACtBA,CAAM,CAACvB,EAAU,EADa,QACD,OAAO,CAAC/G,EAAMR,EAAI,SAC7CqI,EACAC,OAAQ+C,CACV,GAEAvC,CAAM,CAACvB,EAAU,UAAY,OAAO,CAACvH,GAAMQ,EAAM,CAC/C8H,OAAQ+C,CACV,EAEJ,GAGF,EA6UoBb,EAAG1B,EAAQtI,EAAMR,EAAIuH,EAASc,EAASC,EACrD,EACAE,aAAagC,CAAC,EACP5B,GAA8C,YAAY,OAAjCH,GAC5BA,EAAiB+B,GAIjB5B,GACA7Q,EAAMnB,KAAK,EACX,YACA,OADOmB,EAAMnB,KAAK,CAAC4R,YAAY,EAE/BzQ,EAAMnB,KAAK,CAAC4R,YAAY,CAACgC,EAc7B,EACA9B,aAEI,CAFUlO,MAAsC,EAEvCkO,CAAc,CADd7S,CAEF+S,GAAkB,YAAwC,OAAjCD,GAC5BA,EAAiB6B,GAIjB5B,GACA7Q,EAAMnB,KAAK,EACyB,YAApC,OAAOmB,EAAMnB,KAAK,CAAC8R,YAAY,EAE/B3Q,EAAMnB,KAAK,CAAC8R,YAAY,CAAC8B,EAc7B,CACN,EAeA,MAVIc,CAAAA,EAAAA,EAAAA,EAAAA,CAAaA,CAACtL,GAChBuK,EADqB,IACN,CAAGvK,EAEjB4I,IACDR,IACgB,MAAfrQ,CAAsB,CAAhBC,IAAI,EAAc,SAAUD,EAAMnB,KAAAA,GACzC,CACA2T,EAAW/J,IAAI,CAAG+K,CAAAA,EAAAA,EAAAA,CAAAA,CAAWA,CAACvL,EAAAA,EAGzB4I,EACL3Q,EAAAA,YAAkB,CADb2Q,EACqB2B,GAE1B,MAHK3B,EAGL,EAACzH,CAFyBoJ,GAEzBpJ,CAAG,GAAG0H,CAAS,CAAG,EAFO0B,CAEJA,CAAU,UAC7B7S,GAGP,mDC/kBK,SAASgI,EACd8L,CAAmB,CACnBC,CAAmB,EAEnB,IAAMC,EAAWpK,CAAAA,EAAAA,EAAAA,MAAAA,CAAMA,CAAa,KAAO,GACrCqK,EAAWrK,CAAAA,EAAAA,EAAAA,MAAAA,CAAMA,CAAa,KAAO,GAE3C,MAAOR,CAAAA,EAAAA,EAAAA,OAAAA,CAAOA,CAAC,IACb,GAAc2K,EAIP,GAJM,CAAO,MAKI,CAAlBzO,GACF0O,EAAS1O,OAAO,GAChB2O,EAAS3O,OAAO,KAEhB0O,EAAS1O,OAAO,CAAG4O,EAASJ,EAAMxO,GAClC2O,EAAS3O,OAAO,CAAG4O,EAASH,EAAMzO,GAEtC,EAXSwO,GAAQC,EAYhB,CAACD,EAAMC,EAAK,CACjB,CAEA,SAASG,EACPJ,CAAgC,CAChCxO,CAAiB,EAEjB,GAAoB,YAAhB,OAAOwO,EAST,OADAA,EAAKxO,OAAO,CAAGA,EACR,KACLwO,EAAKxO,OAAO,CAAG,IACjB,CAX8B,EAC9B,IAAM6O,EAAUL,EAAKxO,SACrB,YAAI,OAAO6O,EACFA,EAEA,IAAML,EAAK,KAEtB,CAMF", "sources": ["webpack://_N_E/../../../src/icons/log-in.ts", "webpack://_N_E/../../../src/icons/mail.ts", "webpack://_N_E/../../../src/icons/moon.ts", "webpack://_N_E/../../../src/icons/sun.ts", "webpack://_N_E/../../../src/shared/lib/image-blur-svg.ts", "webpack://_N_E/../../../src/shared/lib/get-img-props.ts", "webpack://_N_E/../../../src/shared/lib/side-effect.tsx", "webpack://_N_E/../../../src/shared/lib/amp-mode.ts", "webpack://_N_E/../../../src/shared/lib/head.tsx", "webpack://_N_E/../../../src/shared/lib/image-loader.ts", "webpack://_N_E/../../src/client/image-component.tsx", "webpack://_N_E/../../../src/shared/lib/image-external.tsx", "webpack://_N_E/./node_modules/next/dist/esm/api/image.js", "webpack://_N_E/./node_modules/next/dist/esm/api/navigation.js", "webpack://_N_E/../../../../src/shared/lib/router/utils/format-url.ts", "webpack://_N_E/../../src/client/request-idle-callback.ts", "webpack://_N_E/../../src/client/use-intersection.tsx", "webpack://_N_E/../../../src/client/app-dir/link.tsx", "webpack://_N_E/../../src/client/use-merged-ref.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name LogIn\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTUgM2g0YTIgMiAwIDAgMSAyIDJ2MTRhMiAyIDAgMCAxLTIgMmgtNCIgLz4KICA8cG9seWxpbmUgcG9pbnRzPSIxMCAxNyAxNSAxMiAxMCA3IiAvPgogIDxsaW5lIHgxPSIxNSIgeDI9IjMiIHkxPSIxMiIgeTI9IjEyIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/log-in\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst LogIn = createLucideIcon('LogIn', [\n  ['path', { d: 'M15 3h4a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-4', key: 'u53s6r' }],\n  ['polyline', { points: '10 17 15 12 10 7', key: '1ail0h' }],\n  ['line', { x1: '15', x2: '3', y1: '12', y2: '12', key: 'v6grx8' }],\n]);\n\nexport default LogIn;\n", "import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Mail\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cmVjdCB3aWR0aD0iMjAiIGhlaWdodD0iMTYiIHg9IjIiIHk9IjQiIHJ4PSIyIiAvPgogIDxwYXRoIGQ9Im0yMiA3LTguOTcgNS43YTEuOTQgMS45NCAwIDAgMS0yLjA2IDBMMiA3IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/mail\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Mail = createLucideIcon('Mail', [\n  ['rect', { width: '20', height: '16', x: '2', y: '4', rx: '2', key: '18n3k1' }],\n  ['path', { d: 'm22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7', key: '1ocrg3' }],\n]);\n\nexport default Mail;\n", "import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Moon\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTIgM2E2IDYgMCAwIDAgOSA5IDkgOSAwIDEgMS05LTlaIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/moon\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Moon = createLucideIcon('Moon', [\n  ['path', { d: 'M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z', key: 'a7tn18' }],\n]);\n\nexport default Moon;\n", "import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Sun\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSI0IiAvPgogIDxwYXRoIGQ9Ik0xMiAydjIiIC8+CiAgPHBhdGggZD0iTTEyIDIwdjIiIC8+CiAgPHBhdGggZD0ibTQuOTMgNC45MyAxLjQxIDEuNDEiIC8+CiAgPHBhdGggZD0ibTE3LjY2IDE3LjY2IDEuNDEgMS40MSIgLz4KICA8cGF0aCBkPSJNMiAxMmgyIiAvPgogIDxwYXRoIGQ9Ik0yMCAxMmgyIiAvPgogIDxwYXRoIGQ9Im02LjM0IDE3LjY2LTEuNDEgMS40MSIgLz4KICA8cGF0aCBkPSJtMTkuMDcgNC45My0xLjQxIDEuNDEiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/sun\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Sun = createLucideIcon('Sun', [\n  ['circle', { cx: '12', cy: '12', r: '4', key: '4exip2' }],\n  ['path', { d: 'M12 2v2', key: 'tus03m' }],\n  ['path', { d: 'M12 20v2', key: '1lh1kg' }],\n  ['path', { d: 'm4.93 4.93 1.41 1.41', key: '149t6j' }],\n  ['path', { d: 'm17.66 17.66 1.41 1.41', key: 'ptbguv' }],\n  ['path', { d: 'M2 12h2', key: '1t8f8n' }],\n  ['path', { d: 'M20 12h2', key: '1q8mjw' }],\n  ['path', { d: 'm6.34 17.66-1.41 1.41', key: '1m8zz5' }],\n  ['path', { d: 'm19.07 4.93-1.41 1.41', key: '1shlcs' }],\n]);\n\nexport default Sun;\n", "/**\n * A shared function, used on both client and server, to generate a SVG blur placeholder.\n */\nexport function getImageBlurSvg({\n  widthInt,\n  heightInt,\n  blurWidth,\n  blurHeight,\n  blurDataURL,\n  objectFit,\n}: {\n  widthInt?: number\n  heightInt?: number\n  blurWidth?: number\n  blurHeight?: number\n  blurDataURL: string\n  objectFit?: string\n}): string {\n  const std = 20\n  const svgWidth = blurWidth ? blurWidth * 40 : widthInt\n  const svgHeight = blurHeight ? blurHeight * 40 : heightInt\n\n  const viewBox =\n    svgWidth && svgHeight ? `viewBox='0 0 ${svgWidth} ${svgHeight}'` : ''\n  const preserveAspectRatio = viewBox\n    ? 'none'\n    : objectFit === 'contain'\n      ? 'xMidYMid'\n      : objectFit === 'cover'\n        ? 'xMidYMid slice'\n        : 'none'\n\n  return `%3Csvg xmlns='http://www.w3.org/2000/svg' ${viewBox}%3E%3Cfilter id='b' color-interpolation-filters='sRGB'%3E%3CfeGaussianBlur stdDeviation='${std}'/%3E%3CfeColorMatrix values='1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 100 -1' result='s'/%3E%3CfeFlood x='0' y='0' width='100%25' height='100%25'/%3E%3CfeComposite operator='out' in='s'/%3E%3CfeComposite in2='SourceGraphic'/%3E%3CfeGaussianBlur stdDeviation='${std}'/%3E%3C/filter%3E%3Cimage width='100%25' height='100%25' x='0' y='0' preserveAspectRatio='${preserveAspectRatio}' style='filter: url(%23b);' href='${blurDataURL}'/%3E%3C/svg%3E`\n}\n", "import { warnOnce } from './utils/warn-once'\nimport { getImageBlurSvg } from './image-blur-svg'\nimport { imageConfigDefault } from './image-config'\nimport type {\n  ImageConfigComplete,\n  ImageLoaderProps,\n  ImageLoaderPropsWithConfig,\n} from './image-config'\n\nimport type { JSX } from 'react'\n\nexport interface StaticImageData {\n  src: string\n  height: number\n  width: number\n  blurDataURL?: string\n  blurWidth?: number\n  blurHeight?: number\n}\n\nexport interface StaticRequire {\n  default: StaticImageData\n}\n\nexport type StaticImport = StaticRequire | StaticImageData\n\nexport type ImageProps = Omit<\n  JSX.IntrinsicElements['img'],\n  'src' | 'srcSet' | 'ref' | 'alt' | 'width' | 'height' | 'loading'\n> & {\n  src: string | StaticImport\n  alt: string\n  width?: number | `${number}`\n  height?: number | `${number}`\n  fill?: boolean\n  loader?: ImageLoader\n  quality?: number | `${number}`\n  priority?: boolean\n  loading?: LoadingValue\n  placeholder?: PlaceholderValue\n  blurDataURL?: string\n  unoptimized?: boolean\n  overrideSrc?: string\n  /**\n   * @deprecated Use `onLoad` instead.\n   * @see https://nextjs.org/docs/app/api-reference/components/image#onload\n   */\n  onLoadingComplete?: OnLoadingComplete\n  /**\n   * @deprecated Use `fill` prop instead of `layout=\"fill\"` or change import to `next/legacy/image`.\n   * @see https://nextjs.org/docs/api-reference/next/legacy/image\n   */\n  layout?: string\n  /**\n   * @deprecated Use `style` prop instead.\n   */\n  objectFit?: string\n  /**\n   * @deprecated Use `style` prop instead.\n   */\n  objectPosition?: string\n  /**\n   * @deprecated This prop does not do anything.\n   */\n  lazyBoundary?: string\n  /**\n   * @deprecated This prop does not do anything.\n   */\n  lazyRoot?: string\n}\n\nexport type ImgProps = Omit<ImageProps, 'src' | 'loader'> & {\n  loading: LoadingValue\n  width: number | undefined\n  height: number | undefined\n  style: NonNullable<JSX.IntrinsicElements['img']['style']>\n  sizes: string | undefined\n  srcSet: string | undefined\n  src: string\n}\n\nconst VALID_LOADING_VALUES = ['lazy', 'eager', undefined] as const\ntype LoadingValue = (typeof VALID_LOADING_VALUES)[number]\ntype ImageConfig = ImageConfigComplete & {\n  allSizes: number[]\n  output?: 'standalone' | 'export'\n}\n\nexport type ImageLoader = (p: ImageLoaderProps) => string\n\n// Do not export - this is an internal type only\n// because `next.config.js` is only meant for the\n// built-in loaders, not for a custom loader() prop.\ntype ImageLoaderWithConfig = (p: ImageLoaderPropsWithConfig) => string\n\nexport type PlaceholderValue = 'blur' | 'empty' | `data:image/${string}`\nexport type OnLoad = React.ReactEventHandler<HTMLImageElement> | undefined\nexport type OnLoadingComplete = (img: HTMLImageElement) => void\n\nfunction isStaticRequire(\n  src: StaticRequire | StaticImageData\n): src is StaticRequire {\n  return (src as StaticRequire).default !== undefined\n}\n\nfunction isStaticImageData(\n  src: StaticRequire | StaticImageData\n): src is StaticImageData {\n  return (src as StaticImageData).src !== undefined\n}\n\nfunction isStaticImport(src: string | StaticImport): src is StaticImport {\n  return (\n    !!src &&\n    typeof src === 'object' &&\n    (isStaticRequire(src as StaticImport) ||\n      isStaticImageData(src as StaticImport))\n  )\n}\n\nconst allImgs = new Map<\n  string,\n  { src: string; priority: boolean; placeholder: PlaceholderValue }\n>()\nlet perfObserver: PerformanceObserver | undefined\n\nfunction getInt(x: unknown): number | undefined {\n  if (typeof x === 'undefined') {\n    return x\n  }\n  if (typeof x === 'number') {\n    return Number.isFinite(x) ? x : NaN\n  }\n  if (typeof x === 'string' && /^[0-9]+$/.test(x)) {\n    return parseInt(x, 10)\n  }\n  return NaN\n}\n\nfunction getWidths(\n  { deviceSizes, allSizes }: ImageConfig,\n  width: number | undefined,\n  sizes: string | undefined\n): { widths: number[]; kind: 'w' | 'x' } {\n  if (sizes) {\n    // Find all the \"vw\" percent sizes used in the sizes prop\n    const viewportWidthRe = /(^|\\s)(1?\\d?\\d)vw/g\n    const percentSizes = []\n    for (let match; (match = viewportWidthRe.exec(sizes)); match) {\n      percentSizes.push(parseInt(match[2]))\n    }\n    if (percentSizes.length) {\n      const smallestRatio = Math.min(...percentSizes) * 0.01\n      return {\n        widths: allSizes.filter((s) => s >= deviceSizes[0] * smallestRatio),\n        kind: 'w',\n      }\n    }\n    return { widths: allSizes, kind: 'w' }\n  }\n  if (typeof width !== 'number') {\n    return { widths: deviceSizes, kind: 'w' }\n  }\n\n  const widths = [\n    ...new Set(\n      // > This means that most OLED screens that say they are 3x resolution,\n      // > are actually 3x in the green color, but only 1.5x in the red and\n      // > blue colors. Showing a 3x resolution image in the app vs a 2x\n      // > resolution image will be visually the same, though the 3x image\n      // > takes significantly more data. Even true 3x resolution screens are\n      // > wasteful as the human eye cannot see that level of detail without\n      // > something like a magnifying glass.\n      // https://blog.twitter.com/engineering/en_us/topics/infrastructure/2019/capping-image-fidelity-on-ultra-high-resolution-devices.html\n      [width, width * 2 /*, width * 3*/].map(\n        (w) => allSizes.find((p) => p >= w) || allSizes[allSizes.length - 1]\n      )\n    ),\n  ]\n  return { widths, kind: 'x' }\n}\n\ntype GenImgAttrsData = {\n  config: ImageConfig\n  src: string\n  unoptimized: boolean\n  loader: ImageLoaderWithConfig\n  width?: number\n  quality?: number\n  sizes?: string\n}\n\ntype GenImgAttrsResult = {\n  src: string\n  srcSet: string | undefined\n  sizes: string | undefined\n}\n\nfunction generateImgAttrs({\n  config,\n  src,\n  unoptimized,\n  width,\n  quality,\n  sizes,\n  loader,\n}: GenImgAttrsData): GenImgAttrsResult {\n  if (unoptimized) {\n    return { src, srcSet: undefined, sizes: undefined }\n  }\n\n  const { widths, kind } = getWidths(config, width, sizes)\n  const last = widths.length - 1\n\n  return {\n    sizes: !sizes && kind === 'w' ? '100vw' : sizes,\n    srcSet: widths\n      .map(\n        (w, i) =>\n          `${loader({ config, src, quality, width: w })} ${\n            kind === 'w' ? w : i + 1\n          }${kind}`\n      )\n      .join(', '),\n\n    // It's intended to keep `src` the last attribute because React updates\n    // attributes in order. If we keep `src` the first one, Safari will\n    // immediately start to fetch `src`, before `sizes` and `srcSet` are even\n    // updated by React. That causes multiple unnecessary requests if `srcSet`\n    // and `sizes` are defined.\n    // This bug cannot be reproduced in Chrome or Firefox.\n    src: loader({ config, src, quality, width: widths[last] }),\n  }\n}\n\n/**\n * A shared function, used on both client and server, to generate the props for <img>.\n */\nexport function getImgProps(\n  {\n    src,\n    sizes,\n    unoptimized = false,\n    priority = false,\n    loading,\n    className,\n    quality,\n    width,\n    height,\n    fill = false,\n    style,\n    overrideSrc,\n    onLoad,\n    onLoadingComplete,\n    placeholder = 'empty',\n    blurDataURL,\n    fetchPriority,\n    decoding = 'async',\n    layout,\n    objectFit,\n    objectPosition,\n    lazyBoundary,\n    lazyRoot,\n    ...rest\n  }: ImageProps,\n  _state: {\n    defaultLoader: ImageLoaderWithConfig\n    imgConf: ImageConfigComplete\n    showAltText?: boolean\n    blurComplete?: boolean\n  }\n): {\n  props: ImgProps\n  meta: {\n    unoptimized: boolean\n    priority: boolean\n    placeholder: NonNullable<ImageProps['placeholder']>\n    fill: boolean\n  }\n} {\n  const { imgConf, showAltText, blurComplete, defaultLoader } = _state\n  let config: ImageConfig\n  let c = imgConf || imageConfigDefault\n  if ('allSizes' in c) {\n    config = c as ImageConfig\n  } else {\n    const allSizes = [...c.deviceSizes, ...c.imageSizes].sort((a, b) => a - b)\n    const deviceSizes = c.deviceSizes.sort((a, b) => a - b)\n    config = { ...c, allSizes, deviceSizes }\n  }\n\n  if (typeof defaultLoader === 'undefined') {\n    throw new Error(\n      'images.loaderFile detected but the file is missing default export.\\nRead more: https://nextjs.org/docs/messages/invalid-images-config'\n    )\n  }\n  let loader: ImageLoaderWithConfig = rest.loader || defaultLoader\n\n  // Remove property so it's not spread on <img> element\n  delete rest.loader\n  delete (rest as any).srcSet\n\n  // This special value indicates that the user\n  // didn't define a \"loader\" prop or \"loader\" config.\n  const isDefaultLoader = '__next_img_default' in loader\n\n  if (isDefaultLoader) {\n    if (config.loader === 'custom') {\n      throw new Error(\n        `Image with src \"${src}\" is missing \"loader\" prop.` +\n          `\\nRead more: https://nextjs.org/docs/messages/next-image-missing-loader`\n      )\n    }\n  } else {\n    // The user defined a \"loader\" prop or config.\n    // Since the config object is internal only, we\n    // must not pass it to the user-defined \"loader\".\n    const customImageLoader = loader as ImageLoader\n    loader = (obj) => {\n      const { config: _, ...opts } = obj\n      return customImageLoader(opts)\n    }\n  }\n\n  if (layout) {\n    if (layout === 'fill') {\n      fill = true\n    }\n    const layoutToStyle: Record<string, Record<string, string> | undefined> = {\n      intrinsic: { maxWidth: '100%', height: 'auto' },\n      responsive: { width: '100%', height: 'auto' },\n    }\n    const layoutToSizes: Record<string, string | undefined> = {\n      responsive: '100vw',\n      fill: '100vw',\n    }\n    const layoutStyle = layoutToStyle[layout]\n    if (layoutStyle) {\n      style = { ...style, ...layoutStyle }\n    }\n    const layoutSizes = layoutToSizes[layout]\n    if (layoutSizes && !sizes) {\n      sizes = layoutSizes\n    }\n  }\n\n  let staticSrc = ''\n  let widthInt = getInt(width)\n  let heightInt = getInt(height)\n  let blurWidth: number | undefined\n  let blurHeight: number | undefined\n  if (isStaticImport(src)) {\n    const staticImageData = isStaticRequire(src) ? src.default : src\n\n    if (!staticImageData.src) {\n      throw new Error(\n        `An object should only be passed to the image component src parameter if it comes from a static image import. It must include src. Received ${JSON.stringify(\n          staticImageData\n        )}`\n      )\n    }\n    if (!staticImageData.height || !staticImageData.width) {\n      throw new Error(\n        `An object should only be passed to the image component src parameter if it comes from a static image import. It must include height and width. Received ${JSON.stringify(\n          staticImageData\n        )}`\n      )\n    }\n\n    blurWidth = staticImageData.blurWidth\n    blurHeight = staticImageData.blurHeight\n    blurDataURL = blurDataURL || staticImageData.blurDataURL\n    staticSrc = staticImageData.src\n\n    if (!fill) {\n      if (!widthInt && !heightInt) {\n        widthInt = staticImageData.width\n        heightInt = staticImageData.height\n      } else if (widthInt && !heightInt) {\n        const ratio = widthInt / staticImageData.width\n        heightInt = Math.round(staticImageData.height * ratio)\n      } else if (!widthInt && heightInt) {\n        const ratio = heightInt / staticImageData.height\n        widthInt = Math.round(staticImageData.width * ratio)\n      }\n    }\n  }\n  src = typeof src === 'string' ? src : staticSrc\n\n  let isLazy =\n    !priority && (loading === 'lazy' || typeof loading === 'undefined')\n  if (!src || src.startsWith('data:') || src.startsWith('blob:')) {\n    // https://developer.mozilla.org/docs/Web/HTTP/Basics_of_HTTP/Data_URIs\n    unoptimized = true\n    isLazy = false\n  }\n  if (config.unoptimized) {\n    unoptimized = true\n  }\n  if (\n    isDefaultLoader &&\n    !config.dangerouslyAllowSVG &&\n    src.split('?', 1)[0].endsWith('.svg')\n  ) {\n    // Special case to make svg serve as-is to avoid proxying\n    // through the built-in Image Optimization API.\n    unoptimized = true\n  }\n\n  const qualityInt = getInt(quality)\n\n  if (process.env.NODE_ENV !== 'production') {\n    if (config.output === 'export' && isDefaultLoader && !unoptimized) {\n      throw new Error(\n        `Image Optimization using the default loader is not compatible with \\`{ output: 'export' }\\`.\n  Possible solutions:\n    - Remove \\`{ output: 'export' }\\` and run \"next start\" to run server mode including the Image Optimization API.\n    - Configure \\`{ images: { unoptimized: true } }\\` in \\`next.config.js\\` to disable the Image Optimization API.\n  Read more: https://nextjs.org/docs/messages/export-image-api`\n      )\n    }\n    if (!src) {\n      // React doesn't show the stack trace and there's\n      // no `src` to help identify which image, so we\n      // instead console.error(ref) during mount.\n      unoptimized = true\n    } else {\n      if (fill) {\n        if (width) {\n          throw new Error(\n            `Image with src \"${src}\" has both \"width\" and \"fill\" properties. Only one should be used.`\n          )\n        }\n        if (height) {\n          throw new Error(\n            `Image with src \"${src}\" has both \"height\" and \"fill\" properties. Only one should be used.`\n          )\n        }\n        if (style?.position && style.position !== 'absolute') {\n          throw new Error(\n            `Image with src \"${src}\" has both \"fill\" and \"style.position\" properties. Images with \"fill\" always use position absolute - it cannot be modified.`\n          )\n        }\n        if (style?.width && style.width !== '100%') {\n          throw new Error(\n            `Image with src \"${src}\" has both \"fill\" and \"style.width\" properties. Images with \"fill\" always use width 100% - it cannot be modified.`\n          )\n        }\n        if (style?.height && style.height !== '100%') {\n          throw new Error(\n            `Image with src \"${src}\" has both \"fill\" and \"style.height\" properties. Images with \"fill\" always use height 100% - it cannot be modified.`\n          )\n        }\n      } else {\n        if (typeof widthInt === 'undefined') {\n          throw new Error(\n            `Image with src \"${src}\" is missing required \"width\" property.`\n          )\n        } else if (isNaN(widthInt)) {\n          throw new Error(\n            `Image with src \"${src}\" has invalid \"width\" property. Expected a numeric value in pixels but received \"${width}\".`\n          )\n        }\n        if (typeof heightInt === 'undefined') {\n          throw new Error(\n            `Image with src \"${src}\" is missing required \"height\" property.`\n          )\n        } else if (isNaN(heightInt)) {\n          throw new Error(\n            `Image with src \"${src}\" has invalid \"height\" property. Expected a numeric value in pixels but received \"${height}\".`\n          )\n        }\n        // eslint-disable-next-line no-control-regex\n        if (/^[\\x00-\\x20]/.test(src)) {\n          throw new Error(\n            `Image with src \"${src}\" cannot start with a space or control character. Use src.trimStart() to remove it or encodeURIComponent(src) to keep it.`\n          )\n        }\n        // eslint-disable-next-line no-control-regex\n        if (/[\\x00-\\x20]$/.test(src)) {\n          throw new Error(\n            `Image with src \"${src}\" cannot end with a space or control character. Use src.trimEnd() to remove it or encodeURIComponent(src) to keep it.`\n          )\n        }\n      }\n    }\n    if (!VALID_LOADING_VALUES.includes(loading)) {\n      throw new Error(\n        `Image with src \"${src}\" has invalid \"loading\" property. Provided \"${loading}\" should be one of ${VALID_LOADING_VALUES.map(\n          String\n        ).join(',')}.`\n      )\n    }\n    if (priority && loading === 'lazy') {\n      throw new Error(\n        `Image with src \"${src}\" has both \"priority\" and \"loading='lazy'\" properties. Only one should be used.`\n      )\n    }\n    if (\n      placeholder !== 'empty' &&\n      placeholder !== 'blur' &&\n      !placeholder.startsWith('data:image/')\n    ) {\n      throw new Error(\n        `Image with src \"${src}\" has invalid \"placeholder\" property \"${placeholder}\".`\n      )\n    }\n    if (placeholder !== 'empty') {\n      if (widthInt && heightInt && widthInt * heightInt < 1600) {\n        warnOnce(\n          `Image with src \"${src}\" is smaller than 40x40. Consider removing the \"placeholder\" property to improve performance.`\n        )\n      }\n    }\n    if (placeholder === 'blur' && !blurDataURL) {\n      const VALID_BLUR_EXT = ['jpeg', 'png', 'webp', 'avif'] // should match next-image-loader\n\n      throw new Error(\n        `Image with src \"${src}\" has \"placeholder='blur'\" property but is missing the \"blurDataURL\" property.\n        Possible solutions:\n          - Add a \"blurDataURL\" property, the contents should be a small Data URL to represent the image\n          - Change the \"src\" property to a static import with one of the supported file types: ${VALID_BLUR_EXT.join(\n            ','\n          )} (animated images not supported)\n          - Remove the \"placeholder\" property, effectively no blur effect\n        Read more: https://nextjs.org/docs/messages/placeholder-blur-data-url`\n      )\n    }\n    if ('ref' in rest) {\n      warnOnce(\n        `Image with src \"${src}\" is using unsupported \"ref\" property. Consider using the \"onLoad\" property instead.`\n      )\n    }\n\n    if (!unoptimized && !isDefaultLoader) {\n      const urlStr = loader({\n        config,\n        src,\n        width: widthInt || 400,\n        quality: qualityInt || 75,\n      })\n      let url: URL | undefined\n      try {\n        url = new URL(urlStr)\n      } catch (err) {}\n      if (urlStr === src || (url && url.pathname === src && !url.search)) {\n        warnOnce(\n          `Image with src \"${src}\" has a \"loader\" property that does not implement width. Please implement it or use the \"unoptimized\" property instead.` +\n            `\\nRead more: https://nextjs.org/docs/messages/next-image-missing-loader-width`\n        )\n      }\n    }\n\n    if (onLoadingComplete) {\n      warnOnce(\n        `Image with src \"${src}\" is using deprecated \"onLoadingComplete\" property. Please use the \"onLoad\" property instead.`\n      )\n    }\n\n    for (const [legacyKey, legacyValue] of Object.entries({\n      layout,\n      objectFit,\n      objectPosition,\n      lazyBoundary,\n      lazyRoot,\n    })) {\n      if (legacyValue) {\n        warnOnce(\n          `Image with src \"${src}\" has legacy prop \"${legacyKey}\". Did you forget to run the codemod?` +\n            `\\nRead more: https://nextjs.org/docs/messages/next-image-upgrade-to-13`\n        )\n      }\n    }\n\n    if (\n      typeof window !== 'undefined' &&\n      !perfObserver &&\n      window.PerformanceObserver\n    ) {\n      perfObserver = new PerformanceObserver((entryList) => {\n        for (const entry of entryList.getEntries()) {\n          // @ts-ignore - missing \"LargestContentfulPaint\" class with \"element\" prop\n          const imgSrc = entry?.element?.src || ''\n          const lcpImage = allImgs.get(imgSrc)\n          if (\n            lcpImage &&\n            !lcpImage.priority &&\n            lcpImage.placeholder === 'empty' &&\n            !lcpImage.src.startsWith('data:') &&\n            !lcpImage.src.startsWith('blob:')\n          ) {\n            // https://web.dev/lcp/#measure-lcp-in-javascript\n            warnOnce(\n              `Image with src \"${lcpImage.src}\" was detected as the Largest Contentful Paint (LCP). Please add the \"priority\" property if this image is above the fold.` +\n                `\\nRead more: https://nextjs.org/docs/api-reference/next/image#priority`\n            )\n          }\n        }\n      })\n      try {\n        perfObserver.observe({\n          type: 'largest-contentful-paint',\n          buffered: true,\n        })\n      } catch (err) {\n        // Log error but don't crash the app\n        console.error(err)\n      }\n    }\n  }\n  const imgStyle = Object.assign(\n    fill\n      ? {\n          position: 'absolute',\n          height: '100%',\n          width: '100%',\n          left: 0,\n          top: 0,\n          right: 0,\n          bottom: 0,\n          objectFit,\n          objectPosition,\n        }\n      : {},\n    showAltText ? {} : { color: 'transparent' },\n    style\n  )\n\n  const backgroundImage =\n    !blurComplete && placeholder !== 'empty'\n      ? placeholder === 'blur'\n        ? `url(\"data:image/svg+xml;charset=utf-8,${getImageBlurSvg({\n            widthInt,\n            heightInt,\n            blurWidth,\n            blurHeight,\n            blurDataURL: blurDataURL || '', // assume not undefined\n            objectFit: imgStyle.objectFit,\n          })}\")`\n        : `url(\"${placeholder}\")` // assume `data:image/`\n      : null\n\n  let placeholderStyle = backgroundImage\n    ? {\n        backgroundSize: imgStyle.objectFit || 'cover',\n        backgroundPosition: imgStyle.objectPosition || '50% 50%',\n        backgroundRepeat: 'no-repeat',\n        backgroundImage,\n      }\n    : {}\n\n  if (process.env.NODE_ENV === 'development') {\n    if (\n      placeholderStyle.backgroundImage &&\n      placeholder === 'blur' &&\n      blurDataURL?.startsWith('/')\n    ) {\n      // During `next dev`, we don't want to generate blur placeholders with webpack\n      // because it can delay starting the dev server. Instead, `next-image-loader.js`\n      // will inline a special url to lazily generate the blur placeholder at request time.\n      placeholderStyle.backgroundImage = `url(\"${blurDataURL}\")`\n    }\n  }\n\n  const imgAttributes = generateImgAttrs({\n    config,\n    src,\n    unoptimized,\n    width: widthInt,\n    quality: qualityInt,\n    sizes,\n    loader,\n  })\n\n  if (process.env.NODE_ENV !== 'production') {\n    if (typeof window !== 'undefined') {\n      let fullUrl: URL\n      try {\n        fullUrl = new URL(imgAttributes.src)\n      } catch (e) {\n        fullUrl = new URL(imgAttributes.src, window.location.href)\n      }\n      allImgs.set(fullUrl.href, { src, priority, placeholder })\n    }\n  }\n\n  const props: ImgProps = {\n    ...rest,\n    loading: isLazy ? 'lazy' : loading,\n    fetchPriority,\n    width: widthInt,\n    height: heightInt,\n    decoding,\n    className,\n    style: { ...imgStyle, ...placeholderStyle },\n    sizes: imgAttributes.sizes,\n    srcSet: imgAttributes.srcSet,\n    src: overrideSrc || imgAttributes.src,\n  }\n  const meta = { unoptimized, priority, placeholder, fill }\n  return { props, meta }\n}\n", "import type React from 'react'\nimport { Children, useEffect, useLayoutEffect, type JSX } from 'react'\n\ntype State = JSX.Element[] | undefined\n\nexport type SideEffectProps = {\n  reduceComponentsToState: <T extends {}>(\n    components: Array<React.ReactElement<any>>,\n    props: T\n  ) => State\n  handleStateChange?: (state: State) => void\n  headManager: any\n  inAmpMode?: boolean\n  children: React.ReactNode\n}\n\nconst isServer = typeof window === 'undefined'\nconst useClientOnlyLayoutEffect = isServer ? () => {} : useLayoutEffect\nconst useClientOnlyEffect = isServer ? () => {} : useEffect\n\nexport default function SideEffect(props: SideEffectProps) {\n  const { headManager, reduceComponentsToState } = props\n\n  function emitChange() {\n    if (headManager && headManager.mountedInstances) {\n      const headElements = Children.toArray(\n        Array.from(headManager.mountedInstances as Set<React.ReactNode>).filter(\n          Boolean\n        )\n      ) as React.ReactElement[]\n      headManager.updateHead(reduceComponentsToState(headElements, props))\n    }\n  }\n\n  if (isServer) {\n    headManager?.mountedInstances?.add(props.children)\n    emitChange()\n  }\n\n  useClientOnlyLayoutEffect(() => {\n    headManager?.mountedInstances?.add(props.children)\n    return () => {\n      headManager?.mountedInstances?.delete(props.children)\n    }\n  })\n\n  // We need to call `updateHead` method whenever the `SideEffect` is trigger in all\n  // life-cycles: mount, update, unmount. However, if there are multiple `SideEffect`s\n  // being rendered, we only trigger the method from the last one.\n  // This is ensured by keeping the last unflushed `updateHead` in the `_pendingUpdate`\n  // singleton in the layout effect pass, and actually trigger it in the effect pass.\n  useClientOnlyLayoutEffect(() => {\n    if (headManager) {\n      headManager._pendingUpdate = emitChange\n    }\n    return () => {\n      if (headManager) {\n        headManager._pendingUpdate = emitChange\n      }\n    }\n  })\n\n  useClientOnlyEffect(() => {\n    if (headManager && headManager._pendingUpdate) {\n      headManager._pendingUpdate()\n      headManager._pendingUpdate = null\n    }\n    return () => {\n      if (headManager && headManager._pendingUpdate) {\n        headManager._pendingUpdate()\n        headManager._pendingUpdate = null\n      }\n    }\n  })\n\n  return null\n}\n", "export function isInAmpMode({\n  ampFirst = false,\n  hybrid = false,\n  hasQuery = false,\n} = {}): boolean {\n  return ampFirst || (hybrid && hasQuery)\n}\n", "'use client'\n\nimport React, { useContext, type JSX } from 'react'\nimport Effect from './side-effect'\nimport { AmpStateContext } from './amp-context.shared-runtime'\nimport { HeadManagerContext } from './head-manager-context.shared-runtime'\nimport { isInAmpMode } from './amp-mode'\nimport { warnOnce } from './utils/warn-once'\n\ntype WithInAmpMode = {\n  inAmpMode?: boolean\n}\n\nexport function defaultHead(inAmpMode = false): JSX.Element[] {\n  const head = [<meta charSet=\"utf-8\" key=\"charset\" />]\n  if (!inAmpMode) {\n    head.push(\n      <meta name=\"viewport\" content=\"width=device-width\" key=\"viewport\" />\n    )\n  }\n  return head\n}\n\nfunction onlyReactElement(\n  list: Array<React.ReactElement<any>>,\n  child: React.ReactElement | number | string\n): Array<React.ReactElement<any>> {\n  // React children can be \"string\" or \"number\" in this case we ignore them for backwards compat\n  if (typeof child === 'string' || typeof child === 'number') {\n    return list\n  }\n  // Adds support for React.Fragment\n  if (child.type === React.Fragment) {\n    return list.concat(\n      // @ts-expect-error @types/react does not remove fragments but this could also return ReactPortal[]\n      React.Children.toArray(child.props.children).reduce(\n        // @ts-expect-error @types/react does not remove fragments but this could also return ReactPortal[]\n        (\n          fragmentList: Array<React.ReactElement<any>>,\n          fragmentChild: React.ReactElement | number | string\n        ): Array<React.ReactElement<any>> => {\n          if (\n            typeof fragmentChild === 'string' ||\n            typeof fragmentChild === 'number'\n          ) {\n            return fragmentList\n          }\n          return fragmentList.concat(fragmentChild)\n        },\n        []\n      )\n    )\n  }\n  return list.concat(child)\n}\n\nconst METATYPES = ['name', 'httpEquiv', 'charSet', 'itemProp']\n\n/*\n returns a function for filtering head child elements\n which shouldn't be duplicated, like <title/>\n Also adds support for deduplicated `key` properties\n*/\nfunction unique() {\n  const keys = new Set()\n  const tags = new Set()\n  const metaTypes = new Set()\n  const metaCategories: { [metatype: string]: Set<string> } = {}\n\n  return (h: React.ReactElement<any>) => {\n    let isUnique = true\n    let hasKey = false\n\n    if (h.key && typeof h.key !== 'number' && h.key.indexOf('$') > 0) {\n      hasKey = true\n      const key = h.key.slice(h.key.indexOf('$') + 1)\n      if (keys.has(key)) {\n        isUnique = false\n      } else {\n        keys.add(key)\n      }\n    }\n\n    // eslint-disable-next-line default-case\n    switch (h.type) {\n      case 'title':\n      case 'base':\n        if (tags.has(h.type)) {\n          isUnique = false\n        } else {\n          tags.add(h.type)\n        }\n        break\n      case 'meta':\n        for (let i = 0, len = METATYPES.length; i < len; i++) {\n          const metatype = METATYPES[i]\n          if (!h.props.hasOwnProperty(metatype)) continue\n\n          if (metatype === 'charSet') {\n            if (metaTypes.has(metatype)) {\n              isUnique = false\n            } else {\n              metaTypes.add(metatype)\n            }\n          } else {\n            const category = h.props[metatype]\n            const categories = metaCategories[metatype] || new Set()\n            if ((metatype !== 'name' || !hasKey) && categories.has(category)) {\n              isUnique = false\n            } else {\n              categories.add(category)\n              metaCategories[metatype] = categories\n            }\n          }\n        }\n        break\n    }\n\n    return isUnique\n  }\n}\n\n/**\n *\n * @param headChildrenElements List of children of <Head>\n */\nfunction reduceComponents<T extends {} & WithInAmpMode>(\n  headChildrenElements: Array<React.ReactElement<any>>,\n  props: T\n) {\n  const { inAmpMode } = props\n  return headChildrenElements\n    .reduce(onlyReactElement, [])\n    .reverse()\n    .concat(defaultHead(inAmpMode).reverse())\n    .filter(unique())\n    .reverse()\n    .map((c: React.ReactElement<any>, i: number) => {\n      const key = c.key || i\n      if (\n        process.env.NODE_ENV !== 'development' &&\n        process.env.__NEXT_OPTIMIZE_FONTS &&\n        !inAmpMode\n      ) {\n        if (\n          c.type === 'link' &&\n          c.props['href'] &&\n          // TODO(prateekbh@): Replace this with const from `constants` when the tree shaking works.\n          ['https://fonts.googleapis.com/css', 'https://use.typekit.net/'].some(\n            (url) => c.props['href'].startsWith(url)\n          )\n        ) {\n          const newProps = { ...(c.props || {}) }\n          newProps['data-href'] = newProps['href']\n          newProps['href'] = undefined\n\n          // Add this attribute to make it easy to identify optimized tags\n          newProps['data-optimized-fonts'] = true\n\n          return React.cloneElement(c, newProps)\n        }\n      }\n      if (process.env.NODE_ENV === 'development') {\n        // omit JSON-LD structured data snippets from the warning\n        if (c.type === 'script' && c.props['type'] !== 'application/ld+json') {\n          const srcMessage = c.props['src']\n            ? `<script> tag with src=\"${c.props['src']}\"`\n            : `inline <script>`\n          warnOnce(\n            `Do not add <script> tags using next/head (see ${srcMessage}). Use next/script instead. \\nSee more info here: https://nextjs.org/docs/messages/no-script-tags-in-head-component`\n          )\n        } else if (c.type === 'link' && c.props['rel'] === 'stylesheet') {\n          warnOnce(\n            `Do not add stylesheets using next/head (see <link rel=\"stylesheet\"> tag with href=\"${c.props['href']}\"). Use Document instead. \\nSee more info here: https://nextjs.org/docs/messages/no-stylesheets-in-head-component`\n          )\n        }\n      }\n      return React.cloneElement(c, { key })\n    })\n}\n\n/**\n * This component injects elements to `<head>` of your page.\n * To avoid duplicated `tags` in `<head>` you can use the `key` property, which will make sure every tag is only rendered once.\n */\nfunction Head({ children }: { children: React.ReactNode }) {\n  const ampState = useContext(AmpStateContext)\n  const headManager = useContext(HeadManagerContext)\n  return (\n    <Effect\n      reduceComponentsToState={reduceComponents}\n      headManager={headManager}\n      inAmpMode={isInAmpMode(ampState)}\n    >\n      {children}\n    </Effect>\n  )\n}\n\nexport default Head\n", "import type { ImageLoaderPropsWithConfig } from './image-config'\n\nfunction defaultLoader({\n  config,\n  src,\n  width,\n  quality,\n}: ImageLoaderPropsWithConfig): string {\n  if (process.env.NODE_ENV !== 'production') {\n    const missingValues = []\n\n    // these should always be provided but make sure they are\n    if (!src) missingValues.push('src')\n    if (!width) missingValues.push('width')\n\n    if (missingValues.length > 0) {\n      throw new Error(\n        `Next Image Optimization requires ${missingValues.join(\n          ', '\n        )} to be provided. Make sure you pass them as props to the \\`next/image\\` component. Received: ${JSON.stringify(\n          { src, width, quality }\n        )}`\n      )\n    }\n\n    if (src.startsWith('//')) {\n      throw new Error(\n        `Failed to parse src \"${src}\" on \\`next/image\\`, protocol-relative URL (//) must be changed to an absolute URL (http:// or https://)`\n      )\n    }\n\n    if (src.startsWith('/') && config.localPatterns) {\n      if (\n        process.env.NODE_ENV !== 'test' &&\n        // micromatch isn't compatible with edge runtime\n        process.env.NEXT_RUNTIME !== 'edge'\n      ) {\n        // We use dynamic require because this should only error in development\n        const { hasLocalMatch } = require('./match-local-pattern')\n        if (!hasLocalMatch(config.localPatterns, src)) {\n          throw new Error(\n            `Invalid src prop (${src}) on \\`next/image\\` does not match \\`images.localPatterns\\` configured in your \\`next.config.js\\`\\n` +\n              `See more info: https://nextjs.org/docs/messages/next-image-unconfigured-localpatterns`\n          )\n        }\n      }\n    }\n\n    if (!src.startsWith('/') && (config.domains || config.remotePatterns)) {\n      let parsedSrc: URL\n      try {\n        parsedSrc = new URL(src)\n      } catch (err) {\n        console.error(err)\n        throw new Error(\n          `Failed to parse src \"${src}\" on \\`next/image\\`, if using relative image it must start with a leading slash \"/\" or be an absolute URL (http:// or https://)`\n        )\n      }\n\n      if (\n        process.env.NODE_ENV !== 'test' &&\n        // micromatch isn't compatible with edge runtime\n        process.env.NEXT_RUNTIME !== 'edge'\n      ) {\n        // We use dynamic require because this should only error in development\n        const { hasRemoteMatch } = require('./match-remote-pattern')\n        if (!hasRemoteMatch(config.domains, config.remotePatterns, parsedSrc)) {\n          throw new Error(\n            `Invalid src prop (${src}) on \\`next/image\\`, hostname \"${parsedSrc.hostname}\" is not configured under images in your \\`next.config.js\\`\\n` +\n              `See more info: https://nextjs.org/docs/messages/next-image-unconfigured-host`\n          )\n        }\n      }\n    }\n  }\n\n  return `${config.path}?url=${encodeURIComponent(src)}&w=${width}&q=${\n    quality || 75\n  }${\n    src.startsWith('/_next/static/media/') && process.env.NEXT_DEPLOYMENT_ID\n      ? `&dpl=${process.env.NEXT_DEPLOYMENT_ID}`\n      : ''\n  }`\n}\n\n// We use this to determine if the import is the default loader\n// or a custom loader defined by the user in next.config.js\ndefaultLoader.__next_img_default = true\n\nexport default defaultLoader\n", "'use client'\n\nimport React, {\n  useRef,\n  useEffect,\n  useCallback,\n  useContext,\n  useMemo,\n  useState,\n  forwardRef,\n  use,\n} from 'react'\nimport ReactDOM from 'react-dom'\nimport Head from '../shared/lib/head'\nimport { getImgProps } from '../shared/lib/get-img-props'\nimport type {\n  ImageProps,\n  ImgProps,\n  OnLoad,\n  OnLoadingComplete,\n  PlaceholderValue,\n} from '../shared/lib/get-img-props'\nimport type {\n  ImageConfigComplete,\n  ImageLoaderProps,\n} from '../shared/lib/image-config'\nimport { imageConfigDefault } from '../shared/lib/image-config'\nimport { ImageConfigContext } from '../shared/lib/image-config-context.shared-runtime'\nimport { warnOnce } from '../shared/lib/utils/warn-once'\nimport { RouterContext } from '../shared/lib/router-context.shared-runtime'\n\n// @ts-ignore - This is replaced by webpack alias\nimport defaultLoader from 'next/dist/shared/lib/image-loader'\nimport { useMergedRef } from './use-merged-ref'\n\n// This is replaced by webpack define plugin\nconst configEnv = process.env.__NEXT_IMAGE_OPTS as any as ImageConfigComplete\n\nif (typeof window === 'undefined') {\n  ;(globalThis as any).__NEXT_IMAGE_IMPORTED = true\n}\n\nexport type { ImageLoaderProps }\nexport type ImageLoader = (p: ImageLoaderProps) => string\n\ntype ImgElementWithDataProp = HTMLImageElement & {\n  'data-loaded-src': string | undefined\n}\n\ntype ImageElementProps = ImgProps & {\n  unoptimized: boolean\n  placeholder: PlaceholderValue\n  onLoadRef: React.MutableRefObject<OnLoad | undefined>\n  onLoadingCompleteRef: React.MutableRefObject<OnLoadingComplete | undefined>\n  setBlurComplete: (b: boolean) => void\n  setShowAltText: (b: boolean) => void\n  sizesInput: string | undefined\n}\n\n// See https://stackoverflow.com/q/39777833/266535 for why we use this ref\n// handler instead of the img's onLoad attribute.\nfunction handleLoading(\n  img: ImgElementWithDataProp,\n  placeholder: PlaceholderValue,\n  onLoadRef: React.MutableRefObject<OnLoad | undefined>,\n  onLoadingCompleteRef: React.MutableRefObject<OnLoadingComplete | undefined>,\n  setBlurComplete: (b: boolean) => void,\n  unoptimized: boolean,\n  sizesInput: string | undefined\n) {\n  const src = img?.src\n  if (!img || img['data-loaded-src'] === src) {\n    return\n  }\n  img['data-loaded-src'] = src\n  const p = 'decode' in img ? img.decode() : Promise.resolve()\n  p.catch(() => {}).then(() => {\n    if (!img.parentElement || !img.isConnected) {\n      // Exit early in case of race condition:\n      // - onload() is called\n      // - decode() is called but incomplete\n      // - unmount is called\n      // - decode() completes\n      return\n    }\n    if (placeholder !== 'empty') {\n      setBlurComplete(true)\n    }\n    if (onLoadRef?.current) {\n      // Since we don't have the SyntheticEvent here,\n      // we must create one with the same shape.\n      // See https://reactjs.org/docs/events.html\n      const event = new Event('load')\n      Object.defineProperty(event, 'target', { writable: false, value: img })\n      let prevented = false\n      let stopped = false\n      onLoadRef.current({\n        ...event,\n        nativeEvent: event,\n        currentTarget: img,\n        target: img,\n        isDefaultPrevented: () => prevented,\n        isPropagationStopped: () => stopped,\n        persist: () => {},\n        preventDefault: () => {\n          prevented = true\n          event.preventDefault()\n        },\n        stopPropagation: () => {\n          stopped = true\n          event.stopPropagation()\n        },\n      })\n    }\n    if (onLoadingCompleteRef?.current) {\n      onLoadingCompleteRef.current(img)\n    }\n    if (process.env.NODE_ENV !== 'production') {\n      const origSrc = new URL(src, 'http://n').searchParams.get('url') || src\n      if (img.getAttribute('data-nimg') === 'fill') {\n        if (!unoptimized && (!sizesInput || sizesInput === '100vw')) {\n          let widthViewportRatio =\n            img.getBoundingClientRect().width / window.innerWidth\n          if (widthViewportRatio < 0.6) {\n            if (sizesInput === '100vw') {\n              warnOnce(\n                `Image with src \"${origSrc}\" has \"fill\" prop and \"sizes\" prop of \"100vw\", but image is not rendered at full viewport width. Please adjust \"sizes\" to improve page performance. Read more: https://nextjs.org/docs/api-reference/next/image#sizes`\n              )\n            } else {\n              warnOnce(\n                `Image with src \"${origSrc}\" has \"fill\" but is missing \"sizes\" prop. Please add it to improve page performance. Read more: https://nextjs.org/docs/api-reference/next/image#sizes`\n              )\n            }\n          }\n        }\n        if (img.parentElement) {\n          const { position } = window.getComputedStyle(img.parentElement)\n          const valid = ['absolute', 'fixed', 'relative']\n          if (!valid.includes(position)) {\n            warnOnce(\n              `Image with src \"${origSrc}\" has \"fill\" and parent element with invalid \"position\". Provided \"${position}\" should be one of ${valid\n                .map(String)\n                .join(',')}.`\n            )\n          }\n        }\n        if (img.height === 0) {\n          warnOnce(\n            `Image with src \"${origSrc}\" has \"fill\" and a height value of 0. This is likely because the parent element of the image has not been styled to have a set height.`\n          )\n        }\n      }\n\n      const heightModified =\n        img.height.toString() !== img.getAttribute('height')\n      const widthModified = img.width.toString() !== img.getAttribute('width')\n      if (\n        (heightModified && !widthModified) ||\n        (!heightModified && widthModified)\n      ) {\n        warnOnce(\n          `Image with src \"${origSrc}\" has either width or height modified, but not the other. If you use CSS to change the size of your image, also include the styles 'width: \"auto\"' or 'height: \"auto\"' to maintain the aspect ratio.`\n        )\n      }\n    }\n  })\n}\n\nfunction getDynamicProps(\n  fetchPriority?: string\n): Record<string, string | undefined> {\n  if (Boolean(use)) {\n    // In React 19.0.0 or newer, we must use camelCase\n    // prop to avoid \"Warning: Invalid DOM property\".\n    // See https://github.com/facebook/react/pull/25927\n    return { fetchPriority }\n  }\n  // In React 18.2.0 or older, we must use lowercase prop\n  // to avoid \"Warning: Invalid DOM property\".\n  return { fetchpriority: fetchPriority }\n}\n\nconst ImageElement = forwardRef<HTMLImageElement | null, ImageElementProps>(\n  (\n    {\n      src,\n      srcSet,\n      sizes,\n      height,\n      width,\n      decoding,\n      className,\n      style,\n      fetchPriority,\n      placeholder,\n      loading,\n      unoptimized,\n      fill,\n      onLoadRef,\n      onLoadingCompleteRef,\n      setBlurComplete,\n      setShowAltText,\n      sizesInput,\n      onLoad,\n      onError,\n      ...rest\n    },\n    forwardedRef\n  ) => {\n    const ownRef = useCallback(\n      (img: ImgElementWithDataProp | null) => {\n        if (!img) {\n          return\n        }\n        if (onError) {\n          // If the image has an error before react hydrates, then the error is lost.\n          // The workaround is to wait until the image is mounted which is after hydration,\n          // then we set the src again to trigger the error handler (if there was an error).\n          // eslint-disable-next-line no-self-assign\n          img.src = img.src\n        }\n        if (process.env.NODE_ENV !== 'production') {\n          if (!src) {\n            console.error(`Image is missing required \"src\" property:`, img)\n          }\n          if (img.getAttribute('alt') === null) {\n            console.error(\n              `Image is missing required \"alt\" property. Please add Alternative Text to describe the image for screen readers and search engines.`\n            )\n          }\n        }\n        if (img.complete) {\n          handleLoading(\n            img,\n            placeholder,\n            onLoadRef,\n            onLoadingCompleteRef,\n            setBlurComplete,\n            unoptimized,\n            sizesInput\n          )\n        }\n      },\n      [\n        src,\n        placeholder,\n        onLoadRef,\n        onLoadingCompleteRef,\n        setBlurComplete,\n        onError,\n        unoptimized,\n        sizesInput,\n      ]\n    )\n\n    const ref = useMergedRef(forwardedRef, ownRef)\n\n    return (\n      <img\n        {...rest}\n        {...getDynamicProps(fetchPriority)}\n        // It's intended to keep `loading` before `src` because React updates\n        // props in order which causes Safari/Firefox to not lazy load properly.\n        // See https://github.com/facebook/react/issues/25883\n        loading={loading}\n        width={width}\n        height={height}\n        decoding={decoding}\n        data-nimg={fill ? 'fill' : '1'}\n        className={className}\n        style={style}\n        // It's intended to keep `src` the last attribute because React updates\n        // attributes in order. If we keep `src` the first one, Safari will\n        // immediately start to fetch `src`, before `sizes` and `srcSet` are even\n        // updated by React. That causes multiple unnecessary requests if `srcSet`\n        // and `sizes` are defined.\n        // This bug cannot be reproduced in Chrome or Firefox.\n        sizes={sizes}\n        srcSet={srcSet}\n        src={src}\n        ref={ref}\n        onLoad={(event) => {\n          const img = event.currentTarget as ImgElementWithDataProp\n          handleLoading(\n            img,\n            placeholder,\n            onLoadRef,\n            onLoadingCompleteRef,\n            setBlurComplete,\n            unoptimized,\n            sizesInput\n          )\n        }}\n        onError={(event) => {\n          // if the real image fails to load, this will ensure \"alt\" is visible\n          setShowAltText(true)\n          if (placeholder !== 'empty') {\n            // If the real image fails to load, this will still remove the placeholder.\n            setBlurComplete(true)\n          }\n          if (onError) {\n            onError(event)\n          }\n        }}\n      />\n    )\n  }\n)\n\nfunction ImagePreload({\n  isAppRouter,\n  imgAttributes,\n}: {\n  isAppRouter: boolean\n  imgAttributes: ImgProps\n}) {\n  const opts = {\n    as: 'image',\n    imageSrcSet: imgAttributes.srcSet,\n    imageSizes: imgAttributes.sizes,\n    crossOrigin: imgAttributes.crossOrigin,\n    referrerPolicy: imgAttributes.referrerPolicy,\n    ...getDynamicProps(imgAttributes.fetchPriority),\n  }\n\n  if (isAppRouter && ReactDOM.preload) {\n    // See https://github.com/facebook/react/pull/26940\n    ReactDOM.preload(\n      imgAttributes.src,\n      // @ts-expect-error TODO: upgrade to `@types/react-dom@18.3.x`\n      opts\n    )\n    return null\n  }\n\n  return (\n    <Head>\n      <link\n        key={\n          '__nimg-' +\n          imgAttributes.src +\n          imgAttributes.srcSet +\n          imgAttributes.sizes\n        }\n        rel=\"preload\"\n        // Note how we omit the `href` attribute, as it would only be relevant\n        // for browsers that do not support `imagesrcset`, and in those cases\n        // it would cause the incorrect image to be preloaded.\n        //\n        // https://html.spec.whatwg.org/multipage/semantics.html#attr-link-imagesrcset\n        href={imgAttributes.srcSet ? undefined : imgAttributes.src}\n        {...opts}\n      />\n    </Head>\n  )\n}\n\n/**\n * The `Image` component is used to optimize images.\n *\n * Read more: [Next.js docs: `Image`](https://nextjs.org/docs/app/api-reference/components/image)\n */\nexport const Image = forwardRef<HTMLImageElement | null, ImageProps>(\n  (props, forwardedRef) => {\n    const pagesRouter = useContext(RouterContext)\n    // We're in the app directory if there is no pages router.\n    const isAppRouter = !pagesRouter\n\n    const configContext = useContext(ImageConfigContext)\n    const config = useMemo(() => {\n      const c = configEnv || configContext || imageConfigDefault\n      const allSizes = [...c.deviceSizes, ...c.imageSizes].sort((a, b) => a - b)\n      const deviceSizes = c.deviceSizes.sort((a, b) => a - b)\n      return { ...c, allSizes, deviceSizes }\n    }, [configContext])\n\n    const { onLoad, onLoadingComplete } = props\n    const onLoadRef = useRef(onLoad)\n\n    useEffect(() => {\n      onLoadRef.current = onLoad\n    }, [onLoad])\n\n    const onLoadingCompleteRef = useRef(onLoadingComplete)\n\n    useEffect(() => {\n      onLoadingCompleteRef.current = onLoadingComplete\n    }, [onLoadingComplete])\n\n    const [blurComplete, setBlurComplete] = useState(false)\n    const [showAltText, setShowAltText] = useState(false)\n\n    const { props: imgAttributes, meta: imgMeta } = getImgProps(props, {\n      defaultLoader,\n      imgConf: config,\n      blurComplete,\n      showAltText,\n    })\n\n    return (\n      <>\n        {\n          <ImageElement\n            {...imgAttributes}\n            unoptimized={imgMeta.unoptimized}\n            placeholder={imgMeta.placeholder}\n            fill={imgMeta.fill}\n            onLoadRef={onLoadRef}\n            onLoadingCompleteRef={onLoadingCompleteRef}\n            setBlurComplete={setBlurComplete}\n            setShowAltText={setShowAltText}\n            sizesInput={props.sizes}\n            ref={forwardedRef}\n          />\n        }\n        {imgMeta.priority ? (\n          <ImagePreload\n            isAppRouter={isAppRouter}\n            imgAttributes={imgAttributes}\n          />\n        ) : null}\n      </>\n    )\n  }\n)\n", "import type { ImageConfigComplete, ImageLoaderProps } from './image-config'\nimport type { ImageProps, ImageLoader, StaticImageData } from './get-img-props'\n\nimport { getImgProps } from './get-img-props'\nimport { Image } from '../../client/image-component'\n\n// @ts-ignore - This is replaced by webpack alias\nimport defaultLoader from 'next/dist/shared/lib/image-loader'\n\n/**\n * For more advanced use cases, you can call `getImageProps()`\n * to get the props that would be passed to the underlying `<img>` element,\n * and instead pass to them to another component, style, canvas, etc.\n *\n * Read more: [Next.js docs: `getImageProps`](https://nextjs.org/docs/app/api-reference/components/image#getimageprops)\n */\nexport function getImageProps(imgProps: ImageProps) {\n  const { props } = getImgProps(imgProps, {\n    defaultLoader,\n    // This is replaced by webpack define plugin\n    imgConf: process.env.__NEXT_IMAGE_OPTS as any as ImageConfigComplete,\n  })\n  // Normally we don't care about undefined props because we pass to JSX,\n  // but this exported function could be used by the end user for anything\n  // so we delete undefined props to clean it up a little.\n  for (const [key, value] of Object.entries(props)) {\n    if (value === undefined) {\n      delete props[key as keyof typeof props]\n    }\n  }\n  return { props }\n}\n\nexport default Image\n\nexport type { ImageProps, ImageLoaderProps, ImageLoader, StaticImageData }\n", "export { default } from '../shared/lib/image-external';\nexport * from '../shared/lib/image-external';\n\n//# sourceMappingURL=image.js.map", "export * from '../client/components/navigation';\n\n//# sourceMappingURL=navigation.js.map", "// Format function modified from nodejs\n// Copyright Joyent, Inc. and other Node contributors.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a\n// copy of this software and associated documentation files (the\n// \"Software\"), to deal in the Software without restriction, including\n// without limitation the rights to use, copy, modify, merge, publish,\n// distribute, sublicense, and/or sell copies of the Software, and to permit\n// persons to whom the Software is furnished to do so, subject to the\n// following conditions:\n//\n// The above copyright notice and this permission notice shall be included\n// in all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS\n// OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n// MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN\n// NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,\n// DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR\n// OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE\n// USE OR OTHER DEALINGS IN THE SOFTWARE.\n\nimport type { UrlObject } from 'url'\nimport type { ParsedUrlQuery } from 'querystring'\nimport * as querystring from './querystring'\n\nconst slashedProtocols = /https?|ftp|gopher|file/\n\nexport function formatUrl(urlObj: UrlObject) {\n  let { auth, hostname } = urlObj\n  let protocol = urlObj.protocol || ''\n  let pathname = urlObj.pathname || ''\n  let hash = urlObj.hash || ''\n  let query = urlObj.query || ''\n  let host: string | false = false\n\n  auth = auth ? encodeURIComponent(auth).replace(/%3A/i, ':') + '@' : ''\n\n  if (urlObj.host) {\n    host = auth + urlObj.host\n  } else if (hostname) {\n    host = auth + (~hostname.indexOf(':') ? `[${hostname}]` : hostname)\n    if (urlObj.port) {\n      host += ':' + urlObj.port\n    }\n  }\n\n  if (query && typeof query === 'object') {\n    query = String(querystring.urlQueryToSearchParams(query as ParsedUrlQuery))\n  }\n\n  let search = urlObj.search || (query && `?${query}`) || ''\n\n  if (protocol && !protocol.endsWith(':')) protocol += ':'\n\n  if (\n    urlObj.slashes ||\n    ((!protocol || slashedProtocols.test(protocol)) && host !== false)\n  ) {\n    host = '//' + (host || '')\n    if (pathname && pathname[0] !== '/') pathname = '/' + pathname\n  } else if (!host) {\n    host = ''\n  }\n\n  if (hash && hash[0] !== '#') hash = '#' + hash\n  if (search && search[0] !== '?') search = '?' + search\n\n  pathname = pathname.replace(/[?#]/g, encodeURIComponent)\n  search = search.replace('#', '%23')\n\n  return `${protocol}${host}${pathname}${search}${hash}`\n}\n\nexport const urlObjectKeys = [\n  'auth',\n  'hash',\n  'host',\n  'hostname',\n  'href',\n  'path',\n  'pathname',\n  'port',\n  'protocol',\n  'query',\n  'search',\n  'slashes',\n]\n\nexport function formatWithValidation(url: UrlObject): string {\n  if (process.env.NODE_ENV === 'development') {\n    if (url !== null && typeof url === 'object') {\n      Object.keys(url).forEach((key) => {\n        if (!urlObjectKeys.includes(key)) {\n          console.warn(\n            `Unknown key passed via urlObject into url.format: ${key}`\n          )\n        }\n      })\n    }\n  }\n\n  return formatUrl(url)\n}\n", "export const requestIdleCallback =\n  (typeof self !== 'undefined' &&\n    self.requestIdleCallback &&\n    self.requestIdleCallback.bind(window)) ||\n  function (cb: IdleRequestCallback): number {\n    let start = Date.now()\n    return self.setTimeout(function () {\n      cb({\n        didTimeout: false,\n        timeRemaining: function () {\n          return Math.max(0, 50 - (Date.now() - start))\n        },\n      })\n    }, 1)\n  }\n\nexport const cancelIdleCallback =\n  (typeof self !== 'undefined' &&\n    self.cancelIdleCallback &&\n    self.cancelIdleCallback.bind(window)) ||\n  function (id: number) {\n    return clearTimeout(id)\n  }\n", "import { useCallback, useEffect, useRef, useState } from 'react'\nimport {\n  requestIdleCallback,\n  cancelIdleCallback,\n} from './request-idle-callback'\n\ntype UseIntersectionObserverInit = Pick<\n  IntersectionObserverInit,\n  'rootMargin' | 'root'\n>\n\ntype UseIntersection = { disabled?: boolean } & UseIntersectionObserverInit & {\n    rootRef?: React.RefObject<HTMLElement | null> | null\n  }\ntype ObserveCallback = (isVisible: boolean) => void\ntype Identifier = {\n  root: Element | Document | null\n  margin: string\n}\ntype Observer = {\n  id: Identifier\n  observer: IntersectionObserver\n  elements: Map<Element, ObserveCallback>\n}\n\nconst hasIntersectionObserver = typeof IntersectionObserver === 'function'\n\nconst observers = new Map<Identifier, Observer>()\nconst idList: Identifier[] = []\n\nfunction createObserver(options: UseIntersectionObserverInit): Observer {\n  const id = {\n    root: options.root || null,\n    margin: options.rootMargin || '',\n  }\n  const existing = idList.find(\n    (obj) => obj.root === id.root && obj.margin === id.margin\n  )\n  let instance: Observer | undefined\n\n  if (existing) {\n    instance = observers.get(existing)\n    if (instance) {\n      return instance\n    }\n  }\n\n  const elements = new Map<Element, ObserveCallback>()\n  const observer = new IntersectionObserver((entries) => {\n    entries.forEach((entry) => {\n      const callback = elements.get(entry.target)\n      const isVisible = entry.isIntersecting || entry.intersectionRatio > 0\n      if (callback && isVisible) {\n        callback(isVisible)\n      }\n    })\n  }, options)\n  instance = {\n    id,\n    observer,\n    elements,\n  }\n\n  idList.push(id)\n  observers.set(id, instance)\n  return instance\n}\n\nfunction observe(\n  element: Element,\n  callback: ObserveCallback,\n  options: UseIntersectionObserverInit\n): () => void {\n  const { id, observer, elements } = createObserver(options)\n  elements.set(element, callback)\n\n  observer.observe(element)\n  return function unobserve(): void {\n    elements.delete(element)\n    observer.unobserve(element)\n\n    // Destroy observer when there's nothing left to watch:\n    if (elements.size === 0) {\n      observer.disconnect()\n      observers.delete(id)\n      const index = idList.findIndex(\n        (obj) => obj.root === id.root && obj.margin === id.margin\n      )\n      if (index > -1) {\n        idList.splice(index, 1)\n      }\n    }\n  }\n}\n\nexport function useIntersection<T extends Element>({\n  rootRef,\n  rootMargin,\n  disabled,\n}: UseIntersection): [(element: T | null) => void, boolean, () => void] {\n  const isDisabled: boolean = disabled || !hasIntersectionObserver\n\n  const [visible, setVisible] = useState(false)\n  const elementRef = useRef<T | null>(null)\n  const setElement = useCallback((element: T | null) => {\n    elementRef.current = element\n  }, [])\n\n  useEffect(() => {\n    if (hasIntersectionObserver) {\n      if (isDisabled || visible) return\n\n      const element = elementRef.current\n      if (element && element.tagName) {\n        const unobserve = observe(\n          element,\n          (isVisible) => isVisible && setVisible(isVisible),\n          { root: rootRef?.current, rootMargin }\n        )\n\n        return unobserve\n      }\n    } else {\n      if (!visible) {\n        const idleCallback = requestIdleCallback(() => setVisible(true))\n        return () => cancelIdleCallback(idleCallback)\n      }\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [isDisabled, rootMargin, rootRef, visible, elementRef.current])\n\n  const resetVisible = useCallback(() => {\n    setVisible(false)\n  }, [])\n\n  return [setElement, visible, resetVisible]\n}\n", "'use client'\n\nimport type { NextRouter } from '../../shared/lib/router/router'\n\nimport React from 'react'\nimport type { UrlObject } from 'url'\nimport { formatUrl } from '../../shared/lib/router/utils/format-url'\nimport { AppRouterContext } from '../../shared/lib/app-router-context.shared-runtime'\nimport type { AppRouterInstance } from '../../shared/lib/app-router-context.shared-runtime'\nimport type { PrefetchOptions } from '../../shared/lib/app-router-context.shared-runtime'\nimport { useIntersection } from '../use-intersection'\nimport { PrefetchKind } from '../components/router-reducer/router-reducer-types'\nimport { useMergedRef } from '../use-merged-ref'\nimport { isAbsoluteUrl } from '../../shared/lib/utils'\nimport { addBasePath } from '../add-base-path'\nimport { warnOnce } from '../../shared/lib/utils/warn-once'\n\ntype Url = string | UrlObject\ntype RequiredKeys<T> = {\n  [K in keyof T]-?: {} extends Pick<T, K> ? never : K\n}[keyof T]\ntype OptionalKeys<T> = {\n  [K in keyof T]-?: {} extends Pick<T, K> ? K : never\n}[keyof T]\n\ntype InternalLinkProps = {\n  /**\n   * The path or URL to navigate to. It can also be an object.\n   *\n   * @example https://nextjs.org/docs/api-reference/next/link#with-url-object\n   */\n  href: Url\n  /**\n   * Optional decorator for the path that will be shown in the browser URL bar. Before Next.js 9.5.3 this was used for dynamic routes, check our [previous docs](https://github.com/vercel/next.js/blob/v9.5.2/docs/api-reference/next/link.md#dynamic-routes) to see how it worked. Note: when this path differs from the one provided in `href` the previous `href`/`as` behavior is used as shown in the [previous docs](https://github.com/vercel/next.js/blob/v9.5.2/docs/api-reference/next/link.md#dynamic-routes).\n   */\n  as?: Url\n  /**\n   * Replace the current `history` state instead of adding a new url into the stack.\n   *\n   * @defaultValue `false`\n   */\n  replace?: boolean\n  /**\n   * Whether to override the default scroll behavior\n   *\n   * @example https://nextjs.org/docs/api-reference/next/link#disable-scrolling-to-the-top-of-the-page\n   *\n   * @defaultValue `true`\n   */\n  scroll?: boolean\n  /**\n   * Update the path of the current page without rerunning [`getStaticProps`](https://nextjs.org/docs/pages/building-your-application/data-fetching/get-static-props), [`getServerSideProps`](https://nextjs.org/docs/pages/building-your-application/data-fetching/get-server-side-props) or [`getInitialProps`](/docs/pages/api-reference/functions/get-initial-props).\n   *\n   * @defaultValue `false`\n   */\n  shallow?: boolean\n  /**\n   * Forces `Link` to send the `href` property to its child.\n   *\n   * @defaultValue `false`\n   */\n  passHref?: boolean\n  /**\n   * Prefetch the page in the background.\n   * Any `<Link />` that is in the viewport (initially or through scroll) will be prefetched.\n   * Prefetch can be disabled by passing `prefetch={false}`. Prefetching is only enabled in production.\n   *\n   * In App Router:\n   * - `null` (default): For statically generated pages, this will prefetch the full React Server Component data. For dynamic pages, this will prefetch up to the nearest route segment with a [`loading.js`](https://nextjs.org/docs/app/api-reference/file-conventions/loading) file. If there is no loading file, it will not fetch the full tree to avoid fetching too much data.\n   * - `true`: This will prefetch the full React Server Component data for all route segments, regardless of whether they contain a segment with `loading.js`.\n   * - `false`: This will not prefetch any data, even on hover.\n   *\n   * In Pages Router:\n   * - `true` (default): The full route & its data will be prefetched.\n   * - `false`: Prefetching will not happen when entering the viewport, but will still happen on hover.\n   * @defaultValue `true` (pages router) or `null` (app router)\n   */\n  prefetch?: boolean | null\n  /**\n   * The active locale is automatically prepended. `locale` allows for providing a different locale.\n   * When `false` `href` has to include the locale as the default behavior is disabled.\n   * Note: This is only available in the Pages Router.\n   */\n  locale?: string | false\n  /**\n   * Enable legacy link behavior.\n   * @defaultValue `false`\n   * @see https://github.com/vercel/next.js/commit/489e65ed98544e69b0afd7e0cfc3f9f6c2b803b7\n   */\n  legacyBehavior?: boolean\n  /**\n   * Optional event handler for when the mouse pointer is moved onto Link\n   */\n  onMouseEnter?: React.MouseEventHandler<HTMLAnchorElement>\n  /**\n   * Optional event handler for when Link is touched.\n   */\n  onTouchStart?: React.TouchEventHandler<HTMLAnchorElement>\n  /**\n   * Optional event handler for when Link is clicked.\n   */\n  onClick?: React.MouseEventHandler<HTMLAnchorElement>\n}\n\n// TODO-APP: Include the full set of Anchor props\n// adding this to the publicly exported type currently breaks existing apps\n\n// `RouteInferType` is a stub here to avoid breaking `typedRoutes` when the type\n// isn't generated yet. It will be replaced when the webpack plugin runs.\n// eslint-disable-next-line @typescript-eslint/no-unused-vars\nexport type LinkProps<RouteInferType = any> = InternalLinkProps\ntype LinkPropsRequired = RequiredKeys<LinkProps>\ntype LinkPropsOptional = OptionalKeys<Omit<InternalLinkProps, 'locale'>>\n\nfunction prefetch(\n  router: AppRouterInstance,\n  href: string,\n  options: PrefetchOptions\n): void {\n  if (typeof window === 'undefined') {\n    return\n  }\n\n  const doPrefetch = async () => {\n    // note that `appRouter.prefetch()` is currently sync,\n    // so we have to wrap this call in an async function to be able to catch() errors below.\n    return router.prefetch(href, options)\n  }\n\n  // Prefetch the page if asked (only in the client)\n  // We need to handle a prefetch error here since we may be\n  // loading with priority which can reject but we don't\n  // want to force navigation since this is only a prefetch\n  doPrefetch().catch((err) => {\n    if (process.env.NODE_ENV !== 'production') {\n      // rethrow to show invalid URL errors\n      throw err\n    }\n  })\n}\n\nfunction isModifiedEvent(event: React.MouseEvent): boolean {\n  const eventTarget = event.currentTarget as HTMLAnchorElement | SVGAElement\n  const target = eventTarget.getAttribute('target')\n  return (\n    (target && target !== '_self') ||\n    event.metaKey ||\n    event.ctrlKey ||\n    event.shiftKey ||\n    event.altKey || // triggers resource download\n    (event.nativeEvent && event.nativeEvent.which === 2)\n  )\n}\n\nfunction linkClicked(\n  e: React.MouseEvent,\n  router: NextRouter | AppRouterInstance,\n  href: string,\n  as: string,\n  replace?: boolean,\n  shallow?: boolean,\n  scroll?: boolean\n): void {\n  const { nodeName } = e.currentTarget\n\n  // anchors inside an svg have a lowercase nodeName\n  const isAnchorNodeName = nodeName.toUpperCase() === 'A'\n\n  if (isAnchorNodeName && isModifiedEvent(e)) {\n    // ignore click for browser’s default behavior\n    return\n  }\n\n  e.preventDefault()\n\n  const navigate = () => {\n    // If the router is an NextRouter instance it will have `beforePopState`\n    const routerScroll = scroll ?? true\n    if ('beforePopState' in router) {\n      router[replace ? 'replace' : 'push'](href, as, {\n        shallow,\n        scroll: routerScroll,\n      })\n    } else {\n      router[replace ? 'replace' : 'push'](as || href, {\n        scroll: routerScroll,\n      })\n    }\n  }\n\n  React.startTransition(navigate)\n}\n\ntype LinkPropsReal = React.PropsWithChildren<\n  Omit<React.AnchorHTMLAttributes<HTMLAnchorElement>, keyof LinkProps> &\n    LinkProps\n>\n\nfunction formatStringOrUrl(urlObjOrString: UrlObject | string): string {\n  if (typeof urlObjOrString === 'string') {\n    return urlObjOrString\n  }\n\n  return formatUrl(urlObjOrString)\n}\n\n/**\n * A React component that extends the HTML `<a>` element to provide [prefetching](https://nextjs.org/docs/app/building-your-application/routing/linking-and-navigating#2-prefetching)\n * and client-side navigation between routes.\n *\n * It is the primary way to navigate between routes in Next.js.\n *\n * Read more: [Next.js docs: `<Link>`](https://nextjs.org/docs/app/api-reference/components/link)\n */\nconst Link = React.forwardRef<HTMLAnchorElement, LinkPropsReal>(\n  function LinkComponent(props, forwardedRef) {\n    let children: React.ReactNode\n\n    const {\n      href: hrefProp,\n      as: asProp,\n      children: childrenProp,\n      prefetch: prefetchProp = null,\n      passHref,\n      replace,\n      shallow,\n      scroll,\n      onClick,\n      onMouseEnter: onMouseEnterProp,\n      onTouchStart: onTouchStartProp,\n      legacyBehavior = false,\n      ...restProps\n    } = props\n\n    children = childrenProp\n\n    if (\n      legacyBehavior &&\n      (typeof children === 'string' || typeof children === 'number')\n    ) {\n      children = <a>{children}</a>\n    }\n\n    const router = React.useContext(AppRouterContext)\n\n    const prefetchEnabled = prefetchProp !== false\n    /**\n     * The possible states for prefetch are:\n     * - null: this is the default \"auto\" mode, where we will prefetch partially if the link is in the viewport\n     * - true: we will prefetch if the link is visible and prefetch the full page, not just partially\n     * - false: we will not prefetch if in the viewport at all\n     */\n    const appPrefetchKind =\n      prefetchProp === null ? PrefetchKind.AUTO : PrefetchKind.FULL\n\n    if (process.env.NODE_ENV !== 'production') {\n      function createPropError(args: {\n        key: string\n        expected: string\n        actual: string\n      }) {\n        return new Error(\n          `Failed prop type: The prop \\`${args.key}\\` expects a ${args.expected} in \\`<Link>\\`, but got \\`${args.actual}\\` instead.` +\n            (typeof window !== 'undefined'\n              ? \"\\nOpen your browser's console to view the Component stack trace.\"\n              : '')\n        )\n      }\n\n      // TypeScript trick for type-guarding:\n      const requiredPropsGuard: Record<LinkPropsRequired, true> = {\n        href: true,\n      } as const\n      const requiredProps: LinkPropsRequired[] = Object.keys(\n        requiredPropsGuard\n      ) as LinkPropsRequired[]\n      requiredProps.forEach((key: LinkPropsRequired) => {\n        if (key === 'href') {\n          if (\n            props[key] == null ||\n            (typeof props[key] !== 'string' && typeof props[key] !== 'object')\n          ) {\n            throw createPropError({\n              key,\n              expected: '`string` or `object`',\n              actual: props[key] === null ? 'null' : typeof props[key],\n            })\n          }\n        } else {\n          // TypeScript trick for type-guarding:\n          // eslint-disable-next-line @typescript-eslint/no-unused-vars\n          const _: never = key\n        }\n      })\n\n      // TypeScript trick for type-guarding:\n      const optionalPropsGuard: Record<LinkPropsOptional, true> = {\n        as: true,\n        replace: true,\n        scroll: true,\n        shallow: true,\n        passHref: true,\n        prefetch: true,\n        onClick: true,\n        onMouseEnter: true,\n        onTouchStart: true,\n        legacyBehavior: true,\n      } as const\n      const optionalProps: LinkPropsOptional[] = Object.keys(\n        optionalPropsGuard\n      ) as LinkPropsOptional[]\n      optionalProps.forEach((key: LinkPropsOptional) => {\n        const valType = typeof props[key]\n\n        if (key === 'as') {\n          if (props[key] && valType !== 'string' && valType !== 'object') {\n            throw createPropError({\n              key,\n              expected: '`string` or `object`',\n              actual: valType,\n            })\n          }\n        } else if (\n          key === 'onClick' ||\n          key === 'onMouseEnter' ||\n          key === 'onTouchStart'\n        ) {\n          if (props[key] && valType !== 'function') {\n            throw createPropError({\n              key,\n              expected: '`function`',\n              actual: valType,\n            })\n          }\n        } else if (\n          key === 'replace' ||\n          key === 'scroll' ||\n          key === 'shallow' ||\n          key === 'passHref' ||\n          key === 'prefetch' ||\n          key === 'legacyBehavior'\n        ) {\n          if (props[key] != null && valType !== 'boolean') {\n            throw createPropError({\n              key,\n              expected: '`boolean`',\n              actual: valType,\n            })\n          }\n        } else {\n          // TypeScript trick for type-guarding:\n          // eslint-disable-next-line @typescript-eslint/no-unused-vars\n          const _: never = key\n        }\n      })\n    }\n\n    if (process.env.NODE_ENV !== 'production') {\n      if (props.locale) {\n        warnOnce(\n          'The `locale` prop is not supported in `next/link` while using the `app` router. Read more about app router internalization: https://nextjs.org/docs/app/building-your-application/routing/internationalization'\n        )\n      }\n      if (!asProp) {\n        let href: string | undefined\n        if (typeof hrefProp === 'string') {\n          href = hrefProp\n        } else if (\n          typeof hrefProp === 'object' &&\n          typeof hrefProp.pathname === 'string'\n        ) {\n          href = hrefProp.pathname\n        }\n\n        if (href) {\n          const hasDynamicSegment = href\n            .split('/')\n            .some((segment) => segment.startsWith('[') && segment.endsWith(']'))\n\n          if (hasDynamicSegment) {\n            throw new Error(\n              `Dynamic href \\`${href}\\` found in <Link> while using the \\`/app\\` router, this is not supported. Read more: https://nextjs.org/docs/messages/app-dir-dynamic-href`\n            )\n          }\n        }\n      }\n    }\n\n    const { href, as } = React.useMemo(() => {\n      const resolvedHref = formatStringOrUrl(hrefProp)\n      return {\n        href: resolvedHref,\n        as: asProp ? formatStringOrUrl(asProp) : resolvedHref,\n      }\n    }, [hrefProp, asProp])\n\n    const previousHref = React.useRef<string>(href)\n    const previousAs = React.useRef<string>(as)\n\n    // This will return the first child, if multiple are provided it will throw an error\n    let child: any\n    if (legacyBehavior) {\n      if (process.env.NODE_ENV === 'development') {\n        if (onClick) {\n          console.warn(\n            `\"onClick\" was passed to <Link> with \\`href\\` of \\`${hrefProp}\\` but \"legacyBehavior\" was set. The legacy behavior requires onClick be set on the child of next/link`\n          )\n        }\n        if (onMouseEnterProp) {\n          console.warn(\n            `\"onMouseEnter\" was passed to <Link> with \\`href\\` of \\`${hrefProp}\\` but \"legacyBehavior\" was set. The legacy behavior requires onMouseEnter be set on the child of next/link`\n          )\n        }\n        try {\n          child = React.Children.only(children)\n        } catch (err) {\n          if (!children) {\n            throw new Error(\n              `No children were passed to <Link> with \\`href\\` of \\`${hrefProp}\\` but one child is required https://nextjs.org/docs/messages/link-no-children`\n            )\n          }\n          throw new Error(\n            `Multiple children were passed to <Link> with \\`href\\` of \\`${hrefProp}\\` but only one child is supported https://nextjs.org/docs/messages/link-multiple-children` +\n              (typeof window !== 'undefined'\n                ? \" \\nOpen your browser's console to view the Component stack trace.\"\n                : '')\n          )\n        }\n      } else {\n        child = React.Children.only(children)\n      }\n    } else {\n      if (process.env.NODE_ENV === 'development') {\n        if ((children as any)?.type === 'a') {\n          throw new Error(\n            'Invalid <Link> with <a> child. Please remove <a> or use <Link legacyBehavior>.\\nLearn more: https://nextjs.org/docs/messages/invalid-new-link-with-extra-anchor'\n          )\n        }\n      }\n    }\n\n    const childRef: any = legacyBehavior\n      ? child && typeof child === 'object' && child.ref\n      : forwardedRef\n\n    const [setIntersectionRef, isVisible, resetVisible] = useIntersection({\n      rootMargin: '200px',\n    })\n\n    const setIntersectionWithResetRef = React.useCallback(\n      (el: Element) => {\n        // Before the link getting observed, check if visible state need to be reset\n        if (previousAs.current !== as || previousHref.current !== href) {\n          resetVisible()\n          previousAs.current = as\n          previousHref.current = href\n        }\n\n        setIntersectionRef(el)\n      },\n      [as, href, resetVisible, setIntersectionRef]\n    )\n\n    const setRef = useMergedRef(setIntersectionWithResetRef, childRef)\n\n    // Prefetch the URL if we haven't already and it's visible.\n    React.useEffect(() => {\n      // in dev, we only prefetch on hover to avoid wasting resources as the prefetch will trigger compiling the page.\n      if (process.env.NODE_ENV !== 'production') {\n        return\n      }\n\n      if (!router) {\n        return\n      }\n\n      // If we don't need to prefetch the URL, don't do prefetch.\n      if (!isVisible || !prefetchEnabled) {\n        return\n      }\n\n      // Prefetch the URL.\n      prefetch(router, href, {\n        kind: appPrefetchKind,\n      })\n    }, [as, href, isVisible, prefetchEnabled, router, appPrefetchKind])\n\n    const childProps: {\n      onTouchStart?: React.TouchEventHandler<HTMLAnchorElement>\n      onMouseEnter: React.MouseEventHandler<HTMLAnchorElement>\n      onClick: React.MouseEventHandler<HTMLAnchorElement>\n      href?: string\n      ref?: any\n    } = {\n      ref: setRef,\n      onClick(e) {\n        if (process.env.NODE_ENV !== 'production') {\n          if (!e) {\n            throw new Error(\n              `Component rendered inside next/link has to pass click event to \"onClick\" prop.`\n            )\n          }\n        }\n\n        if (!legacyBehavior && typeof onClick === 'function') {\n          onClick(e)\n        }\n\n        if (\n          legacyBehavior &&\n          child.props &&\n          typeof child.props.onClick === 'function'\n        ) {\n          child.props.onClick(e)\n        }\n\n        if (!router) {\n          return\n        }\n\n        if (e.defaultPrevented) {\n          return\n        }\n\n        linkClicked(e, router, href, as, replace, shallow, scroll)\n      },\n      onMouseEnter(e) {\n        if (!legacyBehavior && typeof onMouseEnterProp === 'function') {\n          onMouseEnterProp(e)\n        }\n\n        if (\n          legacyBehavior &&\n          child.props &&\n          typeof child.props.onMouseEnter === 'function'\n        ) {\n          child.props.onMouseEnter(e)\n        }\n\n        if (!router) {\n          return\n        }\n\n        if (!prefetchEnabled || process.env.NODE_ENV === 'development') {\n          return\n        }\n\n        prefetch(router, href, {\n          kind: appPrefetchKind,\n        })\n      },\n      onTouchStart: process.env.__NEXT_LINK_NO_TOUCH_START\n        ? undefined\n        : function onTouchStart(e) {\n            if (!legacyBehavior && typeof onTouchStartProp === 'function') {\n              onTouchStartProp(e)\n            }\n\n            if (\n              legacyBehavior &&\n              child.props &&\n              typeof child.props.onTouchStart === 'function'\n            ) {\n              child.props.onTouchStart(e)\n            }\n\n            if (!router) {\n              return\n            }\n\n            if (!prefetchEnabled) {\n              return\n            }\n\n            prefetch(router, href, {\n              kind: appPrefetchKind,\n            })\n          },\n    }\n\n    // If child is an <a> tag and doesn't have a href attribute, or if the 'passHref' property is\n    // defined, we specify the current 'href', so that repetition is not needed by the user.\n    // If the url is absolute, we can bypass the logic to prepend the basePath.\n    if (isAbsoluteUrl(as)) {\n      childProps.href = as\n    } else if (\n      !legacyBehavior ||\n      passHref ||\n      (child.type === 'a' && !('href' in child.props))\n    ) {\n      childProps.href = addBasePath(as)\n    }\n\n    return legacyBehavior ? (\n      React.cloneElement(child, childProps)\n    ) : (\n      <a {...restProps} {...childProps}>\n        {children}\n      </a>\n    )\n  }\n)\n\nexport default Link\n", "import { useMemo, useRef, type Ref } from 'react'\n\n// This is a compatibility hook to support React 18 and 19 refs.\n// In 19, a cleanup function from refs may be returned.\n// In 18, returning a cleanup function creates a warning.\n// Since we take userspace refs, we don't know ahead of time if a cleanup function will be returned.\n// This implements cleanup functions with the old behavior in 18.\n// We know refs are always called alternating with `null` and then `T`.\n// So a call with `null` means we need to call the previous cleanup functions.\nexport function useMergedRef<TElement>(\n  refA: Ref<TElement>,\n  refB: Ref<TElement>\n): Ref<TElement> {\n  const cleanupA = useRef<() => void>(() => {})\n  const cleanupB = useRef<() => void>(() => {})\n\n  return useMemo(() => {\n    if (!refA || !refB) {\n      return refA || refB\n    }\n\n    return (current: TElement | null): void => {\n      if (current === null) {\n        cleanupA.current()\n        cleanupB.current()\n      } else {\n        cleanupA.current = applyRef(refA, current)\n        cleanupB.current = applyRef(refB, current)\n      }\n    }\n  }, [refA, refB])\n}\n\nfunction applyRef<TElement>(\n  refA: NonNullable<Ref<TElement>>,\n  current: TElement\n) {\n  if (typeof refA === 'function') {\n    const cleanup = refA(current)\n    if (typeof cleanup === 'function') {\n      return cleanup\n    } else {\n      return () => refA(null)\n    }\n  } else {\n    refA.current = current\n    return () => {\n      refA.current = null\n    }\n  }\n}\n"], "names": ["isStaticRequire", "src", "undefined", "default", "getInt", "x", "Number", "isFinite", "NaN", "test", "parseInt", "useClientOnlyLayoutEffect", "useClientOnlyEffect", "useLayoutEffect", "useEffect", "SideEffect", "isServer", "props", "headManager", "reduceComponentsToState", "emitChange", "mountedInstances", "headElements", "Children", "toArray", "Array", "from", "filter", "Boolean", "updateHead", "add", "children", "delete", "_pendingUpdate", "onlyReactElement", "list", "child", "type", "React", "concat", "reduce", "fragmentList", "fragmentChild", "METATYPES", "reduceComponents", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "inAmpMode", "reverse", "defaultHead", "head", "meta", "charSet", "push", "name", "content", "unique", "keys", "Set", "tags", "metaTypes", "metaCategories", "isUnique", "<PERSON><PERSON><PERSON>", "h", "key", "indexOf", "slice", "has", "i", "len", "length", "metatype", "hasOwnProperty", "category", "categories", "map", "c", "process", "env", "__NEXT_OPTIMIZE_FONTS", "some", "startsWith", "url", "newProps", "Head", "ampState", "useContext", "AmpStateContext", "HeadManagerContext", "Effect", "isInAmpMode", "ampFirs<PERSON>", "hybrid", "<PERSON><PERSON><PERSON><PERSON>", "defaultLoader", "config", "width", "quality", "path", "encodeURIComponent", "__next_img_default", "configEnv", "handleLoading", "img", "placeholder", "onLoadRef", "onLoadingCompleteRef", "setBlurComplete", "unoptimized", "sizesInput", "p", "decode", "Promise", "resolve", "catch", "then", "parentElement", "current", "event", "Event", "Object", "defineProperty", "writable", "value", "prevented", "stopped", "nativeEvent", "currentTarget", "target", "isDefaultPrevented", "isPropagationStopped", "persist", "preventDefault", "stopPropagation", "getDynamicProps", "fetchPriority", "use", "fetchpriority", "globalThis", "__NEXT_IMAGE_IMPORTED", "ImageElement", "forwardRef", "forwardedRef", "srcSet", "sizes", "height", "decoding", "className", "style", "loading", "fill", "setShowAltText", "onLoad", "onError", "rest", "ownRef", "useCallback", "complete", "ref", "useMergedRef", "data-nimg", "ImagePreload", "isAppRouter", "imgAttributes", "opts", "as", "imageSrcSet", "imageSizes", "crossOrigin", "referrerPolicy", "ReactDOM", "link", "rel", "href", "Image", "pagesRouter", "RouterContext", "configContext", "ImageConfigContext", "useMemo", "imageConfigDefault", "allSizes", "deviceSizes", "sort", "a", "b", "onLoadingComplete", "useRef", "blurComplete", "useState", "showAltText", "imgMeta", "getImgProps", "_state", "blur<PERSON>idth", "blurHeight", "priority", "overrideSrc", "blurDataURL", "layout", "objectFit", "objectPosition", "lazyBoundary", "lazyRoot", "imgConf", "loader", "isDefaultLoader", "Error", "customImageLoader", "_", "obj", "layoutStyle", "intrinsic", "max<PERSON><PERSON><PERSON>", "responsive", "layoutSizes", "staticSrc", "widthInt", "heightInt", "isStaticImageData", "staticImageData", "JSON", "stringify", "ratio", "Math", "round", "isLazy", "dangerouslyAllowSVG", "split", "endsWith", "qualityInt", "imgStyle", "assign", "position", "left", "top", "right", "bottom", "color", "backgroundImage", "getImageBlurSvg", "svgWidth", "svgHeight", "viewBox", "std", "preserveAspectRatio", "backgroundSize", "backgroundPosition", "backgroundRepeat", "generateImgAttrs", "widths", "kind", "getWidths", "viewportWidthRe", "percentSizes", "match", "exec", "smallestRatio", "min", "s", "w", "find", "last", "join", "placeholder<PERSON><PERSON><PERSON>", "slashedProtocols", "self", "requestIdleCallback", "bind", "window", "cb", "cancelIdleCallback", "id", "formatStringOrUrl", "urlObjOrString", "formatUrl", "auth", "hostname", "url<PERSON>bj", "protocol", "pathname", "hash", "query", "host", "replace", "port", "String", "querystring", "search", "slashes", "Link", "LinkComponent", "hrefProp", "asProp", "childrenProp", "prefetch", "prefetchProp", "passHref", "shallow", "scroll", "onClick", "onMouseEnter", "onMouseEnterProp", "onTouchStart", "onTouchStartProp", "legacyBeh<PERSON>or", "restProps", "router", "AppRouterContext", "appPrefetchKind", "PrefetchKind", "AUTO", "FULL", "resolvedHref", "previousHref", "previousAs", "only", "childRef", "setIntersectionRef", "isVisible", "resetVisible", "useIntersection", "rootRef", "rootMargin", "disabled", "visible", "setVisible", "elementRef", "element", "setIntersectionWithResetRef", "el", "setRef", "childProps", "e", "defaultPrevented", "linkClicked", "nodeName", "toUpperCase", "isModifiedEvent", "getAttribute", "metaKey", "ctrl<PERSON>ey", "shift<PERSON>ey", "altKey", "which", "navigate", "routerScroll", "isAbsoluteUrl", "addBasePath", "refA", "refB", "cleanupA", "cleanupB", "applyRef", "cleanup"], "sourceRoot": "", "ignoreList": [12, 13]}