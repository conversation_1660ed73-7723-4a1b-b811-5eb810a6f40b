import { SignJWT, jwtVerify } from 'jose'

// JWT payload types
export interface EmailCredentialPayload {
  emailAddress: string
  emailId: string
  credentialId: string
  iat: number
  // exp字段不存在，邮箱凭证JWT永久有效
}

export interface UserPayload {
  userEmail: string
  userId: string
  exp: number
  iat: number
}

// JWT utilities
export class JWTUtils {
  private static getSecret(): Uint8Array {
    const secret = process.env.JWT_SECRET || process.env.AUTH_SECRET
    if (!secret) {
      throw new Error('JWT_SECRET or AUTH_SECRET environment variable is required')
    }
    return new TextEncoder().encode(secret)
  }

  // 生成邮箱凭证JWT（永久有效，与cloudflare_temp_email保持一致）
  static async signEmailCredential(payload: Omit<EmailCredentialPayload, 'iat'>): Promise<string> {
    const now = Math.floor(Date.now() / 1000)

    return await new SignJWT({
      ...payload,
      iat: now,
      // 不设置exp，使JWT永久有效（与原项目保持一致）
    })
      .setProtectedHeader({ alg: 'HS256' })
      .setIssuedAt(now)
      // 不设置过期时间
      .sign(this.getSecret())
  }

  // 生成用户JWT
  static async signUser(payload: Omit<UserPayload, 'exp' | 'iat'>): Promise<string> {
    const now = Math.floor(Date.now() / 1000)
    const exp = now + (90 * 24 * 60 * 60) // 90 days
    
    return await new SignJWT({
      ...payload,
      exp,
      iat: now,
    })
      .setProtectedHeader({ alg: 'HS256' })
      .setIssuedAt(now)
      .setExpirationTime(exp)
      .sign(this.getSecret())
  }

  // 验证邮箱凭证JWT
  static async verifyEmailCredential(token: string): Promise<EmailCredentialPayload | null> {
    try {
      const { payload } = await jwtVerify(token, this.getSecret())

      // 邮箱凭证JWT永久有效，不检查过期时间
      // 这与cloudflare_temp_email项目保持一致
      return payload as EmailCredentialPayload
    } catch (error) {
      console.error('JWT verification failed:', error)
      return null
    }
  }

  // 验证用户JWT
  static async verifyUser(token: string): Promise<UserPayload | null> {
    try {
      const { payload } = await jwtVerify(token, this.getSecret())
      
      // 检查是否过期
      const now = Math.floor(Date.now() / 1000)
      if (payload.exp && payload.exp < now) {
        return null
      }

      return payload as UserPayload
    } catch (error) {
      console.error('JWT verification failed:', error)
      return null
    }
  }

  // 生成验证码
  static generateVerificationCode(): string {
    return Math.floor(100000 + Math.random() * 900000).toString()
  }
}

// 邮箱凭证相关的工具函数
export class EmailCredentialUtils {
  // 生成邮箱凭证名称
  static generateCredentialName(emailAddress: string): string {
    const timestamp = new Date().toISOString().slice(0, 10)
    const shortAddress = emailAddress.split('@')[0].slice(0, 8)
    return `${shortAddress}-${timestamp}`
  }

  // 检查邮箱地址格式
  static isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return emailRegex.test(email)
  }

  // 检查是否为临时邮箱域名
  static async isTemporaryEmailDomain(email: string, allowedDomains?: string[]): Promise<boolean> {
    const domain = email.split('@')[1]?.toLowerCase()
    if (!domain) return false

    // 如果有配置允许的域名列表，检查是否在列表中
    if (allowedDomains && allowedDomains.length > 0) {
      return allowedDomains.some(allowedDomain => 
        domain === allowedDomain.toLowerCase() || 
        domain.endsWith('.' + allowedDomain.toLowerCase())
      )
    }

    return true // 默认允许所有域名
  }
}
