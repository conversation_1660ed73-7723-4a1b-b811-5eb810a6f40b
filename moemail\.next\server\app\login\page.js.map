{"version": 3, "file": "app/login/page.js", "mappings": "sFAAA,8DCAA,oHEAA,ooCDWA,MACA,CACA,GACA,CACA,UACA,QACA,CACA,uBAAiC,EACjC,MAfA,IAAoB,sCAA+F,CAenH,8DAES,EACF,CACP,CAEA,UACA,sBAAoC,wCAAkP,aACtR,SACA,aACA,WACA,eACA,CACA,EACA,CACO,CACP,CACA,QApCA,IAAsB,uCAA0F,CAoChH,yDACA,aApCA,IAAsB,uCAAgF,CAoCtG,+CACA,WApCA,IAAsB,sCAAgF,CAoCtG,+CACA,cApCA,IAAsB,uCAAmF,CAoCzG,kDACA,UACA,sBAAoC,wCAAkP,aACtR,SACA,aACA,WACA,eACA,CACA,EACA,CAEA,iEAKO,GACP,QAH6B,EAI7B,UAHA,OADgD,KAChD,SAIA,EAGO,MAAwB,oBAAkB,EACjD,YACA,KAAc,GAAS,UACvB,mBACA,kBAEA,cACA,YACA,YACK,CACL,UACA,YACA,CACA,CAAC,sCC5DD,iCAEA,gCALA,CAEA,CAGA,EAWA,gCACA,wBACA,oCACA,wDACA,gCAEA,+BACA,oDACA,MACI,QAA8B,EAClC,mBACA,0BACA,wBACA,gBAAyB,OAAqB,EAC9C,uBACA,CAAS,CACT,CAAK,EAEL,IAAM,EAAS,OAAS,EACxB,UAAe,GAAU,KACzB,IAvBA,GAwBA,mBACA,OA9BA,KA+BA,OAAW,GACX,SA/BA,KAgCA,YA/BA,KAgCA,SAnCA,KAoCA,gBACA,YAAgB,KAChB,wBACA,0BACA,wBACA,cAlCA,OAmCA,6BA5BA,OA6BA,OAnCA,CAAoB,MAAQ,SAAW,sBAA2B,aAAe,kDAAyD,uOAAsQ,2RAAuT,kBAAkB,QAAQ,uDAA2D,0DAA8D,iBAAsB,gBAAkB,sEAAgF,kBAAoB,uCAA6C,MAAQ,iBAAmB,2BAA+B,gHAAkI,uBAAyB,8FAA0G,aAAiB,WAAa,sEAA6E,uBAAuB,iCAAmC,QAAQ,EAAE,SAAW,oBAAsB,QAAQ,GAAG,4EAAgF,WAAa,SAAW,2CAAiD,UAAY,+BAAqC,UAAY,oCAA0C,QAAU,uCAA6C,OAAS,yCAA+C,QAAU,0CAAiD,MAAQ,+CAAsD,iBAAmB,ywBAA+5B,4CAA8C,kNAAqQ,qBAAyB,s+CAA0/C,qIAoC7wJ,oCACA,mBACA,wBAtDA,KAuDA,2BACA,CAAC,EACM,EAAqB,EACb,cACf,MAAW,OAAO,EAClB,KACA,IAJmC,YAIX,KACxB,QAAiB,CACjB,CAAK,CACL,mBC1EA,sCAA+I,kBCA/I,uCAA+I,mJCG/I,IAAMA,EAAOC,EAAAA,UAAgB,CAG3B,CAAC,WAAEC,CAAS,CAAE,GAAGC,EAAO,CAAEC,IAC1B,UAACC,MAAAA,CACCD,IAAKA,EACLF,UAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CACX,2DACAJ,GAED,GAAGC,CAAK,IAGbH,EAAKO,WAAW,CAAG,OAEnB,IAAMC,EAAaP,EAAAA,UAAgB,CAGjC,CAAC,WAAEC,CAAS,CAAE,GAAGC,EAAO,CAAEC,IAC1B,UAACC,MAAAA,CACCD,IAAKA,EACLF,UAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,gCAAiCJ,GAC9C,GAAGC,CAAK,GAGbK,GAAWD,WAAW,CAAG,aAEzB,IAAME,EAAYR,EAAAA,UAAgB,CAGhC,CAAC,WAAEC,CAAS,CAAE,GAAGC,EAAO,CAAEC,IAC1B,UAACM,KAAAA,CACCN,IAAKA,EACLF,UAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CACX,qDACAJ,GAED,GAAGC,CAAK,IAGbM,EAAUF,WAAW,CAAG,YAExB,IAAMI,EAAkBV,EAAAA,UAAgB,CAGtC,CAAC,WAAEC,CAAS,CAAE,GAAGC,EAAO,CAAEC,IAC1B,UAACQ,IAAAA,CACCR,IAAKA,EACLF,UAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,gCAAiCJ,GAC9C,GAAGC,CAAK,IAGbQ,EAAgBJ,WAAW,CAAG,kBAE9B,IAAMM,EAAcZ,EAAAA,UAAgB,CAGlC,CAAC,WAAEC,CAAS,CAAE,GAAGC,EAAO,CAAEC,IAC1B,UAACC,MAAAA,CAAID,IAAKA,EAAKF,UAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,WAAYJ,GAAa,GAAGC,CAAK,IC5DhE,yCAAuE,MAAkC,EAAI,EAC7G,mBAEA,GADA,OACA,4BACA,aAEA,CACA,CCWA,mBACA,SAqBA,OACA,eACa,eAAmB,KAEhC,mBACA,gBACA,OAAa,SAAa,CAC1B,MAAiB,WAAW,EAAU,IAAM,aAAmC,CAC/E,MAEA,CACA,EAEA,OADA,cACA,CAjCA,cACA,MAAwB,eAAmB,IAC3C,WACA,WACA,UACA,UAAc,mBAA8B,EAC5C,iBACA,EAAoB,SAAa,yBACjC,MAA6B,SAAG,mBAAqB,aAAiB,CACtE,SACA,2BAQA,GAPA,cACA,qBACA,EAAsB,YAAgB,IACtC,cACA,sBACA,kBAA2B,EAAa,2BAA2B,EAAkB,IACrF,EACA,EAeA,SAEA,MACA,WACA,yBACA,WACA,iBACA,aACA,sBACA,CAAK,EACL,mBACA,4BAA2D,cAAqB,IAEhF,MADA,IACA,WAAkD,EAAU,GAC5D,OAAiB,UACjB,CAAO,GAAI,EACX,OAAa,SAAa,OAAU,WAAW,YAAoB,MAAgB,KACnF,CACA,EAEA,OADA,wBACA,CACA,EArBA,SCpDA,gBACA,wBACA,kBACI,GACJ,aAEA,CACA,iBACA,WACA,SACA,YACA,aAIA,OAHA,uBN+BY,EM9BZ,OAEA,CACA,CAAK,EACL,KACA,WACA,YAAwB,WAAqB,KAC7C,WACA,qBACA,IAEA,YAEA,CACA,CAEA,CACA,CACA,iBACA,OAAS,aAAiB,WAC1B,CC9BA,cACA,eAwBA,GACA,MAAoB,YAAgB,SACpC,aAAY,QAAyB,EACrC,GAAQ,gBAAoB,SAoD5B,EACA,EApDA,MAoDA,CADA,wDACA,uCApDA,EAsDA,IAEA,mCAxDA,EAwDA,cACA,uCAzDA,EA2DA,UA3DA,EA6DA,WA7DA,EA6DA,IA5DA,EAyBA,cACA,OAA0B,MAC1B,gBACA,WACA,OACA,mBAEA,KACA,cACA,cAEA,OADA,QACA,CACA,EACQ,GACR,SAEM,YACN,MAAkC,WAC5B,iBACN,sCAEA,CACA,OAAW,UACX,EAhDA,WAIA,OAHA,SAA4B,UAAc,EAC1C,SAAoC,EAAW,QAElC,CAFkC,CAElC,YAAkB,KAC/B,CACA,OAAW,UAAc,YAAuB,UAAc,gBAC9D,CAAG,EAEH,OADA,iBAA6B,EAAU,YACvC,CACA,EAvCA,GACA,EAAgB,YAAgB,SAChC,aAAY,QAAyB,EACrC,EAA0B,UAAc,YACxC,YACA,MACA,uBACA,WACA,MAIA,EAHA,EAAc,QAAc,YAA+B,UAAc,YACxD,gBAAoB,2BAKrC,MAA6B,SAAG,IAAc,oBAA2C,gBAAoB,IAAe,cAAkB,kBAA0C,CACxL,CACA,MAA2B,SAAG,IAAc,sBAA2C,CACvF,CAAG,EAEH,OADA,iBAAyB,EAAU,OACnC,CACA,CJoCAU,EAAYN,WAAW,CAAG,cAY1BO,EAVmBb,UAAgB,CAGjC,CAAC,WAAEC,CAAS,CAAE,GAAGC,EAAO,CAAEC,IAC1B,UAACC,MAAAA,CACCD,IAAKA,EACLF,UAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,6BAA8BJ,GAC3C,GAAGC,CAAK,IAGFI,WAAW,CAAG,aI7BzB,gCAWA,cACA,OAAS,gBAAoB,0EAC7B,CIzDA,2BAA8C,iBAAqB,MACnE,ECAA,EAAiB,CAAK,4CACtB,IACA,cACA,SAAsB,UAAc,MAIpC,OAHE,EAAe,KACjB,SADiB,EACjB,YACA,CAAG,MACH,eAA2C,EAAG,KAC9C,UCaA,MAnBA,CACA,IACA,SACA,MACA,OACA,KACA,KACA,MACA,QACA,QACA,KACA,MACA,KACA,IACA,SACA,OACA,MACA,KACA,CACA,eACA,MAAe,EAAU,aAAc,EAAK,GAC5C,EAAe,YAAgB,SAC/B,YAAY,QAA6B,EACzC,QAIA,MAHA,4BACA,oCAE2B,SAAG,IAAS,WAAsC,CAC7E,CAAG,EAEH,OADA,2BAAkC,EAAK,EACvC,CAAW,WACX,CAAC,GAAI,EEjCL,EAAyB,CAAK,4CAA8C,EAC5E,YACA,EAF2F,GAE3F,EACA,cACA,gBACA,CAAG,CACH,SACC,EACD,oBAmCA,CACA,cACA,WACC,EACD,SAA4B,UAAc,IAC1C,EAAuB,QAAY,IACnC,EAAsB,QAAY,IAUlC,OATA,OACA,WACA,CAAG,MACD,WAAe,MACjB,gBACA,eACA,YAEA,CAAG,QACH,OACA,EApDA,CACA,cACA,UACA,CAAG,EACH,aACA,OACU,EACV,MAA4B,QAAY,aACpC,WAAe,MACnB,gBACA,UAEA,oCACA,aACA,GAAa,GAAQ,mBAHrB,8BAGwC,CAAM,KAAK,EAAG,4KAEtD,CACA,WACA,CAAK,OACL,CAcA,SAbmB,aAAiB,CACpC,IACA,MACA,kBA+BA,OA/BA,SACA,OACA,cAEA,EAAQ,IACR,IAEA,CAAK,CACL,WAEA,CA2BA,+BCpEA,EAAuB,eAAmB,SAK1C,cACA,MAAoB,YAAgB,IACpC,kBACA,CCEA,IAAM,EAAc,gCACd,EAAgB,CAAE,SAAS,EAAO,YAAY,CAAK,EAMnD,EAAa,mBAGb,CAAC,EAAY,EAAe,EAAqB,CTPvD,SAAS,CAAiE,EAAc,IAKhF,EAAgB,EAAO,qBACvB,CAAC,EAAyB,EAAqB,CAAI,EAAmB,GAUtE,CAAC,EAAwB,EAAoB,CAAI,EACrD,EAXuF,CAYrF,EAZiD,UAUF,EAEhC,CAAE,QAAS,IAAK,EAAG,QAAS,IAAI,GAAM,CAAF,EAGjD,EAA4E,IAChF,GAAM,OAAE,WAAO,EAAS,CAAI,EACtB,EAAM,QAAM,CAA0B,IAAI,EAC1C,EAAU,QAAM,CAAgC,IAAI,IAAI,CAAC,OAAE,CACjE,MACE,UAAC,GAAuB,gBAAc,EAAkB,cAAe,WACpE,EACH,CAEJ,EAEA,EAAmB,YAAc,EAMjC,IAAM,EAAuB,EAAO,iBAE9B,EAAqB,EAAW,GAChC,EAAiB,GADc,SACR,CAC3B,CAAC,CAFuD,CAEhD,KACN,GAAM,OAAE,WAAO,EAAS,CAAI,EAEtB,EAAe,EAAgB,EADrB,EAAqB,EAAsB,GACA,EADK,EAC5B,SAAoC,EACxE,MAAO,UAAC,GAAmB,IAAK,WAAe,EAAS,CAC1D,GAGF,EAAe,YAAc,EAM7B,IAAM,EAAiB,EAAO,qBACxB,EAAiB,6BAOjB,EAAyB,EAAW,GACpC,EAAqB,GADc,MAAe,GACvB,CAC/B,CAAC,EAAO,KACN,GAAM,OAAE,WAAO,EAAU,GAAG,EAAS,CAAI,EACnC,EAAM,QAAM,CAAoB,IAAI,EACpC,EAAe,EAAgB,EAAc,GAAG,EACtC,EAAqB,EAAgB,EADjB,CAQpC,EAP0D,KAQxD,EANF,SAAM,CAAU,KACd,EAAQ,QAAQ,IAAI,EAAK,KAAE,EAAK,GAAI,CAAiC,CAAC,EAC/D,IAAM,KAAK,EAAQ,QAAQ,OAAO,GAAG,EAI5C,UAAC,GAA6B,CAAC,EAAc,CAAG,GAAM,IAAK,IAAd,OAC1C,EACH,CAEJ,UAGF,EAAmB,YAAc,EAuB1B,CACL,CAAE,SAAU,EAAoB,KAAM,EAAgB,SAAU,CAAmB,EAlBrF,SAAS,CAAc,EAAY,IAC3B,EAAU,EAAqB,EAAO,qBAAsB,GAalE,EAbuE,KAahE,EAXU,WAAM,CAAY,KACjC,IAAM,EAAiB,EAAQ,cAAc,QAC7C,GAAI,CAAC,EAAgB,MAAO,CAAC,EAC7B,IADqB,EACA,MAAM,KAAK,EAAe,iBAAiB,IAAI,EAAc,EAAG,CAAC,EAKtF,OAJc,MAAM,KAAK,EAAQ,QAAQ,OAAO,CAAC,EACtB,KACzB,CAAC,EAAG,IAAM,EAAa,QAAQ,EAAE,IAAI,OAAQ,EAAI,EAAa,QAAQ,EAAE,IAAI,OAAQ,EAGxF,EAAG,CAAC,EAAQ,cAAe,EAAQ,OAAO,CAAC,CAG7C,EAKE,EACF,EStGA,GAGI,CAAC,EAA+B,EAA2B,CAAI,CAHzD,CAIV,EACA,CAAC,EAAqB,EA+BlB,CAAC,EAAqB,EAAqB,CAC/C,EAAkD,CAlCmC,EAuCjF,EAAyB,IArCP,CAgCsC,MADb,CAMlB,CAC7B,CAAC,EAA2C,IAExC,UAAC,EAAW,SAAX,CAAoB,MAAO,EAAM,wBAChC,mBAAC,EAAW,KAAX,CAAgB,MAAO,EAAM,wBAC5B,mBAAC,GAAsB,GAAG,EAAO,IAAK,EAAc,EACtD,EACF,GAKN,EAAiB,YAAc,EAgB/B,IAAM,EAA6B,aAGjC,CAAC,EAA+C,KAChD,GAAM,yBACJ,cACA,OACA,GAAO,MACP,EACA,iBAAkB,0BAClB,2BACA,eACA,4BACA,GAA4B,EAC5B,GAAG,EACL,CAAI,EACE,EAAY,SAAoC,IAAI,EACpD,EAAe,EAAgB,EAAc,GAAG,EACpC,EAAa,GAAG,CADE,EAEX,EAAmB,CAAI,CADlB,CACuC,CACnE,KAAM,EACN,MAF0C,IAAwB,EAErD,GAA2B,KACxC,SAAU,EACV,OAAQ,CACV,CAAC,EACK,CAAC,EAAkB,EAAmB,CAAU,YAAS,GACzD,CADsC,CACnB,SHxH3B,GACA,EGuHyC,EHvHzC,EAAsB,QAAY,IAIlC,OAHE,WAAe,MACjB,WACA,CAAG,EACM,SAAa,2BdiEC,CcjED,MACtB,EGkH0C,GAClC,EAAW,EAAc,GACzB,EAF8C,EAEtB,QAAO,GAC/B,CAAC,CADmC,CACd,EAF0B,CAEM,WAAS,CAAC,EAUtE,KAVkD,EAWhD,EATI,UAAU,KACd,IAAM,EAAO,EAAI,QACjB,GAAI,EAEF,IAFQ,GACR,EAAK,iBAAiB,EAAa,GAC5B,IAAM,EAAK,OADiC,YACjC,CAAoB,EAAa,EAEvD,EAAG,CAAC,EAAiB,EAGnB,OALqE,CAKrE,EAAC,GACC,MAAO,cACP,EACA,IAAK,OACL,mBACA,EACA,YAAmB,cACjB,GAAe,EAAoB,GACnC,CAAC,EAAmB,EAEtB,CAH8C,cACxB,EAEA,YAAY,IAAM,GAAoB,GAAO,CAAH,CAAK,EACrE,mBAA0B,cACxB,IAAM,EAAuB,GAAe,EAAY,CAAC,EACzD,CAAC,GAEH,sBAA6B,cAC3B,IAAM,EAAuB,GAAe,EAAY,CAAC,EACzD,CAAC,GAGH,mBAAC,EAAU,IAAV,CACC,EADQ,OACE,GAA4C,IAAxB,EAA4B,GAAK,EAC/D,mBAAkB,EACjB,GAAG,EACJ,IAAK,EACL,MAAO,CAAE,QAAS,OAAQ,GAAG,EAAM,OACnC,YAAa,EAAqB,EAAM,YAAa,IAApB,CAC/B,EAAgB,SAAU,CAC5B,CAAC,EACD,QAAS,EAAqB,EAAM,QAAS,IAK3C,IAL2B,EAKH,CAAC,EAAgB,QAEzC,GAAI,EAAM,SAAW,EAAM,eAAiB,GAAmB,CAAC,EAAkB,CAChF,IAAM,EAAkB,IAAI,YAAY,EAAa,GAGrD,GAFA,EAAM,KAD4D,QAC5D,CAAc,cAAc,GAE9B,CAAC,EAAgB,SAF4B,OAE5B,CAAkB,CACrC,IAAM,EAAQ,IAAW,KAAF,CAAE,CAAO,GAAU,EAAK,SAAS,EAOxD,EAJuB,CAFJ,EAAM,KAAK,CAMnB,EAN6B,EAAK,MAAM,EAC/B,EAAM,KAAK,GAAU,EAAK,KAAO,MACD,EAAK,CAAE,EAAF,IAAE,CACzD,SAEoC,IAAK,GAAS,EAAK,IAAI,OAAQ,EAC1C,EAC7B,CACF,CAEA,EAAgB,SAAU,CAC5B,CAAC,EACD,MAN0D,CAMlD,EAAqB,EAAM,OAAQ,IAAM,GAAoB,EAAzC,CAA+C,EAAD,CAAC,CAInF,CAAC,EAMK,EAAY,uBAaZ,EAA6B,aACjC,CAAC,EAA0C,KACzC,GAAM,yBACJ,YACA,GAAY,SACZ,EAAS,aACT,WACA,EACA,GAAG,EACL,CAAI,EACE,EAAS,IACT,CADc,CAAC,GACG,EAClB,EAAU,EAAsB,EAAW,GAC3C,EAAmB,EAAQ,mBAAqB,EAChD,EAAW,EAAc,GAEzB,oBAFgD,CAE9C,uBAAoB,mBAAuB,EAAiB,CAAI,EASxE,OACE,EARI,UAAU,KACd,GAAI,EAEF,OADA,EADa,EAEN,IAAM,GAEjB,EAAG,CAAC,EAAW,EAAoB,CAHZ,CAGkC,EAGvD,UAAC,EAAW,KAH0C,GAG1C,CAAX,CACC,MAAO,KACP,EACA,YACA,SAEA,mBAAC,EAAU,KAAV,CACC,CADQ,QACE,EAAmB,EAAI,GACjC,mBAAkB,EAAQ,YACzB,GAAG,EACJ,IAAK,EACL,YAAa,EAAqB,EAAM,YAAa,IAApB,EAK1B,EAAQ,MAFG,KAEH,CAAY,EAAE,CAFX,EAAM,eAAe,CAGvC,CAAC,EACD,QAAS,EAAqB,EAAM,QAAS,IAAM,EAAQ,EAA9B,SAA8B,CAAY,EAAE,CAAC,CAC1E,UAAW,EAAqB,EAAM,UAAY,IAChD,EAD6B,CACX,QAAd,EAAM,KAAiB,EAAM,SAAU,CACzC,EAAQ,eAAe,EACvB,MACF,CAEA,GAAI,EAAM,SAAW,EAAM,cAAe,OAE1C,IAAM,EAAc,SAqDvB,CAAe,CAA4B,EAA2B,GAAiB,MAC9F,IAAM,GARsB,EAQK,CAArB,CAA2B,CARE,EAQF,CAPvC,EAD0D,IACvC,EAOyB,EAPlB,CAOqB,CANxC,gBAAsB,aAAuB,eAAR,EAAuB,YAAc,GAOjF,IAAI,CAAgB,gBAAc,CAAC,YAAa,YAAY,EAAE,SAAS,EAAG,EAAG,GACzD,KADgE,UAChF,GAAgC,CAAC,UAAW,WAAW,EAAE,SAAS,EAAG,EAAG,OACrE,CAD4E,CACpD,EACjC,CADoC,CAzDW,EAAO,EAAQ,YAAa,EAAQ,GAAG,EAE1E,GAAoB,SAAhB,EAA2B,CAC7B,GAAI,EAAM,SAAW,EAAM,SAAW,EAAM,QAAU,EAAM,SAAU,OACtE,EAAM,eAAe,EAErB,IAAI,EADU,IAAW,KAAF,CAAE,CAAQ,GAAS,EAAK,SAAS,EAC7B,IAAI,GAAU,EAAK,IAAI,OAAQ,EAE1D,GAAoB,OAAQ,EAAxB,EAAwB,EAAe,QAAQ,UAC1B,SAAhB,GAA0C,SAAhB,EAAwB,CACrC,OAAQ,EAAxB,GAAwB,EAAe,QAAQ,EACnD,IAAM,EAAe,EAAe,QAAQ,EAAM,aAAa,EAC/D,EAAiB,EAAQ,KACrB,SA6DX,CAAa,CAAY,GAAoB,OAC7C,EAAM,IAAO,CAAC,EAAG,IAAU,GAAO,EAAa,GAAS,EAAM,MAAM,CAAE,CAC/E,EA/D8B,EAAgB,EAAe,CAAC,EAC1C,EAAe,MAAM,EAAe,CAAC,CAC3C,CAMA,WAAW,IAAM,EAAW,GAC9B,CACF,CAAC,EAEA,OAJ6C,CAAC,CAI1B,YAApB,OAAO,EACJ,EAAS,kBAAE,EAAkB,WAAgC,MAApB,CAAyB,CAAC,EACnE,GACN,EAGN,GAGF,EAAqB,YAAc,EAKnC,IAAM,EAAuD,CAC3D,UAAW,OAAQ,QAAS,OAC5B,WAAY,OAAQ,UAAW,OAC/B,OAAQ,QAAS,KAAM,QACvB,SAAU,OAAQ,IAAK,MACzB,EAgBA,SAAS,EAAW,EAA2B,EAAgB,IAAO,IAC9D,EAA6B,SAAS,cAC5C,QAAW,KAAa,EAEtB,GAAI,IAAc,EAFgB,EAGlC,EAAU,MAAM,eAAE,CAAc,CAAC,EAC7B,SAAS,gBAAkB,GAFe,MAIlD,CCpUA,IAAM,EAAoC,IACxC,GAAM,IDiUuD,KCjUrD,EAAS,WAAS,CAAI,EACxB,EAAW,SAmBV,CAAY,EAAkB,QACrC,GAAM,CAAC,EAAM,EAAO,CAAU,IAAV,MAAU,CAAsB,EAC9C,EAAkB,SAAmC,IAAI,EACzD,EAAuB,SAAO,GAC9B,EAA6B,EADQ,MACR,CAAe,MAAM,EAElD,CAAC,EAAO,EAAI,GAAI,CADD,EAAU,UAAY,GACL,SCxBtC,EDwBoD,CAClD,MCxBF,EDwBW,CACP,QAAS,YACT,cAAe,kBACjB,EACA,iBAAkB,CAChB,MAAO,UACP,cAAe,WACjB,EACA,UAAW,CACT,MAAO,SACT,CACF,CAAC,CClCY,aAAW,CAAC,EAAwB,IAC5B,EAAQ,EAAK,CAAU,EAAK,EAC3B,CAD2B,CAE9C,IDsIH,OArGM,CCjCS,CDiCT,UAAU,KACd,IAAM,EAAuB,EAAiB,EAAU,OAAO,EAC/D,EAAqB,QAAoB,YAAV,EAAsB,EAAuB,MAC9E,EAAG,CAAC,EAAM,EAEV,CAFS,CAEO,KACd,IAAM,EAAS,EAAU,CADZ,MACY,CACnB,EAAa,EAAe,QAGlC,GAF0B,CAEtB,GAFqC,EAElB,CACrB,IAAM,EAAoB,EAAqB,QACzC,EAAuB,EAAiB,GAE1C,EACF,CAHkD,CAG7C,KADM,EACC,EACsB,SAAzB,GAAmC,GAAQ,UAAY,OAGhE,CAHwE,CAGnE,SAAS,EAUV,GAFgB,IAAsB,EAGxC,EAAK,GADW,YACI,CADS,CAG7B,EAAK,SAAS,EAIlB,EAAe,QAAU,CAC3B,CACF,EAAG,CAAC,EAAS,EAAK,EAAD,EAED,KACd,GAAI,EAAM,IADG,CAEP,EACJ,IAAM,EAAc,EAAK,cAAc,aAAe,OAMhD,EAAqB,IAEzB,IAAM,EADuB,EAAiB,EAAU,OAAO,EACf,SAAS,EAAM,aAAa,EAC5E,GAAI,EAAM,SAAW,GAAQ,IAW3B,EAAK,cAX0C,CAW3B,EAChB,CAAC,EAAe,SAAS,CAC3B,IAAM,EAAkB,EAAK,MAAM,kBACnC,EAAK,MAAM,kBAAoB,WAK/B,EAAY,EAAY,WAAW,KACI,YAAY,CAA7C,EAAK,MAAM,oBACb,EAAK,MAAM,kBAAoB,EAEnC,CAAC,CACH,CAEJ,EACM,EAAuB,IACvB,EAAM,SAAW,IAEnB,EAFyB,OAEJ,CAAU,EAAiB,EAAU,QAAO,CAErE,EAIA,OAHA,EAAK,iBAAiB,iBAAkB,GACxC,EAAK,eADuD,CACvD,CAAiB,kBAAmB,GACzC,EAAK,aADsD,GACtD,CAAiB,eAAgB,GAC/B,KACL,EAAY,QAF0C,IAE1C,CAAa,GACzB,EAAK,IAD6B,eAC7B,CAAoB,iBAAkB,GAC3C,EAAK,eAD0D,IAC1D,CAAoB,kBAAmB,GAC5C,EAAK,aADyD,MACzD,CAAoB,eAAgB,EAC3C,CACF,CAGE,EAAK,IAHA,QAFwD,GAKzC,CAExB,EAAG,CAAC,EAAM,EAAK,EAAD,CAGZ,UAAW,CAAC,UAAW,kBAAkB,EAAE,SAAS,GACpD,EADyD,EAC9C,cAAaQ,IACtB,EAAU,QAAUA,EAAO,iBAAiBA,GAAQ,EAAJ,GAChD,EAAQA,EACV,EAAG,CADW,CACT,CACP,CACF,EAnJ+B,GAEvB,EACgB,EAHc,UAGlC,OAAO,EACH,EAAS,CAAE,QAAS,EAAS,UAAW,EAClC,WAAS,KAAK,GAGpB,EAAM,EAAgB,CAHM,CAGG,IAAK,OAAf,EAwJpB,CAAc,EAA2D,IAE5E,EA1JmD,OA0JnC,EAFA,sBAEA,CAAyB,EAAQ,MAAO,KAAK,GAAG,IAChE,EAAU,GAAU,mBAAoB,GAAU,EAAO,sBAC7D,EACU,EAAgB,KADb,EAKb,GAAS,OAAO,yBAAyB,EAAS,KAAK,GAAG,MACtC,mBAAoB,GAAU,EAAO,gBAEhD,EAAQ,MAAM,IAIhB,EAAQ,MAAM,KAAQ,EAAgB,KAxKW,IAExD,CAF6D,CAAC,IACvB,YAApB,OAAO,GACL,EAAS,UAAkB,eAAa,EAAO,KAAE,CAAI,CAAC,EAAI,IACjF,EA4IA,SAAS,EAAiB,GAAoC,OACrD,GAAQ,eAAiB,MAClC,CA5IA,EAAS,YAAc,WERvB,IAAM,EAAY,OAGZ,CAAC,EAAmB,EAAe,CAAI,EAAmB,EAAW,CACzE,EACD,EACK,GAHmC,IAcnC,CAAC,CAdwD,EAc1C,GAAc,CAAI,EAAoC,GA6BrE,GAAa,EA7BgB,CAAiD,IAXxB,CAAC,IAwC1C,CACjB,CAAC,EAA+B,KAC9B,GAAM,aACJ,EACA,MAAO,gBACP,eACA,cACA,EAAc,iBACd,iBACA,EAAiB,YACjB,GAAG,EACL,CAAI,EACE,EAAY,EAAa,GAAG,CAC3B,EAAO,EAAQ,CAAI,CADI,CACiB,CAC7C,EADoB,GACd,EACN,SAAU,CAFkC,CAG5C,YAAa,GAAgB,GAC7B,OAAQ,CACV,CAAC,EAED,MACE,UAAC,IACC,MAAO,EACP,OAAQ,KAAK,CAAC,IACd,EACA,cAAe,cACf,EACA,IAAK,iBACL,EAEA,mBAAC,EAAU,IAAV,CACC,EADQ,EACH,EACL,mBAAkB,EACjB,GAAG,EACJ,IAAK,GACP,EAGN,GAGF,GAAK,YAAc,EAMnB,IAAM,GAAgB,WAOhB,GAAiB,aACrB,CAAC,EAAmC,KAClC,GAAM,aAAE,OAAa,GAAO,EAAM,GAAG,EAAU,CAAI,EAC7C,EAAU,GAD+B,GACD,GACxC,EAAwB,GAAyB,GACvD,MACE,EAFgE,CAEhE,OAAkB,EAAjB,CACC,SAAO,EACN,GAAG,EACJ,YAAa,EAAQ,YACrB,IAAK,EAAQ,SACb,EAEA,mBAAC,EAAU,IAAV,CACC,EADQ,GACH,UACL,mBAAkB,EAAQ,YACzB,GAAG,EACJ,IAAK,GACP,EAGN,EAGF,IAAS,YAAc,GAMvB,IAAM,GAAe,cAQf,GAAoB,aACxB,CAAC,EAAsC,KACrC,GAAM,aAAE,QAAa,WAAO,GAAW,EAAO,GAAG,EAAa,CAAI,EAC5D,EAAU,GAAe,GAAc,GACvC,EAAwB,GAAyB,GADC,EAEtC,GAAc,EAAQ,CAD0B,KAC1B,CAAQ,GAC1C,EAD+C,GACrB,EAAQ,OAAQ,GAC1C,EAD+C,IACxB,EAAQ,MACrC,MACE,UAAkB,EAAjB,CACC,SAAO,EACN,GAAG,EACJ,UAAW,CAAC,EACZ,OAAQ,EAER,mBAAC,EAAU,OAAD,CACR,KAAK,SACL,KAAK,MACL,gBAAe,EACf,gBAAe,EACf,aAAY,EAAa,SAAW,WACpC,gBAAe,EAAW,GAAK,OAC/B,WACA,GAAI,EACH,GAAG,EACJ,IAAK,EACL,YAAa,EAAqB,EAAM,YAAa,IAG/C,GAA8B,IAAjB,EAAM,QAAkC,KAAlB,EAAM,QAI3C,EAAM,eAAe,EAHrB,EAAQ,cAAc,EAK1B,CAAC,EACD,UAAW,EAAqB,EAAM,UAAW,IAC3C,CAAC,CADwB,GACnB,OAAO,EAAE,SAAS,EAAM,GAAG,EAAG,GAAQ,cAAc,EAChE,CAAC,EADoE,QAE5D,EAAqB,EAAM,QAAS,KAG3C,GAH2B,CAGrB,EAAmD,WAA3B,EAAQ,eACjC,GAAe,IAAY,GAC9B,EAAQ,EADS,WACT,CAAc,EAE1B,CAAC,CAHwD,CAC1B,CAGjC,EAGN,GAGF,GAAY,YAAc,GAM1B,IAAM,GAAe,cAaf,GAAoB,aACxB,CAAC,EAAsC,KACrC,GAAM,aAAE,EAAa,mBAAO,WAAY,EAAU,GAAG,EAAa,CAAI,EAChE,EAAU,GAAe,GADmC,GAE5D,EAAY,GAAc,EAAQ,CADgB,KAChB,CAAQ,GAC1C,EAD+C,GACrB,EAAQ,OAAQ,GAC1C,EAD+C,IACxB,EAAQ,MAC/B,EAAqC,SAAO,GAOlD,OAP4D,EAEtD,UAAU,KACd,IAAM,EAAM,sBAAsB,IAAO,EAA6B,SAAU,GAChF,EADsF,IAC/E,IAAM,qBAAqB,EACpC,CADuC,CACpC,CAAC,CAAC,EAGH,UAAC,GAAS,KAAD,GAAU,GAAc,EAC9B,UAAC,SAAE,EAAQ,GACV,UAAC,EAAU,IAAV,CACC,EADQ,WACI,EAAa,SAAW,WACpC,mBAAkB,EAAQ,YAC1B,KAAK,WACL,kBAAiB,EACjB,OAAQ,CAAC,EACT,GAAI,EACJ,SAAU,EACT,GAAG,EACJ,IAAK,EACL,MAAO,CACL,GAAG,EAAM,MACT,kBAAmB,EAA6B,QAAU,KAAO,MACnE,EAEC,YAAW,GACd,CAEJ,CAEJ,GAOF,SAAS,GAAc,EAAgB,GAAe,MAC7C,GAAG,EAAM,WAAY,EAAK,EAGnC,CAHmC,QAG1B,GAAc,EAAgB,GAAe,MAC7C,GAAG,EAAM,WAAY,EAAK,EATnC,CASmC,EATvB,YAAc,GCnQ1B,IAAMC,GAAWf,EAAAA,QAAHe,EAAmB,CAG/B,CAAC,WAAEd,CAAS,CAAE,GAAGC,EAAO,CAAEC,IAC1B,UAACa,GAAkB,CACjBb,IAAKA,EACLF,UAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CACX,6FACAJ,GAED,GAAGC,CAAK,IAGba,GAAST,UAADS,CAAY,CAAGC,GAAmBV,CAAD,UAAY,CAErD,IAAMW,GAAcjB,EAAAA,UAAgB,CAGlC,CAAC,WAAEC,CAAS,CAAE,GAAGC,EAAO,CAAEC,IAC1B,UAACa,GAAqB,CACpBb,GADoB,CACfA,EACLF,UAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CACX,sYACAJ,GAED,GAAGC,CAAK,GAGbe,IAAYX,WAAW,CAAZW,GAAqCX,IAAD,OAAY,CAE3D,IAAMY,GAAclB,EAAAA,UAAgB,CAAnBkB,CAGd,WAAEjB,CAAS,CAAE,GAAGC,EAAO,CAAEC,IAC1B,UAACa,GAAqB,CACpBb,GADoB,CACfA,EACLF,UAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CACX,kIACAJ,GAED,GAAGC,CAAK,IAGbgB,GAAYZ,WAAW,CAAGU,CAAfE,EAAqCZ,IAAD,OAAY,iBCtCrD,OAAW,cAAgB,CAAC,UAAY,EAC5C,CACE,OACA,CACE,CAAG,8KACH,GAAK,SACP,EACF,CACA,CAAC,SAAU,CAAE,GAAI,CAAQ,SAAI,CAAO,OAAG,IAAM,MAAM,cAAgB,KAAK,SAAU,EACnF,8BCOM,SAASa,KACd,GAAM,CAACC,EAAUC,EAAY,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,IACnC,CAACC,EAAUC,EAAY,CAAGF,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,IACnC,CAACG,EAAiBC,EAAmB,CAAGJ,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,IACjD,CAACK,EAASC,EAAW,CAAGN,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GACjC,CAACO,EAAQC,EAAU,CAAGR,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAa,CAAC,GAC5C,OAAES,CAAK,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAQA,GAEpBC,EAAoB,KACxB,IAAMC,EAAwB,CAAC,EAM/B,OALKd,IAAUc,EAAUd,QAAQ,CAAG,UAC/BG,IAAUW,EAAUX,QAAQ,CAAG,SAChCH,EAASe,QAAQ,CAAC,OAAMD,EAAUd,QAAQ,CAAG,gBAC7CG,GAAYA,EAASa,MAAM,CAAG,IAAGF,EAAUX,QAAQ,CAAG,gBAC1DO,EAAUI,GAC+B,IAAlCG,OAAOC,IAAI,CAACJ,GAAWE,MAAM,EAGhCG,EAAuB,KAC3B,IAAML,EAAwB,CAAC,EAQ/B,OAPKd,IAAUc,EAAUd,QAAQ,CAAG,UAC/BG,IAAUW,EAAUX,QAAQ,CAAG,SAChCH,EAASe,QAAQ,CAAC,MAAMD,GAAUd,QAAQ,CAAG,gBAC7CG,GAAYA,EAASa,MAAM,CAAG,IAAGF,EAAUX,QAAQ,CAAG,gBACrDE,IAAiBS,EAAUT,eAAe,CAAG,SAC9CF,IAAaE,IAAiBS,EAAUT,eAAe,CAAG,cAC9DK,EAAUI,GAC+B,IAAlCG,OAAOC,IAAI,CAACJ,GAAWE,MAAM,EAGhCI,EAAc,UAClB,GAAKP,CAAD,IAEJL,GAAW,GACX,GAAI,CACF,IAAMa,EAJkB,MAIHC,CAAAA,EAAAA,EAAAA,EAAAA,CAAMA,CAAC,cAAe,UACzCtB,WACAG,EACAoB,UAAU,CACZ,GAEA,GAAIF,GAAQG,MAAO,CACjBb,EAAM,CACJc,MAAO,OACPC,YAAa,WACbC,QAAS,aACX,GACAnB,GAAW,GACX,MACF,CAEAoB,OAAOC,QAAQ,CAACC,IAAI,CAAG,GACzB,CAAE,MAAON,EAAO,CACdb,EAAM,CACJc,MAAO,OACPC,YAAaF,aAAiBO,MAAQP,EAAMQ,OAAO,CAAG,QACtDL,QAAS,aACX,GACAnB,GAAW,EACb,EACF,EAEMyB,EAAiB,UACrB,GAAKd,CAAD,IAEJX,GAAW,GACX,GAAI,CACF,IAAM0B,EAAW,GAJU,GAIJC,MAAM,qBAAsB,CACjDC,OAAQ,OACRC,QAAS,CAAE,eAAgB,kBAAmB,EAC9CC,KAAMC,KAAKC,SAAS,CAAC,CAAExC,oBAAUG,CAAS,EAC5C,GAEMsC,EAAO,MAAMP,EAASQ,IAAI,GAEhC,GAAI,CAACR,EAASS,EAAE,CAAE,CAChBhC,EAAM,CACJc,MAAO,OACPC,YAAae,EAAKjB,KAAK,EAAI,QAC3BG,QAAS,aACX,GACAnB,GAAW,GACX,MACF,CAGA,IAAMa,EAAS,MAAMC,CAAAA,EAAAA,EAAAA,EAAAA,CAAMA,CAAC,cAAe,UACzCtB,WACAG,EACAoB,UAAU,CACZ,GAEA,GAAIF,GAAQG,MAAO,CACjBb,EAAM,CACJc,MAAO,OACPC,YAAa,eACbC,QAAS,aACX,GACAnB,GAAW,GACX,MACF,CAEAoB,OAAOC,QAAQ,CAACC,IAAI,CAAG,GACzB,CAAE,MAAON,EAAO,CACdb,EAAM,CACJc,MAAO,OACPC,YAAaF,aAAiBO,MAAQP,EAAMQ,OAAO,CAAG,QACtDL,QAAS,aACX,GACAnB,GAAW,EACb,EACF,EAaA,MACE,WAAC7B,EAAIA,CAACE,CAADF,SAAW,wDACd,WAACQ,EAAUA,CAACN,OAADM,GAAW,sBACpB,UAACC,EAASA,CAACP,MAADO,IAAW,0GAAiG,iBAGtH,UAACE,EAAeA,CAACT,UAAU,EAAXS,qBAAyB,yBAI3C,UAACE,EAAWA,CAACX,QAADW,EAAW,gBACrB,WAACoD,GAAIA,CAACC,KAADD,QAAc,QAAQ/D,UAAU,SAASiE,cAlBlC,CAkBiDC,IAjBjE9C,EAAY,IACZG,EAAY,IACZE,EAAmB,IACnBI,EAAU,CAAC,EACb,YAcQ,WAACf,GAAQA,CAACd,SAADc,CAAW,yCAClB,UAACE,GAAWA,CAACmD,MAAM,MAAPnD,WAAe,OAC3B,UAACA,GAAWA,CAACmD,MAAM,MAAPnD,cAAkB,UAEhC,WAACb,MAAAA,CAAIH,UAAU,0BACb,WAACiB,GAAWA,CAACkD,MAAM,MAAPlD,EAAejB,UAAU,2BACnC,WAACG,MAAAA,CAAIH,UAAU,sBACb,WAACG,MAAAA,CAAIH,UAAU,wBACb,WAACG,MAAAA,CAAIH,UAAU,qBACb,UAACG,MAAAA,CAAIH,UAAU,yDACb,UAACoE,GAAAA,CAAKA,CAAAA,CAACpE,UAAU,cAEnB,UAACqE,EAAAA,CAAKA,CAAAA,CACJrE,UAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CACX,gBACAwB,EAAOT,QAAQ,EAAI,qDAErBmD,YAAY,MACZH,MAAOhD,EACPoD,SAAU,IACRnD,EAAYoD,EAAEC,MAAM,CAACN,KAAK,EAC1BtC,EAAU,CAAC,EACb,EACA6C,SAAUhD,OAGbE,EAAOT,QAAQ,EACd,UAACT,IAAAA,CAAEV,UAAU,oCAA4B4B,EAAOT,QAAQ,MAG5D,WAAChB,MAAAA,CAAIH,UAAU,wBACb,WAACG,MAAAA,CAAIH,UAAU,qBACb,UAACG,MAAAA,CAAIH,UAAU,yDACb,UAAC2E,GAAQA,CAAC3E,IAAD2E,MAAW,cAEtB,UAACN,EAAAA,CAAKA,CAAAA,CACJrE,UAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CACX,gBACAwB,EAAON,QAAQ,EAAI,qDAErBsD,KAAK,WACLN,YAAY,KACZH,MAAO7C,EACPiD,SAAWC,IACTjD,EAAYiD,EAAEC,MAAM,CAACN,KAAK,EAC1BtC,EAAU,CAAC,EACb,EACA6C,SAAUhD,OAGbE,EAAON,QAAQ,EACd,UAACZ,IAAAA,CAAEV,UAAU,oCAA4B4B,EAAON,QAAQ,SAK9D,WAACnB,MAAAA,CAAIH,UAAU,2BACb,WAAC6E,EAAAA,CAAMA,CAAAA,CACL7E,UAAU,SACV8E,QAASvC,EACTmC,SAAUhD,YAETA,GAAW,UAACqD,GAAAA,CAAOA,CAAAA,CAAC/E,UAAU,8BAA+B,QAIhE,WAACG,MAAAA,CAAIH,UAAU,qBACb,UAACG,MAAAA,CAAIH,UAAU,8CACb,UAACgF,OAAAA,CAAKhF,UAAU,sBAElB,UAACG,MAAAA,CAAIH,UAAU,0DACb,UAACgF,OAAAA,CAAKhF,UAAU,oDAA2C,YAM/D,WAAC6E,EAAAA,CAAMA,CAAAA,CACL/B,QAAQ,UACR9C,UAAU,SACV8E,QAvGU,CAuGDG,IAtGvBxC,CAAAA,EAAAA,EAAAA,EAAAA,CAAMA,CAAC,SAAU,CAAEyC,YAAa,GAAI,EACtC,YAuGgB,UAACC,GAAAA,CAAMA,CAAAA,CAACnF,UAAU,iBAAiB,0BAKzC,WAACiB,GAAWA,CAACkD,MAAM,MAAPlD,KAAkBjB,UAAU,2BACtC,WAACG,MAAAA,CAAIH,UAAU,sBACb,WAACG,MAAAA,CAAIH,UAAU,wBACb,WAACG,MAAAA,CAAIH,UAAU,qBACb,UAACG,MAAAA,CAAIH,UAAU,yDACb,UAACoE,GAAAA,CAAKA,CAAAA,CAACpE,UAAU,cAEnB,UAACqE,EAAAA,CAAKA,CAAAA,CACJrE,UAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CACX,gBACAwB,EAAOT,QAAQ,EAAI,qDAErBmD,YAAY,MACZH,MAAOhD,EACPoD,SAAU,IACRnD,EAAYoD,EAAEC,MAAM,CAACN,KAAK,EAC1BtC,EAAU,CAAC,EACb,EACA6C,SAAUhD,OAGbE,EAAOT,QAAQ,EACd,UAACT,IAAAA,CAAEV,UAAU,oCAA4B4B,EAAOT,QAAQ,MAG5D,WAAChB,MAAAA,CAAIH,UAAU,wBACb,WAACG,MAAAA,CAAIH,UAAU,qBACb,UAACG,MAAAA,CAAIH,UAAU,yDACb,UAAC2E,GAAQA,CAAC3E,IAAD2E,MAAW,cAEtB,UAACN,EAAAA,CAAKA,CAAAA,CACJrE,UAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CACX,gBACAwB,EAAON,QAAQ,EAAI,qDAErBsD,KAAK,WACLN,YAAY,KACZH,MAAO7C,EACPiD,SAAU,IACRhD,EAAYiD,EAAEC,MAAM,CAACN,KAAK,EAC1BtC,EAAU,CAAC,EACb,EACA6C,SAAUhD,OAGbE,EAAON,QAAQ,EACd,UAACZ,IAAAA,CAAEV,UAAU,oCAA4B4B,EAAON,QAAQ,MAG5D,WAACnB,MAAAA,CAAIH,UAAU,wBACb,WAACG,MAAAA,CAAIH,UAAU,qBACb,UAACG,MAAAA,CAAIH,UAAU,yDACb,UAAC2E,GAAQA,CAAC3E,IAAD2E,MAAW,cAEtB,UAACN,EAAAA,CAAKA,CAAAA,CACJrE,UAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CACX,gBACAwB,EAAOJ,eAAe,EAAI,qDAE5BoD,KAAK,WACLN,YAAY,OACZH,MAAO3C,EACP+C,SAAU,IACR9C,EAAmB+C,EAAEC,MAAM,CAACN,KAAK,EACjCtC,EAAU,CAAC,EACb,EACA6C,SAAUhD,OAGbE,EAAOJ,eAAe,EACrB,UAACd,IAAAA,CAAEV,UAAU,oCAA4B4B,EAAOJ,eAAe,SAKrE,UAACrB,MAAAA,CAAIH,UAAU,0BACb,WAAC6E,EAAAA,CAAMA,CAAAA,CACL7E,UAAU,SACV8E,QAAS1B,EACTsB,SAAUhD,YAETA,GAAW,UAACqD,GAAAA,CAAOA,CAAAA,CAAC/E,UAAU,8BAA+B,uBAUhF,sFC/UA,IAAMqE,EAAQtE,EAAAA,UAAgB,CAC5B,CAAC,WAAEC,CAAS,MAAE4E,CAAI,CAAE,GAAG3E,EAAO,CAAEC,IAE5B,UAACkF,QAAAA,CACCR,KAAMA,EACN5E,UAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CACX,wUACAJ,GAEFE,IAAKA,EACJ,GAAGD,CAAK,IAKjBoE,EAAMhE,WAAW,CAAG,uDCVd,MAAe,cAAgB,CAAC,cAAgB,EACpD,CAAC,MAAQ,EAAE,EAAG,CAA+B,iCAAK,SAAU,EAC7D,iDCFK,MAAY,cAAgB,CAAC,WAAa,EAC9C,CAAC,QAAU,EAAE,EAAI,MAAM,CAAI,MAAK,CAAG,KAAK,GAAK,UAAU,EACvD,CAAC,MAAQ,EAAE,EAAG,CAAyB,2BAAK,SAAU,EACvD,yhBCZM,IAAMgF,EAAU,OAAM,eAECC,IAC5B,IAAMC,EAAU,MAAMC,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,GAM1B,OAJID,GAASE,MAAM,CACjB/C,EAAAA,EAAAA,EAAAA,CAAQA,CAAC,KAIT,UAACvC,MAAAA,CAAIH,UAAU,uIACb,UAACkB,EAAAA,SAASA,CAAAA,CAAAA,IAGhB", "sources": ["webpack://_N_E/external commonjs \"node:async_hooks\"", "webpack://_N_E/external commonjs \"node:buffer\"", "webpack://_N_E/./app/login/page.tsx?7965", "webpack://_N_E/|ssr?c5f4", "webpack://_N_E/?4d63", "webpack://_N_E/?7a79", "webpack://_N_E/./app/components/ui/card.tsx", "webpack://_N_E/./node_modules/@radix-ui/react-tabs/node_modules/@radix-ui/primitive/dist/index.mjs", "webpack://_N_E/./node_modules/@radix-ui/react-tabs/node_modules/@radix-ui/react-context/dist/index.mjs", "webpack://_N_E/./node_modules/@radix-ui/react-tabs/node_modules/@radix-ui/react-compose-refs/dist/index.mjs", "webpack://_N_E/./node_modules/@radix-ui/react-tabs/node_modules/@radix-ui/react-slot/dist/index.mjs", "webpack://_N_E/../src/collection-legacy.tsx", "webpack://_N_E/../src/collection.tsx", "webpack://_N_E/../src/ordered-dictionary.ts", "webpack://_N_E/./node_modules/@radix-ui/react-tabs/node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs", "webpack://_N_E/./node_modules/@radix-ui/react-tabs/node_modules/@radix-ui/react-id/dist/index.mjs", "webpack://_N_E/./node_modules/@radix-ui/react-tabs/node_modules/@radix-ui/react-primitive/dist/index.mjs", "webpack://_N_E/./node_modules/@radix-ui/react-tabs/node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs", "webpack://_N_E/./node_modules/@radix-ui/react-tabs/node_modules/@radix-ui/react-use-controllable-state/dist/index.mjs", "webpack://_N_E/./node_modules/@radix-ui/react-tabs/node_modules/@radix-ui/react-direction/dist/index.mjs", "webpack://_N_E/../src/roving-focus-group.tsx", "webpack://_N_E/../src/presence.tsx", "webpack://_N_E/../src/use-state-machine.tsx", "webpack://_N_E/../src/tabs.tsx", "webpack://_N_E/./app/components/ui/tabs.tsx", "webpack://_N_E/../../../src/icons/key-round.ts", "webpack://_N_E/./app/components/auth/login-form.tsx", "webpack://_N_E/./app/components/ui/input.tsx", "webpack://_N_E/../../../src/icons/loader-circle.ts", "webpack://_N_E/../../../src/icons/user-round.ts", "webpack://_N_E/./app/login/page.tsx"], "sourcesContent": ["module.exports = require(\"node:async_hooks\");", "module.exports = require(\"node:buffer\");", "const module0 = () => import(/* webpackMode: \"eager\" */ \"F:\\\\CODE\\\\Project\\\\tempmail\\\\moemail\\\\app\\\\layout.tsx\");\nconst module1 = () => import(/* webpackMode: \"eager\" */ \"next/dist/client/components/not-found-error\");\nconst module2 = () => import(/* webpackMode: \"eager\" */ \"next/dist/client/components/forbidden-error\");\nconst module3 = () => import(/* webpackMode: \"eager\" */ \"next/dist/client/components/unauthorized-error\");\nconst page4 = () => import(/* webpackMode: \"eager\" */ \"F:\\\\CODE\\\\Project\\\\tempmail\\\\moemail\\\\app\\\\login\\\\page.tsx\");\nimport { AppPageRouteModule } from \"next/dist/server/route-modules/app-page/module.compiled\" with {\n    'turbopack-transition': 'next-ssr'\n};\nimport { RouteKind } from \"next/dist/server/route-kind\";\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'login',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"F:\\\\CODE\\\\Project\\\\tempmail\\\\moemail\\\\app\\\\login\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        metadata: {\n    icon: [(async (props) => (await import(/* webpackMode: \"eager\" */ \"next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!F:\\\\CODE\\\\Project\\\\tempmail\\\\moemail\\\\app\\\\favicon.ico?__next_metadata__\")).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [module0, \"F:\\\\CODE\\\\Project\\\\tempmail\\\\moemail\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await import(/* webpackMode: \"eager\" */ \"next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!F:\\\\CODE\\\\Project\\\\tempmail\\\\moemail\\\\app\\\\favicon.ico?__next_metadata__\")).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"F:\\\\CODE\\\\Project\\\\tempmail\\\\moemail\\\\app\\\\login\\\\page.tsx\"];\nexport { tree, pages };\nexport { default as GlobalError } from \"next/dist/client/components/error-boundary\";\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nexport const __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\nexport * from \"next/dist/server/app-render/entry-base\";\n// Create and export the route module that will be consumed.\nexport const routeModule = new AppPageRouteModule({\n    definition: {\n        kind: RouteKind.APP_PAGE,\n        page: \"/login/page\",\n        pathname: \"/login\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map", "var _self___RSC_MANIFEST;\nimport \"next/dist/server/web/globals\";\nimport { adapter } from \"next/dist/server/web/adapter\";\nimport { getRender } from \"next/dist/build/webpack/loaders/next-edge-ssr-loader/render\";\nimport { IncrementalCache } from \"next/dist/server/lib/incremental-cache\";\nimport { renderToHTMLOrFlight as renderToHTML } from \"next/dist/server/app-render/app-render\";\nimport * as pageMod from \"next-app-loader?name=app%2Flogin%2Fpage&page=%2Flogin%2Fpage&pagePath=private-next-app-dir%2Flogin%2Fpage.tsx&appDir=F%3A%5CCODE%5CProject%5Ctempmail%5Cmoemail%5Capp&appPaths=%2Flogin%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!private-next-app-dir/login/page.tsx?__next_edge_ssr_entry__\";\nimport { PAGE_TYPES } from \"next/dist/lib/page-types\";\nimport { setReferenceManifestsSingleton } from \"next/dist/server/app-render/encryption-utils\";\nimport { createServerModuleMap } from \"next/dist/server/app-render/action-utils\";\nconst incrementalCacheHandler = null\nconst cacheHandlers = {\n\n};\nif (!globalThis.__nextCacheHandlers) {\n    ;\n    globalThis.__nextCacheHandlers = cacheHandlers;\n}\nconst Document = null;\nconst appMod = null;\nconst errorMod = null;\nconst error500Mod = null;\nconst sriEnabled = false\nconst isServerComponent = true\nconst dev = false\nconst serverActions = undefined\nconst nextConfig = {\"env\":{},\"eslint\":{\"ignoreDuringBuilds\":false},\"typescript\":{\"ignoreBuildErrors\":false,\"tsconfigPath\":\"tsconfig.json\"},\"distDir\":\".next\",\"cleanDistDir\":true,\"assetPrefix\":\"\",\"cacheMaxMemorySize\":52428800,\"configOrigin\":\"next.config.ts\",\"useFileSystemPublicRoutes\":true,\"generateEtags\":true,\"pageExtensions\":[\"tsx\",\"ts\",\"jsx\",\"js\"],\"poweredByHeader\":true,\"compress\":true,\"images\":{\"deviceSizes\":[640,750,828,1080,1200,1920,2048,3840],\"imageSizes\":[16,32,48,64,96,128,256,384],\"path\":\"/_next/image\",\"loader\":\"default\",\"loaderFile\":\"\",\"domains\":[],\"disableStaticImages\":false,\"minimumCacheTTL\":60,\"formats\":[\"image/webp\"],\"dangerouslyAllowSVG\":false,\"contentSecurityPolicy\":\"script-src 'none'; frame-src 'none'; sandbox;\",\"contentDispositionType\":\"attachment\",\"remotePatterns\":[{\"protocol\":\"https\",\"hostname\":\"avatars.githubusercontent.com\"}],\"unoptimized\":false},\"devIndicators\":{\"appIsrStatus\":true,\"buildActivity\":true,\"buildActivityPosition\":\"bottom-right\"},\"onDemandEntries\":{\"maxInactiveAge\":60000,\"pagesBufferLength\":5},\"amp\":{\"canonicalBase\":\"\"},\"basePath\":\"\",\"sassOptions\":{},\"trailingSlash\":false,\"i18n\":null,\"productionBrowserSourceMaps\":false,\"excludeDefaultMomentLocales\":true,\"serverRuntimeConfig\":{},\"publicRuntimeConfig\":{},\"reactProductionProfiling\":false,\"reactStrictMode\":null,\"reactMaxHeadersLength\":6000,\"httpAgentOptions\":{\"keepAlive\":true},\"logging\":{},\"expireTime\":31536000,\"staticPageGenerationTimeout\":60,\"modularizeImports\":{\"@mui/icons-material\":{\"transform\":\"@mui/icons-material/{{member}}\"},\"lodash\":{\"transform\":\"lodash/{{member}}\"}},\"outputFileTracingRoot\":\"F:\\\\CODE\\\\Project\\\\tempmail\\\\moemail\",\"experimental\":{\"cacheLife\":{\"default\":{\"stale\":300,\"revalidate\":900,\"expire\":4294967294},\"seconds\":{\"stale\":0,\"revalidate\":1,\"expire\":60},\"minutes\":{\"stale\":300,\"revalidate\":60,\"expire\":3600},\"hours\":{\"stale\":300,\"revalidate\":3600,\"expire\":86400},\"days\":{\"stale\":300,\"revalidate\":86400,\"expire\":604800},\"weeks\":{\"stale\":300,\"revalidate\":604800,\"expire\":2592000},\"max\":{\"stale\":300,\"revalidate\":2592000,\"expire\":4294967294}},\"cacheHandlers\":{},\"cssChunking\":true,\"multiZoneDraftMode\":false,\"appNavFailHandling\":false,\"prerenderEarlyExit\":true,\"serverMinification\":true,\"serverSourceMaps\":false,\"linkNoTouchStart\":false,\"caseSensitiveRoutes\":false,\"clientSegmentCache\":false,\"preloadEntriesOnStart\":true,\"clientRouterFilter\":true,\"clientRouterFilterRedirects\":false,\"fetchCacheKeyPrefix\":\"\",\"middlewarePrefetch\":\"flexible\",\"optimisticClientCache\":true,\"manualClientBasePath\":false,\"cpus\":11,\"memoryBasedWorkersCount\":false,\"imgOptConcurrency\":null,\"imgOptTimeoutInSeconds\":7,\"imgOptMaxInputPixels\":268402689,\"imgOptSequentialRead\":null,\"isrFlushToDisk\":true,\"workerThreads\":false,\"optimizeCss\":false,\"nextScriptWorkers\":false,\"scrollRestoration\":false,\"externalDir\":false,\"disableOptimizedLoading\":false,\"gzipSize\":true,\"craCompat\":false,\"esmExternals\":true,\"fullySpecified\":false,\"swcTraceProfiling\":false,\"forceSwcTransforms\":false,\"largePageDataBytes\":128000,\"turbo\":{\"root\":\"F:\\\\CODE\\\\Project\\\\tempmail\\\\moemail\"},\"typedRoutes\":false,\"typedEnv\":false,\"parallelServerCompiles\":false,\"parallelServerBuildTraces\":false,\"ppr\":false,\"authInterrupts\":false,\"reactOwnerStack\":false,\"webpackMemoryOptimizations\":false,\"optimizeServerReact\":true,\"useEarlyImport\":false,\"staleTimes\":{\"dynamic\":0,\"static\":300},\"serverComponentsHmrCache\":true,\"staticGenerationMaxConcurrency\":8,\"staticGenerationMinPagesPerWorker\":25,\"dynamicIO\":false,\"inlineCss\":false,\"optimizePackageImports\":[\"lucide-react\",\"date-fns\",\"lodash-es\",\"ramda\",\"antd\",\"react-bootstrap\",\"ahooks\",\"@ant-design/icons\",\"@headlessui/react\",\"@headlessui-float/react\",\"@heroicons/react/20/solid\",\"@heroicons/react/24/solid\",\"@heroicons/react/24/outline\",\"@visx/visx\",\"@tremor/react\",\"rxjs\",\"@mui/material\",\"@mui/icons-material\",\"recharts\",\"react-use\",\"effect\",\"@effect/schema\",\"@effect/platform\",\"@effect/platform-node\",\"@effect/platform-browser\",\"@effect/platform-bun\",\"@effect/sql\",\"@effect/sql-mssql\",\"@effect/sql-mysql2\",\"@effect/sql-pg\",\"@effect/sql-squlite-node\",\"@effect/sql-squlite-bun\",\"@effect/sql-squlite-wasm\",\"@effect/sql-squlite-react-native\",\"@effect/rpc\",\"@effect/rpc-http\",\"@effect/typeclass\",\"@effect/experimental\",\"@effect/opentelemetry\",\"@material-ui/core\",\"@material-ui/icons\",\"@tabler/icons-react\",\"mui-core\",\"react-icons/ai\",\"react-icons/bi\",\"react-icons/bs\",\"react-icons/cg\",\"react-icons/ci\",\"react-icons/di\",\"react-icons/fa\",\"react-icons/fa6\",\"react-icons/fc\",\"react-icons/fi\",\"react-icons/gi\",\"react-icons/go\",\"react-icons/gr\",\"react-icons/hi\",\"react-icons/hi2\",\"react-icons/im\",\"react-icons/io\",\"react-icons/io5\",\"react-icons/lia\",\"react-icons/lib\",\"react-icons/lu\",\"react-icons/md\",\"react-icons/pi\",\"react-icons/ri\",\"react-icons/rx\",\"react-icons/si\",\"react-icons/sl\",\"react-icons/tb\",\"react-icons/tfi\",\"react-icons/ti\",\"react-icons/vsc\",\"react-icons/wi\"]},\"bundlePagesRouterDependencies\":false,\"configFile\":\"F:\\\\CODE\\\\Project\\\\tempmail\\\\moemail\\\\next.config.ts\",\"configFileName\":\"next.config.ts\"}\nconst maybeJSONParse = (str)=>str ? JSON.parse(str) : undefined;\nconst buildManifest = self.__BUILD_MANIFEST;\nconst reactLoadableManifest = maybeJSONParse(self.__REACT_LOADABLE_MANIFEST);\nconst rscManifest = (_self___RSC_MANIFEST = self.__RSC_MANIFEST) == null ? void 0 : _self___RSC_MANIFEST[\"/login/page\"];\nconst rscServerManifest = maybeJSONParse(self.__RSC_SERVER_MANIFEST);\nconst subresourceIntegrityManifest = sriEnabled ? maybeJSONParse(self.__SUBRESOURCE_INTEGRITY_MANIFEST) : undefined;\nconst nextFontManifest = maybeJSONParse(self.__NEXT_FONT_MANIFEST);\nconst interceptionRouteRewrites = maybeJSONParse(self.__INTERCEPTION_ROUTE_REWRITE_MANIFEST) ?? [];\nif (rscManifest && rscServerManifest) {\n    setReferenceManifestsSingleton({\n        page: \"/login/page\",\n        clientReferenceManifest: rscManifest,\n        serverActionsManifest: rscServerManifest,\n        serverModuleMap: createServerModuleMap({\n            serverActionsManifest: rscServerManifest\n        })\n    });\n}\nconst render = getRender({\n    pagesType: PAGE_TYPES.APP,\n    dev,\n    page: \"/login/page\",\n    appMod,\n    pageMod,\n    errorMod,\n    error500Mod,\n    Document,\n    buildManifest,\n    renderToHTML,\n    reactLoadableManifest,\n    clientReferenceManifest: isServerComponent ? rscManifest : null,\n    serverActionsManifest: isServerComponent ? rscServerManifest : null,\n    serverActions: isServerComponent ? serverActions : undefined,\n    subresourceIntegrityManifest,\n    config: nextConfig,\n    buildId: process.env.__NEXT_BUILD_ID,\n    nextFontManifest,\n    incrementalCacheHandler,\n    interceptionRouteRewrites\n});\nexport const ComponentMod = pageMod;\nexport default function nHandler(opts) {\n    return adapter({\n        ...opts,\n        IncrementalCache,\n        handler: render\n    });\n}\n\n//# sourceMappingURL=edge-ssr-app.js.map", "import(/* webpackMode: \"eager\", webpackExports: [\"LoginForm\"] */ \"F:\\\\CODE\\\\Project\\\\tempmail\\\\moemail\\\\app\\\\components\\\\auth\\\\login-form.tsx\");\n", "import(/* webpackMode: \"eager\", webpackExports: [\"LoginForm\"] */ \"F:\\\\CODE\\\\Project\\\\tempmail\\\\moemail\\\\app\\\\components\\\\auth\\\\login-form.tsx\");\n", "import * as React from \"react\"\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Card = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\r\n      \"rounded-lg border bg-card text-card-foreground shadow-sm\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nCard.displayName = \"Card\"\r\n\r\nconst CardHeader = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\r\n    {...props}\r\n  />\r\n))\r\nCardHeader.displayName = \"CardHeader\"\r\n\r\nconst CardTitle = React.forwardRef<\r\n  HTMLParagraphElement,\r\n  React.HTMLAttributes<HTMLHeadingElement>\r\n>(({ className, ...props }, ref) => (\r\n  <h3\r\n    ref={ref}\r\n    className={cn(\r\n      \"text-2xl font-semibold leading-none tracking-tight\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nCardTitle.displayName = \"CardTitle\"\r\n\r\nconst CardDescription = React.forwardRef<\r\n  HTMLParagraphElement,\r\n  React.HTMLAttributes<HTMLParagraphElement>\r\n>(({ className, ...props }, ref) => (\r\n  <p\r\n    ref={ref}\r\n    className={cn(\"text-sm text-muted-foreground\", className)}\r\n    {...props}\r\n  />\r\n))\r\nCardDescription.displayName = \"CardDescription\"\r\n\r\nconst CardContent = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\r\n))\r\nCardContent.displayName = \"CardContent\"\r\n\r\nconst CardFooter = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"flex items-center p-6 pt-0\", className)}\r\n    {...props}\r\n  />\r\n))\r\nCardFooter.displayName = \"CardFooter\"\r\n\r\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent } ", "// packages/core/primitive/src/primitive.tsx\nfunction composeEventHandlers(originalEventHandler, ourEventHandler, { checkForDefaultPrevented = true } = {}) {\n  return function handleEvent(event) {\n    originalEventHandler?.(event);\n    if (checkForDefaultPrevented === false || !event.defaultPrevented) {\n      return ourEventHandler?.(event);\n    }\n  };\n}\nexport {\n  composeEventHandlers\n};\n//# sourceMappingURL=index.mjs.map\n", "// packages/react/context/src/create-context.tsx\nimport * as React from \"react\";\nimport { jsx } from \"react/jsx-runtime\";\nfunction createContext2(rootComponentName, defaultContext) {\n  const Context = React.createContext(defaultContext);\n  const Provider = (props) => {\n    const { children, ...context } = props;\n    const value = React.useMemo(() => context, Object.values(context));\n    return /* @__PURE__ */ jsx(Context.Provider, { value, children });\n  };\n  Provider.displayName = rootComponentName + \"Provider\";\n  function useContext2(consumerName) {\n    const context = React.useContext(Context);\n    if (context) return context;\n    if (defaultContext !== void 0) return defaultContext;\n    throw new Error(`\\`${consumerName}\\` must be used within \\`${rootComponentName}\\``);\n  }\n  return [Provider, useContext2];\n}\nfunction createContextScope(scopeName, createContextScopeDeps = []) {\n  let defaultContexts = [];\n  function createContext3(rootComponentName, defaultContext) {\n    const BaseContext = React.createContext(defaultContext);\n    const index = defaultContexts.length;\n    defaultContexts = [...defaultContexts, defaultContext];\n    const Provider = (props) => {\n      const { scope, children, ...context } = props;\n      const Context = scope?.[scopeName]?.[index] || BaseContext;\n      const value = React.useMemo(() => context, Object.values(context));\n      return /* @__PURE__ */ jsx(Context.Provider, { value, children });\n    };\n    Provider.displayName = rootComponentName + \"Provider\";\n    function useContext2(consumerName, scope) {\n      const Context = scope?.[scopeName]?.[index] || BaseContext;\n      const context = React.useContext(Context);\n      if (context) return context;\n      if (defaultContext !== void 0) return defaultContext;\n      throw new Error(`\\`${consumerName}\\` must be used within \\`${rootComponentName}\\``);\n    }\n    return [Provider, useContext2];\n  }\n  const createScope = () => {\n    const scopeContexts = defaultContexts.map((defaultContext) => {\n      return React.createContext(defaultContext);\n    });\n    return function useScope(scope) {\n      const contexts = scope?.[scopeName] || scopeContexts;\n      return React.useMemo(\n        () => ({ [`__scope${scopeName}`]: { ...scope, [scopeName]: contexts } }),\n        [scope, contexts]\n      );\n    };\n  };\n  createScope.scopeName = scopeName;\n  return [createContext3, composeContextScopes(createScope, ...createContextScopeDeps)];\n}\nfunction composeContextScopes(...scopes) {\n  const baseScope = scopes[0];\n  if (scopes.length === 1) return baseScope;\n  const createScope = () => {\n    const scopeHooks = scopes.map((createScope2) => ({\n      useScope: createScope2(),\n      scopeName: createScope2.scopeName\n    }));\n    return function useComposedScopes(overrideScopes) {\n      const nextScopes = scopeHooks.reduce((nextScopes2, { useScope, scopeName }) => {\n        const scopeProps = useScope(overrideScopes);\n        const currentScope = scopeProps[`__scope${scopeName}`];\n        return { ...nextScopes2, ...currentScope };\n      }, {});\n      return React.useMemo(() => ({ [`__scope${baseScope.scopeName}`]: nextScopes }), [nextScopes]);\n    };\n  };\n  createScope.scopeName = baseScope.scopeName;\n  return createScope;\n}\nexport {\n  createContext2 as createContext,\n  createContextScope\n};\n//# sourceMappingURL=index.mjs.map\n", "// packages/react/compose-refs/src/compose-refs.tsx\nimport * as React from \"react\";\nfunction setRef(ref, value) {\n  if (typeof ref === \"function\") {\n    return ref(value);\n  } else if (ref !== null && ref !== void 0) {\n    ref.current = value;\n  }\n}\nfunction composeRefs(...refs) {\n  return (node) => {\n    let hasCleanup = false;\n    const cleanups = refs.map((ref) => {\n      const cleanup = setRef(ref, node);\n      if (!hasCleanup && typeof cleanup == \"function\") {\n        hasCleanup = true;\n      }\n      return cleanup;\n    });\n    if (hasCleanup) {\n      return () => {\n        for (let i = 0; i < cleanups.length; i++) {\n          const cleanup = cleanups[i];\n          if (typeof cleanup == \"function\") {\n            cleanup();\n          } else {\n            setRef(refs[i], null);\n          }\n        }\n      };\n    }\n  };\n}\nfunction useComposedRefs(...refs) {\n  return React.useCallback(composeRefs(...refs), refs);\n}\nexport {\n  composeRefs,\n  useComposedRefs\n};\n//# sourceMappingURL=index.mjs.map\n", "// src/slot.tsx\nimport * as React from \"react\";\nimport { composeRefs } from \"@radix-ui/react-compose-refs\";\nimport { Fragment as Fragment2, jsx } from \"react/jsx-runtime\";\n// @__NO_SIDE_EFFECTS__\nfunction createSlot(ownerName) {\n  const SlotClone = /* @__PURE__ */ createSlotClone(ownerName);\n  const Slot2 = React.forwardRef((props, forwardedRef) => {\n    const { children, ...slotProps } = props;\n    const childrenArray = React.Children.toArray(children);\n    const slottable = childrenArray.find(isSlottable);\n    if (slottable) {\n      const newElement = slottable.props.children;\n      const newChildren = childrenArray.map((child) => {\n        if (child === slottable) {\n          if (React.Children.count(newElement) > 1) return React.Children.only(null);\n          return React.isValidElement(newElement) ? newElement.props.children : null;\n        } else {\n          return child;\n        }\n      });\n      return /* @__PURE__ */ jsx(SlotClone, { ...slotProps, ref: forwardedRef, children: React.isValidElement(newElement) ? React.cloneElement(newElement, void 0, newChildren) : null });\n    }\n    return /* @__PURE__ */ jsx(SlotClone, { ...slotProps, ref: forwardedRef, children });\n  });\n  Slot2.displayName = `${ownerName}.Slot`;\n  return Slot2;\n}\nvar Slot = /* @__PURE__ */ createSlot(\"Slot\");\n// @__NO_SIDE_EFFECTS__\nfunction createSlotClone(ownerName) {\n  const SlotClone = React.forwardRef((props, forwardedRef) => {\n    const { children, ...slotProps } = props;\n    if (React.isValidElement(children)) {\n      const childrenRef = getElementRef(children);\n      const props2 = mergeProps(slotProps, children.props);\n      if (children.type !== React.Fragment) {\n        props2.ref = forwardedRef ? composeRefs(forwardedRef, childrenRef) : childrenRef;\n      }\n      return React.cloneElement(children, props2);\n    }\n    return React.Children.count(children) > 1 ? React.Children.only(null) : null;\n  });\n  SlotClone.displayName = `${ownerName}.SlotClone`;\n  return SlotClone;\n}\nvar SLOTTABLE_IDENTIFIER = Symbol(\"radix.slottable\");\n// @__NO_SIDE_EFFECTS__\nfunction createSlottable(ownerName) {\n  const Slottable2 = ({ children }) => {\n    return /* @__PURE__ */ jsx(Fragment2, { children });\n  };\n  Slottable2.displayName = `${ownerName}.Slottable`;\n  Slottable2.__radixId = SLOTTABLE_IDENTIFIER;\n  return Slottable2;\n}\nvar Slottable = /* @__PURE__ */ createSlottable(\"Slottable\");\nfunction isSlottable(child) {\n  return React.isValidElement(child) && typeof child.type === \"function\" && \"__radixId\" in child.type && child.type.__radixId === SLOTTABLE_IDENTIFIER;\n}\nfunction mergeProps(slotProps, childProps) {\n  const overrideProps = { ...childProps };\n  for (const propName in childProps) {\n    const slotPropValue = slotProps[propName];\n    const childPropValue = childProps[propName];\n    const isHandler = /^on[A-Z]/.test(propName);\n    if (isHandler) {\n      if (slotPropValue && childPropValue) {\n        overrideProps[propName] = (...args) => {\n          const result = childPropValue(...args);\n          slotPropValue(...args);\n          return result;\n        };\n      } else if (slotPropValue) {\n        overrideProps[propName] = slotPropValue;\n      }\n    } else if (propName === \"style\") {\n      overrideProps[propName] = { ...slotPropValue, ...childPropValue };\n    } else if (propName === \"className\") {\n      overrideProps[propName] = [slotPropValue, childPropValue].filter(Boolean).join(\" \");\n    }\n  }\n  return { ...slotProps, ...overrideProps };\n}\nfunction getElementRef(element) {\n  let getter = Object.getOwnPropertyDescriptor(element.props, \"ref\")?.get;\n  let mayWarn = getter && \"isReactWarning\" in getter && getter.isReactWarning;\n  if (mayWarn) {\n    return element.ref;\n  }\n  getter = Object.getOwnPropertyDescriptor(element, \"ref\")?.get;\n  mayWarn = getter && \"isReactWarning\" in getter && getter.isReactWarning;\n  if (mayWarn) {\n    return element.props.ref;\n  }\n  return element.props.ref || element.ref;\n}\nexport {\n  Slot as Root,\n  Slot,\n  Slottable,\n  createSlot,\n  createSlottable\n};\n//# sourceMappingURL=index.mjs.map\n", "import React from 'react';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { useComposedRefs } from '@radix-ui/react-compose-refs';\nimport { createSlot, type Slot } from '@radix-ui/react-slot';\n\ntype SlotProps = React.ComponentPropsWithoutRef<typeof Slot>;\ntype CollectionElement = HTMLElement;\ninterface CollectionProps extends SlotProps {\n  scope: any;\n}\n\n// We have resorted to returning slots directly rather than exposing primitives that can then\n// be slotted like `<CollectionItem as={Slot}>…</CollectionItem>`.\n// This is because we encountered issues with generic types that cannot be statically analysed\n// due to creating them dynamically via createCollection.\n\nfunction createCollection<ItemElement extends HTMLElement, ItemData = {}>(name: string) {\n  /* -----------------------------------------------------------------------------------------------\n   * CollectionProvider\n   * ---------------------------------------------------------------------------------------------*/\n\n  const PROVIDER_NAME = name + 'CollectionProvider';\n  const [createCollectionContext, createCollectionScope] = createContextScope(PROVIDER_NAME);\n\n  type ContextValue = {\n    collectionRef: React.RefObject<CollectionElement | null>;\n    itemMap: Map<\n      React.RefObject<ItemElement | null>,\n      { ref: React.RefObject<ItemElement | null> } & ItemData\n    >;\n  };\n\n  const [CollectionProviderImpl, useCollectionContext] = createCollectionContext<ContextValue>(\n    PROVIDER_NAME,\n    { collectionRef: { current: null }, itemMap: new Map() }\n  );\n\n  const CollectionProvider: React.FC<{ children?: React.ReactNode; scope: any }> = (props) => {\n    const { scope, children } = props;\n    const ref = React.useRef<CollectionElement>(null);\n    const itemMap = React.useRef<ContextValue['itemMap']>(new Map()).current;\n    return (\n      <CollectionProviderImpl scope={scope} itemMap={itemMap} collectionRef={ref}>\n        {children}\n      </CollectionProviderImpl>\n    );\n  };\n\n  CollectionProvider.displayName = PROVIDER_NAME;\n\n  /* -----------------------------------------------------------------------------------------------\n   * CollectionSlot\n   * ---------------------------------------------------------------------------------------------*/\n\n  const COLLECTION_SLOT_NAME = name + 'CollectionSlot';\n\n  const CollectionSlotImpl = createSlot(COLLECTION_SLOT_NAME);\n  const CollectionSlot = React.forwardRef<CollectionElement, CollectionProps>(\n    (props, forwardedRef) => {\n      const { scope, children } = props;\n      const context = useCollectionContext(COLLECTION_SLOT_NAME, scope);\n      const composedRefs = useComposedRefs(forwardedRef, context.collectionRef);\n      return <CollectionSlotImpl ref={composedRefs}>{children}</CollectionSlotImpl>;\n    }\n  );\n\n  CollectionSlot.displayName = COLLECTION_SLOT_NAME;\n\n  /* -----------------------------------------------------------------------------------------------\n   * CollectionItem\n   * ---------------------------------------------------------------------------------------------*/\n\n  const ITEM_SLOT_NAME = name + 'CollectionItemSlot';\n  const ITEM_DATA_ATTR = 'data-radix-collection-item';\n\n  type CollectionItemSlotProps = ItemData & {\n    children: React.ReactNode;\n    scope: any;\n  };\n\n  const CollectionItemSlotImpl = createSlot(ITEM_SLOT_NAME);\n  const CollectionItemSlot = React.forwardRef<ItemElement, CollectionItemSlotProps>(\n    (props, forwardedRef) => {\n      const { scope, children, ...itemData } = props;\n      const ref = React.useRef<ItemElement>(null);\n      const composedRefs = useComposedRefs(forwardedRef, ref);\n      const context = useCollectionContext(ITEM_SLOT_NAME, scope);\n\n      React.useEffect(() => {\n        context.itemMap.set(ref, { ref, ...(itemData as unknown as ItemData) });\n        return () => void context.itemMap.delete(ref);\n      });\n\n      return (\n        <CollectionItemSlotImpl {...{ [ITEM_DATA_ATTR]: '' }} ref={composedRefs}>\n          {children}\n        </CollectionItemSlotImpl>\n      );\n    }\n  );\n\n  CollectionItemSlot.displayName = ITEM_SLOT_NAME;\n\n  /* -----------------------------------------------------------------------------------------------\n   * useCollection\n   * ---------------------------------------------------------------------------------------------*/\n\n  function useCollection(scope: any) {\n    const context = useCollectionContext(name + 'CollectionConsumer', scope);\n\n    const getItems = React.useCallback(() => {\n      const collectionNode = context.collectionRef.current;\n      if (!collectionNode) return [];\n      const orderedNodes = Array.from(collectionNode.querySelectorAll(`[${ITEM_DATA_ATTR}]`));\n      const items = Array.from(context.itemMap.values());\n      const orderedItems = items.sort(\n        (a, b) => orderedNodes.indexOf(a.ref.current!) - orderedNodes.indexOf(b.ref.current!)\n      );\n      return orderedItems;\n    }, [context.collectionRef, context.itemMap]);\n\n    return getItems;\n  }\n\n  return [\n    { Provider: CollectionProvider, Slot: CollectionSlot, ItemSlot: CollectionItemSlot },\n    useCollection,\n    createCollectionScope,\n  ] as const;\n}\n\nexport { createCollection };\nexport type { CollectionProps };\n", "import React from 'react';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { useComposedRefs } from '@radix-ui/react-compose-refs';\nimport { createSlot, type Slot } from '@radix-ui/react-slot';\nimport type { EntryOf } from './ordered-dictionary';\nimport { OrderedDict } from './ordered-dictionary';\n\ntype SlotProps = React.ComponentPropsWithoutRef<typeof Slot>;\ntype CollectionElement = HTMLElement;\ninterface CollectionProps extends SlotProps {\n  scope: any;\n}\n\ninterface BaseItemData {\n  id?: string;\n}\n\ntype ItemDataWithElement<\n  ItemData extends BaseItemData,\n  ItemElement extends HTMLElement,\n> = ItemData & {\n  element: ItemElement;\n};\n\ntype ItemMap<ItemElement extends HTMLElement, ItemData extends BaseItemData> = OrderedDict<\n  ItemElement,\n  ItemDataWithElement<ItemData, ItemElement>\n>;\n\nfunction createCollection<\n  ItemElement extends HTMLElement,\n  ItemData extends BaseItemData = BaseItemData,\n>(name: string) {\n  /* -----------------------------------------------------------------------------------------------\n   * CollectionProvider\n   * ---------------------------------------------------------------------------------------------*/\n\n  const PROVIDER_NAME = name + 'CollectionProvider';\n  const [createCollectionContext, createCollectionScope] = createContextScope(PROVIDER_NAME);\n\n  type ContextValue = {\n    collectionElement: CollectionElement | null;\n    collectionRef: React.Ref<CollectionElement | null>;\n    collectionRefObject: React.RefObject<CollectionElement | null>;\n    itemMap: ItemMap<ItemElement, ItemData>;\n    setItemMap: React.Dispatch<React.SetStateAction<ItemMap<ItemElement, ItemData>>>;\n  };\n\n  const [CollectionContextProvider, useCollectionContext] = createCollectionContext<ContextValue>(\n    PROVIDER_NAME,\n    {\n      collectionElement: null,\n      collectionRef: { current: null },\n      collectionRefObject: { current: null },\n      itemMap: new OrderedDict(),\n      setItemMap: () => void 0,\n    }\n  );\n\n  type CollectionState = [\n    ItemMap: ItemMap<ItemElement, ItemData>,\n    SetItemMap: React.Dispatch<React.SetStateAction<ItemMap<ItemElement, ItemData>>>,\n  ];\n\n  const CollectionProvider: React.FC<{\n    children?: React.ReactNode;\n    scope: any;\n    state?: CollectionState;\n  }> = ({ state, ...props }) => {\n    return state ? (\n      <CollectionProviderImpl {...props} state={state} />\n    ) : (\n      <CollectionInit {...props} />\n    );\n  };\n  CollectionProvider.displayName = PROVIDER_NAME;\n\n  const CollectionInit: React.FC<{\n    children?: React.ReactNode;\n    scope: any;\n  }> = (props) => {\n    const state = useInitCollection();\n    return <CollectionProviderImpl {...props} state={state} />;\n  };\n  CollectionInit.displayName = PROVIDER_NAME + 'Init';\n\n  const CollectionProviderImpl: React.FC<{\n    children?: React.ReactNode;\n    scope: any;\n    state: CollectionState;\n  }> = (props) => {\n    const { scope, children, state } = props;\n    const ref = React.useRef<CollectionElement>(null);\n    const [collectionElement, setCollectionElement] = React.useState<CollectionElement | null>(\n      null\n    );\n    const composeRefs = useComposedRefs(ref, setCollectionElement);\n    const [itemMap, setItemMap] = state;\n\n    React.useEffect(() => {\n      if (!collectionElement) return;\n\n      const observer = getChildListObserver(() => {\n        // setItemMap((map) => {\n        //   const copy = new OrderedDict(map).toSorted(([, a], [, b]) =>\n        //     !a.element || !b.element ? 0 : isElementPreceding(a.element, b.element) ? -1 : 1\n        //   );\n        //   // check if the order has changed\n        //   let index = -1;\n        //   for (const entry of copy) {\n        //     index++;\n        //     const key = map.keyAt(index)!;\n        //     const [copyKey] = entry;\n        //     if (key !== copyKey) {\n        //       // order has changed!\n        //       return copy;\n        //     }\n        //   }\n        //   return map;\n        // });\n      });\n      observer.observe(collectionElement, {\n        childList: true,\n        subtree: true,\n      });\n      return () => {\n        observer.disconnect();\n      };\n    }, [collectionElement]);\n\n    return (\n      <CollectionContextProvider\n        scope={scope}\n        itemMap={itemMap}\n        setItemMap={setItemMap}\n        collectionRef={composeRefs}\n        collectionRefObject={ref}\n        collectionElement={collectionElement}\n      >\n        {children}\n      </CollectionContextProvider>\n    );\n  };\n\n  CollectionProviderImpl.displayName = PROVIDER_NAME + 'Impl';\n\n  /* -----------------------------------------------------------------------------------------------\n   * CollectionSlot\n   * ---------------------------------------------------------------------------------------------*/\n\n  const COLLECTION_SLOT_NAME = name + 'CollectionSlot';\n\n  const CollectionSlotImpl = createSlot(COLLECTION_SLOT_NAME);\n  const CollectionSlot = React.forwardRef<CollectionElement, CollectionProps>(\n    (props, forwardedRef) => {\n      const { scope, children } = props;\n      const context = useCollectionContext(COLLECTION_SLOT_NAME, scope);\n      const composedRefs = useComposedRefs(forwardedRef, context.collectionRef);\n      return <CollectionSlotImpl ref={composedRefs}>{children}</CollectionSlotImpl>;\n    }\n  );\n\n  CollectionSlot.displayName = COLLECTION_SLOT_NAME;\n\n  /* -----------------------------------------------------------------------------------------------\n   * CollectionItem\n   * ---------------------------------------------------------------------------------------------*/\n\n  const ITEM_SLOT_NAME = name + 'CollectionItemSlot';\n  const ITEM_DATA_ATTR = 'data-radix-collection-item';\n\n  type CollectionItemSlotProps = ItemData & {\n    children: React.ReactNode;\n    scope: any;\n  };\n\n  const CollectionItemSlotImpl = createSlot(ITEM_SLOT_NAME);\n  const CollectionItemSlot = React.forwardRef<ItemElement, CollectionItemSlotProps>(\n    (props, forwardedRef) => {\n      const { scope, children, ...itemData } = props;\n      const ref = React.useRef<ItemElement>(null);\n      const [element, setElement] = React.useState<ItemElement | null>(null);\n      const composedRefs = useComposedRefs(forwardedRef, ref, setElement);\n      const context = useCollectionContext(ITEM_SLOT_NAME, scope);\n\n      const { setItemMap } = context;\n\n      const itemDataRef = React.useRef(itemData);\n      if (!shallowEqual(itemDataRef.current, itemData)) {\n        itemDataRef.current = itemData;\n      }\n      const memoizedItemData = itemDataRef.current;\n\n      React.useEffect(() => {\n        const itemData = memoizedItemData;\n        setItemMap((map) => {\n          if (!element) {\n            return map;\n          }\n\n          if (!map.has(element)) {\n            map.set(element, { ...(itemData as unknown as ItemData), element });\n            return map.toSorted(sortByDocumentPosition);\n          }\n\n          return map\n            .set(element, { ...(itemData as unknown as ItemData), element })\n            .toSorted(sortByDocumentPosition);\n        });\n\n        return () => {\n          setItemMap((map) => {\n            if (!element || !map.has(element)) {\n              return map;\n            }\n            map.delete(element);\n            return new OrderedDict(map);\n          });\n        };\n      }, [element, memoizedItemData, setItemMap]);\n\n      return (\n        <CollectionItemSlotImpl {...{ [ITEM_DATA_ATTR]: '' }} ref={composedRefs as any}>\n          {children}\n        </CollectionItemSlotImpl>\n      );\n    }\n  );\n\n  CollectionItemSlot.displayName = ITEM_SLOT_NAME;\n\n  /* -----------------------------------------------------------------------------------------------\n   * useInitCollection\n   * ---------------------------------------------------------------------------------------------*/\n\n  function useInitCollection() {\n    return React.useState<ItemMap<ItemElement, ItemData>>(new OrderedDict());\n  }\n\n  /* -----------------------------------------------------------------------------------------------\n   * useCollection\n   * ---------------------------------------------------------------------------------------------*/\n\n  function useCollection(scope: any) {\n    const { itemMap } = useCollectionContext(name + 'CollectionConsumer', scope);\n\n    return itemMap;\n  }\n\n  const functions = {\n    createCollectionScope,\n    useCollection,\n    useInitCollection,\n  };\n\n  return [\n    { Provider: CollectionProvider, Slot: CollectionSlot, ItemSlot: CollectionItemSlot },\n    functions,\n  ] as const;\n}\n\nexport { createCollection };\nexport type { CollectionProps };\n\nfunction shallowEqual(a: any, b: any) {\n  if (a === b) return true;\n  if (typeof a !== 'object' || typeof b !== 'object') return false;\n  if (a == null || b == null) return false;\n  const keysA = Object.keys(a);\n  const keysB = Object.keys(b);\n  if (keysA.length !== keysB.length) return false;\n  for (const key of keysA) {\n    if (!Object.prototype.hasOwnProperty.call(b, key)) return false;\n    if (a[key] !== b[key]) return false;\n  }\n  return true;\n}\n\nfunction isElementPreceding(a: Element, b: Element) {\n  return !!(b.compareDocumentPosition(a) & Node.DOCUMENT_POSITION_PRECEDING);\n}\n\nfunction sortByDocumentPosition<E extends HTMLElement, T extends BaseItemData>(\n  a: EntryOf<ItemMap<E, T>>,\n  b: EntryOf<ItemMap<E, T>>\n) {\n  return !a[1].element || !b[1].element\n    ? 0\n    : isElementPreceding(a[1].element, b[1].element)\n      ? -1\n      : 1;\n}\n\nfunction getChildListObserver(callback: () => void) {\n  const observer = new MutationObserver((mutationsList) => {\n    for (const mutation of mutationsList) {\n      if (mutation.type === 'childList') {\n        callback();\n        return;\n      }\n    }\n  });\n\n  return observer;\n}\n", "// Not a real member because it shouldn't be accessible, but the super class\n// calls `set` which needs to read the instanciation state, so it can't be a\n// private member.\nconst __instanciated = new WeakMap<OrderedDict<any, any>, boolean>();\nexport class OrderedDict<K, V> extends Map<K, V> {\n  #keys: K[];\n\n  constructor(iterable?: Iterable<readonly [K, V]> | null | undefined);\n  constructor(entries?: readonly (readonly [K, V])[] | null) {\n    super(entries);\n    this.#keys = [...super.keys()];\n    __instanciated.set(this, true);\n  }\n\n  set(key: K, value: V) {\n    if (__instanciated.get(this)) {\n      if (this.has(key)) {\n        this.#keys[this.#keys.indexOf(key)] = key;\n      } else {\n        this.#keys.push(key);\n      }\n    }\n    super.set(key, value);\n    return this;\n  }\n\n  insert(index: number, key: K, value: V) {\n    const has = this.has(key);\n    const length = this.#keys.length;\n    const relativeIndex = toSafeInteger(index);\n    let actualIndex = relativeIndex >= 0 ? relativeIndex : length + relativeIndex;\n    const safeIndex = actualIndex < 0 || actualIndex >= length ? -1 : actualIndex;\n\n    if (safeIndex === this.size || (has && safeIndex === this.size - 1) || safeIndex === -1) {\n      this.set(key, value);\n      return this;\n    }\n\n    const size = this.size + (has ? 0 : 1);\n\n    // If you insert at, say, -2, without this bit you'd replace the\n    // second-to-last item and push the rest up one, which means the new item is\n    // 3rd to last. This isn't very intuitive; inserting at -2 is more like\n    // saying \"make this item the second to last\".\n    if (relativeIndex < 0) {\n      actualIndex++;\n    }\n\n    const keys = [...this.#keys];\n    let nextValue: V | undefined;\n    let shouldSkip = false;\n    for (let i = actualIndex; i < size; i++) {\n      if (actualIndex === i) {\n        let nextKey = keys[i]!;\n        if (keys[i] === key) {\n          nextKey = keys[i + 1]!;\n        }\n        if (has) {\n          // delete first to ensure that the item is moved to the end\n          this.delete(key);\n        }\n        nextValue = this.get(nextKey);\n        this.set(key, value);\n      } else {\n        if (!shouldSkip && keys[i - 1] === key) {\n          shouldSkip = true;\n        }\n        const currentKey = keys[shouldSkip ? i : i - 1]!;\n        const currentValue = nextValue!;\n        nextValue = this.get(currentKey);\n        this.delete(currentKey);\n        this.set(currentKey, currentValue);\n      }\n    }\n    return this;\n  }\n\n  with(index: number, key: K, value: V) {\n    const copy = new OrderedDict(this);\n    copy.insert(index, key, value);\n    return copy;\n  }\n\n  before(key: K) {\n    const index = this.#keys.indexOf(key) - 1;\n    if (index < 0) {\n      return undefined;\n    }\n    return this.entryAt(index);\n  }\n\n  /**\n   * Sets a new key-value pair at the position before the given key.\n   */\n  setBefore(key: K, newKey: K, value: V) {\n    const index = this.#keys.indexOf(key);\n    if (index === -1) {\n      return this;\n    }\n    return this.insert(index, newKey, value);\n  }\n\n  after(key: K) {\n    let index = this.#keys.indexOf(key);\n    index = index === -1 || index === this.size - 1 ? -1 : index + 1;\n    if (index === -1) {\n      return undefined;\n    }\n    return this.entryAt(index);\n  }\n\n  /**\n   * Sets a new key-value pair at the position after the given key.\n   */\n  setAfter(key: K, newKey: K, value: V) {\n    const index = this.#keys.indexOf(key);\n    if (index === -1) {\n      return this;\n    }\n    return this.insert(index + 1, newKey, value);\n  }\n\n  first() {\n    return this.entryAt(0);\n  }\n\n  last() {\n    return this.entryAt(-1);\n  }\n\n  clear() {\n    this.#keys = [];\n    return super.clear();\n  }\n\n  delete(key: K) {\n    const deleted = super.delete(key);\n    if (deleted) {\n      this.#keys.splice(this.#keys.indexOf(key), 1);\n    }\n    return deleted;\n  }\n\n  deleteAt(index: number) {\n    const key = this.keyAt(index);\n    if (key !== undefined) {\n      return this.delete(key);\n    }\n    return false;\n  }\n\n  at(index: number) {\n    const key = at(this.#keys, index);\n    if (key !== undefined) {\n      return this.get(key);\n    }\n  }\n\n  entryAt(index: number): [K, V] | undefined {\n    const key = at(this.#keys, index);\n    if (key !== undefined) {\n      return [key, this.get(key)!];\n    }\n  }\n\n  indexOf(key: K) {\n    return this.#keys.indexOf(key);\n  }\n\n  keyAt(index: number) {\n    return at(this.#keys, index);\n  }\n\n  from(key: K, offset: number) {\n    const index = this.indexOf(key);\n    if (index === -1) {\n      return undefined;\n    }\n    let dest = index + offset;\n    if (dest < 0) dest = 0;\n    if (dest >= this.size) dest = this.size - 1;\n    return this.at(dest);\n  }\n\n  keyFrom(key: K, offset: number) {\n    const index = this.indexOf(key);\n    if (index === -1) {\n      return undefined;\n    }\n    let dest = index + offset;\n    if (dest < 0) dest = 0;\n    if (dest >= this.size) dest = this.size - 1;\n    return this.keyAt(dest);\n  }\n\n  find(\n    predicate: (entry: [K, V], index: number, dictionary: OrderedDict<K, V>) => boolean,\n    thisArg?: any\n  ) {\n    let index = 0;\n    for (const entry of this) {\n      if (Reflect.apply(predicate, thisArg, [entry, index, this])) {\n        return entry;\n      }\n      index++;\n    }\n    return undefined;\n  }\n\n  findIndex(\n    predicate: (entry: [K, V], index: number, dictionary: OrderedDict<K, V>) => boolean,\n    thisArg?: any\n  ) {\n    let index = 0;\n    for (const entry of this) {\n      if (Reflect.apply(predicate, thisArg, [entry, index, this])) {\n        return index;\n      }\n      index++;\n    }\n    return -1;\n  }\n\n  filter<KK extends K, VV extends V>(\n    predicate: (entry: [K, V], index: number, dict: OrderedDict<K, V>) => entry is [KK, VV],\n    thisArg?: any\n  ): OrderedDict<KK, VV>;\n\n  filter(\n    predicate: (entry: [K, V], index: number, dictionary: OrderedDict<K, V>) => unknown,\n    thisArg?: any\n  ): OrderedDict<K, V>;\n\n  filter(\n    predicate: (entry: [K, V], index: number, dictionary: OrderedDict<K, V>) => unknown,\n    thisArg?: any\n  ) {\n    const entries: Array<[K, V]> = [];\n    let index = 0;\n    for (const entry of this) {\n      if (Reflect.apply(predicate, thisArg, [entry, index, this])) {\n        entries.push(entry);\n      }\n      index++;\n    }\n    return new OrderedDict(entries);\n  }\n\n  map<U>(\n    callbackfn: (entry: [K, V], index: number, dictionary: OrderedDict<K, V>) => U,\n    thisArg?: any\n  ): OrderedDict<K, U> {\n    const entries: [K, U][] = [];\n    let index = 0;\n    for (const entry of this) {\n      entries.push([entry[0], Reflect.apply(callbackfn, thisArg, [entry, index, this])]);\n      index++;\n    }\n    return new OrderedDict(entries);\n  }\n\n  reduce(\n    callbackfn: (\n      previousValue: [K, V],\n      currentEntry: [K, V],\n      currentIndex: number,\n      dictionary: OrderedDict<K, V>\n    ) => [K, V]\n  ): [K, V];\n  reduce(\n    callbackfn: (\n      previousValue: [K, V],\n      currentEntry: [K, V],\n      currentIndex: number,\n      dictionary: OrderedDict<K, V>\n    ) => [K, V],\n    initialValue: [K, V]\n  ): [K, V];\n  reduce<U>(\n    callbackfn: (\n      previousValue: U,\n      currentEntry: [K, V],\n      currentIndex: number,\n      dictionary: OrderedDict<K, V>\n    ) => U,\n    initialValue: U\n  ): U;\n\n  reduce<U>(\n    ...args: [\n      (\n        previousValue: U,\n        currentEntry: [K, V],\n        currentIndex: number,\n        dictionary: OrderedDict<K, V>\n      ) => U,\n      U?,\n    ]\n  ) {\n    const [callbackfn, initialValue] = args;\n    let index = 0;\n    let accumulator = initialValue ?? this.at(0)!;\n    for (const entry of this) {\n      if (index === 0 && args.length === 1) {\n        accumulator = entry as any;\n      } else {\n        accumulator = Reflect.apply(callbackfn, this, [accumulator, entry, index, this]);\n      }\n      index++;\n    }\n    return accumulator;\n  }\n\n  reduceRight(\n    callbackfn: (\n      previousValue: [K, V],\n      currentEntry: [K, V],\n      currentIndex: number,\n      dictionary: OrderedDict<K, V>\n    ) => [K, V]\n  ): [K, V];\n  reduceRight(\n    callbackfn: (\n      previousValue: [K, V],\n      currentEntry: [K, V],\n      currentIndex: number,\n      dictionary: OrderedDict<K, V>\n    ) => [K, V],\n    initialValue: [K, V]\n  ): [K, V];\n  reduceRight<U>(\n    callbackfn: (\n      previousValue: [K, V],\n      currentValue: U,\n      currentIndex: number,\n      dictionary: OrderedDict<K, V>\n    ) => U,\n    initialValue: U\n  ): U;\n\n  reduceRight<U>(\n    ...args: [\n      (\n        previousValue: U,\n        currentEntry: [K, V],\n        currentIndex: number,\n        dictionary: OrderedDict<K, V>\n      ) => U,\n      U?,\n    ]\n  ) {\n    const [callbackfn, initialValue] = args;\n    let accumulator = initialValue ?? this.at(-1)!;\n    for (let index = this.size - 1; index >= 0; index--) {\n      const entry = this.at(index)!;\n      if (index === this.size - 1 && args.length === 1) {\n        accumulator = entry as any;\n      } else {\n        accumulator = Reflect.apply(callbackfn, this, [accumulator, entry, index, this]);\n      }\n    }\n    return accumulator;\n  }\n\n  toSorted(compareFn?: (a: [K, V], b: [K, V]) => number): OrderedDict<K, V> {\n    const entries = [...this.entries()].sort(compareFn);\n    return new OrderedDict(entries);\n  }\n\n  toReversed(): OrderedDict<K, V> {\n    const reversed = new OrderedDict<K, V>();\n    for (let index = this.size - 1; index >= 0; index--) {\n      const key = this.keyAt(index)!;\n      const element = this.get(key)!;\n      reversed.set(key, element);\n    }\n    return reversed;\n  }\n\n  toSpliced(start: number, deleteCount?: number): OrderedDict<K, V>;\n  toSpliced(start: number, deleteCount: number, ...items: [K, V][]): OrderedDict<K, V>;\n\n  toSpliced(...args: [start: number, deleteCount: number, ...items: [K, V][]]) {\n    const entries = [...this.entries()];\n    entries.splice(...args);\n    return new OrderedDict(entries);\n  }\n\n  slice(start?: number, end?: number) {\n    const result = new OrderedDict<K, V>();\n    let stop = this.size - 1;\n\n    if (start === undefined) {\n      return result;\n    }\n\n    if (start < 0) {\n      start = start + this.size;\n    }\n\n    if (end !== undefined && end > 0) {\n      stop = end - 1;\n    }\n\n    for (let index = start; index <= stop; index++) {\n      const key = this.keyAt(index)!;\n      const element = this.get(key)!;\n      result.set(key, element);\n    }\n    return result;\n  }\n\n  every(\n    predicate: (entry: [K, V], index: number, dictionary: OrderedDict<K, V>) => unknown,\n    thisArg?: any\n  ) {\n    let index = 0;\n    for (const entry of this) {\n      if (!Reflect.apply(predicate, thisArg, [entry, index, this])) {\n        return false;\n      }\n      index++;\n    }\n    return true;\n  }\n\n  some(\n    predicate: (entry: [K, V], index: number, dictionary: OrderedDict<K, V>) => unknown,\n    thisArg?: any\n  ) {\n    let index = 0;\n    for (const entry of this) {\n      if (Reflect.apply(predicate, thisArg, [entry, index, this])) {\n        return true;\n      }\n      index++;\n    }\n    return false;\n  }\n}\n\nexport type KeyOf<D extends OrderedDict<any, any>> =\n  D extends OrderedDict<infer K, any> ? K : never;\nexport type ValueOf<D extends OrderedDict<any, any>> =\n  D extends OrderedDict<any, infer V> ? V : never;\nexport type EntryOf<D extends OrderedDict<any, any>> = [KeyOf<D>, ValueOf<D>];\nexport type KeyFrom<E extends EntryOf<any>> = E[0];\nexport type ValueFrom<E extends EntryOf<any>> = E[1];\n\nfunction at<T>(array: ArrayLike<T>, index: number): T | undefined {\n  if ('at' in Array.prototype) {\n    return Array.prototype.at.call(array, index);\n  }\n  const actualIndex = toSafeIndex(array, index);\n  return actualIndex === -1 ? undefined : array[actualIndex];\n}\n\nfunction toSafeIndex(array: ArrayLike<any>, index: number) {\n  const length = array.length;\n  const relativeIndex = toSafeInteger(index);\n  const actualIndex = relativeIndex >= 0 ? relativeIndex : length + relativeIndex;\n  return actualIndex < 0 || actualIndex >= length ? -1 : actualIndex;\n}\n\nfunction toSafeInteger(number: number) {\n  // eslint-disable-next-line no-self-compare\n  return number !== number || number === 0 ? 0 : Math.trunc(number);\n}\n", "// packages/react/use-layout-effect/src/use-layout-effect.tsx\nimport * as React from \"react\";\nvar useLayoutEffect2 = globalThis?.document ? React.useLayoutEffect : () => {\n};\nexport {\n  useLayoutEffect2 as useLayoutEffect\n};\n//# sourceMappingURL=index.mjs.map\n", "// packages/react/id/src/id.tsx\nimport * as React from \"react\";\nimport { useLayoutEffect } from \"@radix-ui/react-use-layout-effect\";\nvar useReactId = React[\" useId \".trim().toString()] || (() => void 0);\nvar count = 0;\nfunction useId(deterministicId) {\n  const [id, setId] = React.useState(useReactId());\n  useLayoutEffect(() => {\n    if (!deterministicId) setId((reactId) => reactId ?? String(count++));\n  }, [deterministicId]);\n  return deterministicId || (id ? `radix-${id}` : \"\");\n}\nexport {\n  useId\n};\n//# sourceMappingURL=index.mjs.map\n", "// src/primitive.tsx\nimport * as React from \"react\";\nimport * as ReactDOM from \"react-dom\";\nimport { createSlot } from \"@radix-ui/react-slot\";\nimport { jsx } from \"react/jsx-runtime\";\nvar NODES = [\n  \"a\",\n  \"button\",\n  \"div\",\n  \"form\",\n  \"h2\",\n  \"h3\",\n  \"img\",\n  \"input\",\n  \"label\",\n  \"li\",\n  \"nav\",\n  \"ol\",\n  \"p\",\n  \"select\",\n  \"span\",\n  \"svg\",\n  \"ul\"\n];\nvar Primitive = NODES.reduce((primitive, node) => {\n  const Slot = createSlot(`Primitive.${node}`);\n  const Node = React.forwardRef((props, forwardedRef) => {\n    const { asChild, ...primitiveProps } = props;\n    const Comp = asChild ? Slot : node;\n    if (typeof window !== \"undefined\") {\n      window[Symbol.for(\"radix-ui\")] = true;\n    }\n    return /* @__PURE__ */ jsx(Comp, { ...primitiveProps, ref: forwardedRef });\n  });\n  Node.displayName = `Primitive.${node}`;\n  return { ...primitive, [node]: Node };\n}, {});\nfunction dispatchDiscreteCustomEvent(target, event) {\n  if (target) ReactDOM.flushSync(() => target.dispatchEvent(event));\n}\nvar Root = Primitive;\nexport {\n  Primitive,\n  Root,\n  dispatchDiscreteCustomEvent\n};\n//# sourceMappingURL=index.mjs.map\n", "// packages/react/use-callback-ref/src/use-callback-ref.tsx\nimport * as React from \"react\";\nfunction useCallbackRef(callback) {\n  const callbackRef = React.useRef(callback);\n  React.useEffect(() => {\n    callbackRef.current = callback;\n  });\n  return React.useMemo(() => (...args) => callbackRef.current?.(...args), []);\n}\nexport {\n  useCallbackRef\n};\n//# sourceMappingURL=index.mjs.map\n", "// src/use-controllable-state.tsx\nimport * as React from \"react\";\nimport { useLayoutEffect } from \"@radix-ui/react-use-layout-effect\";\nvar useInsertionEffect = React[\" useInsertionEffect \".trim().toString()] || useLayoutEffect;\nfunction useControllableState({\n  prop,\n  defaultProp,\n  onChange = () => {\n  },\n  caller\n}) {\n  const [uncontrolledProp, setUncontrolledProp, onChangeRef] = useUncontrolledState({\n    defaultProp,\n    onChange\n  });\n  const isControlled = prop !== void 0;\n  const value = isControlled ? prop : uncontrolledProp;\n  if (true) {\n    const isControlledRef = React.useRef(prop !== void 0);\n    React.useEffect(() => {\n      const wasControlled = isControlledRef.current;\n      if (wasControlled !== isControlled) {\n        const from = wasControlled ? \"controlled\" : \"uncontrolled\";\n        const to = isControlled ? \"controlled\" : \"uncontrolled\";\n        console.warn(\n          `${caller} is changing from ${from} to ${to}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`\n        );\n      }\n      isControlledRef.current = isControlled;\n    }, [isControlled, caller]);\n  }\n  const setValue = React.useCallback(\n    (nextValue) => {\n      if (isControlled) {\n        const value2 = isFunction(nextValue) ? nextValue(prop) : nextValue;\n        if (value2 !== prop) {\n          onChangeRef.current?.(value2);\n        }\n      } else {\n        setUncontrolledProp(nextValue);\n      }\n    },\n    [isControlled, prop, setUncontrolledProp, onChangeRef]\n  );\n  return [value, setValue];\n}\nfunction useUncontrolledState({\n  defaultProp,\n  onChange\n}) {\n  const [value, setValue] = React.useState(defaultProp);\n  const prevValueRef = React.useRef(value);\n  const onChangeRef = React.useRef(onChange);\n  useInsertionEffect(() => {\n    onChangeRef.current = onChange;\n  }, [onChange]);\n  React.useEffect(() => {\n    if (prevValueRef.current !== value) {\n      onChangeRef.current?.(value);\n      prevValueRef.current = value;\n    }\n  }, [value, prevValueRef]);\n  return [value, setValue, onChangeRef];\n}\nfunction isFunction(value) {\n  return typeof value === \"function\";\n}\n\n// src/use-controllable-state-reducer.tsx\nimport * as React2 from \"react\";\nimport { useEffectEvent } from \"@radix-ui/react-use-effect-event\";\nvar SYNC_STATE = Symbol(\"RADIX:SYNC_STATE\");\nfunction useControllableStateReducer(reducer, userArgs, initialArg, init) {\n  const { prop: controlledState, defaultProp, onChange: onChangeProp, caller } = userArgs;\n  const isControlled = controlledState !== void 0;\n  const onChange = useEffectEvent(onChangeProp);\n  if (true) {\n    const isControlledRef = React2.useRef(controlledState !== void 0);\n    React2.useEffect(() => {\n      const wasControlled = isControlledRef.current;\n      if (wasControlled !== isControlled) {\n        const from = wasControlled ? \"controlled\" : \"uncontrolled\";\n        const to = isControlled ? \"controlled\" : \"uncontrolled\";\n        console.warn(\n          `${caller} is changing from ${from} to ${to}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`\n        );\n      }\n      isControlledRef.current = isControlled;\n    }, [isControlled, caller]);\n  }\n  const args = [{ ...initialArg, state: defaultProp }];\n  if (init) {\n    args.push(init);\n  }\n  const [internalState, dispatch] = React2.useReducer(\n    (state2, action) => {\n      if (action.type === SYNC_STATE) {\n        return { ...state2, state: action.state };\n      }\n      const next = reducer(state2, action);\n      if (isControlled && !Object.is(next.state, state2.state)) {\n        onChange(next.state);\n      }\n      return next;\n    },\n    ...args\n  );\n  const uncontrolledState = internalState.state;\n  const prevValueRef = React2.useRef(uncontrolledState);\n  React2.useEffect(() => {\n    if (prevValueRef.current !== uncontrolledState) {\n      prevValueRef.current = uncontrolledState;\n      if (!isControlled) {\n        onChange(uncontrolledState);\n      }\n    }\n  }, [onChange, uncontrolledState, prevValueRef, isControlled]);\n  const state = React2.useMemo(() => {\n    const isControlled2 = controlledState !== void 0;\n    if (isControlled2) {\n      return { ...internalState, state: controlledState };\n    }\n    return internalState;\n  }, [internalState, controlledState]);\n  React2.useEffect(() => {\n    if (isControlled && !Object.is(controlledState, internalState.state)) {\n      dispatch({ type: SYNC_STATE, state: controlledState });\n    }\n  }, [controlledState, internalState.state, isControlled]);\n  return [state, dispatch];\n}\nexport {\n  useControllableState,\n  useControllableStateReducer\n};\n//# sourceMappingURL=index.mjs.map\n", "// packages/react/direction/src/direction.tsx\nimport * as React from \"react\";\nimport { jsx } from \"react/jsx-runtime\";\nvar DirectionContext = React.createContext(void 0);\nvar DirectionProvider = (props) => {\n  const { dir, children } = props;\n  return /* @__PURE__ */ jsx(DirectionContext.Provider, { value: dir, children });\n};\nfunction useDirection(localDir) {\n  const globalDir = React.useContext(DirectionContext);\n  return localDir || globalDir || \"ltr\";\n}\nvar Provider = DirectionProvider;\nexport {\n  DirectionProvider,\n  Provider,\n  useDirection\n};\n//# sourceMappingURL=index.mjs.map\n", "import * as React from 'react';\nimport { composeEventHandlers } from '@radix-ui/primitive';\nimport { createCollection } from '@radix-ui/react-collection';\nimport { useComposedRefs } from '@radix-ui/react-compose-refs';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { useId } from '@radix-ui/react-id';\nimport { Primitive } from '@radix-ui/react-primitive';\nimport { useCallbackRef } from '@radix-ui/react-use-callback-ref';\nimport { useControllableState } from '@radix-ui/react-use-controllable-state';\nimport { useDirection } from '@radix-ui/react-direction';\n\nimport type { Scope } from '@radix-ui/react-context';\n\nconst ENTRY_FOCUS = 'rovingFocusGroup.onEntryFocus';\nconst EVENT_OPTIONS = { bubbles: false, cancelable: true };\n\n/* -------------------------------------------------------------------------------------------------\n * RovingFocusGroup\n * -----------------------------------------------------------------------------------------------*/\n\nconst GROUP_NAME = 'RovingFocusGroup';\n\ntype ItemData = { id: string; focusable: boolean; active: boolean };\nconst [Collection, useCollection, createCollectionScope] = createCollection<\n  HTMLSpanElement,\n  ItemData\n>(GROUP_NAME);\n\ntype ScopedProps<P> = P & { __scopeRovingFocusGroup?: Scope };\nconst [createRovingFocusGroupContext, createRovingFocusGroupScope] = createContextScope(\n  GROUP_NAME,\n  [createCollectionScope]\n);\n\ntype Orientation = React.AriaAttributes['aria-orientation'];\ntype Direction = 'ltr' | 'rtl';\n\ninterface RovingFocusGroupOptions {\n  /**\n   * The orientation of the group.\n   * Mainly so arrow navigation is done accordingly (left & right vs. up & down)\n   */\n  orientation?: Orientation;\n  /**\n   * The direction of navigation between items.\n   */\n  dir?: Direction;\n  /**\n   * Whether keyboard navigation should loop around\n   * @defaultValue false\n   */\n  loop?: boolean;\n}\n\ntype RovingContextValue = RovingFocusGroupOptions & {\n  currentTabStopId: string | null;\n  onItemFocus(tabStopId: string): void;\n  onItemShiftTab(): void;\n  onFocusableItemAdd(): void;\n  onFocusableItemRemove(): void;\n};\n\nconst [RovingFocusProvider, useRovingFocusContext] =\n  createRovingFocusGroupContext<RovingContextValue>(GROUP_NAME);\n\ntype RovingFocusGroupElement = RovingFocusGroupImplElement;\ninterface RovingFocusGroupProps extends RovingFocusGroupImplProps {}\n\nconst RovingFocusGroup = React.forwardRef<RovingFocusGroupElement, RovingFocusGroupProps>(\n  (props: ScopedProps<RovingFocusGroupProps>, forwardedRef) => {\n    return (\n      <Collection.Provider scope={props.__scopeRovingFocusGroup}>\n        <Collection.Slot scope={props.__scopeRovingFocusGroup}>\n          <RovingFocusGroupImpl {...props} ref={forwardedRef} />\n        </Collection.Slot>\n      </Collection.Provider>\n    );\n  }\n);\n\nRovingFocusGroup.displayName = GROUP_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\ntype RovingFocusGroupImplElement = React.ComponentRef<typeof Primitive.div>;\ntype PrimitiveDivProps = React.ComponentPropsWithoutRef<typeof Primitive.div>;\ninterface RovingFocusGroupImplProps\n  extends Omit<PrimitiveDivProps, 'dir'>,\n    RovingFocusGroupOptions {\n  currentTabStopId?: string | null;\n  defaultCurrentTabStopId?: string;\n  onCurrentTabStopIdChange?: (tabStopId: string | null) => void;\n  onEntryFocus?: (event: Event) => void;\n  preventScrollOnEntryFocus?: boolean;\n}\n\nconst RovingFocusGroupImpl = React.forwardRef<\n  RovingFocusGroupImplElement,\n  RovingFocusGroupImplProps\n>((props: ScopedProps<RovingFocusGroupImplProps>, forwardedRef) => {\n  const {\n    __scopeRovingFocusGroup,\n    orientation,\n    loop = false,\n    dir,\n    currentTabStopId: currentTabStopIdProp,\n    defaultCurrentTabStopId,\n    onCurrentTabStopIdChange,\n    onEntryFocus,\n    preventScrollOnEntryFocus = false,\n    ...groupProps\n  } = props;\n  const ref = React.useRef<RovingFocusGroupImplElement>(null);\n  const composedRefs = useComposedRefs(forwardedRef, ref);\n  const direction = useDirection(dir);\n  const [currentTabStopId, setCurrentTabStopId] = useControllableState({\n    prop: currentTabStopIdProp,\n    defaultProp: defaultCurrentTabStopId ?? null,\n    onChange: onCurrentTabStopIdChange,\n    caller: GROUP_NAME,\n  });\n  const [isTabbingBackOut, setIsTabbingBackOut] = React.useState(false);\n  const handleEntryFocus = useCallbackRef(onEntryFocus);\n  const getItems = useCollection(__scopeRovingFocusGroup);\n  const isClickFocusRef = React.useRef(false);\n  const [focusableItemsCount, setFocusableItemsCount] = React.useState(0);\n\n  React.useEffect(() => {\n    const node = ref.current;\n    if (node) {\n      node.addEventListener(ENTRY_FOCUS, handleEntryFocus);\n      return () => node.removeEventListener(ENTRY_FOCUS, handleEntryFocus);\n    }\n  }, [handleEntryFocus]);\n\n  return (\n    <RovingFocusProvider\n      scope={__scopeRovingFocusGroup}\n      orientation={orientation}\n      dir={direction}\n      loop={loop}\n      currentTabStopId={currentTabStopId}\n      onItemFocus={React.useCallback(\n        (tabStopId) => setCurrentTabStopId(tabStopId),\n        [setCurrentTabStopId]\n      )}\n      onItemShiftTab={React.useCallback(() => setIsTabbingBackOut(true), [])}\n      onFocusableItemAdd={React.useCallback(\n        () => setFocusableItemsCount((prevCount) => prevCount + 1),\n        []\n      )}\n      onFocusableItemRemove={React.useCallback(\n        () => setFocusableItemsCount((prevCount) => prevCount - 1),\n        []\n      )}\n    >\n      <Primitive.div\n        tabIndex={isTabbingBackOut || focusableItemsCount === 0 ? -1 : 0}\n        data-orientation={orientation}\n        {...groupProps}\n        ref={composedRefs}\n        style={{ outline: 'none', ...props.style }}\n        onMouseDown={composeEventHandlers(props.onMouseDown, () => {\n          isClickFocusRef.current = true;\n        })}\n        onFocus={composeEventHandlers(props.onFocus, (event) => {\n          // We normally wouldn't need this check, because we already check\n          // that the focus is on the current target and not bubbling to it.\n          // We do this because Safari doesn't focus buttons when clicked, and\n          // instead, the wrapper will get focused and not through a bubbling event.\n          const isKeyboardFocus = !isClickFocusRef.current;\n\n          if (event.target === event.currentTarget && isKeyboardFocus && !isTabbingBackOut) {\n            const entryFocusEvent = new CustomEvent(ENTRY_FOCUS, EVENT_OPTIONS);\n            event.currentTarget.dispatchEvent(entryFocusEvent);\n\n            if (!entryFocusEvent.defaultPrevented) {\n              const items = getItems().filter((item) => item.focusable);\n              const activeItem = items.find((item) => item.active);\n              const currentItem = items.find((item) => item.id === currentTabStopId);\n              const candidateItems = [activeItem, currentItem, ...items].filter(\n                Boolean\n              ) as typeof items;\n              const candidateNodes = candidateItems.map((item) => item.ref.current!);\n              focusFirst(candidateNodes, preventScrollOnEntryFocus);\n            }\n          }\n\n          isClickFocusRef.current = false;\n        })}\n        onBlur={composeEventHandlers(props.onBlur, () => setIsTabbingBackOut(false))}\n      />\n    </RovingFocusProvider>\n  );\n});\n\n/* -------------------------------------------------------------------------------------------------\n * RovingFocusGroupItem\n * -----------------------------------------------------------------------------------------------*/\n\nconst ITEM_NAME = 'RovingFocusGroupItem';\n\ntype RovingFocusItemElement = React.ComponentRef<typeof Primitive.span>;\ntype PrimitiveSpanProps = React.ComponentPropsWithoutRef<typeof Primitive.span>;\ninterface RovingFocusItemProps extends Omit<PrimitiveSpanProps, 'children'> {\n  tabStopId?: string;\n  focusable?: boolean;\n  active?: boolean;\n  children?:\n    | React.ReactNode\n    | ((props: { hasTabStop: boolean; isCurrentTabStop: boolean }) => React.ReactNode);\n}\n\nconst RovingFocusGroupItem = React.forwardRef<RovingFocusItemElement, RovingFocusItemProps>(\n  (props: ScopedProps<RovingFocusItemProps>, forwardedRef) => {\n    const {\n      __scopeRovingFocusGroup,\n      focusable = true,\n      active = false,\n      tabStopId,\n      children,\n      ...itemProps\n    } = props;\n    const autoId = useId();\n    const id = tabStopId || autoId;\n    const context = useRovingFocusContext(ITEM_NAME, __scopeRovingFocusGroup);\n    const isCurrentTabStop = context.currentTabStopId === id;\n    const getItems = useCollection(__scopeRovingFocusGroup);\n\n    const { onFocusableItemAdd, onFocusableItemRemove, currentTabStopId } = context;\n\n    React.useEffect(() => {\n      if (focusable) {\n        onFocusableItemAdd();\n        return () => onFocusableItemRemove();\n      }\n    }, [focusable, onFocusableItemAdd, onFocusableItemRemove]);\n\n    return (\n      <Collection.ItemSlot\n        scope={__scopeRovingFocusGroup}\n        id={id}\n        focusable={focusable}\n        active={active}\n      >\n        <Primitive.span\n          tabIndex={isCurrentTabStop ? 0 : -1}\n          data-orientation={context.orientation}\n          {...itemProps}\n          ref={forwardedRef}\n          onMouseDown={composeEventHandlers(props.onMouseDown, (event) => {\n            // We prevent focusing non-focusable items on `mousedown`.\n            // Even though the item has tabIndex={-1}, that only means take it out of the tab order.\n            if (!focusable) event.preventDefault();\n            // Safari doesn't focus a button when clicked so we run our logic on mousedown also\n            else context.onItemFocus(id);\n          })}\n          onFocus={composeEventHandlers(props.onFocus, () => context.onItemFocus(id))}\n          onKeyDown={composeEventHandlers(props.onKeyDown, (event) => {\n            if (event.key === 'Tab' && event.shiftKey) {\n              context.onItemShiftTab();\n              return;\n            }\n\n            if (event.target !== event.currentTarget) return;\n\n            const focusIntent = getFocusIntent(event, context.orientation, context.dir);\n\n            if (focusIntent !== undefined) {\n              if (event.metaKey || event.ctrlKey || event.altKey || event.shiftKey) return;\n              event.preventDefault();\n              const items = getItems().filter((item) => item.focusable);\n              let candidateNodes = items.map((item) => item.ref.current!);\n\n              if (focusIntent === 'last') candidateNodes.reverse();\n              else if (focusIntent === 'prev' || focusIntent === 'next') {\n                if (focusIntent === 'prev') candidateNodes.reverse();\n                const currentIndex = candidateNodes.indexOf(event.currentTarget);\n                candidateNodes = context.loop\n                  ? wrapArray(candidateNodes, currentIndex + 1)\n                  : candidateNodes.slice(currentIndex + 1);\n              }\n\n              /**\n               * Imperative focus during keydown is risky so we prevent React's batching updates\n               * to avoid potential bugs. See: https://github.com/facebook/react/issues/20332\n               */\n              setTimeout(() => focusFirst(candidateNodes));\n            }\n          })}\n        >\n          {typeof children === 'function'\n            ? children({ isCurrentTabStop, hasTabStop: currentTabStopId != null })\n            : children}\n        </Primitive.span>\n      </Collection.ItemSlot>\n    );\n  }\n);\n\nRovingFocusGroupItem.displayName = ITEM_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\n// prettier-ignore\nconst MAP_KEY_TO_FOCUS_INTENT: Record<string, FocusIntent> = {\n  ArrowLeft: 'prev', ArrowUp: 'prev',\n  ArrowRight: 'next', ArrowDown: 'next',\n  PageUp: 'first', Home: 'first',\n  PageDown: 'last', End: 'last',\n};\n\nfunction getDirectionAwareKey(key: string, dir?: Direction) {\n  if (dir !== 'rtl') return key;\n  return key === 'ArrowLeft' ? 'ArrowRight' : key === 'ArrowRight' ? 'ArrowLeft' : key;\n}\n\ntype FocusIntent = 'first' | 'last' | 'prev' | 'next';\n\nfunction getFocusIntent(event: React.KeyboardEvent, orientation?: Orientation, dir?: Direction) {\n  const key = getDirectionAwareKey(event.key, dir);\n  if (orientation === 'vertical' && ['ArrowLeft', 'ArrowRight'].includes(key)) return undefined;\n  if (orientation === 'horizontal' && ['ArrowUp', 'ArrowDown'].includes(key)) return undefined;\n  return MAP_KEY_TO_FOCUS_INTENT[key];\n}\n\nfunction focusFirst(candidates: HTMLElement[], preventScroll = false) {\n  const PREVIOUSLY_FOCUSED_ELEMENT = document.activeElement;\n  for (const candidate of candidates) {\n    // if focus is already where we want to go, we don't want to keep going through the candidates\n    if (candidate === PREVIOUSLY_FOCUSED_ELEMENT) return;\n    candidate.focus({ preventScroll });\n    if (document.activeElement !== PREVIOUSLY_FOCUSED_ELEMENT) return;\n  }\n}\n\n/**\n * Wraps an array around itself at a given start index\n * Example: `wrapArray(['a', 'b', 'c', 'd'], 2) === ['c', 'd', 'a', 'b']`\n */\nfunction wrapArray<T>(array: T[], startIndex: number) {\n  return array.map<T>((_, index) => array[(startIndex + index) % array.length]!);\n}\n\nconst Root = RovingFocusGroup;\nconst Item = RovingFocusGroupItem;\n\nexport {\n  createRovingFocusGroupScope,\n  //\n  RovingFocusGroup,\n  RovingFocusGroupItem,\n  //\n  Root,\n  Item,\n};\nexport type { RovingFocusGroupProps, RovingFocusItemProps };\n", "import * as React from 'react';\nimport { useComposedRefs } from '@radix-ui/react-compose-refs';\nimport { useLayoutEffect } from '@radix-ui/react-use-layout-effect';\nimport { useStateMachine } from './use-state-machine';\n\ninterface PresenceProps {\n  children: React.ReactElement | ((props: { present: boolean }) => React.ReactElement);\n  present: boolean;\n}\n\nconst Presence: React.FC<PresenceProps> = (props) => {\n  const { present, children } = props;\n  const presence = usePresence(present);\n\n  const child = (\n    typeof children === 'function'\n      ? children({ present: presence.isPresent })\n      : React.Children.only(children)\n  ) as React.ReactElement<{ ref?: React.Ref<HTMLElement> }>;\n\n  const ref = useComposedRefs(presence.ref, getElementRef(child));\n  const forceMount = typeof children === 'function';\n  return forceMount || presence.isPresent ? React.cloneElement(child, { ref }) : null;\n};\n\nPresence.displayName = 'Presence';\n\n/* -------------------------------------------------------------------------------------------------\n * usePresence\n * -----------------------------------------------------------------------------------------------*/\n\nfunction usePresence(present: boolean) {\n  const [node, setNode] = React.useState<HTMLElement>();\n  const stylesRef = React.useRef<CSSStyleDeclaration | null>(null);\n  const prevPresentRef = React.useRef(present);\n  const prevAnimationNameRef = React.useRef<string>('none');\n  const initialState = present ? 'mounted' : 'unmounted';\n  const [state, send] = useStateMachine(initialState, {\n    mounted: {\n      UNMOUNT: 'unmounted',\n      ANIMATION_OUT: 'unmountSuspended',\n    },\n    unmountSuspended: {\n      MOUNT: 'mounted',\n      ANIMATION_END: 'unmounted',\n    },\n    unmounted: {\n      MOUNT: 'mounted',\n    },\n  });\n\n  React.useEffect(() => {\n    const currentAnimationName = getAnimationName(stylesRef.current);\n    prevAnimationNameRef.current = state === 'mounted' ? currentAnimationName : 'none';\n  }, [state]);\n\n  useLayoutEffect(() => {\n    const styles = stylesRef.current;\n    const wasPresent = prevPresentRef.current;\n    const hasPresentChanged = wasPresent !== present;\n\n    if (hasPresentChanged) {\n      const prevAnimationName = prevAnimationNameRef.current;\n      const currentAnimationName = getAnimationName(styles);\n\n      if (present) {\n        send('MOUNT');\n      } else if (currentAnimationName === 'none' || styles?.display === 'none') {\n        // If there is no exit animation or the element is hidden, animations won't run\n        // so we unmount instantly\n        send('UNMOUNT');\n      } else {\n        /**\n         * When `present` changes to `false`, we check changes to animation-name to\n         * determine whether an animation has started. We chose this approach (reading\n         * computed styles) because there is no `animationrun` event and `animationstart`\n         * fires after `animation-delay` has expired which would be too late.\n         */\n        const isAnimating = prevAnimationName !== currentAnimationName;\n\n        if (wasPresent && isAnimating) {\n          send('ANIMATION_OUT');\n        } else {\n          send('UNMOUNT');\n        }\n      }\n\n      prevPresentRef.current = present;\n    }\n  }, [present, send]);\n\n  useLayoutEffect(() => {\n    if (node) {\n      let timeoutId: number;\n      const ownerWindow = node.ownerDocument.defaultView ?? window;\n      /**\n       * Triggering an ANIMATION_OUT during an ANIMATION_IN will fire an `animationcancel`\n       * event for ANIMATION_IN after we have entered `unmountSuspended` state. So, we\n       * make sure we only trigger ANIMATION_END for the currently active animation.\n       */\n      const handleAnimationEnd = (event: AnimationEvent) => {\n        const currentAnimationName = getAnimationName(stylesRef.current);\n        const isCurrentAnimation = currentAnimationName.includes(event.animationName);\n        if (event.target === node && isCurrentAnimation) {\n          // With React 18 concurrency this update is applied a frame after the\n          // animation ends, creating a flash of visible content. By setting the\n          // animation fill mode to \"forwards\", we force the node to keep the\n          // styles of the last keyframe, removing the flash.\n          //\n          // Previously we flushed the update via ReactDom.flushSync, but with\n          // exit animations this resulted in the node being removed from the\n          // DOM before the synthetic animationEnd event was dispatched, meaning\n          // user-provided event handlers would not be called.\n          // https://github.com/radix-ui/primitives/pull/1849\n          send('ANIMATION_END');\n          if (!prevPresentRef.current) {\n            const currentFillMode = node.style.animationFillMode;\n            node.style.animationFillMode = 'forwards';\n            // Reset the style after the node had time to unmount (for cases\n            // where the component chooses not to unmount). Doing this any\n            // sooner than `setTimeout` (e.g. with `requestAnimationFrame`)\n            // still causes a flash.\n            timeoutId = ownerWindow.setTimeout(() => {\n              if (node.style.animationFillMode === 'forwards') {\n                node.style.animationFillMode = currentFillMode;\n              }\n            });\n          }\n        }\n      };\n      const handleAnimationStart = (event: AnimationEvent) => {\n        if (event.target === node) {\n          // if animation occurred, store its name as the previous animation.\n          prevAnimationNameRef.current = getAnimationName(stylesRef.current);\n        }\n      };\n      node.addEventListener('animationstart', handleAnimationStart);\n      node.addEventListener('animationcancel', handleAnimationEnd);\n      node.addEventListener('animationend', handleAnimationEnd);\n      return () => {\n        ownerWindow.clearTimeout(timeoutId);\n        node.removeEventListener('animationstart', handleAnimationStart);\n        node.removeEventListener('animationcancel', handleAnimationEnd);\n        node.removeEventListener('animationend', handleAnimationEnd);\n      };\n    } else {\n      // Transition to the unmounted state if the node is removed prematurely.\n      // We avoid doing so during cleanup as the node may change but still exist.\n      send('ANIMATION_END');\n    }\n  }, [node, send]);\n\n  return {\n    isPresent: ['mounted', 'unmountSuspended'].includes(state),\n    ref: React.useCallback((node: HTMLElement) => {\n      stylesRef.current = node ? getComputedStyle(node) : null;\n      setNode(node);\n    }, []),\n  };\n}\n\n/* -----------------------------------------------------------------------------------------------*/\n\nfunction getAnimationName(styles: CSSStyleDeclaration | null) {\n  return styles?.animationName || 'none';\n}\n\n// Before React 19 accessing `element.props.ref` will throw a warning and suggest using `element.ref`\n// After React 19 accessing `element.ref` does the opposite.\n// https://github.com/facebook/react/pull/28348\n//\n// Access the ref using the method that doesn't yield a warning.\nfunction getElementRef(element: React.ReactElement<{ ref?: React.Ref<unknown> }>) {\n  // React <=18 in DEV\n  let getter = Object.getOwnPropertyDescriptor(element.props, 'ref')?.get;\n  let mayWarn = getter && 'isReactWarning' in getter && getter.isReactWarning;\n  if (mayWarn) {\n    return (element as any).ref;\n  }\n\n  // React 19 in DEV\n  getter = Object.getOwnPropertyDescriptor(element, 'ref')?.get;\n  mayWarn = getter && 'isReactWarning' in getter && getter.isReactWarning;\n  if (mayWarn) {\n    return element.props.ref;\n  }\n\n  // Not DEV\n  return element.props.ref || (element as any).ref;\n}\n\nconst Root = Presence;\n\nexport {\n  Presence,\n  //\n  Root,\n};\nexport type { PresenceProps };\n", "import * as React from 'react';\n\ntype Machine<S> = { [k: string]: { [k: string]: S } };\ntype MachineState<T> = keyof T;\ntype MachineEvent<T> = keyof UnionToIntersection<T[keyof T]>;\n\n// 🤯 https://fettblog.eu/typescript-union-to-intersection/\ntype UnionToIntersection<T> = (T extends any ? (x: T) => any : never) extends (x: infer R) => any\n  ? R\n  : never;\n\nexport function useStateMachine<M>(\n  initialState: MachineState<M>,\n  machine: M & Machine<MachineState<M>>\n) {\n  return React.useReducer((state: MachineState<M>, event: MachineEvent<M>): MachineState<M> => {\n    const nextState = (machine[state] as any)[event];\n    return nextState ?? state;\n  }, initialState);\n}\n", "import * as React from 'react';\nimport { composeEventHandlers } from '@radix-ui/primitive';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { createRovingFocusGroupScope } from '@radix-ui/react-roving-focus';\nimport { Presence } from '@radix-ui/react-presence';\nimport { Primitive } from '@radix-ui/react-primitive';\nimport * as RovingFocusGroup from '@radix-ui/react-roving-focus';\nimport { useDirection } from '@radix-ui/react-direction';\nimport { useControllableState } from '@radix-ui/react-use-controllable-state';\nimport { useId } from '@radix-ui/react-id';\n\nimport type { Scope } from '@radix-ui/react-context';\n\n/* -------------------------------------------------------------------------------------------------\n * Tabs\n * -----------------------------------------------------------------------------------------------*/\n\nconst TABS_NAME = 'Tabs';\n\ntype ScopedProps<P> = P & { __scopeTabs?: Scope };\nconst [createTabsContext, createTabsScope] = createContextScope(TABS_NAME, [\n  createRovingFocusGroupScope,\n]);\nconst useRovingFocusGroupScope = createRovingFocusGroupScope();\n\ntype TabsContextValue = {\n  baseId: string;\n  value: string;\n  onValueChange: (value: string) => void;\n  orientation?: TabsProps['orientation'];\n  dir?: TabsProps['dir'];\n  activationMode?: TabsProps['activationMode'];\n};\n\nconst [TabsProvider, useTabsContext] = createTabsContext<TabsContextValue>(TABS_NAME);\n\ntype TabsElement = React.ComponentRef<typeof Primitive.div>;\ntype RovingFocusGroupProps = React.ComponentPropsWithoutRef<typeof RovingFocusGroup.Root>;\ntype PrimitiveDivProps = React.ComponentPropsWithoutRef<typeof Primitive.div>;\ninterface TabsProps extends PrimitiveDivProps {\n  /** The value for the selected tab, if controlled */\n  value?: string;\n  /** The value of the tab to select by default, if uncontrolled */\n  defaultValue?: string;\n  /** A function called when a new tab is selected */\n  onValueChange?: (value: string) => void;\n  /**\n   * The orientation the tabs are layed out.\n   * Mainly so arrow navigation is done accordingly (left & right vs. up & down)\n   * @defaultValue horizontal\n   */\n  orientation?: RovingFocusGroupProps['orientation'];\n  /**\n   * The direction of navigation between toolbar items.\n   */\n  dir?: RovingFocusGroupProps['dir'];\n  /**\n   * Whether a tab is activated automatically or manually.\n   * @defaultValue automatic\n   * */\n  activationMode?: 'automatic' | 'manual';\n}\n\nconst Tabs = React.forwardRef<TabsElement, TabsProps>(\n  (props: ScopedProps<TabsProps>, forwardedRef) => {\n    const {\n      __scopeTabs,\n      value: valueProp,\n      onValueChange,\n      defaultValue,\n      orientation = 'horizontal',\n      dir,\n      activationMode = 'automatic',\n      ...tabsProps\n    } = props;\n    const direction = useDirection(dir);\n    const [value, setValue] = useControllableState({\n      prop: valueProp,\n      onChange: onValueChange,\n      defaultProp: defaultValue ?? '',\n      caller: TABS_NAME,\n    });\n\n    return (\n      <TabsProvider\n        scope={__scopeTabs}\n        baseId={useId()}\n        value={value}\n        onValueChange={setValue}\n        orientation={orientation}\n        dir={direction}\n        activationMode={activationMode}\n      >\n        <Primitive.div\n          dir={direction}\n          data-orientation={orientation}\n          {...tabsProps}\n          ref={forwardedRef}\n        />\n      </TabsProvider>\n    );\n  }\n);\n\nTabs.displayName = TABS_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * TabsList\n * -----------------------------------------------------------------------------------------------*/\n\nconst TAB_LIST_NAME = 'TabsList';\n\ntype TabsListElement = React.ComponentRef<typeof Primitive.div>;\ninterface TabsListProps extends PrimitiveDivProps {\n  loop?: RovingFocusGroupProps['loop'];\n}\n\nconst TabsList = React.forwardRef<TabsListElement, TabsListProps>(\n  (props: ScopedProps<TabsListProps>, forwardedRef) => {\n    const { __scopeTabs, loop = true, ...listProps } = props;\n    const context = useTabsContext(TAB_LIST_NAME, __scopeTabs);\n    const rovingFocusGroupScope = useRovingFocusGroupScope(__scopeTabs);\n    return (\n      <RovingFocusGroup.Root\n        asChild\n        {...rovingFocusGroupScope}\n        orientation={context.orientation}\n        dir={context.dir}\n        loop={loop}\n      >\n        <Primitive.div\n          role=\"tablist\"\n          aria-orientation={context.orientation}\n          {...listProps}\n          ref={forwardedRef}\n        />\n      </RovingFocusGroup.Root>\n    );\n  }\n);\n\nTabsList.displayName = TAB_LIST_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * TabsTrigger\n * -----------------------------------------------------------------------------------------------*/\n\nconst TRIGGER_NAME = 'TabsTrigger';\n\ntype TabsTriggerElement = React.ComponentRef<typeof Primitive.button>;\ntype PrimitiveButtonProps = React.ComponentPropsWithoutRef<typeof Primitive.button>;\ninterface TabsTriggerProps extends PrimitiveButtonProps {\n  value: string;\n}\n\nconst TabsTrigger = React.forwardRef<TabsTriggerElement, TabsTriggerProps>(\n  (props: ScopedProps<TabsTriggerProps>, forwardedRef) => {\n    const { __scopeTabs, value, disabled = false, ...triggerProps } = props;\n    const context = useTabsContext(TRIGGER_NAME, __scopeTabs);\n    const rovingFocusGroupScope = useRovingFocusGroupScope(__scopeTabs);\n    const triggerId = makeTriggerId(context.baseId, value);\n    const contentId = makeContentId(context.baseId, value);\n    const isSelected = value === context.value;\n    return (\n      <RovingFocusGroup.Item\n        asChild\n        {...rovingFocusGroupScope}\n        focusable={!disabled}\n        active={isSelected}\n      >\n        <Primitive.button\n          type=\"button\"\n          role=\"tab\"\n          aria-selected={isSelected}\n          aria-controls={contentId}\n          data-state={isSelected ? 'active' : 'inactive'}\n          data-disabled={disabled ? '' : undefined}\n          disabled={disabled}\n          id={triggerId}\n          {...triggerProps}\n          ref={forwardedRef}\n          onMouseDown={composeEventHandlers(props.onMouseDown, (event) => {\n            // only call handler if it's the left button (mousedown gets triggered by all mouse buttons)\n            // but not when the control key is pressed (avoiding MacOS right click)\n            if (!disabled && event.button === 0 && event.ctrlKey === false) {\n              context.onValueChange(value);\n            } else {\n              // prevent focus to avoid accidental activation\n              event.preventDefault();\n            }\n          })}\n          onKeyDown={composeEventHandlers(props.onKeyDown, (event) => {\n            if ([' ', 'Enter'].includes(event.key)) context.onValueChange(value);\n          })}\n          onFocus={composeEventHandlers(props.onFocus, () => {\n            // handle \"automatic\" activation if necessary\n            // ie. activate tab following focus\n            const isAutomaticActivation = context.activationMode !== 'manual';\n            if (!isSelected && !disabled && isAutomaticActivation) {\n              context.onValueChange(value);\n            }\n          })}\n        />\n      </RovingFocusGroup.Item>\n    );\n  }\n);\n\nTabsTrigger.displayName = TRIGGER_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * TabsContent\n * -----------------------------------------------------------------------------------------------*/\n\nconst CONTENT_NAME = 'TabsContent';\n\ntype TabsContentElement = React.ComponentRef<typeof Primitive.div>;\ninterface TabsContentProps extends PrimitiveDivProps {\n  value: string;\n\n  /**\n   * Used to force mounting when more control is needed. Useful when\n   * controlling animation with React animation libraries.\n   */\n  forceMount?: true;\n}\n\nconst TabsContent = React.forwardRef<TabsContentElement, TabsContentProps>(\n  (props: ScopedProps<TabsContentProps>, forwardedRef) => {\n    const { __scopeTabs, value, forceMount, children, ...contentProps } = props;\n    const context = useTabsContext(CONTENT_NAME, __scopeTabs);\n    const triggerId = makeTriggerId(context.baseId, value);\n    const contentId = makeContentId(context.baseId, value);\n    const isSelected = value === context.value;\n    const isMountAnimationPreventedRef = React.useRef(isSelected);\n\n    React.useEffect(() => {\n      const rAF = requestAnimationFrame(() => (isMountAnimationPreventedRef.current = false));\n      return () => cancelAnimationFrame(rAF);\n    }, []);\n\n    return (\n      <Presence present={forceMount || isSelected}>\n        {({ present }) => (\n          <Primitive.div\n            data-state={isSelected ? 'active' : 'inactive'}\n            data-orientation={context.orientation}\n            role=\"tabpanel\"\n            aria-labelledby={triggerId}\n            hidden={!present}\n            id={contentId}\n            tabIndex={0}\n            {...contentProps}\n            ref={forwardedRef}\n            style={{\n              ...props.style,\n              animationDuration: isMountAnimationPreventedRef.current ? '0s' : undefined,\n            }}\n          >\n            {present && children}\n          </Primitive.div>\n        )}\n      </Presence>\n    );\n  }\n);\n\nTabsContent.displayName = CONTENT_NAME;\n\n/* ---------------------------------------------------------------------------------------------- */\n\nfunction makeTriggerId(baseId: string, value: string) {\n  return `${baseId}-trigger-${value}`;\n}\n\nfunction makeContentId(baseId: string, value: string) {\n  return `${baseId}-content-${value}`;\n}\n\nconst Root = Tabs;\nconst List = TabsList;\nconst Trigger = TabsTrigger;\nconst Content = TabsContent;\n\nexport {\n  createTabsScope,\n  //\n  Tabs,\n  TabsList,\n  TabsTrigger,\n  TabsContent,\n  //\n  Root,\n  List,\n  Trigger,\n  Content,\n};\nexport type { TabsProps, TabsListProps, TabsTriggerProps, TabsContentProps };\n", "\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as TabsPrimitive from \"@radix-ui/react-tabs\"\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Tabs = TabsPrimitive.Root\r\n\r\nconst TabsList = React.forwardRef<\r\n  React.ElementRef<typeof TabsPrimitive.List>,\r\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.List>\r\n>(({ className, ...props }, ref) => (\r\n  <TabsPrimitive.List\r\n    ref={ref}\r\n    className={cn(\r\n      \"inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nTabsList.displayName = TabsPrimitive.List.displayName\r\n\r\nconst TabsTrigger = React.forwardRef<\r\n  React.ElementRef<typeof TabsPrimitive.Trigger>,\r\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Trigger>\r\n>(({ className, ...props }, ref) => (\r\n  <TabsPrimitive.Trigger\r\n    ref={ref}\r\n    className={cn(\r\n      \"inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nTabsTrigger.displayName = TabsPrimitive.Trigger.displayName\r\n\r\nconst TabsContent = React.forwardRef<\r\n  React.ElementRef<typeof TabsPrimitive.Content>,\r\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Content>\r\n>(({ className, ...props }, ref) => (\r\n  <TabsPrimitive.Content\r\n    ref={ref}\r\n    className={cn(\r\n      \"mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nTabsContent.displayName = TabsPrimitive.Content.displayName\r\n\r\nexport { Tabs, TabsList, TabsTrigger, TabsContent } ", "import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name KeyRound\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMi41ODYgMTcuNDE0QTIgMiAwIDAgMCAyIDE4LjgyOFYyMWExIDEgMCAwIDAgMSAxaDNhMSAxIDAgMCAwIDEtMXYtMWExIDEgMCAwIDEgMS0xaDFhMSAxIDAgMCAwIDEtMXYtMWExIDEgMCAwIDEgMS0xaC4xNzJhMiAyIDAgMCAwIDEuNDE0LS41ODZsLjgxNC0uODE0YTYuNSA2LjUgMCAxIDAtNC00eiIgLz4KICA8Y2lyY2xlIGN4PSIxNi41IiBjeT0iNy41IiByPSIuNSIgZmlsbD0iY3VycmVudENvbG9yIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/key-round\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst KeyRound = createLucideIcon('KeyRound', [\n  [\n    'path',\n    {\n      d: 'M2.586 17.414A2 2 0 0 0 2 18.828V21a1 1 0 0 0 1 1h3a1 1 0 0 0 1-1v-1a1 1 0 0 1 1-1h1a1 1 0 0 0 1-1v-1a1 1 0 0 1 1-1h.172a2 2 0 0 0 1.414-.586l.814-.814a6.5 6.5 0 1 0-4-4z',\n      key: '1s6t7t',\n    },\n  ],\n  ['circle', { cx: '16.5', cy: '7.5', r: '.5', fill: 'currentColor', key: 'w0ekpg' }],\n]);\n\nexport default KeyRound;\n", "\"use client\"\r\n\r\nimport { useState } from \"react\"\r\nimport { signIn } from \"next-auth/react\"\r\nimport { useToast } from \"@/components/ui/use-toast\"\r\nimport { Button } from \"@/components/ui/button\"\r\nimport { Input } from \"@/components/ui/input\"\r\nimport {\r\n  Card,\r\n  CardContent,\r\n  CardDescription,\r\n  CardHeader,\r\n  CardTitle,\r\n} from \"@/components/ui/card\"\r\nimport {\r\n  <PERSON><PERSON>,\r\n  <PERSON>bsContent,\r\n  TabsList,\r\n  TabsTrigger,\r\n} from \"@/components/ui/tabs\"\r\nimport { Github, Loader2, KeyRound, User2 } from \"lucide-react\"\r\nimport { cn } from \"@/lib/utils\"\r\n\r\ninterface FormErrors {\r\n  username?: string\r\n  password?: string\r\n  confirmPassword?: string\r\n}\r\n\r\nexport function LoginForm() {\r\n  const [username, setUsername] = useState(\"\")\r\n  const [password, setPassword] = useState(\"\")\r\n  const [confirmPassword, setConfirmPassword] = useState(\"\")\r\n  const [loading, setLoading] = useState(false)\r\n  const [errors, setErrors] = useState<FormErrors>({})\r\n  const { toast } = useToast()\r\n\r\n  const validateLoginForm = () => {\r\n    const newErrors: FormErrors = {}\r\n    if (!username) newErrors.username = \"请输入用户名\"\r\n    if (!password) newErrors.password = \"请输入密码\"\r\n    if (username.includes('@')) newErrors.username = \"用户名不能包含 @ 符号\"\r\n    if (password && password.length < 8) newErrors.password = \"密码长度必须大于等于8位\"\r\n    setErrors(newErrors)\r\n    return Object.keys(newErrors).length === 0\r\n  }\r\n\r\n  const validateRegisterForm = () => {\r\n    const newErrors: FormErrors = {}\r\n    if (!username) newErrors.username = \"请输入用户名\"\r\n    if (!password) newErrors.password = \"请输入密码\"\r\n    if (username.includes('@')) newErrors.username = \"用户名不能包含 @ 符号\"\r\n    if (password && password.length < 8) newErrors.password = \"密码长度必须大于等于8位\"\r\n    if (!confirmPassword) newErrors.confirmPassword = \"请确认密码\"\r\n    if (password !== confirmPassword) newErrors.confirmPassword = \"两次输入的密码不一致\"\r\n    setErrors(newErrors)\r\n    return Object.keys(newErrors).length === 0\r\n  }\r\n\r\n  const handleLogin = async () => {\r\n    if (!validateLoginForm()) return\r\n\r\n    setLoading(true)\r\n    try {\r\n      const result = await signIn(\"credentials\", {\r\n        username,\r\n        password,\r\n        redirect: false,\r\n      })\r\n\r\n      if (result?.error) {\r\n        toast({\r\n          title: \"登录失败\",\r\n          description: \"用户名或密码错误\",\r\n          variant: \"destructive\",\r\n        })\r\n        setLoading(false)\r\n        return\r\n      }\r\n\r\n      window.location.href = \"/\"\r\n    } catch (error) {\r\n      toast({\r\n        title: \"登录失败\",\r\n        description: error instanceof Error ? error.message : \"请稍后重试\",\r\n        variant: \"destructive\",\r\n      })\r\n      setLoading(false)\r\n    }\r\n  }\r\n\r\n  const handleRegister = async () => {\r\n    if (!validateRegisterForm()) return\r\n\r\n    setLoading(true)\r\n    try {\r\n      const response = await fetch(\"/api/auth/register\", {\r\n        method: \"POST\",\r\n        headers: { \"Content-Type\": \"application/json\" },\r\n        body: JSON.stringify({ username, password }),\r\n      })\r\n\r\n      const data = await response.json() as { error?: string }\r\n\r\n      if (!response.ok) {\r\n        toast({\r\n          title: \"注册失败\",\r\n          description: data.error || \"请稍后重试\",\r\n          variant: \"destructive\",\r\n        })\r\n        setLoading(false)\r\n        return\r\n      }\r\n\r\n      // 注册成功后自动登录\r\n      const result = await signIn(\"credentials\", {\r\n        username,\r\n        password,\r\n        redirect: false,\r\n      })\r\n\r\n      if (result?.error) {\r\n        toast({\r\n          title: \"登录失败\",\r\n          description: \"自动登录失败，请手动登录\",\r\n          variant: \"destructive\",\r\n        })\r\n        setLoading(false)\r\n        return\r\n      }\r\n\r\n      window.location.href = \"/\"\r\n    } catch (error) {\r\n      toast({\r\n        title: \"注册失败\",\r\n        description: error instanceof Error ? error.message : \"请稍后重试\",\r\n        variant: \"destructive\",\r\n      })\r\n      setLoading(false)\r\n    }\r\n  }\r\n\r\n  const handleGithubLogin = () => {\r\n    signIn(\"github\", { callbackUrl: \"/\" })\r\n  }\r\n\r\n  const clearForm = () => {\r\n    setUsername(\"\")\r\n    setPassword(\"\")\r\n    setConfirmPassword(\"\")\r\n    setErrors({})\r\n  }\r\n\r\n  return (\r\n    <Card className=\"w-[95%] max-w-lg border-2 border-primary/20\">\r\n      <CardHeader className=\"space-y-2\">\r\n        <CardTitle className=\"text-2xl text-center bg-gradient-to-r from-primary to-purple-600 bg-clip-text text-transparent\">\r\n          欢迎使用 MoeMail\r\n        </CardTitle>\r\n        <CardDescription className=\"text-center\">\r\n          萌萌哒临时邮箱服务 (。・∀・)ノ\r\n        </CardDescription>\r\n      </CardHeader>\r\n      <CardContent className=\"px-6\">\r\n        <Tabs defaultValue=\"login\" className=\"w-full\" onValueChange={clearForm}>\r\n          <TabsList className=\"grid w-full grid-cols-2 mb-6\">\r\n            <TabsTrigger value=\"login\">登录</TabsTrigger>\r\n            <TabsTrigger value=\"register\">注册</TabsTrigger>\r\n          </TabsList>\r\n          <div className=\"min-h-[220px]\">\r\n            <TabsContent value=\"login\" className=\"space-y-4 mt-0\">\r\n              <div className=\"space-y-3\">\r\n                <div className=\"space-y-1.5\">\r\n                  <div className=\"relative\">\r\n                    <div className=\"absolute left-2.5 top-2 text-muted-foreground\">\r\n                      <User2 className=\"h-5 w-5\" />\r\n                    </div>\r\n                    <Input\r\n                      className={cn(\r\n                        \"h-9 pl-9 pr-3\",\r\n                        errors.username && \"border-destructive focus-visible:ring-destructive\"\r\n                      )}\r\n                      placeholder=\"用户名\"\r\n                      value={username}\r\n                      onChange={(e) => {\r\n                        setUsername(e.target.value)\r\n                        setErrors({})\r\n                      }}\r\n                      disabled={loading}\r\n                    />\r\n                  </div>\r\n                  {errors.username && (\r\n                    <p className=\"text-xs text-destructive\">{errors.username}</p>\r\n                  )}\r\n                </div>\r\n                <div className=\"space-y-1.5\">\r\n                  <div className=\"relative\">\r\n                    <div className=\"absolute left-2.5 top-2 text-muted-foreground\">\r\n                      <KeyRound className=\"h-5 w-5\" />\r\n                    </div>\r\n                    <Input\r\n                      className={cn(\r\n                        \"h-9 pl-9 pr-3\",\r\n                        errors.password && \"border-destructive focus-visible:ring-destructive\"\r\n                      )}\r\n                      type=\"password\"\r\n                      placeholder=\"密码\"\r\n                      value={password}\r\n                      onChange={(e) => {\r\n                        setPassword(e.target.value)\r\n                        setErrors({})\r\n                      }}\r\n                      disabled={loading}\r\n                    />\r\n                  </div>\r\n                  {errors.password && (\r\n                    <p className=\"text-xs text-destructive\">{errors.password}</p>\r\n                  )}\r\n                </div>\r\n              </div>\r\n\r\n              <div className=\"space-y-3 pt-1\">\r\n                <Button\r\n                  className=\"w-full\"\r\n                  onClick={handleLogin}\r\n                  disabled={loading}\r\n                >\r\n                  {loading && <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />}\r\n                  登录\r\n                </Button>\r\n\r\n                <div className=\"relative\">\r\n                  <div className=\"absolute inset-0 flex items-center\">\r\n                    <span className=\"w-full border-t\" />\r\n                  </div>\r\n                  <div className=\"relative flex justify-center text-xs uppercase\">\r\n                    <span className=\"bg-background px-2 text-muted-foreground\">\r\n                      或者\r\n                    </span>\r\n                  </div>\r\n                </div>\r\n\r\n                <Button\r\n                  variant=\"outline\"\r\n                  className=\"w-full\"\r\n                  onClick={handleGithubLogin}\r\n                >\r\n                  <Github className=\"mr-2 h-4 w-4\" />\r\n                  使用 GitHub 账号登录\r\n                </Button>\r\n              </div>\r\n            </TabsContent>\r\n            <TabsContent value=\"register\" className=\"space-y-4 mt-0\">\r\n              <div className=\"space-y-3\">\r\n                <div className=\"space-y-1.5\">\r\n                  <div className=\"relative\">\r\n                    <div className=\"absolute left-2.5 top-2 text-muted-foreground\">\r\n                      <User2 className=\"h-5 w-5\" />\r\n                    </div>\r\n                    <Input\r\n                      className={cn(\r\n                        \"h-9 pl-9 pr-3\",\r\n                        errors.username && \"border-destructive focus-visible:ring-destructive\"\r\n                      )}\r\n                      placeholder=\"用户名\"\r\n                      value={username}\r\n                      onChange={(e) => {\r\n                        setUsername(e.target.value)\r\n                        setErrors({})\r\n                      }}\r\n                      disabled={loading}\r\n                    />\r\n                  </div>\r\n                  {errors.username && (\r\n                    <p className=\"text-xs text-destructive\">{errors.username}</p>\r\n                  )}\r\n                </div>\r\n                <div className=\"space-y-1.5\">\r\n                  <div className=\"relative\">\r\n                    <div className=\"absolute left-2.5 top-2 text-muted-foreground\">\r\n                      <KeyRound className=\"h-5 w-5\" />\r\n                    </div>\r\n                    <Input\r\n                      className={cn(\r\n                        \"h-9 pl-9 pr-3\",\r\n                        errors.password && \"border-destructive focus-visible:ring-destructive\"\r\n                      )}\r\n                      type=\"password\"\r\n                      placeholder=\"密码\"\r\n                      value={password}\r\n                      onChange={(e) => {\r\n                        setPassword(e.target.value)\r\n                        setErrors({})\r\n                      }}\r\n                      disabled={loading}\r\n                    />\r\n                  </div>\r\n                  {errors.password && (\r\n                    <p className=\"text-xs text-destructive\">{errors.password}</p>\r\n                  )}\r\n                </div>\r\n                <div className=\"space-y-1.5\">\r\n                  <div className=\"relative\">\r\n                    <div className=\"absolute left-2.5 top-2 text-muted-foreground\">\r\n                      <KeyRound className=\"h-5 w-5\" />\r\n                    </div>\r\n                    <Input\r\n                      className={cn(\r\n                        \"h-9 pl-9 pr-3\",\r\n                        errors.confirmPassword && \"border-destructive focus-visible:ring-destructive\"\r\n                      )}\r\n                      type=\"password\"\r\n                      placeholder=\"确认密码\"\r\n                      value={confirmPassword}\r\n                      onChange={(e) => {\r\n                        setConfirmPassword(e.target.value)\r\n                        setErrors({})\r\n                      }}\r\n                      disabled={loading}\r\n                    />\r\n                  </div>\r\n                  {errors.confirmPassword && (\r\n                    <p className=\"text-xs text-destructive\">{errors.confirmPassword}</p>\r\n                  )}\r\n                </div>\r\n              </div>\r\n\r\n              <div className=\"space-y-3 pt-1\">\r\n                <Button\r\n                  className=\"w-full\"\r\n                  onClick={handleRegister}\r\n                  disabled={loading}\r\n                >\r\n                  {loading && <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />}\r\n                  注册\r\n                </Button>\r\n              </div>\r\n            </TabsContent>\r\n          </div>\r\n        </Tabs>\r\n      </CardContent>\r\n    </Card>\r\n  )\r\n}", "import * as React from \"react\"\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nexport interface InputProps\r\n  extends React.InputHTMLAttributes<HTMLInputElement> {\r\n  className?: string\r\n}\r\n\r\nconst Input = React.forwardRef<HTMLInputElement, InputProps>(\r\n  ({ className, type, ...props }, ref) => {\r\n    return (\r\n      <input\r\n        type={type}\r\n        className={cn(\r\n          \"flex h-9 w-full rounded-md border border-input bg-transparent px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50\",\r\n          className\r\n        )}\r\n        ref={ref}\r\n        {...props}\r\n      />\r\n    )\r\n  }\r\n)\r\nInput.displayName = \"Input\"\r\n\r\nexport { Input } ", "import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name LoaderCircle\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMjEgMTJhOSA5IDAgMSAxLTYuMjE5LTguNTYiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/loader-circle\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst LoaderCircle = createLucideIcon('LoaderCircle', [\n  ['path', { d: 'M21 12a9 9 0 1 1-6.219-8.56', key: '13zald' }],\n]);\n\nexport default LoaderCircle;\n", "import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name UserRound\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjgiIHI9IjUiIC8+CiAgPHBhdGggZD0iTTIwIDIxYTggOCAwIDAgMC0xNiAwIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/user-round\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst UserRound = createLucideIcon('UserRound', [\n  ['circle', { cx: '12', cy: '8', r: '5', key: '1hypcn' }],\n  ['path', { d: 'M20 21a8 8 0 0 0-16 0', key: 'rfgkzh' }],\n]);\n\nexport default UserRound;\n", "import { LoginForm } from \"@/components/auth/login-form\"\r\nimport { auth } from \"@/lib/auth\"\r\nimport { redirect } from \"next/navigation\"\r\n\r\nexport const runtime = \"edge\"\r\n\r\nexport default async function LoginPage() {\r\n  const session = await auth()\r\n  \r\n  if (session?.user) {\r\n    redirect(\"/\")\r\n  }\r\n\r\n  return (\r\n    <div className=\"min-h-screen flex items-center justify-center bg-gradient-to-b from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800\">\r\n      <LoginForm />\r\n    </div>\r\n  )\r\n} "], "names": ["Card", "React", "className", "props", "ref", "div", "cn", "displayName", "<PERSON><PERSON><PERSON><PERSON>", "CardTitle", "h3", "CardDescription", "p", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>er", "node", "TabsList", "TabsPrimitive", "TabsTrigger", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "LoginForm", "username", "setUsername", "useState", "password", "setPassword", "confirmPassword", "setConfirmPassword", "loading", "setLoading", "errors", "setErrors", "toast", "useToast", "validateLoginForm", "newErrors", "includes", "length", "Object", "keys", "validateRegisterForm", "handleLogin", "result", "signIn", "redirect", "error", "title", "description", "variant", "window", "location", "href", "Error", "message", "handleRegister", "response", "fetch", "method", "headers", "body", "JSON", "stringify", "data", "json", "ok", "Tabs", "defaultValue", "onValueChange", "clearForm", "value", "User2", "Input", "placeholder", "onChange", "e", "target", "disabled", "KeyRound", "type", "<PERSON><PERSON>", "onClick", "Loader2", "span", "handleGithubLogin", "callbackUrl", "<PERSON><PERSON><PERSON>", "input", "runtime", "LoginPage", "session", "auth", "user"], "sourceRoot": "", "ignoreList": [7, 8, 9, 10, 14, 15, 16, 17, 18, 19]}