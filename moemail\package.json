{"name": "moemail", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "build:pages": "npx @cloudflare/next-on-pages", "db:migrate-local": "tsx scripts/migrate.ts local", "db:migrate-remote": "tsx scripts/migrate.ts remote", "webhook-test-server": "bun run scripts/webhook-test-server.ts", "generate-test-data": "wrangler dev scripts/generate-test-data.ts", "dev:cleanup": "wrangler dev --config wrangler.cleanup.json --test-scheduled", "test:cleanup": "curl http://localhost:8787/__scheduled", "deploy:email": "wrangler deploy --config wrangler.email.json", "deploy:cleanup": "wrangler deploy --config wrangler.cleanup.json", "deploy:pages": "npm run build:pages && wrangler pages deploy .vercel/output/static --branch main", "fix-issues": "tsx scripts/fix-project-issues.ts", "test-email-credentials": "tsx scripts/test-email-credentials.ts", "migrate-email-credentials": "tsx scripts/migrate-email-credentials.ts"}, "type": "module", "dependencies": {"@auth/drizzle-adapter": "^1.7.4", "@cloudflare/next-on-pages": "^1.13.6", "@radix-ui/react-alert-dialog": "^1.1.4", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.4", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-radio-group": "^1.2.1", "@radix-ui/react-select": "^2.1.4", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-switch": "^1.1.2", "@radix-ui/react-tabs": "^1.1.2", "@radix-ui/react-toast": "^1.1.5", "@radix-ui/react-tooltip": "^1.1.6", "@tailwindcss/typography": "^0.5.15", "class-variance-authority": "^0.7.0", "clsx": "^2.1.0", "drizzle-orm": "^0.36.4", "jose": "^6.0.11", "lucide-react": "^0.468.0", "nanoid": "^5.0.6", "next": "15.1.1", "next-auth": "5.0.0-beta.25", "next-pwa": "^5.6.0", "next-themes": "^0.2.1", "postal-mime": "^2.3.2", "react": "19.0.0", "react-dom": "19.0.0", "server-only": "^0.0.1", "tailwind-merge": "^2.2.2", "tailwindcss-animate": "^1.0.7", "zod": "^3.24.1", "zustand": "^5.0.3"}, "devDependencies": {"@cloudflare/workers-types": "^4.20241127.0", "@types/better-sqlite3": "^7.6.13", "@types/bun": "^1.1.14", "@types/next-pwa": "^5.6.9", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "better-sqlite3": "^11.10.0", "bun": "^1.1.39", "cloudflare": "^4.1.0", "dotenv": "^16.4.7", "drizzle-kit": "^0.28.1", "eslint": "^8", "eslint-config-next": "15.0.3", "postcss": "^8", "tailwindcss": "^3.4.1", "tsx": "^4.19.3", "typescript": "^5", "vercel": "39.1.1", "wrangler": "^3.91.0"}}