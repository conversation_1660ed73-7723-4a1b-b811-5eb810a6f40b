(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[636],{65521:e=>{"use strict";e.exports=require("node:async_hooks")},25356:e=>{"use strict";e.exports=require("node:buffer")},57102:(e,s,a)=>{"use strict";a.r(s),a.d(s,{ComponentMod:()=>P,default:()=>E});var t,i={};a.r(i),a.d(i,{ClientPageRoot:()=>h.Fy,ClientSegmentRoot:()=>h.pl,GlobalError:()=>m.default,HTTPAccessFallbackBoundary:()=>h.nQ,LayoutRouter:()=>h.C3,MetadataBoundary:()=>h.qB,OutletBoundary:()=>h.Cr,Postpone:()=>h.fK,RenderFromTemplateContext:()=>h.IY,ViewportBoundary:()=>h.PX,__next_app__:()=>u,actionAsyncStorage:()=>h.sc,collectSegmentData:()=>h.Uy,createMetadataComponents:()=>h.IB,createPrerenderParamsForClientSegment:()=>h.lu,createPrerenderSearchParamsForClientPage:()=>h.jO,createServerParamsForMetadata:()=>h.Kx,createServerParamsForServerSegment:()=>h.LV,createServerSearchParamsForMetadata:()=>h.mh,createServerSearchParamsForServerPage:()=>h.Vv,createTemporaryReferenceSet:()=>h.XI,decodeAction:()=>h.Jk,decodeFormState:()=>h.Am,decodeReply:()=>h.X$,pages:()=>p,patchFetch:()=>h.V5,preconnect:()=>h.kZ,preloadFont:()=>h.PY,preloadStyle:()=>h.vI,prerender:()=>h.CR,renderToReadableStream:()=>h.WK,routeModule:()=>f,serverHooks:()=>h.ge,taintObjectReference:()=>h.N2,tree:()=>x,workAsyncStorage:()=>h.J_,workUnitAsyncStorage:()=>h.FP}),a(79556);var r=a(19022),n=a(58661),l=a(20809),c=a(11939),o=a(13348),d=a(14139),m=a(86867),h=a(71102);let x=["",{children:["profile",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,51758)),"F:\\CODE\\Project\\tempmail\\moemail\\app\\profile\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(a.bind(a,27711)),"F:\\CODE\\Project\\tempmail\\moemail\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.bind(a,64913)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(a.bind(a,2412)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.bind(a,77757)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],p=["F:\\CODE\\Project\\tempmail\\moemail\\app\\profile\\page.tsx"],u={require:a,loadChunk:()=>Promise.resolve()},f=new o.AppPageRouteModule({definition:{kind:d.A.APP_PAGE,page:"/profile/page",pathname:"/profile",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:x}});var j=a(18058),g=a(72495),y=a(75376);globalThis.__nextCacheHandlers||(globalThis.__nextCacheHandlers={});let v=e=>e?JSON.parse(e):void 0,N=self.__BUILD_MANIFEST,b=v(self.__REACT_LOADABLE_MANIFEST),w=null==(t=self.__RSC_MANIFEST)?void 0:t["/profile/page"],A=v(self.__RSC_SERVER_MANIFEST),k=v(self.__NEXT_FONT_MANIFEST),C=v(self.__INTERCEPTION_ROUTE_REWRITE_MANIFEST)??[];w&&A&&(0,g.fQ)({page:"/profile/page",clientReferenceManifest:w,serverActionsManifest:A,serverModuleMap:(0,y.e)({serverActionsManifest:A})});let S=(0,n.R)({pagesType:j.g.APP,dev:!1,page:"/profile/page",appMod:null,pageMod:i,errorMod:null,error500Mod:null,Document:null,buildManifest:N,renderToHTML:c.W,reactLoadableManifest:b,clientReferenceManifest:w,serverActionsManifest:A,serverActions:void 0,subresourceIntegrityManifest:void 0,config:{env:{},eslint:{ignoreDuringBuilds:!1},typescript:{ignoreBuildErrors:!1,tsconfigPath:"tsconfig.json"},distDir:".next",cleanDistDir:!0,assetPrefix:"",cacheMaxMemorySize:0x3200000,configOrigin:"next.config.ts",useFileSystemPublicRoutes:!0,generateEtags:!0,pageExtensions:["tsx","ts","jsx","js"],poweredByHeader:!0,compress:!0,images:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",loaderFile:"",domains:[],disableStaticImages:!1,minimumCacheTTL:60,formats:["image/webp"],dangerouslyAllowSVG:!1,contentSecurityPolicy:"script-src 'none'; frame-src 'none'; sandbox;",contentDispositionType:"attachment",remotePatterns:[{protocol:"https",hostname:"avatars.githubusercontent.com"}],unoptimized:!1},devIndicators:{appIsrStatus:!0,buildActivity:!0,buildActivityPosition:"bottom-right"},onDemandEntries:{maxInactiveAge:6e4,pagesBufferLength:5},amp:{canonicalBase:""},basePath:"",sassOptions:{},trailingSlash:!1,i18n:null,productionBrowserSourceMaps:!1,excludeDefaultMomentLocales:!0,serverRuntimeConfig:{},publicRuntimeConfig:{},reactProductionProfiling:!1,reactStrictMode:null,reactMaxHeadersLength:6e3,httpAgentOptions:{keepAlive:!0},logging:{},expireTime:31536e3,staticPageGenerationTimeout:60,modularizeImports:{"@mui/icons-material":{transform:"@mui/icons-material/{{member}}"},lodash:{transform:"lodash/{{member}}"}},outputFileTracingRoot:"F:\\CODE\\Project\\tempmail\\moemail",experimental:{cacheLife:{default:{stale:300,revalidate:900,expire:0xfffffffe},seconds:{stale:0,revalidate:1,expire:60},minutes:{stale:300,revalidate:60,expire:3600},hours:{stale:300,revalidate:3600,expire:86400},days:{stale:300,revalidate:86400,expire:604800},weeks:{stale:300,revalidate:604800,expire:2592e3},max:{stale:300,revalidate:2592e3,expire:0xfffffffe}},cacheHandlers:{},cssChunking:!0,multiZoneDraftMode:!1,appNavFailHandling:!1,prerenderEarlyExit:!0,serverMinification:!0,serverSourceMaps:!1,linkNoTouchStart:!1,caseSensitiveRoutes:!1,clientSegmentCache:!1,preloadEntriesOnStart:!0,clientRouterFilter:!0,clientRouterFilterRedirects:!1,fetchCacheKeyPrefix:"",middlewarePrefetch:"flexible",optimisticClientCache:!0,manualClientBasePath:!1,cpus:11,memoryBasedWorkersCount:!1,imgOptConcurrency:null,imgOptTimeoutInSeconds:7,imgOptMaxInputPixels:0xfff8001,imgOptSequentialRead:null,isrFlushToDisk:!0,workerThreads:!1,optimizeCss:!1,nextScriptWorkers:!1,scrollRestoration:!1,externalDir:!1,disableOptimizedLoading:!1,gzipSize:!0,craCompat:!1,esmExternals:!0,fullySpecified:!1,swcTraceProfiling:!1,forceSwcTransforms:!1,largePageDataBytes:128e3,turbo:{root:"F:\\CODE\\Project\\tempmail\\moemail"},typedRoutes:!1,typedEnv:!1,parallelServerCompiles:!1,parallelServerBuildTraces:!1,ppr:!1,authInterrupts:!1,reactOwnerStack:!1,webpackMemoryOptimizations:!1,optimizeServerReact:!0,useEarlyImport:!1,staleTimes:{dynamic:0,static:300},serverComponentsHmrCache:!0,staticGenerationMaxConcurrency:8,staticGenerationMinPagesPerWorker:25,dynamicIO:!1,inlineCss:!1,optimizePackageImports:["lucide-react","date-fns","lodash-es","ramda","antd","react-bootstrap","ahooks","@ant-design/icons","@headlessui/react","@headlessui-float/react","@heroicons/react/20/solid","@heroicons/react/24/solid","@heroicons/react/24/outline","@visx/visx","@tremor/react","rxjs","@mui/material","@mui/icons-material","recharts","react-use","effect","@effect/schema","@effect/platform","@effect/platform-node","@effect/platform-browser","@effect/platform-bun","@effect/sql","@effect/sql-mssql","@effect/sql-mysql2","@effect/sql-pg","@effect/sql-squlite-node","@effect/sql-squlite-bun","@effect/sql-squlite-wasm","@effect/sql-squlite-react-native","@effect/rpc","@effect/rpc-http","@effect/typeclass","@effect/experimental","@effect/opentelemetry","@material-ui/core","@material-ui/icons","@tabler/icons-react","mui-core","react-icons/ai","react-icons/bi","react-icons/bs","react-icons/cg","react-icons/ci","react-icons/di","react-icons/fa","react-icons/fa6","react-icons/fc","react-icons/fi","react-icons/gi","react-icons/go","react-icons/gr","react-icons/hi","react-icons/hi2","react-icons/im","react-icons/io","react-icons/io5","react-icons/lia","react-icons/lib","react-icons/lu","react-icons/md","react-icons/pi","react-icons/ri","react-icons/rx","react-icons/si","react-icons/sl","react-icons/tb","react-icons/tfi","react-icons/ti","react-icons/vsc","react-icons/wi"]},bundlePagesRouterDependencies:!1,configFile:"F:\\CODE\\Project\\tempmail\\moemail\\next.config.ts",configFileName:"next.config.ts"},buildId:process.env.__NEXT_BUILD_ID,nextFontManifest:k,incrementalCacheHandler:null,interceptionRouteRewrites:C}),P=i;function E(e){return(0,r.O)({...e,IncrementalCache:l.N,handler:S})}},58522:(e,s,a)=>{Promise.resolve().then(a.bind(a,44245)),Promise.resolve().then(a.bind(a,9090)),Promise.resolve().then(a.bind(a,34980)),Promise.resolve().then(a.bind(a,26336))},98690:(e,s,a)=>{Promise.resolve().then(a.bind(a,37357)),Promise.resolve().then(a.bind(a,87911)),Promise.resolve().then(a.bind(a,56588)),Promise.resolve().then(a.bind(a,52744))},87911:(e,s,a)=>{"use strict";a.d(s,{ProfileCard:()=>ex});var t=a(37785),i=a(34655),r=a(277),n=a(31648),l=a(93823);let c=(0,l.A)("Crown",[["path",{d:"M11.562 3.266a.5.5 0 0 1 .876 0L15.39 8.87a1 1 0 0 0 1.516.294L21.183 5.5a.5.5 0 0 1 .798.519l-2.834 10.246a1 1 0 0 1-.956.734H5.81a1 1 0 0 1-.957-.734L2.02 6.02a.5.5 0 0 1 .798-.519l4.276 3.664a1 1 0 0 0 1.516-.294z",key:"1vdc57"}],["path",{d:"M5 21h14",key:"11awu3"}]]),o=(0,l.A)("Gem",[["path",{d:"M6 3h12l4 6-10 13L2 9Z",key:"1pcd5k"}],["path",{d:"M11 3 8 9l4 13 4-13-3-6",key:"1fcu3u"}],["path",{d:"M2 9h20",key:"16fsjt"}]]),d=(0,l.A)("Sword",[["polyline",{points:"14.5 17.5 3 6 3 3 6 3 17.5 14.5",key:"1hfsw2"}],["line",{x1:"13",x2:"19",y1:"19",y2:"13",key:"1vrmhu"}],["line",{x1:"16",x2:"20",y1:"16",y2:"20",key:"1bron3"}],["line",{x1:"19",x2:"21",y1:"21",y2:"19",key:"13pww6"}]]);var m=a(93295),h=a(91378);let x=(0,l.A)("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]);var p=a(43178),u=a(90842),f=a(57845),j=a(70937),g=a(89515),y=a(19816),v=a(53852),N=a(87496),b=a(20812),w=a(60834),A=a(67870),k=a(39706),C="Switch",[S,P]=(0,N.A)(C),[E,I]=S(C),T=f.forwardRef((e,s)=>{let{__scopeSwitch:a,name:i,checked:r,defaultChecked:n,required:l,disabled:c,value:o="on",onCheckedChange:d,form:m,...h}=e,[x,p]=f.useState(null),u=(0,v.s)(s,e=>p(e)),j=f.useRef(!1),g=!x||m||!!x.closest("form"),[N=!1,w]=(0,b.i)({prop:r,defaultProp:n,onChange:d});return(0,t.jsxs)(E,{scope:a,checked:N,disabled:c,children:[(0,t.jsx)(k.sG.button,{type:"button",role:"switch","aria-checked":N,"aria-required":l,"data-state":R(N),"data-disabled":c?"":void 0,disabled:c,value:o,...h,ref:u,onClick:(0,y.m)(e.onClick,e=>{w(e=>!e),g&&(j.current=e.isPropagationStopped(),j.current||e.stopPropagation())})}),g&&(0,t.jsx)(M,{control:x,bubbles:!j.current,name:i,value:o,checked:N,required:l,disabled:c,form:m,style:{transform:"translateX(-100%)"}})]})});T.displayName=C;var _="SwitchThumb",O=f.forwardRef((e,s)=>{let{__scopeSwitch:a,...i}=e,r=I(_,a);return(0,t.jsx)(k.sG.span,{"data-state":R(r.checked),"data-disabled":r.disabled?"":void 0,...i,ref:s})});O.displayName=_;var M=e=>{let{control:s,checked:a,bubbles:i=!0,...r}=e,n=f.useRef(null),l=(0,w.Z)(a),c=(0,A.X)(s);return f.useEffect(()=>{let e=n.current,s=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(l!==a&&s){let t=new Event("click",{bubbles:i});s.call(e,a),e.dispatchEvent(t)}},[l,a,i]),(0,t.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:a,...r,tabIndex:-1,ref:n,style:{...e.style,...c,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})};function R(e){return e?"checked":"unchecked"}var $=a(39086);let K=f.forwardRef(({className:e,...s},a)=>(0,t.jsx)(T,{className:(0,$.cn)("peer inline-flex h-5 w-9 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent shadow-sm transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input",e),...s,ref:a,children:(0,t.jsx)(O,{className:(0,$.cn)("pointer-events-none block h-4 w-4 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-4 data-[state=unchecked]:translate-x-0")})}));K.displayName=T.displayName;var L=a(37062),F=a(88563);let D=(0,l.A)("Send",[["path",{d:"M14.536 21.686a.5.5 0 0 0 .937-.024l6.5-19a.496.496 0 0 0-.635-.635l-19 6.5a.5.5 0 0 0-.024.937l7.93 3.18a2 2 0 0 1 1.112 1.11z",key:"1ffxy3"}],["path",{d:"m21.854 2.147-10.94 10.939",key:"12cjpa"}]]),H=(0,l.A)("ChevronUp",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]]);var U=a(88539),z=a(48156);function J(){let[e,s]=(0,f.useState)(!1),[a,i]=(0,f.useState)(""),[n,l]=(0,f.useState)(!1),[c,o]=(0,f.useState)(!1),[d,m]=(0,f.useState)(!1),[h,x]=(0,f.useState)(!0),{toast:p}=(0,L.dj)();if(h)return(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center mx-auto",children:(0,t.jsx)(F.A,{className:"w-6 h-6 text-primary animate-spin"})}),(0,t.jsx)("div",{children:(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"加载中..."})})]});let u=async s=>{if(s.preventDefault(),a){l(!0);try{if(!(await fetch("/api/webhook",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({url:a,enabled:e})})).ok)throw Error("Failed to save");p({title:"保存成功",description:"Webhook 配置已更新"})}catch(e){p({title:"保存失败",description:"请稍后重试",variant:"destructive"})}finally{l(!1)}}},y=async()=>{if(a){o(!0);try{if(!(await fetch("/api/webhook/test",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({url:a})})).ok)throw Error("测试失败");p({title:"测试成功",description:"Webhook 调用成功,请检查目标服务器是否收到请求"})}catch(e){p({title:"测试失败",description:"请检查 URL 是否正确且可访问",variant:"destructive"})}finally{o(!1)}}};return(0,t.jsxs)("form",{onSubmit:u,className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"space-y-0.5",children:[(0,t.jsx)(g.J,{children:"启用 Webhook"}),(0,t.jsx)("div",{className:"text-sm text-muted-foreground",children:"当收到新邮件时通知指定的 URL"})]}),(0,t.jsx)(K,{checked:e,onCheckedChange:s})]}),e&&(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(g.J,{htmlFor:"webhook-url",children:"Webhook URL"}),(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsx)(j.p,{id:"webhook-url",placeholder:"https://example.com/webhook",value:a,onChange:e=>i(e.target.value),type:"url",required:!0}),(0,t.jsx)(r.$,{type:"submit",disabled:n,className:"flex-shrink-0",children:n?(0,t.jsx)(F.A,{className:"w-4 h-4 animate-spin"}):"保存"}),(0,t.jsx)(z.Bc,{children:(0,t.jsxs)(z.m_,{children:[(0,t.jsx)(z.k$,{asChild:!0,children:(0,t.jsx)(r.$,{type:"button",variant:"outline",onClick:y,disabled:c||!a,children:c?(0,t.jsx)(F.A,{className:"w-4 h-4 animate-spin"}):(0,t.jsx)(D,{className:"w-4 h-4"})})}),(0,t.jsx)(z.ZI,{children:(0,t.jsx)("p",{children:"发送测试消息到此 Webhook"})})]})})]}),(0,t.jsx)("p",{className:"text-xs text-muted-foreground",children:"我们会向此 URL 发送 POST 请求,包含新邮件的相关信息"})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("button",{type:"button",className:"flex items-center gap-1 text-sm text-muted-foreground hover:text-foreground transition-colors",onClick:()=>m(!d),children:[d?(0,t.jsx)(H,{className:"w-4 h-4"}):(0,t.jsx)(U.A,{className:"w-4 h-4"}),"查看数据格式说明"]}),d&&(0,t.jsxs)("div",{className:"rounded-md bg-muted p-4 text-sm space-y-3",children:[(0,t.jsx)("p",{children:"当收到新邮件时，我们会向配置的 URL 发送 POST 请求，请求头包含:"}),(0,t.jsxs)("pre",{className:"bg-background p-2 rounded text-xs",children:["Content-Type: application/json","\n","X-Webhook-Event: new_message"]}),(0,t.jsx)("p",{children:"请求体示例:"}),(0,t.jsx)("pre",{className:"bg-background p-2 rounded text-xs overflow-auto",children:`{
  "emailId": "email-uuid",
  "messageId": "message-uuid",
  "fromAddress": "<EMAIL>",
  "subject": "邮件主题",
  "content": "邮件文本内容",
  "html": "邮件HTML内容",
  "receivedAt": "2024-01-01T12:00:00.000Z",
  "toAddress": "your-email@${window.location.host}"
}`})]})]})]})]})}var q=a(12779),G=a(67213);let Y={[q.gg.DUKE]:o,[q.gg.KNIGHT]:d,[q.gg.CIVILIAN]:m.A},B={[q.gg.DUKE]:"公爵",[q.gg.KNIGHT]:"骑士",[q.gg.CIVILIAN]:"平民"};function X(){let[e,s]=(0,f.useState)(""),[a,i]=(0,f.useState)(!1),[n,l]=(0,f.useState)(q.gg.KNIGHT),{toast:c}=(0,L.dj)(),h=async()=>{if(e){i(!0);try{let a=await fetch("/api/roles/users",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({searchText:e})}),t=await a.json();if(!a.ok)throw Error(t.error||"未知错误");if(!t.user){c({title:"未找到用户",description:"请确认用户名或邮箱地址是否正确",variant:"destructive"});return}if(t.user.role===n){c({title:`用户已是${B[n]}`,description:"无需重复设置"});return}let i=await fetch("/api/roles/promote",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({userId:t.user.id,roleName:n})});if(!i.ok){let e=await i.json();throw Error(e.error||"设置失败")}c({title:"设置成功",description:`已将用户 ${t.user.username||t.user.email} 设为${B[n]}`}),s("")}catch(e){c({title:"设置失败",description:e instanceof Error?e.message:"请稍后重试",variant:"destructive"})}finally{i(!1)}}},x=Y[n];return(0,t.jsxs)("div",{className:"bg-background rounded-lg border-2 border-primary/20 p-6",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2 mb-6",children:[(0,t.jsx)(x,{className:"w-5 h-5 text-primary"}),(0,t.jsx)("h2",{className:"text-lg font-semibold",children:"角色管理"})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex gap-4",children:[(0,t.jsx)("div",{className:"flex-1",children:(0,t.jsx)(j.p,{value:e,onChange:e=>s(e.target.value),placeholder:"输入用户名或邮箱"})}),(0,t.jsxs)(G.l6,{value:n,onValueChange:e=>l(e),children:[(0,t.jsx)(G.bq,{className:"w-32",children:(0,t.jsx)(G.yv,{})}),(0,t.jsxs)(G.gC,{children:[(0,t.jsx)(G.eb,{value:q.gg.DUKE,children:(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(o,{className:"w-4 h-4"}),"公爵"]})}),(0,t.jsx)(G.eb,{value:q.gg.KNIGHT,children:(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(d,{className:"w-4 h-4"}),"骑士"]})}),(0,t.jsx)(G.eb,{value:q.gg.CIVILIAN,children:(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(m.A,{className:"w-4 h-4"}),"平民"]})})]})]})]}),(0,t.jsx)(r.$,{onClick:h,disabled:a||!e.trim(),className:"w-full",children:a?(0,t.jsx)(F.A,{className:"w-4 h-4 animate-spin"}):`设为${B[n]}`})]})]})}function V(){let{data:e}=(0,n.wV)(),s=e?.user?.roles;return{checkPermission:e=>!!s&&(0,q._m)(s.map(e=>e.name),e),hasRole:e=>!!s&&s.some(s=>s.name===e),roles:s}}var W=a(32061);function Z(){let[e,s]=(0,f.useState)(""),[a,i]=(0,f.useState)(""),[n,l]=(0,f.useState)(""),[c,o]=(0,f.useState)(W.q.MAX_ACTIVE_EMAILS.toString()),[d,m]=(0,f.useState)(!1),{toast:h}=(0,L.dj)(),p=async()=>{m(!0);try{if(!(await fetch("/api/config",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({defaultRole:e,emailDomains:a,adminContact:n,maxEmails:c||W.q.MAX_ACTIVE_EMAILS.toString()})})).ok)throw Error("保存失败");h({title:"保存成功",description:"网站设置已更新"})}catch(e){h({title:"保存失败",description:e instanceof Error?e.message:"请稍后重试",variant:"destructive"})}finally{m(!1)}};return(0,t.jsxs)("div",{className:"bg-background rounded-lg border-2 border-primary/20 p-6",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2 mb-6",children:[(0,t.jsx)(x,{className:"w-5 h-5 text-primary"}),(0,t.jsx)("h2",{className:"text-lg font-semibold",children:"网站设置"})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center gap-4",children:[(0,t.jsx)("span",{className:"text-sm",children:"新用户默认角色:"}),(0,t.jsxs)(G.l6,{value:e,onValueChange:s,children:[(0,t.jsx)(G.bq,{className:"w-32",children:(0,t.jsx)(G.yv,{})}),(0,t.jsxs)(G.gC,{children:[(0,t.jsx)(G.eb,{value:q.gg.DUKE,children:"公爵"}),(0,t.jsx)(G.eb,{value:q.gg.KNIGHT,children:"骑士"}),(0,t.jsx)(G.eb,{value:q.gg.CIVILIAN,children:"平民"})]})]})]}),(0,t.jsxs)("div",{className:"flex items-center gap-4",children:[(0,t.jsx)("span",{className:"text-sm",children:"邮箱域名:"}),(0,t.jsx)("div",{className:"flex-1",children:(0,t.jsx)(j.p,{value:a,onChange:e=>i(e.target.value),placeholder:"多个域名用逗号分隔，如: moemail.app,bitibiti.com"})})]}),(0,t.jsxs)("div",{className:"flex items-center gap-4",children:[(0,t.jsx)("span",{className:"text-sm",children:"管理员联系方式:"}),(0,t.jsx)("div",{className:"flex-1",children:(0,t.jsx)(j.p,{value:n,onChange:e=>l(e.target.value),placeholder:"如: 微信号、邮箱等"})})]}),(0,t.jsxs)("div",{className:"flex items-center gap-4",children:[(0,t.jsx)("span",{className:"text-sm",children:"最大邮箱数量:"}),(0,t.jsx)("div",{className:"flex-1",children:(0,t.jsx)(j.p,{type:"number",min:"1",max:"100",value:c,onChange:e=>o(e.target.value),placeholder:`默认为 ${W.q.MAX_ACTIVE_EMAILS}`})})]}),(0,t.jsx)(r.$,{onClick:p,disabled:d,className:"w-full",children:"保存"})]})]})}var Q=a(84690),ee=a(11965),es=a(37800),ea=a(75456),et=a(12181),ei=a(6917),er=a(32290);function en(){let[e,s]=(0,f.useState)([]),[a,i]=(0,f.useState)(!1),[n,l]=(0,f.useState)(!1),[c,o]=(0,f.useState)(""),[d,m]=(0,f.useState)(null),{toast:h}=(0,L.dj)(),{copyToClipboard:x}=(0,ei.T)(),[p,u]=(0,f.useState)(!1),[y,v]=(0,f.useState)(!0),{checkPermission:N}=V(),b=N(q.Jj.MANAGE_API_KEY),w=async()=>{try{let e=await fetch("/api/api-keys");if(!e.ok)throw Error("获取 API Keys 失败");let a=await e.json();s(a.apiKeys)}catch(e){console.error(e),h({title:"获取失败",description:"获取 API Keys 列表失败",variant:"destructive"})}finally{v(!1)}},{config:A}=(0,er.U)(),k=async()=>{if(c.trim()){i(!0);try{let e=await fetch("/api/api-keys",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({name:c})});if(!e.ok)throw Error("创建 API Key 失败");let s=await e.json();m(s.key),w()}catch(e){h({title:"创建失败",description:e instanceof Error?e.message:"请稍后重试",variant:"destructive"}),l(!1)}finally{i(!1)}}},C=async(e,a)=>{try{if(!(await fetch(`/api/api-keys/${e}`,{method:"PATCH",headers:{"Content-Type":"application/json"},body:JSON.stringify({enabled:a})})).ok)throw Error("更新失败");s(s=>s.map(s=>s.id===e?{...s,enabled:a}:s))}catch(e){console.error(e),h({title:"更新失败",description:"更新 API Key 状态失败",variant:"destructive"})}},S=async e=>{try{if(!(await fetch(`/api/api-keys/${e}`,{method:"DELETE"})).ok)throw Error("删除失败");s(s=>s.filter(s=>s.id!==e)),h({title:"删除成功",description:"API Key 已删除"})}catch(e){console.error(e),h({title:"删除失败",description:"删除 API Key 失败",variant:"destructive"})}};return(0,t.jsxs)("div",{className:"bg-background rounded-lg border-2 border-primary/20 p-6 space-y-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(Q.A,{className:"w-5 h-5 text-primary"}),(0,t.jsx)("h2",{className:"text-lg font-semibold",children:"API Keys"})]}),b&&(0,t.jsxs)(et.lG,{open:n,onOpenChange:l,children:[(0,t.jsx)(et.zM,{asChild:!0,children:(0,t.jsxs)(r.$,{className:"gap-2",onClick:()=>l(!0),children:[(0,t.jsx)(ee.A,{className:"w-4 h-4"}),"创建 API Key"]})}),(0,t.jsxs)(et.Cf,{children:[(0,t.jsxs)(et.c7,{children:[(0,t.jsx)(et.L3,{children:d?"API Key 创建成功":"创建新的 API Key"}),d&&(0,t.jsx)(et.rr,{className:"text-destructive",children:"请立即保存此密钥，它只会显示一次且无法恢复"})]}),d?(0,t.jsx)("div",{className:"space-y-4 py-4",children:(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(g.J,{children:"API Key"}),(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsx)(j.p,{value:d,readOnly:!0,className:"font-mono text-sm"}),(0,t.jsx)(r.$,{variant:"outline",size:"icon",onClick:()=>x(d),children:(0,t.jsx)(es.A,{className:"w-4 h-4"})})]})]})}):(0,t.jsx)("div",{className:"space-y-4 py-4",children:(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(g.J,{children:"名称"}),(0,t.jsx)(j.p,{value:c,onChange:e=>o(e.target.value),placeholder:"为你的 API Key 起个名字"})]})}),(0,t.jsxs)(et.Es,{children:[(0,t.jsx)(et.HM,{asChild:!0,children:(0,t.jsx)(r.$,{variant:"outline",onClick:()=>{l(!1),o(""),m(null)},disabled:a,children:d?"完成":"取消"})}),!d&&(0,t.jsx)(r.$,{onClick:k,disabled:a||!c.trim(),children:a?(0,t.jsx)(F.A,{className:"w-4 h-4 animate-spin"}):"创建"})]})]})]})]}),b?(0,t.jsx)("div",{className:"space-y-4",children:y?(0,t.jsxs)("div",{className:"text-center py-8 space-y-3",children:[(0,t.jsx)("div",{className:"w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center mx-auto",children:(0,t.jsx)(F.A,{className:"w-6 h-6 text-primary animate-spin"})}),(0,t.jsx)("div",{children:(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"加载中..."})})]}):0===e.length?(0,t.jsxs)("div",{className:"text-center py-8 space-y-3",children:[(0,t.jsx)("div",{className:"w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center mx-auto",children:(0,t.jsx)(Q.A,{className:"w-6 h-6 text-primary"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-lg font-medium",children:"没有 API Keys"}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground mt-1",children:'点击上方的创建 "API Key" 按钮来创建你的第一个 API Key'})]})]}):(0,t.jsxs)(t.Fragment,{children:[e.map(e=>(0,t.jsxs)("div",{className:"flex items-center justify-between p-4 rounded-lg border bg-card",children:[(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsx)("div",{className:"font-medium",children:e.name}),(0,t.jsxs)("div",{className:"text-sm text-muted-foreground",children:["创建于 ",new Date(e.createdAt).toLocaleString()]})]}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(K,{checked:e.enabled,onCheckedChange:s=>C(e.id,s)}),(0,t.jsx)(r.$,{variant:"ghost",size:"icon",onClick:()=>S(e.id),children:(0,t.jsx)(ea.A,{className:"w-4 h-4"})})]})]},e.id)),(0,t.jsxs)("div",{className:"mt-8 space-y-4",children:[(0,t.jsxs)("button",{type:"button",className:"flex items-center gap-1 text-sm text-muted-foreground hover:text-foreground transition-colors",onClick:()=>u(!p),children:[p?(0,t.jsx)(H,{className:"w-4 h-4"}):(0,t.jsx)(U.A,{className:"w-4 h-4"}),"查看使用文档"]}),p&&(0,t.jsxs)("div",{className:"rounded-lg border bg-card p-4 space-y-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("div",{className:"text-sm font-medium",children:"生成临时邮箱"}),(0,t.jsx)(r.$,{variant:"ghost",size:"icon",onClick:()=>x(`curl -X POST ${window.location.protocol}//${window.location.host}/api/emails/generate \\
  -H "X-API-Key: YOUR_API_KEY" \\
  -H "Content-Type: application/json" \\
  -d '{
    "name": "test",
    "expiryTime": 3600000,
    "domain": "moemail.app"
  }'`),children:(0,t.jsx)(es.A,{className:"w-4 h-4"})})]}),(0,t.jsx)("pre",{className:"text-xs bg-muted/50 rounded-lg p-4 overflow-x-auto",children:`curl -X POST ${window.location.protocol}//${window.location.host}/api/emails/generate \\
  -H "X-API-Key: YOUR_API_KEY" \\
  -H "Content-Type: application/json" \\
  -d '{
    "name": "test",
    "expiryTime": 3600000,
    "domain": "moemail.app"
  }'`})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("div",{className:"text-sm font-medium",children:"获取邮箱列表"}),(0,t.jsx)(r.$,{variant:"ghost",size:"icon",onClick:()=>x(`curl ${window.location.protocol}//${window.location.host}/api/emails?cursor=CURSOR \\
  -H "X-API-Key: YOUR_API_KEY"`),children:(0,t.jsx)(es.A,{className:"w-4 h-4"})})]}),(0,t.jsx)("pre",{className:"text-xs bg-muted/50 rounded-lg p-4 overflow-x-auto",children:`curl ${window.location.protocol}//${window.location.host}/api/emails?cursor=CURSOR \\
  -H "X-API-Key: YOUR_API_KEY"`})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("div",{className:"text-sm font-medium",children:"获取邮件列表"}),(0,t.jsx)(r.$,{variant:"ghost",size:"icon",onClick:()=>x(`curl ${window.location.protocol}//${window.location.host}/api/emails/{emailId}?cursor=CURSOR \\
  -H "X-API-Key: YOUR_API_KEY"`),children:(0,t.jsx)(es.A,{className:"w-4 h-4"})})]}),(0,t.jsx)("pre",{className:"text-xs bg-muted/50 rounded-lg p-4 overflow-x-auto",children:`curl ${window.location.protocol}//${window.location.host}/api/emails/{emailId}?cursor=CURSOR \\
  -H "X-API-Key: YOUR_API_KEY"`})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("div",{className:"text-sm font-medium",children:"获取单封邮件"}),(0,t.jsx)(r.$,{variant:"ghost",size:"icon",onClick:()=>x(`curl ${window.location.protocol}//${window.location.host}/api/emails/{emailId}/{messageId} \\
  -H "X-API-Key: YOUR_API_KEY"`),children:(0,t.jsx)(es.A,{className:"w-4 h-4"})})]}),(0,t.jsx)("pre",{className:"text-xs bg-muted/50 rounded-lg p-4 overflow-x-auto",children:`curl ${window.location.protocol}//${window.location.host}/api/emails/{emailId}/{messageId} \\
  -H "X-API-Key: YOUR_API_KEY"`})]}),(0,t.jsxs)("div",{className:"text-xs text-muted-foreground mt-4",children:[(0,t.jsx)("p",{children:"注意："}),(0,t.jsxs)("ul",{className:"list-disc list-inside space-y-1 mt-2",children:[(0,t.jsx)("li",{children:"请将 YOUR_API_KEY 替换为你的实际 API Key"}),(0,t.jsx)("li",{children:"emailId 是邮箱的唯一标识符"}),(0,t.jsx)("li",{children:"messageId 是邮件的唯一标识符"}),(0,t.jsx)("li",{children:"expiryTime 是邮箱的有效期（毫秒），可选值：3600000（1小时）、86400000（1天）、604800000（7天）、0（永久）"}),(0,t.jsx)("li",{children:"domain 是邮箱域名，可通过 /api/emails/domains 获取可用域名列表"}),(0,t.jsx)("li",{children:"cursor 用于分页，从上一次请求的响应中获取 nextCursor"}),(0,t.jsx)("li",{children:"所有请求都需要包含 X-API-Key 请求头"})]})]})]})]})]})}):(0,t.jsxs)("div",{className:"text-center text-muted-foreground py-8",children:[(0,t.jsx)("p",{children:"需要公爵或更高权限才能管理 API Key"}),(0,t.jsx)("p",{className:"mt-2",children:"请联系网站管理员升级您的角色"}),A?.adminContact&&(0,t.jsxs)("p",{className:"mt-2",children:["管理员联系方式：",A.adminContact]})]})]})}var el=a(18866);let ec=(0,l.A)("Eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]),eo=(0,l.A)("EyeOff",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]]),ed=(0,l.A)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]]);function em(){let[e,s]=(0,f.useState)([]),[a,i]=(0,f.useState)([]),[n,l]=(0,f.useState)(!1),[c,o]=(0,f.useState)(!1),[d,m]=(0,f.useState)(!1),[h,x]=(0,f.useState)({jwt:""}),[u,y]=(0,f.useState)(""),[v,N]=(0,f.useState)(null),[b,w]=(0,f.useState)([]),[A,k]=(0,f.useState)([]),[C,S]=(0,f.useState)("input"),[P,E]=(0,f.useState)(!1),[I,T]=(0,f.useState)(!1),[_,O]=(0,f.useState)(!0),[M,R]=(0,f.useState)(!1),{toast:$}=(0,L.dj)(),{checkPermission:K}=V(),D=K(q.Jj.MANAGE_WEBHOOK),z=async()=>{if(D)try{let e=await fetch("/api/email-credentials");if(!e.ok)throw Error("获取邮箱凭证失败");let a=await e.json();s(a.credentials)}catch(e){console.error(e),$({title:"获取失败",description:"获取邮箱凭证列表失败",variant:"destructive"})}},J=async()=>{try{let e=await fetch("/api/email-credentials/bindings");if(!e.ok)throw Error("获取邮箱绑定失败");let s=await e.json();i(s.bindings)}catch(e){console.error(e),$({title:"获取失败",description:"获取邮箱绑定列表失败",variant:"destructive"})}},G=async()=>{O(!0),await Promise.all([D?z():Promise.resolve(),J()]),O(!1),R(!1)},{config:Y}=(0,er.U)(),B=async()=>{R(!0),await G()},X=async()=>{if(!h.jwt.trim()){$({title:"请输入邮箱凭证",description:"邮箱凭证不能为空",variant:"destructive"});return}l(!0);try{let e=await fetch("/api/email-credentials",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(h)});if(!e.ok){let s=await e.json();throw Error(s.error||"绑定邮箱失败")}$({title:"绑定成功",description:"邮箱绑定成功"}),J(),Z()}catch(e){$({title:"绑定失败",description:e instanceof Error?e.message:"请稍后重试",variant:"destructive"})}finally{l(!1)}},W=async()=>{if("input"===C){if(!u.trim()){$({title:"请输入邮箱地址",description:"邮箱地址不能为空",variant:"destructive"});return}}else if(0===A.length){$({title:"请选择邮箱",description:"至少选择一个邮箱",variant:"destructive"});return}l(!0);try{let e=D?"/api/email-credentials/extract":"/api/email-credentials/extract-user",s="input"===C?{emailAddress:u}:{emailAddresses:A},a=await fetch(e,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(s)});if(!a.ok){let e=await a.json();throw Error(e.error||"提取邮箱凭证失败")}let t=await a.json();"input"===C?(N(t.credential),w([])):(w(t.credentials),N(null)),$({title:"提取成功",description:`已提取 ${"input"===C?1:t.credentials.length} 个邮箱凭证`})}catch(e){$({title:"提取失败",description:e instanceof Error?e.message:"请稍后重试",variant:"destructive"})}finally{l(!1)}},Z=()=>{o(!1),x({jwt:""})},Q=async e=>{try{if(!(await fetch("/api/email-credentials/bindings",{method:"DELETE",headers:{"Content-Type":"application/json"},body:JSON.stringify({emailAddress:e})})).ok)throw Error("解绑失败");i(s=>s.filter(s=>s.emailAddress!==e)),$({title:"解绑成功",description:"邮箱已解绑"})}catch(e){console.error(e),$({title:"解绑失败",description:"解绑邮箱失败",variant:"destructive"})}},ei=async e=>{try{await navigator.clipboard.writeText(e),$({title:"复制成功",description:"已复制到剪贴板"})}catch(e){$({title:"复制失败",description:"无法复制到剪贴板",variant:"destructive"})}};return(0,t.jsxs)("div",{className:"bg-background rounded-lg border-2 border-primary/20 p-6 space-y-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(p.A,{className:"w-5 h-5 text-primary"}),(0,t.jsx)("h2",{className:"text-lg font-semibold",children:"邮箱凭证系统"})]}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(r.$,{variant:"ghost",size:"icon",onClick:B,disabled:M,className:M?"animate-spin":"",children:(0,t.jsx)(el.A,{className:"w-4 h-4"})}),(0,t.jsxs)(et.lG,{open:c,onOpenChange:o,children:[(0,t.jsx)(et.zM,{asChild:!0,children:(0,t.jsxs)(r.$,{className:"gap-2",children:[(0,t.jsx)(ee.A,{className:"w-4 h-4"}),"绑定邮箱"]})}),(0,t.jsxs)(et.Cf,{className:"max-w-md",children:[(0,t.jsxs)(et.c7,{children:[(0,t.jsx)(et.L3,{children:"绑定邮箱"}),(0,t.jsx)(et.rr,{children:"输入邮箱凭证来绑定邮箱到您的账户"})]}),(0,t.jsx)("div",{className:"space-y-4 py-4",children:(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(g.J,{children:"邮箱凭证 (JWT)"}),(0,t.jsx)(j.p,{value:h.jwt,onChange:e=>x(s=>({...s,jwt:e.target.value})),placeholder:"请输入邮箱凭证..."})]})}),(0,t.jsxs)(et.Es,{children:[(0,t.jsx)(et.HM,{asChild:!0,children:(0,t.jsx)(r.$,{variant:"outline",onClick:Z,disabled:n,children:"取消"})}),(0,t.jsx)(r.$,{onClick:X,disabled:n,children:n?(0,t.jsx)(F.A,{className:"w-4 h-4 animate-spin"}):"绑定"})]})]})]}),D&&(0,t.jsxs)(et.lG,{open:d,onOpenChange:m,children:[(0,t.jsx)(et.zM,{asChild:!0,children:(0,t.jsxs)(r.$,{variant:"outline",className:"gap-2",children:[(0,t.jsx)(ec,{className:"w-4 h-4"}),"提取凭证"]})}),(0,t.jsxs)(et.Cf,{className:"max-w-md",children:[(0,t.jsxs)(et.c7,{children:[(0,t.jsx)(et.L3,{children:"提取邮箱凭证"}),(0,t.jsx)(et.rr,{children:"输入邮箱地址来提取对应的邮箱凭证"})]}),(0,t.jsxs)("div",{className:"space-y-4 py-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(g.J,{children:"邮箱地址"}),(0,t.jsx)(j.p,{type:"email",value:u,onChange:e=>y(e.target.value),placeholder:"<EMAIL>"})]}),v&&(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(g.J,{children:"提取的凭证"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(j.p,{type:P?"text":"password",value:v.jwt,readOnly:!0,className:"pr-20"}),(0,t.jsxs)("div",{className:"absolute right-2 top-0 h-full flex items-center gap-1",children:[(0,t.jsx)(r.$,{type:"button",variant:"ghost",size:"icon",onClick:()=>E(!P),children:P?(0,t.jsx)(eo,{className:"w-4 h-4"}):(0,t.jsx)(ec,{className:"w-4 h-4"})}),(0,t.jsx)(r.$,{type:"button",variant:"ghost",size:"icon",onClick:()=>ei(v.jwt),children:(0,t.jsx)(es.A,{className:"w-4 h-4"})})]})]}),(0,t.jsxs)("div",{className:"text-sm text-muted-foreground",children:["绑定用户数: ",v.bindingCount]})]})]}),(0,t.jsxs)(et.Es,{children:[(0,t.jsx)(et.HM,{asChild:!0,children:(0,t.jsx)(r.$,{variant:"outline",onClick:()=>{m(!1),y(""),N(null),w([]),k([]),S("input"),E(!1)},children:"关闭"})}),(0,t.jsx)(r.$,{onClick:W,disabled:n,children:n?(0,t.jsx)(F.A,{className:"w-4 h-4 animate-spin"}):"提取"})]})]})]})]})]}),_?(0,t.jsxs)("div",{className:"text-center py-8 space-y-3",children:[(0,t.jsx)("div",{className:"w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center mx-auto",children:(0,t.jsx)(F.A,{className:"w-6 h-6 text-primary animate-spin"})}),(0,t.jsx)("div",{children:(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"加载中..."})})]}):(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)("h3",{className:"text-lg font-medium",children:"我绑定的邮箱"}),0===a.length?(0,t.jsxs)("div",{className:"text-center py-6 space-y-3 border rounded-lg",children:[(0,t.jsx)("div",{className:"w-10 h-10 rounded-full bg-primary/10 flex items-center justify-center mx-auto",children:(0,t.jsx)(p.A,{className:"w-5 h-5 text-primary"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"您还没有绑定任何邮箱"}),(0,t.jsx)("p",{className:"text-xs text-muted-foreground mt-1",children:'点击上方的 "绑定邮箱" 按钮来绑定第一个邮箱'})]})]}):(0,t.jsx)("div",{className:"space-y-2",children:a.map(e=>(0,t.jsxs)("div",{className:"flex items-center justify-between p-3 rounded-lg border bg-card",children:[(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsx)("div",{className:"font-medium",children:e.emailAddress}),(0,t.jsxs)("div",{className:"text-xs text-muted-foreground",children:["绑定于 ",new Date(e.createdAt).toLocaleString()]})]}),(0,t.jsx)(r.$,{variant:"ghost",size:"sm",onClick:()=>Q(e.emailAddress),children:(0,t.jsx)(ea.A,{className:"w-4 h-4"})})]},e.id))})]}),D&&(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)("h3",{className:"text-lg font-medium",children:"系统邮箱凭证 (管理员)"}),0===e.length?(0,t.jsxs)("div",{className:"text-center py-6 space-y-3 border rounded-lg",children:[(0,t.jsx)("div",{className:"w-10 h-10 rounded-full bg-primary/10 flex items-center justify-center mx-auto",children:(0,t.jsx)(p.A,{className:"w-5 h-5 text-primary"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"系统中还没有邮箱凭证"}),(0,t.jsx)("p",{className:"text-xs text-muted-foreground mt-1",children:"创建邮箱时会自动生成对应的凭证"})]})]}):(0,t.jsx)("div",{className:"space-y-2",children:e.map(e=>(0,t.jsxs)("div",{className:"flex items-center justify-between p-4 rounded-lg border bg-card",children:[(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsx)("div",{className:"font-medium",children:e.emailAddress}),(0,t.jsxs)("div",{className:"flex items-center gap-4 text-xs text-muted-foreground",children:[(0,t.jsxs)("span",{children:["创建于 ",new Date(e.createdAt).toLocaleString()]}),e.lastUsedAt&&(0,t.jsxs)("span",{children:["最后使用: ",new Date(e.lastUsedAt).toLocaleString()]}),(0,t.jsxs)("div",{className:"flex items-center gap-1",children:[(0,t.jsx)(ed,{className:"w-3 h-3"}),(0,t.jsxs)("span",{children:[e.bindingCount," 个用户绑定"]})]})]})]}),(0,t.jsx)("div",{className:"flex items-center gap-2",children:(0,t.jsx)("span",{className:`text-xs px-2 py-1 rounded ${e.enabled?"bg-green-100 text-green-800":"bg-gray-100 text-gray-800"}`,children:e.enabled?"启用":"禁用"})})]},e.id))})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("button",{type:"button",className:"flex items-center gap-1 text-sm text-muted-foreground hover:text-foreground transition-colors",onClick:()=>T(!I),children:[I?(0,t.jsx)(H,{className:"w-4 h-4"}):(0,t.jsx)(U.A,{className:"w-4 h-4"}),"查看使用说明"]}),I&&(0,t.jsxs)("div",{className:"rounded-lg border bg-card p-4 space-y-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("h4",{className:"font-medium",children:"邮箱凭证系统说明"}),(0,t.jsxs)("div",{className:"text-sm text-muted-foreground space-y-2",children:[(0,t.jsxs)("p",{children:["1. ",(0,t.jsx)("strong",{children:"邮箱创建"}),"：系统创建邮箱时会自动生成对应的JWT凭证"]}),(0,t.jsxs)("p",{children:["2. ",(0,t.jsx)("strong",{children:"凭证绑定"}),"：用户可以通过邮箱凭证绑定邮箱到自己的账户"]}),(0,t.jsxs)("p",{children:["3. ",(0,t.jsx)("strong",{children:"凭证提取"}),"：管理员可以提取任何邮箱的凭证供用户使用"]}),(0,t.jsxs)("p",{children:["4. ",(0,t.jsx)("strong",{children:"绑定统计"}),"：显示每个邮箱绑定了多少个用户（不包括管理员）"]})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("h4",{className:"font-medium",children:"使用流程"}),(0,t.jsxs)("div",{className:"text-sm text-muted-foreground space-y-1",children:[(0,t.jsxs)("p",{children:[(0,t.jsx)("strong",{children:"用户："}),' 获取邮箱凭证 → 点击"绑定邮箱" → 输入凭证 → 完成绑定']}),(0,t.jsxs)("p",{children:[(0,t.jsx)("strong",{children:"管理员："})," 查看所有邮箱凭证 → 提取特定邮箱凭证 → 提供给用户"]})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("h4",{className:"font-medium",children:"安全说明"}),(0,t.jsxs)("div",{className:"text-sm text-muted-foreground space-y-1",children:[(0,t.jsx)("p",{children:"• 邮箱凭证是JWT格式，包含邮箱访问权限"}),(0,t.jsx)("p",{children:"• 凭证永久有效，请妥善保管"}),(0,t.jsx)("p",{children:"• 系统会记录凭证使用情况"}),(0,t.jsx)("p",{children:"• 管理员可以查看绑定统计信息"})]})]})]})]})]})]})}let eh={emperor:{name:"皇帝",icon:c},duke:{name:"公爵",icon:o},knight:{name:"骑士",icon:d},civilian:{name:"平民",icon:m.A}};function ex({user:e}){let s=(0,u.rd)(),{checkPermission:a}=V(),l=a(q.Jj.MANAGE_WEBHOOK),c=a(q.Jj.PROMOTE_USER),o=a(q.Jj.MANAGE_CONFIG),d=a(q.Jj.MANAGE_EMAIL_CREDENTIALS);return(0,t.jsxs)("div",{className:"max-w-2xl mx-auto space-y-6",children:[(0,t.jsx)("div",{className:"bg-background rounded-lg border-2 border-primary/20 p-6",children:(0,t.jsxs)("div",{className:"flex items-center gap-6",children:[(0,t.jsx)("div",{className:"relative",children:e.image&&(0,t.jsx)(i.A,{src:e.image,alt:e.name||"用户头像",width:80,height:80,className:"rounded-full ring-2 ring-primary/20"})}),(0,t.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("h2",{className:"text-xl font-bold truncate",children:e.name}),e.email&&(0,t.jsxs)("div",{className:"flex items-center gap-1 text-xs text-primary bg-primary/10 px-2 py-0.5 rounded-full flex-shrink-0",children:[(0,t.jsx)(h.A,{className:"w-3 h-3"}),"已关联"]})]}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground truncate mt-1",children:e.email?e.email:`用户名: ${e.username}`}),e.roles&&(0,t.jsx)("div",{className:"flex gap-2 mt-2",children:e.roles.map(({name:e})=>{let s=eh[e],a=s.icon;return(0,t.jsxs)("div",{className:"flex items-center gap-1 text-xs bg-primary/10 text-primary px-2 py-0.5 rounded",title:s.name,children:[(0,t.jsx)(a,{className:"w-3 h-3"}),s.name]},e)})})]})]})}),l&&(0,t.jsxs)("div",{className:"bg-background rounded-lg border-2 border-primary/20 p-6",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2 mb-6",children:[(0,t.jsx)(x,{className:"w-5 h-5 text-primary"}),(0,t.jsx)("h2",{className:"text-lg font-semibold",children:"Webhook 配置"})]}),(0,t.jsx)(J,{})]}),o&&(0,t.jsx)(Z,{}),c&&(0,t.jsx)(X,{}),l&&(0,t.jsx)(en,{}),d&&(0,t.jsx)(em,{}),(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 px-1",children:[(0,t.jsxs)(r.$,{onClick:()=>s.push("/moe"),className:"gap-2 flex-1",children:[(0,t.jsx)(p.A,{className:"w-4 h-4"}),"返回邮箱"]}),(0,t.jsx)(r.$,{variant:"outline",onClick:()=>(0,n.CI)({callbackUrl:"/"}),className:"flex-1",children:"退出登录"})]})]})}},93295:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(93823).A)("UserRound",[["circle",{cx:"12",cy:"8",r:"5",key:"1hypcn"}],["path",{d:"M20 21a8 8 0 0 0-16 0",key:"rfgkzh"}]])},9090:(e,s,a)=>{"use strict";a.d(s,{ProfileCard:()=>t});let t=(0,a(45710).YR)(function(){throw Error("Attempted to call ProfileCard() from the server but ProfileCard is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"F:\\CODE\\Project\\tempmail\\moemail\\app\\components\\profile\\profile-card.tsx","ProfileCard")},51758:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>o,runtime:()=>c});var t=a(4302),i=a(96942),r=a(9090),n=a(83553),l=a(51756);let c="edge";async function o(){let e=await (0,n.j2)();return e?.user||(0,l.V2)("/"),(0,t.jsx)("div",{className:"min-h-screen bg-gradient-to-b from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800",children:(0,t.jsxs)("div",{className:"container mx-auto px-4 lg:px-8 max-w-[1600px]",children:[(0,t.jsx)(i.Y,{}),(0,t.jsx)("main",{className:"pt-20 pb-5",children:(0,t.jsx)(r.ProfileCard,{user:e.user})})]})})}}},e=>{var s=s=>e(e.s=s);e.O(0,[730,752,899,498,943,86,672,156,481,889],()=>s(57102));var a=e.O();(_ENTRIES="undefined"==typeof _ENTRIES?{}:_ENTRIES)["middleware_app/profile/page"]=a}]);
//# sourceMappingURL=page.js.map