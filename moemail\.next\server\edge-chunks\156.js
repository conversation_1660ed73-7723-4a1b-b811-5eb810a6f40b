"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[156],{5120:(e,t,a)=>{a.d(t,{F6:()=>s}),a(85885),a(43144),a(25601);var r=a(83553),n=a(45669);let s=async()=>{let e=(await (0,n.b3)()).get("X-User-Id");if(e)return e;let t=await (0,r.j2)();return t?.user.id}},83553:(e,t,a)=>{a.d(t,{fG:()=>g,LO:()=>D,iz:()=>y,j2:()=>Q,Yj:()=>N,uG:()=>E,kz:()=>R});var r=a(36348),n=a(18779),s=a(32964),i=a(85885),d=a(43144),l=a(13091),o=a(69179),u=a(17451),m=a(1120),c=a(29678),_=a(93252),p=a(25356).Buffer;let f=["#2196F3","#009688","#9C27B0","#F44336","#673AB7","#3F51B5","#4CAF50","#FF5722","#795548","#607D8B"];var I=a(5120);let A={[u.gg.EMPEROR]:"皇帝（网站所有者）",[u.gg.DUKE]:"公爵（超级用户）",[u.gg.KNIGHT]:"骑士（高级用户）",[u.gg.CIVILIAN]:"平民（普通用户）"},w=async()=>await (0,o.getRequestContext)().env.SITE_CONFIG.get("DEFAULT_ROLE")===u.gg.KNIGHT?u.gg.KNIGHT:u.gg.CIVILIAN;async function q(e,t){let a=await e.query.roles.findFirst({where:(0,l.eq)(d.roles.name,t)});if(!a){let[r]=await e.insert(d.roles).values({name:t,description:A[t]}).returning();a=r}return a}async function y(e,t,a){await e.delete(d.userRoles).where((0,l.eq)(d.userRoles.userId,t)),await e.insert(d.userRoles).values({userId:t,roleId:a})}async function E(e){let t=(0,i.d)();return(await t.query.userRoles.findMany({where:(0,l.eq)(d.userRoles.userId,e),with:{role:!0}}))[0].role.name}async function N(e){let t=await (0,I.F6)();if(!t)return!1;let a=(0,i.d)(),r=(await a.query.userRoles.findMany({where:(0,l.eq)(d.userRoles.userId,t),with:{role:!0}})).map(e=>e.role.name);return(0,u._m)(r,e)}let{handlers:{GET:g,POST:D},auth:Q,signIn:h,signOut:x}=(0,r.Ay)(()=>({secret:process.env.AUTH_SECRET,adapter:(0,s._)((0,i.d)(),{usersTable:d.users,accountsTable:d.accounts}),providers:[(0,n.A)({clientId:process.env.AUTH_GITHUB_ID,clientSecret:process.env.AUTH_GITHUB_SECRET}),(0,m.A)({name:"Credentials",credentials:{username:{label:"用户名",type:"text",placeholder:"请输入用户名"},password:{label:"密码",type:"password",placeholder:"请输入密码"}},async authorize(e){if(!e)throw Error("请输入用户名和密码");let{username:t,password:a}=e;try{_.Q.parse({username:t,password:a})}catch(e){throw Error("输入格式不正确")}let r=(0,i.d)(),n=await r.query.users.findFirst({where:(0,l.eq)(d.users.username,t)});if(!n||!await (0,c.b)(a,n.password))throw Error("用户名或密码错误");return{...n,password:void 0}}})],events:{async signIn({user:e}){if(e.id)try{let t=(0,i.d)();if(await t.query.userRoles.findFirst({where:(0,l.eq)(d.userRoles.userId,e.id)}))return;let a=await w(),r=await q(t,a);await y(t,e.id,r.id)}catch(e){console.error("Error assigning role:",e)}}},callbacks:{jwt:async({token:e,user:t})=>(t&&(e.id=t.id,e.name=t.name||t.username,e.username=t.username,e.image=t.image||function(e){let t=e[0].toUpperCase(),a=Array.from(e).reduce((e,t)=>e+t.charCodeAt(0),0)%f.length,r=f[a],n=`
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100" width="100" height="100">
      <rect width="100" height="100" fill="${r}"/>
      <text 
        x="50%" 
        y="50%" 
        fill="white" 
        font-family="system-ui, -apple-system, sans-serif" 
        font-size="45" 
        font-weight="500"
        text-anchor="middle"
        alignment-baseline="central"
        dominant-baseline="central"
        style="text-transform: uppercase"
      >
        ${t}
      </text>
    </svg>
  `.trim(),s=new TextEncoder().encode(n),i=p.from(s).toString("base64");return`data:image/svg+xml;base64,${i}`}(e.name)),e),async session({session:e,token:t}){if(t&&e.user){e.user.id=t.id,e.user.name=t.name,e.user.username=t.username,e.user.image=t.image;let a=(0,i.d)(),r=await a.query.userRoles.findMany({where:(0,l.eq)(d.userRoles.userId,e.user.id),with:{role:!0}});if(!r.length){let t=await w(),n=await q(a,t);await y(a,e.user.id,n.id),r=[{userId:e.user.id,roleId:n.id,createdAt:new Date,role:n}]}e.user.roles=r.map(e=>({name:e.role.name}))}return e}},session:{strategy:"jwt"}}));async function R(e,t){let a=(0,i.d)();if(await a.query.users.findFirst({where:(0,l.eq)(d.users.username,e)}))throw Error("用户名已存在");let r=await (0,c.E)(t),[n]=await a.insert(d.users).values({username:e,password:r}).returning();return n}},85885:(e,t,a)=>{a.d(t,{d:()=>i});var r=a(69179),n=a(39235),s=a(43144);let i=()=>(0,n.f)((0,r.getRequestContext)().env.DB,{schema:s})},17451:(e,t,a)=>{a.d(t,{Jj:()=>n,_m:()=>i,gg:()=>r});let r={EMPEROR:"emperor",DUKE:"duke",KNIGHT:"knight",CIVILIAN:"civilian"},n={MANAGE_EMAIL:"manage_email",MANAGE_WEBHOOK:"manage_webhook",PROMOTE_USER:"promote_user",MANAGE_CONFIG:"manage_config",MANAGE_API_KEY:"manage_api_key",MANAGE_EMAIL_CREDENTIALS:"manage_email_credentials"},s={[r.EMPEROR]:Object.values(n),[r.DUKE]:[n.MANAGE_EMAIL,n.MANAGE_WEBHOOK,n.MANAGE_API_KEY,n.MANAGE_EMAIL_CREDENTIALS],[r.KNIGHT]:[n.MANAGE_EMAIL,n.MANAGE_WEBHOOK,n.MANAGE_EMAIL_CREDENTIALS],[r.CIVILIAN]:[n.MANAGE_EMAIL,n.MANAGE_EMAIL_CREDENTIALS]};function i(e,t){return e.some(e=>s[e]?.includes(t))}},43144:(e,t,a)=>{a.r(t),a.d(t,{accounts:()=>u,apiKeys:()=>I,apiKeysRelations:()=>A,emailCredentials:()=>w,emailCredentialsRelations:()=>q,emailVerificationCodes:()=>N,emails:()=>m,messages:()=>c,roles:()=>p,rolesRelations:()=>Q,userEmailBindings:()=>y,userEmailBindingsRelations:()=>E,userRoles:()=>f,userRolesRelations:()=>g,users:()=>o,usersRelations:()=>D,webhooks:()=>_});var r=a(82792),n=a(50382),s=a(71647),i=a(89376),d=a(57689),l=a(88795);let o=(0,r.D)("user",{id:(0,n.Qq)("id").primaryKey().$defaultFn(()=>crypto.randomUUID()),name:(0,n.Qq)("name"),email:(0,n.Qq)("email").unique(),emailVerified:(0,s.nd)("emailVerified",{mode:"timestamp_ms"}),image:(0,n.Qq)("image"),username:(0,n.Qq)("username").unique(),password:(0,n.Qq)("password")}),u=(0,r.D)("account",{userId:(0,n.Qq)("userId").notNull().references(()=>o.id,{onDelete:"cascade"}),type:(0,n.Qq)("type").$type().notNull(),provider:(0,n.Qq)("provider").notNull(),providerAccountId:(0,n.Qq)("providerAccountId").notNull(),refresh_token:(0,n.Qq)("refresh_token"),access_token:(0,n.Qq)("access_token"),expires_at:(0,s.nd)("expires_at"),token_type:(0,n.Qq)("token_type"),scope:(0,n.Qq)("scope"),id_token:(0,n.Qq)("id_token"),session_state:(0,n.Qq)("session_state")},e=>({compoundKey:(0,i.ie)({columns:[e.provider,e.providerAccountId]})})),m=(0,r.D)("email",{id:(0,n.Qq)("id").primaryKey().$defaultFn(()=>crypto.randomUUID()),address:(0,n.Qq)("address").notNull().unique(),userId:(0,n.Qq)("userId").references(()=>o.id,{onDelete:"cascade"}),createdAt:(0,s.nd)("created_at",{mode:"timestamp_ms"}).notNull().$defaultFn(()=>new Date),expiresAt:(0,s.nd)("expires_at",{mode:"timestamp_ms"}).notNull()},e=>({expiresAtIdx:(0,d.Pe)("email_expires_at_idx").on(e.expiresAt)})),c=(0,r.D)("message",{id:(0,n.Qq)("id").primaryKey().$defaultFn(()=>crypto.randomUUID()),emailId:(0,n.Qq)("emailId").notNull().references(()=>m.id,{onDelete:"cascade"}),fromAddress:(0,n.Qq)("from_address").notNull(),subject:(0,n.Qq)("subject").notNull(),content:(0,n.Qq)("content").notNull(),html:(0,n.Qq)("html"),receivedAt:(0,s.nd)("received_at",{mode:"timestamp_ms"}).notNull().$defaultFn(()=>new Date)},e=>({emailIdIdx:(0,d.Pe)("message_email_id_idx").on(e.emailId)})),_=(0,r.D)("webhook",{id:(0,n.Qq)("id").primaryKey().$defaultFn(()=>crypto.randomUUID()),userId:(0,n.Qq)("user_id").notNull().references(()=>o.id,{onDelete:"cascade"}),url:(0,n.Qq)("url").notNull(),enabled:(0,s.nd)("enabled",{mode:"boolean"}).notNull().default(!0),createdAt:(0,s.nd)("created_at",{mode:"timestamp_ms"}).notNull().$defaultFn(()=>new Date),updatedAt:(0,s.nd)("updated_at",{mode:"timestamp_ms"}).notNull().$defaultFn(()=>new Date)}),p=(0,r.D)("role",{id:(0,n.Qq)("id").primaryKey().$defaultFn(()=>crypto.randomUUID()),name:(0,n.Qq)("name").notNull(),description:(0,n.Qq)("description"),createdAt:(0,s.nd)("created_at",{mode:"timestamp"}).$defaultFn(()=>new Date),updatedAt:(0,s.nd)("updated_at",{mode:"timestamp"}).$defaultFn(()=>new Date)}),f=(0,r.D)("user_role",{userId:(0,n.Qq)("user_id").notNull().references(()=>o.id,{onDelete:"cascade"}),roleId:(0,n.Qq)("role_id").notNull().references(()=>p.id,{onDelete:"cascade"}),createdAt:(0,s.nd)("created_at",{mode:"timestamp"}).$defaultFn(()=>new Date)},e=>({pk:(0,i.ie)({columns:[e.userId,e.roleId]})})),I=(0,r.D)("api_keys",{id:(0,n.Qq)("id").primaryKey().$defaultFn(()=>crypto.randomUUID()),userId:(0,n.Qq)("user_id").notNull().references(()=>o.id),name:(0,n.Qq)("name").notNull(),key:(0,n.Qq)("key").notNull().unique(),createdAt:(0,s.nd)("created_at",{mode:"timestamp"}).$defaultFn(()=>new Date),expiresAt:(0,s.nd)("expires_at",{mode:"timestamp"}),enabled:(0,s.nd)("enabled",{mode:"boolean"}).notNull().default(!0)},e=>({nameUserIdUnique:(0,d.GL)("name_user_id_unique").on(e.name,e.userId)})),A=(0,l.K1)(I,({one:e})=>({user:e(o,{fields:[I.userId],references:[o.id]})})),w=(0,r.D)("email_credentials",{id:(0,n.Qq)("id").primaryKey().$defaultFn(()=>crypto.randomUUID()),userId:(0,n.Qq)("user_id").notNull().references(()=>o.id,{onDelete:"cascade"}),emailAddress:(0,n.Qq)("email_address").notNull(),name:(0,n.Qq)("name").notNull(),jwt:(0,n.Qq)("jwt").notNull(),enabled:(0,s.nd)("enabled",{mode:"boolean"}).notNull().default(!0),createdAt:(0,s.nd)("created_at",{mode:"timestamp"}).$defaultFn(()=>new Date),expiresAt:(0,s.nd)("expires_at",{mode:"timestamp"}),lastUsedAt:(0,s.nd)("last_used_at",{mode:"timestamp"})},e=>({emailUnique:(0,d.GL)("email_address_unique").on(e.emailAddress),nameUserUnique:(0,d.GL)("email_credential_name_user_id_unique").on(e.name,e.userId)})),q=(0,l.K1)(w,({many:e})=>({bindings:e(y)})),y=(0,r.D)("user_email_bindings",{id:(0,n.Qq)("id").primaryKey().$defaultFn(()=>crypto.randomUUID()),userId:(0,n.Qq)("user_id").notNull().references(()=>o.id,{onDelete:"cascade"}),emailId:(0,n.Qq)("email_id").notNull().references(()=>m.id,{onDelete:"cascade"}),credentialId:(0,n.Qq)("credential_id").references(()=>w.id,{onDelete:"set null"}),createdAt:(0,s.nd)("created_at",{mode:"timestamp"}).$defaultFn(()=>new Date)},e=>({userEmailUnique:(0,d.GL)("user_email_binding_unique").on(e.userId,e.emailId)})),E=(0,l.K1)(y,({one:e})=>({user:e(o,{fields:[y.userId],references:[o.id]}),email:e(m,{fields:[y.emailId],references:[m.id]}),credential:e(w,{fields:[y.credentialId],references:[w.id]})})),N=(0,r.D)("email_verification_codes",{id:(0,n.Qq)("id").primaryKey().$defaultFn(()=>crypto.randomUUID()),email:(0,n.Qq)("email").notNull(),code:(0,n.Qq)("code").notNull(),createdAt:(0,s.nd)("created_at",{mode:"timestamp"}).$defaultFn(()=>new Date),expiresAt:(0,s.nd)("expires_at",{mode:"timestamp"}).notNull(),used:(0,s.nd)("used",{mode:"boolean"}).notNull().default(!1)},e=>({emailIdx:(0,d.Pe)("email_verification_email_idx").on(e.email),expiresAtIdx:(0,d.Pe)("email_verification_expires_at_idx").on(e.expiresAt)})),g=(0,l.K1)(f,({one:e})=>({user:e(o,{fields:[f.userId],references:[o.id]}),role:e(p,{fields:[f.roleId],references:[p.id]})})),D=(0,l.K1)(o,({many:e})=>({userRoles:e(f),apiKeys:e(I)})),Q=(0,l.K1)(p,({many:e})=>({userRoles:e(f)}))},29678:(e,t,a)=>{a.d(t,{E:()=>i,b:()=>d,cn:()=>s});var r=a(41649),n=a(6477);function s(...e){return(0,n.QP)((0,r.$)(e))}async function i(e){let t=new TextEncoder,a=process.env.AUTH_SECRET||"",r=t.encode(e+a);return btoa(String.fromCharCode(...new Uint8Array(await crypto.subtle.digest("SHA-256",r))))}async function d(e,t){return await i(e)===t}},93252:(e,t,a)=>{a.d(t,{Q:()=>n});var r=a(8899);let n=r.z.object({username:r.z.string().min(1,"用户名不能为空").max(20,"用户名不能超过20个字符").regex(/^[a-zA-Z0-9_-]+$/,"用户名只能包含字母、数字、下划线和横杠").refine(e=>!e.includes("@"),"用户名不能是邮箱格式"),password:r.z.string().min(8,"密码长度必须大于等于8位")})}}]);
//# sourceMappingURL=156.js.map