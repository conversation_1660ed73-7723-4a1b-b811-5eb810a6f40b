import { auth } from "@/lib/auth"
import { NextResponse } from "next/server"
import { PERMISSIONS } from "@/lib/permissions"
import { checkPermission } from "@/lib/auth"
import { Permission } from "@/lib/permissions"
import { handleApiKeyAuth } from "@/lib/apiKey"
import { EmailCredentialManager } from "@/lib/emailCredentials"

const API_PERMISSIONS: Record<string, Permission> = {
  '/api/emails': PERMISSIONS.MANAGE_EMAIL,
  '/api/webhook': PERMISSIONS.MANAGE_WEBHOOK,
  '/api/roles/promote': PERMISSIONS.PROMOTE_USER,
  '/api/config': PERMISSIONS.MANAGE_CONFIG,
  '/api/api-keys': PERMISSIONS.MANAGE_API_KEY,
  '/api/email-credentials': PERMISSIONS.MANAGE_API_KEY, // 邮箱凭证管理使用相同权限
}

// 处理邮箱凭证认证
async function handleEmailCredentialAuth(token: string, pathname: string) {
  // 只允许访问邮件相关的API
  if (!pathname.startsWith('/api/emails') && !pathname.startsWith('/api/config')) {
    return NextResponse.json(
      { error: "无权限访问" },
      { status: 403 }
    )
  }

  const credential = await EmailCredentialManager.verifyCredential(token)
  if (!credential) {
    return NextResponse.json(
      { error: "无效的邮箱凭证" },
      { status: 401 }
    )
  }

  const response = NextResponse.next()
  response.headers.set("X-User-Id", credential.userId)
  response.headers.set("X-Email-Address", credential.emailAddress)
  response.headers.set("X-Credential-Id", credential.id)
  return response
}

export async function middleware(request: Request) {
  const pathname = new URL(request.url).pathname

  // 清理请求头
  request.headers.delete("X-User-Id")
  request.headers.delete("X-Email-Address")
  request.headers.delete("X-Credential-Id")

  // 邮箱凭证认证
  const emailCredentialToken = request.headers.get("X-Email-Credential")
  if (emailCredentialToken) {
    return handleEmailCredentialAuth(emailCredentialToken, pathname)
  }

  // API Key 认证
  const apiKey = request.headers.get("X-API-Key")
  if (apiKey) {
    return handleApiKeyAuth(apiKey, pathname)
  }

  // Session 认证
  const session = await auth()
  if (!session?.user) {
    return NextResponse.json(
      { error: "未授权" },
      { status: 401 }
    )
  }

  if (pathname === '/api/config' && request.method === 'GET') {
    return NextResponse.next()
  }

  for (const [route, permission] of Object.entries(API_PERMISSIONS)) {
    if (pathname.startsWith(route)) {
      const hasAccess = await checkPermission(permission)

      if (!hasAccess) {
        return NextResponse.json(
          { error: "权限不足" },
          { status: 403 }
        )
      }
      break
    }
  }

  return NextResponse.next()
}

export const config = {
  matcher: [
    '/api/emails/:path*',
    '/api/webhook/:path*',
    '/api/roles/:path*',
    '/api/config/:path*',
    '/api/api-keys/:path*',
  ]
} 