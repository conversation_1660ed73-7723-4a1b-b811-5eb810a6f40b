(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[371],{65521:e=>{"use strict";e.exports=require("node:async_hooks")},25356:e=>{"use strict";e.exports=require("node:buffer")},60025:(e,t,i)=>{"use strict";i.r(t),i.d(t,{ComponentMod:()=>I,default:()=>_});var r,a={};i.r(a),i.d(a,{GET:()=>P,POST:()=>k,runtime:()=>S});var s={};i.r(s),i.d(s,{patchFetch:()=>A,routeModule:()=>w,serverHooks:()=>b,workAsyncStorage:()=>E,workUnitAsyncStorage:()=>R});var o=i(26312),n=i(35471),c=i(22680),l=i(65954),p=i(14139),u=i(43113),f=i(83553),m=i(85885),d=i(43144),g=i(3105),y=i(25601),h=i(17451),x=i(13091),v=i(66369);let S="edge";async function P(){if(!await (0,f.Yj)(h.Jj.MANAGE_API_KEY))return y.Rp.json({error:"权限不足"},{status:403});let e=await (0,f.j2)();try{let t=(0,m.d)(),i=await t.query.apiKeys.findMany({where:(0,x.eq)(d.apiKeys.userId,e.user.id),orderBy:(0,v.i)(d.apiKeys.createdAt)});return y.Rp.json({apiKeys:i.map(e=>({...e,key:void 0}))})}catch(e){return console.error("Failed to fetch API keys:",e),y.Rp.json({error:"获取 API Keys 失败"},{status:500})}}async function k(e){if(!await (0,f.Yj)(h.Jj.MANAGE_API_KEY))return y.Rp.json({error:"权限不足"},{status:403});let t=await (0,f.j2)();try{let{name:i}=await e.json();if(!i?.trim())return y.Rp.json({error:"名称不能为空"},{status:400});let r=`mk_${(0,g.Ak)(32)}`,a=(0,m.d)();return await a.insert(d.apiKeys).values({name:i,key:r,userId:t.user.id,expiresAt:new Date(Date.now()+31536e6)}),y.Rp.json({key:r})}catch(e){return console.error("Failed to create API key:",e),y.Rp.json({error:"创建 API Key 失败"},{status:500})}}let w=new l.AppRouteRouteModule({definition:{kind:p.A.APP_ROUTE,page:"/api/api-keys/route",pathname:"/api/api-keys",filename:"route",bundlePath:"app/api/api-keys/route"},resolvedPagePath:"F:\\CODE\\Project\\tempmail\\moemail\\app\\api\\api-keys\\route.ts",nextConfigOutput:"",userland:a}),{workAsyncStorage:E,workUnitAsyncStorage:R,serverHooks:b}=w;function A(){return(0,u.V5)({workAsyncStorage:E,workUnitAsyncStorage:R})}let C=null==(r=self.__RSC_MANIFEST)?void 0:r["/api/api-keys/route"],M=(e=>e?JSON.parse(e):void 0)(self.__RSC_SERVER_MANIFEST);C&&M&&(0,n.fQ)({page:"/api/api-keys/route",clientReferenceManifest:C,serverActionsManifest:M,serverModuleMap:(0,o.e)({serverActionsManifest:M})});let I=s,_=c.s.wrap(w,{nextConfig:{env:{},eslint:{ignoreDuringBuilds:!1},typescript:{ignoreBuildErrors:!1,tsconfigPath:"tsconfig.json"},distDir:".next",cleanDistDir:!0,assetPrefix:"",cacheMaxMemorySize:0x3200000,configOrigin:"next.config.ts",useFileSystemPublicRoutes:!0,generateEtags:!0,pageExtensions:["tsx","ts","jsx","js"],poweredByHeader:!0,compress:!0,images:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",loaderFile:"",domains:[],disableStaticImages:!1,minimumCacheTTL:60,formats:["image/webp"],dangerouslyAllowSVG:!1,contentSecurityPolicy:"script-src 'none'; frame-src 'none'; sandbox;",contentDispositionType:"attachment",remotePatterns:[{protocol:"https",hostname:"avatars.githubusercontent.com"}],unoptimized:!1},devIndicators:{appIsrStatus:!0,buildActivity:!0,buildActivityPosition:"bottom-right"},onDemandEntries:{maxInactiveAge:6e4,pagesBufferLength:5},amp:{canonicalBase:""},basePath:"",sassOptions:{},trailingSlash:!1,i18n:null,productionBrowserSourceMaps:!1,excludeDefaultMomentLocales:!0,serverRuntimeConfig:{},publicRuntimeConfig:{},reactProductionProfiling:!1,reactStrictMode:null,reactMaxHeadersLength:6e3,httpAgentOptions:{keepAlive:!0},logging:{},expireTime:31536e3,staticPageGenerationTimeout:60,modularizeImports:{"@mui/icons-material":{transform:"@mui/icons-material/{{member}}"},lodash:{transform:"lodash/{{member}}"}},outputFileTracingRoot:"F:\\CODE\\Project\\tempmail\\moemail",experimental:{cacheLife:{default:{stale:300,revalidate:900,expire:0xfffffffe},seconds:{stale:0,revalidate:1,expire:60},minutes:{stale:300,revalidate:60,expire:3600},hours:{stale:300,revalidate:3600,expire:86400},days:{stale:300,revalidate:86400,expire:604800},weeks:{stale:300,revalidate:604800,expire:2592e3},max:{stale:300,revalidate:2592e3,expire:0xfffffffe}},cacheHandlers:{},cssChunking:!0,multiZoneDraftMode:!1,appNavFailHandling:!1,prerenderEarlyExit:!0,serverMinification:!0,serverSourceMaps:!1,linkNoTouchStart:!1,caseSensitiveRoutes:!1,clientSegmentCache:!1,preloadEntriesOnStart:!0,clientRouterFilter:!0,clientRouterFilterRedirects:!1,fetchCacheKeyPrefix:"",middlewarePrefetch:"flexible",optimisticClientCache:!0,manualClientBasePath:!1,cpus:11,memoryBasedWorkersCount:!1,imgOptConcurrency:null,imgOptTimeoutInSeconds:7,imgOptMaxInputPixels:0xfff8001,imgOptSequentialRead:null,isrFlushToDisk:!0,workerThreads:!1,optimizeCss:!1,nextScriptWorkers:!1,scrollRestoration:!1,externalDir:!1,disableOptimizedLoading:!1,gzipSize:!0,craCompat:!1,esmExternals:!0,fullySpecified:!1,swcTraceProfiling:!1,forceSwcTransforms:!1,largePageDataBytes:128e3,turbo:{root:"F:\\CODE\\Project\\tempmail\\moemail"},typedRoutes:!1,typedEnv:!1,parallelServerCompiles:!1,parallelServerBuildTraces:!1,ppr:!1,authInterrupts:!1,reactOwnerStack:!1,webpackMemoryOptimizations:!1,optimizeServerReact:!0,useEarlyImport:!1,staleTimes:{dynamic:0,static:300},serverComponentsHmrCache:!0,staticGenerationMaxConcurrency:8,staticGenerationMinPagesPerWorker:25,dynamicIO:!1,inlineCss:!1,optimizePackageImports:["lucide-react","date-fns","lodash-es","ramda","antd","react-bootstrap","ahooks","@ant-design/icons","@headlessui/react","@headlessui-float/react","@heroicons/react/20/solid","@heroicons/react/24/solid","@heroicons/react/24/outline","@visx/visx","@tremor/react","rxjs","@mui/material","@mui/icons-material","recharts","react-use","effect","@effect/schema","@effect/platform","@effect/platform-node","@effect/platform-browser","@effect/platform-bun","@effect/sql","@effect/sql-mssql","@effect/sql-mysql2","@effect/sql-pg","@effect/sql-squlite-node","@effect/sql-squlite-bun","@effect/sql-squlite-wasm","@effect/sql-squlite-react-native","@effect/rpc","@effect/rpc-http","@effect/typeclass","@effect/experimental","@effect/opentelemetry","@material-ui/core","@material-ui/icons","@tabler/icons-react","mui-core","react-icons/ai","react-icons/bi","react-icons/bs","react-icons/cg","react-icons/ci","react-icons/di","react-icons/fa","react-icons/fa6","react-icons/fc","react-icons/fi","react-icons/gi","react-icons/go","react-icons/gr","react-icons/hi","react-icons/hi2","react-icons/im","react-icons/io","react-icons/io5","react-icons/lia","react-icons/lib","react-icons/lu","react-icons/md","react-icons/pi","react-icons/ri","react-icons/rx","react-icons/si","react-icons/sl","react-icons/tb","react-icons/tfi","react-icons/ti","react-icons/vsc","react-icons/wi"]},bundlePagesRouterDependencies:!1,configFile:"F:\\CODE\\Project\\tempmail\\moemail\\next.config.ts",configFileName:"next.config.ts"}})},96487:()=>{},78335:()=>{},3105:(e,t,i)=>{"use strict";i.d(t,{Ak:()=>r});let r=(e=21)=>{let t="",i=crypto.getRandomValues(new Uint8Array(e|=0));for(;e--;)t+="useandom-26T198340PX75pxJACKVERYMINDBUSHWOLF_GQZbfghjklqvwyzrict"[63&i[e]];return t}}},e=>{var t=t=>e(e.s=t);e.O(0,[730,752,899,498,220,156],()=>t(60025));var i=e.O();(_ENTRIES="undefined"==typeof _ENTRIES?{}:_ENTRIES)["middleware_app/api/api-keys/route"]=i}]);
//# sourceMappingURL=route.js.map